(function() {
    'use strict';
    app.controller("staffProbationPeriodController", ['comService','$rootScope', '$stateParams', '$scope', 'staffPerformanceService','$modal','inform', 'Trans', 'AgreeConstant','$state', 'LocalCache', '$http',
        function(comService,$rootScope,$stateParams,$scope, staffPerformanceService,$modal,inform, Trans, AgreeConstant,$state, LocalCache, $http ) {
           /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            $scope.datepicker = {
               currentDate :  new Date()
            };
            $scope.stateSearchList=[{
                'code':'0',
                'name':'试用期'
            },{
            	'code':'1',
            	'name':'在职'
            },{
            	'code':'2',
                'name':'离职'
            }];
            //绑定预算文件控件改变事件
            $("#filesUpload").change(submitForm);
            //设置列表的高度
    		setDivHeight();
    		//新增数据对象
            $scope.addParam = {};
    		//窗体大小变化时重新计算高度
    		$(window).resize(setDivHeight);
            getAreaList();//地区
            $scope.searchObject = {};
            getStaffList();//员工编号和姓名列表

            $("#filesImg1").change(fileChangeEvent);//绑定文件控件改变事件
            $scope.getData = getData;
            $scope.pages = {
               pageNum:"1",
               size:"100"
            };
            $scope.flag = false;
            //设置员工地区的访问权限
            $scope.areaCodeFlag = true;
            initPrimaryDeptList();//初始化一级部门列表
            $scope.searchObject.primaryDept = "";//初始化一级部门
            $scope.searchObject.staffState='0';//初始化员工状态
            initPage();

    		/**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */


            /**
             * 设置列表的高度
             */
            function setDivHeight(){
                //网页可见区域高度
                 var clientHeight = document.body.clientHeight;
                 var divHeight = clientHeight - (150 + 185);
                 $("#divTBDis").height(divHeight);
                 $("#subDivTBDis").height(divHeight - 65);
            }



            /**
             * 选择上传文件后事件
             */
            function fileChangeEvent(e){
                var fileName = "文件名称：" + e.currentTarget.files[0].name;
                $("#fileNameDis").text(fileName);
            }


            //重置查询条件
            $scope.clearParams = function() {
                //区域权限
                if (!$scope.areaCodeFlag) {
                    $scope.searchObject.area = "";
                }
                if($scope.flag){
                   $scope.searchObject.department = "";
                   $scope.searchObject.primaryDept = "";
                }
                $scope.searchObject.staffState='0';
                $scope.searchObject.employeeName = "";
                $scope.searchObject.onboardingTime = "";
                $scope.searchObject.type = "";
            };

            //获取地区
            function getAreaList(){
                $scope.areaList = [];
                comService.getParamList('AREA_TYPE','AREA_TYPE').then(function(data) {
                   $scope.areaList = data.data;
                });
            }

             /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function(data) {
                     if (data.data) {
                         $scope.primaryDeptList = data.data;
                     }
                });
            }
             /**
             * 初始化二级部门列表
             */
            function initSecDeptList() {
                //获取二级部门
                setDept();

            }

            function setDept(){
                //获取二级部门
                $scope.deptList = [];
                comService.getOrgChildren($scope.searchObject.primaryDept).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.deptList = data.data;
                    }
                });

            }

            //修改一级部门，二级部门进行联动
            $scope.changeDept = function(){
                setDept();
            };

            //定义排序对象
            $scope.orderObj = {
                title: '$index',
                desc: true,
                order: function(str) {
                    $scope.orderObj.title = str;
                    $scope.orderObj.desc = !$scope.orderObj.desc;
                }
            };

            //入职时间
            $scope.openOnboardingTime = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = true;
                $scope.openTurnPositiveDate1 = false;
            };

            //转正时间
            $scope.openTurnPositiveDate = function($event) {
               $event.preventDefault();
               $event.stopPropagation();
               $scope.openOnboardingTime1 = false;
               $scope.openTurnPositiveDate1 = true;
            };

            /**
            ** 员工编号和姓名列表
            */
            function getStaffList(){
                // '1'代表获取所有的员工（包括离职的）
                comService.getEmployeesByOrgId('','1').then(function (data) {
                    if (data.data) {
                        $scope.staffList = data.data;
                    }
                });
            }

            //分页查询
            function getData(page) {
                if (inform.format($scope.searchObject.onboardTime,'yyyy-MM-dd')==="NaN-NaN-NaN") {
                    $scope.searchObject.onboardTime='';
                }else{
                    $scope.searchObject.onboardTime = inform.format($scope.searchObject.onboardingTime,'yyyy-MM-dd');
                }

                //拼装查询条件
                var params = {
                    area:$scope.searchObject.area,
                    department:$scope.searchObject.department,
                    primaryDept:$scope.searchObject.primaryDept,
                    employeeName:$scope.searchObject.employeeName,
                    onboardingTime:$scope.searchObject.onboardingTime,
                    state:$scope.searchObject.staffState,
                    page: page,
                    size: $scope.pages.size
                };
                //获取数据
                staffPerformanceService.selectProbationPeriodByParam(JSON.stringify(params)).then(function (result) {
                     if (result.code === '0000') {
                        $scope.dataList = result.data.list;
                        if (null ==result.data.list || result.data.list.length === 0) {
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages();
                        } else {
                        // 分页信息设置
                              $scope.pages.total = result.data.total;		// 页面总数
                              $scope.pages.star = result.data.startRow;  	//页面起始数
                              $scope.pages.end = result.data.endRow;  		//页面大小数
                              $scope.pages.pageNum = result.data.pageNum;  	//页面页数
                        }

                    } else {
                          inform.common(result.message);
                    }
                }, function (reason) {
                    console.log("error");
                });
            }

            //初始化页面
            function initPage() {
                getDepartmentCode();
            }
            //获取部门权限码(先判断是否为中心办，如果不是，在判断是否为白名单的人员)
            function getDepartmentCode(){
                   //判断是否为中心办
                   comService.isCenterOffice().then(function (res) {
                      if(res.code==="0000" && res.data.code==='01'){
                        //01全部权限
                        $scope.flag = true;
                        getData(1);
                        return;
                      }else{
                        comService.validAuthentication("0003").then(function (result) {
                             if(result.code==='0000'){
                                 if(result.data.code==='00'){
                                     $state.go('app.office.unAuthority');
                                     return;
                                 }
                                 if(result.data.code==='01'){
                                     $scope.flag = true;
                                     getData(1);
                                     return;
                                 }
                                 if (result.data.code === '03') {
                                     $scope.flag = true;
                                     $scope.areaCodeFlag = true;
                                     $('#area').attr('disabled', true);
                                     $scope.searchObject.area = result.data.areaCode;
                                     getData(1);
                                     return;
                                 }
                                $scope.flagAuth = true;
                                $scope.searchObject.primaryDept = res.data.primaryDeptCode;
                                $("#primaryDeptName").attr("disabled","disabled");
                                initSecDeptList();
                                if(res.data.departmentCode){
                                    $scope.searchObject.department = res.data.departmentCode;
                                    $("#departmentName").attr("disabled","disabled");
                                }
                                getData(1);


                             }
                          });


                      }
                   });

            }




            /**
             * 添加
             */
            $scope.openAddModal = function () {
                $scope.addParam = {};
            };

            //保存新增数据
            $scope.saveAddData = function () {
                var param = {
                    "employeeName":$scope.addParam.employeeName,
                    "employeeId":$scope.addParam.employeeNo,
                    "examinationMonth":$scope.addParam.examinationMonth,
                    "evaluationResult":$scope.addParam.evaluationResult,
                    "probationNote":$scope.addParam.probationNote
                };
                staffPerformanceService.addStaffProbationPeriodInfo(param).then(function (result) {
                    if (result.code==='0000') {
                        $("#add_staffInfo").modal('hide');
                        layer.msg(result.message,
                             {
                               time:1000,//1秒自动关闭
                             }, function () {
                                 getData(1);
                                 $scope.addParam = {};
                             }
                        );
                    }else{
                        layer.msg(result.message,
                             {
                               time:1000,//1秒自动关闭
                             }, function () {
                                 getData(1);
                                 $scope.addParam = {};
                             }
                        );

                    }
                });

            }


            //打开修改窗口
            $scope.openUpdateModal = function(item) {
                $scope.updateParam = angular.copy(item);
            }

             //保存修改数据
            $scope.saveUpdateData = function () {

                    var param = {
                        "id":$scope.updateParam.id,
                        "employeeName":$scope.updateParam.employeeName,
                        "employeeId":$scope.updateParam.employeeId,
                        "onboardingTime":$scope.updateParam.onboardingTime,
                        "firstMonth":$scope.updateParam.firstMonth,
                        "secondMonth":$scope.updateParam.secondMonth,
                        "thirdMonth":$scope.updateParam.thirdMonth,
                        "fourthMonth":$scope.updateParam.fourthMonth,
                        "fifthMonth":$scope.updateParam.fifthMonth,
                        "turnPositiveDate":inform.format($scope.updateParam.turnPositiveDate,'yyyy-MM-dd')==='NaN-NaN-NaN'?null:inform.format($scope.updateParam.turnPositiveDate,'yyyy-MM-dd'),
                        "turnPositive":$scope.updateParam.turnPositive,
                        "probationNote":$scope.updateParam.probationNote
                    };
                    staffPerformanceService.updateStaffProbationPeriodInfo(param).then(function (result) {
                        if ( result.code==='0000') {
                           $("#update_staffInfo").modal("hide");
                           layer.msg(result.message,
                             {
                               time:1000,//1秒自动关闭
                             }, function () {
                                 getData(1);
                                 $scope.addParam = {};
                             }
                           );

                        }else{
                            inform.common(result.message);
                        }
                    });

            }

             //生成Excel表格
            $scope.toExcel = function() {

                inform.modalInstance("确定要下载吗?").result.then(function() {

                    var params = {
                        area:$scope.searchObject.area,
                        primaryDept:$scope.searchObject.primaryDept,
                        department:$scope.searchObject.department,
                        employeeName:$scope.searchObject.employeeName,
                        companyTitle:$scope.searchObject.companyTitle,
                        onboardingTime:$scope.searchObject.onboardTime,
                        onboardingStopTime:$scope.searchObject.onboardStopTime,
                        education:$scope.searchObject.education,
                        state:$scope.searchObject.staffState,
                    }
                    inform.downLoadFile('staffProbationPeriodInfo/toExcel',params,"员工试用期信息表.xlsx");
                });

            };

            /**
	      * 导入Excel
	      */
	     $scope.selectFile = function() {
            document.getElementById("filesUpload").click();
	     }

	     //上传文件
         function submitForm(){
	    	//表单id  初始化表单值
            var formData = new FormData();
            var file = document.querySelector('input[id=filesUpload]').files[0];
            if (!file) {
                inform.common("请先选择文件!");
                return false;
            }
            if (file.size > AgreeConstant.fileSize) {
                inform.common("上传文件大小不能超过2M");
                fileChangeReset();
                return false;
            }
            formData.append('file', file);
            var a = file.type;
            if (a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
                inform.common("请选择.xlsx类型的文档进行上传!");
                return false;
            } else {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function items() {
                            return "确定要导入吗！";
                        }
                    }
                });
                var uploadUrl = $rootScope.getWaySystemApi + 'staffProbationPeriodInfo/uploadInfoExcel';
                modalInstance.result.then(function () {
                    //开启遮罩层
                    inform.showLayer("上传中。。。。。。");
                    $.ajax({
                        url: uploadUrl,
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        beforeSend: function beforeSend(request) {
                            request.setRequestHeader("Authorization", 'Bearer ' + LocalCache.getSession("token") || '');
                        },
                        success: function success(result) {
                            if (result.code === AgreeConstant.code) {
                                // 关闭遮罩层
                                inform.closeLayer();
                                if(result.data!==null){
                                	var budgetDto = result.data
                                	$scope.confrimBudget($scope.type,budgetDto);
                                }else {
                                	layer.confirm(result.message,{
                                        title:false,
                                        btn:['确定']
                                    },function(result){
                                        layer.close(result);
                                        getData(1);
                                    });
                                }
                            } else {
                                inform.closeLayer();
                                $modal.open({
                                    templateUrl: 'tpl/common/errorModel.html',
                                    controller: 'ModalInstanceCtrl',
                                    size: "lg",
                                    resolve: {
                                        items: function() {
                                            return result.message;
                                        }
                                    }
                                });
                            }
                            //移除文件名称 通过表单元素的reset方法实现选择文件的重置
                            $("#formUpload")[0].reset();
                        },
                        error: function error(_error) {
                            inform.common(Trans("tip.requestError"));
                        }
                    });
                });
            }
	     }

         /**
	     * 下载模板信息
	     */
        $scope.toExcelModule = function () {
        	inform.modalInstance("确定要下载吗?").result.then(function () {
            	inform.downLoadFile('staffProbationPeriodInfo/toExcelModule',null,'试用期绩效导入.xlsx');
        	});
        };

 /**
         * *************************************************************
         *              方法声明部分                                结束
         * *************************************************************
         */

        }
    ]);
})();
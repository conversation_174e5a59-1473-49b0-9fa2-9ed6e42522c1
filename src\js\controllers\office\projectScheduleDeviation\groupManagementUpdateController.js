
(function () {
	app.controller("groupManagementUpdate", ['comService', '$rootScope', '$scope', 'projectManagementService', '$stateParams', 'inform', 'Trans', '$modal','AgreeConstant','LocalCache', '$state',
	function (comService, $rootScope, $scope, projectManagementService, $stateParams, inform, Trans, $modal,AgreeConstant, LocalCache,$state) {
		/**
   * *************************************************************
   *             初始化部分                                 开始
   * *************************************************************
   */
		// 正则校验配置
		$scope.limitList = AgreeConstant.limitList;
		//绑定项目导入excel文件控件改变事件
        $("#filesUpload").change(submitForm);
		//增加还是修改0：增加，1：修改
		$scope.isAdd = $stateParams.isAdd;

        $scope.param = JSON.parse($stateParams.groupInfoParam);
        $scope.specDetail = {
			'reportProhibitions': []
		};
        //为报表禁用控制标识赋值
        popModalDetail($scope.param.reportProhibitions);
        $scope.groupId = null;
        $scope.flag = 0;
        //是否为增加时的修改0：是，1：否
        $scope.isAddUpdate = "1";
		//项目状态下拉框数
		$scope.projectStatusSelect = AgreeConstant.teamStatusList;
        $scope.setData = setData;
        $scope.groupStatusChange = groupStatusChange;
		//项目评审等级
		$scope.reviewGradeList = ['一级', '二级', '三级'];
		//地区
        $scope.areaList = ['威海主导', '北京主导', '西安主导','深圳主导'];
        //行业
        $scope.professionList = ['金融行业','物流行业','新零售行业','新兴行业'];
        //团队类型
        $scope.groupTypeList = AgreeConstant.groupTypeList;
		//设置列表的高度
		setDivHeight();
		//初始化页面信息
		initPages();
		var projectManagerName = ""; //项目经理姓名
		$(window).resize(setDivHeight); //窗体大小变化时重新计算高度
		/**
   * *************************************************************
   *              初始化部分                                 结束
   * *************************************************************
   */

		/**
   * *************************************************************
   *              方法声明部分                                开始
   * *************************************************************
   */

		/**
    	 * 设置列表的高度
    	 */
		function setDivHeight() {
			//网页可见区域高度
			var clientHeight = document.body.clientHeight;
			var divHeight = clientHeight - (150 + 185);
			$("#divTBDis").height(divHeight);
			$("#subDivTBDis").height(divHeight - 50);
			var clientWidth = document.body.clientWidth;
			$("#buttonStyle").css(inform.getButtonStyle(clientHeight, clientWidth));
		}
		/**
   * 页面初始化
   */
		function initPages() {
            //获取员工信息
			$scope.employeesList = [];
			comService.getEmployeesByOrgId('','1').then(function (data) {
				if (data.data) {
					$scope.employeesList = data.data;
                    $scope.flag++;
                    $scope.setParams();
				}
			});
            //获取产品线
			$scope.projectLine = [];
			comService.queryEffectiveParam('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
				if (data.data) {
					$scope.projectLine = data.data;
					$scope.flag++;
                    $scope.setParams();
				}
			});
            //获取山东新北洋集团的下级部门信息
			$scope.orgList = [];
			comService.getOrgChildren('D010053').then(function (data) {
				$scope.orgList = comService.getDepartment(data.data);
				$scope.flag++;
                $scope.setParams();
			});
            //获取报表信息
			$scope.reportLine = [];
			comService.queryEffectiveParam('REPORT', 'REPORT').then(function (data) {
				if (data.data) {
					$scope.reportLine = data.data;
					if($scope.isAdd==="0" ){
                       $scope.specDetail.reportProhibitions.push('0001');
                    }
				}
			});
		}

		/**修改项目经理
        **/
		$scope.changeManager = function () {

                for (var i = 0; i < $scope.employeesList.length; i++) {
                    if ($scope.employeesList[i].employeeNo === $scope.param.projectManagerNo) {
                        projectManagerName = $scope.employeesList[i].realName;
                        break;
                    }
                }

		};

        $scope.setParams = function () {
            if($scope.isAdd==="0" && $scope.isAddUpdate ==="1"){
                return;
            }
            if ($scope.flag===3) {
                angular.forEach($scope.employeesList, function (employee, i) {//遍历人员
                    if (employee.realName===$scope.param.productManager) { //Product Owner
                         $scope.param.productManager = employee.employeeNo;
                    }
               });
                angular.forEach($scope.projectLine, function (line, i) {//遍历产品线
                  if (line.paramValue===$scope.param.productLine) {
                      $scope.param.productLine = line.paramCode;
                      $scope.changeProductTypeName(line.paramCode);
                  }
               });
                angular.forEach($scope.orgList, function (org, i) {//遍历部门
                    if (org.orgName===$scope.param.department) {
                       $scope.param.department = org.orgCode;
                    }
                });

                $scope.groupId = $scope.param.id;
                //设置选择框的值
                $scope.setCheckBosValue();
            }
        };

        $scope.setCheckBosValue = function(){
            setTimeout(setData,3000);
        }

        //初始化checkbox的值
         function setData() {
                if($scope.isAdd==="0" && $scope.isAddUpdate ==="1"){
                     return;
                }
                var boxes = document.getElementsByName("products");
                var dataList = [];
                if($scope.param.companyProductTypeNameCode){
                    dataList = $scope.param.companyProductTypeNameCode.split(";");
                }
            	//获取页面所有产品名称
            	for(var i=0;i<boxes.length;i++){
            	    boxes[i].checked = false;
            		//现有的产品名称
            		for(var j=0;j<dataList.length;j++){
            			if (boxes[i].value === dataList[j]){
            				boxes[i].checked = true;
            			}
            		}
            	}
        }

        //改变产品类别及名称
		$scope.changeProductTypeName = function (productLine) {
            //获取产品类别及名称
			$scope.productTypeNameList = [];
            projectManagementService.getProductTypeNameList(productLine).then(function (data) {
				if (data.data) {
					$scope.productTypeNameList = data.data;
				}
			});
		};

		/**
   * 修改信息
   */
		$scope.updateInfo  = function () {
			if (!$scope.param.startTime) {
				inform.common("请填写开始时间");
			} else if ($scope.isAdd==="1" && $scope.param.projectStatus == null) {
                    inform.common("请选择项目状态");
            } else {
				var urlData = {
				    'projectStatus':$scope.isAdd==="1" ? $scope.param.projectStatus:"进行中",
				    'id': $scope.groupId,
				    'projectNumber': $scope.param.projectNumber,
					'cname': $scope.param.cname,
					'projectManager': projectManagerName, //项目经理姓名
                    'projectManagerNo': $scope.param.projectManagerNo, //项目经理工号
					'startTime': inform.format($scope.param.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.param.endTime, 'yyyy-MM-dd'),
					'testLeader': $scope.param.testLeader, //测试负责人
                    'operations': null == $scope.param.operations ?null:$scope.param.operations.join(','), //运维工程师
                    'productLine': $scope.param.productLine,//软件产品线
                    'productManager': $scope.param.productManager,//Product Owner
                    'companyProductTypeNameCode':$scope.param.companyProductTypeNameCode,
                    'higherUp': $scope.param.higherUp,//直属上级

                    'department': $scope.param.department,   //部门
                    'reviewGrade': $scope.param.reviewGrade,//团队级别
                    'projectTypeFlag': "T",//团队类型标志
                    'projectGroupType': $scope.param.projectGroupType,//团队类型

                    'groupOrientation': $scope.param.groupOrientation,//团队定位
                    'groupTarget': $scope.param.groupTarget,//团队目标
                    'softwareCopyright': $scope.param.softwareCopyright,//软件著作权要求
                    'patent': $scope.param.patent,//专利要求
                    'area':$scope.param.area,//地区
                    'profession':$scope.param.profession,//行业
                    'reportProhibitions': $scope.specDetail.reportProhibitions.join(','),
                 //   'reportProhibitions': '0001',
                    'type':"敏捷",
                    //团队没有以下字段，但是数据库中要求不能为空
                    'projectScale':"",
                    'projectAssistant':"",
                    'qualityEngineer':""

				};
				projectManagementService.updateProjectInfo(urlData).then(function (data) {
					if (data.code === AgreeConstant.code) {
						inform.common(data.message);
						$scope.goback();
					} else {
					    //弹框提示
					    if(data.message.indexOf("项目id=")>-1){
                            //选择是否覆盖项目(新增)
                           inform.modalInstance("团队已存在，是否覆盖？").result.then(function () {
                              $scope.groupId = data.message.split("=")[1];
                              $scope.updateInfo();
                            });
					    }else{
					        inform.common(data.message);
					    }
					}
				}, function (error) {
					inform.common(Trans("tip.requestError"));
				});
			}
		};
		$scope.notSelect = function () {
			if ($scope.zerochecked) {
				$scope.zerochecked = false;
			}
			if ($scope.onechecked) {
				$scope.onechecked = false;
			}
		};
        /*
        * 团队状态发生改变,将结束时间改为当前时间
        * */
        function groupStatusChange() {
             if($scope.param.projectStatus === '2') {
                $scope.param.endTime = inform.format(new Date(),'yyyy-MM-dd');
             } else {
                $scope.param.endTime ='';
             }
        }
		/**
   * 报表禁止范围控制
   */
		$scope.selectAll = function () {
			angular.forEach($scope.reportLine, function (report) {
				if ($scope.specDetail.reportProhibitions.indexOf(report.paramCode) === -1) {
					$scope.specDetail.reportProhibitions.push(report.paramCode);
				}
			});
			$scope.zerochecked = true;
			$scope.onechecked = false;
		};
		$scope.clearAll = function () {
			$scope.specDetail.reportProhibitions = [];
			$scope.onechecked = true;
			$scope.zerochecked = false;
		};
        //报表禁用控制赋值
            function popModalDetail(item) {
                if (null ==item) {
                    item = '';
                }
                var participants = item.slice(0, item.length);
                $scope.specDetail.reportProhibitions = participants.split(',');
            }
		/**
   * 返回项目信息历
   */
		$scope.goback = function () {
			$state.go('app.office.groupManagement');
		};

		//配置产品类别
		$scope.configProductType = function () {
            $("#configProductType_modal").modal("hide");
            var boxes = document.getElementsByName("products");
       		 var details = "";
       		 angular.forEach(boxes, function (detail, i) {
       			 if (boxes[i].checked){
       				 details = details+boxes[i].value+";";
       			 }
       		 });
       		 $scope.param.companyProductTypeNameCode = details.substr(0,details.length-1);
		}

		/**
   *  新增的开始时间按钮
   */
		$scope.startTime_update = function ($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.startTime1 = false;
			$scope.startTime_update1 = true;
			$scope.endTime1 = false;
			$scope.endTime_update1 = false;
		};
		/**
   *  新增的修改时间按钮
   */
		$scope.endTime_update = function ($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.startTime1 = false;
			$scope.startTime_update1 = false;
			$scope.endTime1 = false;
			$scope.endTime_update1 = true;
		};
		/**
        * 导入预算
        */
        $scope.selectFile = function() {
               document.getElementById("filesUpload").click();
        }

        /**
	     * 项目信息上传文件
	     */
	    function submitForm(e){
	    	//表单id  初始化表单值
            var formData = new FormData();
            //获取文档中有类型为file的第一个input元素
            var file = document.querySelector('input[type=file]').files[0];
            if (!file) {
                inform.common("请先选择文件!");
                return false;
            }
            if (file.size > AgreeConstant.fileSize) {
                inform.common("上传文件大小不能超过2M");
                fileChangeReset();
                return false;
            }
            formData.append('file', file);
            var a = file.type;
            if (a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
                inform.common("请选择.xlsx类型的文档进行上传!");
                return false;
            } else {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function items() {
                            return "确定要导入吗！";
                        }
                    }
                });
                var uploadUrl = $rootScope.getWaySystemApi + 'projectmanagement/uploadGroupInfo';

                modalInstance.result.then(function () {
                    //开启遮罩层
                    inform.showLayer("上传中。。。。。。");
                    $.ajax({
                        url: uploadUrl,
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        beforeSend: function beforeSend(request) {
                            request.setRequestHeader("Authorization", 'Bearer ' + LocalCache.getSession("token") || '');
                        },
                        success: function success(result) {
                            if (result.code === AgreeConstant.code) {
                                // 关闭遮罩层
                                inform.closeLayer();
                                layer.confirm(result.message,{
                                    title:false,
                                    btn:['确定']
                                },function(confirmMsg){
                                    layer.close(confirmMsg);
                                    $scope.isAddUpdate ="0";
                                    $scope.param = result.data["projectInfo"];
                                    $scope.groupId = $scope.param.id;
                                    $scope.changeProductTypeName($scope.param.productLine);
                                });
                            } else {
                                //校验不通过,弹框提示
           					    if(result.message.indexOf("项目id=")>-1){
           					        // 关闭遮罩层
                                      inform.closeLayer();
                                       //选择是否覆盖项目
                                      inform.modalInstance("团队已存在，是否覆盖？").result.then(function () {
                                          $scope.groupId = result.message.split("=")[1];
                                          var urlData = {
                                          		'id': $scope.groupId
                                          };
                                           projectManagementService.addGroupInfo(urlData).then(function (data) {
                            					if (data.code === AgreeConstant.code) {
                            						inform.common(data.message);
                            						$scope.isAddUpdate ="0";
                            						$scope.param = data.data["projectInfo"];
                            						$scope.changeProductTypeName($scope.param.productLine);
                            					} else {
                                                    $modal.open({
                                                              templateUrl: 'tpl/common/errorModel.html',
                                                              controller: 'ModalInstanceCtrl',
                                                              size: "lg",
                                                              resolve: {
                                                                  items: function() {
                                                                     return data.message;
                                                                  }
                                                              }
                                                    });
                            					}
                            				}, function (error) {
                            					inform.common(Trans("tip.requestError"));
                            				});
                                       });
           					    }else{
           	                        inform.closeLayer();
                                    $modal.open({
                                         templateUrl: 'tpl/common/errorModel.html',
                                         controller: 'ModalInstanceCtrl',
                                         size: "lg",
                                         resolve: {
                                             items: function() {
                                                return result.message;
                                             }
                                         }
                                    });
           					    }
                            }
                            //移除文件名称 通过表单元素的reset方法实现选择文件的重置
                            $("#formUpload")[0].reset();
                        },
                        error: function error(_error) {
                            inform.common(Trans("tip.requestError"));
                        }
                    });
                });
            }
	     }


		/**
     * *************************************************************
     *              方法声明部分                                结束
     * *************************************************************
     */
	}]);
})();

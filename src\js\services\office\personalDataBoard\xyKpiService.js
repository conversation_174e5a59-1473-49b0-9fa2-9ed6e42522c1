(function() {
	'use strict';
	app.factory('xyKpiService', xyKpiService);
	xyKpiService.$inject = [ "HttpService", '$rootScope' ];

	function xyKpiService(HttpService, $rootScope) {
		var service = {
            getXYKpiTableData:getXYKpiTableData,
            getProjectPlanCompleteRate:getProjectPlanCompleteRate,
            getProjectCostRate:getProjectCostRate,
            getPlmFinishRate:getPlmFinishRate,
            getWorkHoursRate:getWorkHoursRate,
		};
		return service;

        //获取系研kpi表格数据
        function getXYKpiTableData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyKpi/getXYKpiTableData', urlData);
        }

        //获取项目计划完成率
        function getProjectPlanCompleteRate(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyKpi/getProjectPlanCompleteRate', urlData);
        }

        //获取成本偏差
        function getProjectCostRate(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyKpi/getProjectCostRate', urlData);
        }

        //获取plm按时完成率
        function getPlmFinishRate(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyKpi/getPlmFinishRate', urlData);
        }

        //获取工时占比
        function getWorkHoursRate(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyKpi/getWorkHoursRate', urlData);
        }

	}
})();

(function () {
    app.controller("versionReleaseDetailController", ['comService','$scope','$modal','$state','$stateParams','versionReleaseService','inform','Trans','AgreeConstant','LocalCache',
        function (comService, $scope, $modal,$state,$stateParams,versionReleaseService,inform,Trans,AgreeConstant,LocalCache) {
       	
     	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 * 
		 */
    	$scope.limitList = AgreeConstant.limitList; // 正则校验配置
		$scope.releaseTypes = ['计划发布','问题处理','紧急上线','回滚版本','配置变更'];
		//初始化页面信息
    	initPages();


		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
	  	
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */	

    	/**
     	 * 获取产品线code
     	 */
	    function initPages() {

	    	//点击查看详情获取项目名称
	    	var urlData = {
	    		'id':$stateParams.id,
	    	}
	    	versionReleaseService.getVersionReleaseLog(urlData).then(function(data) {
	    		if(data.code===AgreeConstant.code){
	    			$scope.changeParam =  data.data;

	    		}
	    	},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
	    }

		/**
 		 * *************************************************************
 		 *              方法声明部分                                 结束
 		 * *************************************************************
 		 */	
		
		
	}]);
})();
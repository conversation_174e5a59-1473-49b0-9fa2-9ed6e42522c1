(function () {
    'use strict';
    app.controller("kpiManagemetController", ['$scope','$state','comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','kpiManagemetService','$stateParams','LocalCache', '$modal','$http',
        function ($scope,$state,comService, $rootScope, inform, Trans, AgreeConstant,kpiManagemetService,$stateParams,LocalCache, $modal,$http) {

		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		 //判断是项目还是团队，团队的话可判断是管理还是监控
		 $scope.flag = $stateParams.flag;
         //查询条件 从缓存中读取
         $scope.formRefer = LocalCache.getObject('kpiManagement_formRefer');
         if($scope.formRefer.year==null && $scope.flag!=null){
             //默认当前年度
             $scope.formRefer.year = inform.format(new Date(),"yyyy");
         }
         LocalCache.setObject('kpiManagement_formRefer',{});
         // 初始化分页数据
         $scope.pages = inform.initPages();
         //被选择kpi集合
         $scope.kpiSelected = [];
         //评价人维护
         $scope.appraiser = {};
         //季度下拉框数据源
	     $scope.quarterSelect = [{
             value: '6',
             label: '上半年'
         }, {
             value: '7',
             label: '下半年'
         }, {
             value: '5',
             label: '年度'
         }, {
	        value: '1',
	        label: '第1季度'
	     }, {
	        value: '2',
	        label: '第2季度'
	     }, {
	        value: '3',
	        label: '第3季度'
	     }, {
	        value: '4',
	        label: '第4季度'
	     }];
         //考核状态
	     $scope.stateSelect = [
         {
	        value: '0',
	        label: '未考核'
	     },{
	        value: '1',
	        label: '考核中'
	     }];
         $scope.stateMap = {
	         '0': '未考核',
             '1': '考核中'
	     };
         //考核类型
	     $scope.checkTypeSelect = [{
	        value: '0',
	        label: '结项考核'
	     }, {
	        value: '1',
	        label: '二次考核'
	     }];
         $scope.checkTypeMap = {
	        '0': '结项考核',
	        '1': '二次考核'
	     };
	     //评价状态
	     $scope.evaluateStateMap = {
             '0': '待评价',
             '1': '取消',
             '2': '已评价',
             '3': '已存档'
	     }
	     //结算状态
	     $scope.settleSelect = [{
	        'value':'1',
	        'label':'未结算'
	     },{
	        'value':'2',
	        'label':'结算申请'
	     },{
            'value':'3',
            'label':'已结算'
         }];
         var periodMap = {
             '5':'年度',
             '6':'上半年',
             '7':'下半年',
             '1':'Q1',
             '2':'Q2',
             '3':'Q3',
             '4':'Q4'
         };
         //设置列表的高度
         setDivHeight();
         //窗体大小变化时重新计算高度
         $(window).resize(setDivHeight);
         //初始化查询
         initPages();
         //获取数据
         $scope.getData = getData;
         getData();
         //判断按钮是否具有权限
         $scope.management=false;
         $scope.monitor=false;
         getButtonPermission();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */
        /**
         * 重置
         */
        $scope.reset = function() {
            $scope.formRefer = {};
            if($scope.flag!=null){
                //默认当前年度
                $scope.formRefer.year = inform.format(new Date(),"yyyy");
            }
        }
        /**
         * 设置列表的高度
         */
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (150 + 185);
            $("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight - 50);
        }
        /**
         * 初始化
         */
        function initPages() {
        	//获取产品线
            $scope.projectLine = [];
            comService.queryEffectiveParam('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                if (data.data) {
                    $scope.projectLine = data.data;
                }
            });
            //获取部门信息
          	$scope.departmentSelect = [];
          	comService.getOrgChildren('D010053').then(function(data) {
      			$scope.departmentSelect = comService.getDepartment(data.data);
            });
            //获取员工信息
            $scope.staffList = [];
            $scope.nameMap={};
            comService.getStaffList().then(function(data) {
                $scope.staffList = data.data;
                angular.forEach($scope.staffList, function (staff, index) {
                    $scope.nameMap[staff.loginName] = staff.employeeName;
                });
            });
        }
        /**
         * 获取kpi管理信息
         */
        function getData(pageNum) {
            var urlData = {
                'productLine': $scope.formRefer.productLine,
                'department': $scope.formRefer.department,
                'cname': $scope.formRefer.cname,
                'pm':$scope.formRefer.pm,
                'year':$scope.formRefer.year,
                'quarter':$scope.formRefer.quarter,
                'type':$scope.flag==null?'0':'1',
                'state':$scope.formRefer.state,
                'checkType':$scope.formRefer.checkType,
                'settle':$scope.formRefer.settle,
                'currentPage': pageNum,
                'pageSize': $scope.pages.size,
            };
            if ($scope.flag==='managemet'){
                getTeamManagement(urlData);
            } else {
                getMainData(urlData);
            }
        }
        /**
         * 获取团队管理信息
         */
        function getTeamManagement(urlData){
            kpiManagemetService.getTeamManagement(urlData).then(function (data) {getDataReturn(data)},function () {
                inform.common(Trans("tip.requestError"));
            });
        }
        /**
         * 获取项目管理信息与团队考核监控
         */
        function getMainData(urlData){
            kpiManagemetService.getData(urlData).then(function (data) {getDataReturn(data)},function () {
                inform.common(Trans("tip.requestError"));
            });
        }
        /**
         * 获取查询数据后的回调用方法
         */
        function getDataReturn(data) {
            if (data.code === AgreeConstant.code) {
                //信息
                $scope.infoList = data.data.list;
                // 分页信息设置
                $scope.pages.total = data.data.total;           // 页面数据总数
                $scope.pages.star = data.data.startRow;         // 页面起始数
                $scope.pages.end = data.data.endRow;            // 页面结束数
                $scope.pages.pageNum = data.data.pageNum;       //页号
            } else {
                inform.common(data.message);
            }
        }
        /**
         * 获取按钮权限
         */
        function getButtonPermission(){
        	var buttons = {
            	'Button-kpiManagement-management':'management',
                'Button-kpiManagement-monitor':'monitor'
            };
            var urlData = {
                'userId':LocalCache.getSession("userId"),
                'parentPermission':'ButtonKpiManagement',
                'buttons':buttons
            };
           comService.getButtonPermission(urlData,$scope);
        }
        /**
         * 详情方法
         */
        $scope.toDetails = function (m){

            //名称、领导、时间 放缓存
            var topInfo = {
                'name':m.cname,
                'pm':m.pm,
                'score':m.score,
                //团队周期
                'period':m.year+'-'+periodMap[m.quarter],
                'state':m.state
            }
            LocalCache.setObject('kpiInfo_topInfo', topInfo);
            //查询条件放缓存
            LocalCache.setObject('kpiManagement_formRefer',$scope.formRefer);
            if ($scope.flag==null){
            //项目
                $state.go("app.office.projectKpiDetail", {kpiId: m.kpiId});
            } else {
            //团队
                $state.go("app.office.teamKpiDetail", {kpiId: m.kpiId});
            }
        }
        /**
         * 权重维护方法
         */
        $scope.toManagementDetails = function (m){
            if (m.state!=='0'){
                inform.common(m.cname+'属于考核中，不允许修改权重。');
                return;
            }
            //名称、领导、时间 放缓存
            var topInfo = {
                'name':m.cname,
                'pm':m.pm,
                //团队周期
                'period':m.year+'-'+periodMap[m.quarter]
            }
            LocalCache.setObject('kpiInfo_topInfo', topInfo);
            //查询条件放缓存
            LocalCache.setObject('kpiManagement_formRefer',$scope.formRefer);
            if ($scope.flag==null){
            //项目
                $state.go("app.office.projectKpiManagementDetail", {kpiId: m.kpiId});
            } else {
            //团队
                $state.go("app.office.teamKpiManagementDetail", {kpiId: m.kpiId});
            }
        }

        /**
         * 评价人信息返现
         */
        $scope.setAppraiser = function (m){
            $scope.appraiser.projectId = m.projectId;
            $scope.appraiser.kpiId = m.kpiId;
            $scope.appraiser.customer = m.customer;
            $scope.appraiser.teamAbility = m.teamAbility;
            $scope.appraiser.course = m.course;
            $scope.appraiser.program = m.program;
            $scope.appraiser.values = m.values;
            $scope.appraiser.bugAbility = m.bugAbility;
        }
        /**
         * 评价人维护
         */
        $scope.saveAppraiser = function (){
            kpiManagemetService.saveAppraiser($scope.appraiser).then(function (data) {
                $("#appraiser_maintain").modal('hide');
                getData($scope.pages.pageNum);
                inform.common(data.message);
            },
            function () {
                inform.common(Trans("tip.requestError"));
            });
        }
        /**
         * 全选函数
         */
        $scope.selectAll = function () {
            if ($scope.select_all) {
                $scope.kpiSelected = [];
                angular.forEach($scope.infoList, function (i) {
                    i.checked = true;
                    $scope.kpiSelected.push(i);
                });
                console.log($scope.kpiSelected.length);
            } else {
                angular.forEach($scope.infoList, function (i) {
                    i.checked = false;
                });
                $scope.kpiSelected = [];
            }
        }

        /**
         * 单选项目
         */
        $scope.selectOne = function (i) {
            $scope.select_all = false;
            var index = $scope.kpiSelected.indexOf(i);
            if (index === -1 && i.checked) {
                $scope.kpiSelected.push(i);
            } else if (index !== -1 && !i.checked) {
                $scope.kpiSelected.splice(index, 1);
            }
        };

        /**
         * 发起考核
         */
         $scope.startAssessment = function (list,flag) {
            var kpiIds = [];
            for (var i=0;i<list.length;i++){
                var one = list[i];
                if ($scope.flag==null && (!(one.customer && one.course && one.program && one.values))){
                    inform.common(one.cname+'评价人未维护，请检查。');
                    return;
                }
                if ($scope.flag==null && one.settle==='未结算'){
                    inform.common(one.cname+'未结算，请检查。');
                    return;
                }
                if ($scope.flag!=null && (!(one.teamAbility && one.course && one.program && one.values))){
                    inform.common(one.cname+'评价人未维护，请检查。');
                    return;
                }
                if (one.state!=='0'){
                    inform.common(one.cname+'属于考核中，请检查。');
                    return;
                }
                kpiIds.push(one.kpiId)
            }
            var urlData = {
               'kpiIds' : kpiIds,
               'type' : $scope.flag==null?'0':'1'
            }
            kpiManagemetService.startAssessment(urlData).then(function (data) {returnThenGetData(data)},function () {
                inform.common(Trans("tip.requestError"));
            });
        };
        /**
         * 结束考核
         */
         $scope.saveAssessment = function (m) {
            if ($scope.flag==null && (!(m.customerState==='2' && m.courseState==='2' && m.programState==='2' && m.valuesState==='2'))){
                inform.common(m.cname+'评价未完成，请检查。');
                return;
            }
            if ($scope.flag!=null && (!(m.teamAilityState==='2' && m.courseState==='2' && m.programState==='2' && m.valuesState==='2'))){
                inform.common(m.cname+'评价未完成，请检查。');
                return;
            }
            var urlData = {
                'kpiId' : m.kpiId,
                'projectId' : m.projectId
            }
            kpiManagemetService.saveAssessment(urlData).then(function (data) {returnThenGetData(data)},function () {
                inform.common(Trans("tip.requestError"));
            });
        };
        /**
         * 取消考核
         */
         $scope.cancelAssessment = function (m) {
            if(m.state!=='1'){
                inform.common(m.cname+'属于未考核，无法取消，请检查。');
                return;
            }
            var urlData = {
                'kpiId' : m.kpiId,
                'type' : $scope.flag==null?'0':'1'
            }
            kpiManagemetService.cancelAssessment(urlData).then(function (data) {returnThenGetData(data)}, function () {
                inform.common(Trans("tip.requestError"));
            });
        };
        /**
         * 操作后重新获取数据
         */
         function returnThenGetData (data) {
            if (data.code === AgreeConstant.code) {
                getData($scope.pages.pageNum);
            }
            inform.common(data.message);
         }
	     /**
          * *************************************************************
          *              方法声明部分                                 结束
          * *************************************************************
          */
         }
    ]);
})();
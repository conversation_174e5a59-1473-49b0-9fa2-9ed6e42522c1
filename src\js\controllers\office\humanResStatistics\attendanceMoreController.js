
(function () {
    app.controller("attendanceMoreController", [ '$scope','$rootScope','$stateParams','$state','LocalCache','inform','Trans','comService','attendanceStatisticsService','AgreeConstant','$modal',
        function ($scope,$rootScope, $stateParams,$state,LocalCache,inform,Trans,comService,attendanceStatisticsService,AgreeConstant,$modal) {

    		$scope.formRefer = {};
            //标记数据获取进度
            $scope.flag=0;
            //是否点击了查询按钮
            $scope.selectFlag;
    		$scope.resetParam = resetParam;
    		resetParam();
            //初始化数据
            initData();
            $scope.getData = getData;


           /**
            * 当窗体大小变化时，修改图例大小
            */
           window.addEventListener("resize", function () {
               if ($scope.currentXiYanWorkStrengthChart) { $scope.currentXiYanWorkStrengthChart.resize(); }
               if ($scope.currentOneWorkStrengthChart) { $scope.currentOneWorkStrengthChart.resize(); }
               if ($scope.currentTwoWorkStrengthChart) { $scope.currentTwoWorkStrengthChart.resize(); }
               if ($scope.currentThreeWorkStrengthChart) { $scope.currentThreeWorkStrengthChart.resize(); }
               if ($scope.currentFourWorkStrengthChart) { $scope.currentFourWorkStrengthChart.resize(); }
           });

            function timeout(){
                //表示图表信息全部获取完成
                if(5 === $scope.flag){
                    setTimeout(eChartForWorkStrength,500);
                }
            }
            function initData(){
                //初始化部门
				$scope.departmentShowList = [];
				$scope.departmentMap = {};
                comService.getOrgChildren('D010053').then(function(data) {
                    var department = comService.getDepartment(data.data);
                    angular.forEach(department, function (i) {
                        $scope.departmentMap[i.orgName]=i.orgCode;
                        if(i.orgCode !=='D010053'){
                            $scope.departmentShowList.push(i);
                        }
                    });
                    getData();
                });
            }
            function resetParam(){
                var date = new Date();
                var endDate = date.setMonth(date.getMonth() - 1);
                $scope.formRefer.endTime = inform.format(endDate,"yyyy-MM");
                var startDate = date.setMonth(date.getMonth() - 5);
                $scope.formRefer.startTime = inform.format(startDate,"yyyy-MM");
            }

            //查询当前团队的首页信息
            function getData(flag){
                $scope.selectFlag=flag;
                timeout();
                //系研工作强度统计
                $scope.xiYanWorkStrengthInfo=[];
                getDepartmentWorkStrengthInfo(null,xiYanWorkStrengthData);
                //部门工作强度统计--平台研究室
                $scope.oneWorkStrengthInfo=[];
                getDepartmentWorkStrengthInfo('平台研究室',oneWorkStrengthData);
                //部门工作强度统计--应用研究室
                $scope.twoWorkStrengthInfo=[];
                getDepartmentWorkStrengthInfo('应用研究室',twoWorkStrengthData);
                //部门工作强度统计--驱动研究室
                $scope.threeWorkStrengthInfo=[];
                getDepartmentWorkStrengthInfo('驱动研究室',threeWorkStrengthData);
                //部门工作强度统计--软测研究室
                $scope.fourWorkStrengthInfo=[];
                getDepartmentWorkStrengthInfo('软测研究室',fourWorkStrengthData);
            }

            //部门工作强度统计
            function getDepartmentWorkStrengthInfo(dept,backFunction){
                var urlData={
                    'statisDateStart':$scope.formRefer.startTime,
                    'statisDateEnd':$scope.formRefer.endTime
                }
                if(dept){
                    var deptCodeList = [];
                    var deptCode = $scope.departmentMap[dept];
                    deptCodeList.push(deptCode);
                    urlData.deptCodeList=deptCodeList;
                }
                attendanceStatisticsService.getWorkIntensityInfoGroupByTime(urlData).then(backFunction,
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            //处理系研整体工作强度统计
            function xiYanWorkStrengthData(data){
                if (data.code===AgreeConstant.code) {
                     $scope.xiYanWorkStrengthInfo = data.data;
                     if($scope.selectFlag){
                         eChartShowForWorkStrength($scope.currentXiYanWorkStrengthChart,$scope.xiYanWorkStrengthInfo,'系研工作强度统计');
                     }
                } else {
                    inform.common(data.message);
                }
                $scope.flag++;
                timeout();
            }
            //处理部门工作强度统计--平台研究室
            function oneWorkStrengthData(data){
                if (data.code===AgreeConstant.code) {
                     $scope.oneWorkStrengthInfo = data.data;
                     if($scope.selectFlag){
                         eChartShowForWorkStrength($scope.currentOneWorkStrengthChart,$scope.oneWorkStrengthInfo,'工作强度统计--平台研究室');
                     }
                } else {
                    inform.common(data.message);
                }
                $scope.flag++;
                timeout();
            }
            //处理部门工作强度统计--应用研究室
            function twoWorkStrengthData(data){
                if (data.code===AgreeConstant.code) {
                     $scope.twoWorkStrengthInfo = data.data;
                     if($scope.selectFlag){
                         eChartShowForWorkStrength($scope.currentTwoWorkStrengthChart,$scope.twoWorkStrengthInfo,'工作强度统计--应用研究室');
                     }
                } else {
                    inform.common(data.message);
                }
                $scope.flag++;
                timeout();
            }
            //处理部门工作强度统计--驱动研究室
            function threeWorkStrengthData(data){
                if (data.code===AgreeConstant.code) {
                     $scope.threeWorkStrengthInfo = data.data;
                     if($scope.selectFlag){
                         eChartShowForWorkStrength($scope.currentThreeWorkStrengthChart,$scope.threeWorkStrengthInfo,'工作强度统计--驱动研究室');
                     }
                } else {
                    inform.common(data.message);
                }
                $scope.flag++;
                timeout();
            }
            //处理部门工作强度统计--软测研究室
            function fourWorkStrengthData(data){
                if (data.code===AgreeConstant.code) {
                     $scope.fourWorkStrengthInfo = data.data;
                     if($scope.selectFlag){
                         eChartShowForWorkStrength($scope.currentFourWorkStrengthChart,$scope.fourWorkStrengthInfo,'工作强度统计--软测研究室');
                     }
                } else {
                    inform.common(data.message);
                }
                $scope.flag++;
                timeout();
            }

            //工作强度图表创建
            function eChartForWorkStrength(){
                $scope.currentXiYanWorkStrengthChart = echarts.init(document.getElementById('xiYanWorkStrengthChart'));
                $scope.currentOneWorkStrengthChart = echarts.init(document.getElementById('oneWorkStrengthChart'));
                $scope.currentTwoWorkStrengthChart = echarts.init(document.getElementById('twoWorkStrengthChart'));
                $scope.currentThreeWorkStrengthChart = echarts.init(document.getElementById('threeWorkStrengthChart'));
                $scope.currentFourWorkStrengthChart = echarts.init(document.getElementById('fourWorkStrengthChart'));

                eChartShowForWorkStrength($scope.currentXiYanWorkStrengthChart,$scope.xiYanWorkStrengthInfo,'系研工作强度统计');
                eChartShowForWorkStrength($scope.currentOneWorkStrengthChart,$scope.oneWorkStrengthInfo,'工作强度统计--平台研究室');
                eChartShowForWorkStrength($scope.currentTwoWorkStrengthChart,$scope.twoWorkStrengthInfo,'工作强度统计--应用研究室');
                eChartShowForWorkStrength($scope.currentThreeWorkStrengthChart,$scope.threeWorkStrengthInfo,'工作强度统计--驱动研究室');
                eChartShowForWorkStrength($scope.currentFourWorkStrengthChart,$scope.fourWorkStrengthInfo,'工作强度统计--软测研究室');
            }

            //工作强度图表显示
            function eChartShowForWorkStrength(currentChart,data,title){
                var xData=[];
                var workHourDelayNormalAverage=[];
                var workHourDelayTotalAverage=[];
                var workIntensityNormal=[];
                var workIntensityTotal=[];
                angular.forEach(data, function (workHourDelay) {
                    xData.push(workHourDelay.statisTargetName);
                    workHourDelayNormalAverage.push(workHourDelay.workHourDelayNormalAverage);
                    workHourDelayTotalAverage.push(workHourDelay.workHourDelayTotalAverage);
                    workIntensityNormal.push(workHourDelay.workIntensityNormal);
                    workIntensityTotal.push(workHourDelay.workIntensityTotal);
                });

                var option = {
                  title:{
                      text:title,
                      textStyle:{
                          fontSize: 12,
                          color: '#333'
                      }
                  },
                  tooltip: {
                    trigger: 'axis',
                    formatter:formatterCall,
                    axisPointer: {
                      type: 'cross',
                      crossStyle: {
                        color: '#999'
                      }
                    }
                  },
                  legend: {
                    data: ['平日延时打卡工时', '总延时打卡工时', '平日工作强度','总工作强度'],
                    textStyle:{
                        fontSize: 10,
                    },
                    top:16
                  },
                  xAxis: [
                    {
                      type: 'category',
                      data:xData,
                      axisPointer: {
                        type: 'shadow'
                      },
                      axisLabel:{
                        rotate:30
                      }
                    }
                  ],
                  yAxis: [
                    {
                      type: 'value',
                      name: '小时数',
                      nameTextStyle:{
                        padding:[0,0,0,-58]
                      },
                      axisLabel: {
                        formatter: '{value}'
                      }
                    },
                    {
                      type: 'value',
                      name: '工作强度',
                      min:100,
                      nameTextStyle:{
                        padding:[0,0,0,55]
                      },
                      axisLabel: {
                        formatter: '{value}%'
                      }
                    }
                  ],
                  series: [
                    {
                      name: '平日延时打卡工时',
                      type: 'bar',
                      label: {
                        show: true,
                      },
                      data: workHourDelayNormalAverage
                    },
                    {
                      name: '总延时打卡工时',
                      type: 'bar',
                      label: {
                        show: true,
                      },
                      data: workHourDelayTotalAverage

                    },
                    {
                      name: '平日工作强度',
                      type: 'line',
                      yAxisIndex: 1,
                      label:{
                          show:true,
                          formatter: '{c}%'
                      },
                      data: workIntensityNormal
                    },
                    {
                      name: '总工作强度',
                      type: 'line',
                      yAxisIndex: 1,
                      label:{
                          show:true,
                          formatter: '{c}%'
                      },
                      data: workIntensityTotal
                    }

                  ]
                };

                currentChart.setOption(option, true);
            }
        //自定义鼠标悬浮样式
		 function formatterCall (params, ticket, callback) {
            var htmlStr = '';
            for(var i=0;i<params.length;i++){
                var param = params[i];
                var xName = param.name;//x轴的名称
                var seriesName = param.seriesName;//图例名称
                var value = param.value;//y轴值
                var color = param.color;//图例颜色
                if(i===0){
                    htmlStr += xName + '<br/>';//x轴的名称
                }
                htmlStr +='<div>';
                //为了保证和原来的效果一样，这里自己实现了一个点的效果
                htmlStr += '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:'+color+';"></span>';
                //圆点后面显示的文本
                htmlStr += seriesName;
                htmlStr += '<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">';
                if('平日工作强度'===seriesName || '总工作强度'===seriesName){
                    htmlStr += +value+ '%';
                }else{
                    htmlStr += +value;
                }

                htmlStr += '</span>';
                htmlStr += '</div>';
            }
            return htmlStr;
         }
        //返回
        $scope.goBack = function(){
            $state.go('app.office.humanResStatistics',{type:2});
        }
        //开始时间
        $scope.openSearchOnboardTime = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openSearchOnboardTime1 = true;
            $scope.openStopOnboardTime1 = false;
        };
        //截止时间
         $scope.openStopOnboardTime = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openSearchOnboardTime1 = false;
            $scope.openStopOnboardTime1 = true;
        };

      }]);
})();

(function() {
    'use strict';
  app.factory('leakService', leakService);
  leakService.$inject=["HttpService",'$rootScope'];

  function leakService(HttpService,$rootScope){
    
    var service={
    	getData:getData,
    	createExcel:createExcel,
    	getLineData:getLineData
    };
    return service;
    /**
     * 查询所有项目的相关所有信息
     * @param urlData 'startTime':$scope.start,//格式化后的开始时间
					  'endTime':$scope.end,//格式化后的结束时间
					  'name':$scope.formRefer.projectname,//项目名称
					  'currentPage':pageNum,//当前页数
	          		  'pageSize':$scope.pages.size//每页显示条数
     */
    function getData(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'bug/getData', urlData);
    }
    function getLineData(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'bug/getLineData', urlData);
    }
    /**
     * 将前台表格内容进行下载
     *  @param urlData projectname	// 项目名称
					  startTime	//开始时间
					  endTime	//结束时间
     */
    function createExcel(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'bug/createExcel', urlData);
    }
  }
})();

/*!
 * ZUI: Standard edition - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
/*! Some code copy from Bootstrap v3.0.0 by @fat and @mdo. (Copyright 2013 Twitter, Inc. Licensed under http://www.apache.org/licenses/)*/
!function(t,e,i){"use strict";if("undefined"==typeof t)throw new Error("ZUI requires jQuery");t.zui||(t.zui=function(e){t.isPlainObject(e)&&t.extend(t.zui,e)});var n={all:-1,left:0,middle:1,right:2},o=0;t.zui({uuid:function(t){var e=1e5*(Date.now()-1580890015292)+10*Math.floor(1e4*Math.random())+o++%10;return t?e:e.toString(36)},callEvent:function(t,e,n){if("function"==typeof t){n!==i&&(t=t.bind(n));var o=t(e);return e&&(e.result=o),!(o!==i&&!o)}return 1},strCode:function(t){var e=0;if("string"!=typeof t&&(t=String(t)),t&&t.length)for(var i=0;i<t.length;++i)e+=(i+1)*t.charCodeAt(i);return e},getMouseButtonCode:function(t){return"number"!=typeof t&&(t=n[t]),t!==i&&null!==t||(t=-1),t},defaultLang:"en",clientLang:function(){var i,n=e.config;if("undefined"!=typeof n&&n.clientLang&&(i=n.clientLang),!i){var o=t("html").attr("lang");i=o?o:navigator.userLanguage||navigator.userLanguage||t.zui.defaultLang}return i.replace("-","_").toLowerCase()},langDataMap:{},addLangData:function(e,i,n){var o={};n&&i&&e?(o[i]={},o[i][e]=n):e&&i&&!n?(n=i,t.each(n,function(t){o[t]={},o[t][e]=n[t]})):!e||i||n||t.each(n,function(e){var i=n[e];t.each(i,function(t){o[t]||(o[t]={}),o[t][e]=i[t]})}),t.extend(!0,t.zui.langDataMap,o)},getLangData:function(e,i,n){if(!arguments.length)return t.extend({},t.zui.langDataMap);if(1===arguments.length)return t.extend({},t.zui.langDataMap[e]);if(2===arguments.length){var o=t.zui.langDataMap[e];return o?i?o[i]:o:{}}if(3===arguments.length){i=i||t.zui.clientLang();var o=t.zui.langDataMap[e],a=o?o[i]:{};return t.extend(!0,{},n[i]||n.en||n.zh_cn,a)}return null},lang:function(){return arguments.length&&t.isPlainObject(arguments[arguments.length-1])?t.zui.addLangData.apply(null,arguments):t.zui.getLangData.apply(null,arguments)},_scrollbarWidth:0,checkBodyScrollbar:function(){if(document.body.clientWidth>=e.innerWidth)return 0;if(!t.zui._scrollbarWidth){var i=document.createElement("div");i.className="scrollbar-measure",document.body.appendChild(i),t.zui._scrollbarWidth=i.offsetWidth-i.clientWidth,document.body.removeChild(i)}return t.zui._scrollbarWidth},fixBodyScrollbar:function(){if(t.zui.checkBodyScrollbar()){var e=t("body"),i=parseInt(e.css("padding-right")||0,10);return t.zui._scrollbarWidth&&e.css({paddingRight:i+t.zui._scrollbarWidth,overflowY:"hidden"}),!0}},resetBodyScrollbar:function(){t("body").css({paddingRight:"",overflowY:""})}}),t.fn.callEvent=function(e,n,o){var a=t(this),r=e.indexOf(".zui."),s=r<0?e:e.substring(0,r),l=t.Event(s,n);if(o===i&&r>0&&(o=a.data(e.substring(r+1))),o&&o.options){var d=o.options[s];"function"==typeof d&&(l.result=t.zui.callEvent(d,l,o))}return a.trigger(l),l},t.fn.callComEvent=function(t,e,n){n===i||Array.isArray(n)||(n=[n]);var o,a=this;a.trigger(e,n);var r=t.options[e];return r&&(o=r.apply(t,n)),o}}(jQuery,window,void 0),function(t){"use strict";t.fn.fixOlPd=function(e){return e=e||10,this.each(function(){var i=t(this);i.css("paddingLeft",Math.ceil(Math.log10(i.children().length))*e+10)})},t(function(){t(".ol-pd-fix,.article ol").fixOlPd()})}(jQuery),+function(t){"use strict";var e=function(i,n){this.$element=t(i),this.options=t.extend({},e.DEFAULTS,n),this.isLoading=!1};e.DEFAULTS={loadingText:"loading..."},e.prototype.setState=function(t){var e="disabled",i=this.$element,n=i.is("input")?"val":"html",o=i.data();t+="Text",o.resetText||i.data("resetText",i[n]()),i[n](o[t]||this.options[t]),setTimeout(function(){"loadingText"==t?(this.isLoading=!0,i.addClass(e).attr(e,e)):this.isLoading&&(this.isLoading=!1,i.removeClass(e).removeAttr(e))}.bind(this),0)},e.prototype.toggle=function(){var t=!0,e=this.$element.closest('[data-toggle="buttons"]');if(e.length){var i=this.$element.find("input");"radio"==i.prop("type")&&(i.prop("checked")&&this.$element.hasClass("active")?t=!1:e.find(".active").removeClass("active")),t&&i.prop("checked",!this.$element.hasClass("active")).trigger("change")}t&&this.$element.toggleClass("active")};var i=t.fn.button;t.fn.button=function(i){return this.each(function(){var n=t(this),o=n.data("zui.button"),a="object"==typeof i&&i;o||n.data("zui.button",o=new e(this,a)),"toggle"==i?o.toggle():i&&o.setState(i)})},t.fn.button.Constructor=e,t.fn.button.noConflict=function(){return t.fn.button=i,this},t(document).on("click.zui.button.data-api","[data-toggle^=button]",function(e){var i=t(e.target);i.hasClass("btn")||(i=i.closest(".btn")),i.button("toggle"),e.preventDefault()})}(jQuery),+function(t){"use strict";var e='[data-dismiss="alert"]',i="zui.alert",n=function(i){t(i).on("click",e,this.close)};n.prototype.close=function(e){function n(){r.trigger("closed."+i).remove()}var o=t(this),a=o.attr("data-target");a||(a=o.attr("href"),a=a&&a.replace(/.*(?=#[^\s]*$)/,""));var r=t(a);e&&e.preventDefault(),r.length||(r=o.hasClass("alert")?o:o.parent()),r.trigger(e=t.Event("close."+i)),e.isDefaultPrevented()||(r.removeClass("in"),t.support.transition&&r.hasClass("fade")?r.one(t.support.transition.end,n).emulateTransitionEnd(150):n())};var o=t.fn.alert;t.fn.alert=function(e){return this.each(function(){var o=t(this),a=o.data(i);a||o.data(i,a=new n(this)),"string"==typeof e&&a[e].call(o)})},t.fn.alert.Constructor=n,t.fn.alert.noConflict=function(){return t.fn.alert=o,this},t(document).on("click."+i+".data-api",e,n.prototype.close)}(window.jQuery),function(t,e){"use strict";var i="zui.pager",n={page:1,recTotal:0,recPerPage:10},o={zh_cn:{pageOfText:"第 {0} 页",prev:"上一页",next:"下一页",first:"第一页",last:"最后一页","goto":"跳转",pageOf:"第 <strong>{page}</strong> 页",totalPage:"共 <strong>{totalPage}</strong> 页",totalCount:"共 <strong>{recTotal}</strong> 项",pageSize:"每页 <strong>{recPerPage}</strong> 项",itemsRange:"第 <strong>{start}</strong> ~ <strong>{end}</strong> 项",pageOfTotal:"第 <strong>{page}</strong>/<strong>{totalPage}</strong> 页"},zh_tw:{pageOfText:"第 {0} 頁",prev:"上一頁",next:"下一頁",first:"第一頁",last:"最後一頁","goto":"跳轉",pageOf:"第 <strong>{page}</strong> 頁",totalPage:"共 <strong>{totalPage}</strong> 頁",totalCount:"共 <strong>{recTotal}</strong> 項",pageSize:"每頁 <strong>{recPerPage}</strong> 項",itemsRange:"第 <strong>{start}</strong> ~ <strong>{end}</strong> 項",pageOfTotal:"第 <strong>{page}</strong>/<strong>{totalPage}</strong> 頁"},en:{pageOfText:"Page {0}",prev:"Prev",next:"Next",first:"First",last:"Last","goto":"Goto",pageOf:"Page <strong>{page}</strong>",totalPage:"<strong>{totalPage}</strong> pages",totalCount:"Total: <strong>{recTotal}</strong> items",pageSize:"<strong>{recPerPage}</strong> per page",itemsRange:"From <strong>{start}</strong> to <strong>{end}</strong>",pageOfTotal:"Page <strong>{page}</strong> of <strong>{totalPage}</strong>"}},a=function(e,n){var r=this;r.name=i,r.$=t(e),n=r.options=t.extend({},a.DEFAULTS,this.$.data(),n),r.langName=n.lang||t.zui.clientLang(),r.lang=t.zui.getLangData(i,r.langName,o),r.state={},r.set(n.page,n.recTotal,n.recPerPage,!0),r.$.on("click",".pager-goto-btn",function(){var e=t(this).closest(".pager-goto"),i=parseInt(e.find(".pager-goto-input").val());NaN!==i&&r.set(i)}).on("click",".pager-item",function(){var e=t(this).data("page");"number"==typeof e&&e>0&&r.set(e)}).on("click",".pager-size-menu [data-size]",function(){var e=t(this).data("size");"number"==typeof e&&e>0&&r.set(-1,-1,e)})};a.prototype.set=function(e,i,o,a){var r=this;"object"==typeof e&&null!==e&&(o=e.recPerPage,i=e.recTotal,e=e.page);var s=r.state;s||(s=t.extend({},n));var l=t.extend({},s);return"number"==typeof o&&o>0&&(s.recPerPage=o),"number"==typeof i&&i>=0&&(s.recTotal=i),"number"==typeof e&&e>=0&&(s.page=e),s.totalPage=s.recTotal&&s.recPerPage?Math.ceil(s.recTotal/s.recPerPage):1,s.page=Math.max(0,Math.min(s.page,s.totalPage)),s.pageRecCount=s.recTotal,s.page&&s.recTotal&&(s.page<s.totalPage?s.pageRecCount=s.recPerPage:s.page>1&&(s.pageRecCount=s.recTotal-s.recPerPage*(s.page-1))),s.skip=s.page>1?(s.page-1)*s.recPerPage:0,s.start=s.skip+1,s.end=s.skip+s.pageRecCount,s.prev=s.page>1?s.page-1:0,s.next=s.page<s.totalPage?s.page+1:0,r.state=s,a||l.page===s.page&&l.recTotal===s.recTotal&&l.recPerPage===s.recPerPage||r.$.callComEvent(r,"onPageChange",[s,l]),r.render()},a.prototype.createLinkItem=function(i,n,o){var a=this;n===e&&(n=i);var r=t('<a title="'+a.lang.pageOfText.format(i)+'" class="pager-item" data-page="'+i+'"/>').attr("href",i?a.createLink(i,a.state):"###").html(n);return o||(r=t("<li />").append(r).toggleClass("active",i===a.state.page).toggleClass("disabled",!i||i===a.state.page)),r},a.prototype.createNavItems=function(t){var i=this,n=i.$,o=i.state,a=o.totalPage,r=o.page,s=function(t,o){if(t===!1)return void n.append(i.createLinkItem(0,o||i.options.navEllipsisItem));o===e&&(o=t);for(var a=t;a<=o;++a)n.append(i.createLinkItem(a))};t===e&&(t=i.options.maxNavCount||10),s(1),a>1&&(a<=t?s(2,a):r<t-2?(s(2,t-2),s(!1),s(a)):r>a-t+2?(s(!1),s(a-t+2,a)):(s(!1),s(r-Math.ceil((t-4)/2),r+Math.floor((t-4)/2)),s(!1),s(a)))},a.prototype.createGoto=function(){var e=this,i=this.state,n=t('<div class="input-group pager-goto" style="width: '+(35+9*(i.page+"").length+25+12*e.lang["goto"].length)+'px"><input value="'+i.page+'" type="number" min="1" max="'+i.totalPage+'" placeholder="'+i.page+'" class="form-control pager-goto-input"><span class="input-group-btn"><button class="btn pager-goto-btn" type="button">'+e.lang["goto"]+"</button></span></div>");return n},a.prototype.createSizeMenu=function(){var e=this,i=this.state,n=t('<ul class="dropdown-menu"></ul>'),o=e.options.pageSizeOptions;"string"==typeof o&&(o=o.split(","));for(var a=0;a<o.length;++a){var r=o[a];"string"==typeof r&&(r=parseInt(r));var s=t('<li><a href="###" data-size="'+r+'">'+r+"</a></li>").toggleClass("active",r===i.recPerPage);n.append(s)}return t('<div class="btn-group pager-size-menu"><button type="button" class="btn dropdown-toggle" data-toggle="dropdown">'+e.lang.pageSize.format(i)+' <span class="caret"></span></button></div>').addClass(e.options.menuDirection).append(n)},a.prototype.createElement=function(e,i,n){var o=this,a=o.createLinkItem.bind(o),r=o.lang;switch(e){case"prev":return a(n.prev,r.prev);case"prev_icon":return a(n.prev,'<i class="icon '+o.options.prevIcon+'"></i>');case"next":return a(n.next,r.next);case"next_icon":return a(n.next,'<i class="icon '+o.options.nextIcon+'"></i>');case"first":return a(1,r.first);case"first_icon":return a(1,'<i class="icon '+o.options.firstIcon+'"></i>');case"last":return a(n.totalPage,r.last);case"last_icon":return a(n.totalPage,'<i class="icon '+o.options.lastIcon+'"></i>');case"space":case"|":return t('<li class="space" />');case"nav":case"pages":return void o.createNavItems();case"total_text":return t(('<div class="pager-label">'+r.totalCount+"</div>").format(n));case"page_text":return t(('<div class="pager-label">'+r.pageOf+"</div>").format(n));case"total_page_text":return t(('<div class="pager-label">'+r.totalPage+"</div>").format(n));case"page_of_total_text":return t(('<div class="pager-label">'+r.pageOfTotal+"</div>").format(n));case"page_size_text":return t(('<div class="pager-label">'+r.pageSize+"</div>").format(n));case"items_range_text":return t(('<div class="pager-label">'+r.itemsRange+"</div>").format(n));case"goto":return o.createGoto();case"size_menu":return o.createSizeMenu();default:return t("<li/>").html(e.format(n))}},a.prototype.createLink=function(i,n){i===e&&(i=this.state.page),n===e&&(n=this.state);var o=this.options.linkCreator;return"string"==typeof o?o.format(t.extend({},n,{page:i})):"function"==typeof o?o(i,n):"#page="+i},a.prototype.render=function(e){var i=this,n=i.state,o=i.options.elementCreator||i.createElement,a=t.isPlainObject(o);e=e||i.elements||i.options.elements,"string"==typeof e&&(e=e.split(",")),i.elements=e,i.$.empty();for(var r=0;r<e.length;++r){var s=t.trim(e[r]),l=a?o[s]||o:o,d=l.call(i,s,i.$,n);d===!1&&(d=i.createElement(s,i.$,n)),d instanceof t&&("LI"!==d[0].tagName&&(d=t("<li/>").append(d)),i.$.append(d))}var c=null;return i.$.children("li").each(function(){var e=t(this),i=!!e.children(".pager-item").length;c?c.toggleClass("pager-item-right",!i):i&&e.addClass("pager-item-left"),c=i?e:null}),c&&c.addClass("pager-item-right"),i.$.callComEvent(i,"onRender",[n]),i},a.DEFAULTS=t.extend({elements:["first_icon","prev_icon","pages","next_icon","last_icon","page_of_total_text","items_range_text","total_text"],prevIcon:"icon-double-angle-left",nextIcon:"icon-double-angle-right",firstIcon:"icon-step-backward",lastIcon:"icon-step-forward",navEllipsisItem:'<i class="icon icon-ellipsis-h"></i>',maxNavCount:10,menuDirection:"dropdown",pageSizeOptions:[10,20,30,50,100]},n),t.fn.pager=function(e){return this.each(function(){var n=t(this),o=n.data(i),r="object"==typeof e&&e;o||n.data(i,o=new a(this,r)),"string"==typeof e&&o[e]()})},a.NAME=i,a.LANG=o,t.fn.pager.Constructor=a,t(function(){t('[data-ride="pager"]').pager()})}(jQuery,void 0),+function(t){"use strict";var e="zui.tab",i=function(e){this.element=t(e)};i.prototype.show=function(){var i=this.element,n=i.closest("ul:not(.dropdown-menu)"),o=i.attr("data-target")||i.attr("data-tab");if(o||(o=i.attr("href"),o=o&&o.replace(/.*(?=#[^\s]*$)/,"")),!i.parent("li").hasClass("active")){var a=n.find(".active:last a")[0],r=t.Event("show."+e,{relatedTarget:a});if(i.trigger(r),!r.isDefaultPrevented()){var s=t(o);this.activate(i.parent("li"),n),this.activate(s,s.parent(),function(){i.trigger({type:"shown."+e,relatedTarget:a})})}}},i.prototype.activate=function(e,i,n){function o(){a.removeClass("active").find("> .dropdown-menu > .active").removeClass("active"),e.addClass("active"),r?(e[0].offsetWidth,e.addClass("in")):e.removeClass("fade"),e.parent(".dropdown-menu")&&e.closest("li.dropdown").addClass("active"),n&&n()}var a=i.find("> .active"),r=n&&t.support.transition&&a.hasClass("fade");r?a.one(t.support.transition.end,o).emulateTransitionEnd(150):o(),a.removeClass("in")};var n=t.fn.tab;t.fn.tab=function(n){return this.each(function(){var o=t(this),a=o.data(e);a||o.data(e,a=new i(this)),"string"==typeof n&&a[n]()})},t.fn.tab.Constructor=i,t.fn.tab.noConflict=function(){return t.fn.tab=n,this},t(document).on("click.zui.tab.data-api",'[data-toggle="tab"], [data-tab]',function(e){e.preventDefault(),t(this).tab("show")})}(window.jQuery),+function(t){"use strict";function e(){var t=document.createElement("bootstrap"),e={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var i in e)if(void 0!==t.style[i])return{end:e[i]};return!1}t.fn.emulateTransitionEnd=function(e){var i=!1,n=this;t(this).one("bsTransitionEnd",function(){i=!0});var o=function(){i||t(n).trigger(t.support.transition.end)};return setTimeout(o,e),this},t(function(){t.support.transition=e(),t.support.transition&&(t.event.special.bsTransitionEnd={bindType:t.support.transition.end,delegateType:t.support.transition.end,handle:function(e){if(t(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}})})}(jQuery),+function(t){"use strict";var e="zui.collapse",i=function(e,n){this.$element=t(e),this.options=t.extend({},i.DEFAULTS,n),this.transitioning=null,this.options.parent&&(this.$parent=t(this.options.parent)),this.options.toggle&&this.toggle()};i.DEFAULTS={toggle:!0},i.prototype.dimension=function(){var t=this.$element.hasClass("width");return t?"width":"height"},i.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var i=t.Event("show."+e);if(this.$element.trigger(i),!i.isDefaultPrevented()){var n=this.$parent&&this.$parent.find(".in");if(n&&n.length){var o=n.data(e);if(o&&o.transitioning)return;n.collapse("hide"),o||n.data(e,null)}var a=this.dimension();this.$element.removeClass("collapse").addClass("collapsing")[a](0),this.transitioning=1;var r=function(){this.$element.removeClass("collapsing").addClass("in")[a]("auto"),this.transitioning=0,this.$element.trigger("shown."+e)};if(!t.support.transition)return r.call(this);var s=t.camelCase(["scroll",a].join("-"));this.$element.one(t.support.transition.end,r.bind(this)).emulateTransitionEnd(350)[a](this.$element[0][s])}}},i.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var i=t.Event("hide."+e);if(this.$element.trigger(i),!i.isDefaultPrevented()){var n=this.dimension();this.$element[n](this.$element[n]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse").removeClass("in"),this.transitioning=1;var o=function(){this.transitioning=0,this.$element.trigger("hidden."+e).removeClass("collapsing").addClass("collapse")};return t.support.transition?void this.$element[n](0).one(t.support.transition.end,o.bind(this)).emulateTransitionEnd(350):o.call(this)}}},i.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()};var n=t.fn.collapse;t.fn.collapse=function(n){return this.each(function(){var o=t(this),a=o.data(e),r=t.extend({},i.DEFAULTS,o.data(),"object"==typeof n&&n);a||o.data(e,a=new i(this,r)),"string"==typeof n&&a[n]()})},t.fn.collapse.Constructor=i,t.fn.collapse.noConflict=function(){return t.fn.collapse=n,this},t(document).on("click."+e+".data-api","[data-toggle=collapse]",function(i){var n,o=t(this),a=o.attr("data-target")||i.preventDefault()||(n=o.attr("href"))&&n.replace(/.*(?=#[^\s]+$)/,""),r=t(a),s=r.data(e),l=s?"toggle":o.data(),d=o.attr("data-parent"),c=d&&t(d);s&&s.transitioning||(c&&c.find('[data-toggle=collapse][data-parent="'+d+'"]').not(o).addClass("collapsed"),o[r.hasClass("in")?"addClass":"removeClass"]("collapsed")),r.collapse(l)})}(window.jQuery),function(t,e){"use strict";var i=1200,n=992,o=768,a=e(t),r=function(){var t=a.width();e("html").toggleClass("screen-desktop",t>=n&&t<i).toggleClass("screen-desktop-wide",t>=i).toggleClass("screen-tablet",t>=o&&t<n).toggleClass("screen-phone",t<o).toggleClass("device-mobile",t<n).toggleClass("device-desktop",t>=n)},s="",l=navigator.userAgent;l.match(/(iPad|iPhone|iPod)/i)?s+=" os-ios":l.match(/android/i)?s+=" os-android":l.match(/Win/i)?s+=" os-windows":l.match(/Mac/i)?s+=" os-mac":l.match(/Linux/i)?s+=" os-linux":l.match(/X11/i)&&(s+=" os-unix"),"ontouchstart"in document.documentElement&&(s+=" is-touchable"),e("html").addClass(s),a.resize(r),r()}(window,jQuery),function(t){"use strict";var e={zh_cn:'您的浏览器版本过低，无法体验所有功能，建议升级或者更换浏览器。 <a href="https://browsehappy.com/" target="_blank" class="alert-link">了解更多...</a>',zh_tw:'您的瀏覽器版本過低，無法體驗所有功能，建議升級或者更换瀏覽器。<a href="https://browsehappy.com/" target="_blank" class="alert-link">了解更多...</a>',en:'Your browser is too old, it has been unable to experience the colorful internet. We strongly recommend that you upgrade a better one. <a href="https://browsehappy.com/" target="_blank" class="alert-link">Learn more...</a>'},i=function(){for(var t=!1,e=11;e>5;e--)if(this.isIE(e)){t=e;break}this.ie=t,this.cssHelper()};i.prototype.cssHelper=function(){var e=this.ie,i=t("html");i.toggleClass("ie",e).removeClass("ie-6 ie-7 ie-8 ie-9 ie-10"),e&&i.addClass("ie-"+e).toggleClass("gt-ie-7 gte-ie-8 support-ie",e>=8).toggleClass("lte-ie-7 lt-ie-8 outdated-ie",e<8).toggleClass("gt-ie-8 gte-ie-9",e>=9).toggleClass("lte-ie-8 lt-ie-9",e<9).toggleClass("gt-ie-9 gte-ie-10",e>=10).toggleClass("lte-ie-9 lt-ie-10",e<10).toggleClass("gt-ie-10 gte-ie-11",e>=11).toggleClass("lte-ie-10 lt-ie-11",e<11)},i.prototype.tip=function(i){var n=t("#browseHappyTip");n.length||(n=t('<div id="browseHappyTip" class="alert alert-dismissable alert-danger-inverse alert-block" style="position: relative; z-index: 99999"><button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button><div class="container"><div class="content text-center"></div></div></div>'),n.prependTo("body")),i||(i=t.zui.getLangData("zui.browser",t.zui.clientLang(),e),"object"==typeof i&&(i=i.tip)),n.find(".content").html(i)},i.prototype.isIE=function(t){if(11===t)return this.isIE11();if(10===t)return this.isIE10();if(!t&&(this.isIE11()||this.isIE10()))return!0;var e=document.createElement("b");return e.innerHTML="<!--[if IE "+(t||"")+"]><i></i><![endif]-->",1===e.getElementsByTagName("i").length},i.prototype.isIE10=function(){return navigator.appVersion.indexOf("MSIE 10")!==-1},i.prototype.isIE11=function(){var t=navigator.userAgent;return t.indexOf("Trident")!==-1&&t.indexOf("rv:11")!==-1},t.zui({browser:new i}),t(function(){t("body").hasClass("disabled-browser-tip")||t.zui.browser.ie&&t.zui.browser.ie<8&&t.zui.browser.tip()})}(jQuery),function(t){"use strict";var e=864e5,i=function(t){return t instanceof Date||("number"==typeof t&&t<1e10&&(t*=1e3),t=new Date(t)),t},n=function(t){return i(t).getTime()},o=function(t,e){t=i(t),void 0===e&&(e="yyyy-MM-dd hh:mm:ss");var n={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds(),"q+":Math.floor((t.getMonth()+3)/3),"S+":t.getMilliseconds()};/(y+)/i.test(e)&&(e=e.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));for(var o in n)new RegExp("("+o+")").test(e)&&(e=e.replace(RegExp.$1,1==RegExp.$1.length?n[o]:("00"+n[o]).substr((""+n[o]).length)));return e},a=function(t,e){return t.setTime(t.getTime()+e),t},r=function(t,i){return a(t,i*e)},s=function(t){return new Date(i(t).getTime())},l=function(t){return t%4===0&&t%100!==0||t%400===0},d=function(t,e){return[31,l(t)?29:28,31,30,31,30,31,31,30,31,30,31][e]},c=function(t){return d(t.getFullYear(),t.getMonth())},p=function(t){return t.setHours(0),t.setMinutes(0),t.setSeconds(0),t.setMilliseconds(0),t},u=function(t,e){var i=t.getDate();return t.setDate(1),t.setMonth(t.getMonth()+e),t.setDate(Math.min(i,c(t))),t},f=function(t,e){e=e||1;for(var i=new Date(t.getTime());i.getDay()!=e;)i=r(i,-1);return p(i)},h=function(t,e){return t.toDateString()===e.toDateString()},g=function(t,e){var i=f(t),n=r(s(i),7);return e>=i&&e<n},m=function(t,e){return t.getFullYear()===e.getFullYear()},v={formatDate:o,createDate:i,date:{ONEDAY_TICKS:e,create:i,getTimestamp:n,format:o,addMilliseconds:a,addDays:r,cloneDate:s,isLeapYear:l,getDaysInMonth:d,getDaysOfThisMonth:c,clearTime:p,addMonths:u,getLastWeekday:f,isSameDay:h,isSameWeek:g,isSameYear:m}};t.$&&t.$.zui?$.zui(v):t.dateHelper=v.date,t.noDatePrototypeHelper||(Date.ONEDAY_TICKS=e,Date.prototype.format||(Date.prototype.format=function(t){return o(this,t)}),Date.prototype.addMilliseconds||(Date.prototype.addMilliseconds=function(t){return a(this,t)}),Date.prototype.addDays||(Date.prototype.addDays=function(t){return r(this,t)}),Date.prototype.clone||(Date.prototype.clone=function(){return s(this)}),Date.isLeapYear||(Date.isLeapYear=function(t){return l(t)}),Date.getDaysInMonth||(Date.getDaysInMonth=function(t,e){return d(t,e)}),Date.prototype.isLeapYear||(Date.prototype.isLeapYear=function(){return l(this.getFullYear())}),Date.prototype.clearTime||(Date.prototype.clearTime=function(){return p(this)}),Date.prototype.getDaysInMonth||(Date.prototype.getDaysInMonth=function(){return c(this)}),Date.prototype.addMonths||(Date.prototype.addMonths=function(t){return u(this,t)}),Date.prototype.getLastWeekday||(Date.prototype.getLastWeekday=function(t){return f(this,t)}),Date.prototype.isSameDay||(Date.prototype.isSameDay=function(t){return h(t,this)}),Date.prototype.isSameWeek||(Date.prototype.isSameWeek=function(t){return g(t,this)}),Date.prototype.isSameYear||(Date.prototype.isSameYear=function(t){return m(this,t)}),Date.create||(Date.create=function(t){return i(t)}),Date.timestamp||(Date.timestamp=function(t){return n(t)}))}(window),function(){"use strict";var t=function(t,e){if(arguments.length>1){var i;if(2==arguments.length&&"object"==typeof e)for(var n in e)void 0!==e[n]&&(i=new RegExp("({"+n+"})","g"),t=t.replace(i,e[n]));else for(var o=1;o<arguments.length;o++)void 0!==arguments[o]&&(i=new RegExp("({["+(o-1)+"]})","g"),t=t.replace(i,arguments[o]))}return t},e=function(t){if(null!==t){var e,i;return i=/\d*/i,e=t.match(i),e==t}return!1},i={formatString:t,string:{format:t,isNum:e}};window.$&&window.$.zui?$.zui(i):window.stringHelper=i.string,window.noStringPrototypeHelper||(String.prototype.format||(String.prototype.format=function(){var e=[].slice.call(arguments);return e.unshift(this),t.apply(this,e)}),String.prototype.isNum||(String.prototype.isNum=function(){return e(this)}),String.prototype.endsWith||(String.prototype.endsWith=function(t,e){return(void 0===e||e>this.length)&&(e=this.length),this.substring(e-t.length,e)===t}),String.prototype.startsWith||Object.defineProperty(String.prototype,"startsWith",{value:function(t,e){return e=!e||e<0?0:+e,this.substring(e,e+t.length)===t}}),String.prototype.includes||(String.prototype.includes=function(){return String.prototype.indexOf.apply(this,arguments)!==-1}))}(),/*!
 * jQuery resize event - v1.1
 * http://benalman.com/projects/jquery-resize-plugin/
 * Copyright (c) 2010 "Cowboy" Ben Alman
 * MIT & GPL http://benalman.com/about/license/
 */
function(t,e,i){"$:nomunge";function n(){o=e[s](function(){a.each(function(){var e=t(this),i=e.width(),n=e.height(),o=t.data(this,d);i===o.w&&n===o.h||e.trigger(l,[o.w=i,o.h=n])}),n()},r[c])}var o,a=t([]),r=t.resize=t.extend(t.resize,{}),s="setTimeout",l="resize",d=l+"-special-event",c="delay",p="throttleWindow";r[c]=250,r[p]=!0,t.event.special[l]={setup:function(){if(!r[p]&&this[s])return!1;var e=t(this);a=a.add(e),t.data(this,d,{w:e.width(),h:e.height()}),1===a.length&&n()},teardown:function(){if(!r[p]&&this[s])return!1;var e=t(this);a=a.not(e),e.removeData(d),a.length||clearTimeout(o)},add:function(e){function n(e,n,a){var r=t(this),s=t.data(this,d)||{};s.w=n!==i?n:r.width(),s.h=a!==i?a:r.height(),o.apply(this,arguments)}if(!r[p]&&this[s])return!1;var o;return"function"==typeof e?(o=e,n):(o=e.handler,void(e.handler=n))}}}(jQuery,this),+function(t){"use strict";function e(n,o){var a,r=this.process.bind(this);this.$element=t(t(n).is("body")?window:n),this.$body=t("body"),this.$scrollElement=this.$element.on("scroll."+i+".data-api",r),this.options=t.extend({},e.DEFAULTS,o),this.selector||(this.selector=(this.options.target||(a=t(n).attr("href"))&&a.replace(/.*(?=#[^\s]+$)/,"")||"")+" .nav li > a"),this.offsets=t([]),this.targets=t([]),this.activeTarget=null,this.refresh(),this.process()}var i="zui.scrollspy";e.DEFAULTS={offset:10},e.prototype.refresh=function(){var e=this.$element[0]==window?"offset":"position";this.offsets=t([]),this.targets=t([]);var i=this;this.$body.find(this.selector).map(function(){var n=t(this),o=n.data("target")||n.attr("href"),a=/^#./.test(o)&&t(o);return a&&a.length&&a.is(":visible")&&[[a[e]().top+(!t.isWindow(i.$scrollElement.get(0))&&i.$scrollElement.scrollTop()),o]]||null}).sort(function(t,e){return t[0]-e[0]}).each(function(){i.offsets.push(this[0]),i.targets.push(this[1])})},e.prototype.process=function(){var t,e=this.$scrollElement.scrollTop()+this.options.offset,i=this.$scrollElement[0].scrollHeight||this.$body[0].scrollHeight,n=i-this.$scrollElement.height(),o=this.offsets,a=this.targets,r=this.activeTarget;if(e>=n)return r!=(t=a.last()[0])&&this.activate(t);if(r&&e<=o[0])return r!=(t=a[0])&&this.activate(t);for(t=o.length;t--;)r!=a[t]&&e>=o[t]&&(!o[t+1]||e<=o[t+1])&&this.activate(a[t])},e.prototype.activate=function(e){this.activeTarget=e,t(this.selector).parentsUntil(this.options.target,".active").removeClass("active");var n=this.selector+'[data-target="'+e+'"],'+this.selector+'[href="'+e+'"]',o=t(n).parents("li").addClass("active");o.parent(".dropdown-menu").length&&(o=o.closest("li.dropdown").addClass("active")),o.trigger("activate."+i)};var n=t.fn.scrollspy;t.fn.scrollspy=function(n){return this.each(function(){var o=t(this),a=o.data(i),r="object"==typeof n&&n;a||o.data(i,a=new e(this,r)),"string"==typeof n&&a[n]()})},t.fn.scrollspy.Constructor=e,t.fn.scrollspy.noConflict=function(){return t.fn.scrollspy=n,this},t(window).on("load",function(){t('[data-spy="scroll"]').each(function(){var e=t(this);e.scrollspy(e.data())})})}(jQuery),function(t,e){"use strict";var i,n,o="localStorage",a="page_"+t.location.pathname+t.location.search,r=function(){this.silence=!0;try{o in t&&t[o]&&t[o].setItem&&(this.enable=!0,i=t[o])}catch(r){}this.enable||(n={},i={getLength:function(){var t=0;return e.each(n,function(){t++}),t},key:function(t){var i,o=0;return e.each(n,function(e){return o===t?(i=e,!1):void o++}),i},removeItem:function(t){delete n[t]},getItem:function(t){return n[t]},setItem:function(t,e){n[t]=e},clear:function(){n={}}}),this.storage=i,this.page=this.get(a,{})};r.prototype.pageSave=function(){if(e.isEmptyObject(this.page))this.remove(a);else{var t,i=[];for(t in this.page){var n=this.page[t];null===n&&i.push(t)}for(t=i.length-1;t>=0;t--)delete this.page[i[t]];this.set(a,this.page)}},r.prototype.pageRemove=function(t){"undefined"!=typeof this.page[t]&&(this.page[t]=null,this.pageSave())},r.prototype.pageClear=function(){this.page={},this.pageSave()},r.prototype.pageGet=function(t,e){var i=this.page[t];return void 0===e||null!==i&&void 0!==i?i:e},r.prototype.pageSet=function(t,i){e.isPlainObject(t)?e.extend(!0,this.page,t):this.page[this.serialize(t)]=i,this.pageSave()},r.prototype.check=function(){if(!this.enable&&!this.silence)throw new Error("Browser not support localStorage or enable status been set true.");return this.enable},r.prototype.length=function(){return this.check()?i.getLength?i.getLength():i.length:0},r.prototype.removeItem=function(t){return i.removeItem(t),this},r.prototype.remove=function(t){return this.removeItem(t)},r.prototype.getItem=function(t){return i.getItem(t)},r.prototype.get=function(t,e){var i=this.deserialize(this.getItem(t));return"undefined"!=typeof i&&null!==i||"undefined"==typeof e?i:e},r.prototype.key=function(t){return i.key(t)},r.prototype.setItem=function(t,e){return i.setItem(t,e),this},r.prototype.set=function(t,e){return void 0===e?this.remove(t):(this.setItem(t,this.serialize(e)),this)},r.prototype.clear=function(){return i.clear(),this},r.prototype.forEach=function(t){for(var e=this.length(),n=e-1;n>=0;n--){var o=i.key(n);t(o,this.get(o))}return this},r.prototype.getAll=function(){var t={};return this.forEach(function(e,i){t[e]=i}),t},r.prototype.serialize=function(t){return"string"==typeof t?t:JSON.stringify(t)},r.prototype.deserialize=function(t){if("string"==typeof t)try{return JSON.parse(t)}catch(e){return t||void 0}},e.zui({store:new r})}(window,jQuery),function(t){"use strict";var e="zui.searchBox",i=function(e,n){var o=this;o.name=name,o.$=t(e),o.options=n=t.extend({},i.DEFAULTS,o.$.data(),n);var a=o.$.is(n.inputSelector)?o.$:o.$.find(n.inputSelector);if(a.length){var r=function(){o.changeTimer&&(clearTimeout(o.changeTimer),o.changeTimer=null)},s=function(){r();var t=o.getSearch();if(t!==o.lastValue){var e=""===t;a.toggleClass("empty",e),o.$.callComEvent(o,"onSearchChange",[t,e]),o.lastValue=t}};o.$input=a=a.first(),a.on(n.listenEvent,function(t){o.changeTimer=setTimeout(function(){s()},n.changeDelay)}).on("focus",function(t){a.addClass("focus"),o.$.callComEvent(o,"onFocus",[t])}).on("blur",function(t){a.removeClass("focus"),o.$.callComEvent(o,"onBlur",[t])}).on("keydown",function(t){var e=0,i=t.which;27===i&&n.escToClear?(this.setSearch("",!0),s(),e=1):13===i&&n.onPressEnter&&(s(),o.$.callComEvent(o,"onPressEnter",[t]));var a=o.$.callComEvent(o,"onKeyDown",[t]);a===!1&&(e=1),e&&t.preventDefault()}),o.$.on("click",".search-clear-btn",function(t){o.setSearch("",!0),s(),o.focus(),t.preventDefault()}),s()}else console.error("ZUI: search box init error, cannot find search box input element.")};i.DEFAULTS={inputSelector:'input[type="search"],input[type="text"]',listenEvent:"change input paste",changeDelay:500},i.prototype.getSearch=function(){return this.$input&&t.trim(this.$input.val())},i.prototype.setSearch=function(t,e){var i=this.$input;i&&(i.val(t),e||i.trigger("change"))},i.prototype.focus=function(){this.$input&&this.$input.focus()},t.fn.searchBox=function(n){return this.each(function(){var o=t(this),a=o.data(e),r="object"==typeof n&&n;a||o.data(e,a=new i(this,r)),"string"==typeof n&&a[n]()})},i.NAME=e,t.fn.searchBox.Constructor=i}(jQuery),function(t,e){"use strict";var i="zui.draggable",n={container:"body",move:!0},o=0,a=function(e,i){var a=this;a.$=t(e),a.id=o++,a.options=t.extend({},n,a.$.data(),i),a.init()};a.DEFAULTS=n,a.NAME=i,a.prototype.init=function(){var n,o,a,r,s,l=this,d=l.$,c="before",p="drag",u="finish",f="."+i+"."+l.id,h="mousedown"+f,g="mouseup"+f,m="mousemove"+f,v=l.options,y=v.selector,b=v.handle,w=d,C="function"==typeof v.move,x=function(t){var e=t.pageX,i=t.pageY;s=!0;var o={left:e-a.x,top:i-a.y};w.removeClass("drag-ready").addClass("dragging"),v.move&&(C?v.move(o,w):w.css(o)),v[p]&&v[p]({event:t,element:w,startOffset:a,pos:o,offset:{x:e-n.x,y:i-n.y},smallOffset:{x:e-r.x,y:i-r.y}}),r.x=e,r.y=i,v.stopPropagation&&t.stopPropagation()},$=function(i){if(t(e).off(f),!s)return void w.removeClass("drag-ready");var o={left:i.pageX-a.x,top:i.pageY-a.y};w.removeClass("drag-ready dragging"),v.move&&(C?v.move(o,w):w.css(o)),v[u]&&v[u]({event:i,element:w,startOffset:a,pos:o,offset:{x:i.pageX-n.x,y:i.pageY-n.y},smallOffset:{x:i.pageX-r.x,y:i.pageY-r.y}}),i.preventDefault(),v.stopPropagation&&i.stopPropagation()},T=function(i){var l=t.zui.getMouseButtonCode(v.mouseButton);if(!(l>-1&&i.button!==l)){var d=t(this);if(y&&(w=b?d.closest(y):d),v[c]){var p=v[c]({event:i,element:w});if(p===!1)return}var u=t(v.container),f=w.offset();o=u.offset(),n={x:i.pageX,y:i.pageY},a={x:i.pageX-f.left+o.left,y:i.pageY-f.top+o.top},r=t.extend({},n),s=!1,w.addClass("drag-ready"),i.preventDefault(),v.stopPropagation&&i.stopPropagation(),t(e).on(m,x).on(g,$)}};b?d.on(h,b,T):y?d.on(h,y,T):d.on(h,T)},a.prototype.destroy=function(){var n="."+i+"."+this.id;this.$.off(n),t(e).off(n),this.$.data(i,null)},t.fn.draggable=function(e){return this.each(function(){var n=t(this),o=n.data(i),r="object"==typeof e&&e;o||n.data(i,o=new a(this,r)),"string"==typeof e&&o[e]()})},t.fn.draggable.Constructor=a}(jQuery,document),function(t,e,i){"use strict";var n="zui.droppable",o={target:".droppable-target",deviation:5,sensorOffsetX:0,sensorOffsetY:0,dropToClass:"drop-to"},a=0,r=function(e,i){var n=this;n.id=a++,n.$=t(e),n.options=t.extend({},o,n.$.data(),i),n.init()};r.DEFAULTS=o,r.NAME=n,r.prototype.trigger=function(e,i){return t.zui.callEvent(this.options[e],i,this)},r.prototype.init=function(){var o,a,r,s,l,d,c,p,u,f,h,g,m,v=this,y=v.$,b=v.options,w=b.deviation,C="."+n+"."+v.id,x="mousedown"+C,$="mouseup"+C,T="mousemove"+C,D=b.selector,S=b.handle,k=b.flex,z=b.container,E=b.canMoveHere,P=b.dropToClass,I=y,M=!1,O=z?t(b.container).first():D?y:t("body"),L=function(e){if(M&&(h={left:e.pageX,top:e.pageY},!(i.abs(h.left-p.left)<w&&i.abs(h.top-p.top)<w))){if(null===r){var n=O.css("position");"absolute"!=n&&"relative"!=n&&"fixed"!=n&&(d=n,O.css("position","relative")),r=I.clone().removeClass("drag-from").addClass("drag-shadow").css({position:"absolute",width:I.outerWidth(),transition:"none"}).appendTo(O),I.addClass("dragging"),v.trigger("start",{event:e,element:I,shadowElement:r,targets:o})}var c={left:h.left-f.left,top:h.top-f.top},m={left:c.left-u.left,top:c.top-u.top};r.css(m),t.extend(g,h);var y=!1;s=!1,k||o.removeClass(P);var C=null;if(o.each(function(){var e=t(this),i=e.offset(),n=e.outerWidth(),o=e.outerHeight(),a=i.left+b.sensorOffsetX,r=i.top+b.sensorOffsetY;if(h.left>a&&h.top>r&&h.left<a+n&&h.top<r+o&&(C&&C.removeClass(P),C=e,!b.nested))return!1}),C){s=!0;var x=C.data("id");I.data("id")!=x&&(l=!1),(null===a||a.data("id")!==x&&!l)&&(y=!0),a=C,k&&o.removeClass(P),a.addClass(P)}k?null!==a&&a.length&&(s=!0):(I.toggleClass("drop-in",s),r.toggleClass("drop-in",s)),E&&E(I,a)===!1||v.trigger("drag",{event:e,isIn:s,target:a,element:I,isNew:y,selfTarget:l,clickOffset:f,offset:c,position:{left:c.left-u.left,top:c.top-u.top},mouseOffset:h}),e.preventDefault()}},j=function(i){if(t(e).off(C),clearTimeout(m),M){if(M=!1,d&&O.css("position",d),null===r)return I.removeClass("drag-from"),void v.trigger("always",{event:i,cancel:!0});s||(a=null);var n=!0;h=i?{left:i.pageX,top:i.pageY}:g;var c={left:h.left-f.left,top:h.top-f.top},p={left:h.left-g.left,top:h.top-g.top};g.left=h.left,g.top=h.top;var y={event:i,isIn:s,target:a,element:I,isNew:!l&&null!==a,selfTarget:l,offset:c,mouseOffset:h,position:{left:c.left-u.left,top:c.top-u.top},lastMouseOffset:g,moveOffset:p};n=v.trigger("beforeDrop",y),n&&s&&v.trigger("drop",y),o.removeClass(P),I.removeClass("dragging").removeClass("drag-from"),r.remove(),r=null,v.trigger("finish",y),v.trigger("always",y),i&&i.preventDefault()}},A=function(i){var n=t.zui.getMouseButtonCode(b.mouseButton);if(!(n>-1&&i.button!==n)){var h=t(this);D&&(I=S?h.closest(D):h),I.hasClass("drag-shadow")||b.before&&b.before({event:i,element:I})===!1||(M=!0,o="function"==typeof b.target?b.target(I,y):O.find(b.target),a=null,r=null,s=!1,l=!0,d=null,c=I.offset(),u=O.offset(),u.top=u.top-O.scrollTop(),u.left=u.left-O.scrollLeft(),p={left:i.pageX,top:i.pageY},g=t.extend({},p),f={left:p.left-c.left,top:p.top-c.top},I.addClass("drag-from"),t(e).on(T,L).on($,j),m=setTimeout(function(){t(e).on(x,j)},10),i.preventDefault(),b.stopPropagation&&i.stopPropagation())}};S?y.on(x,S,A):D?y.on(x,D,A):y.on(x,A)},r.prototype.destroy=function(){var i="."+n+"."+this.id;this.$.off(i),t(e).off(i),this.$.data(n,null)},r.prototype.reset=function(){this.destroy(),this.init()},t.fn.droppable=function(e){return this.each(function(){var i=t(this),o=i.data(n),a="object"==typeof e&&e;o||i.data(n,o=new r(this,a)),"string"==typeof e&&o[e]()})},t.fn.droppable.Constructor=r}(jQuery,document,Math),+function(t,e){"use strict";function i(e,i,a){return this.each(function(){var r=t(this),s=r.data(n),l=t.extend({},o.DEFAULTS,r.data(),"object"==typeof e&&e);s||r.data(n,s=new o(this,l)),"string"==typeof e?s[e](i,a):l.show&&s.show(i,a)})}var n="zui.modal1",o=function(i,o){var a=this;a.options=o,a.$body=t(document.body),a.$element=t(i),a.$backdrop=a.isShown=null,a.scrollbarWidth=0,o.moveable===e&&(a.options.moveable=a.$element.hasClass("modal1-moveable")),o.remote&&a.$element.find(".modal1-content").load(o.remote,function(){a.$element.trigger("loaded."+n)}),o.scrollInside&&t(window).on("resize."+n,function(){a.isShown&&a.adjustPosition(e,100)})};o.VERSION="3.2.0",o.TRANSITION_DURATION=300,o.BACKDROP_TRANSITION_DURATION=150,o.DEFAULTS={backdrop:!0,keyboard:!0,show:!0,position:"fit"};var a=function(e,i){var n=t(window);i.left=Math.max(0,Math.min(i.left,n.width()-e.outerWidth())),i.top=Math.max(0,Math.min(i.top,n.height()-e.outerHeight())),e.css(i)};o.prototype.toggle=function(t,e){return this.isShown?this.hide():this.show(t,e)},o.prototype.adjustPosition=function(i,o){var r=this;if(clearTimeout(r.reposTask),o)return void(r.reposTask=setTimeout(r.adjustPosition.bind(r,i,0),o));var s=r.options;if(i===e&&(i=s.position),i!==e&&null!==i){"function"==typeof i&&(i=i(r));var l=r.$element.find(".modal1-dialog"),d=t(window).height(),c={maxHeight:"initial",overflow:"visible"},p=l.find(".modal1-body").css(c);if(s.scrollInside&&p.length){var u=s.headerHeight,f=s.footerHeight,h=l.find(".modal1-header"),g=l.find(".modal1-footer");"number"!=typeof u&&(u=h.length?h.outerHeight():"function"==typeof u?u(h):0),"number"!=typeof f&&(f=g.length?g.outerHeight():"function"==typeof f?f(g):0),c.maxHeight=d-u-f,c.overflow=p[0].scrollHeight>c.maxHeight?"auto":"visible",p.css(c)}var m=Math.max(0,(d-l.outerHeight())/2);if("fit"===i?i={top:m>50?Math.floor(2*m/3):m}:"center"===i?i={top:m}:t.isPlainObject(i)||(i={top:i}),l.hasClass("modal1-moveable")){var v=null,y=s.rememberPos;y&&(y===!0?v=r.$element.data("modal1-pos"):t.zui.store&&(v=t.zui.store.pageGet(n+".rememberPos."+y))),i=t.extend(i,{left:Math.max(0,(t(window).width()-l.outerWidth())/2)},v),"inside"===s.moveable?a(l,i):l.css(i)}else l.css(i)}},o.prototype.setMoveable=function(){t.fn.draggable||console.error("Moveable modal1 requires draggable.js.");var e=this,i=e.options,o=e.$element.find(".modal1-dialog").removeClass("modal1-dragged");o.toggleClass("modal1-moveable",!!i.moveable),e.$element.data("modal1-moveable-setup")||o.draggable({container:e.$element,handle:".modal1-header",before:function(){var t=o.css("margin-top");t&&"0px"!==t&&o.css("top",t).css("margin-top","").addClass("modal1-dragged")},finish:function(o){var a=i.rememberPos;a&&(e.$element.data("modal1-pos",o.pos),t.zui.store&&a!==!0&&t.zui.store.pageSet(n+".rememberPos."+a,o.pos))},move:"inside"!==i.moveable||function(t){a(o,t)}})},o.prototype.show=function(e,i){var a=this,r=t.Event("show."+n,{relatedTarget:e});a.$element.trigger(r),a.$element.toggleClass("modal1-scroll-inside",!!a.options.scrollInside),a.isShown||r.isDefaultPrevented()||(a.isShown=!0,a.options.moveable&&a.setMoveable(),a.options.backdrop!==!1&&(a.setScrollbar(),a.$body.addClass("modal1-open")),a.escape(),a.$element.on("click.dismiss."+n,'[data-dismiss="modal1"]',function(t){a.hide(),t.stopPropagation()}),a.backdrop(function(){var r=t.support.transition&&a.$element.hasClass("fade");a.$element.parent().length||a.$element.appendTo(a.$body),a.$element.show().scrollTop(0),r&&a.$element[0].offsetWidth,a.$element.addClass("in").attr("aria-hidden",!1),a.adjustPosition(i),a.enforceFocus();var s=t.Event("shown."+n,{relatedTarget:e});r?a.$element.find(".modal1-dialog").one("bsTransitionEnd",function(){a.$element.trigger("focus").trigger(s)}).emulateTransitionEnd(o.TRANSITION_DURATION):a.$element.trigger("focus").trigger(s)}))},o.prototype.hide=function(e){e&&e.preventDefault&&e.preventDefault();var i=this;e=t.Event("hide."+n),i.$element.trigger(e),i.isShown&&!e.isDefaultPrevented()&&(i.isShown=!1,i.options.backdrop!==!1&&(i.$body.removeClass("modal1-open"),i.resetScrollbar()),i.escape(),t(document).off("focusin."+n),i.$element.removeClass("in").attr("aria-hidden",!0).off("click.dismiss."+n),t.support.transition&&i.$element.hasClass("fade")?i.$element.one("bsTransitionEnd",i.hidemodal1.bind(i)).emulateTransitionEnd(o.TRANSITION_DURATION):i.hidemodal1())},o.prototype.enforceFocus=function(){t(document).off("focusin."+n).on("focusin."+n,function(t){this.$element[0]===t.target||this.$element.has(t.target).length||this.$element.trigger("focus")}.bind(this))},o.prototype.escape=function(){this.isShown&&this.options.keyboard?t(document).on("keydown.dismiss."+n,function(i){if(27==i.which){var o=t.Event("escaping."+n),a=this.$element.triggerHandler(o,"esc");if(a!=e&&!a)return;this.hide()}}.bind(this)):this.isShown||t(document).off("keydown.dismiss."+n)},o.prototype.hidemodal1=function(){var t=this;this.$element.hide(),this.backdrop(function(){t.$element.trigger("hidden."+n)})},o.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},o.prototype.backdrop=function(e){var i=this,a=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var r=t.support.transition&&a;if(this.$backdrop=t('<div class="modal1-backdrop '+a+'" />').appendTo(this.$body),this.$element.on("mousedown.dismiss."+n,function(t){t.target===t.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus.call(this.$element[0]):this.hide.call(this))}.bind(this)),r&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!e)return;r?this.$backdrop.one("bsTransitionEnd",e).emulateTransitionEnd(o.BACKDROP_TRANSITION_DURATION):e()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var s=function(){i.removeBackdrop(),e&&e()};t.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",s).emulateTransitionEnd(o.BACKDROP_TRANSITION_DURATION):s()}else e&&e()},o.prototype.setScrollbar=function(){t.zui.fixBodyScrollbar()&&this.options.onSetScrollbar&&this.options.onSetScrollbar()},o.prototype.resetScrollbar=function(){t.zui.resetBodyScrollbar(),this.options.onSetScrollbar&&this.options.onSetScrollbar("")},o.prototype.measureScrollbar=function(){var t=document.createElement("div");t.className="modal1-scrollbar-measure",this.$body.append(t);var e=t.offsetWidth-t.clientWidth;return this.$body[0].removeChild(t),e};var r=t.fn.modal1;t.fn.modal1=i,t.fn.modal1.Constructor=o,t.fn.modal1.noConflict=function(){return t.fn.modal1=r,this},t(document).on("click."+n+".data-api",'[data-toggle="modal1"]',function(e){var o=t(this),a=o.attr("href"),r=null;try{r=t(o.attr("data-target")||a&&a.replace(/.*(?=#[^\s]+$)/,""))}catch(s){return}if(r.length){var l=r.data(n)?"toggle":t.extend({remote:!/#/.test(a)&&a},r.data(),o.data());o.is("a")&&e.preventDefault(),r.one("show."+n,function(t){t.isDefaultPrevented()||r.one("hidden."+n,function(){o.is(":visible")&&o.trigger("focus")})}),i.call(r,l,this,o.data("position"))}})}(jQuery,void 0),function(t,e,i){"use strict";if(!t.fn.modal1)throw new Error("modal1 trigger requires modal1.js");var n="zui.modal1trigger",o="ajax",a=".zui.modal1",r="string",s=function(e,i){e=t.extend({},s.DEFAULTS,t.modal1TriggerDefaults,i?i.data():null,e),this.isShown,this.$trigger=i,this.options=e,this.id=t.zui.uuid(),e.show&&this.show()};s.DEFAULTS={type:"custom",height:"auto",name:"triggermodal1",fade:!0,position:"fit",showHeader:!0,delay:0,backdrop:!0,keyboard:!0,waittime:0,loadingIcon:"icon-spinner-indicator",scrollInside:!1},s.prototype.initOptions=function(i){if(i.url&&(!i.type||i.type!=o&&"iframe"!=i.type)&&(i.type=o),i.remote)i.type=o,typeof i.remote===r&&(i.url=i.remote);else if(i.iframe)i.type="iframe",typeof i.iframe===r&&(i.url=i.iframe);else if(i.custom&&(i.type="custom",typeof i.custom===r)){var n;try{n=t(i.custom)}catch(a){}n&&n.length?i.custom=n:"function"==typeof e[i.custom]&&(i.custom=e[i.custom])}return i},s.prototype.init=function(e){var i=this,o=t("#"+e.name);o.length&&(i.isShown||o.off(a),o.remove()),o=t('<div id="'+e.name+'" class="modal1 modal1-trigger '+(e.className||"")+'">'+("string"==typeof e.loadingIcon&&0===e.loadingIcon.indexOf("icon-")?'<div class="icon icon-spin loader '+e.loadingIcon+'"></div>':e.loadingIcon)+'<div class="modal1-dialog"><div class="modal1-content"><div class="modal1-header"><button class="close" data-dismiss="modal1">×</button><h4 class="modal1-title"><i class="modal1-icon"></i> <span class="modal1-title-name"></span></h4></div><div class="modal1-body"></div></div></div></div>').appendTo("body").data(n,i);var r=function(t,i,n){n=n||e[t],"function"==typeof n&&o.on(i+a,n)};r("onShow","show"),r("shown","shown"),r("onHide","hide",function(t){if("iframe"===e.type&&i.$iframeBody){var n=i.$iframeBody.triggerHandler("modal1hide"+a,[i]);n===!1&&t.preventDefault()}var o=e.onHide;if(o)return o(t)}),r("hidden","hidden"),r("loaded","loaded"),o.on("shown"+a,function(){i.isShown=!0}).on("hidden"+a,function(){i.isShown=!1}),this.$modal1=o,this.$dialog=o.find(".modal1-dialog"),e.mergeOptions&&(this.options=e)},s.prototype.show=function(i){var a=this,l=t.extend({},s.DEFAULTS,a.options,{url:a.$trigger?a.$trigger.attr("href")||a.$trigger.attr("data-url")||a.$trigger.data("url"):a.options.url},i),d=a.isShown;l=a.initOptions(l),d||a.init(l);var c=a.$modal1,p=c.find(".modal1-dialog"),u=l.custom,f=p.find(".modal1-body").css("padding","").toggleClass("load-indicator loading",!!d),h=p.find(".modal1-header"),g=p.find(".modal1-content");c.toggleClass("fade",l.fade).addClass(l.className).toggleClass("modal1-loading",!d).toggleClass("modal1-scroll-inside",!!l.scrollInside),p.toggleClass("modal1-md","md"===l.size).toggleClass("modal1-sm","sm"===l.size).toggleClass("modal1-lg","lg"===l.size).toggleClass("modal1-fullscreen","fullscreen"===l.size),h.toggle(l.showHeader),h.find(".modal1-icon").attr("class","modal1-icon icon-"+l.icon),h.find(".modal1-title-name").text(l.title||""),l.size&&"fullscreen"===l.size&&(l.width="",l.height="");var m=function(){clearTimeout(this.resizeTask),this.resizeTask=setTimeout(function(){a.adjustPosition(l.position)},100)},v=function(t,e){return"undefined"==typeof t&&(t=l.delay),setTimeout(function(){p=c.find(".modal1-dialog"),l.width&&"auto"!=l.width&&p.css("width",l.width),l.height&&"auto"!=l.height&&(p.css("height",l.height),"iframe"===l.type&&f.css("height",p.height()-h.outerHeight())),a.adjustPosition(l.position),c.removeClass("modal1-loading").removeClass("modal1-updating"),d&&f.removeClass("loading"),"iframe"!=l.type&&(f=p.off("resize."+n).find(".modal1-body").off("resize."+n),l.scrollInside&&(f=f.children().off("resize."+n)),(f.length?f:p).on("resize."+n,m)),e&&e()},t)};if("custom"===l.type&&u)if("function"==typeof u){var y=u({modal1:c,options:l,modal1Trigger:a,ready:v});typeof y===r&&(f.html(y),v())}else u instanceof t?(f.html(t("<div>").append(u.clone()).html()),v()):(f.html(u),v());else if(l.url){var b=function(){var t=c.callComEvent(a,"broken");"string"==typeof t&&f.html(t),v()};if(c.attr("ref",l.url),"iframe"===l.type){c.addClass("modal1-iframe"),this.firstLoad=!0;var w="iframe-"+l.name;h.detach(),f.detach(),g.empty().append(h).append(f),f.css("padding",0).html('<iframe id="'+w+'" name="'+w+'" src="'+l.url+'" frameborder="no"  allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true"  allowtransparency="true" scrolling="auto" style="width: 100%; height: 100%; left: 0px;"></iframe>'),l.waittime>0&&(a.waitTimeout=v(l.waittime,b));var C=document.getElementById(w);C.onload=C.onreadystatechange=function(i){var o=!!l.scrollInside;if(a.firstLoad&&c.addClass("modal1-loading"),!this.readyState||"complete"==this.readyState){a.firstLoad=!1,l.waittime>0&&clearTimeout(a.waitTimeout);try{c.attr("ref",C.contentWindow.location.href);var r=e.frames[w].$;if(r&&"auto"===l.height&&"fullscreen"!=l.size){var s=r("body").addClass("body-modal1").toggleClass("body-modal1-scroll-inside",o);a.$iframeBody=s,l.iframeBodyClass&&s.addClass(l.iframeBodyClass);var d=[],p=function(i){c.removeClass("fade");var n=s.outerHeight();if(i===!0&&l.onlyIncreaseHeight&&(n=Math.max(n,f.data("minmodal1Height")||0),f.data("minmodal1Height",n)),o){var a=l.headerHeight;"number"!=typeof a?a=h.outerHeight():"function"==typeof a&&(a=a(h));var r=t(e).height();n=Math.min(n,r-a)}for(d.length>1&&n===d[0]&&(n=Math.max(n,d[1])),d.push(n);d.length>2;)d.shift();f.css("height",n),l.fade&&c.addClass("fade"),v()};c.callComEvent(a,"loaded",{modal1Type:"iframe",jQuery:r}),setTimeout(p,100),s.off("resize."+n).on("resize."+n,p),o&&t(e).off("resize."+n).on("resize."+n,p)}else v();var u=l.handleLinkInIframe;u&&r("body").on("click","string"==typeof u?u:"a[href]",function(){t(this).is('[data-toggle="modal1"]')||c.addClass("modal1-updating")}),l.iframeStyle&&r("head").append("<style>"+l.iframeStyle+"</style>")}catch(i){v()}}}}else t.ajax(t.extend({url:l.url,success:function(i){try{var r=t(i);r.filter(".modal1-dialog").length?p.parent().empty().append(r):r.filter(".modal1-content").length?p.find(".modal1-content").replaceWith(r):f.wrapInner(r)}catch(s){e.console&&e.console.warn&&console.warn("ZUI: Cannot recogernize remote content.",{error:s,data:i}),c.html(i)}c.callComEvent(a,"loaded",{modal1Type:o}),v(),l.scrollInside&&t(e).off("resize."+n).on("resize."+n,m)},error:b},l.ajaxOptions))}d||c.modal1({show:"show",backdrop:l.backdrop,moveable:l.moveable,rememberPos:l.rememberPos,keyboard:l.keyboard,scrollInside:l.scrollInside})},s.prototype.close=function(t,i){var n=this;(t||i)&&n.$modal1.on("hidden"+a,function(){"function"==typeof t&&t(),typeof i===r&&i.length&&!n.$modal1.data("cancel-reload")&&("this"===i?e.location.reload():e.location=i)}),n.$modal1.modal1("hide")},s.prototype.toggle=function(t){this.isShown?this.close():this.show(t)},s.prototype.adjustPosition=function(t){t=t===i?this.options.position:t,"function"==typeof t&&(t=t(this)),this.$modal1.modal1("adjustPosition",t)},t.zui({modal1Trigger:s,modal1Trigger:new s}),t.fn.modal1Trigger=function(e,i){return t(this).each(function(){var o=t(this),a=o.data(n),l=t.extend({title:o.attr("title")||o.text(),url:o.attr("href"),type:o.hasClass("iframe")?"iframe":""},o.data(),t.isPlainObject(e)&&e);return a?void(typeof e==r?a[e](i):l.show&&a.show(i)):(o.data(n,a=new s(l,o)),void o.on((l.trigger||"click")+".toggle."+n,function(e){l=t.extend(l,{url:o.attr("href")||o.attr("data-url")||o.data("url")||l.url}),a.toggle(l),o.is("a")&&e.preventDefault()}))})};var l=t.fn.modal1;t.fn.modal1=function(e,i){return t(this).each(function(){var n=t(this);n.hasClass("modal1")?l.call(n,e,i):n.modal1Trigger(e,i)})},t.fn.modal1.bs=l;var d=function(e){return e?e=t(e):(e=t(".modal1.modal1-trigger"),!e.length),e&&e instanceof t?e:null},c=function(i,o,a){var r=i;if("function"==typeof i){var s=a;a=o,o=i,i=s}i=d(i),i&&i.length?i.each(function(){t(this).data(n).close(o,a)}):t("body").hasClass("modal1-open")||t(".modal1.in").length||t("body").hasClass("body-modal1")&&e.parent.$.zui.closemodal1(r,o,a)},p=function(t,e){e=d(e),e&&e.length&&e.modal1("adjustPosition",t)},u=function(e,i){"string"==typeof e&&(e={url:e});var o=d(i);o&&o.length&&o.each(function(){t(this).data(n).show(e)})};t.zui({reloadmodal1:u,closemodal1:c,ajustmodal1Position:p,adjustmodal1Position:p}),t(document).on("click."+n+".data-api",'[data-toggle="modal1"]',function(e){var i=t(this),o=i.attr("href"),a=null;try{a=t(i.attr("data-target")||o&&o.replace(/.*(?=#[^\s]+$)/,""))}catch(r){}a&&a.length||(i.data(n)?i.trigger(".toggle."+n):i.modal1Trigger({show:!0})),i.is("a")&&e.preventDefault()}).on("click."+n+".data-api",'[data-dismiss="modal1"]',function(){t.zui.closemodal1()})}(window.jQuery,window,void 0),+function(t){"use strict";var e=function(t,e){this.type=null,this.options=null,this.enabled=null,this.timeout=null,this.hoverState=null,this.$element=null,this.init("tooltip",t,e)};e.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1},e.prototype.init=function(e,i,n){this.enabled=!0,this.type=e,this.$element=t(i),this.options=this.getOptions(n);for(var o=this.options.trigger.split(" "),a=o.length;a--;){var r=o[a];if("click"==r)this.$element.on("click."+this.type,this.options.selector,this.toggle.bind(this));else if("manual"!=r){var s="hover"==r?"mouseenter":"focus",l="hover"==r?"mouseleave":"blur";this.$element.on(s+"."+this.type,this.options.selector,this.enter.bind(this)),this.$element.on(l+"."+this.type,this.options.selector,this.leave.bind(this))}}this.options.selector?this._options=t.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},e.prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.getOptions=function(e){return e=t.extend({},this.getDefaults(),this.$element.data(),e),e.delay&&"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),e},e.prototype.getDelegateOptions=function(){var e={},i=this.getDefaults();return this._options&&t.each(this._options,function(t,n){i[t]!=n&&(e[t]=n)}),e},e.prototype.enter=function(e){var i=e instanceof this.constructor?e:t(e.currentTarget)[this.type](this.getDelegateOptions()).data("zui."+this.type);return clearTimeout(i.timeout),i.hoverState="in",i.options.delay&&i.options.delay.show?void(i.timeout=setTimeout(function(){"in"==i.hoverState&&i.show()},i.options.delay.show)):i.show()},e.prototype.leave=function(e){var i=e instanceof this.constructor?e:t(e.currentTarget)[this.type](this.getDelegateOptions()).data("zui."+this.type);return clearTimeout(i.timeout),i.hoverState="out",i.options.delay&&i.options.delay.hide?void(i.timeout=setTimeout(function(){"out"==i.hoverState&&i.hide()},i.options.delay.hide)):i.hide()},e.prototype.show=function(e){var i=t.Event("show.zui."+this.type);if((e||this.hasContent())&&this.enabled){var n=this;if(n.$element.trigger(i),i.isDefaultPrevented())return;var o=n.tip();n.setContent(e),n.options.animation&&o.addClass("fade");var a="function"==typeof n.options.placement?n.options.placement.call(n,o[0],n.$element[0]):n.options.placement,r=/\s?auto?\s?/i,s=r.test(a);s&&(a=a.replace(r,"")||"top"),o.detach().css({top:0,left:0,display:"block"}).addClass(a),n.options.container?o.appendTo(n.options.container):o.insertAfter(n.$element);var l=n.getPosition(),d=o[0].offsetWidth,c=o[0].offsetHeight;if(s){var p=n.$element.parent(),u=a,f=document.documentElement.scrollTop||document.body.scrollTop,h="body"==n.options.container?window.innerWidth:p.outerWidth(),g="body"==n.options.container?window.innerHeight:p.outerHeight(),m="body"==n.options.container?0:p.offset().left;a="bottom"==a&&l.top+l.height+c-f>g?"top":"top"==a&&l.top-f-c<0?"bottom":"right"==a&&l.right+d>h?"left":"left"==a&&l.left-d<m?"right":a,o.removeClass(u).addClass(a)}var v=n.getCalculatedOffset(a,l,d,c);n.applyPlacement(v,a);var y=function(){var t=n.hoverState;n.$element.trigger("shown.zui."+n.type),n.hoverState=null,"out"==t&&n.leave(n)};t.support.transition&&n.$tip.hasClass("fade")?o.one("bsTransitionEnd",y).emulateTransitionEnd(150):y()}},e.prototype.applyPlacement=function(t,e){var i,n=this.tip(),o=n[0].offsetWidth,a=n[0].offsetHeight,r=parseInt(n.css("margin-top"),10),s=parseInt(n.css("margin-left"),10);isNaN(r)&&(r=0),isNaN(s)&&(s=0),t.top=t.top+r,t.left=t.left+s,n.offset(t).addClass("in");var l=n[0].offsetWidth,d=n[0].offsetHeight;if("top"==e&&d!=a&&(i=!0,t.top=t.top+a-d),/bottom|top/.test(e)){
var c=0;t.left<0&&(c=t.left*-2,t.left=0,n.offset(t),l=n[0].offsetWidth,d=n[0].offsetHeight),this.replaceArrow(c-o+l,l,"left")}else this.replaceArrow(d-a,d,"top");i&&n.offset(t)},e.prototype.replaceArrow=function(t,e,i){this.arrow().css(i,t?50*(1-t/e)+"%":"")},e.prototype.setContent=function(t){var e=this.tip(),i=t||this.getTitle();this.options.tipId&&e.attr("id",this.options.tipId),this.options.tipClass&&e.addClass(this.options.tipClass),e.find(".tooltip-inner")[this.options.html?"html":"text"](i),e.removeClass("fade in top bottom left right")},e.prototype.hide=function(){function e(){"in"!=i.hoverState&&n.detach()}var i=this,n=this.tip(),o=t.Event("hide.zui."+this.type);if(this.$element.trigger(o),!o.isDefaultPrevented())return n.removeClass("in"),t.support.transition&&this.$tip.hasClass("fade")?n.one(t.support.transition.end,e).emulateTransitionEnd(150):e(),this.$element.trigger("hidden.zui."+this.type),this},e.prototype.fixTitle=function(){var t=this.$element;(t.attr("title")||"string"!=typeof t.attr("data-original-title"))&&t.attr("data-original-title",t.attr("title")||"").attr("title","")},e.prototype.hasContent=function(){return this.getTitle()},e.prototype.getPosition=function(){var e=this.$element[0];return t.extend({},"function"==typeof e.getBoundingClientRect?e.getBoundingClientRect():{width:e.offsetWidth,height:e.offsetHeight},this.$element.offset())},e.prototype.getCalculatedOffset=function(t,e,i,n){return"bottom"==t?{top:e.top+e.height,left:e.left+e.width/2-i/2}:"top"==t?{top:e.top-n,left:e.left+e.width/2-i/2}:"left"==t?{top:e.top+e.height/2-n/2,left:e.left-i}:{top:e.top+e.height/2-n/2,left:e.left+e.width}},e.prototype.getTitle=function(){var t,e=this.$element,i=this.options;return t=e.attr("data-original-title")||("function"==typeof i.title?i.title.call(e[0]):i.title)},e.prototype.tip=function(){return this.$tip=this.$tip||t(this.options.template)},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},e.prototype.validate=function(){this.$element[0].parentNode||(this.hide(),this.$element=null,this.options=null)},e.prototype.enable=function(){this.enabled=!0},e.prototype.disable=function(){this.enabled=!1},e.prototype.toggleEnabled=function(){this.enabled=!this.enabled},e.prototype.toggle=function(e){var i=e?t(e.currentTarget)[this.type](this.getDelegateOptions()).data("zui."+this.type):this;i.tip().hasClass("in")?i.leave(i):i.enter(i)},e.prototype.destroy=function(){this.hide().$element.off("."+this.type).removeData("zui."+this.type)};var i=t.fn.tooltip;t.fn.tooltip=function(i,n){return this.each(function(){var o=t(this),a=o.data("zui.tooltip"),r="object"==typeof i&&i;a||o.data("zui.tooltip",a=new e(this,r)),"string"==typeof i&&a[i](n)})},t.fn.tooltip.Constructor=e,t.fn.tooltip.noConflict=function(){return t.fn.tooltip=i,this}}(window.jQuery),+function(t){"use strict";var e=function(t,e){this.init("popover",t,e)};if(!t.fn.tooltip)throw new Error("Popover requires tooltip.js");e.DEFAULTS=t.extend({},t.fn.tooltip.Constructor.DEFAULTS,{placement:"right",trigger:"click",content:"",template:'<div class="popover"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),e.prototype=t.extend({},t.fn.tooltip.Constructor.prototype),e.prototype.constructor=e,e.prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.setContent=function(){var t=this.tip(),e=this.getTarget();if(e)return e.find(".arrow").length<1&&t.addClass("no-arrow"),void t.html(e.html());var i=this.getTitle(),n=this.getContent();t.find(".popover-title")[this.options.html?"html":"text"](i),t.find(".popover-content")[this.options.html?"html":"text"](n),t.removeClass("fade top bottom left right in"),this.options.tipId&&t.attr("id",this.options.tipId),this.options.tipClass&&t.addClass(this.options.tipClass),t.find(".popover-title").html()||t.find(".popover-title").hide()},e.prototype.hasContent=function(){return this.getTarget()||this.getTitle()||this.getContent()},e.prototype.getContent=function(){var t=this.$element,e=this.options;return t.attr("data-content")||("function"==typeof e.content?e.content.call(t[0]):e.content)},e.prototype.getTarget=function(){var e=this.$element,i=this.options,n=e.attr("data-target")||("function"==typeof i.target?i.target.call(e[0]):i.target);return!!n&&("$next"==n?e.next(".popover"):t(n))},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".arrow")},e.prototype.tip=function(){return this.$tip||(this.$tip=t(this.options.template)),this.$tip};var i=t.fn.popover;t.fn.popover=function(i){return this.each(function(){var n=t(this),o=n.data("zui.popover"),a="object"==typeof i&&i;o||n.data("zui.popover",o=new e(this,a)),"string"==typeof i&&o[i]()})},t.fn.popover.Constructor=e,t.fn.popover.noConflict=function(){return t.fn.popover=i,this}}(window.jQuery),+function(t){"use strict";function e(e){t(o).remove(),t(a).each(function(e){var o=i(t(this));o.hasClass("open")&&(o.trigger(e=t.Event("hide."+n)),e.isDefaultPrevented()||o.removeClass("open").trigger("hidden."+n))})}function i(e){var i=e.attr("data-target");i||(i=e.attr("href"),i=i&&/#/.test(i)&&i.replace(/.*(?=#[^\s]*$)/,""));var n;try{n=i&&t(i)}catch(o){}return n&&n.length?n:e.parent()}var n="zui.dropdown",o=".dropdown-backdrop",a="[data-toggle=dropdown]",r=function(e){t(e).on("click."+n,this.toggle)};r.prototype.toggle=function(o){var a=t(this);if(!a.is(".disabled, :disabled")){var r=i(a),s=r.hasClass("open");if(e(),!s){if("ontouchstart"in document.documentElement&&!r.closest(".navbar-nav").length&&t('<div class="dropdown-backdrop"/>').insertAfter(t(this)).on("click",e),r.trigger(o=t.Event("show."+n)),o.isDefaultPrevented())return;r.toggleClass("open").trigger("shown."+n),a.focus()}return!1}},r.prototype.keydown=function(e){if(/(38|40|27)/.test(e.keyCode)){var n=t(this);if(e.preventDefault(),e.stopPropagation(),!n.is(".disabled, :disabled")){var o=i(n),r=o.hasClass("open");if(!r||r&&27==e.keyCode)return 27==e.which&&o.find(a).focus(),n.click();var s=t("[role=menu] li:not(.divider):visible a",o);if(s.length){var l=s.index(s.filter(":focus"));38==e.keyCode&&l>0&&l--,40==e.keyCode&&l<s.length-1&&l++,~l||(l=0),s.eq(l).focus()}}}};var s=t.fn.dropdown;t.fn.dropdown=function(e){return this.each(function(){var i=t(this),n=i.data("dropdown");n||i.data("dropdown",n=new r(this)),"string"==typeof e&&n[e].call(i)})},t.fn.dropdown.Constructor=r,t.fn.dropdown.noConflict=function(){return t.fn.dropdown=s,this};var l=n+".data-api";t(document).on("click."+l,e).on("click."+l,".dropdown form,.not-clear-menu",function(t){t.stopPropagation()}).on("click."+l,a,r.prototype.toggle).on("keydown."+l,a+", [role=menu]",r.prototype.keydown)}(window.jQuery),function(t,e){"use strict";var i="zui.contextmenu",n={animation:"fade",menuTemplate:'<ul class="dropdown-menu"></ul>',toggleTrigger:!1,duration:200,limitInsideWindow:!0},o=!1,a={},r="zui-contextmenu-"+t.zui.uuid(),s=0,l=0,d=function(){return t(document).off("mousemove."+i).on("mousemove."+i,function(t){s=t.clientX,l=t.clientY}),a},c=function(e,i){if("string"==typeof e&&(e="seperator"===e||"divider"===e||"-"===e||"|"===e?{type:"seperator"}:{label:e,id:i}),"seperator"===e.type||"divider"===e.type)return t('<li class="divider"></li>');var n=t("<a/>").attr(t.extend({href:e.url||"###","class":e.className,style:e.style},e.attrs)).data("item",e);return e.html?e.html===!0?n.html(e.label||e.text):n=t(e.html):n.text(e.label||e.text),e.icon&&n.prepend('<i class="icon icon-'+e.icon+'"></i>'),e.onClick&&n.on("click",e.onClick),t("<li />").toggleClass("disabled",e.disabled===!0).append(n)},p=function(e){var i=t("#"+r);return i.length&&i.hasClass("contextmenu-show")&&(!e||(i.data("options")||{}).id===e)},u=null,f=function(e,i){"function"==typeof e&&(i=e,e=null),u&&(clearTimeout(u),u=null);var n=t("#"+r);if(n.length){var o=n.removeClass("contextmenu-show").data("options");if(!e||o.id===e){var s=function(){n.find(".contextmenu-menu").removeClass("open"),o.onHidden&&o.onHidden(),i&&i()};o.onHide&&o.onHide();var l=o.animation;n.find(".contextmenu-menu").removeClass("in"),l?u=setTimeout(s,o.duration):s()}}return a},h=function(d,p,h){t.isPlainObject(d)&&(h=p,p=d,d=p.items),o=!0,p=t.extend({},n,p);var g=t("#"+r);g.length||(g=t('<div style="position: fixed; z-index: 2000;" class="contextmenu" id="'+r+'"><div class="contextmenu-menu"></div></div>').appendTo("body"));var m=g.find(".contextmenu-menu").off("click."+i).on("click."+i,"a,.contextmenu-item",function(e){var i=t(this),n=p.onClickItem&&p.onClickItem(i.data("item"),i,e,p);n!==!1&&f()}).empty();m.attr("class","contextmenu-menu"+(p.className?" "+p.className:"")),g.attr("class","contextmenu contextmenu-show");var v=p.menuCreator;if(v)m.append(v(d,p));else{m.append(p.menuTemplate);var y=m.children().first(),b=p.itemCreator||c,w=typeof d;if("string"===w?d=d.split(","):"function"===w&&(d=d(p)),!d)return!1;t.each(d,function(t,e){y.append(b(e,t,p))})}var C=p.animation,x=p.duration;C===!0&&(p.animation=C="fade"),u&&(clearTimeout(u),u=null);var $=function(){m.addClass("in"),p.onShown&&p.onShown(),h&&h()};p.onShow&&p.onShow(),g.data("options",{animation:C,onHide:p.onHide,onHidden:p.onHidden,id:p.id,duration:x});var T=p.x,D=p.y;T===e&&(T=(p.event||p).clientX),T===e&&(T=s),D===e&&(D=(p.event||p).clientY),D===e&&(D=l);var y=m.children().first(),S=y.outerWidth(),k=y.outerHeight();if(p.position){var z=p.position({x:T,y:D,width:S,height:k},p,m);z&&(T=z.x,D=z.y)}if(p.limitInsideWindow){var E=t(window);T=Math.max(0,Math.min(T,E.width()-S)),D=Math.max(0,Math.min(D,E.height()-k))}return g.css({left:T,top:D}).show(),m.addClass("open"),C?(m.addClass(C),u=setTimeout(function(){$(),o=!1},10)):($(),o=!1),a};t.extend(a,{NAME:i,DEFAULTS:n,show:h,hide:f,listenMouse:d,isShow:p}),t.zui({ContextMenu:a});var g=function(e,n){var o=this;o.name=i,o.$=t(e),o.id=t.zui.uuid(),n=o.options=t.extend({trigger:"contextmenu"},a.DEFAULTS,this.$.data(),n);var r=function(t){if("mousedown"!==t.type||2===t.button){if(n.toggleTrigger&&o.isShow())o.hide();else{var e={x:t.clientX,y:t.clientY,event:t};if(o.show(e)===!1)return}return t.preventDefault(),t.returnValue=!1,!1}},s=n.trigger,l=s+"."+i;n.selector?o.$.on(l,n.selector,r):o.$.on(l,r),n.show&&o.show("object"==typeof n.show?n.show:null)};g.prototype.destory=function(){that.$.off("."+i)},g.prototype.hide=function(t){return a.hide(this.id,t)},g.prototype.show=function(e,i){return e=t.extend({id:this.id,$toggle:this.$},this.options,e),a.show(e,i)},g.prototype.isShow=function(){return p(this.id)},t.fn.contextmenu=function(e){return this.each(function(){var n=t(this),o=n.data(i),a="object"==typeof e&&e;o||n.data(i,o=new g(this,a)),"string"==typeof e&&o[e]()})},t.fn.contextmenu.Constructor=g,t.fn.contextDropdown=function(e){t(this).contextmenu(t.extend({trigger:"click",animation:"fade",toggleTrigger:!0,menuCreator:function(e,i){var n=i.$toggle,o=n.attr("data-target");o||(o=n.attr("href"),o=o&&/#/.test(o)&&o.replace(/.*(?=#[^\s]*$)/,""));var a=o?t(o):n.next(".dropdown-menu"),r=i.transferEvent;if(r!==!1){var s="data-contextmenu-index";a.find("a,.contextmenu-item").each(function(e){t(this).attr(s,e)});var l=a.clone();return l.on("string"==typeof r?r:"click","a,.contextmenu-item",function(e){var i=a.find("["+s+'="'+t(this).attr(s)+'"]'),n=i[0];if(n)return n[e.type]?n[e.type]():i.trigger(e.type),e.preventDefault(),e.stopPropagation(),!1}),l}return a.clone()},position:function(t,e,i){var n=e.placement,o=e.$toggle;if(!n){var a=i.find(".dropdown-menu"),r=a.hasClass("pull-right"),s=o.parent().hasClass("dropup");n=r?s?"top-right":"bottom-right":s?"top-left":"bottom-left",r&&a.removeClass("pull-right")}var l=o[0].getBoundingClientRect();switch(n){case"top-left":return{x:l.left,y:Math.floor(l.top-t.height)};case"top-right":return{x:Math.floor(l.right-t.width),y:Math.floor(l.top-t.height)};case"bottom-left":return{x:l.left,y:l.bottom};case"bottom-right":return{x:Math.floor(l.right-t.width),y:l.bottom}}return t}},e))},t(document).on("click",function(e){var n=t(e.target),a=n.closest('[data-toggle="context-dropdown"]');if(a.length){var r=a.data(i);r||a.contextDropdown({show:!0})}else o||n.closest(".contextmenu").length||f()})}(jQuery,void 0),+function(t){"use strict";var e=function(e,i){this.$element=t(e),this.$indicators=this.$element.find(".carousel-indicators"),this.options=i,this.paused=this.sliding=this.interval=this.$active=this.$items=null,"hover"==this.options.pause&&this.$element.on("mouseenter",this.pause.bind(this)).on("mouseleave",this.cycle.bind(this))};e.DEFAULTS={interval:5e3,pause:"hover",wrap:!0,touchable:!0},e.prototype.touchable=function(){function e(e){var e=e||window.event;e.originalEvent&&(e=e.originalEvent);var a=t(this);switch(e.type){case"touchstart":n=e.touches[0].pageX,o=e.touches[0].pageY;break;case"touchend":var r=e.changedTouches[0].pageX-n,s=e.changedTouches[0].pageY-o;if(Math.abs(r)>Math.abs(s))i(a,r),Math.abs(r)>10&&e.preventDefault();else{var l=t(window);t("body,html").animate({scrollTop:l.scrollTop()-s},400)}}}function i(t,e){e>10?a.prev():e<-10&&a.next()}if(this.options.touchable){this.$element.on("touchstart touchmove touchend",e);var n,o,a=this}},e.prototype.cycle=function(t){return t||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(this.next.bind(this),this.options.interval)),this},e.prototype.getActiveIndex=function(){return this.$active=this.$element.find(".item.active"),this.$items=this.$active.parent().children(),this.$items.index(this.$active)},e.prototype.to=function(e){var i=this,n=this.getActiveIndex();if(!(e>this.$items.length-1||e<0))return this.sliding?this.$element.one("slid",function(){i.to(e)}):n==e?this.pause().cycle():this.slide(e>n?"next":"prev",t(this.$items[e]))},e.prototype.pause=function(e){return e||(this.paused=!0),this.$element.find(".next, .prev").length&&t.support.transition.end&&(this.$element.trigger(t.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this},e.prototype.next=function(){if(!this.sliding)return this.slide("next")},e.prototype.prev=function(){if(!this.sliding)return this.slide("prev")},e.prototype.slide=function(e,i){var n=this.$element.find(".item.active"),o=i||n[e](),a=this.interval,r="next"==e?"left":"right",s="next"==e?"first":"last",l=this;if(!o.length){if(!this.options.wrap)return;o=this.$element.find(".item")[s]()}this.sliding=!0,a&&this.pause();var d=t.Event("slide.zui.carousel",{relatedTarget:o[0],direction:r});if(!o.hasClass("active")){if(this.$indicators.length&&(this.$indicators.find(".active").removeClass("active"),this.$element.one("slid",function(){var e=t(l.$indicators.children()[l.getActiveIndex()]);e&&e.addClass("active")})),t.support.transition&&this.$element.hasClass("slide")){if(this.$element.trigger(d),d.isDefaultPrevented())return;o.addClass(e),o[0].offsetWidth,n.addClass(r),o.addClass(r),n.one(t.support.transition.end,function(){o.removeClass([e,r].join(" ")).addClass("active"),n.removeClass(["active",r].join(" ")),l.sliding=!1,setTimeout(function(){l.$element.trigger("slid")},0)}).emulateTransitionEnd(600)}else{if(this.$element.trigger(d),d.isDefaultPrevented())return;n.removeClass("active"),o.addClass("active"),this.sliding=!1,this.$element.trigger("slid")}return a&&this.cycle(),this}};var i=t.fn.carousel;t.fn.carousel=function(i){return this.each(function(){var n=t(this),o=n.data("zui.carousel"),a=t.extend({},e.DEFAULTS,n.data(),"object"==typeof i&&i),r="string"==typeof i?i:a.slide;o||n.data("zui.carousel",o=new e(this,a)),"number"==typeof i?o.to(i):r?o[r]():a.interval&&o.pause().cycle(),a.touchable&&o.touchable()})},t.fn.carousel.Constructor=e,t.fn.carousel.noConflict=function(){return t.fn.carousel=i,this},t(document).on("click.zui.carousel.data-api","[data-slide], [data-slide-to]",function(e){var i,n=t(this),o=t(n.attr("data-target")||(i=n.attr("href"))&&i.replace(/.*(?=#[^\s]+$)/,"")),a=t.extend({},o.data(),n.data()),r=n.attr("data-slide-to");r&&(a.interval=!1),o.carousel(a),(r=n.attr("data-slide-to"))&&o.data("zui.carousel").to(r),e.preventDefault()}),t(window).on("load",function(){t('[data-ride="carousel"]').each(function(){var e=t(this);e.carousel(e.data())})})}(window.jQuery),/*! TangBin: image.ready.js http://www.planeart.cn/?p=1121 */
function(t){"use strict";t.zui.imgReady=function(){var t=[],e=null,i=function(){for(var e=0;e<t.length;e++)t[e].end?t.splice(e--,1):t[e]();!t.length&&n()},n=function(){clearInterval(e),e=null};return function(n,o,a,r){var s,l,d,c,p,u=new Image;return u.src=n,u.complete?(o.call(u),void(a&&a.call(u))):(l=u.width,d=u.height,u.onerror=function(){r&&r.call(u),s.end=!0,u=u.onload=u.onerror=null},s=function(){c=u.width,p=u.height,(c!==l||p!==d||c*p>1024)&&(o.call(u),s.end=!0)},s(),u.onload=function(){!s.end&&s(),a&&a.call(u),u=u.onload=u.onerror=null},void(s.end||(t.push(s),null===e&&(e=setInterval(i,40)))))}}()}(jQuery),function(t,e,i){"use strict";if(!t.fn.modal1Trigger)throw new Error("modal1 & modal1Trigger requires for lightbox");if(!t.zui.imgReady)throw new Error("imgReady requires for lightbox");var n=function(e,i){this.$=t(e),this.options=this.getOptions(i),this.init()};n.DEFAULTS={modal1Teamplate:'<div class="icon-spinner icon-spin loader"></div><div class="modal1-dialog"><button class="close" data-dismiss="modal1" aria-hidden="true"><i class="icon-remove"></i></button><button class="controller prev"><i class="icon icon-chevron-left"></i></button><button class="controller next"><i class="icon icon-chevron-right"></i></button><img class="lightbox-img" src="{image}" alt="" data-dismiss="modal1" /><div class="caption"><div class="content">{caption}<div></div></div>'},n.prototype.getOptions=function(e){var i="image";return e=t.extend({},n.DEFAULTS,this.$.data(),e),e[i]||(e[i]=this.$.attr("src")||this.$.attr("href")||this.$.find("img").attr("src"),this.$.data(i,e[i])),e},n.prototype.init=function(){this.bindEvents()},n.prototype.initGroups=function(){var e=this.$.data("groups");e||(e=t('[data-toggle="lightbox"][data-group="'+this.options.group+'"], [data-lightbox-group="'+this.options.group+'"]'),this.$.data("groups",e),e.each(function(e){t(this).attr("data-group-index",e)})),this.groups=e,this.groupIndex=parseInt(this.$.data("group-index"))},n.prototype.setImage=function(t,e){void 0!==t&&(this.options.image=t),void 0!==e&&(this.options.caption=e)},n.prototype.show=function(t,e){this.setImage(t,e),this.$.triggerHandler("click")},n.prototype.bindEvents=function(){var n=this.$,o=this,a=this.options;return!!a.image&&void n.modal1Trigger({type:"custom",name:"lightboxmodal1",position:"center",custom:function(n){o.initGroups();var r=n.modal1,s=o.groups,l=o.groupIndex;r.addClass("modal1-lightbox").html(a.modal1Teamplate.format(a)).toggleClass("lightbox-with-caption","string"==typeof a.caption).removeClass("lightbox-full").data("group-index",l);var d=r.find(".modal1-dialog"),c=t(e).width();t.zui.imgReady(a.image,function(){d.css({width:i.min(c,this.width)}),c<this.width+30&&r.addClass("lightbox-full"),n.ready(200)}),r.find(".prev").toggleClass("show",s.filter('[data-group-index="'+(l-1)+'"]').length>0),r.find(".next").toggleClass("show",s.filter('[data-group-index="'+(l+1)+'"]').length>0),r.find(".controller").click(function(){var o=t(this),a=r.data("group-index")+(o.hasClass("prev")?-1:1),l=s.filter('[data-group-index="'+a+'"]');if(l.length){var p=l.data("image"),u=l.data("caption");r.addClass("modal1-loading").data("group-index",a).toggleClass("lightbox-with-caption","string"==typeof u).removeClass("lightbox-full"),r.find(".lightbox-img").attr("src",p),r.find(".caption > .content").text(u),c=t(e).width(),t.zui.imgReady(p,function(){d.css({width:i.min(c,this.width)}),c<this.width+30&&r.addClass("lightbox-full"),n.ready()})}return r.find(".prev").toggleClass("show",s.filter('[data-group-index="'+(a-1)+'"]').length>0),r.find(".next").toggleClass("show",s.filter('[data-group-index="'+(a+1)+'"]').length>0),!1})}})},t.fn.lightbox=function(e){var i="group"+(new Date).getTime();return this.each(function(){var o=t(this),a="object"==typeof e&&e;"object"==typeof a&&a.group?o.attr("data-lightbox-group",a.group):o.data("group")?o.attr("data-lightbox-group",o.data("group")):o.attr("data-lightbox-group",i),o.data("group",o.data("lightbox-group"));var r=o.data("zui.lightbox");r||o.data("zui.lightbox",r=new n(this,a)),"string"==typeof e&&r[e]()})},t.fn.lightbox.Constructor=n,t(function(){t('[data-toggle="lightbox"]').lightbox()})}(jQuery,window,Math),function(t,e,i){"use strict";var n=0,o='<div class="messager messager-{type} {placement}" style="display: none"><div class="messager-content"></div><div class="messager-actions"></div></div>',a={icons:{},type:"default",placement:"top",time:4e3,parent:"body",close:!0,fade:!0,scale:!0},r={},s=function(e,s){t.isPlainObject(e)?s=t.extend({},s,e):e&&(s?s.content=e:s={content:e});var l=this;s=l.options=t.extend({},a,s),l.id=s.id||n++;var d=r[l.id];d&&d.destroy(),r[l.id]=l,l.$=t(o.format(s)).toggleClass("fade",s.fade).toggleClass("scale",s.scale).attr("id","messager-"+l.id),s.cssClass&&l.$.addClass(s.cssClass);var c=!1,p=l.$.find(".messager-actions"),u=function(e){var n=t('<button type="button" class="action action-'+e.name+'"/>');"close"===e.name&&n.addClass("close"),e.html!==i&&n.html(e.html),e.icon!==i&&n.append('<i class="action-icon icon-'+e.icon+'"/>'),e.text!==i&&n.append('<span class="action-text">'+e.text+"</span>"),e.tooltip!==i&&n.attr("title",e.tooltip).tooltip(),n.data("action",e),p.append(n)};s.actions&&t.each(s.actions,function(t,e){e.name===i&&(e.name=t),"close"==e.name&&(c=!0),u(e)}),!c&&s.close&&u({name:"close",html:"&times;"}),l.$.on("click",".action",function(e){var i,n=t(this).data("action");s.onAction&&(i=s.onAction.call(this,n.name,n,l),i===!1)||"function"==typeof n.action&&(i=n.action.call(this,l),i===!1)||(l.hide(),e.stopPropagation())}),l.$.on("click",function(t){if(s.onAction){var e=s.onAction.call(this,"content",null,l);e===!0&&l.hide()}}),l.$.data("zui.messager",l),s.show&&l.message!==i&&l.show()};s.prototype.update=function(e,i){t.isPlainObject(e)?i=e:e&&(i?i.content=e:i={content:e});var n=this,o=n.options;n.$.removeClass("messager-"+o.type);var a=n.$.find(".messager-content");o.contentClass&&a.removeClass(o.contentClass),i&&(o=t.extend(o,i)),n.$.addClass("messager-"+o.type).toggleClass("messager-notification",!!o.notification),o.contentClass&&a.addClass(o.contentClass);var r=o.title,s=o.icon;if(e=o.content,a.empty(),r){var l=t('<div class="messager-title"></div>');l[o.html?"html":"text"](r),a.append(l)}if(e){var d=t('<div class="messager-text"></div>');d[o.html?"html":"text"](e),a.append(d)}var c=n.$.find(".messager-icon");if(s){var p=t.isPlainObject(s)?s.html:'<i class="icon-'+s+' icon"></i>';c.length?c.html(p):a.before('<div class="messager-icon">'+p+"<div>")}else c.remove();n.$.toggleClass("messager-has-icon",!!s),n.updateTime||o.onUpdate&&o.onUpdate.call(n,o),n.updateTime=Date.now()},s.prototype.show=function(n,o){var a=this,r=this.options;if("function"==typeof n){var s=o;o=n,s!==i&&(n=s)}if(a.isShow)return void a.hide(function(){a.show(n,o)});a.hiding&&(clearTimeout(a.hiding),a.hiding=null),a.update(n);var l=r.placement,d=t(r.parent),c=d.children(".messagers-holder."+l);if(c.length||(c=t("<div/>").attr("class","messagers-holder "+l).appendTo(d)),c.append(a.$),"center"===l){var p=t(e).height()-c.height();c.css("top",Math.max(-p,p/2))}return a.$.show().addClass("in"),r.time&&(a.hiding=setTimeout(function(){a.hide()},r.time)),a.isShow=!0,o&&o(),r.onShow&&r.onShow.call(a,r),a},s.prototype.hide=function(t,e){t===!0&&(e=!0,t=null);var i=this,n=i.options;if(i.$.hasClass("in")){i.$.removeClass("in");var o=function(){var o=i.$.parent();i.$.detach(),o.children().length||o.remove(),t&&t(!0),n.onHide&&n.onHide.call(i,e)};e?o():setTimeout(o,200)}else t&&t(!1),n.onHide&&n.onHide.call(i,e);i.isShow=!1},s.prototype.destroy=function(){var t=this;t.hide(function(){t.$.remove(),t.$=null},!0),delete r[t.id]};var l=function(e){if(e===i)t(".messager").each(function(){var e=t(this).data("zui.messager");e&&e.hide&&e.hide(!0)});else{var n=t("#messager-"+e).data("zui.messager");n&&n.hide&&n.hide()}},d=function(e,n){"string"==typeof n&&(n={type:n}),t.isPlainObject(e)&&(n=t.extend({},n,e),e=null),n=t.extend({},n),n.id===i&&l();var o=r[n.id]||new s(e,n);return o.show(),o},c={notification:!0,placement:"bottom-right",time:0,icon:"bell icon-2x"},p=function(e,i,n){var o=t.extend({id:t.zui.uuid()},c),a="string"==typeof e,r="string"==typeof i;return a&&r?n=t.extend(o,n,{title:e,content:i}):a&&t.isPlainObject(i)?n=t.extend(o,n,i,{title:e}):t.isPlainObject(e)?n=t.extend(o,n,i,e):a&&(n=t.extend(o,n,{title:e})),d(n)},u=function(t){return"string"==typeof t?{placement:t}:t},f={show:d,hide:l};s.all=r,s.DEFAULTS=a,s.NOTIFICATION_DEFAULTS=c,t.each({primary:0,success:"ok-sign",info:"info-sign",warning:"warning-sign",danger:"exclamation-sign",important:0,special:0},function(e,i){f[e]=function(n,o){return d(n,t.extend({type:e,icon:s.DEFAULTS.icons[e]||i||null},u(o)))}}),t.zui({Messager:s,showMessager:d,showNotification:p,messager:f})}(jQuery,window,void 0),function(t,e,i,n){"use strict";function o(t){if(t=t.toLowerCase(),t&&c.test(t)){var e;if(4===t.length){var i="#";for(e=1;e<4;e+=1)i+=t.slice(e,e+1).concat(t.slice(e,e+1));t=i}var n=[];for(e=1;e<7;e+=2)n.push(b("0x"+t.slice(e,e+2)));return{r:n[0],g:n[1],b:n[2],a:1}}throw new Error("Wrong hex string! (hex: "+t+")")}function a(e){return typeof e===h&&("transparent"===e.toLowerCase()||m[e.toLowerCase()]||c.test(t.trim(e.toLowerCase())))}function r(t){function e(t){return t=t<0?t+1:t>1?t-1:t,6*t<1?s+(r-s)*t*6:2*t<1?r:3*t<2?s+(r-s)*(2/3-t)*6:s}var i=t.h,n=t.s,o=t.l,a=t.a;i=d(i)%u/u,n=l(d(n)),o=l(d(o)),a=l(d(a));var r=o<=.5?o*(n+1):o+n-o*n,s=2*o-r,c={r:e(i+1/3)*p,g:e(i)*p,b:e(i-1/3)*p,a:a};return c}function s(t,i,n){return v(n)&&(n=0),v(i)&&(i=p),e.min(e.max(t,n),i)}function l(t,e){return s(t,e)}function d(t){return"number"==typeof t?t:parseFloat(t)}var c=/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/,p=255,u=360,f=100,h="string",g="object",m={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},v=function(t){return t===n},y=function(t){return!v(t)},b=function(t){return parseInt(t)},w=function(t){return b(l(d(t),p))},C=function(t,e,i,n){var a=this;if(a.r=a.g=a.b=0,a.a=1,y(n)&&(a.a=l(d(n),1)),y(t)&&y(e)&&y(i))a.r=w(t),a.g=w(e),a.b=w(i);else if(y(t)){var s=typeof t;if(s==h)if(t=t.toLowerCase(),"transparent"===t)a.a=0;else if(m[t])a.rgb(o(m[t]));else if(0===t.indexOf("rgb")){var c=t.substring(t.indexOf("(")+1,t.lastIndexOf(")")).split(",",4);a.rgb({r:c[0],g:c[1],b:c[2],a:c[3]})}else a.rgb(o(t));else if("number"==s&&v(e))a.r=a.g=a.b=w(t);else if(s==g&&y(t.r))a.r=w(t.r),y(t.g)&&(a.g=w(t.g)),y(t.b)&&(a.b=w(t.b)),y(t.a)&&(a.a=l(d(t.a),1));else if(s==g&&y(t.h)){var p={h:l(d(t.h),u),s:1,l:1,a:1};y(t.s)&&(p.s=l(d(t.s),1)),y(t.l)&&(p.l=l(d(t.l),1)),y(t.a)&&(p.a=l(d(t.a),1)),a.rgb(r(p))}}};C.prototype.rgb=function(t){var e=this;if(y(t)){if(typeof t==g)y(t.r)&&(e.r=w(t.r)),y(t.g)&&(e.g=w(t.g)),y(t.b)&&(e.b=w(t.b)),y(t.a)&&(e.a=l(d(t.a),1));else{var i=b(d(t));e.r=i,e.g=i,e.b=i}return e}return{r:e.r,g:e.g,b:e.b,a:e.a}},C.prototype.hue=function(t){var e=this,i=e.toHsl();return v(t)?i.h:(i.h=l(d(t),u),e.rgb(r(i)),e)},C.prototype.darken=function(t){var e=this,i=e.toHsl();return i.l-=t/f,i.l=l(i.l,1),e.rgb(r(i)),e},C.prototype.clone=function(){var t=this;return new C(t.r,t.g,t.b,t.a)},C.prototype.lighten=function(t){return this.darken(-t)},C.prototype.fade=function(t){return this.a=l(t/f,1),this},C.prototype.spin=function(t){var e=this.toHsl(),i=(e.h+t)%u;return e.h=i<0?u+i:i,this.rgb(r(e))},C.prototype.toHsl=function(){var t,i,n=this,o=n.r/p,a=n.g/p,r=n.b/p,s=n.a,l=e.max(o,a,r),d=e.min(o,a,r),c=(l+d)/2,f=l-d;if(l===d)t=i=0;else{switch(i=c>.5?f/(2-l-d):f/(l+d),l){case o:t=(a-r)/f+(a<r?6:0);break;case a:t=(r-o)/f+2;break;case r:t=(o-a)/f+4}t/=6}return{h:t*u,s:i,l:c,a:s}},C.prototype.luma=function(){var t=this.r/p,i=this.g/p,n=this.b/p;return t=t<=.03928?t/12.92:e.pow((t+.055)/1.055,2.4),i=i<=.03928?i/12.92:e.pow((i+.055)/1.055,2.4),n=n<=.03928?n/12.92:e.pow((n+.055)/1.055,2.4),.2126*t+.7152*i+.0722*n},C.prototype.saturate=function(t){var e=this.toHsl();return e.s+=t/f,e.s=l(e.s),this.rgb(r(e))},C.prototype.desaturate=function(t){return this.saturate(-t)},C.prototype.contrast=function(t,e,i){if(e=v(e)?new C(p,p,p,1):new C(e),t=v(t)?new C(0,0,0,1):new C(t),t.luma()>e.luma()){var n=e;e=t,t=n}return this.a<.5?t:(i=v(i)?.43:d(i),this.luma()<i?e:t)},C.prototype.hexStr=function(){var t=this.r.toString(16),e=this.g.toString(16),i=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==i.length&&(i="0"+i),"#"+t+e+i},C.prototype.toCssStr=function(){var t=this;return t.a>0?t.a<1?"rgba("+t.r+","+t.g+","+t.b+","+t.a+")":t.hexStr():"transparent"},C.isColor=a,C.names=m,C.get=function(t){return new C(t)},t.zui({Color:C})}(jQuery,Math,window,void 0),function(t){"use strict";function e(e,i){if(e===!1)return e;if(!e)return i;e===!0?e={add:!0,"delete":!0,edit:!0,sort:!0}:"string"==typeof e&&(e=e.split(","));var n;return Array.isArray(e)&&(n={},t.each(e,function(e,i){t.isPlainObject(i)?n[i.action]=i:n[i]=!0}),e=n),t.isPlainObject(e)&&(n={},t.each(e,function(e,i){i?n[e]=t.extend({type:e},r[e],t.isPlainObject(i)?i:null):n[e]=!1}),e=n),i?t.extend(!0,{},i,e):e}function i(e,i,n){return i=i||e.type,t(n||e.template).addClass("tree-action").attr(t.extend({"data-type":i,title:e.title||""},e.attr)).data("action",e)}var n="zui.tree",o=0,a=function(e,i){this.name=n,this.$=t(e),this.getOptions(i),this._init()},r={sort:{template:'<a class="sort-handler" href="javascript:;"><i class="icon icon-move"></i></a>'},add:{template:'<a href="javascript:;"><i class="icon icon-plus"></i></a>'},edit:{template:'<a href="javascript:;"><i class="icon icon-pencil"></i></a>'},"delete":{template:'<a href="javascript:;"><i class="icon icon-trash"></i></a>'}};a.DEFAULTS={animate:null,initialState:"normal",toggleTemplate:'<i class="list-toggle icon"></i>'},a.prototype.add=function(e,i,n,o,a){var r,s=t(e),l=this.options;if(s.is("li")?(r=s.children("ul"),r.length||(r=t("<ul/>"),s.append(r),this._initList(r,s))):r=s,r){var d=this;Array.isArray(i)||(i=[i]),t.each(i,function(e,i){var n=t("<li/>").data(i).appendTo(r);void 0!==i.id&&n.attr("data-id",i.id);var o=l.itemWrapper?t(l.itemWrapper===!0?'<div class="tree-item-wrapper"/>':l.itemWrapper).appendTo(n):n;if(i.html)o.html(i.html);else if("function"==typeof d.options.itemCreator){var a=d.options.itemCreator(n,i);a!==!0&&a!==!1&&o.html(a)}else i.url?o.append(t("<a/>",{href:i.url}).text(i.title||i.name)):o.append(t("<span/>").text(i.title||i.name));d._initItem(n,i.idx||e,r,i),i.children&&i.children.length&&d.add(n,i.children)}),this._initList(r),n&&!r.hasClass("tree")&&d.expand(r.parent("li"),o,a)}},a.prototype.reload=function(e){var i=this;e&&(i.$.empty(),i.add(i.$,e)),i.isPreserve&&i.store.time&&i.$.find("li:not(.tree-action-item)").each(function(){var e=t(this);i[i.store[e.data("id")]?"expand":"collapse"](e,!0,!0)})},a.prototype._initList=function(n,o,a,r){var s=this;n.hasClass("tree")?(a=0,o=null):(o=(o||n.closest("li")).addClass("has-list"),o.find(".list-toggle").length||o.prepend(this.options.toggleTemplate),a=a||o.data("idx")),n.removeClass("has-active-item");var l=n.attr("data-idx",a||0).children("li:not(.tree-action-item)").each(function(e){s._initItem(t(this),e+1,n)});1!==l.length||l.find("ul").length||l.addClass("tree-single-item"),r=r||(o?o.data():null);var d=e(r?r.actions:null,this.actions);if(d){if(d.add&&d.add.templateInList!==!1){var c=n.children("li.tree-action-item");c.length?c.detach().appendTo(n):t('<li class="tree-action-item"/>').append(i(d.add,"add",d.add.templateInList)).appendTo(n)}d.sort&&n.sortable(t.extend({dragCssClass:"tree-drag-holder",trigger:".sort-handler",selector:"li:not(.tree-action-item)",finish:function(t){s.callEvent("action",{action:d.sort,$list:n,target:t.target,item:r})}},d.sort.options,t.isPlainObject(this.options.sortable)?this.options.sortable:null))}o&&(o.hasClass("open")||r&&r.open)&&o.addClass("open in")},a.prototype._initItem=function(n,o,a,r){if(void 0===o){var s=n.prev("li");o=s.length?s.data("idx")+1:1}if(a=a||n.closest("ul"),n.attr("data-idx",o).removeClass("tree-single-item"),!n.data("id")){var l=o;a.hasClass("tree")||(l=a.parent("li").data("id")+"-"+l),n.attr("data-id",l)}n.hasClass("active")&&a.parent("li").addClass("has-active-item"),r=r||n.data();var d=e(r.actions,this.actions);if(d){var c=n.find(".tree-actions");c.length||(c=t('<div class="tree-actions"/>').appendTo(this.options.itemWrapper?n.find(".tree-item-wrapper"):n),t.each(d,function(t,e){e&&c.append(i(e,t))}))}var p=n.children("ul");p.length&&this._initList(p,n,o,r)},a.prototype._init=function(){var i=this.options,a=this;this.actions=e(i.actions),this.$.addClass("tree"),i.animate&&this.$.addClass("tree-animate"),this._initList(this.$);var r=i.initialState,s=t.zui&&t.zui.store&&t.zui.store.enable;s&&(this.selector=n+"::"+(i.name||"")+"#"+(this.$.attr("id")||o++),this.store=t.zui.store[i.name?"get":"pageGet"](this.selector,{})),"preserve"===r&&(s?this.isPreserve=!0:this.options.initialState=r="normal"),this.reload(i.data),s&&(this.isPreserve=!0),"expand"===r?this.expand():"collapse"===r?this.collapse():"active"===r&&this.expandSelect(".active"),this.$.on("click",'.list-toggle,a[href="#"],.tree-toggle',function(e){var i=t(this),n=i.parent("li");a.callEvent("hit",{target:n,item:n.data()}),a.toggle(n),i.is("a")&&e.preventDefault()}).on("click",".tree-action",function(){var e=t(this),i=e.data();if(i.action&&(i=i.action),"sort"!==i.type){var n=e.closest("li:not(.tree-action-item)");a.callEvent("action",{action:i,target:this,$item:n,item:n.data()})}})},a.prototype.preserve=function(e,i,n){if(this.isPreserve)if(e)i=i||e.data("id"),n=void 0===n&&e.hasClass("open"),n?this.store[i]=n:delete this.store[i],this.store.time=(new Date).getTime(),t.zui.store[this.options.name?"set":"pageSet"](this.selector,this.store);else{var o=this;this.store={},this.$.find("li").each(function(){o.preserve(t(this))})}},a.prototype.expandSelect=function(t){this.show(t,!0)},a.prototype.expand=function(t,e,i){t?(t.addClass("open"),!e&&this.options.animate?setTimeout(function(){t.addClass("in")},10):t.addClass("in")):t=this.$.find("li.has-list").addClass("open in"),i||this.preserve(t),this.callEvent("expand",t,this)},a.prototype.show=function(e,i,n){var o=this;e instanceof t||(e=o.$.find("li").filter(e)),e.each(function(){var e=t(this);if(o.expand(e,i,n),e)for(var a=e.parent("ul");a&&a.length&&!a.hasClass("tree");){var r=a.parent("li");r.length?(o.expand(r,i,n),a=r.parent("ul")):a=!1}})},a.prototype.collapse=function(t,e,i){t?!e&&this.options.animate?(t.removeClass("in"),setTimeout(function(){t.removeClass("open")},300)):t.removeClass("open in"):t=this.$.find("li.has-list").removeClass("open in"),i||this.preserve(t),this.callEvent("collapse",t,this)},a.prototype.toggle=function(t){var e=t&&t.hasClass("open")||t===!1||void 0===t&&this.$.find("li.has-list.open").length;this[e?"collapse":"expand"](t)},a.prototype.getOptions=function(e){this.options=t.extend({},a.DEFAULTS,this.$.data(),e),null===this.options.animate&&this.$.hasClass("tree-animate")&&(this.options.animate=!0)},a.prototype.toData=function(e,i){"function"==typeof e&&(i=e,e=null),e=e||this.$;var n=this;return e.children("li:not(.tree-action-item)").map(function(){var e=t(this),o=e.data();delete o["zui.droppable"];var a=e.children("ul");return a.length&&(o.children=n.toData(a)),"function"==typeof i?i(o,e):o}).get()},a.prototype.callEvent=function(e,i){var n;return"function"==typeof this.options[e]&&(n=this.options[e](i,this)),this.$.trigger(t.Event(e+"."+this.name,i)),n},t.fn.tree=function(e,i){return this.each(function(){var o=t(this),r=o.data(n),s="object"==typeof e&&e;r||o.data(n,r=new a(this,s)),"string"==typeof e&&r[e](i)})},t.fn.tree.Constructor=a,t(function(){t('[data-ride="tree"]').tree()})}(jQuery);
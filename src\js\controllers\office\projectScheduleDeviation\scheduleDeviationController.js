
(function() {
	app.controller("scheduleDeviationController", ['comService', '$rootScope', '$scope', 'scheduleDeviationService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
		function(comService, $rootScope, $scope, scheduleDeviationService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		//保存查询出的产品线信息
    	$scope.jsonData = [];//产品线情况报告
    	$scope.projectData = [];//项目情况报告

    	$scope.datepicker = {};
        $scope.toggleMin = toggleMin;
        toggleMin();
		//获取数据
		$scope.getData = getData;

        //初始化页面信息
        initPages();

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */	
    	/**
    	 * 页面初始化
    	 */
    	function initPages() {
    		//获取产品线
    		$scope.productLineList = [];
    		comService.queryEffectiveParam('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
    			if (data.data) {
    				$scope.productLineList =  data.data;

                    //获取部门
                    $scope.departmentList = [];
                    comService.getOrgChildren('D010053').then(function(data) {
                        $scope.departmentList = data.data;
                        //获取缓存信息
                        getLocalcache();
                    });
    			}
    		});

    	}

    	//获取缓存信息
    	function getLocalcache() {
            //获取缓存，设置初始值
            var param = LocalCache.getObject('scheduleDeviationManagement_searchParam');
            if(typeof (param) !== 'undefined'  && typeof (param.searchParam) !== 'undefined'){
                //单选按钮的初始值
                $scope.checkedProj = param.searchParam.checkedProj;
                $scope.checkedYear = typeof ($scope.checkedProj)==='undefined'? false : undefined;
                $scope.dataFlag = param.searchParam.dataFlag;
                //判断时间框是否出现
                $scope.startTime = param.searchParam.startTime;
                $scope.endTime = param.searchParam.endTime;
                LocalCache.setObject('scheduleDeviationManagement_searchParam',{});
            }else{
                //单选按钮的初始值
                $scope.checkedProj = false;
                //判断时间框是否出现
                $scope.dataFlag = false;
            }

            getData();
        }
    	//获取当前选定时间
        function toggleMin() {
            $scope.datepicker.currentDate = $scope.datepicker.currentDate ? null : new Date();
        }

            /**
             * 选择查询条件
             */
            $scope.radioSelect = function (item) {
                if(item==='year'){
                    $scope.checkedProj = false;
                    $scope.dataFlag = false;
                }else{
                    $scope.checkedYear = false;
                    $scope.dataFlag = true;
                }
            };
		/**
		 * 获取项目
		 */
		function getData() {
			 $scope.deferredProjectNoTotal = 0;
			//判断时间条件
			time();
			if ($scope.start <= $scope.end && $scope.start <= $scope.time){
                var urlData = {};
                //若选中“本年度计划完成率数据”则修改查询条件，使之查询数据和kpi看板一致
			    if(!$scope.dataFlag){
                    urlData ={
                        'productLine':'KPI'
                    };
                }else{
                    urlData ={
                        'startTime':$scope.start,
                        'endTime':$scope.end
                    };
                }
			scheduleDeviationService.getInformation(urlData).then(function(data) {
				if (data.code===AgreeConstant.code) {
					//产品线情况报告
					$scope.jsonData = data.data.scheduleDevationCountVO;
					resetJsonData();
                    //部门报告
                    $scope.projectData = data.data.scheduleDevationDepartment;
                    resetProjectData();
				} else {
					inform.common(data.message);
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
			}else {
     			inform.common(Trans("任务开始时间不得大于任务结束时间,且不得大于当前日期！"));
     		}
		}
		/**
      	 * 重新处理列表
      	 */
     	function resetJsonData() {
     		var finalSum = $scope.jsonData.pop();
     		angular.forEach($scope.productLineList, function(one, i) {
            	var list = []; 
            	angular.forEach($scope.jsonData, function(oneLine, i) {
            		list.push(oneLine.productLine);
            	});
          		if (list.indexOf(one.paramValue)===-1){
          			 $scope.jsonData.push({
          				 "productLine":one.paramValue,
          				 "projectNum":0,
          				 "normalNo":0,
          				 "lovwerNo":0,
          				 "higherNo":0,
          				 "delayNo":0,
          				 "proportion":100
          			 });
          		 }
      	     });
     		$scope.jsonData.push(finalSum);
        }
        function resetProjectData() {
     		var finalSum = $scope.projectData.pop();
     		angular.forEach($scope.departmentList, function(one, i) {
            	var list = []; 
            	angular.forEach($scope.projectData, function(oneDep) {
            		list.push(oneDep.productLine);
            	});
          		if (list.indexOf(one.orgName)===-1  && one.orgCode !== 'D010133' && one.orgCode !== 'D010131'){
          			 $scope.projectData.push({
          				 "productLine":one.orgName,
          				 "projectNum":0,
          				 "normalNo":0,
          				 "lovwerNo":0,
          				 "higherNo":0,
          				 "delayNo":0,
          				 "proportion":100
          			 });
          		 }
      	     });
     		$scope.projectData.push(finalSum);
        }

        /**
    	 * 查询开始时间
    	 */
		$scope.openDateStart = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.openedStart = true;
			$scope.openedEnd = false;
		};
		/**
		 * 查询结束时间
		 */
		$scope.openDateEnd = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.openedStart = false;    
			$scope.openedEnd = true;
		};
		/**
		 * 重置
		 */
		$scope.rest = function() {
			$scope.startTime = '';
			$scope.endTime = '';
			$scope.productLineInfo = '';
		};
		/**
		 * 校验时间
		 */
		function time() {
			$scope.start = "";
			$scope.end = "";
			$scope.time = "";
			if ($scope.startTime != null && $scope.startTime !== "" ){
				$scope.start = inform.format($scope.startTime,'yyyy-MM-dd');
				if ($scope.endTime==null || $scope.endTime===""){
					$scope.end = inform.format(new Date(),'yyyy-MM-dd');
				}
			} 
			if ($scope.endTime != null && $scope.endTime !== "" ){
				$scope.end = inform.format($scope.endTime,'yyyy-MM-dd');
			} 
			$scope.time = inform.format(new Date(),'yyyy-MM-dd');
		}

        
		/**
		 * excel下载
		 */
		$scope.toExcel = function() {
			//判断时间条件
			time();
			if ($scope.start <= $scope.end && $scope.start <= $scope.time){
			var modalInstance = $modal.open({
				 templateUrl: 'myModalContent.html',
                 controller: 'ModalInstanceCtrl',
                 size: "sm",
                 resolve: {
                	 items: function() {
                		 return "确定要下载吗！";
                	 }
                 }
			});
		    modalInstance.result.then(function() {
				//开启遮罩层
				inform.showLayer("下载中。。。。。。");
				var urlData = {};
				//拼装下载内容
                if(!$scope.dataFlag){
                    urlData ={
                        'productLine':'KPI'
                    };
                }else{
                    urlData ={
                        'startTime':$scope.start,
                        'endTime':$scope.end
                    };
                }
				$http.post(
					$rootScope.getWaySystemApi+'scheduleDeviation/toExcel',
					urlData,
		            {headers: {
						'Content-Type': 'application/json',
						'Authorization':'Bearer ' + LocalCache.getSession("token")||''
					},
					responseType: 'arraybuffer'//防止中文乱码
		            }
		         ).success(function(data){
		            var aForExcel;
		            //如果是IE浏览器
		            if (window.navigator && window.navigator.msSaveOrOpenBlob) {
		            	var csvData = new Blob([data], {type:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
		            	window.navigator.msSaveOrOpenBlob(csvData);
		            	URL.createObjectURL(csvData);
		        		aForExcel = $("<a download='01 项目计划进度偏差.xlsx'><span class='forExcel'>下载excel</span></a>");
		        		$("body").append(aForExcel);
		        		$(".forExcel").click();
		        		aForExcel.remove();
		            }
		            //google或者火狐浏览器
		            else{
		            	var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
		    			var objectUrl = URL.createObjectURL(blob);
		    			aForExcel = $("<a download='01 项目计划进度偏差.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href",objectUrl);
		    			$("body").append(aForExcel);
		    			$(".forExcel").click();
		    			aForExcel.remove();
		            }
		            // 关闭遮罩层
		 			inform.closeLayer();
		 			inform.common("下载成功!");
		            });
		       });
			}else {
     			inform.common(Trans("任务开始时间不得大于任务结束时间,且不得大于当前日期！"));
     		}
	      };

            /**
             * 页面点击项目总数后跳转页面
             */
            $scope.clickModal = function (m) {
                var param = {
                    checkedProj:$scope.checkedProj,
                    dataFlag:$scope.dataFlag,
                    startTime:$scope.start,
                    endTime:$scope.end,
                    selectType:!$scope.dataFlag?'KPI':''
                };
                //写入缓存
                LocalCache.setObject('scheduleDeviationController_department',{
                    department:m,
                    writeFlag:false,
                    searchParam:param
                });
                $state.go('app.office.scheduleDeviationManagement');
            };

	      /**
		     * *************************************************************
		     *              方法声明部分                                结束
		     * *************************************************************
		     */	
		} ]);
})();
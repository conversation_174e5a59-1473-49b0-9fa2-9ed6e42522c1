/*
* @Author: fubaole
* @Date:   2017-09-05 10:05:00
* @Last Modified by:   fubaole
* @Last Modified time: 2018-02-24 13:45:13
*/
(function() {
    'use strict';
  app.factory('demoService', demoService);
  demoService.$inject=["HttpService",'$rootScope'];

  function demoService(HttpService,$rootScope){
    var service={
      getAllInfos:getAllInfos,
      getAllInfosByPage:getAllInfosByPage,
      addObject:addObject
    };
    return service;
    
    /**
     * 获取所有的信息
     * 
     */
	function getAllInfos() {				
		return HttpService.post($rootScope.getWaySystemApi+'demo/getAll',null);
	}
	
    /**
     * 获取所有的信息以分页的形式
     * 
     */
	function getAllInfosByPage(page) {			
		return HttpService.post($rootScope.getWaySystemApi+'demo/getAllzByPage',page);
	}
    /**
     * 新增一条数据
     * 
     * id 编号
     * name 名称
     */
	function addObject(id,name) {
		
		var urlData={'id':id,'name':name};
		return HttpService.post($rootScope.getWaySystemApi+'demo/add',urlData);
	}
  }
})();
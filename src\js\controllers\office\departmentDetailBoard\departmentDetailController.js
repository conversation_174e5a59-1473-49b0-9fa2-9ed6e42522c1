(function () {
    app.controller("departmentDetailController", ['$rootScope','comService', '$scope', '$stateParams', '$state',
        function ($rootScope,comService,$scope, $stateParams, $state) {
            // 顶部选项卡选择
            if($stateParams.typeSelect){
                $scope.typeSelect = $stateParams.typeSelect;
            } else {
                $scope.typeSelect = '1';
            }
            
            $scope.backToList = function () {
                $state.go('app.office.departmentList');
            }

            $scope.toOther = function (typeSelect) {
                $state.go('app.office.departmentDetail', {
                    'typeSelect': typeSelect,
                    'orgCode': $stateParams.orgCode
                });
            }
        }]);
})();
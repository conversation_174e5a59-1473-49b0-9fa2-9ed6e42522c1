
(function () {
	app.controller("tvDisplayManagement", ['$rootScope', 'comService', 'tvService', '$scope', '$state', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http', function ($rootScope, comService, tvService, $scope, $state, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {

	/**
	* *************************************************************
	*             初始化部分                                 开始
	* *************************************************************
	*/

		//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		//分页相关函数
		$scope.getData = getData;
		getData();
	/**
	* *************************************************************
	*              初始化部分                                 结束
	* *************************************************************
	*/

	/**
	* *************************************************************
	*              方法声明部分                                 开始
	* *************************************************************
	*/
	/**
	* 设置列表的高度
	*/
		function setDivHeight() {
			//网页可见区域高度
			var clientHeight = document.body.clientHeight;
			var divHeight = clientHeight - 200;
			$("#divTBDis").height(divHeight);
			$("#subDivTBDis").height(divHeight);
		}
	/**
	* 获取所有项目的版本信息
	*/
		function getData() {
			$scope.jsonData = [];
			tvService.getTvDisplay().then(function (result) {
				if (result.code === AgreeConstant.code) {
					$scope.jsonData = angular.fromJson(result.data);
				} else {
					inform.common(result.message);
				}
			}, function (error) {
				inform.common(Trans("tip.requestError"));
			});
		}
	/**
	* 确认弹框
	*/
		$scope.confirm = function (str, func) {
			var modalInstance = $modal.open({
				templateUrl: 'myModalContent.html',
				controller: 'ModalInstanceCtrl',
				size: "sm",
				resolve: {
					items: function items() {
						return Trans(str);
					}
				}
			});
			modalInstance.result.then(function () {
				func();
			});
		};
	/**
	* 启用任务
	* @param item 当前行的信息
	*/
		$scope.resumeJob = function (item) {
			$scope.confirm("确定要启用该看板吗？", function () {
				tvService.resumeJob(item).then(function (result) {
					fun(result);
				}, function (error) {
					inform.common(Trans("tip.requestError"));
				});
			});
		};
	/**
	* 禁用任务
	* @param item 当前行的信息
	*/
		$scope.stopJob = function (item) {
			$scope.confirm("确定要禁用该看板吗？", function () {
				tvService.stopJob(item).then(function (result) {
					fun(result);
				}, function (error) {
					inform.common(Trans("tip.requestError"));
				});
			});
		};
	/**
	 * 删除任务
	 * @param result
	 */
		$scope.deleteJob = function (item){
			$scope.confirm("确定要删除任务吗？",function () {
                tvService.deleteJob(item).then(function (result){
					fun(result);
				},function (error){
					inform.common(Trans("tip.requestError"));
				});
            })
		}
	/*
	* 通用函数
	* */
	function fun(result) {
		if (result.code === AgreeConstant.code) {
			getData();
		} else {
			inform.common(result.message);
		}
	}
	/**
	* 更新排列顺序
	*/
		$scope.setOrderNum = function (item, element) {
			if (!element) {
				inform.common("不需要此操作！");
				return;
			}
			//两条记录更换顺序
			var urlData = Array(item, element);
			tvService.setOrderNum(urlData).then(function (result) {
				fun(result);
			});
		};
	/**
	* *************************************************************
	*              方法声明部分                                 结束
	* *************************************************************
	*/
	}]);
})();
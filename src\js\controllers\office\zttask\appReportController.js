(function () {
    app.controller("appReportManagement", ['comService', '$rootScope', '$scope', '$state', '$stateParams', '$modal', 'appReportService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, $state, $stateParams, $modal, appReportService, inform, Trans, AgreeConstant, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //日期控件的绑定值和获取
            $scope.datepicker = {
                'currentDate': new Date()
            };
            //前端页面显示flag,为false则显示产品线汇总信息，ture则显示项目汇总信息
            $scope.isProjectFlag = false;
            //checkbox全选,默认置false
            $scope.selectAll = false;
            //产品线列表
            $scope.productLineList = [];
            //页面查询得到的项目信息List
            $scope.projectZtApplyList = [];
            //页面查询得到的产品线禅道应用汇总信息
            $scope.productZtApplyList = [];
            //禅道应用的查询条件
            $scope.ztSearchObj = {
                'startTime': '',
                'endTime': '',
                'line': '',
                'proProjectName': '',
                'projectManager':'',
                'department':'',
                'projectList': []
            };
            //项目状态
            $scope.projectStatus = {
                'closed': '关闭',
                'doing': '进行中',
                'wait': '未开始',
                'suspended': '延期'
            };
            //项目状态下拉框数据源
            $scope.statusSelect = [{
                value: 'doing',
                label: '进行中'
            }, {
                value: 'wait',
                label: '未开始'
            }, {
                value: 'suspended',
                label: '延期'
            }, {
                value: 'closed',
                label: '关闭'
            }];
            //声明查询禅道应用信息的方法
            $scope.getData = getZtApplyInfo;
            //获取初始化时间的方法
            $scope.initTime = initTime;
            //声明下载禅道应用报表的方法
            $scope.downloadZtApplyReport = downloadZtApplyReport;
            //全（不）选方法
            $scope.selectAllProject = selectAllProject;
            //单选方法
            $scope.selectOneProject = selectOneProject;

            //初始化分页信息
            $scope.pages = inform.initPages();
            //初始化产品线list和页面的起止时间
            init();
            //初始查询产品线的禅道汇总结果
            getZtApplyInfo($scope.pages.pageNum);
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 80);
                $("#subDivTB").height(divHeight - 80);
            }

            /**
             * 重置
             */
            $scope.reset = function () {
                $scope.ztSearchObj.line = "";
                $scope.ztSearchObj.proProjectName = "";
                $scope.ztSearchObj.projectManager = "";
                $scope.ztSearchObj.department = "";
                $scope.ztSearchObj.status = "doing";
                initTime();
            };
            /**
             * 开始时间
             */
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;    //开始时间
                $scope.openedEnd = false;
            };
            /**
             *  结束时间
             */
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;    //结束时间
            };

            /**
             * 初始化检索条件开始时间 及 结束时间
             */
            function initTime() {
                var date = new Date();
                //结束时间默认为当前日期
                $scope.ztSearchObj.endTime = inform.format(date, 'yyyy-MM-dd');
                if ($scope.isProjectFlag) {
                	//项目信息,开始日期向前推三个月（90天）
                    date.setMonth(date.getMonth() - 3);
                } else {
                	//产品线信息,开始时间默认为上一年度的开始时间
                    var time = $scope.ztSearchObj.endTime.split("-");
                    date = (parseInt(time[0])-1)+"/01"+"/01";
                    //项目状态默认显示进行中
                    $scope.ztSearchObj.status = "doing";
                }
                //对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
                $scope.ztSearchObj.startTime = inform.format(date, 'yyyy-MM-dd');
            }

            /**
             * 页面初始化
             */
            function init() {
                //获取产品线
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.productLineList = data.data;
                    }
                });
                //获取部门
				$scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentList = comService.getDepartment(data.data);
                });
                //初始化检索条件开始时间 及 结束时间
                initTime();
            }

            /**
             *  查询禅道应用信息
             */
            function getZtApplyInfo(pageNum) {
                //根据isProjectFlag查询产品线信息还是项目信息
                if ($scope.isProjectFlag) {
                    getProjectZtApplyInfo(pageNum);
                } else {
                    getProductZtApplyInfo();
                }
            }

            /**
             *  获取项目的数据
             */
            function getProjectZtApplyInfo(pageNum) {
                var postData = {
                    'startTime': inform.format($scope.ztSearchObj.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.ztSearchObj.endTime, 'yyyy-MM-dd'),
                    'line': $scope.ztSearchObj.line,
                    'proProjectName': $scope.ztSearchObj.proProjectName,
                    'status': $scope.ztSearchObj.status,
                    'projectManager':$scope.ztSearchObj.projectManager,
                    'department':$scope.ztSearchObj.department,
                    'currentPage': pageNum,//当前页数
                    'pageSize': $scope.pages.size//每页显示条数
                };
                appReportService.getProjectResultByParam(postData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.projectZtApplyList = data.data.list;
                            //分页信息设置
                            $scope.pages.total = data.data.total;
                            $scope.pages.star = data.data.startRow;
                            $scope.pages.end = data.data.endRow;
                            if (data.data.total === 0) {
                                inform.common(Trans("tip.noData"));
                                return;
                            }
                        }
                        inform.common(data.message);
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            /**
             *  获取项目的数据
             */
            function getProductZtApplyInfo() {
                var postData = {
                    'startTime': inform.format($scope.ztSearchObj.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.ztSearchObj.endTime, 'yyyy-MM-dd'),
                    'status': $scope.ztSearchObj.status
                };
                appReportService.getProductLineResult(postData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.productZtApplyList = data.data;
                            var finalSum = $scope.productZtApplyList.pop();
                            angular.forEach($scope.productLineList, function(one, i) {
                            	var list = []; 
                            	angular.forEach($scope.productZtApplyList, function(oneLine, i) {
                            		list.push(oneLine.line);
                            	});
                          		if (list.indexOf(one.param_value) === -1){
                          			 $scope.productZtApplyList.push({
                          				 "line":one.param_value,
                          				 "projectNum":0,
                          				 "projectInTime":0,
                          				 "projectInTimePer":0,
                          				 "taskNum":0,
                          				 "taskInTime":0,
                          				 "taskInTimePer":0,
                          				 "bugNum":0,
                          				 "bugInTime":0,
                          				 "bugInTimePer":0,
                          				 "bugActivatedCount":'0',
                          				 "bugActivatedPer":'0'
                          			 });
                          		 }
                      	     });
                            if (finalSum==null){
                            	$scope.productZtApplyList.push({
                     				 "line":"汇总",
                     				 "projectNum":0,
                     				 "projectInTime":0,
                     				 "projectInTimePer":0,
                     				 "taskNum":0,
                     				 "taskInTime":0,
                     				 "taskInTimePer":0,
                     				 "bugNum":0,
                     				 "bugInTime":0,
                     				 "bugInTimePer":0,
                     				 "bugActivatedCount":'0',
                     				 "bugActivatedPer":'0'
                     			 });
                            	return;
                            }
                            $scope.productZtApplyList.push(finalSum);
                            if (data.data.length === 0) {
                                inform.common(Trans("tip.noData"));
                                return;
                            }
                        }
                        inform.common(data.message);
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }


            /**
             * 下载禅道应用报表
             */
            function downloadZtApplyReport() {
                //根据isProjectFlag判断下载汇总或是任务/bug的Excel
                if ($scope.isProjectFlag) {
                    getBugAndTaskDetails();
                } else {
                    getTotalAndProjectDetails();
                }
            }

            /**
             * 下载禅道应用的汇总和项目明细相关Excel表格
             */
            function getTotalAndProjectDetails() {
                inform.modalInstance("确定要下载吗！").result.then(function () {
                    //入参只保留项目开始时间的区间
                    var param = {
                        'startTime': inform.format($scope.ztSearchObj.startTime, 'yyyy-MM-dd'),
                        'endTime': inform.format($scope.ztSearchObj.endTime, 'yyyy-MM-dd'),
                        'status': $scope.ztSearchObj.status
                    };
                    inform.downLoadFile(
                        'ztApply/getTotalAndProjectDetails',
                        param,
                        "禅道应用情况报表-汇总及项目详情.xlsx");
                });
            }

            /**
             * 下载禅道应用的任务和bug明细相关Excel表格
             */
            function getBugAndTaskDetails() {
                //项目列表不可以为空,空的话就提示
                if($scope.ztSearchObj.projectList.length === 0){
                    inform.common("请选择要下载明细的项目列表");
                    return;
                }
                //入参有bug/task开始时间的区间和产品线、项目list
                var param = {
                    'startTime': inform.format($scope.ztSearchObj.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.ztSearchObj.endTime, 'yyyy-MM-dd'),
                    'line': $scope.ztSearchObj.line,
                    'proProjectName': $scope.ztSearchObj.proProjectName,
                    'status': $scope.ztSearchObj.status,
                    'projectManager':$scope.ztSearchObj.projectManager,
                    'department':$scope.ztSearchObj.department,
                    'projectList': $scope.ztSearchObj.projectList
                };
                appReportService.getTaskNum(param).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            //如果大于2000条,不予导出
                            if (data.data > 2000) {
                                inform.common("要下载的任务明细共" + data.data + "条,大于2000条,\n" +
                                    "不予导出,请通过查询条件缩小查询范围");
                                return;
                            }
                            inform.modalInstance("***   要下载的任务明细共" + data.data + "条,确定要下载吗！").result.then(function () {
                                inform.downLoadFile(
                                    'ztApply/getBugAndTaskDetails',
                                    param,
                                    "禅道应用情况报表-任务及bug详情.xlsx");
                            });
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });

            }

            /**
             * 全（不）选项目
             */
            function selectAllProject() {
                $scope.ztSearchObj.projectList = [];
                //若为全选,则通过foreach循环将项目的id写入到projectList中
                if ($scope.selectAll) {
                    angular.forEach($scope.projectZtApplyList, function (i){
                        $scope.ztSearchObj.projectList.push(i.id);
                        i.checked = true;
                    });
                    return;
                }
                //若为全不选,则设置所有项为不选
                angular.forEach($scope.projectZtApplyList, function (i){
                    i.checked = false;
                });

            }

            /**
             * 单选checkbox改变时调用的方法
             *
             * @param item  checkbox对应的项目记录
             */
            function selectOneProject(item) {
                //查询项目列表中是否有该条记录
                var index = $scope.ztSearchObj.projectList.indexOf(item.id);
                //如果为选中且不在项目列表中,则向projectList中添加该projectId
                if (index === -1 && item.checked) {
                    $scope.ztSearchObj.projectList.push(item.id);
                }
                //如果为不选中且项目列表中有该id,则移除指定元素
                if (index !== -1 && !item.checked) {
                    $scope.ztSearchObj.projectList.splice(index, 1);
                }
                //如果选中数量等于本页数据的数量,则全选标识selectAll变为true,否则为false
                $scope.selectAll = ($scope.projectZtApplyList.length === $scope.ztSearchObj.projectList.length);
            }

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

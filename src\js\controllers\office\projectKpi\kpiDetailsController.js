(function () {
    'use strict';
    app.controller("kpiDetailsController", ['$scope','$state','comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','kpiInfoService','kpiManagemetService','$stateParams','LocalCache', '$modal','$http',
        function ($scope,$state,comService, $rootScope, inform, Trans, AgreeConstant,kpiInfoService,kpiManagemetService,$stateParams,LocalCache, $modal,$http) {

		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		$scope.limitList = AgreeConstant.limitList; // 正则校验配置
        $scope.topInfo = LocalCache.getObject('kpiInfo_topInfo');
        $scope.seeRule=seeRule;

        $scope.proDefaultList = {
             'KPI_0_01':copyBaseKpiDetail(),
             'KPI_0_02':copyBaseKpiDetail(),
             'KPI_0_03':copyBaseKpiDetail(),
             'KPI_0_04':copyBaseKpiDetail(),
             'KPI_0_05':copyBaseKpiDetail(),
             'KPI_0_06':copyBaseKpiDetail(),
             'score':'-'
        }

        $scope.proDefaultList.KPI_0_01.returnFunction = 'kpi001Details';
        $scope.proDefaultList.KPI_0_02.returnFunction = 'kpi002Details';
        $scope.proDefaultList.KPI_0_03.returnFunction = 'kpi003Details';
        $scope.proDefaultList.KPI_0_04.returnFunction = 'kpi004Details';
        $scope.proDefaultList.KPI_0_06.dataDetail = '-';
        $scope.proDefaultList.score = $scope.topInfo.score;

        $scope.proDefaultList.KPI_0_01.KPI_0_01_01 = copyBaseKpiDetail();
        $scope.proDefaultList.KPI_0_01.KPI_0_01_02 = copyBaseKpiDetail();
        $scope.proDefaultList.KPI_0_02.KPI_0_02_01 = copyBaseKpiDetail();
        $scope.proDefaultList.KPI_0_02.KPI_0_02_02 = copyBaseKpiDetail();
        $scope.proDefaultList.KPI_0_03.KPI_0_03_01 = copyBaseKpiDetail();
        $scope.proDefaultList.KPI_0_03.KPI_0_03_02 = copyBaseKpiDetail();
        $scope.proDefaultList.KPI_0_03.KPI_0_03_03 = copyBaseKpiDetail();
        $scope.proDefaultList.KPI_0_04.KPI_0_04_01 = copyBaseKpiDetail();
        $scope.proDefaultList.KPI_0_05.KPI_0_05_01 = copyBaseKpiDetail();

        $scope.teamDefaultList = {
             'KPI_1_01':copyBaseKpiDetail(),
             'KPI_1_02':copyBaseKpiDetail(),
             'KPI_1_03':copyBaseKpiDetail(),
             'KPI_1_04':copyBaseKpiDetail(),
             'KPI_1_05':copyBaseKpiDetail(),
             'score':'-'
        }

        $scope.teamDefaultList.KPI_1_01.returnFunction = 'kpi101Details';
        $scope.teamDefaultList.KPI_1_02.returnFunction = 'kpi102Details';
        $scope.teamDefaultList.KPI_1_03.returnFunction = 'kpi103Details';
        $scope.teamDefaultList.KPI_1_05.dataDetail = '-';
        $scope.teamDefaultList.score = $scope.topInfo.score;

        $scope.teamDefaultList.KPI_1_01.KPI_1_01_01 = copyBaseKpiDetail();
        $scope.teamDefaultList.KPI_1_01.KPI_1_01_02 = copyBaseKpiDetail();
        $scope.teamDefaultList.KPI_1_02.KPI_1_02_01 = copyBaseKpiDetail();
        $scope.teamDefaultList.KPI_1_02.KPI_1_02_02 = copyBaseKpiDetail();
        $scope.teamDefaultList.KPI_1_03.KPI_1_03_01 = copyBaseKpiDetail();
        $scope.teamDefaultList.KPI_1_03.KPI_1_03_02 = copyBaseKpiDetail();
        $scope.teamDefaultList.KPI_1_03.KPI_1_03_03 = copyBaseKpiDetail();
        $scope.teamDefaultList.KPI_1_03.KPI_1_03_04 = copyBaseKpiDetail();
        $scope.teamDefaultList.KPI_1_04.KPI_1_04_01 = copyBaseKpiDetail();

        getFirstKpi();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */
		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */
        /**
         * 复制KPI指标数据结构
         */
         function copyBaseKpiDetail() {
            return {
                'baseLine':'-',
                'realityVal':'-',
                'weight':'-',
                'weightB':'',
                'score':'-',
                'remark':'',
                'dataDetail':''
            };
         }
		/**
		 * 获取一级指标数据
		 */
        function getFirstKpi(){
            var urlData = {
                'kpiId': $stateParams.kpiId
            };
            kpiInfoService.getFirstKpi(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    //获取一级指标数据，判断是项目还是团队（根据返回一级指标CODE中是否包含KPI_0）
                    for (var i=0;i<data.data.length;i++){
                        var one = data.data[i];
                        //由于一级指标下有returnFunction初始化定义，所以不可以直接$scope.proDefaultList[one.kpiCode]=one;
                        if (one.kpiCode.indexOf("KPI_0")>-1){
                            $scope.proDefaultList[one.kpiCode] = one/*.baseLine;
                            $scope.proDefaultList[one.kpiCode].weight = one.weight;
                            $scope.proDefaultList[one.kpiCode].weightB = one.weightB;
                            $scope.proDefaultList[one.kpiCode].score = one.score;
                            $scope.proDefaultList[one.kpiCode].id = one.id;
                            $scope.proDefaultList[one.kpiCode].kpiCode = one.kpiCode;
                            $scope.proDefaultList[one.kpiCode].dataDetail = one.dataDetail;*/
                        } else {
                            $scope.teamDefaultList[one.kpiCode] = one/*.baseLine;
                            $scope.teamDefaultList[one.kpiCode].weight = one.weight;
                            $scope.teamDefaultList[one.kpiCode].weightB = one.weightB;
                            $scope.teamDefaultList[one.kpiCode].score = one.score;
                            $scope.teamDefaultList[one.kpiCode].id = one.id;
                            $scope.teamDefaultList[one.kpiCode].kpiCode = one.kpiCode;
                            $scope.teamDefaultList[one.kpiCode].dataDetail = one.dataDetail;*/
                        }
                        //循环返回的一级指标数据，调用查询二级指标接口，动态给list赋值，
                        getSecKpi(one.kpiCode);
                    }
                } else {
                    inform.common(data.message);
                }
            },
            function () {
                inform.common(Trans("tip.requestError"));
            });
        }
        /**
		 * 获取二级指标数据
		 */
        function getSecKpi(firstKpiCode){
            var urlData = {
                'kpiId': $stateParams.kpiId,
                'firstKpiCode':firstKpiCode
            };
            kpiInfoService.getSecKpi(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    //查询二级指标接口，根据一级指标CODE与二级指标Code动态给list赋值，
                    for (var i=0;i<data.data.length;i++){
                        var one = data.data[i];
                        if (one.subKpiCode.indexOf("KPI_0")>-1){
                            $scope.proDefaultList[firstKpiCode][one.subKpiCode] = one;
                        } else {
                            $scope.teamDefaultList[firstKpiCode][one.subKpiCode] = one;
                        }
                    }
                } else {
                    inform.common(data.message);
                }
            },
            function () {
                inform.common(Trans("tip.requestError"));
            });
        }
        /**
		 * 查看规则详情
		 */
        function seeRule(firstKpiCode){
            var kpiInfo = {};
            var returnFunction = "";
            if (firstKpiCode.indexOf('KPI_0')>-1){
                //项目
                kpiInfo =  $scope.proDefaultList[firstKpiCode];
                returnFunction = $scope.proDefaultList[firstKpiCode].returnFunction;
            } else {
                //团队
                kpiInfo =  $scope.teamDefaultList[firstKpiCode];
                returnFunction = $scope.teamDefaultList[firstKpiCode].returnFunction;
            }
            LocalCache.setObject('kpiDetails_kpiInfo', kpiInfo);
            $state.go("app.office."+firstKpiCode, { kpiId:$stateParams.kpiId,
                                                    firstKpiCode:firstKpiCode,
                                                    returnFunction:returnFunction});
        }
        /**
         * 检查权重信息
         */
        function checkWeight (name,urlData){
            var list = document.getElementsByName(name);
            var sum = 0;
    	    //获取页面所有该级指标
    		for(var i=0;i<list.length;i++){
    		    sum = sum*1 + list[i].value*1;
    		    if (!list[i].value){
    		        return 0;
    		    }
    		    urlData.push({
                    'id':list[i].id,
                    'weight':list[i].value
    		    })
    	    }
    	    return sum;
        }
        /**
         * 保存权重方法
         */
        $scope.saveKpiWeight = function (type){
            var urlData = {
                'type':type,
                'kpiId':$stateParams.kpiId,
                'detailData':[]
            }
            //判断是项目还是团队
            if (type==='0'){
                //检查权重数据（是否为空，是否为100加和）
                if (checkWeight("proOne",urlData.detailData)!==100){
                    inform.common('一级指标权重加和不为100或者权重输入不合规，请检查。');
                    return;
                };
                //检查交付质量下的二级指标数据
                if (checkWeight("pro02Two",urlData.detailData)!==100){
                    inform.common('交付质量下的二级指标权重加和不为100或者权重输入不合规，请检查。');
                    return;
                };
                //检查交付过程质量下的二级指标数据
                if (checkWeight("pro03Two",urlData.detailData)!==100){
                    inform.common('交付过程质量下的二级指标权重加和不为100或者权重输入不合规，请检查。');
                    return;
                };
            } else {
                //检查权重数据（是否为空，是否为100加和）
                if (checkWeight("teamOne",urlData.detailData)!==100){
                    inform.common('一级指标权重加和不为100或者权重输入不合规，请检查。');
                    return;
                };
                //检查交付能力下的二级指标数据
                if (checkWeight("team01Two",urlData.detailData)!==100){
                    inform.common('交付能力下的二级指标权重加和不为100或者权重输入不合规，请检查。');
                    return;
                };
                //检查交付质量下的二级指标数据
                if (checkWeight("team02Two",urlData.detailData)!==100){
                    inform.common('交付质量下的二级指标权重加和不为100或者权重输入不合规，请检查。');
                    return;
                }
                //检查交付过程质量下的二级指标数据
                if (checkWeight("team03Two",urlData.detailData)!==100){
                    inform.common('交付过程质量下的二级指标权重加和不为100或者权重输入不合规，请检查。');
                    return;
                }
            }
            kpiManagemetService.saveKpiWeight(urlData).then(function (data) {
                inform.common(data.message);
            },
            function () {
                inform.common(Trans("tip.requestError"));
            });
        }

        /**
         * 导出Excel
         */
        $scope.toExcel = function () {
            if($scope.topInfo.state==='0'){
                inform.common("当前为待考核状态，禁止下载考核数据。");
                return;
            }
            inform.modalInstance("确定要下载吗?").result.then(function () {
                inform.downLoadFile('kpiInfo/toExcel',$stateParams.kpiId,$scope.topInfo.name+'--KPI考核数据.xlsx');
            });
        };
	     /**
          * *************************************************************
          *              方法声明部分                                 结束
          * *************************************************************
          */
         }
    ]);
})();
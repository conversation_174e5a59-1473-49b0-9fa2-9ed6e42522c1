
(function () {
    app.controller("immediateRewardController", ['$rootScope', 'comService', 'SystemService', 'tvService', '$scope', '$state','$location', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function ($rootScope, comService, SystemService, tvService, $scope, $state,$location, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {

        /**
         * *************************************************************
         *             初始化部分                                 开始
         * *************************************************************
         */
        // 捕捉鼠标事件并反馈给父页面
        document.addEventListener('click',function () {
            $('#iframeDiv',window.parent.document).click();
        },false);
        // 参数
        var params = $location.search();
        //窗体大小变化时重新计算高度
        $(window).resize(setCss);
        // 从后端获取相关数据
        getData();
        // 名字高度的最大值
        var nameSizeMax;
        // 设置css
        setCss();
        // 页面中内容对象
        var valueObj = {
            valueList:[] ,	//即时奖励集合
            valueIndex:-1      //播放即时奖励的下标
        };
        // 延时执行函数
        var timeoutFun;
        // 间隔时间
        var playDuration;
        // 名字大小
        var nameFontSize = 10;
        // 播放名字标志位
        var nameFlag = -1;
        // 名字的播放列表
        var nameList = [];
        // 播放名字时的延时函数
        var nameSizeChangeSetTimeOut;

        /**
         * *************************************************************
         *              初始化部分                                 结束
         * *************************************************************
         */

        /**
         * *************************************************************
         *              方法声明部分                                 开始
         * *************************************************************
         */

        /**
         * 获取信息
         */
        function getData() {
            var urlData = {
                'id': params.tvFrameId
            };
            tvService.getTvFrameDetail(urlData).then(function (result) {
                if (result.code === AgreeConstant.code) {
                    var data = angular.fromJson(result.data);
                    // 从参数列表中找到即时奖励内容和名字列表，并匹配
                    valueObj.valueList = data.tvParamList.filter(item => item.paramName.indexOf('message') >= 0).map(item => {
                        item.nameList = data.tvParamList.filter(ele => ele.paramName.indexOf('nameList') >= 0
                            && ele.paramName.substring(ele.paramName.indexOf('nameList') + 'nameList'.length) === item.paramName.substring(item.paramName.indexOf('message') + 'message'.length))
                            .map(ele => ele.paramValue)[0];
                        return item;
                    });
                    playDuration = parseInt(data.playDuration)/valueObj.valueList.length * 1000;
                    showPlay();
                } else {
                    inform.common(result.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        }
        /**
         * 设置css样式
         */
        function setCss() {
            //网页可见区域高度
            let clientHeight = document.body.clientHeight;
            //网页可见区域宽度
            let clientWidth = document.body.clientWidth;
            //名字高度的最大值
            nameSizeMax = clientHeight / 25;
            $('#bodyDiv').css({'height': clientHeight,'background-image': 'url(' + params.imgUrl + ')'});
            $('#titleDiv').css({'font-size': parseInt(clientWidth / 25),
                'padding-top': parseInt(clientWidth / 22)
            });
            $('#contentDiv').css({'width': clientWidth * 0.9,
                'font-size': parseInt(clientWidth / 50)});
            $('#nameDiv').css({'width': clientWidth,
                'height': parseInt(clientWidth / 20),
                'margin-top': parseInt(-clientHeight + clientWidth / 3 + clientWidth / 20)
            });
        }
        /*
        * 从第一页开始播放
        * */
        $scope.isShow = function(){
            nameFlag = -1
            valueObj.valueIndex = -1;
            showPlay();
        }
        /*
        * 页面隐藏的时候停止播放
        * */
        $scope.isHide = function(){
            clearTimeout(nameSizeChangeSetTimeOut);
            clearTimeout(timeoutFun);
        }

        /*
        * 自动滚动
        * */
        function showPlay() {
            clearTimeout(nameSizeChangeSetTimeOut);
            var index = (valueObj.valueIndex + 1) % valueObj.valueList.length;
            valueObj.valueIndex = index;
            if (valueObj.valueList[index].nameList) {
                nameFontSize = 10;
                // 当前页播放的名字列表
                nameList = valueObj.valueList[index].nameList.split('、');
                // 播放名字
                showName();
            }else {
                $('#nameDiv').html('');
                nameList = [''];
            }
            $('#titleDiv').html(valueObj.valueList[index].paramValue);
            $('#contentDiv').html('&emsp;&emsp;' + valueObj.valueList[index].remark);
            timeoutFun = setTimeout(showPlay,playDuration);
        }
        /*
        * 播放名字
        * */
        function showName() {
            nameFontSize = 10;
            $('#nameDiv').css('font-size', nameFontSize);
            ++nameFlag;
            let count = (nameFlag) % nameList.length;
            $('#nameDiv').html(nameList[count]);
            nameSizeChange();
        }
        /*
        * 改变名字大小
        * */
        function nameSizeChange() {
            if (nameFontSize > nameSizeMax) {
                setTimeout(showName,100);
            } else {
                nameSizeChangeSetTimeOut = setTimeout(()=>{
                    ++nameFontSize;
                    $('#nameDiv').css('font-size', nameFontSize);
                    nameSizeChange();
                },30);
            }
        }
        /**
         * *************************************************************
         *              方法声明部分                                 结束
         * *************************************************************
         */
    }]);
})();
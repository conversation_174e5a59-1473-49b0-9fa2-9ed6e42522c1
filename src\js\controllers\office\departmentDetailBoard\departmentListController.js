(function () {
    app.controller("departmentListController", ['LocalCache', '$rootScope','comService','AgreeConstant', '$scope', '$stateParams', '$state', 'deptBoardFactory', 'departmentListService', 'inform', 'Trans',
        function (LocalCache, $rootScope,comService,AgreeConstant, $scope, $stateParams, $state, deptBoardFactory, departmentListService, inform, Trans) {
            // 初始化
            deptBoardFactory.init($scope, '1');
            // 重置部分
            $scope.resetParam = resetParam;
            function resetParam(){
                deptBoardFactory.initTime($scope, '本年度');
            }
            $scope.toDetailBoard = function (item) {
                var departmentList_formRefer = $scope.formRefer;
                departmentList_formRefer['searchTimeString'] = $scope.butFlag;
                LocalCache.setObject('departmentList_formRefer', departmentList_formRefer);
                $state.go('app.office.departmentDetail', {
                    'typeSelect': '1',
                    'orgCode': item.orgCode
                });
            }
            $scope.getData = getData;
            function getData() {
                $scope.secondTitle = ['计划完成率', '客户需求完成率', '市场里程碑按时完成率','冲刺成功率', '线上问题数量', '严重线上问题数量', '缺陷逃逸', '开发过程reopen比率', '人均Bug', '培训', '知识库'];
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01'
                }
                departmentListService.getDeptListIndex(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.tableList = result.data
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            // 页面加载后触发
            $scope.$watch('$viewContentLoaded', function () {
                var localFormRefer = LocalCache.getObject('departmentList_formRefer');
                if (Object.keys(localFormRefer).length > 0) {
                    LocalCache.setObject("departmentList_formRefer",{});
                }
                getData();
            });
        }]);
})();
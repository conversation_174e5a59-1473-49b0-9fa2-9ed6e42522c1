/**
 * Vue3 版本考勤明细页面的 AngularJS 控制器
 * 主要负责数据传递和事件处理
 */
(function() {
    'use strict';

    angular.module('app').controller('attendanceDetailVue', [
        '$scope',
        '$http',
        '$state',
        '$stateParams',
        'VueComponentService',
        'comService',
        function($scope, $http, $state, $stateParams, VueComponentService, comService) {
            var vm = this;

            // 初始化数据
            vm.loading = false;
            vm.vueProps = {
                initialData: {}
            };

            /**
             * 处理来自 Vue 组件的事件
             */
            vm.handleVueEvent = function(eventName, data) {
                console.log('Vue Event:', eventName, data);
                
                switch(eventName) {
                    case 'search':
                        vm.handleSearch(data);
                        break;
                    case 'export':
                        vm.handleExport(data);
                        break;
                    case 'pageChange':
                        vm.handlePageChange(data);
                        break;
                    default:
                        console.log('Unhandled Vue event:', eventName, data);
                }
            };

            /**
             * 处理搜索请求
             */
            vm.handleSearch = function(searchParams) {
                vm.loading = true;
                
                // 构建请求参数
                var params = {
                    primaryDeptCode: searchParams.primaryDeptCode,
                    departmentCode: searchParams.departmentCode,
                    realName: searchParams.realName,
                    status: searchParams.status,
                    region: searchParams.region,
                    startTime: searchParams.startTime,
                    endTime: searchParams.endTime,
                    holidayWorkFlag: searchParams.holidayWorkFlag,
                    pageNum: searchParams.pageNum || 1,
                    pageSize: searchParams.pageSize || 20,
                    type: searchParams.type || '1'
                };

                // 调用后端API
                var apiUrl = $scope.getWaySystemApi + 'office/attendance/detail';
                
                $http.post(apiUrl, params).then(function(response) {
                    vm.loading = false;
                    
                    if (response.data && response.data.success) {
                        // 更新 Vue 组件的数据
                        vm.updateVueData(response.data.data);
                    } else {
                        comService.alertError('查询失败：' + (response.data.message || '未知错误'));
                    }
                }).catch(function(error) {
                    vm.loading = false;
                    console.error('API Error:', error);
                    comService.alertError('网络请求失败，请稍后重试');
                });
            };

            /**
             * 处理导出请求
             */
            vm.handleExport = function(exportParams) {
                var params = {
                    primaryDeptCode: exportParams.primaryDeptCode,
                    departmentCode: exportParams.departmentCode,
                    realName: exportParams.realName,
                    status: exportParams.status,
                    region: exportParams.region,
                    startTime: exportParams.startTime,
                    endTime: exportParams.endTime,
                    holidayWorkFlag: exportParams.holidayWorkFlag,
                    type: exportParams.type || '1'
                };

                // 构建下载URL
                var downloadUrl = $scope.getWaySystemApi + 'office/attendance/export?' + 
                    Object.keys(params).map(function(key) {
                        return key + '=' + encodeURIComponent(params[key] || '');
                    }).join('&');

                // 触发下载
                window.open(downloadUrl, '_blank');
            };

            /**
             * 处理分页变化
             */
            vm.handlePageChange = function(pageParams) {
                // 重新搜索数据
                vm.handleSearch(pageParams);
            };

            /**
             * 更新 Vue 组件的数据
             */
            vm.updateVueData = function(data) {
                vm.vueProps = {
                    attendanceDetailList: data.attendanceDetailList || [],
                    workOverTimeGroupByPersonList: data.workOverTimeGroupByPersonList || [],
                    summaryData: data.summaryData || [],
                    pagination: data.pagination || {
                        pageNum: 1,
                        size: 20,
                        total: 0
                    },
                    primaryDeptList: vm.primaryDeptList || [],
                    departmentList: vm.departmentList || [],
                    areaList: vm.areaList || []
                };
            };

            /**
             * 加载基础数据
             */
            vm.loadBaseData = function() {
                // 加载一级部门列表
                vm.loadPrimaryDeptList();
                // 加载地区列表
                vm.loadAreaList();
            };

            /**
             * 加载一级部门列表
             */
            vm.loadPrimaryDeptList = function() {
                var url = $scope.getWaySystemApi + 'office/dept/primary';
                $http.get(url).then(function(response) {
                    if (response.data && response.data.success) {
                        vm.primaryDeptList = response.data.data || [];
                        vm.updateVueData({});
                    }
                });
            };

            /**
             * 加载地区列表
             */
            vm.loadAreaList = function() {
                var url = $scope.getWaySystemApi + 'office/area/list';
                $http.get(url).then(function(response) {
                    if (response.data && response.data.success) {
                        vm.areaList = response.data.data || [];
                        vm.updateVueData({});
                    }
                });
            };

            /**
             * 初始化页面
             */
            vm.init = function() {
                // 等待 Vue 组件库加载完成
                VueComponentService.waitForVueComponents(function(error) {
                    if (error) {
                        console.error('Vue Components loading failed:', error);
                        comService.alertError('Vue 组件加载失败，请刷新页面重试');
                        return;
                    }

                    // 加载基础数据
                    vm.loadBaseData();
                    
                    // 初始化搜索
                    vm.handleSearch({
                        pageNum: 1,
                        pageSize: 20,
                        type: '1'
                    });
                });
            };

            // 页面初始化
            vm.init();

            // 暴露给模板使用
            $scope.vm = vm;
            $scope.vueProps = vm.vueProps;
            $scope.handleVueEvent = vm.handleVueEvent;
        }
    ]);

})();

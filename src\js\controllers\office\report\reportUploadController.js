
(function () {
    app.controller("reportUploadController", ['comService', '$http', 'LocalCache', '$rootScope', '$state', '$stateParams', '$scope', '$modal', 'reportService', 'reviewProblemService', 'inform', 'Trans', 'AgreeConstant', 'proProjectModule',
        function (comService, $http, LocalCache, $rootScope, $state, $stateParams, $scope, $modal, reportService, reviewProblemService, inform, Trans, AgreeConstant, proProjectModule) {

        /**
         * *************************************************************
         *             初始化部分                                 开始
         * *************************************************************
         */
        //是否创建禅道任务
        $scope.taskFlag = true;
        $scope.taskInfo={};
        // 正则校验配置
        $scope.limitList = AgreeConstant.limitList;
        //控制评审时间字段是否必填
        $scope.planCompletionDatFlag = false;
        //校验变量
        $scope.limitList = AgreeConstant.limitList;
        //页面分页信息
        $scope.pages = {
            pageNum: '', //分页页数
            size: '', //分页每页大小
            total: '' //数据总数
        };
        //新增表单绑定的参数
        $scope.spec = {};
        //评委列表
        $scope.jList = [];
        // 问题列表中的错误名字或者重复名字
        $scope.problemRedName = "";
        // 问题列表是否显示名字错误的提示信息
        $scope.problemErrIsShow = false;
        // 评委列表中的错误名字或者重复名字
        $scope.judgeRedName = "";
        // 评委列表是否显示名字错误的提示信息
        $scope.judgeErrIsShow = false;
        //季度下拉框数据源
        $scope.quarterSelect = [{
            value: '0',
            label: '第1季度'
        }, {
            value: '1',
            label: '第2季度'
        }, {
            value: '2',
            label: '第3季度'
        }, {
            value: '3',
            label: '第4季度'
        }];
        //评审主题Map
        $scope.themeMap = reviewThemeListConfig;
        //评审级别下拉框数据源
        $scope.levelSelect = [{
            value: '0',
            label: '一级'
        }, {
            value: '1',
            label: '二级'
        }, {
            value: '2',
            label: '三级'
        }];

        //评审结果下拉框数据源
        $scope.resSelect = [{
            value: '0',
            label: '有条件通过'
        }, {
            value: '1',
            label: '通过'
        }, {
            value: '2',
            label: '不通过'
        }, {
            value: '3',
            label: '未评审'
        }];

        //是否跟踪下拉框数据源
        $scope.trackSelect = [{
            value: '0',
            label: '是'
        }, {
            value: '1',
            label: '否'
        }];

        //是否跟踪下拉框数据源
        $scope.typeMap = [{
            value: '0',
            label: '邮件'
        }, {
            value: '1',
            label: '会议'
        }];

        //初始化问题级别下拉框
        $scope.problemLevel = problemLevelConfig;
        $scope.acceptLevel = [{
            label: "是",
            value: "0"
        }, {
            label: "否",
            value: "1"
        }];
        //问题列表
        $scope.problemList = [];

        //预评审list
        $scope.preRerviewList = [];

        //初始化页面信息
        initPages();
        //设置列表的高度
        setDivHeight();
        //窗体大小变化时重新计算高度
        $(window).resize(setDivHeight);
        //当评审时间发生改变时，自动将季度回填
        $scope.dateToQuarter = dateToQuarter;
        //选择项目时，回填产品线项目经理、项目助理信息
        $scope.projectChange = projectChange;
        //根据关键角色名称，获取其等级
        $scope.setJudgeGrade = setJudgeGrade;
        //新增一个评委，重新计算
        $scope.addNewBind = addNewBind;
        //持续时间变化时,说明是会议
        $scope.duringChange = duringChange;
        //名字校验
        $scope.nameCheck = nameCheck;

        //设置列表的高度
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (150 + 180);
            $("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight - 50);
            var clientWidth = document.body.clientWidth;
            $("#buttonStyle").css(inform.getButtonStyle(clientHeight, clientWidth));
        }

        /**
         * *************************************************************
         *              初始化部分                                 结束
         * *************************************************************
         */

        /**
         * *************************************************************
         *              方法声明部分                                 开始
         * *************************************************************
         */

          /**
          *问题分类变动修改问题级别
          */
         $scope.changeProblemType = function (indexVal) {
             $scope.problemList[indexVal].level = problemTypeLevelConfig[$scope.problemList[indexVal].problemType];
         };

       /**
         * 根据关键角色名称，遍历以获取其编号
         * @param item
         */
        function setJudgeNo(item){

            for(var i = 0;i <$scope.employeeList.length;i++){
                if($scope.employeeList[i].realName === item.judge){
                    item.employeeId = $scope.employeeList[i].employeeNo;
                }
            }
        }

        /*
        * 导入的文件信息赋值给页面
        * */
        function dataInitPages() {
            // 获取缓存
            var reviewData = LocalCache.getObject('reportUploadExcelData');
            // 清除缓存
            LocalCache.setObject("reportUploadExcelData",{});
            // 评审时间
            $scope.spec.PLAN_COMPLETION_DATE = reviewData.reviewDate;
            // 季度
            dateToQuarter();
            // 评审内容
            $scope.spec.REVIEW_CONTENT = reviewData.reviewContent;
            // 评审类别
            if (reviewData.mode) {
                $scope.spec.REVIEW_TYPE = $scope.typeMap.find(function (item) {
                    return item.label === reviewData.mode;
                }).value;
            }
            // 评审结果
            if (reviewData.reviewResult) {
                $scope.spec.REVIEW_RESULT = $scope.resSelect.find(function (item) {
                    return item.label === reviewData.reviewResult;
                }).value;
            }
            // 评审材料责任人
            $scope.spec.LIABLE_PERSON = reviewData.reviewMaterialResPerson;
            // 评审主题
            $scope.spec.REVIEW_THEME = reviewData.reviewTheme;
            //初始化问题分类下拉框
            $scope.problemTypeArray = problemTypeConfig[problemProcessConfig[reviewData.reviewTheme]];
            // 问题列表
            reviewData.problems.forEach(function (item) {
                item.reviewAccept = '0';
                item.deadLine = item.deadLine.replace(/\./g,'-');
                $scope.problemList.push(nameCheck(item));
            });
            // 参加人员
            $scope.spec.PARTICIPANTS = null === reviewData.notJudges ? "" : reviewData.notJudges.toString();
            // 会议和邮件格式不同导致数据需要拆分赋值
            if (reviewData.mode === "会议") {
                // 评委
                for (var i = 0; i < reviewData.judges.length; i++) {
                    addNewBind();
                    $scope.jList[i].judge = reviewData.judges[i];
                    nameCheck($scope.jList[i]);
                    setJudgeGrade($scope.jList[i]);
                }
                // 会议持续时间
                $scope.spec.REVIEW_MEETING_TIME = getTime(reviewData.meetingTime);
                // 评审材料页数/条数
                $scope.spec.DOC_PAGES = reviewData.docPages;
                // 会议持续时间改变也要改变评委投入工作量
                duringChange();
            } else {
                // 评审材料页数/条数
                $scope.spec.DOC_PAGES = reviewData.docPages;
                // 项目名称
                var project = $scope.projectList.find(function (item) {
                    return item.cname === reviewData.projectName;
                });
                if (project) {
                    $scope.spec.TEM_NAME = project.id;
                    // 项目相关数据
                    projectChange($scope.spec.TEM_NAME);
                }

                // 添加评委
                for (var j = 0; j < reviewData.judgesAndWorkLoad.length; j++) {
                    addNewBind();
                    $scope.jList[j].judge = reviewData.judgesAndWorkLoad[j].judge;
                    $scope.jList[j].judgeWorkLoad = reviewData.judgesAndWorkLoad[j].workLoad;
                    nameCheck($scope.jList[j]);
                    setJudgeGrade($scope.jList[j]);
                }
                // 评审工作量
                $scope.addCalWorkLoad();
            }
        }
        //当新增数据时选择日期，自动将季度回填
        function dateToQuarter() {
            if (typeof $scope.spec.PLAN_COMPLETION_DATE === 'undefined' || '' === $scope.spec.PLAN_COMPLETION_DATE || null == $scope.spec.PLAN_COMPLETION_DATE) {
                $scope.spec.QUARTER = '';
                return;
            }

            var month = new Date($scope.spec.PLAN_COMPLETION_DATE).getMonth() + 1;
            $scope.spec.QUARTER = inform.dateToQuarter(month);
        }

        /**
         * 页面初始化
         */
        function initPages() {
            //获取所有项目名称
            $scope.projectList = [];
            //获取最近关闭或者进行中的所有项目名称
            var urlData = {flag: 'doing'};
            comService.getProjectsNameByParams(urlData).then(function (data) {
                $scope.project = angular.fromJson(data.data);
                angular.forEach($scope.project, function (res) {
                    $scope.projectList.push(res);
                });
                //获取员工信息
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function (data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                        $scope.employeeMap = {};
                        for (var i = 0; i < $scope.employeeList.length; i++) {
                            $scope.employeeMap[$scope.employeeList[i].realName] = $scope.employeeList[i].companyTitleLevel;
                        }
                    }
                    // 导入的文件赋值给页面
                    dataInitPages();
                });
            });
        }

        /**
         * 根据关键角色名称，获取其等级
         * @param item
         */
        function setJudgeGrade(item) {
            if (typeof item !== 'undefined') {
                item.judgeGrade = $scope.employeeMap[item.judge];
                $scope.addCalAvgGrade();
            }
            // 名字校验
            return nameCheck(item);
        }

        /**
         * 校验评委列表中是否存在重复
         * 规则：
         * 1.重复则提示"评委×××,×××重复,请修改"并返回false
         * 2.无重复则返回true
         * @returns {boolean}
         */
        function verifyJudgeList() {
            var verifyList = [];
            var duplicate = "";
            for (var i = 0; i < $scope.jList.length; i++) {
                if (verifyList.indexOf($scope.jList[i].judge) > -1 && duplicate.indexOf($scope.jList[i].judge) < 0) {
                    duplicate = duplicate.concat($scope.jList[i].judge).concat(",");
                }
                verifyList.push($scope.jList[i].judge);
            }
            //如果为空,说明无重复则返回true;
            if (!duplicate) {
                return true;
            }
            //不为空,则提示哪些评委重复,并返回false
            inform.common("评委" + duplicate.substring(0, duplicate.length - 1) + "存在重复,请修改");
            return false;
        }

        //添加信息
        $scope.addInfo = function () {
            if ($scope.taskFlag && $scope.taskInfo.proProjectName === undefined) {
                inform.common("联动创建禅道任务时请选择禅道项目。否则请取消联动创建禅道任务勾选。");
                return;
            }
            //校验jList中是否存在相同的评委
            if (!verifyJudgeList()) {
                return;
            }
            //jList中评委姓名key值修改
            $scope.jList.forEach(function (item) {
                item.judgeName = item.judge;
            });
            var urlData = {
                'excelName': 'app.office.report_0004',
                'ID': '',
                'QUARTER': $scope.spec.QUARTER,
                'REVIEW_LEVEL': $scope.spec.REVIEW_LEVEL,
                'PLAN_COMPLETION_DATE': inform.format($scope.spec.PLAN_COMPLETION_DATE, 'yyyy-MM-dd'),
                'PRODUCT_LINE': $scope.spec.productLine,
                'TEM_NAME': $scope.spec.TEM_NAME,
                'REVIEW_CONTENT': $scope.spec.REVIEW_CONTENT,
                'REVIEW_MEETING_TIME': $scope.spec.REVIEW_MEETING_TIME,
                'REVIEW_RESULT': $scope.spec.REVIEW_RESULT,
                'REVIEW_PROBLEM_NUMBER': '0',
                'LIABLE_PERSON': null == $scope.spec.LIABLE_PERSON ? null : $scope.spec.LIABLE_PERSON.join(','),
                'PROJECT_MANAGER': $scope.spec.PROJECT_MANAGER,
                'PARTICIPANTS': $scope.spec.PARTICIPANTS,
                'IS_TRACK_ONZENTAO': $scope.spec.IS_TRACK_ONZENTAO,
                'PROJECT_ADMINISTRATORS': $scope.spec.PROJECT_ADMINISTRATORS,
                'COMMENTS': $scope.spec.COMMENTS,
                'VERSION': $scope.spec.VERSION,
                'REVIEW_TYPE': $scope.spec.REVIEW_TYPE,
                'REVIEW_THEME': $scope.spec.REVIEW_THEME,
                'DOC_PAGES': $scope.spec.DOC_PAGES,
                'WORKLOAD': $scope.spec.WORKLOAD,
                'PREREVIEW': $scope.spec.PREREVIEW,
                'judgeList': JSON.stringify($scope.jList),
                'isReview': '0',
                'pre_review_id': null == $scope.spec.pre_review_id ? null : $scope.spec.pre_review_id.join(',')
            };
            reportService.verifyReviewExist(urlData).then(function (data) {
                if (data.code !== AgreeConstant.code) {
                    inform.common(data.message);
                    return;
                }
                if (data.data > 0) {
                    inform.common("该评审已存在,请勿重复添加");
                    return;
                }
                addReviewInfo(urlData);
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        };

        /**
         * 选择项目时，回填产品线项目经理、项目助理信息
         * 更改关联预评审下拉框的选项
         * @param projectId
         */
        function projectChange(projectId) {
            reportService.getProjectInfoById(projectId).then(function (data) {
                if (data.code !== AgreeConstant.code) {
                    inform.common(data.message);
                    return;
                }
                data.data = angular.fromJson(data.data);
                if (data.data.length === 0) {
                    inform.common("该项目不存在");
                } else {
                    $scope.spec.productLineName = data.data.productLineName;
                    $scope.spec.productLine = data.data.productLine;
                    $scope.spec.PROJECT_MANAGER = data.data.projectManager;
                    $scope.spec.PROJECT_ADMINISTRATORS = data.data.projectAssistant;
                    $scope.spec.project_name = data.data.projectName;
                    $scope.spec.REVIEW_LEVEL = "";
                    for(var i = 0;i < $scope.levelSelect.length;i++){
                        if($scope.levelSelect[i].label === data.data.reviewGrade){
                            $scope.spec.REVIEW_LEVEL = $scope.levelSelect[i].value;
                        }
                    }

                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });

            $scope.spec.pre_review_id = [];
            reportService.getPreRerviewReviewContentAndId(projectId).then(function (data) {
                if (data.data) {
                    $scope.preRerviewList = data.data;
                }
            });
        }

        function addReviewInfo(urlData) {
            reportService.addReportInfo(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    //问题列表添加reviewId 勾选了创建禅道任务
                    $scope.problemList.forEach(function (item) {
                        item.reviewId = data.data;
                        //不是疑难解释 并且是 采纳 的问题
                        if ($scope.taskFlag && item.level !== "疑问解释" && item.reviewAccept === '0') {
                            item.ztProjectId = $scope.taskInfo.proProjectId;
                            item.ztProjectName = $scope.taskInfo.proProjectName;
                            item.parentId = $scope.taskInfo.parentId;
                        }
                    });
                    //给问题设置评委信息并格式化时间
                    setJudgeInfoAndFormatDeadLine();
                    //批量插入问题
                    reviewProblemService.insertProblem($scope.problemList).then(function (itemData) {
                        if (itemData.code === AgreeConstant.code) {
                            inform.common("新增评审数据成功");
                            $state.go('app.office.report_0004',{flag:'notMenu'});
                        } else {
                            inform.common(itemData.message);
                        }
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
                } else {
                    inform.common("新增评审数据失败");
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        }

        /**
         * 新增一个评委，重新计算
         */
        function addNewBind() {
            //评委信息
            var judge = {
                'judge': '',
                'judgeGrade': '',
                'judgeWorkLoad': $scope.spec.REVIEW_MEETING_TIME || '',
                'judgeJoin': '0',
                'flag': true
            };
            $scope.jList.push(judge);
            $scope.addCalWorkLoad();
        }
        //取消一行
        $scope.deleteNewBind = function (index) {
            if (index >= 0) {
                $scope.jList.splice(index, 1);
            }
            $scope.addCalWorkLoad();
            //删除一个列表的时候重新计算评委等级
            $scope.addCalAvgGrade();
        };
        /**
         * 新增一条问题信息
         */
        $scope.addAProblem = function () {
            //问题信息
            var problem = {
                'reviewAccept': '0'
            };
            $scope.problemList.push(problem);
        };
        /**
         *  删除一条问题
         * @param index 列表下标
         */
        $scope.deleteProblem = function (index) {
            inform.modalInstance("确定要删除该问题吗? ").result.then(function () {
                if (index >= 0) {
                    $scope.problemList.splice(index, 1);
                }
            });
        };

        /**
         * 新增：持续时间变化时,说明是会议
         *
         * 各评委工作量 = 持续时间
         * 评审工作量 = 各评委工作量相加
         * 评审效率 = 问题 / 评审工作量
         */
        function duringChange() {
            //评委工作量赋值
            for (var i = 0; i < $scope.jList.length; i++) {
                //若评委不参加，直接置为0
                if ($scope.jList[i].judgeJoin === '1') {
                    //评委工作量是会议时长
                    $scope.jList[i].judgeWorkLoad = '0';
                } else {
                    //评委工作量是会议时长
                    $scope.jList[i].judgeWorkLoad = $scope.spec.REVIEW_MEETING_TIME;
                }
            }
            //工作量变化了,变成评委工作量相加,效率随之更新
            $scope.addCalWorkLoad();
        }

        /**
         * 当新增时评委等级输入框光标移出：计算评委平均等级和评委人数
         */
        $scope.addCalAvgGrade = function () {
            var total = 0;
            var num = 0;
            for (var i = 0; i < $scope.jList.length; i++) {
                //计算评委平均等级
                if (isFinite($scope.jList[i].judgeGrade) && $scope.jList[i].judgeJoin === '0') {
                    total = total + parseInt($scope.jList[i].judgeGrade);
                }
                //评委人数
                if ($scope.jList[i].judgeJoin === '0') {
                    num++;
                }
            }
            $scope.spec.jListNum = num;
            $scope.spec.avgGrade = transToNum(total / $scope.spec.jListNum, 1);
        };

        /**
         * 当新增评审类型变化为邮件时
         */
        $scope.reviewTypeAdd = function () {
            if ($scope.spec.REVIEW_TYPE === '0') {
                //类型为邮件类型,将会议持续时间置为0
                $scope.spec.REVIEW_MEETING_TIME = '';
            }
            $scope.duringChange();
        };

        /**
         * 当会议结果变化为未评审时
         */
        $scope.reviewResultChange = function (item) {
            //若选项不为“未评审”，直接返回
            if (item !== '3') {
                $scope.planCompletionDatFlag = false;
                return;
            }

            $scope.planCompletionDatFlag = true;
            $scope.spec.REVIEW_MEETING_TIME = '0';
            $scope.spec.REVIEW_PROBLEM_NUMBER = '0';
            $scope.spec.DOC_PAGES = '0';
            $scope.spec.REVIEW_TYPE = '1';
            $scope.duringChange();
        };

        /**
         * 当评委投入工作量修改时触发重新计算评审工作量,并从新计算效率
         *
         */
        $scope.addCalWorkLoad = function addCalWorkLoad() {
            var total = 0;
            for (var i = 0; i < $scope.jList.length; i++) {
                var workLoad = parseFloat($scope.jList[i].judgeWorkLoad);
                if (isFinite(workLoad) && $scope.jList[i].judgeJoin === '0') {
                    //工作量等于评委工作量的加合值
                    total = total + workLoad;
                }
            }

            //判断加合结果是否合法,不合法归0,合法保留一位小数
            $scope.spec.WORKLOAD = transToNum(total, 1);
        };

        /**
         *  修改评审时间
         */
        $scope.updateDateOne = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.updateTimeOne = true;
        };

        /**
         * 新增评审时间
         */
        $scope.insertDateOne = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.insertTimeOne = true;
        };

        /**
         * 转化NaN和Infinity为0.0
         *
         * 判断入参str是否合法,不合法归0,合法保留num位小数
         * isNaN方法是判断是不是一个非数字值，若是，则返回true,否则返回false
         * isFinite方法是判断是不是无穷数，参数不是NaN,infinity,-infinity时返回true
         */
        function transToNum(str, num) {
            if (!isFinite(str)) {
                return 0.0;
            } else {
                return str.toFixed(num);
            }
        }

        /**
         * 评委是否参与选项改变后触发的事件
         */
        $scope.judgeJoinChange = function (item) {
            //状态为参加
            if (item.judgeJoin === '1') {
                item.judgeWorkLoad = '0';
            } else {
                //若为会议类型，则为工作量复制，否则清空
                if ($scope.spec.REVIEW_TYPE !== '0') {
                    item.judgeWorkLoad = $scope.spec.REVIEW_MEETING_TIME;
                } else {
                    item.judgeWorkLoad = '';
                }
            }
            //计算平均等级
            $scope.addCalAvgGrade();
            //计算平均工作量
            $scope.addCalWorkLoad();
        };
        /**
         * 上传前,给每一个problem设置提出人（评委）、评委等级,
         * 如果是邮件评审,设置评委投入工作量
         * 会议或默认则不设置
         * 对时间要求格式化
         */
        function setJudgeInfoAndFormatDeadLine() {
            for (var i = 0; i < $scope.problemList.length; i++) {
                if ($scope.problemList[i].deadLine) {
                    //对完成时间要求进行格式化
                    $scope.problemList[i].deadLine = inform.format($scope.problemList[i].deadLine, 'yyyy/MM/dd');
                }
            }
        }
        /*
        * 计算时间差
        * */
        function getTime(time) {
            var index = time.indexOf('-');
            var start = time.substring(0, index);
            var end = time.substring(index + 1);
            var startIndex = start.indexOf(':');
            var startHour = start.substring(0, startIndex);
            var startMin = start.substring(startIndex + 1);
            var endIndex = end.indexOf(':');
            var endHour = end.substring(0, endIndex);
            var endMin = end.substring(endIndex + 1);
            var hour = parseInt(endHour, 10) - parseInt(startHour, 10);
            var min = parseInt(endMin, 10) - parseInt(startMin, 10);
            if(isNaN(hour) || isNaN(min)){
                return "0";
            }else{
                return ((hour * 60 + min) / 60).toFixed(1);
            }
        }
        /*
        * 姓名错误和重名校验
        * */
        function nameCheck(item) {
            // 判断是否有错误名字和重复名字
        	var judges = $scope.employeeList.filter(function (element) {
                return element.realName === item.judge ||
                		(element.realName.indexOf(item.judge) > -1
                		&& !isNaN(parseInt(element.realName.substring(item.judge.length)))
                		);
            });

        	if (!judges || judges.length === 0) {
                item.judgeErrIsShow = true;
                item.judgeErrText = "此姓名错误";

            } else if (judges.length > 1) {
                item.judgeErrIsShow = true;
                item.judgeErrText = "此姓名重复";
            } else {
            	item.judge = judges[0].realName;
            	setJudgeNo(item);
                item.judgeErrIsShow = false;
                item.judgeErrText = '';
            }

            return item;
        }
            /**
             * 初始化产品线与禅道项目弹框
             */
            $scope.initModule = function (){
                var data={
                    'keyWord': $scope.spec.TEM_NAME == null ? $scope.taskInfo.officeProjectName : $scope.spec.project_name
                };
                proProjectModule.initModule(data,$scope,setProProjectInfo);
            }
            /**
             * 根据所选中的禅道项目回填信息
             */
            function setProProjectInfo(data){
                $scope.taskInfo.proProjectName = data.name;
                $scope.taskInfo.proProjectId = data.id;
                $scope.taskInfo.parentId = data.parentId;
            }
        /**
         * *************************************************************
         *              方法声明部分                                 结束
         * *************************************************************
         */
    }]);
})();

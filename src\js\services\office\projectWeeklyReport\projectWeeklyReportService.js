//# sourceURL=js/services/office/projectWeeklyReport/projectWeeklyReportService.js
/*
* @Author: fubaole
* @Date:   2017-09-05 10:05:00
* @Last Modified by:   fubaole
* @Last Modified time: 2017-09-20 15:34:22
*/
(function() {
    'use strict';
  app.factory('projectWeeklyReportService', projectWeeklyReportService);

  projectWeeklyReportService.$inject=['HttpService','$rootScope'];

  function projectWeeklyReportService(HttpService,$rootScope){
    var service={
    	selectData:selectData,
    	updateInfo:updateInfo,
    	addInfo:addInfo,
    	delData:delData,
    	selLastWeekProgress:selLastWeekProgress,
    	getPerson:getPerson
    };

    return service;
    /**
     * 获取所有周报的信息
     */
    function selectData(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'projectWeeklyReport/selectData', urlData);
    }
    /**
     * 修改周报信息
     */
    function updateInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'projectWeeklyReport/updateInfo', urlData);
    }
    /**
     * 新增周报信息
     */
    function addInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'projectWeeklyReport/addInfo', urlData);
    }
    /**
     * 删除周报信息
     */
    function delData(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'projectWeeklyReport/delData', urlData);
    }
    /**
     * 获取上周进展信息
     */
    function selLastWeekProgress(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'projectWeeklyReport/selLastWeekProgress', urlData);
    }
    /**
     * 获取默认显示人数
     */
    function getPerson(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'projectWeeklyReport/getPerson', urlData);
    }
  }
})();
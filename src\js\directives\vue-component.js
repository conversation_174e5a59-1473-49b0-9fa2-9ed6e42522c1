/**
 * AngularJS 指令：用于在 AngularJS 中集成 Vue3 组件
 * 使用方式：<div vue-component="AttendanceDetail" vue-props="vm.vueProps"></div>
 */
(function() {
    'use strict';

    angular.module('app').directive('vueComponent', ['$timeout', function($timeout) {
        return {
            restrict: 'A',
            scope: {
                componentName: '@vueComponent',
                vueProps: '=?',
                onVueEvent: '&?'
            },
            link: function(scope, element, attrs) {
                var vueApp = null;
                var elementId = 'vue-component-' + Math.random().toString(36).substr(2, 9);
                
                // 设置元素ID
                element.attr('id', elementId);

                function mountVueComponent() {
                    // 确保 Vue 组件库已加载
                    if (typeof window.VueComponents === 'undefined') {
                        console.error('VueComponents not loaded. Please ensure vue-components.umd.js is included.');
                        return;
                    }

                    // 卸载之前的组件
                    if (vueApp) {
                        try {
                            window.VueComponents.unmount(vueApp);
                        } catch (e) {
                            console.warn('Error unmounting Vue component:', e);
                        }
                    }

                    // 准备传递给 Vue 组件的 props
                    var props = scope.vueProps || {};
                    
                    // 添加事件回调
                    if (scope.onVueEvent) {
                        props.onEvent = function(eventName, data) {
                            scope.$apply(function() {
                                scope.onVueEvent({
                                    eventName: eventName,
                                    data: data
                                });
                            });
                        };
                    }

                    // 挂载 Vue 组件
                    $timeout(function() {
                        try {
                            vueApp = window.VueComponents.mount(elementId, scope.componentName, props);
                            if (!vueApp) {
                                console.error('Failed to mount Vue component:', scope.componentName);
                            }
                        } catch (error) {
                            console.error('Error mounting Vue component:', error);
                        }
                    }, 0);
                }

                // 监听 props 变化
                scope.$watch('vueProps', function(newProps, oldProps) {
                    if (newProps !== oldProps && vueApp) {
                        // 重新挂载组件以更新 props
                        mountVueComponent();
                    }
                }, true);

                // 初始化挂载
                mountVueComponent();

                // 清理函数
                scope.$on('$destroy', function() {
                    if (vueApp) {
                        try {
                            window.VueComponents.unmount(vueApp);
                        } catch (e) {
                            console.warn('Error unmounting Vue component on destroy:', e);
                        }
                    }
                });
            }
        };
    }]);

    /**
     * 服务：提供 Vue 组件相关的工具方法
     */
    angular.module('app').service('VueComponentService', function() {
        var self = this;

        /**
         * 检查 Vue 组件库是否已加载
         */
        self.isVueComponentsLoaded = function() {
            return typeof window.VueComponents !== 'undefined';
        };

        /**
         * 等待 Vue 组件库加载完成
         */
        self.waitForVueComponents = function(callback, timeout) {
            timeout = timeout || 5000;
            var startTime = Date.now();
            
            function check() {
                if (self.isVueComponentsLoaded()) {
                    callback(null);
                } else if (Date.now() - startTime > timeout) {
                    callback(new Error('Timeout waiting for VueComponents to load'));
                } else {
                    setTimeout(check, 100);
                }
            }
            
            check();
        };

        /**
         * 手动挂载 Vue 组件
         */
        self.mountComponent = function(elementId, componentName, props) {
            if (!self.isVueComponentsLoaded()) {
                throw new Error('VueComponents not loaded');
            }
            return window.VueComponents.mount(elementId, componentName, props);
        };

        /**
         * 手动卸载 Vue 组件
         */
        self.unmountComponent = function(app) {
            if (!self.isVueComponentsLoaded()) {
                throw new Error('VueComponents not loaded');
            }
            return window.VueComponents.unmount(app);
        };
    });

})();

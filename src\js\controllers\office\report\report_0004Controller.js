//# sourceURL=js/controllers/office/report/report_0004Controller.js
(function () {
  app.controller('report_0004Controller', [
    'comService',
    '$http',
    'LocalCache',
    'codeConfigService',
    '$rootScope',
    '$state',
    '$stateParams',
    '$scope',
    '$modal',
    'reportService',
    'inform',
    'Trans',
    'AgreeConstant',
    function (
      comService,
      $http,
      LocalCache,
      codeConfigService,
      $rootScope,
      $state,
      $stateParams,
      $scope,
      $modal,
      reportService,
      inform,
      Trans,
      AgreeConstant
    ) {
      /**
       * *************************************************************
       *             初始化部分                                 开始
       * *************************************************************
       */
      //校验变量
      $scope.limitList = AgreeConstant.limitList;
      //页面分页信息
      $scope.pages = {
        pageNum: '', //分页页数
        size: '', //分页每页大小
        total: '', //数据总数
      };
      //绑定文件控件改变事件
      $('#filesImg').change(submitForm);
      $scope.passNum = 0;
      $scope.conditionalPassNum = 0;
      $scope.noPassNum = 0;
      $scope.noReview = 0;
      $scope.passRate = 0;
      $scope.totalNum = 0;
      $scope.timeFlag = true;
      $scope.resultFlag = false;
      $scope.orderby = "DATE_FORMAT(PLAN_COMPLETION_DATE, '%Y-%m-%d') desc";
      //季度展示Map
      $scope.quarterMap = {
        0: '第1季度',
        1: '第2季度',
        2: '第3季度',
        3: '第4季度',
      };

      //评审级别展示Map
      $scope.levelMap = {
        0: '一级',
        1: '二级',
        2: '三级',
      };

      //评审结果展示Map
      $scope.resMap = {
        0: '有条件通过',
        1: '通过',
        2: '不通过',
        3: '未评审',
      };
      $scope.resList = [
        { param_code: '0', param_value: '有条件通过' },
        { param_code: '1', param_value: '通过' },
        { param_code: '2', param_value: '不通过' },
        { param_code: '3', param_value: '未评审' },
      ];
      //是否跟踪展示Map
      $scope.trackMap = {
        0: '是',
        1: '否',
      };
      //评审方式展示Map
      $scope.reviewTypeMap = {
        0: '邮件',
        1: '会议',
      };
      //初始化页面信息
      initPages();
      //设置列表的高度
      setDivHeight();
      //窗体大小变化时重新计算高度
      $(window).resize(setDivHeight);

      /**
       * *************************************************************
       *              初始化部分                                 结束
       * *************************************************************
       */

      /**
       * *************************************************************
       *              方法声明部分                                 开始
       * *************************************************************
       */

      /*
       * 点击上传按钮
       * */
      $scope.selectFile = function () {
        document.getElementById('filesImg').click();
      };
      /**
       * 上传文件
       */
      function submitForm() {
        //表单id  初始化表单值
        var formData = new FormData();
        //获取文档中有类型为file的第一个input元素
        var file = document.querySelector('input[type=file]').files[0];
        if (!file) {
          inform.common('请先选择文件!');
          return false;
        }
        if (file.size > AgreeConstant.fileSize) {
          inform.common('上传文件大小不能超过2M');
          fileChangeReset();
          return false;
        }
        formData.append('file', file);
        var a = file.type;
        if (a !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          inform.common('请选择.xlsx类型的文档进行上传!');
          return false;
        } else {
          var modalInstance = $modal.open({
            templateUrl: 'myModalContent.html',
            controller: 'ModalInstanceCtrl',
            size: 'sm',
            resolve: {
              items: function items() {
                return '确定要上传文件吗！';
              },
            },
          });
          var uploadUrl = '';
          uploadUrl = $rootScope.getWaySystemApi + 'report/uploadExcel';
          modalInstance.result.then(function () {
            //开启遮罩层
            inform.showLayer('上传中。。。。。。');
            $.ajax({
              url: uploadUrl,
              type: 'POST',
              data: formData,
              processData: false,
              contentType: false,
              beforeSend: function beforeSend(request) {
                request.setRequestHeader('Authorization', 'Bearer ' + LocalCache.getSession('token') || '');
              },
              success: function success(result) {
                if (result.code === AgreeConstant.code) {
                  // 关闭遮罩层
                  inform.closeLayer();
                  // 数据存入缓存
                  LocalCache.setObject('reportUploadExcelData', result.data);
                  // 跳转到上传文件的修改页面
                  $state.go('app.office.reportUpload');
                } else {
                  inform.closeLayer();
                  $modal.open({
                    templateUrl: 'tpl/common/errorModel.html',
                    controller: 'ModalInstanceCtrl',
                    size: 'lg',
                    resolve: {
                      items: function () {
                        return result.message;
                      },
                    },
                  });
                }
                //移除文件名称
                fileChangeReset();
              },
              error: function error(_error) {
                inform.common(Trans('tip.requestError'));
              },
            });
          });
        }
      }
      /**
       * 选择上传文件后事件
       */
      function fileChangeReset() {
        //通过表单元素的reset方法实现选择文件的重置
        $('#uploadForm')[0].reset();
      }
      //设置列表的高度
      function setDivHeight() {
        //网页可见区域高度
        var clientHeight = document.body.clientHeight;
        var divHeight = clientHeight - (150 + 210);
        $('#divTBDis').height(divHeight);
        $('#subDivTBDis').height(divHeight - 50);
        $('#subDivTBDis3').height(divHeight - 30);
      }

      /**
       * 页面初始化
       */
      function initPages() {
        //获取产品线
        $scope.lineMap = [];
        comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
          $scope.lines = angular.fromJson(data.data);
          angular.forEach($scope.lines, function (res, index) {
            $scope.lineMap[res.param_code] = res.param_value;
          });
        });
        //获取项目信息
        $scope.projectIdList = [];
        codeConfigService.getProjectList().then(function (data) {
          $scope.projectIdList = data.data;
          $scope.projectMap = {};
          for (var index in $scope.projectIdList) {
            $scope.projectMap[$scope.projectIdList[index].id] = $scope.projectIdList[index].cname;
          }
        });
        //获取部门
        $scope.departmentList = [];
        comService.getOrgChildren('D010053').then(function (data) {
          $scope.departmentList = comService.getDepartment(data.data);
        });
        /**
         * 查询条件
         */
        var formRefer = {
          reportTypeCode: 'app.office.report_0004', //报表种类
          name: '', //模板/规范名称
          startTime: inform.format(new Date(), 'yyyy-MM-dd').split('-')[0] + '-01' + '-01', //评审时间开始时间
          endTime: '', //评审时间结束时间
          productLine: '', //产品线
          projectAdministators: '', //项目管理员
          department: '', //部门
          liablePerson: '', //责任人
          reviewTheme: '', //评审主题
          reviewContent: '', //评审内容
        };
        // 初始化查询数据
        $scope.formRefer = {};
        $scope.formRefer.startTime = inform.format(new Date(), 'yyyy-MM-dd').split('-')[0] + '-01' + '-01'; //评审时间开始时间
        if ($stateParams.flag !== 'menu') {
          $scope.formRefer = LocalCache.getObject('formRefer_rp_0004') || formRefer;
        }

        $scope.formRefer.reportTypeCode = 'app.office.report_0004'; //报表种类

        /**
         * 查询信息结果
         */
        $scope.reportInfos = [];
        /**
         * 分页
         */
        $scope.pages = inform.initPages(); // 初始化分页数据
        $scope.getData = getData; // 分页相关函数
        getData($scope.pages.pageNum); // 分页显示报表信息
      }
      $scope.resetData = function () {
        $scope.formRefer = {
          reportTypeCode: 'app.office.report_0004', //报表种类
          name: '', //模板/规范名称
          startTime: inform.format(new Date(), 'yyyy-MM-dd').split('-')[0] + '-01' + '-01', //评审时间开始时间
          endTime: '', //评审时间结束时间
          productLine: '', //产品线
          projectAdministators: '', //项目管理员
          department: '', //部门
          liablePerson: '', //责任人
          reviewTheme: '', //评审主题
          reviewContent: '', //评审内容
        };
      };
      /**
       * 根据时间条件排序
       * pageNum 排序条件
       */
      $scope.orderByTime = function () {
        $scope.timeFlag = !$scope.timeFlag;
        $scope.orderby;
        //判断排序规则
        if ($scope.timeFlag) {
          //时间字符串转时间格式
          $scope.orderby = "DATE_FORMAT(PLAN_COMPLETION_DATE, '%Y-%m-%d') desc";
        } else {
          $scope.orderby = "DATE_FORMAT(PLAN_COMPLETION_DATE, '%Y-%m-%d') asc";
        }
        getData('1');
      };
      /**
       * 根据结果条件排序
       * pageNum 排序条件
       */
      $scope.orderByResult = function () {
        $scope.resultFlag = !$scope.resultFlag;
        $scope.orderby;
        //判断排序规则
        if ($scope.resultFlag) {
          $scope.orderby = 'REVIEW_RESULT desc';
        } else {
          $scope.orderby = 'REVIEW_RESULT asc';
        }
        getData('1');
      };

      /**
       * 根据分页显示仓库
       * pageNum 当前页数
       */
      function getData(pageNum) {
        LocalCache.setObject('formRefer_rp_0004', $scope.formRefer);
        //清空报表信息列表
        $scope.reportInfos = [];
        if ($scope.formRefer.type) {
          pageNum = 1;
        }
        //存查询条件
        var urlData = {
          excelName: $scope.formRefer.reportTypeCode, //表名
          name: $scope.formRefer.name, //规范名
          productLine: $scope.formRefer.productLine, //产品线
          department: $scope.formRefer.department, //部门
          liablePerson: $scope.formRefer.liablePerson, //责任人
          projectAdministators: $scope.formRefer.projectAdministators, //项目管理员
          startTime: inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'), //开始时间
          endTime: inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间
          reviewTheme: $scope.formRefer.reviewTheme, //评审主题
          reviewContent: $scope.formRefer.reviewContent, //评审内容
          currentPage: pageNum,
          pageSize: $scope.pages.size,
          orderByParam: $scope.orderby,
          reviewResult: $scope.formRefer.result,
        };

        reportService.getReportInfo(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              data.data = angular.fromJson(data.data);
              if (data.data.list.length === 0) {
                inform.common('无符合条件的同行评审信息');
              }
              $scope.passNum = 0;
              $scope.conditionalPassNum = 0;
              $scope.noPassNum = 0;
              $scope.noReview = 0;
              $scope.passRate = 0;
              $scope.totalNum = 0;
              //查询同行评审结果汇总数据
              reportService.getReportResultGather(urlData).then(
                function (resultGatherData) {
                  if (resultGatherData.code === AgreeConstant.code) {
                    resultGatherData.data = angular.fromJson(resultGatherData.data);
                    $scope.passNum = resultGatherData.data.passNum * 1;
                    $scope.conditionalPassNum = resultGatherData.data.conditionalPassNum * 1;
                    $scope.noPassNum = resultGatherData.data.noPassNum * 1;
                    $scope.noReview = resultGatherData.data.noReview * 1;
                    $scope.totalNum = $scope.passNum + $scope.conditionalPassNum + $scope.noPassNum + $scope.noReview;
                    //评审通过率
                    if ($scope.totalNum !== 0) {
                      $scope.passRate = (
                        (($scope.passNum + $scope.conditionalPassNum) / $scope.totalNum) *
                        100
                      ).toFixed(2);
                    }
                  }
                },
                function (error) {
                  inform.common(Trans('tip.requestError'));
                }
              );
              angular.forEach(data.data.list, function (res, index) {
                //计算评委平均等级
                if (!res.avgGrade) {
                  res.avgGrade = 0;
                }

                //计算评审缺陷密度=问题数/评审材料文档页数
                if (!res.DOC_PAGES || res.DOC_PAGES + '' === '0' || !res.REVIEW_PROBLEM_NUMBER) {
                  res.QUESTION_DENSITY = 0;
                } else {
                  res.QUESTION_DENSITY = (parseFloat(res.REVIEW_PROBLEM_NUMBER) / parseFloat(res.DOC_PAGES)).toFixed(2);
                }

                //计算评审效率
                if (!res.WORKLOAD || res.WORKLOAD === '0.0' || !res.REVIEW_PROBLEM_NUMBER) {
                  res.REVIEW_EFFICIENCY = '0.0';
                } else {
                  res.REVIEW_EFFICIENCY = (parseFloat(res.REVIEW_PROBLEM_NUMBER) / parseFloat(res.WORKLOAD)).toFixed(1);
                }
                $scope.reportInfos.push(res);
              });
              // 分页信息设置
              $scope.pages.total = data.data.total; // 页面数据总数
              $scope.pages.star = data.data.startRow; // 页面起始数
              $scope.pages.end = data.data.endRow; // 页面结束数
              $scope.pages.pageNum = data.data.pageNum; //页号
            } else {
              inform.common(data.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }

      // 删除弹框
      $scope.open = function (m) {
        var modalInstance = $modal.open({
          templateUrl: 'myModalContent.html',
          controller: 'ModalInstanceCtrl',
          size: 'sm',
          resolve: {
            items: function items() {
              return Trans('common.deleteTip');
            },
          },
        });
        modalInstance.result.then(function () {
          if (m) {
            $scope.delete(m);
          }
        });
      };

      //删除信息
      $scope.delete = function (m) {
        var urlData = {
          id: m.ID,
        };
        reportService.deleteById(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              layer.confirm(
                data.message,
                {
                  title: false,
                  btn: ['确定'],
                },
                function (result) {
                  layer.close(result);
                  $scope.getData(1);
                }
              );
            } else {
              inform.common(data.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      };

      /**
       * 页面选中的修改信息复制
       */
      $scope.popModal = function (m) {
        if (null != m.LIABLE_PERSON) {
          m.LIABLE_PERSON = m.LIABLE_PERSON.split(',');
        }
        $scope.changeParam = angular.copy(m);
        var url = 'app.office.report_0004_update';
        //若为预评审，则跳到预评审的修改页面
        if (m.is_review === '1') {
          url = 'app.office.report_0004_inAdvance_update';
        }
        $state.go(url, {
          jsonResult: JSON.stringify($scope.changeParam),
        });
      };

      /**
       * 开始时间
       */
      $scope.openDateStart = function ($event) {
        $event.preventDefault();
        $event.stopPropagation();
        $scope.openedStart = true; //开始时间
        $scope.openedEnd = false;
      };

      /**
       *
       *  结束时间
       */
      $scope.openDateEnd = function ($event) {
        $event.preventDefault();
        $event.stopPropagation();
        $scope.openedStart = false;
        $scope.openedEnd = true; //结束时间
      };

      /**
       * 跳转至评审问题列表页
       * @param m 评审相关信息
       */
      $scope.viewProblem = function (m) {
        var url = 'app.office.reviewProblem';
        //若为预评审，则跳到预评审的修改页面
        if (m.is_review === '1') {
          url = 'app.office.inAdvanceReviewProblem';
        }
        $state.go(url, {
          id: m.ID,
          type: m.REVIEW_TYPE,
          reviewContent: m.REVIEW_CONTENT,
          reviewTheme: m.REVIEW_THEME,
          projectName: $scope.projectMap[m.TEM_NAME],
        });
      };

      /**
       * 下载模板
       */
      $scope.toExcelReview = function () {
        inform.toTemplateExcel('会议纪要模板', 'report_59');
      };
      $scope.toExcelEmailProblem = function () {
        inform.toTemplateExcel('邮件评审问题模板', 'report_60');
      };
      /**
       * *************************************************************
       *              方法声明部分                                 结束
       * *************************************************************
       */
    },
  ]);
})();

/*
 * @Author: jichundong
 * @Date:   2024-09-29 16:12:05
 * @Last Modified by:   jichundong
 * @Last Modified time:  2024-09-29 16:12:05
 */
(function () {
    'use strict';
    app.factory('redEventService', redEventService);
    redEventService.$inject = ["HttpService", '$rootScope'];

    function redEventService(HttpService, $rootScope) {
        var service = {
            getRedEventList: getRedEventList
        };
        return service;

        /**
         * 分页查询红事件列表
         */
        function getRedEventList(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'redEvent/getRedEventList', urlData);
        }
    }
})();
(function() {
	'use strict';
	app.factory('personalQaDataService', personalQaDataService);
	personalQaDataService.$inject = [ "HttpService", '$rootScope' ];

	function personalQaDataService(HttpService, $rootScope) {
		var service = {
            getBugDataStatistics:getBugDataStatistics,
            getCaseDataStatistics:getCaseDataStatistics,
            getTestTaskDataStatistics:getTestTaskDataStatistics,
            getBugDataOfSeverity:getBugDataOfSeverity,
            getBugDataOfType:getBugDataOfType,
            getCaseDataOfPriority:getCaseDataOfPriority,
            getCaseDataOfType:getCaseDataOfType,
            getTestTaskDataOfStatus:getTestTaskDataOfStatus,
            getLaunchOfProject:getLaunchOfProject,
            getReviewOfType:getReviewOfType
            
		};
		return service;

        function getBugDataOfSeverity(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'qaDataBoard/getBugInfoOfSeverity',urlData);
        }
        function getBugDataOfType(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'qaDataBoard/getBugInfoOfType',urlData);
        }
        
        function getCaseDataOfPriority(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'qaDataBoard/getCaseInfoOfPriority',urlData);
        }

        function getCaseDataOfType(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'qaDataBoard/getCaseInfoOfType',urlData);
        }
        
        function getTestTaskDataOfStatus(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'qaDataBoard/getTestTaskOfStatus',urlData);
        }
        
        function getLaunchOfProject(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'qaDataBoard/getLaunchInfoOfProject',urlData);
        }

        function getReviewOfType(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'qaDataBoard/getReviewInfoOfType',urlData);
        }

        //获取bug数据汇总
        function getBugDataStatistics(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'qaDataBoard/getQaDataStatisticsOfBug', urlData);
        }

        //获取用例数据汇总
        function getCaseDataStatistics(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'qaDataBoard/getQaDataStatisticsOfCase', urlData);
        }
        //获取bug数据汇总
        function getTestTaskDataStatistics(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'qaDataBoard/getQaDataStatisticsOfTestTask', urlData);
        }
       
	}
})();
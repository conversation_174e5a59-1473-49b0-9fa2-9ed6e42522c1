// 绩效记录管理
(function () {
    app.controller('performanceLogManagement', [
        'comService',
        'performanceLogManagementService',
        '$scope',
        '$state',
        '$modal',
        'inform',
        '$stateParams',
        'AgreeConstant',
        'LocalCache',
        '$http',
        '$rootScope',
        function (
            comService,
            performanceLogManagementService,
            $scope,
            $state,
            $modal,
            inform,
            $stateParams,
            AgreeConstant,
            LocalCache,
            $http,
            $rootScope
        ) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //获取当前的登录域账号
            $scope.currentEmployeename = LocalCache.getSession('currentUserName');
            // 展示强制dataTable刷新
            $scope.showTable = false;

            //查询条件读取缓存
            if (JSON.stringify(LocalCache.getObject('performanceLogManagement')) !== '{}') {
                $scope.formRefer = LocalCache.getObject('performanceLogManagement');
            } else {
                //初始化查询条件
                $scope.formRefer = {
                    //二级部门名称
                    department: '',
                    employeeId: $scope.currentEmployeename,
                    assessmentCycle: '',
                    employeeName: '',
                    departmentId: '',
                    teamIds: [],
                    areaId: '',
                    recordType: '',
                    communicateStartDate: '',
                    communicateEndDate: '',
                    communicater: '',
                };
                // 设置沟通日期默认值
                const now = new Date();
                const curYear = now.getFullYear();
                const curMonth = now.getMonth();
                const curDay = now.getDate();
                const lastYear = new Date(curYear - 1, curMonth, curDay);
                $scope.formRefer.communicateStartDate = inform.format(lastYear, 'yyyy-MM-dd');
                $scope.formRefer.communicateEndDate = inform.format(now, 'yyyy-MM-dd');
            }

            // 二级部门下拉框选项
            $scope.departmentList = [];
            // 小组下拉框选项
            $scope.groupList = [];
            // 所有的小组的集合
            let allGroupList = [];
            // 属地下拉款选项
            $scope.areaList = [];
            // （绩效沟通）记录类型对应的下拉选项
            $scope.logType = ['绩效沟通', '日常绩效辅导', '低绩效改进', '述职改进', '试用期考核'];

            $scope.pages = {
                pageNum: '', // 分页页数
                size: '', // 分页每页大小
                total: '', // 数据总数
            };
            $scope.pages = inform.initPages(); // 初始化分页数据
            $scope.getData = getData;
            //初始化按钮权限
            $scope.importBlackEvent = false;
            // 查询列表的表头集合
            $scope.performanceLogTableHeaderList = [
                '序号',
                '姓名',
                '二级部门',
                '记录类型',
                '考核周期',
                '沟通日期',
                '沟通人',
                '任务数',
                '任务完成率',
                '不足和改进点',
                '操作',
            ].map((i) => {
                return { title: i, isShow: true };
            });
            // JQuery dataTable对象
            let table = null;
            // 导入时选择的考核周期
            $scope.importPeriod = '';
            // 构造从2024年开始向后十年的数组，用于考核周期的下拉选项
            $scope.performancePeriodList = Array.from({ length: 20 }, (_, index) => {
                const isEven = index % 2 === 0;
                const year = 2024 + Math.floor(index / 2);
                if (isEven) {
                    return `${year}上半年`;
                }
                return `${year}全年`;
            });
            // 禁用查询权限
            $scope.queryDisabled = $stateParams?.type === 'view';
            // 是否从首页来的，是的话展示返回按钮
            $scope.isFromHomePage = !!$stateParams?.type;
            // 初始化列表
            $scope.performanceLogList = [];
            //初始化页面信息
            initPages();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            const resizeTable = () => {
                const property = Object.keys($('#performanceLogTable'));
                if (property.length > 0) {
                    $('#performanceLogTable').DataTable().destroy();
                    // 销毁之后丢到下一次脏检查里，draw方法等都没法实现保证列对齐
                    setTimeout(() => {
                        showDataTable();
                    });
                }
            };
            // 监听侧边栏点击状态，改变之后，保证表格列对齐
            $scope.$watch('app.settings.asideFolded', function () {
                resizeTable();
            });
            //重置
            $scope.reset = function () {
                $scope.formRefer.liablePersonName = '';
                // 员工姓名
                $scope.formRefer.employeeName = '';
                // 二级部门
                $scope.formRefer.departmentId = '';
                // 小组
                $scope.formRefer.teamIds = [];
                // 属地
                $scope.formRefer.areaId = '';
                // 记录类型
                $scope.formRefer.recordType = '';
                // 沟通日期
                const now = new Date();
                const curYear = now.getFullYear();
                const curMonth = now.getMonth();
                const curDay = now.getDate();
                const lastYear = new Date(curYear - 1, curMonth, curDay);
                $scope.formRefer.communicateStartDate = inform.format(lastYear, 'yyyy-MM-dd');
                $scope.formRefer.communicateEndDate = inform.format(now, 'yyyy-MM-dd');
                // 考核周期
                $scope.formRefer.assessmentCycle = '';
                // 查询数据
                getData();
            };
            /**
             * 初始化页面
             */
            async function initPages() {
                try {
                    // 查询数据
                    getData();
                    // 并行获取一级部门下的二级部门数据、二级部门下的小组数据、以及地区数据
                    const [departmentRes, groupListRes, areaRes] = await Promise.all([
                        comService.getOrgChildren('D010053'),
                        performanceLogManagementService.getAllGroupList({ departmentId: 'D010053' }),
                        comService.getParamList('AREA_TYPE', 'AREA_TYPE'),
                    ]);

                    // 处理获取到的数据
                    $scope.departmentList = departmentRes.data;
                    allGroupList = groupListRes.data;
                    $scope.areaList = areaRes.data;
                    $scope.handleDepartmentChange($scope.formRefer.departmentId);
                } catch (error) {
                    console.error(error, '获取部门数据失败');
                }
            }
            //获取列表数据
            async function getData() {
                $scope.showTable = false;
                try {
                    var urlData = {
                        // 员工姓名
                        employeeName: $scope.formRefer.employeeName,
                        // 二级部门
                        departmentId: $scope.formRefer.departmentId ? String($scope.formRefer.departmentId) : '',
                        // 小组
                        teamIds: $scope.formRefer.teamIds,
                        // 属地
                        areaId: $scope.formRefer.areaId,
                        // 记录类型
                        recordType: $scope.formRefer.recordType,
                        // 考核周期
                        assessmentCycle: $scope.formRefer.assessmentCycle,
                        // 沟通日期
                        communicateStartDate: $scope.formRefer.communicateStartDate,
                        communicateEndDate: $scope.formRefer.communicateEndDate,
                        //沟通人
                        communicater: $scope.formRefer.communicater,
                        currentPage: $scope.pages.pageNum, // 分页页数
                        pageSize: $scope.pages.size, // 分页每页大小
                    };
                    const res = await performanceLogManagementService.getPerformanceLog(urlData);
                    $scope.performanceLogList = res.data.list;
                    // 分页信息设置
                    $scope.pages.total = res.data.total; // 页面数据总数
                    $scope.pages.star = res.data.startRow; // 页面起始数
                    $scope.pages.end = res.data.endRow; // 页面结束数
                    $scope.pages.pageNum = res.data.pageNum; //页号
                    $scope.showTable = true;
                    $scope.$apply();
                    setTimeout(() => {
                        showDataTable();
                    });
                } catch (error) {
                    console.error(error, '查询绩效记录失败');
                }
            }
            // dataTable初始化
            function showDataTable() {
                table = $('#performanceLogTable').DataTable({
                    language: {
                        emptyTable: '暂无数据',
                    },
                    //可被重新初始化
                    retrieve: true,
                    //自适应高度
                    scrollY: 'calc(100vh - 350px)',
                    scrollX: true,
                    scrollCollapse: true,
                    //控制每页显示
                    paging: false,
                    //冻结列
                    fixedColumns: {
                        leftColumns: 0,
                        rightColumns: 0,
                    },
                    //search框显示
                    searching: false,
                    //排序箭头
                    ordering: false,
                    //底部统计数据
                    info: false,
                    // 自动调整列宽
                    autoWidth: true,
                });
                $scope.$apply();
            }
            // 点击列可选时的回调
            $scope.handleColSelect = (index) => {
                // 按钮取反
                $scope.performanceLogTableHeaderList[index].isShow =
                    !$scope.performanceLogTableHeaderList[index].isShow;
                // 找到表格中对应的列
                let column = table.column(index);
                column.visible($scope.performanceLogTableHeaderList[index].isShow);
            };
            // 新增
            $scope.add = (type) => {
                LocalCache.setObject('performanceLogManagement', $scope.formRefer);
                if (type === '绩效辅导记录') {
                    LocalCache.setObject('coachingLogUpdate', '');
                    $state.go('app.office.coachingLogUpdate', {
                        type: 'add',
                    });
                } else if (type === '绩效沟通记录') {
                    LocalCache.setObject('communicationLogUpdate', '');
                    $state.go('app.office.communicationLogUpdate', {
                        type: 'add',
                    });
                }
            };
            // 查看
            $scope.view = (item) => {
                LocalCache.setObject('performanceLogManagement', $scope.formRefer);

                // 根据不同的记录状态进行不同的跳转
                if (item.recordType === '绩效沟通') {
                    $state.go('app.office.communicationLogUpdate', {
                        type: 'view',
                        id: item.id,
                    });
                } else {
                    $state.go('app.office.coachingLogUpdate', {
                        type: 'view',
                        id: item.id,
                    });
                }
            };
            // 编辑
            $scope.edit = (item) => {
                LocalCache.setObject('performanceLogManagement', $scope.formRefer);

                if (item.recordType === '绩效沟通') {
                    $state.go('app.office.communicationLogUpdate', {
                        type: 'edit',
                        id: item.id,
                    });
                } else {
                    $state.go('app.office.coachingLogUpdate', {
                        type: 'edit',
                        id: item.id,
                    });
                }
            };
            // 删除
            $scope.delete = (id) => {
                LocalCache.setObject('performanceLogManagement', $scope.formRefer);

                //自定义弹出框
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: 'sm',
                    resolve: {
                        items: function () {
                            return '确认删除此记录吗？';
                        },
                    },
                });
                // 确认删除之后的处理
                modalInstance.result.then(function () {
                    performanceLogManagementService.deleteRecord(Number(id)).then(
                        function (data) {
                            inform.common(data.message);
                            getData();
                        },
                        function (error) {
                            inform.common(Trans('tip.requestError'));
                        }
                    );
                });
            };
            /**
             * 上传文件
             */
            const uploadFile = (assessmentYear, assessmentCycle) => {
                var formData = new FormData();
                var file = document.querySelector('input[type=file]').files[0]; //获取文档中有类型为file的第一个input元素
                if (!file) {
                    inform.common('请先选择文件!');
                    return false;
                } else if (file.size > AgreeConstant.fileSize) {
                    inform.common('上传文件大小不得超过2M，请分割后重新上传!');
                    $('#form')[0].reset();
                    $('#fileNameDis').text('');
                    return false;
                }
                formData.append('file', file);
                var a = file.type;
                if (a !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
                    inform.common('请选择.xlsx类型的文档进行上传!');
                    return false;
                }
                formData.append('assessmentYear', assessmentYear);
                formData.append('assessmentCycle', assessmentCycle);
                $('#performanceLogManagementImportModal').modal('hide');
                inform.uploadFile('performance-communicate/importPerformanceCommunicate', formData, function (result) {
                    // 关闭遮罩层
                    $modal.open({
                        templateUrl: 'errorModel.html',
                        controller: 'ModalInstanceCtrl',
                        size: 'lg',
                        resolve: {
                            items: function () {
                                return result.message;
                            },
                        },
                    });
                    $('#form')[0].reset();
                    $('#fileNameDis').text('');
                    $scope.getData();
                });
            };
            // 导入绩效沟通记录
            $scope.importData = async (importPeriod) => {
                if (!importPeriod) {
                    inform.common('请选择考核周期');
                    return;
                }
                let match = importPeriod.match(/^(\d+)(.*)/);
                if (!match) {
                    return;
                }
                let assessmentYear = match[1];
                let assessmentCycle = match[2];
                uploadFile(assessmentYear, assessmentCycle);
            };
            /**
             * 根据当前选择的部门返回小组列表
             * @param {String} seletedOrgCode 当前选择的部门
             */
            $scope.handleDepartmentChange = (seletedOrgCode) => {
                if (!seletedOrgCode) {
                    $scope.groupList = [];
                    return;
                }
                // 通过 orgCode 找到对应的 item
                const selectedItem = $scope.departmentList.find(function (item) {
                    return item.orgCode === seletedOrgCode;
                });
                $scope.groupList = allGroupList.filter((i) => i.departmentId === String(selectedItem.orgId));
            };
            /**
             * 导出文件
             * @param {String} url 地址
             * @param {Object} params 参数
             * @param {String} fileName 文件名称
             */
            function downLoadFile(url, params, fileName) {
                // 开启遮罩层
                inform.showLayer('下载中。。。。。。');
                $http
                    .post($rootScope.getWaySystemApi + url, params, {
                        headers: {
                            'Content-Type': 'application/json',
                            Authorization: 'Bearer ' + LocalCache.getSession('token') || '',
                        },
                        responseType: 'blob', // 设置响应类型为 blob
                    })
                    .success(function (data, status, headers, config) {
                        // 关闭遮罩层
                        inform.closeLayer();

                        // 检查 HTTP 状态码
                        if (status !== 200) {
                            inform.common('下载失败，请重试！');
                            return;
                        }
                        // 判断是否失败
                        if (data.type === 'application/json') {
                            var reader = new FileReader();
                            reader.readAsText(data, 'utf-8');
                            reader.onload = function (e) {
                                inform.common(JSON.parse(e.target.result).message);
                            };
                            return;
                        }

                        // 创建 Blob 对象
                        var blob = new Blob([data], { type: 'application/octet-stream' });

                        // 如果是 IE 浏览器
                        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                            window.navigator.msSaveOrOpenBlob(blob, fileName);
                        }
                        // 其他浏览器
                        else {
                            var objectUrl = URL.createObjectURL(blob);
                            var a = document.createElement('a');
                            a.href = objectUrl;
                            a.download = fileName;
                            a.style.display = 'none';
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                        }

                        // 提示下载成功
                        inform.common('下载成功!');
                    })
                    .error(function () {
                        // 关闭遮罩层
                        inform.closeLayer();
                        inform.common('下载失败，请重试！');
                    });
            }
            $scope.downLoadImportExample = () => {
                const urlData = {
                    excelName: '绩效沟通模板',
                    excelCode: 'Template_performance_communicate',
                };
                downLoadFile('common/toTemplateExcel', urlData, '绩效沟通模板.xlsx');
            };
            // 导出绩效记录
            $scope.exportPerformanceData = (type) => {
                const name = type === '绩效沟通' ? '绩效反馈沟通记录表' : '绩效辅导记录表';
                const urlData = $scope.formRefer;
                downLoadFile(
                    'performance-communicate/exportPerformanceCommunicate',
                    { ...urlData, recordType: type },
                    `${name}.xlsx`
                );
            };
            // 跳转回首页个人绩效页签
            $scope.goToHomePage = () => {
                $state.go('app.index_bench', {
                    tagType: '9',
                });
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *****************************************************
             */
        },
    ]);
})();

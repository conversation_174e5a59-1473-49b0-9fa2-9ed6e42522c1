(function () {
    app.controller('meetingAddController', [
        'comService',
        '$http',
        'LocalCache',
        '$rootScope',
        '$state',
        '$stateParams',
        '$scope',
        '$modal',
        'meetingService',
        'mailService',
        'inform',
        'Trans',
        'AgreeConstant',
        'expertService',
        'OfficeFileTool',
        'dayjsService',
        '$timeout',
        function (
            comService,
            $http,
            LocalCache,
            $rootScope,
            $state,
            $stateParams,
            $scope,
            $modal,
            meetingService,
            mailService,
            inform,
            Trans,
            AgreeConstant,
            expertService,
            OfficeFileTool,
            dayjsService,
            $timeout
        ) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            const now = dayjsService.format(dayjsService.now());
            //新增的会议室信息
            $scope.addList = [];
            //会议号提示
            $scope.meetingRemark =
                '威海4号楼第一会议室（28041）\n威海4号楼第二会议室（28042）\n4号楼第五会议室（28043）' +
                '\n4号楼3楼讨论区（28203）\n北分视频会议1（28201）\n北分视频会议2（28202）';
            var flag = 0;
            // 外部所有的编辑项
            $scope.changeParam = [];

            $scope.spec = {
                attachmentAddress: [],
                attachmentSize: [],
                attachmentAddressID: [],
                meetingType: 'MeetingType_2', //默认为评审会议
                meetingShape: '1', //会议形式默认为会议
                meetingNumber: '无', //视频会议号默认为无
                meetingState: '01', //会议状态默认为已确定
                meetingGrade: 'MeetingType_2_2', //会议等级默认为评审
                meetingDay: now,
                startTime: null,
                endTime: null,
            };
            $scope.seletedTime = now;

            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;

            //初始化参数
            init();
            // 弹窗中使用的时间段
            $scope.timeSlot = [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19];
            $scope.minute = [];
            for (var i = 0; i < $scope.timeSlot.length; i++) {
                $scope.minute.push('00');
                $scope.minute.push('30');
            }
            $scope.nowTime = inform.format(new Date(), 'yyyy-MM-dd');
            // 8：00 - 19：30构成的时间数组
            $scope.timePriodList = [];
            for (var j = 0; j < 26; j++) {
                let period = '';
                if (j === 0 || j === 25) {
                    period = '';
                } else {
                    period = `${$scope.timeSlot[Math.floor((j - 1) / 2)]}:${$scope.minute[j - 1]}`;
                }
                $scope.timePriodList.push(period);
            }

            // 所有的会议信息列表
            $scope.meetingInfoList = [];
            // 会议室信息的下拉列表
            $scope.meetingList = [];
            // 弹窗上方选中的会议室
            $scope.room = 0;
            // 编辑界面外部的表格界面
            $scope.meetingSeeList = [];

            $scope.getEmployeeByTerritory = getEmployeeByTerritory;
            getEmployeeByTerritory();

            //会议形式下拉框数据源
            $scope.meetingShapeSelect = [
                {
                    value: '0',
                    label: '邮件',
                },
                {
                    value: '1',
                    label: '会议',
                },
            ];
            $scope.roleList = [
                {
                    value: '1',
                    label: '关键人员',
                },
                {
                    value: '2',
                    label: '参会人员',
                },
            ];
            var meetingTitleParam = [];
            //时间选择框下拉框数据源
            $scope.timeSelect = [];
            $scope.keyFigureList = [];
            //设置时间选择框
            setTimeSelect();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //创建文件上传组件
            var paramObj = {
                listId: 'thelist',
                removeCall: function (id) {
                    var index = $scope.spec.attachmentAddressID.indexOf(id);
                    $scope.spec.attachmentAddress.splice(index, 1);
                    $scope.spec.attachmentSize.splice(index, 1);
                    $scope.spec.attachmentAddressID.splice(index, 1);
                },
                getFilePathCall: function (fileId) {
                    var index = $scope.spec.attachmentAddressID.indexOf(fileId);
                    var filePath = $scope.spec.attachmentAddress[index];
                    return filePath;
                },
                getSizeOfFiles: function () {
                    var size = 0;
                    for (var i = 0; i < $scope.spec.attachmentSize.length; i++) {
                        size = size + parseInt($scope.spec.attachmentSize[i]);
                    }
                    return size;
                },
                uploadSuccess: function (file, response) {
                    $scope.spec.attachmentAddress.push(response.data);
                    $scope.spec.attachmentAddressID.push(file.id);
                    $scope.spec.attachmentSize.push(file.size);
                },
            };
            OfficeFileTool.createUploader(paramObj, 'meeting');
            var finalMsg = '';
            var msgFlag = 0;
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            //删除新增的会议信息
            $scope.deleteItem = function (m) {
                var index = $scope.addList.indexOf(m);
                if (index > -1) {
                    $scope.addList.splice(index, 1);
                }
            };

            $scope.toBefore = function () {
                const now = dayjsService.now().format('YYYY-MM-DD');
                if (now === $scope.seletedTime) {
                    return;
                }
                var time = new Date($scope.seletedTime);
                $scope.seletedTime = inform.format(new Date(time.getTime() - 24 * 60 * 60 * 1000), 'yyyy-MM-dd');
                $scope.getMeetingInfoByTime();
            };
            $scope.toAfter = function () {
                var time = new Date($scope.seletedTime);
                $scope.seletedTime = inform.format(new Date(time.getTime() + 24 * 60 * 60 * 1000), 'yyyy-MM-dd');
                $scope.getMeetingInfoByTime();
            };
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var divHeight = 200;
                $('#divTBDis').height(divHeight);
                $('#subDivTBDis').height(divHeight - 50);
                $('#buttonStyle').css(inform.getButtonStyle(500, 800));
            }
            function init() {
                //获取员工信息
                $scope.loginMap = {};
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function (data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                        angular.forEach($scope.employeeList, function (employee) {
                            $scope.loginMap[employee.realName] = employee.loginName;
                        });
                        flag++;
                        copy();
                    }
                });
                getMeetingRoom();
            }

            /**
             * 获取所有会议室
             */
            function getMeetingRoom(init, meetingPlace, index) {
                meetingService.getMeetingRoom().then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            flag++;
                            $scope.meetingRoomList = data.data;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }

            /**
             * 打开弹窗
             * 修改会议室信息
             */
            $scope.getMeetingInfoByTime = function (m) {
                $('#add_meeting').modal('show');
                if ($scope.seletedTime == null || $scope.seletedTime === '') {
                    //默认查询时间
                    $scope.seletedTime = $scope.spec?.meetingDay || now;
                }
                // 如果会议列表为空，添加一条默认空白记录
                if ($scope.meetingList.length === 0) {
                    var item = {
                        seletedTime: $scope.seletedTime,
                        meetingPlace: null,
                        startTime: null,
                        endTime: null,
                        meetingRoom: [],
                        meetingRoomStatus: '未选择',
                    };
                    $scope.meetingList.push(item);
                }
                var urlData = {
                    begindate: $scope.seletedTime,
                    enddate: $scope.seletedTime,
                };
                meetingService.getMeetingInfoByTime(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            // 会议信息列表
                            $scope.meetingInfoList = data.data.map((i) => {
                                i.address = i.address.split(',').map((i) => Number(i));
                                return i;
                            });
                            $scope.getMeetingRoomStatus($scope.meetingList[0], '', '', 'dateChange');

                            $timeout(function () {}, 0);
                        } else {
                            $scope.meetingInfoList = [];
                            inform.common(data.message);

                            // 即使获取会议信息失败，仍然添加一条空白记录
                            $timeout(function () {
                                if ($scope.meetingList.length === 0) {
                                    var item = {
                                        seletedTime: $scope.seletedTime,
                                        meetingPlace: null,
                                        startTime: null,
                                        endTime: null,
                                        meetingRoom: [],
                                        meetingRoomStatus: '未选择',
                                    };
                                    $scope.meetingList.push(item);
                                }
                            }, 0);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };

            /**
             * 设置鼠标移入的title，显示会议信息
             */
            $scope.getTitle = function (id, period) {
                // 00：00 - 8：00和20：00 - 24：00 一定没有会议室占用
                if (!period) return '';

                const orderedMeetingRoom = $scope.meetingInfoList
                    ? $scope.meetingInfoList.filter((i) => i.address.some((addr) => addr === id))
                    : [];

                let currentMeeting = {};
                const curDay = dayjsService.format($scope.seletedTime);

                orderedMeetingRoom.forEach((i) => {
                    const startTimer = `${curDay} ${i.begintime}:00`;
                    const endTimer = `${curDay} ${i.endtime}:00`;

                    const startPeriod = `${curDay} ${period}:00`;
                    // 计算结束时间
                    const endPeriodDateTime = dayjsService.add(startPeriod, 30, 'minute');
                    const endPeriod = `${curDay} ${dayjsService.format(endPeriodDateTime, 'HH:mm')}:00`;

                    // 判断两个时间段是否重叠
                    const isOverlap =
                        dayjsService.isBefore(startPeriod, endTimer) && dayjsService.isAfter(endPeriod, startTimer);

                    if (isOverlap) {
                        currentMeeting = i;
                    }
                });
                if (Object.keys(currentMeeting).length === 0) return '';
                const titleVal = `会议名称：${currentMeeting.NAME}
召集人：${currentMeeting.callercallerLASTNAME || ''}
开始时间：${currentMeeting.begindate} ${currentMeeting.begintime}
结束时间：${currentMeeting.enddate} ${currentMeeting.endtime}`;

                return titleVal;
            };

            /**
             * 会议室是否被预定
             * @param {Number} id 会议室id
             * @param {String} period 开始的时间段
             * @returns {Boolean} 是否被预定
             */
            $scope.getTimePeriodList = (id, period) => {
                // 00：00 - 8：00和20：00 - 24：00 一定没有会议室占用
                if (!period) return false;

                const orderedMeetingRoom = $scope.meetingInfoList
                    ? $scope.meetingInfoList.filter((i) => i.address.some((addr) => addr === id))
                    : [];

                let isOrdered = false;
                const curDay = dayjsService.format($scope.seletedTime);

                orderedMeetingRoom.forEach((i) => {
                    const startTimer = `${curDay} ${i.begintime}:00`;
                    const endTimer = `${curDay} ${i.endtime}:00`;

                    const startPeriod = `${curDay} ${period}:00`;
                    // 计算结束时间
                    const endPeriodDateTime = dayjsService.add(startPeriod, 30, 'minute');
                    const endPeriod = `${curDay} ${dayjsService.format(endPeriodDateTime, 'HH:mm')}:00`;

                    // 判断两个时间段是否重叠
                    const isOverlap =
                        dayjsService.isBefore(startPeriod, endTimer) && dayjsService.isAfter(endPeriod, startTimer);

                    if (isOverlap) {
                        isOrdered = true;
                    }
                });
                return isOrdered;
            };

            /**
             * 设置新增后提示信息
             */
            function setMsg(mail, id) {
                if (msgFlag !== $scope.meetingList.length) {
                    return;
                }
                if (finalMsg === '') {
                    if (mail != null) {
                        packageMail(id);
                    } else {
                        //跳回主页面
                        layer.confirm(
                            '新增会议信息成功',
                            {
                                title: false,
                                btn: ['确定'],
                            },
                            function (result) {
                                layer.close(result);
                                $state.go('app.office.meetingController');
                            }
                        );
                    }
                } else {
                    //跳回修改
                    layer.confirm(
                        finalMsg,
                        {
                            title: false,
                            btn: ['确定'],
                        },
                        function (result) {
                            layer.close(result);
                            $state.go('app.office.meetingUpdateController', {
                                serialNumber: id,
                            });
                        }
                    );
                }
            }
            /**
             * 克隆信息
             */
            function copy() {
                if (flag !== 2 || $stateParams.serialNumber == null) {
                    return;
                }
                var urlData = {
                    id: $stateParams.serialNumber,
                };
                meetingService.getMeeting(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.spec = data.data;
                            $scope.spec.attachmentAddress = [];
                            $scope.spec.attachmentSize = [];
                            $scope.spec.attachmentAddressID = [];
                            $scope.keyFigureList = $scope.spec.keyFigures;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            /**
             * 获取人员信息
             */
            function getEmployeeByTerritory() {
                //拼接选中的行业领域
                var boxes = document.getElementsByName('experts');
                var territory = '';
                for (var i = 0; i < boxes.length; i++) {
                    if (boxes[i].checked) {
                        territory = territory + boxes[i].value + ',';
                    }
                }
            }
            /**
             * 设置时间选择框
             */
            function setTimeSelect() {
                for (var i = 6; i < 24; i++) {
                    //获取小时
                    var hour;
                    if (i < 10) {
                        hour = '0' + i;
                    } else {
                        hour = i;
                    }
                    for (var j = 0; j < 12; j++) {
                        //获取分钟（每10分钟，取一次）
                        var minutes;
                        if (j < 2) {
                            minutes = '0' + j * 5;
                        } else {
                            minutes = j * 5;
                        }
                        var value = {
                            value: hour + ':' + minutes,
                        };
                        //存入数组
                        $scope.timeSelect.push(value);
                    }
                }
                $scope.spec.startTime = '08:30';
                $scope.spec.endTime = '09:30';
            }

            /**
             * 校验关键角色列表中是否存在重复
             * 规则：
             * 1.重复则提示"关键角色×××,×××重复,请修改"并返回false
             * 2.无重复则返回true
             * @returns {boolean}
             */
            function verifyFigureList() {
                var verifyList = [];
                var duplicate = '';
                for (var i = 0; i < $scope.keyFigureList.length; i++) {
                    if (
                        verifyList.indexOf($scope.keyFigureList[i].keyFigure) > -1 &&
                        duplicate.indexOf($scope.keyFigureList[i].keyFigure) < 0
                    ) {
                        duplicate = duplicate.concat($scope.keyFigureList[i].keyFigure).concat(',');
                    }
                    verifyList.push($scope.keyFigureList[i].keyFigure);
                }
                //如果为空,说明无重复则返回true;
                if (!duplicate) {
                    return true;
                }
                //不为空,则提示哪些评委重复,并返回false
                inform.common('关键角色' + duplicate.substring(0, duplicate.length - 1) + '存在重复,请修改');
                return false;
            }
            //删除新增会议的信息
            $scope.deleteMeetingItem = function (m) {
                var index = $scope.meetingSeeList.indexOf(m);
                if (index > -1) {
                    $scope.meetingSeeList.splice(index, 1);
                }
                $scope.meetingList = $scope.meetingSeeList;

                if ($scope.meetingList.length > 0) {
                    $scope.seletedTime = $scope.meetingList[0].seletedTime;
                } else {
                    $scope.seletedTime = inform.format(new Date(), 'yyyy-MM-dd');
                }
            };
            /**
             * 表单内部点击保存
             * @returns
             */
            $scope.toSeeList = function () {
                if ($scope.meetingList[0].meetingRoom.length === 0) {
                    inform.common('请选择会议室');
                    return;
                }
                if (!$scope.meetingList[0].startTime) {
                    inform.common('请选择会议开始时间');
                    return;
                }
                if (!$scope.meetingList[0].endTime) {
                    inform.common('请选择会议结束时间');
                    return;
                }

                // 校验所有会议的开始时间是否小于结束时间
                for (var i = 0; i < $scope.meetingList.length; i++) {
                    const item = $scope.meetingList[i];
                    const startTimeArray = item.startTime.split(':');
                    const endTimeArray = item.endTime.split(':');

                    if (
                        startTimeArray[0] > endTimeArray[0] ||
                        (startTimeArray[0] === endTimeArray[0] && startTimeArray[1] >= endTimeArray[1])
                    ) {
                        inform.common('第' + (i + 1) + '条会议的结束时间必须大于开始时间，请重新选择！');
                        return;
                    }
                }

                let flag = 0;
                // 检查会议室是否可用
                for (var j = 0; j < $scope.meetingList.length; j++) {
                    if ($scope.meetingList[j].meetingRoomStatus === '不可用') {
                        flag++;
                    }
                }

                if (flag) {
                    inform.common('会议室占用,请重新选择.');
                    return;
                }

                $scope.meetingSeeList = [];
                $scope.meetingSeeList = $scope.meetingList.map((i) => {
                    return {
                        meetingRoomStatus: i.meetingRoomStatus,
                        meetingPlace: i.meetingPlace,
                        meetingRoom: i.meetingRoom,
                        startTime: i.startTime,
                        endTime: i.endTime,
                    };
                });
                // 与外部编辑页面保持同步
                $scope.spec.startTime = $scope.meetingList[0].startTime;
                $scope.spec.endTime = $scope.meetingList[0].endTime;
                $scope.spec.meetingDay = $scope.seletedTime;
                $('#add_meeting').modal('hide');
            };
            /**
             *
             * 查看会议室结束时间
             */
            $scope.checkRoom = function (mail) {
                msgFlag = 0;
                finalMsg = '';
                if ($scope.meetingList.length === 0 || $scope.meetingList[0].meetingRoom.length === 0) {
                    $scope.addInfo(mail);
                } else {
                    var engTimeMsg = '';
                    for (var j = 0; j < $scope.meetingList.length; j++) {
                        if ($scope.meetingList[j].endTime !== $scope.spec.endTime) {
                            engTimeMsg =
                                engTimeMsg + $scope.meetingList[j].meetingRoom + '预定结束时间与会议结束时间不符\n';
                        }
                    }
                    if (engTimeMsg === '') {
                        $scope.addInfo(mail);
                    } else {
                        var modalInstance = $modal.open({
                            templateUrl: 'errorModel.html',
                            controller: 'ModalInstanceCtrl',
                            size: 'lg',
                            resolve: {
                                items: function () {
                                    return engTimeMsg;
                                },
                            },
                        });
                        modalInstance.result.then(function () {
                            $scope.addInfo(mail);
                        });
                    }
                }
            };

            function extracted(mail) {
                if ($scope.spec.meetingType == null) {
                    inform.common('请选择会议类别');
                    return;
                }
                if ($scope.spec.meetingType === 'MeetingType_2' && $scope.spec.meetingGrade == null) {
                    inform.common('请选择会议等级');
                    return;
                }
                if ($scope.spec.meetingType === 'MeetingType_1' && $scope.spec.meetingGrade != null) {
                    $scope.spec.meetingGrade = '';
                }
                if ($scope.keyFigureList.length === 0) {
                    inform.common('请选择人员');
                    return;
                }
                //校验关键角色列表是否有重复
                if (!verifyFigureList()) {
                    return;
                }
                if ($scope.keyFigureList != null) {
                    //以逗号拼接关键角色
                    var keyFigure = '';
                    for (var i = 0; i < $scope.keyFigureList.length; i++) {
                        keyFigure = keyFigure
                            .concat($scope.keyFigureList[i].keyFigure)
                            .concat(',')
                            .concat($scope.keyFigureList[i].role)
                            .concat(';');
                    }
                }
                //获取当前时间是星期几
                var now = new Date($scope.spec.meetingDay);
                var meetingWeek = '';
                if (now.getDay() === 0) {
                    meetingWeek = '星期7';
                } else {
                    meetingWeek = '星期' + now.getDay();
                }
                var urlData = {
                    meetingType: $scope.spec.meetingType,
                    meetingDay: inform.format($scope.spec.meetingDay, 'yyyy-MM-dd'),
                    meetingWeek: meetingWeek,
                    startTime: $scope.spec.startTime,
                    endTime: $scope.spec.endTime,
                    meetingShape: $scope.spec.meetingShape,
                    meetingTheme: $scope.spec.meetingTheme,
                    projectAssistant: $scope.spec.projectAssistant,
                    meetingPlace: $scope.spec.meetingPlace,
                    meetingRoom: $scope.meetingSeeList
                        .map((i) => {
                            // 会议室名称的集合
                            const room = $scope.meetingRoomList
                                .filter((j) => i.meetingRoom.includes(j.id))
                                .map((j) => j.NAME)
                                .join(',');
                            return room;
                        })
                        .join(','),
                    meetingState: $scope.spec.meetingState,
                    meetingGrade: $scope.spec.meetingGrade,
                    keyFigure: keyFigure,
                    remark: $scope.spec.remark,
                    accessory: $scope.spec.attachmentAddress.join(','),
                    accessorySize: $scope.spec.attachmentSize.join(','),
                    meetingNumber: $scope.spec.meetingNumber,
                    meetingRooms: $scope.meetingSeeList.map((i) => {
                        i.roomId = i.meetingRoom.join(',');
                        i.place = i.meetingPlace;
                        i.room = $scope.meetingRoomList
                            .filter((j) => i.meetingRoom.includes(j.id))
                            .map((j) => j.NAME)
                            .join(',');
                        return i;
                    }),
                };

                meetingService.addMeetingInfo(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (mail != null) {
                                packageMail(data.data);
                            } else {
                                //跳回主页面
                                layer.confirm(
                                    data.message,
                                    {
                                        title: false,
                                        btn: ['确定'],
                                    },
                                    function (result) {
                                        layer.close(result);
                                        $state.go('app.office.meetingController');
                                    }
                                );
                            }
                        } else {
                            let msg = data.message;
                            if (data.message === '草稿') {
                                msg = '会议室被占用，请重新选择会议室';
                            }
                            inform.common(msg);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }

            /**
             * 添加信息
             */
            $scope.addInfo = function (mail) {
                if (mail !== undefined && $scope.spec.meetingShape === '1' && $scope.meetingList.length === 0) {
                    inform.modalInstance('     您尚未预定会议室，确认继续发送邮件提醒吗？').result.then(function () {
                        extracted(mail);
                    });
                } else {
                    extracted(mail);
                }
            };
            /**
             * 发送邮件
             */
            function packageMail(id) {
                var urlData = {};
                if ($scope.spec.meetingType === 'MeetingType_2') {
                    urlData = {
                        beanName: 'com.snbc.office.service.impl.review.ReviewMeetingSeriveImpl',
                        serialNumber: id,
                    };
                } else if ($scope.spec.meetingType === 'MeetingType_1') {
                    urlData = {
                        beanName: 'com.snbc.office.service.impl.OrdinaryMeetingSeriveImpl',
                        serialNumber: id,
                    };
                }
                mailService.packageMail(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            var info = data.data;
                            info.addressees = [];
                            info.addresseesHand = [];
                            $scope.spec.keyFigureList = $scope.keyFigureList;
                            for (var i = 0; i < $scope.keyFigureList.length; i++) {
                                var name = $scope.keyFigureList[i].keyFigure;
                                if ($scope.loginMap[name] == null || $scope.loginMap[name] === '') {
                                    info.addresseesHand.push(name);
                                } else {
                                    info.addressees.push($scope.loginMap[name] + '@newbeiyang.com');
                                }
                            }
                            info.accessory = $scope.spec.attachmentAddress.join(',');
                            info.accessorySize = $scope.spec.attachmentSize.join(',');
                            info.theme = $scope.spec.meetingTheme;
                            LocalCache.setObject('mail_info', info);
                            $state.go('app.office.mailManagement', {
                                root: 'app.office.meetingUpdateController',
                                serialNumber: id,
                            });
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            /**
             * 修改开始时间后，默认结束时间退后一小时
             */
            $scope.changeEnd = function () {
                var startTimeArray = $scope.spec.startTime.split(':');
                var hour = parseInt(startTimeArray[0]) + 1;
                var before = hour < 10 ? '0' : '';
                $scope.spec.endTime = before + hour + ':' + startTimeArray[1];
            };

            /**
             * 编辑界面外部的会议时间选择
             * 判断会议时间是否正常
             * 时间不正常，弹出提示信息，并清空最后选择的选择框的值
             * @param flag startTime：开始时间选择框  endTime：结束时间选择框
             */
            $scope.timeChange = function (flag, startTime, endTime) {
                // 如果开始时间改变了，自动设置结束时间为开始时间加一小时
                if (flag === 'startTime' && startTime !== '' && startTime != null) {
                    const today = dayjsService.format(dayjsService.now());
                    const startDateTime = `${today} ${startTime}:00`;

                    // 检查开始时间是否接近或等于23:00，超过这个时间不再自动设置结束时间
                    const hourValue = parseInt(startTime.split(':')[0]);
                    if (hourValue < 23) {
                        // 添加一小时
                        const endDateTime = dayjsService.add(startDateTime, 1, 'hour');
                        // 格式化为HH:mm
                        const formattedEndTime = dayjsService.format(endDateTime, 'HH:mm');

                        // 更新结束时间
                        $scope.spec.endTime = formattedEndTime;
                        endTime = formattedEndTime;
                    }
                }

                if (startTime !== '' && endTime !== '' && startTime != null && endTime != null) {
                    const today = dayjsService.format(dayjsService.now());
                    const startDateTime = `${today} ${startTime}:00`;
                    const endDateTime = `${today} ${endTime}:00`;

                    // 使用dayjsService比较时间
                    if (!dayjsService.isBefore(startDateTime, endDateTime)) {
                        inform.common('会议结束时间必须大于会议开始时间，请重新选择！！！');
                    }
                }
            };

            /**
             * 返回按钮
             */
            $scope.backModal = function () {
                $state.go('app.office.meetingController', { type: 2 });
            };
            /**
             * 新增一个关键角色
             */
            $scope.addNewBind = function () {
                //评委信息
                var judge = {
                    role: '参会人员',
                    keyFigure: '',
                    flag: true,
                };
                $scope.keyFigureList.push(judge);
                var div = document.getElementById('add');
                div.scrollTop = div.scrollHeight;
            };
            /**
             * 新增一个会议室信息
             */
            $scope.addMeetingBind = function () {
                var startDate = inform.format(
                    inform.format(new Date(), 'yyyy-MM-dd') + ' ' + $scope.spec.startTime,
                    'yyyy-MM-dd HH:mm'
                );
                var beforeNow = new Date(startDate);
                beforeNow.setMinutes(beforeNow.getMinutes() - 10);
                beforeNow = inform.format(beforeNow, 'yyyy-MM-dd HH:mm');
                var beforeStartTime = beforeNow.split(' ');
                //会议室信息
                var meeting = {
                    meetingPlace: '',
                    meetingRoom: '',
                    meetingRoomList: [],
                    startTime: beforeStartTime[1],
                    endTime: $scope.spec.endTime,
                };
                $scope.meetingList.push(meeting);
            };

            // 删除某一行关键人物信息
            $scope.deleteNewBind = function (index) {
                if (index >= 0) {
                    $scope.keyFigureList.splice(index, 1);
                }
            };
            //取消一行会议室信息
            $scope.delMeetingBind = function (index) {
                if (index >= 0) {
                    $scope.meetingList.splice(index, 1);
                    // 如果会议列表为空，添加一条默认空白记录
                    if ($scope.meetingList.length === 0) {
                        var meetingListItem = {
                            seletedTime: $scope.seletedTime,
                            meetingPlace: null,
                            startTime: null,
                            endTime: null,
                            meetingRoom: [],
                            meetingRoomStatus: '未选择',
                        };
                        $scope.meetingList.push(meetingListItem);
                        $timeout(function () {}, 0);
                    }
                }
            };

            /**
             * 会议日期时间
             */
            $scope.meetingDayOpen = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.meetingDayOne = true;
            };

            /**
             * 获取所有会议室
             */
            function getMeetingRoom(init, meetingPlace, index) {
                meetingService.getMeetingRoom().then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            flag++;
                            $scope.meetingRoomList = data.data;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            // 获取会议室地点
            $scope.getMeetingRoomPlace = function (item) {
                const relevantMeetings = $scope.meetingRoomList.filter((meeting) =>
                    item.meetingRoom.includes(meeting.id)
                );
                return relevantMeetings.map((i) => `${i.mybuilding}${i.myfloor}`).join(',');
            };
            //获取会议室状态
            $scope.getMeetingRoomStatus = function (item, index, mail, tip) {
                // 检查输入参数
                if (!item || !item.meetingRoom || !item.startTime || !item.endTime || item.length === 0) {
                    if (index) {
                        $scope.meetingList[index].meetingRoomStatus = '不可用';
                    } else {
                        $scope.meetingList[0].meetingRoomStatus = '不可用';
                    }
                    if (tip !== 'dateChange') {
                        inform.common('请填写完整的会议室和时间信息');
                    }
                    return;
                }

                // 会议室ID可能是单个值或数组
                const roomIds = Array.isArray(item.meetingRoom) ? item.meetingRoom : [item.meetingRoom];
                const meetingDate = $scope.seletedTime || $scope.spec.meetingDay || now;
                const curDay = dayjsService.format(meetingDate);

                // 处理时间格式
                const startTimer = `${curDay} ${item.startTime}:00`;
                const endTimer = `${curDay} ${item.endTime}:00`;

                // 检查时间有效性
                if (dayjsService.isAfter(startTimer, endTimer)) {
                    if (index) {
                        $scope.meetingList[index].meetingRoomStatus = '不可用';
                    } else {
                        $scope.meetingList[0].meetingRoomStatus = '不可用';
                    }
                    inform.common('会议结束时间必须大于会议开始时间');
                    return;
                }

                // 默认状态为可用
                let isAvailable = true;

                // 检查每个会议室是否可用
                if ($scope.meetingInfoList && $scope.meetingInfoList.length > 0) {
                    // 筛选出所选会议室的预订记录
                    const relevantMeetings = $scope.meetingInfoList.filter((meeting) =>
                        // 判断两个数组是否有交集 - 只要有一个元素重叠即可
                        meeting.address.some((addr) => roomIds.includes(addr))
                    );

                    // 检查是否有时间重叠
                    relevantMeetings.forEach((meeting) => {
                        const meetingStartTime = `${curDay} ${meeting.begintime}:00`;
                        const meetingEndTime = `${curDay} ${meeting.endtime}:00`;

                        // 时间段重叠判断
                        const isOverlap =
                            dayjsService.isBefore(startTimer, meetingEndTime) &&
                            dayjsService.isAfter(endTimer, meetingStartTime);

                        if (isOverlap) {
                            isAvailable = false;
                        }
                    });
                }

                // 设置状态
                if (index) {
                    $scope.meetingList[index].meetingRoomStatus = isAvailable ? '可用' : '不可用';
                } else {
                    item.meetingRoomStatus = isAvailable ? '可用' : '不可用';
                }

                return isAvailable ? '可用' : '不可用';
            };

            //当会议室下拉框值改变时
            $scope.changeMeetingRoom = function (item) {
                if (!item.meetingRoom || item.meetingRoom.length === 0) {
                    if ($scope.meetingList.length > 0) {
                        $scope.meetingList[0].meetingRoomStatus = '未选择';
                        $scope.meetingList[0].meetingPlace = '';
                    }
                    return;
                }

                // 如果已经选择了开始和结束时间，则更新状态
                if (item.startTime && item.endTime) {
                    $scope.meetingList[0].meetingRoomStatus = $scope.getMeetingRoomStatus(item);
                }
                $scope.meetingList[0].meetingPlace = $scope.getMeetingRoomPlace(item);
            };

            //当会议时间下拉框值改变时
            $scope.changeMeetingTime = function (field, item, index) {
                // 如果选择的是开始时间，自动设置结束时间为开始时间+1小时
                if (field === 'startTime' && item.startTime) {
                    // 检查开始时间是否接近或等于23:00，超过这个时间不再自动设置结束时间
                    const hourValue = parseInt(item.startTime.split(':')[0]);
                    if (hourValue < 23) {
                        const today = dayjsService.format(dayjsService.now());
                        const startDateTime = `${today} ${item.startTime}:00`;
                        // 添加一小时
                        const endDateTime = dayjsService.add(startDateTime, 1, 'hour');
                        // 格式化为HH:mm
                        const formattedEndTime = dayjsService.format(endDateTime, 'HH:mm');
                        // 更新结束时间
                        item.endTime = formattedEndTime;
                    }
                }
                // 确保开始时间和结束时间都已设置
                if (item.startTime && item.endTime) {
                    // 校验开始时间必须小于结束时间
                    const startTimeArray = item.startTime.split(':');
                    const endTimeArray = item.endTime.split(':');
                    if (
                        startTimeArray[0] > endTimeArray[0] ||
                        (startTimeArray[0] === endTimeArray[0] && startTimeArray[1] >= endTimeArray[1])
                    ) {
                        inform.common('会议结束时间必须大于会议开始时间，请重新选择！');
                        // 清空当前修改的时间字段
                        if (field === 'startTime') {
                            item.startTime = null;
                        } else {
                            item.endTime = null;
                        }
                        return;
                    }
                    if (item.meetingRoom && item.meetingRoom.length > 0) {
                        // 更新会议室状态
                        item.meetingRoomStatus = $scope.getMeetingRoomStatus(item, index);
                    }
                }
            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        },
    ]);
})();

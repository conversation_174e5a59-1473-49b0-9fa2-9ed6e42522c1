(function () {
    'use strict';
    app.factory('scheduleDeviationService', scheduleDeviationService);
    scheduleDeviationService.$inject = ["HttpService", '$rootScope'];

    function scheduleDeviationService(HttpService, $rootScope) {

        var service = {
            getInformation: getInformation,
            getProjectDetailsInformation: getProjectDetailsInformation,
            toExcel: toExcel,
            getProjectInfo: getProjectInfo,
            getParamValue: getParamValue,
            addProjectInfo: addProjectInfo,
            updateProjectInfo: updateProjectInfo,
            deleteProjectInfo: deleteProjectInfo,
            addProjectDetailInfo: addProjectDetailInfo,
            updateProjectDetailInfo: updateProjectDetailInfo,
            deleteProjectDetailInfo: deleteProjectDetailInfo,
            updateMilestonePlanResource: updateMilestonePlanResource,
            getProjectDetailsHistory: getProjectDetailsHistory
        };
        return service;


        /**
         * 查询产品线信息
         */
        function getInformation(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'scheduleDeviation/getInformation', urlData);
        }

        /**
         * 查询项目信息
         */
        function getProjectDetailsInformation(line) {
            return HttpService.post($rootScope.getWaySystemApi + 'scheduleDeviation/getProjectDetailsInformation', line);
        }

        /**
         * 将前台表格内容进行下载
         */
        function toExcel(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'scheduleDeviation/toExcel', urlData);
        }

        /**
         * 分页查询项目信息
         */
        function getProjectInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'scheduleDeviation/getProjectInfo', urlData);
        }

        /**
         * 获取产品线信息
         */
        function getParamValue() {
            return HttpService.post($rootScope.getWaySystemApi + 'scheduleDeviation/getParamValue');
        }

        /**
         * 产品新增
         */
        function addProjectInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'scheduleDeviation/addProjectInfo', urlData);
        }

        /**
         * 产品修改
         */
        function updateProjectInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'scheduleDeviation/updateProjectInfo', urlData);
        }

        /**
         * 产品删除
         */
        function deleteProjectInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'scheduleDeviation/deleteProjectInfo', urlData);
        }

        /**
         * 产品详情新增
         */
        function addProjectDetailInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'scheduleDeviation/addProjectDetailInfo', urlData);
        }

        /**
         * 产品详情修改
         */
        function updateProjectDetailInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'scheduleDeviation/updateProjectDetailInfo', urlData);
        }

        /**
         * 产品详情删除
         */
        function deleteProjectDetailInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'scheduleDeviation/deleteProjectDetailInfo', urlData);
        }

        /**
         * 更新里程碑计划来源
         */
        function updateMilestonePlanResource(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'scheduleDeviation/updateMilestonePlanResource', urlData);
        }

        /**
         * 查询里程碑的历史信息
         */
        function getProjectDetailsHistory(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'scheduleDeviation/getProjectDetailsHistory', urlData);
        }

    }
})();

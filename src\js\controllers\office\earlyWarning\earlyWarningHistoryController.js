(function () {
    'use strict';
    app.controller("earlyWarningHistoryController", ['$scope','$state','comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','quartzScheduledService','$stateParams','LocalCache', '$modal','$http',
        function ($scope,$state,comService, $rootScope, inform, Trans, AgreeConstant,quartzScheduledService,$stateParams,LocalCache, $modal,$http) {
    	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		//执行结果下拉框数据源
        $scope.statusType = [{
            code: '0',
            value: '成功'
        },{
        	code: '1',
        	value: '失败'
        }];
		//任务结果map
		$scope.statusMap = {
			"0":"成功",
			"1":"失败"
		};
		//分页
    	$scope.pages = inform.initPages(); // 初始化分页数据
    	$scope.reset = reset;
		$scope.getData = getData; 
		reset();
    	getData("");
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */	
    	/**
		  * 设置列表的高度
		  */
		function setDivHeight(){
			//网页可见区域高度
			var clientHeight = document.body.clientHeight;
			var divHeight = clientHeight - (150 + 220);
			$("#divTBDis").height(divHeight );
			$("#subDivTBDis").height(divHeight);
		}
		/**
		 * 重置
		 */
		function reset() {
			$scope.formRefer = {
				startTime:'',//开始时间
				endTime:'',//结束时间
				status:''//执行结果
			}
		}
		/**
         * 查询开始时间
         */
        $scope.openDateStart = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = true;
            $scope.openedEnd = false;
        };

        /**
         * 查询结束时间
         */
        $scope.openDateEnd = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = false;
            $scope.openedEnd = true;
        };
        /**
         * 根据id获取历史记录
         */
        function getData(pageNum) {
			var urlData ={
				'jobId':$stateParams.id,//所属任务
				'status':$scope.formRefer.status,//执行结果
				'startTime':inform.format($scope.formRefer.startTime,'yyyy-MM-dd'),//开始时间
				'endTime':inform.format($scope.formRefer.endTime,'yyyy-MM-dd'),//结束时间
	            'page':pageNum,//当前页数
		        'pageSize':$scope.pages.size//每页显示条数
			};
			quartzScheduledService.getHistoryInfo(urlData).then(function(data) {
					if (data.code===AgreeConstant.code) {
						//项目详情
						$scope.jsonData = data.data.list;
						if ($scope.jsonData.length===0) {
		                    inform.common(Trans("tip.noData"));
	    	                $scope.pages = inform.initPages();
		                } else {
		                    //分页信息设置
		                    $scope.pages.total = data.data.total;
		                    $scope.pages.star = data.data.startRow;
		                    $scope.pages.end = data.data.endRow;
		                    $scope.pages.pageNum = data.data.pageNum;
		                }
					} else {
						inform.common(data.message);
					}
				},
				function(error) {
					inform.common(Trans("tip.requestError"));
				});
		}
	     /**
          * *************************************************************
          *              方法声明部分                                 结束
          * *************************************************************
          */
         } 
    ]);
})();
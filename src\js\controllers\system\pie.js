/*
 * @Author: fubaole
 * @Date:   2018-01-03 11:34:44
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-30 09:59:44
 */
(function() {
  'use strict';
  app.controller("pieCtrl", ['$scope','$rootScope', 'inform', '$timeout', 'Trans', '$interval', '$location',
    function($scope,$rootScope, inform, $timeout, Trans, $interval, $location) {

      $scope.getData = getData;
      $scope.initConfig = initConfig;
      var option = {};
      initConfig();
      getData();

      function getData() {
          option = {
            tooltip: {
              trigger: 'item',
              formatter: "{a} <br/>{b} : {c} ({d}%)"
            },
            legend: {
              orient: 'vertical',
              left: 'left',
              data: ['直接访问', '邮件营销', '联盟广告', '视频广告', '搜索引擎']
            },
            series: [{
              name: '访问来源',
              type: 'pie',
              radius: '55%',
              center: ['50%', '60%'],
              data: [
                { value: 335, name: '直接访问' },
                { value: 310, name: '邮件营销' },
                { value: 234, name: '联盟广告' },
                { value: 135, name: '视频广告' },
                { value: 1548, name: '搜索引擎' }
              ],
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }]
          };
          //tip:接口数据成功后，调用此方法，渲染页面
          reloadPageData(option);

      }

      //接口获取的数据，执行数据渲染操作
      function reloadPageData(option){
        if($scope.$parent.screenFlag){
            $timeout(function(){
              $scope.fullBar.setOption(option,true);
            },0);
          }else{
            $timeout(function(){
              $rootScope.pie.setOption(option,true);
            },0);
          }
      }

      // 全屏图表
      function initConfig(){
        if($scope.$parent.screenFlag){
        $('.fullscreen #pie').width(document.body.clientWidth*0.85);
        $('.fullscreen #pie').height(document.body.clientHeight*0.6);
        console.log($('.fullscreen #pie').height());
        console.log($('.fullscreen #pie').width());
        $timeout(function(){
          $scope.fullBar = echarts.init($('.fullscreen #pie')[0]);
          $scope.fullBar.setOption(option,true);
        },0);
      }else{
        $timeout(function(){
          $rootScope.pie = echarts.init(document.getElementById('pie'));
          $rootScope.pie.setOption(option,true);
        },0);
      }

      // 刷新该模块
      $scope.$on('reload', function(e, id) {
          console.log("父级传来的数据ID"+id+"根据ID重新加载该模块");
          //tip:根据接口给的contentId，判断if(contentId===id),则执行刷新操作,调用getData();
          
      });

      var timeout_upd = $interval(function(){
        if ($location.path() === '/app/index_bench' || $location.path().indexOf('preview_page')!== -1) {
          getData();
        }
      } ,16000);

      // 清除定时器
      $scope.$on('$destroy',function(){
        $interval.cancel(timeout_upd);
      });
      
    }

    }
  ]);
})();
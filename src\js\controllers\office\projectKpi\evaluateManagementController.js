(function () {
    'use strict';
    app.controller("evaluateManagementController", ['$scope','$state','comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','evaluateService','$stateParams','LocalCache', '$modal','$http',
        function ($scope,$state,comService, $rootScope, inform, Trans, AgreeConstant,evaluateService,$stateParams,LocalCache, $modal,$http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 路由中传参数，区分是管理页面还是历史页面
            $scope.flag = $stateParams.flag;
            // 获取登录人的域账户名称
            $scope.loginName = LocalCache.getSession('currentUserName');
            //查询条件 从缓存中读取
            $scope.formRefer = LocalCache.getObject('evaluateManagement_formRefer');
            // 查询时默认值设置
            if (!Object.getOwnPropertyNames($scope.formRefer).length) {
                // 使用默认值查询信息
                resetData();
            }
            LocalCache.setObject('evaluateManagement_formRefer',{});
            // 初始化分页数据
            $scope.pages = inform.initPages();
            //考核状态
            $scope.stateSelect = [{
                value: '0',
                label: '待评价'
            }, {
                value: '2',
                label: '已评价'
            }];
            // 评价事项
            $scope.evaluateEumnSelect = [{
                value: 'TEAM_ABILITY',
                label: '团队能力评价'
            }, {
                value: 'CUSTOMER',
                label: '客户评价'
            }, {
                value: 'COURSE',
                label: '过程质量评价'
            }, {
                value: 'PROGRAM',
                label: '代码质量评价'
            }, {
                value: 'VALUES',
                label: '部门贡献评价'
            }/*, {
                value: 'BUG_ABILITY',
                label: 'bug修改能力评价'
            }*/];
            $scope.checkTypeMap = {
                '0': '项目',
                '1': '团队'
            };
            $scope.checkStatusMap = {
                '0': '待评价',
                '2': '已评价'
            };
            $scope.kpiQuarterMap = {
                '6':'上半年',
                '7':'下半年',
                '5':'年度'
            };
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //初始化查询
            initPages();
            //获取数据
            $scope.getData = getData;
            getData();
            $scope.resetData = resetData;
            $scope.domainChange = getData;
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 95);
            }
            /**
             * 初始化
             */
            function initPages() {
                //获取部门信息
                $scope.departmentSelect = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentSelect = comService.getDepartment(data.data);
                });
            }
            /**
             * 获取kpi查询信息
             */
            function getData(pageNum) {
                var dateTime = '';
                // 评价日期时间 + 1 是为了查询结果正确
                if ($scope.formRefer.evaluateEndTime) {
                    dateTime = new Date($scope.formRefer.evaluateEndTime);
                    dateTime = dateTime.setDate(dateTime.getDate() + 1);
                    dateTime = new Date(dateTime);
                }
                var urlData = {
                    'departmentCode': $scope.formRefer.departmentCode,
                    'projectName': $scope.formRefer.projectName,
                    'projectManager': $scope.formRefer.projectManager,
                    'evaluateEnumCode': $scope.formRefer.evaluateEnumCode,
                    'status': $scope.formRefer.status,
                    'evaluatePersonName': $scope.formRefer.evaluatePersonName,
                    'evaluateBeginTime': inform.format($scope.formRefer.evaluateBeginTime, 'yyyy-MM-dd'),
                    'evaluateEndTime': inform.format(dateTime, 'yyyy-MM-dd'),
                    'currentPage': pageNum,
                    'pageSize': $scope.pages.size,
                };
                // 历史页面状态为已存档
                if ($scope.flag === 'history') {
                    urlData.status = '3';
                }
                // 管理页面 并且没有选择状态时 传‘-1’，后端查询非存档的数据
                if ($scope.flag === 'management' && !$scope.formRefer.status) {
                    urlData.status = '-1';
                }
                // 查询范围（本人/全部）
                if ($scope.formRefer.domain === '0' && $scope.flag === 'management') {
                    urlData.evaluatePersonLoginName = $scope.loginName;
                } else {
                    urlData.evaluatePersonLoginName = '';
                }
                evaluateService.getEvaluateData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            //信息
                            $scope.evealuateInfoList = data.data.list;
                            $scope.evealuateInfoList.forEach(
                                function (m) {
                                    if (m.kpiYyyy != null) {
                                        m.quarter = m.kpiYyyy + '-' + $scope.kpiQuarterMap[m.kpiQuarter]
                                    } else {
                                        m.quarter = ''
                                    }
                                });
                            // 分页信息设置
                            $scope.pages.total = data.data.total;           // 页面数据总数
                            $scope.pages.star = data.data.startRow;         // 页面起始数
                            $scope.pages.end = data.data.endRow;            // 页面结束数
                            $scope.pages.pageNum = data.data.pageNum;       //页号
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            /**
             * 详情方法
             */
            $scope.toDetails = function (m){
                var periodMap = {
                    '5':'年度',
                    '6':'上半年',
                    '7':'下半年',
                    '1':'Q1',
                    '2':'Q2',
                    '3':'Q3',
                    '4':'Q4'
                };

                //名称、领导、时间 放缓存
                var topInfo = {
                    // 项目名称
                    'name':m.projectName,
                    // 成绩
                    'score': m.kpiScore,
                    // 项目经理
                    'pm':m.projectManager,
                    //团队周期
                    'period':m.kpiYyyy+'-'+periodMap[m.kpiQuarter],
                    //考核时间
                    'time':m.assessmentDate
                }
                LocalCache.setObject('kpiInfo_topInfo', topInfo);
                //查询条件放缓存
                LocalCache.setObject('evaluateManagement_formRefer',$scope.formRefer);
                if (m.projectType==='0'){
                    //项目
                    $state.go("app.office.projectKpiDetail", {kpiId: m.kpiId});
                } else {
                    //团队
                    $state.go("app.office.teamKpiDetail", {kpiId: m.kpiId});
                }
            }

            /**
             * 跳转去修改/详情页面
             */
            $scope.toModify = function (m){
                //名称、领导、时间 放缓存
                var topInfo = {
                    // 项目名称
                    'projectName':m.projectName,
                    // 评价负责人
                    'evaluatePersonName':m.evaluatePersonName,
                    // 评价id
                    'id': m.id,
                    // kpiId
                    'kpiId': m.kpiId,
                    // 评价code
                    'evaluateEnumCode': m.evaluateEnumCode,
                    // 评价名称
                    'evaluateEnumLabel': m.evaluateEnumLabel,
                    // 项目id
                    'projectId': m.projectId,
                    // 团队考核周期开始时间
                    'beginTimeTeam': m.beginTimeTeam,
                    // 团队考核周期结束时间
                    'endTimeTeam': m.endTimeTeam,
                    // 项目类型
                    'type': m.projectType,
                    //项目经理
                    'projectManager': m.projectManager
                }
                LocalCache.setObject('evaluate_topInfo', topInfo);
                //查询条件放缓存
                LocalCache.setObject('evaluateManagement_formRefer',$scope.formRefer);
                if ($scope.flag === 'management') {
                    $state.go("app.office.evaluateModify");
                } else {
                    $state.go("app.office.evaluateDetail");
                }
            }

            /**
             * 重置查询数据
             * */
            function resetData () {
                $scope.formRefer = {};
                // 默认查询待考核的评价信息
                $scope.formRefer.status = '0';
                // 默认查询评价人为本人的数据
                $scope.formRefer.domain = '0';
            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }
    ]);
})();

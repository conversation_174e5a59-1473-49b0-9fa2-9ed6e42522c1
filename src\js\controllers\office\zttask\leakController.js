
(function() {
	app.controller("leakManagement", ['comService', '$rootScope', '$scope', 'leakService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
		function(comService, $rootScope, $scope, leakService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {
		
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		
		$scope.datepicker = {};
        $scope.toggleMin = toggleMin;
        toggleMin();
 		//查询条件
		$scope.formRefer = {
			projectName:'',//项目名称
	    	startTime:'',//开始时间
	    	endTime:''//结束时间	
	    };
		$scope.start = "";//格式化后的开始时间
		$scope.end = "";//格式化后的结束时间
		$scope.time = "";//格式化后的当前时间

		$scope.projectList = [];//查询返回的项目列表

		$scope.getData = getData;//获取数据
		initTime($scope.formRefer.endTime);//初始化日期
		getData();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */	
		
		 /**
		  * 设置列表的高度
		  */
 		
 		//获取当前选定时间
        function toggleMin() {
            $scope.datepicker.currentDate = $scope.datepicker.currentDate ? null : new Date();
            //获取部门
            $scope.departmentList = [];
          	comService.getOrgChildren('D010053').then(function(data) {
      			$scope.departmentList = data.data;
            });
          //获取产品线
            $scope.productLineList = [];
            comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                if (data.data) {
                    $scope.productLineList = data.data;
                }
            });
        }
		
 		/**
 		 * 重置
 		 */
		$scope.rest = function() {
			$scope.formRefer.startTime = '';
			$scope.formRefer.endTime = '';
			$scope.formRefer.projectName = '';
			initTime($scope.formRefer.endTime);//初始化日期
		};
		
    	/**
    	 * 开始时间
    	 */
		$scope.openDateStart = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.openedStart = true;
			$scope.openedEnd = false;
		};
		
		/**
		 * 结束时间
		 */
		$scope.openDateEnd = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.openedStart = false;    
			$scope.openedEnd = true;
		};
		
		/**
		 * 初始化日期
		 */
		function initTime(endTime){
			if (endTime==null || endTime === "" ){
				$scope.formRefer.endTime = inform.format(new Date(),'yyyy-MM-dd');//获取当前系统时间
			} 
			var time = $scope.formRefer.endTime.split("-");
			var start = time[0]+"/01"+"/01";
			$scope.formRefer.startTime = inform.format(start,'yyyy-MM-dd');
			//对最后计算出的日期进行格式化（此时 若计算的天数时间不合理 会进行合理化）
		}
		
		/**
		 * 格式化日期并进行条件判断
		 */
		function formatDate() {
			$scope.start = "";
			$scope.end = "";
			$scope.time = "";
			if ($scope.formRefer.startTime !== null && $scope.formRefer.startTime !== "" ){
				$scope.start = inform.format($scope.formRefer.startTime,'yyyy-MM-dd');
				//若开始时间不为空 对开始时间进行格式化
			}
			if ($scope.formRefer.endTime !== null && $scope.formRefer.endTime !== "" ){
				$scope.end = inform.format($scope.formRefer.endTime,'yyyy-MM-dd');
				//若结束时间不为空 对结束时间进行格式化
			} else {
				$scope.end = inform.format(new Date(),'yyyy-MM-dd');
				//若结束时间为空 默认为当前系统时间
			}
			$scope.time = inform.format(new Date(),'yyyy-MM-dd');
			//获取当前系统时间
			if ($scope.start <= $scope.end && $scope.start <= $scope.time && $scope.start !== "" && $scope.start !== null){
				//进行判断： 1.开始时间不得大于结束时间
				//		 2.开始时间不得大于当前系统时间
				//		 3.开始时间不得为空
				return false;
			}
			return true;
		}
		
		/**
		 * 获取项目
		 */
		function getData(pageNum) {
			//查询返回的项目列表
			$scope.projectList = [];

			//若符合条件
			var urlData = {
				'startTime':inform.format($scope.formRefer.startTime,'yyyy-MM-dd'),//格式化后的开始时间
				'endTime':inform.format($scope.formRefer.endTime,'yyyy-MM-dd'),//格式化后的结束时间
				'name':$scope.formRefer.projectName//项目名称
			};
			leakService.getData(urlData).then(function(data) {
	         	if (data.code === AgreeConstant.code) {
	         		var jsonData = data.data;
	         		var finalSum = jsonData.pop();
                     angular.forEach($scope.departmentList, function(one, i) {
                     	var list = []; 
                     	angular.forEach(jsonData, function(oneLine, i) {
                     		list.push(oneLine.product);
                     	});

                   		if (list.indexOf(one.orgName)===-1  && one.orgCode !== 'D010133' && one.orgCode !== 'D010131'){
                   			jsonData.push({
                   				 "product":one.orgName,
                   				 "bugCount":0,
                   				 "bugOnline":0,
                   				 "reopenNum":0,
                   				 "leakPercentage":0,
                                 "weightLeakPercentage":'0%',
                   				 "empCount":0,
                   				 "avgBug":'0%'
                   			 });
                   		 }
               	     });
                    jsonData.push(finalSum);
	         		$scope.projectList = jsonData;
	                if ($scope.projectList.length === 0) {
	                    inform.common(Trans("tip.noData"));
    	                $scope.pages = inform.initPages();
	                }
	                $scope.projectList.sort(function(a,b){
                        // order是规则  objs是需要排序的数组
                        var order = ["平台开发研究室", "产品开发一室", "产品开发二室", "产品开发三室", "测试研究室",
                            "项目管理办公室","汇总"];
                        return order.indexOf(a.product) - order.indexOf(b.product);
                    });
	                getLineData(urlData);
	 	        } else {
	 	            inform.common(data.message);
	 	        }
	        }, function(error) {
	           inform.common(Trans("tip.requestError"));
	        });
		}
		function getLineData(urlData){
			leakService.getLineData(urlData).then(function(data) {
	         	if (data.code === AgreeConstant.code) {
	         		var jsonData = data.data;
	         		var finalSum = jsonData.pop();
                     angular.forEach($scope.productLineList, function(one, i) {
                     	var list = []; 
                     	angular.forEach(jsonData, function(oneLine, i) {
                     		list.push(oneLine.product);
                     	});

                   		if (list.indexOf(one.param_value)===-1 ){
                   			jsonData.push({
                   				 "product":one.param_value,
                   				 "bugCount":0,
                   				 "bugOnline":0,
                   				 "reopenNum":0,
                   				 "leakPercentage":0,
                                 "weightLeakPercentage":'0%',
                   				 "empCount":0,
                   				 "avgBug":'0%'
                   			 });
                   		 }
               	     });
                    jsonData.push(finalSum);
	         		$scope.lineList = jsonData;
	                if ($scope.lineList.length === 0) {
	                    inform.common(Trans("tip.noData"));
    	                $scope.pages = inform.initPages();
	                }
	         	 } else {
		 	            inform.common(data.message);
		 	        }
		        }, function(error) {
		           inform.common(Trans("tip.requestError"));
		        });
		}
		/**
		 * 生成Excel
		 */
		$scope.toExcel = function(flag) {
			//格式化日期并进行判断
			if (formatDate()){
				//若不符合条件
				inform.common(Trans("请选择正确的创建时间段，开始时间不得为空！"));
				return;
			}
			//若符合条件
			var modalInstance = $modal.open({
				templateUrl: 'myModalContent.html',
				controller: 'ModalInstanceCtrl',
				size: "sm",
				resolve: {
					items: function() {
						return "确定要下载吗！";
					}
				}
			});
			//存查询条件
			var urlData = {
				'startTime':$scope.start,//格式化后的开始时间
				'endTime':$scope.end,//格式化后的结束时间
				'name':$scope.formRefer.projectName,//项目名称
				'excelName':flag//判断条件
			};
			modalInstance.result.then(function() {
				//开启遮罩层
				inform.showLayer("下载中。。。。。。");
				$http.post(
					$rootScope.getWaySystemApi+'bug/createExcel',
					urlData,
					{headers: {
						'Content-Type': 'application/json',
						'Authorization':'Bearer ' + LocalCache.getSession("token")||''
						},
					responseType: 'arraybuffer'//防止中文乱码
					}
				).success(function(data){
					//如果是IE浏览器
					if (window.navigator && window.navigator.msSaveOrOpenBlob) {
						var csvData = new Blob([data], {type:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
						if (flag === "leak"){
							window.navigator.msSaveOrOpenBlob(csvData,"08 缺陷逃逸情况报表.xlsx");
						} else {
							window.navigator.msSaveOrOpenBlob(csvData,"09 人均bug数报表.xlsx");
						}
					}
					//google或者火狐浏览器
					else{
						var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
						var objectUrl = URL.createObjectURL(blob);
						var aForExcel;
						if (flag === "leak"){
							aForExcel = $("<a download='08 缺陷逃逸情况报表.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href",objectUrl);
						} else {
							aForExcel = $("<a download='09 人均bug数报表.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href",objectUrl); 
						}
						$("body").append(aForExcel);
						$(".forExcel").click();
						aForExcel.remove();
					}
					// 关闭遮罩层
					inform.closeLayer();
					inform.common("下载成功!");
				});
			});
	      };
	    /**
	     * *************************************************************
	     *              方法声明部分                                结束
	     * *************************************************************
	     */	
	      
		} ]);
})();
/*
 * @Author: ha<PERSON><PERSON><PERSON>
 * @Date:   2017-11-28 11:28:56
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-19 10:13:03
 */
(function() {
    'use strict';
    app.controller("userGroup_Change", ['$rootScope', '$scope', '$state', 'inform', '$stateParams', '$modal', '$log', 'Trans', 'SystemService', 'AgreeConstant',
        function($rootScope, $scope, $state, inform, $stateParams, $modal, $log, Trans, SystemService, AgreeConstant) {
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.groupId = $stateParams.groupId;
            $scope.group = {}; // 获取用户组信息
            $scope.getData = getData;
            $scope.getData(); // 初始化函数
            $scope.map = { 'islock': AgreeConstant.unfreezeStatus };
            $scope.roleTypeData = {};
            getRoleTypeData(); // 获取所有角色类型
            $scope.role = {}; // 根据用户组ID获取角色
            $scope.addUserData = {}; // 根据用户组ID获取用户列表
            getUsers(); //根据groupID获取用户列表
            $scope.addGetUsers = {}; // 新增用户获取用户列表
            $scope.reset = reset; // 重置操作
            $scope.addGetUserList = addGetUserList; // 根据查询条件获取用户列表
            $scope.selected = []; // 存放选中的角色id
            $scope.updataType = updataType;

            $scope.checked = []; // 存放用户选中的id
            $scope.selectAll = selectAll; // 全选
            $scope.selectOne = selectOne; // 单选

            $scope.open = open; //删除操作
            $scope.addUserfun = addUserfun; // 添加用户操作
            $scope.onSubmit = onSubmit; // 保存按钮操作

            // 根据用户组ID获取用户组信息
            function getData() {
                console.log($scope.groupId);
                SystemService.getGroupById($scope.groupId)
                    .then(function(data) {
                        if (data.code === AgreeConstant.resultCode) {
                            $scope.group = data.result;
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 获取所有角色类型
            function getRoleTypeData() {
                SystemService.getRoleByLoginUserIdMap(JSON.stringify($scope.map))
                    .then(function(data) {
                        if (data.code === AgreeConstant.resultCode) {
                            var jsonData = data.result;
                            // $scope.resultData = jsonData;
                            $scope.roleTypeData = jsonData;
                            getRole();
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 根据用户组ID获取角色
            function getRole() {
                console.log($scope.groupId);
                SystemService.getRoleListByGroupId($scope.groupId)
                    .then(function(data) {
                        // console.log("根据用户组ID获取角色 = "+ JSON.stringify(data))
                        if (data.code === AgreeConstant.resultCode) {
                            $scope.role = data.result;
                            compareRoleId();
                            $scope.updataType();
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            //根据groupID获取用户列表
            function getUsers() {
                console.log($scope.groupId);
                SystemService.getUserListByGroupId($scope.groupId)
                    .then(function(data) {
                        if (data.code === AgreeConstant.resultCode) {
                            $scope.addUserData = data.result;
                            //do $scope.checked为弹窗列表选中项 目前为空
                            // $scope.checked = $scope.addUserData;
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 重置函数
            function reset() {
                $scope.map.employeeNo = "";
                $scope.map.realName = "";
                $scope.map.orgName = "";
            }

            // 根据查询条件获取用户列表
            function addGetUserList() {
                $scope.checked = [];
                $scope.select_all = false;
                SystemService.getUserListByMap(JSON.stringify($scope.map))
                    .then(function(data) {
                        if (data.code === AgreeConstant.resultCode) {
                            if (data.result.length === 0) {
                                inform.common(Trans("tip.noData"));
                            }
                            $scope.resultData = data.result;
                            removeHasUser();
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            //点击选择角色，进行选中或取消
            function updataType() {
                angular.forEach($scope.roleTypeData, function(i) {
                    var index = $scope.selected.indexOf(i);
                    if (i.selected && index === -1) {
                        $scope.selected.push(i);
                    } else if (!i.selected && index !== -1) {
                        $scope.selected.splice(index, 1);
                    }
                });
                $scope.group.roleName = $scope.selected;
                console.log($scope.group.roleName);
            }

            //比较两个RoleId是否相同
            function compareRoleId() {
                angular.forEach($scope.roleTypeData, function(obj, i) {
                    angular.forEach($scope.role, function(obj, j) {
                        if ($scope.roleTypeData[i].roleId === $scope.role[j].roleId) {
                            $scope.roleTypeData[i].selected = true;
                        }
                    });
                });
            }

            // 弹窗中的数据选择操作
            // 全选
            function selectAll() {
                if ($scope.select_all) {
                    $scope.checked = [];
                    angular.forEach($scope.resultData, function(i) {
                        $scope.checked.push(i);
                        i.checked = true;
                    });
                } else {
                    angular.forEach($scope.resultData, function(i) {
                        i.checked = false;
                    });
                    $scope.checked = [];
                }
            }

            // 单选
            function selectOne(i) {
                var index = $scope.checked.indexOf(i);
                if (index === -1 && i.checked) {
                    $scope.checked.push(i);

                } else if (index != -1 && !i.checked) {
                    $scope.checked.splice(index, 1);
                }
                if ($scope.resultData.length === $scope.checked.length) {
                    $scope.select_all = true;
                } else {
                    $scope.select_all = false;
                }
            }

            // 详情页用户List删除Item
            function open(item) {
                var index = $scope.addUserData.indexOf(item);
                if (index != -1) {
                    $scope.addUserData.splice(index, 1);
                }
            }

            //点击新增,用户选择列表移除详情页已有项
            function removeHasUser() {
                angular.forEach($scope.resultData, function(obj, i) {
                    angular.forEach($scope.addUserData, function(obj, j) {
                        if ($scope.resultData[i].userId === $scope.addUserData[j].userId) {
                            $scope.resultData.splice(i, 1);
                        }
                    });
                });
            }

            // 点击创建用户按钮，将选中的条目传到详情页
            function addUserfun() {
                if ($scope.checked.length !== 0) {
                    angular.forEach($scope.checked, function(obj, i) {
                        $scope.addUserData.push($scope.checked[i]);
                    });

                    $scope.checked = [];
                    $scope.reset();
                    $("#add_user").modal("hide");
                } else {
                    inform.common(Trans('common.chooseOneOpt'));
                }
            }

            // 保存按钮操作
            function onSubmit() {
                var userIds = '';
                angular.forEach($scope.addUserData, function(obj, i) {
                    if (i === $scope.addUserData.length - 1) {
                        userIds += $scope.addUserData[i].userId;
                    } else {
                        userIds += $scope.addUserData[i].userId + ',';
                    }
                });

                var roleIds = '';
                angular.forEach($scope.selected, function(obj, j) {
                    if (j === $scope.selected.length - 1) {
                        roleIds += $scope.selected[j].roleId;
                    } else {
                        roleIds += $scope.selected[j].roleId + ',';
                    }
                });

                SystemService.saveGroupAndGroupToRoleUser($scope.group, userIds, roleIds)
                    .then(function(data) {
                        console.log(data);
                        console.log($scope.group);
                        console.log(userIds);
                        console.log(roleIds);
                        if (data.code === AgreeConstant.resultCode) {
                            inform.common(Trans("tip.saveSuccess"));
                            $state.go("app.system.userGroup_Management");
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

        }
    ]);
})();
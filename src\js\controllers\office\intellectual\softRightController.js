(function () {
    app.controller("softRightController", ['intellectualService','comService','$rootScope', '$scope','$state','$stateParams','$modal','inform','Trans','AgreeConstant','LocalCache','$http',
        function (intellectualService,comService,$rootScope, $scope,$state,$stateParams, $modal,inform,Trans,AgreeConstant,LocalCache,$http) {
    	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */ 
		//页面数据
		$scope.formRefer = {};
		//是否从我的星星跳转标志
		$scope.formRefer.flag = '0';	
		$scope.limitList = AgreeConstant.limitList; // 正则校验配置
		$scope.formInput ={
				department:'',   //部门名称
				name:'',    	//软件名称
				startDate:'',	//申请起始时间
				endDate:'',		//申请结束时间
				line:''      //产品线名称
				
		};
		//获取缓存
		$scope.formRefer = LocalCache.getObject('softRightController_formRefer');
		if($scope.formRefer.flag === '1'){
			
			$scope.formInput.startDate = $scope.formRefer.startDate;
			$scope.formInput.endDate = $scope.formRefer.endDate;
			$scope.formInput.empName = $scope.formRefer.employeeName;
			console.log($scope.formRefer);
		}
		//清除缓存
		LocalCache.setObject('softRightController_formRefer', {}); 
		
    	$scope.formInsert = {
			 owner:null,
			 name:'',
			 way:'',
			 range:'',
			 firstPubDate:'',
			 authDate:'',
			 line:'',
			 department:'',
			 applyDate:''
    	};
			$scope.changeParam = {
				owner:null,
				name:'',
				way:'',
				range:'',
				firstPubDate:'',
				authDate:'',
				line:'',
				department:'',
				applyDate:''
			};
    	$scope.pages = {
				pageNum : '', 		// 分页页数
				size : '', 			// 分页每页大小
				total : '' 			// 数据总数
		};
    	$scope.pages = inform.initPages(); 	// 初始化分页数据
		$scope.getData = getData; 			// 分页相关函数
    	initPages();//初始化页面信息
		initInsertPerson();
		getData($scope.pages.pageNum);		//在刷新页面时调用该方法
	
	  	$scope.addInfo = addInfo;           // 新增一条信息
	  	$scope.updateInfo = updateInfo;     // 修改一条信息

		$scope.setDraggableTime = setDraggableTime;
		//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
	  	
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */	

	    //设置列表的高度
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 180);
 			$("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight - 70);
 		}

		//重置
		$scope.reset = function() {
			$scope.formInput.department='';
			$scope.formInput.name='';
			$scope.formInput.startDate = '';
			$scope.formInput.endDate = '';
			$scope.formInput.line='';
		}
			function initPerson() {
				// 发明人下拉列表
				$scope.employeeListUpCreation = [];
				$scope.employeeListChangeCreation = [];
				comService.getEmployeesByOrgId('','1').then(function (data) {
					if (data.data) {
						var _$scope$employeeListA;

						$scope.employeeListChangeCreation = data.data;
						// 数组拷贝
						_$scope$employeeListA = $scope.employeeListUpCreation;
						_$scope$employeeListA.push.apply(_$scope$employeeListA, _toConsumableArray($scope.employeeListChangeCreation));

						$scope.changeParam.owner = $scope.changeParam.owner == null ? [] : $scope.changeParam.owner.split(',');
						$scope.employeeListChangeCreation = changeListOrder($scope.changeParam.owner, $scope.employeeListChangeCreation);
						setDraggableTime();
					}
				});
			}

			function initInsertPerson() {
				// 发明人下拉列表
				$scope.employeeListUpCreation = [];
				$scope.employeeListChangeCreation = [];
				comService.getEmployeesByOrgId('','1').then(function (data) {
					if (data.data) {
						var _$scope$employeeListA;

						$scope.employeeListChangeCreation = data.data;
						// 数组拷贝
						_$scope$employeeListA = $scope.employeeListUpCreation;
						_$scope$employeeListA.push.apply(_$scope$employeeListA, _toConsumableArray($scope.employeeListChangeCreation));

						$scope.formInsert.owner = $scope.formInsert.owner == null ? [] : $scope.formInsert.owner.split(',');
						$scope.employeeListUpCreation = changeListOrder($scope.formInsert.owner, $scope.employeeListUpCreation);
						setDraggableTime();
					}
				});
			}

			/**
			 * 名单变化时延迟1秒修改li属性允许拖拽
			 */
			function setDraggableTime() {

				setTimeout(setDraggableOfli, 1000 * 1);
			}

			/**
			 * 修改li属性允许拖拽
			 */
			function setDraggableOfli() {

				var subNodes = document.querySelectorAll("ul.chosen-choices li.search-choice");

				for (var i = 0; i < subNodes.length; i++) {
					$(subNodes[i]).attr("draggable", true);
				}
			}
			/*
        * 让bigList按照 smallList 的顺序显示
        * */
			function changeListOrder(smallList, bigList) {
				/*
                * reverse() 数组倒叙
                * unshift() 插入元素至数组头部
                *
                * */
				smallList.reverse().forEach(function (copy) {
					// 在所有人员中取到抄送人
					var copyItem = bigList.find(function (item) {
						return item.loginName === copy;
					});
					// 在所有人员中删除抄送人
					bigList = bigList.filter(function (item) {
						return item.loginName !== copy;
					});
					// 把抄送人添加到数组头部
					bigList.unshift(copyItem);
				});
				return bigList;
			}
			// 数组复制
			function _toConsumableArray(arr) {
				if (Array.isArray(arr)) {
					for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) {
						arr2[i] = arr[i];
					}
					return arr2;
				} else {
					return Array.from(arr);
				}
			}


			function initPages() {

			//获取山东新北洋集团的下级部门信息
    		$scope.departmentList = [];
    		comService.getOrgChildren('D010053').then(function(data) {
    			$scope.departmentList = comService.getDepartment(data.data);
             });
    		
            $scope.lineList = [];
            comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
            	if(data.data) {
            		$scope.lineList = data.data;
            	}
            });
    		//获取产品线
    		$scope.productLineList = [];
    		comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.productLineList =  data.data;
        		}
            });
    	}

		
	     /**
         * 页面选中的修改信息复制
         */
     	$scope.update = function (m) {
     		$scope.changeParam = angular.copy(m);
			initPerson();
     		angular.forEach($scope.departmentList, function(der, i) {//遍历部门
            	if (der.orgName===$scope.changeParam.department){
            		$scope.changeParam.department = der.orgCode;
            	}
          	 });
     		angular.forEach($scope.lineList, function(line, i) {//遍历产品线
            	if (line.param_value===$scope.changeParam.line){
            		$scope.changeParam.line = line.param_code;
            	}
          	 });
     	}
     	//修改
     	function updateInfo(changeParam){
     		changeParam.firstPubDate = $("#firstPubDateUpdate").val();
     		changeParam.authDate = $("#authDateUpdate").val();
     		changeParam.applyDate = $("#applyDateUpdate").val();	
/*			if(!changeParam.owner){
				inform.common(Trans("著作权人不能为空！"));
				return false;
			} else*/ 
			if(!changeParam.name){
				inform.common(Trans("软件名称不能为空！"));
				return false;
			} 
/*				else if(!changeParam.way){
				inform.common(Trans("权利获得方式不能为空！"));
				return false;
			} else if(!changeParam.range){
				inform.common(Trans("范围不能为空！"));
				return false;
			} else if(!changeParam.firstPubDate){
				inform.common(Trans("首次发表日期不能为空！"));
				return false;
			}else if(!changeParam.authDate){
				inform.common(Trans("授权日期不能为空！"));
				return false;
			}*/
			else if(!changeParam.line){
				inform.common(Trans("产品线不能为空！"));
				return false;
			}else if(!changeParam.department){
				inform.common(Trans("部门不能为空！"));
				return false;
			}else if(!changeParam.applyDate){
				inform.common(Trans("申请日期不能为空！"));
				return false;
			}else {
				$scope.changeParam.owner = $scope.changeParam.owner.toString();
				var urlData = {
						 'id':changeParam.id,
						 'owner':$scope.changeParam.owner,
		    			 'name':changeParam.name,
		    			 'way':changeParam.way,
		    			 'range':changeParam.range,
		    			 'firstPubDate':inform.format(changeParam.firstPubDate,'yyyy-MM-dd'),
		    			 'authDate':inform.format(changeParam.authDate,'yyyy-MM-dd'),
		    			 'line':changeParam.line,
		    			 'department':changeParam.department,
		    			 'applyDate':inform.format(changeParam.applyDate,'yyyy-MM-dd')
				};
				// var modalInstance = $modal.open({
				// 	  templateUrl: 'myModalContent.html',
	   //              controller: 'ModalInstanceCtrl',
	   //              size: "sm",
	   //              resolve: {
	   //                items: function() {
	   //                return "确定要修改吗！";
	   //                }
	   //             }
				// });
		        // modalInstance.result.then(function() {
		        	intellectualService.updateSoftRight(urlData).then(function(data){
		        		if(data.code===AgreeConstant.code){
			        		inform.common(Trans("软著信息修改成功！"));
		        		}else{
		        			inform.common(Trans(data.message));
		        		}
		        		$("#edit_modal").modal('hide');
		        		getData($scope.pages.pageNum);
					},
					function(error) {
						inform.common(Trans("tip.requestError"));
					});	
			    // });
			}
			
		
     	}
     	
		
		//新增
		function addInfo(){
			$scope.formInsert.firstPubDate = $("#firstPubDateInsert").val();
			$scope.formInsert.authDate = $("#authDateInsert").val();
			$scope.formInsert.applyDate = $("#applyDateInsert").val();
		/*	if(!$scope.formInsert.owner){
				inform.common(Trans("著作权人不能为空！"));
				return false;
			} else */
			if(!$scope.formInsert.name){
				inform.common(Trans("软件名称不能为空！"));
				return false;
			} 
	/*			else if(!$scope.formInsert.way){
				inform.common(Trans("权利获得方式不能为空！"));
				return false;
			} else if(!$scope.formInsert.range){
				inform.common(Trans("范围不能为空！"));
				return false;
			} else if(!$scope.formInsert.firstPubDate){
				inform.common(Trans("首次发表日期不能为空！"));
				return false;
			}else if(!$scope.formInsert.authDate){
				inform.common(Trans("授权日期不能为空！"));
				return false;
			}*/
			else if(!$scope.formInsert.line){
				inform.common(Trans("产品线不能为空！"));
				return false;
			}else if(!$scope.formInsert.department){
				inform.common(Trans("部门不能为空！"));
				return false;
			}else if(!$scope.formInsert.applyDate){
				inform.common(Trans("申请日期不能为空！"));
				return false;
			}else {
				$scope.formInsert.owner = $scope.formInsert.owner.toString();
				var urlData = {
		    			 'applyId':$scope.formInsert.applyId,
		    			 'owner':$scope.formInsert.owner,
		    			 'name':$scope.formInsert.name,
		    			 'way':$scope.formInsert.way,
		    			 'range':$scope.formInsert.range,
		    			 'firstPubDate':inform.format($scope.formInsert.firstPubDate,'yyyy-MM-dd'),
		    			 'authDate':inform.format($scope.formInsert.authDate,'yyyy-MM-dd'),
		    			 'line':$scope.formInsert.line,
		    			 'department':$scope.formInsert.department,
		    			 'applyDate':inform.format($scope.formInsert.applyDate,'yyyy-MM-dd')
		    			 
				};
				
				// var modalInstance = $modal.open({
				// 	templateUrl: 'myModalContent.html',
	   //              controller: 'ModalInstanceCtrl',
	   //              size: "sm",
	   //              resolve: {
	   //                items: function() {
	   //                return "确定要添加吗！";
	   //                }
	   //             }
				// });
		        // modalInstance.result.then(function() {
		        	intellectualService.insertSoftRight(urlData).then(function(data){
		        		if(data.code===AgreeConstant.code){
			        		inform.common(Trans("软著信息添加成功！"));
			        		$scope.formInsert={};
		        		}else{
		        			inform.common(Trans(data.message));
		        		}
		        		$("#add_modal").modal('hide');
		        		getData($scope.pages.pageNum);
		        		
					},
				function(error) {
					inform.common(Trans("tip.requestError"));
				});	
	        // });

		}
		}
		
		//获取所有数据以分页的形式
		function getData(pageNum){
		   	$scope.itemList = [];
        	var urlData ={
					'department':$scope.formInput.department,  			
					'name':$scope.formInput.name,
					'startDate':inform.format($scope.formInput.startDate,'yyyy-MM-dd'),
					'endDate':inform.format($scope.formInput.endDate,'yyyy-MM-dd'),
					'line':$scope.formInput.line,//产品线名称
					'currentPage' : pageNum, 								// 分页页数
					'pageSize' : $scope.pages.size    						// 分页每页大小
        	};
        	intellectualService.getSoftRightByMap(urlData).then(function(data){
				if(data.code===AgreeConstant.code){
					var jsonData = data.data;
					$scope.itemList = jsonData.list;
					if ($scope.itemList.length===0) {
							$scope.pages = inform.initPages(); 			//初始化分页数据
							inform.common(Trans("tip.noData"));
					} else {
						// 分页信息设置
						$scope.pages.total = jsonData.total;		// 页面总数
						$scope.pages.star = jsonData.startRow;  	//页面起始数
						$scope.pages.end = jsonData.endRow;  		//页面大小数
						$scope.pages.pageNum = jsonData.pageNum;  	//页面页数
					}
				}
        	},
        	function(error) {
				inform.common(Trans("tip.requestError"));
			});		

		}
	     // 删除弹框
	      $scope.open =function(m) {
	         var modalInstance = $modal.open({
	          templateUrl: 'myModalContent.html',
	          controller: 'ModalInstanceCtrl',
	          size: "sm",
	          resolve: {
	            items: function() {
	              return Trans("common.deleteTip");
	            }
	          }
	        });
	        modalInstance.result.then(function() {
	            if (m) {
	              $scope.delete(m);
	            }
	          });
	      };

	      //删除信息 
	     $scope.delete = function (m){
	        
	            var urlData = {
	                'id':m.id
	            };
	           intellectualService.deleteSoftRightById(urlData).then(function(data){
	             if(data.code===AgreeConstant.code) {
	                 inform.common(data.message);
	                 $scope.getData($scope.pages.pageNum);
	             } else{
	                 inform.common(data.message);
	             }
	         }, function(error) {
	             inform.common(Trans("tip.requestError"));
	         });
	     }
		 //页面跳转到我的星星--我的星星明细部门贡献详情页
		 $scope.getBack = function(){
			$state.go('app.personal_star_department_detail');
		}
	     /**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */	
	}]);
})();
(function() {
    'use strict';
    app.factory('personalBoardService', personalBoardService);
    personalBoardService.$inject = [ "HttpService", '$rootScope' ];

    function personalBoardService(HttpService, $rootScope) {
        var service = {
            getPersonBoardList:getPersonBoardList
        };
        return service;

        //获取个人看板列表展示
        function getPersonBoardList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'personalDataBoard/getPersonBoardList', urlData);
        }
    }
})();

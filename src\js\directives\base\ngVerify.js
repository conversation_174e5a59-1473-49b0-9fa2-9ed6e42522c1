/*
* @Author: fubaole
* @Date:   2017-12-21 12:46:06
* @Last Modified by:   fubaole
* @Last Modified time: 2017-12-29 11:14:10
*/
(function () {
    'use strict';
    angular.module('app')
        .directive('ngVerify', ['$timeout', function ($timeout) {
            return {
                restrict: 'A',
                ngMaxlength:'@',
                ngPattern:'@',
                tipTxt:'@',
                required:'@',
                link: function (scope, ele, att) {
                  var successIcon = $("<span class='glyphicon glyphicon-ok form-control-feedback text-success'></span>");
                  var errorTip = $("<span class='form-vertical'></span>");
                  var requiredIcon = $("<span class='required-icon'>*</span>");
                  // 必填
                  if(att.required){
                    ele.parent().append(requiredIcon);
                  }
                  // 先判断ele有值没，没值新增事件触发，有值修改需走验证；
                  var timer = $timeout(function(){
                    if(ele.val().length!=0){
                      validFun();
                    }
                  },500);
                  // 错误
                  function  showTip(){
                    ele.parent().append(errorTip.html(att.tipTxt));
                    ele.parent().children('.form-control-feedback').remove();
                  }
                  //正确
                  function showIcon(){
                    ele.parent().append(successIcon);
                    ele.parent().children('.form-vertical').remove();
                  }
                  // 全部隐藏
                  function hideTipAndIcon(){
                   ele.parent().children('.form-control-feedback').remove();
                   ele.parent().children('.form-vertical').remove();
                  }
                  // 比较
                  function validFun(){
                    // 长度校验
                    if(att.ngMaxlength){
                      if((ele.val().length>att.ngMaxlength) && (ele.val().length!=0)){
                        showTip();
                      }else{
                        showIcon();
                      }
                    }
                    // 正则校验
                    if(att.ngPattern){
                       if((att.ngPattern.test(ele.val())) && (ele.val().length!=0)){
                           showIcon();
                        }else{
                          showTip();
                        }
                    }
                    if(ele.val().length==0){
                      hideTipAndIcon();
                      ele.val("");
                    }
                  }

                  // 事件
                  ele.bind("keyup keydown blur", function () {
                    validFun();
                  });

                }
            };

        }]);
})();

(function () {
    'use strict';
    app.factory('attendanceDetailService', attendanceDetailService);
    attendanceDetailService.$inject = ["HttpService", '$rootScope'];

    function attendanceDetailService(HttpService, $rootScope) {
        var service = {

           getAttendanceDetail:getAttendanceDetail,
           getGatherWorkOverTime:getGatherWorkOverTime,
           getWorkOverTimeGroupByPerson:getWorkOverTimeGroupByPerson

        };
        return service;

        /**
         * 根据评审查找关联的评审问题
         * @param urlData 查询参数
         */
        function getAttendanceDetail(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'attendanceDetail/getAttendanceDetail', urlData);
        }

        function getGatherWorkOverTime(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'attendanceDetail/getGatherWorkOverTime', urlData);
        }

        function getWorkOverTimeGroupByPerson(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'attendanceDetail/getWorkOverTimeGroupByPerson', urlData);
        }
    }
})();
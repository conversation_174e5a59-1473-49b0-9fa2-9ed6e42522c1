
(function() {
    'use strict';
  app.factory('reworkService', reworkService);
  reworkService.$inject=["HttpService",'$rootScope'];

  function reworkService(HttpService,$rootScope){
    
    var service={
    	selectAllLine:selectAllLine,
    	selectLine:selectLine,
    	toExcel:toExcel,
    	createExcelDetails:createExcelDetails
    };
    return service;
    /**
     * 查询所有产品线 及其对应下的所有工时
     * @param urlData projectname	// 项目名称
					  startTime	//开始时间
					  endTime	//结束时间
     */
    function selectAllLine(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'rework/selectAllLine', urlData);
    }
    /**
     * 查询某个产品线下的所有项目所有工时
     */
    function selectLine(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'rework/selectLine', urlData);
    }
    /**
     * 将前台表格内容进行下载
     *  @param urlData projectname	// 项目名称
					  startTime	//开始时间
					  endTime	//结束时间
     */
    function toExcel(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'rework/toExcel', urlData);
    }
    /**
     * 将前台表格内容进行下载(明细)
     *  @param urlData projectname	// 项目名称
					  startTime	//开始时间
					  endTime	//结束时间
     */
    function createExcelDetails(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'rework/createExcelDetails', urlData);
    }
    
  }
})();

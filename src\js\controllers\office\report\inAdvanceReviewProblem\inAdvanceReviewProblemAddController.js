(function () {
    app.controller("inAdvanceReviewProblemAddController", ['reviewProblemService', 'comService', '$rootScope', '$scope', '$stateParams', '$modal', 'inform', 'LocalCache', 'Trans', 'AgreeConstant', '$http', '$state',
        function (reviewProblemService, comService, $rootScope, $scope, $stateParams, $modal, inform, LocalCache, Trans, AgreeConstant, $http, $state) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.limit = AgreeConstant.limitList;
            //初始化问题级别下拉框
            $scope.problemLevel = problemLevelConfig;
            $scope.problemTypeArray = problemTypeConfig[problemProcessConfig[$stateParams.reviewTheme]];

            $scope.acceptLevel = [{
                label:"是",
                value:"0"
            },{
                label:"否",
                value:"1"
            }];
            //问题列表
            $scope.problemList = [];
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            //页面初始化，初始化评委下拉列表、责任人下拉列表（系研所有人员）,将传入的JSON串转化为要绑定到H5页面的变量
            $scope.initParams = function () {
                $scope.employeeList = [];
                comService.getEmployeesName().then(function (data) {
                    $scope.employeeList = angular.fromJson(data.data);
                    //设置要显示评审内容
                    $scope.reviewContent = $stateParams.reviewContent;
                });
            };
             /**
             *问题分类变动修改问题级别
             */
            $scope.changeProblemType = function (indexVal) {
                $scope.problemList[indexVal].level = problemTypeLevelConfig[$scope.problemList[indexVal].problemType];
            };
            /**
             * 上传前,给每一个problem设置提出人（评委）、评委等级,
              *设置评委的员工编号
             * 如果是邮件评审,设置评委投入工作量
             * 会议或默认则不设置
             * 对时间要求格式化
             */
            function setJudgeInfoAndFormatDeadLine() {
                for (var i = 0; i < $scope.problemList.length; i++) {
                    $scope.problemList[i].judge = $scope.problem.judge;
                    $scope.problemList[i].employeeId = $scope.problem.employeeId;
                    if ($scope.problemList[i].deadLine) {
                        //对完成时间要求进行格式化
                        $scope.problemList[i].deadLine =
                            inform.format($scope.problemList[i].deadLine, 'yyyy/MM/dd');
                    }
                }
            }
            /**
             * 根据关键角色名称，遍历以获取其编号
             * @param problem
             */
            $scope.setJudgeNo = function(problem){
                for(var i = 0;i <$scope.employeeList.length;i++){
                    if($scope.employeeList[i].realName === problem.judge){
                        problem.employeeId = $scope.employeeList[i].employeeNo;
                    }
                }
            };

            //新增评审问题信息
            $scope.addProblem = function () {
                //校验问题列表,若为空,则不提交,给提示
                if($scope.problemList.length <= 0){
                    inform.common("请至少输入一条问题！");
                    return;
                }
                //给问题设置评委信息并格式化时间
                setJudgeInfoAndFormatDeadLine();
                //批量插入问题
                reviewProblemService.insertProblem($scope.problemList).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        //含确定按钮的弹框,点击确定跳转至评审问题列表页
                        layer.confirm(data.message, {
                            title: false,
                            btn: ['确定']
                        }, function (result) {
                            layer.close(result);
                            $scope.back();
                        });

                    } else {
                        inform.common(data.message);
                    }
                },
                function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            };

            //返回评审问题列表页面
            $scope.back = function () {
                $state.go('app.office.inAdvanceReviewProblem', {
                    id: $stateParams.reviewId,
                    type: $stateParams.reviewType,
                    reviewContent: $stateParams.reviewContent,
                    reviewTheme: $stateParams.reviewTheme
                });
            };

            /**
             * 新增一条问题信息
             */
            $scope.addAProblem = function () {
                //问题信息
                var problem = {
                    'reviewId':$stateParams.reviewId,
                    'reviewType': $stateParams.reviewType,
                    'reviewAccept':'0'
                };
                $scope.problemList.push(problem);
            };
            /**
             *  删除一条问题
             * @param index 列表下标
             */
            $scope.deleteProblem = function (index) {
                inform.modalInstance("确定要删除该问题吗? ").result.then(function () {
                    if (index >= 0) {
                        $scope.problemList.splice(index, 1);
                    }
                });
            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法初始化调用部分                           开始
             * *************************************************************
             */
            //初始化页面
            $scope.initParams();
            /**
             * *************************************************************
             *              方法初始化调用部分                           结束
             * *************************************************************
             */
        }]);
})();
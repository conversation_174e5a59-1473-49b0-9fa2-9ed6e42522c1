(function() {
    'use strict';
    app.controller("staffCertificateAddController", ['comService','$rootScope', '$stateParams', '$scope', 'staffCertificateService','$modal','inform', 'Trans', 'AgreeConstant','$state',
        function(comService, $rootScope,$stateParams,$scope, staffCertificateService,$modal,inform, Trans, AgreeConstant,$state) {
         /**
         * *************************************************************
         *             初始化部分                                 开始
         * *************************************************************
         */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            $scope.datepicker = {
               currentDate :  new Date()
            };
            $scope.addParam = {};
            //设置列表的高度
            setDivHeight();
            getDeptList();//获取部门列表
            getAreaList();//地区
            getStaffList();//员工编号和姓名列表
            getCertificateTypeList();//获取证书列表
            var fileTmp = "";
            var formData = {};

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */


            //获取系研二级部门信息
            function getDeptList() {
            	//获取山东新北洋集团的下级部门信息
            	$scope.deptList = [];
            	comService.getOrgChildren('D010053').then(function(data) {
        			$scope.deptList = comService.getDepartment(data.data);
                 });
            }

            //获取地区
            function getAreaList(){
                $scope.areaList = [];
                comService.getAreaList().then(function(data) {
                    $scope.areaList = data.data;
                 });
            }

            /**
            ** 员工编号和姓名列表
            */
            function getStaffList(){
                comService.getStaffList().then(function(data) {
                  $scope.staffList = data.data;
                });
            }

            /**
             * 设置列表的高度
             */
            function setDivHeight(){
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (170 + 65);
                var ss=document.getElementById('addFormId');
                ss.style.height=divHeight + 'px';
                var clientWidth = document.body.clientWidth;
                $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
            }
           //返回按钮
           $scope.goBack = function (){
                $state.go('app.office.staffCertificateController');
           };

            $scope.updatePic = function() {
                 $('#myFile').trigger('click');
            };

           //选择图片
           $scope.changePic = function() {
              $("#prompt3").css("display", "none");
              var f = document.getElementById("myFile").files[0];
              formData = new FormData($('#formTmp')[0]);
              var index_int = f.name.lastIndexOf(".");
              var lastArr = f.name.substring(index_int);
              if(lastArr!==".bmp"&&lastArr!==".png"&&lastArr!==".gif"&&lastArr!==".jpg"&&lastArr!==".jpeg"){
                  event.target.value="";
                  layer.confirm("只支持bmp、png、gif、jpg、jpeg格式上传。",{
                      title:false,
                      btn:['确定']
                  },function(result){

                    if(""=== fileTmp){
                        $("#prompt3").css("display", "block");
                    }
                    layer.close(result);
                  });
              }else{
                  //上传文件大小不能超过4M (1024*1024*4 = 4194304)
                  if(f.size > 4194304){
                    inform.common("上传文件大小不能超过4M");
                    event.target.value="";
                    if(""=== fileTmp){
                        $("#prompt3").css("display", "block");
                    }
                    return;
                  }
                  $("#prompt3").css("display", "none");
                  if(f.size>0){
                       fileTmp = f;
                       var reads = new FileReader();
                       reads.readAsDataURL(f);
                       reads.onload = function(e) {
                       document.getElementById('img_picture_view').src = this.result;
                       $("#img_picture_view").css("display", "block");
                       $("#img_picture_del").css("display", "inline-block");
                       $("#img_picture_view").css("height", "398px");
                       $("#img_picture_view").css("width", "798px");
                       };

                  }
              }
            };




            //上传图片
            function uploadPic(id){
                 formData.append('file', fileTmp);
                 formData.append('id', id);
                 inform.uploadFile('picture/updatePhoto',formData,function func(result){});
            }

            //获取证书类别
            function getCertificateTypeList(){
                $scope.certificateTypeList = [];
                comService.getCertificateTypeList().then(function(data) {
                    $scope.certificateTypeList = data.data;
                 });
            }


            //删除预览的图片
            $scope.img_del = function() {
                  document.getElementById('img_picture_view').src = "";
                  $("#img_picture_del").css("display", "none");
                  $("#prompt3").css("display", "block");
                  $("#img_picture_view").css("display", "none");
                  var obj = document.getElementById('myFile');
                  obj.outerHTML = obj.outerHTML;
                  fileTmp = "";

            };


            //保存新增数据
            $scope.saveAddData = function () {
               if($scope.addParam.getCertificateTime==='' ||$scope.addParam.getCertificateTime==null||$scope.addParam.getCertificateTime=== undefined ){
                  $scope.addParam.getCertificateTime = "";
               }else{
                  $scope.addParam.getCertificateTime = inform.format($scope.addParam.getCertificateTime,'yyyy-MM-dd');
               }
               if($scope.addParam.certificateValidity==='' ||$scope.addParam.certificateValidity==null||$scope.addParam.certificateValidity=== undefined ){
                  $scope.addParam.certificateValidity = "";
               }else{
                  $scope.addParam.certificateValidity = inform.format($scope.addParam.certificateValidity,'yyyy-MM-dd');
               }
                var param = {
                    "employeeId":$scope.addParam.employeeId,
                    "certificate":$scope.addParam.certificate,
                    "getCertificateTime":$scope.addParam.getCertificateTime,
                    "certificateType":$scope.addParam.certificateType,
                    "certificateValidity":$scope.addParam.certificateValidity
                };
                staffCertificateService.addByParam(param).then(function (result) {
                    if (result.code==='0000') {
                       if(fileTmp.size > 0){
                           //上传图片(新增成功，返回id)
                           uploadPic(result.data);
                       }
                       // 关闭遮罩层
                       inform.closeLayer();
                       layer.confirm(result.message,{
                          title:false,
                          btn:['确定']
                       },function(result){
                          layer.close(result);
                          $scope.addParam = {};
                          $scope.goBack();
                       });

                   }else{
                       inform.common(result.message);
                   }
                });
            }


             //获得证书时间
             $scope.openCertificateTime = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openCertificateTime1 = true;
                $scope.openCertificateTime2 = false;
             };
             //获得证书有效期时间
             $scope.openCertificateValidityTime = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openCertificateTime1 = false;
                $scope.openCertificateTime2 = true;
             };
      /**
              * *************************************************************
              *              方法声明部分                                结束
              * *************************************************************
              */

}
  ]);
})();
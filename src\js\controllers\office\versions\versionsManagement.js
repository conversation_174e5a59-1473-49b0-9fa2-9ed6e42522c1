(function () {
    app.controller("versionsManagement", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','versionsService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,versionsService,inform,Trans,AgreeConstant,LocalCache,$http) {
       	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		//分页
    	$scope.pages = inform.initPages(); // 初始化分页数据

		//设置列表的高度
        setDivHeight();
        //窗体大小变化时重新计算高度
        $(window).resize(setDivHeight);

        //获取缓存
        $scope.formRefer = LocalCache.getObject('versions_formRefer');
        //对原缓存进行覆盖
        LocalCache.setObject("versions_formRefer",{});

        //初始化
        initProductLines();
        //是否下拉框数据源
        $scope.points = [{
            value: '0',
            label: '是'
        }, {
            value: '1',
            label: '否'
        }];

        $scope.getData = getData; 			// 分页相关函数
        getData();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
        /**
         * 查询条件中的开始时间
         */
        $scope.openDateStart = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            //仅查询中的开始时间显示控件内容
            $scope.openedStart = true;
            $scope.openedEnd = false;
        };
        /**
         * 查询条件中的结束时间
         */
        $scope.openDateEnd = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = false;
            $scope.openedEnd = true;
        };
        /**
         * 设置列表的高度
         */
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (150 + 185);
            $("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight - 50);
        }
        /**
		 * 跳转修改
		 */ 
		$scope.go = function(item) {
			if(null != item){
				LocalCache.setObject('versions_formRefer',$scope.formRefer);
				$state.go("app.office.versionsAddManagement",{versionId:item.id});
			} else {
				LocalCache.setObject('versions_formRefer',$scope.formRefer);
				$state.go("app.office.versionsAddManagement",{versionId:null});
			}
		};
		
		/**
		 * 初始化
		 */
    	function initProductLines() {
    		//获取产品线
    		$scope.productLines = [];
    		comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.productLines =  data.data;
        		}
            });
    	}
    	/**
         * 获取所有项目的版本信息
         */
        function getData(pageNum) {
            var urlData = {
            	'productLine': $scope.formRefer.productLine,//产品线
                'projectId': $scope.formRefer.projectId,//项目名
                'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间
                'isBaseLine': $scope.formRefer.isBaseLine,//是否发布基线
                'isPlm': $scope.formRefer.isPlm, //是否PLM发布
                'page': pageNum,//当前页数
                'size': $scope.pages.size//每页显示条数
            };
            versionsService.selectData(urlData).then(function (data) {
            		//项目详情
                	$scope.tableData = data.data.list;
                	if ($scope.tableData.length === 0) {
                		inform.common(Trans("tip.noData"));
                		$scope.pages = inform.initPages();
                	} else {
                		//分页信息设置
                		$scope.pages.total = data.data.total;
                		$scope.pages.star = data.data.startRow;
                		$scope.pages.end = data.data.endRow;
                		$scope.pages.pageNum = data.data.pageNum;
                	}
                },
                function () {
                    inform.common(Trans("tip.requestError"));
                });
        }
        /**
         * 删除弹框
         */
        $scope.open = function (m) {
            var modalInstance = $modal.open({
                templateUrl: 'myModalContent.html',
                controller: 'ModalInstanceCtrl',
                size: "sm",
                resolve: {
                    items: function () {
                        return Trans("common.deleteTip");
                    }
                }
            });
            modalInstance.result.then(function () {
                if (m) {
                    $scope.delete(m);
                }
            });
        };

        /**
         * 删除信息
         */
        $scope.delete = function (m) {
            var urlData = {
                'id': m.id
            };
            versionsService.delData(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    layer.confirm(data.message, {
                        title: false,
                        btn: ['确定']
                    }, function (result) {
                        layer.close(result);
                        getData(1);
                    });
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        };
        /**
		 * excel下载
		 */
		$scope.toExcel = function() {
			var modalInstance = $modal.open({
				templateUrl: 'myModalContent.html',
				controller: 'ModalInstanceCtrl',
				size: "sm",
				resolve: {
					items: function() {
						return "确定要下载吗！";
					}
				}
			});
			modalInstance.result.then(function() {
				//拼装下载内容
				var urlData = {
                    'productLine': $scope.formRefer.productLine,//产品线
                    'projectId': $scope.formRefer.projectId,//项目名
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间
                    'isBaseLine': $scope.formRefer.isBaseLine,//是否发布基线
                    'isPlm': $scope.formRefer.isPlm //是否PLM发布
                };
				inform.downLoadFile ('versions/downloadExcel',urlData,'项目版本档案管理表.xlsx');

			});
		}
	 		/**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */	
		
	}]);
})();
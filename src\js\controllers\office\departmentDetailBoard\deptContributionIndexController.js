(function () {
    app.controller("deptContributionIndexController", ['LocalCache', 'inform', 'Trans', '$ocLazyLoad', '$rootScope','comService', '$scope', '$stateParams', '$state', 'deptBoardFactory', 'deptContributionService', 'AgreeConstant',
        function (LocalCache, inform, Trans, $ocLazyLoad, $rootScope,comService,$scope, $stateParams, $state, deptBoardFactory, deptContributionService, AgreeConstant) {
            // 初始化
            deptBoardFactory.init($scope, '1');
            // 重置部分
            $scope.resetParam = resetParam;
            function resetParam(){
                deptBoardFactory.initTime($scope, '本年度');
            }

            $scope.getData = getData;
            function getData() {
                getStatisticsData();
                getTrainOutputData();
                getTrainOutputByPersonData();
                getKnowledgeData();
                getKnowledgeByPersonData();
            }

            // 图表部分
            $scope.currentTrainOutputChart = null;
            $scope.currentTrainOutputByPersonChart = null;
            $scope.currentKnowledgeChart = null;
            $scope.currentKnowledgeByPersonChart = null;
            window.addEventListener("resize", chartResize);
            $scope.$on("$destroy", function() {
                window.removeEventListener('resize', chartResize);
            })
            function chartResize() {
                if ($scope.currentTrainOutputChart) { $scope.currentTrainOutputChart.resize(); }
                if ($scope.currentTrainOutputByPersonChart) { $scope.currentTrainOutputByPersonChart.resize(); }
                if ($scope.currentKnowledgeChart) { $scope.currentKnowledgeChart.resize(); }
                if ($scope.currentKnowledgeByPersonChart) { $scope.currentKnowledgeByPersonChart.resize(); }
            }
            function getTrainOutputData (){
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                deptBoardFactory.chartHideClear($scope.currentTrainOutputChart);
                deptBoardFactory.chartShowLoading($scope.currentTrainOutputChart);
                deptContributionService.getTrainByTeam(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.trainOutputChartInfo = result.data;
                        deptBoardFactory.chartHideLoading($scope.currentTrainOutputChart);
                        deptBoardFactory.showPie($scope.currentTrainOutputChart,$scope.trainOutputChartInfo, {
                            title: '培训输出(按小组统计)',
                            type: 'name',
                            value: 'value',
                            firstContent: '输出培训',
                            needSecondContent: false
                        })
                    } else {
                        inform.common(result.message);
                        deptBoardFactory.chartHideLoading($scope.currentTrainOutputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    deptBoardFactory.chartHideLoading($scope.currentTrainOutputChart);
                });
            }
            function getTrainOutputByPersonData (){
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                deptBoardFactory.chartHideClear($scope.currentTrainOutputByPersonChart);
                deptBoardFactory.chartShowLoading($scope.currentTrainOutputByPersonChart);
                deptContributionService.getTrainByStaff(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.trainOutputByPersonChartInfo = result.data;
                        deptBoardFactory.chartHideLoading($scope.currentTrainOutputByPersonChart);
                        deptBoardFactory.showBar($scope.currentTrainOutputByPersonChart,$scope.trainOutputByPersonChartInfo.slice(0, 10),{
                            title: '',
                            xType: 'name',
                            yType: 'value',
                            unit: '次',
                            grid: {
                                gridLeft: '2%',
                                gridRight: '2%',
                            },
                            needCustomSeries: true,
                            minInterval: 1
                        });
                    } else {
                        inform.common(result.message);
                        deptBoardFactory.chartHideLoading($scope.currentTrainOutputByPersonChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    deptBoardFactory.chartHideLoading($scope.currentTrainOutputByPersonChart);
                });
            }
            function getKnowledgeData (){
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                deptBoardFactory.chartHideClear($scope.currentKnowledgeChart);
                deptBoardFactory.chartShowLoading($scope.currentKnowledgeChart);
                deptContributionService.getKnowledgeByTeam(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.knowledgeChartInfo = result.data;
                        deptBoardFactory.showPie($scope.currentKnowledgeChart,$scope.knowledgeChartInfo, {
                            title: '知识库(按小组统计)',
                            type: 'name',
                            value: 'value',
                            firstContent: '输出知识库',
                            firstContentUnit: '条',
                            needSecondContent: false
                        })
                        deptBoardFactory.chartHideLoading($scope.currentKnowledgeChart);
                    } else {
                        inform.common(result.message);
                        deptBoardFactory.chartHideLoading($scope.currentKnowledgeChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    deptBoardFactory.chartHideLoading($scope.currentKnowledgeChart);
                });
            }
            function getKnowledgeByPersonData (){
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                deptBoardFactory.chartHideClear($scope.currentKnowledgeByPersonChart);
                deptBoardFactory.chartShowLoading($scope.currentKnowledgeByPersonChart);
                deptContributionService.getKnowledgeByStaff(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.knowledgeByPersonChartInfo = result.data;
                        deptBoardFactory.chartHideLoading($scope.currentKnowledgeByPersonChart);
                        deptBoardFactory.showBar($scope.currentKnowledgeByPersonChart,$scope.knowledgeByPersonChartInfo.slice(0, 10),{
                            title: '',
                            xType: 'name',
                            yType: 'value',
                            unit: '条',
                            grid: {
                                gridLeft: '2%',
                                gridRight: '2%',
                            },
                            needCustomSeries: true,
                            minInterval: 1
                        });
                    } else {
                        inform.common(result.message);
                        deptBoardFactory.chartHideLoading($scope.currentKnowledgeByPersonChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    deptBoardFactory.chartHideLoading($scope.currentKnowledgeByPersonChart);
                });
            }

            // 统计区域
            function getStatisticsData() {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                $scope.showStatisticsInfo = false;
                deptContributionService.getDeptContributionTotalInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.deptContributionStatisticsData = result.data;
                        $scope.showStatisticsInfo = true;
                    } else {
                        inform.common(result.message);
                        $scope.showStatisticsInfo = true;
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    $scope.showStatisticsInfo = true;
                });
            }

            $scope.trainManagement = function () {
                $state.go('app.office.trainPlanManagement');
            }
            $scope.toKnowledgeLook = function () {
                window.open($rootScope.wcpSystemApi+"websearch/PubHome.html");
            }
            $scope.toTrainOutput = function (title) {
                $scope.modalTitle = title;
                $scope.type = 'totalNum';
                $scope.sort = 'desc';
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                deptContributionService.getTrainingTableInfo(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.trainTableInfo = result.data;
                        $scope.sortData($scope.type, true, $scope.sort, $scope.trainTableInfo);
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            $scope.toKnowledgeOutput = function (title) {
                $scope.modalTitle = title;
                $scope.type = 'totalNum';
                $scope.sort = 'desc';
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                deptContributionService.getKnowledgeTableInfo(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.knowledgeTableInfo = result.data;
                        $scope.sortData($scope.type, true, $scope.sort, $scope.knowledgeTableInfo);
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            // 排序
            $scope.sortData = function (type, isNumber, sort,  dataList) {
                return deptBoardFactory.sortData($scope, type, isNumber, sort, dataList);
            }
            $scope.activeClass = function (type, sort){
                return deptBoardFactory.activeClass(type, sort, $scope.type, $scope.sort);
            }
            $scope.activeTitleClass = function (type) {
                return deptBoardFactory.activeTitleClass(type, $scope.type);
            }

            $scope.loadSuccess = function (){
                $ocLazyLoad.load(
                    [
                        'library/component/echarts.min.js'
                    ]).then(function (){
                    $scope.currentTrainOutputChart = echarts.init(document.getElementById('trainOutputChart'));
                    $scope.currentTrainOutputByPersonChart = echarts.init(document.getElementById('trainOutputByPersonChart'));
                    $scope.currentKnowledgeChart = echarts.init(document.getElementById('knowledgeChart'));
                    $scope.currentKnowledgeByPersonChart = echarts.init(document.getElementById('knowledgeByPersonChart'));
                    var localFormRefer = LocalCache.getObject('departmentList_formRefer');
                    if (Object.keys(localFormRefer).length > 0) {
                        $scope.formRefer = localFormRefer;
                        $scope.butFlag = localFormRefer.searchTimeString;
                    }
                    if ($stateParams.orgCode) {
                        $scope.formRefer.orgCode = $stateParams.orgCode;
                    }
                    getData();
                });
            }
        }]);
})();
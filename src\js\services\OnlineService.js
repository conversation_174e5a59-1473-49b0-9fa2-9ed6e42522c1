(function() {
	'use strict';
	app
	.factory('OnlineService', OnlineService);
	OnlineService.$inject=['HttpService','$rootScope'];
	function OnlineService(HttpService,$rootScope){
		var service={
            getCookie:getCookie,
            getALlInfo:getALlInfo,
            getOnlineInfo:getOnlineInfo,
            getOnlineInfoBySignCode: getOnlineInfoBySignCode,
            updateOnlineInfo:updateOnlineInfo,
            verification:verification,
            getClientType:getClientType,
            getApplyUser:getApplyUser,
            authStatistic:authStatistic,
            verifyStatistic:verifyStatistic,
            authStatisticAll:authStatisticAll
		};
		return service;
        function getCookie(name) 
            { 
                var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
             
                if(arr=document.cookie.match(reg))
             
                    return unescape(arr[2]); 
                else 
                    return null; 
            } 
        function getALlInfo(isAuthorized) 
            { 
                return HttpService.post($rootScope.gateInfoApi+'lms/lc/selectAll?isAuthorized='+isAuthorized+'&name='+getCookie('name'),{});
            }
        function getOnlineInfo(startTime,endTime,authorizeStatus,machineCode,clientTypeCode,applyUser,isAuthorized) 
            { 
                var param='?startTime='+startTime+'&endTime='+endTime+'&authorizeStatus='+authorizeStatus+'&machineCode='+machineCode
                +'&clientTypeCode='+clientTypeCode+'&applyUser='+applyUser+'&isAuthorized='+isAuthorized+'&name='+getCookie('name');
                return HttpService.post($rootScope.gateInfoApi+'lms/lc/get'+param,{});
            }
        function getOnlineInfoBySignCode(signCode) 
            { 
                return HttpService.post($rootScope.gateInfoApi+'lms/lc/get?signCode='+signCode+'&name='+getCookie('name'),{});
            }
        function updateOnlineInfo(signCode,startTime,endTime,authorizeStatus,applyUser,authorizeUser,remarks) 
            { 
                var param='?signCode='+signCode+'&startTime='+startTime+'&endTime='+endTime+'&authorizeStatus='+authorizeStatus+'&applyUser='+applyUser
                +'&authorizeUser='+authorizeUser+'&remarks='+remarks+'&name='+getCookie('name');
                return HttpService.post($rootScope.gateInfoApi+'lms/lc/update'+param,{});
            }

        function verification(signCode) 
            { 
                return HttpService.post($rootScope.gateInfoApi+'lms/lc/verification?signCode='+signCode,{});
            }
        function getClientType() 
            { 
                return HttpService.post($rootScope.gateInfoApi+'lms/clientType/selectAll?'+'name='+getCookie('name'),{});
            }
        function getApplyUser() 
            { 
               return HttpService.post($rootScope.gateInfoApi+'lms/customer/getAllCustomer?name='+getCookie('name'),{});
            }
         function authStatistic(clientTypeCode,startTime,endTime,isAuthorized){
           
            return HttpService.post($rootScope.gateInfoApi+'lms/lc/authstatistic?clientTypeCode='+clientTypeCode+'&startTime='+startTime+'&endTime='+endTime+'&isAuthorized='+isAuthorized+'&name='+getCookie('name'),{});
            
        }
        function verifyStatistic(clientTypeCode,startTime,endTime){
           
            return HttpService.post($rootScope.gateInfoApi+'lms/lc/verifystatistic?clientTypeCode='+clientTypeCode+'&startTime='+startTime+'&endTime='+endTime+'&name='+getCookie('name'),{});
            
        }

         function authStatisticAll(){
            return HttpService.post($rootScope.gateInfoApi+'lms/lc/authstatisticAll?'+'name='+getCookie('name'),{});
        }

	}
})();
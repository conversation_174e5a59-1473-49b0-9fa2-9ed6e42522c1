(function () {
    'use strict';
    app.factory('feeRecordService', feeRecordService);
    feeRecordService.$inject = ["HttpService", '$rootScope'];

    function feeRecordService(HttpService, $rootScope) {

        var service = {
        		getData:getData,//获取所有费用信息
        		addFeeInfo:addFeeInfo,//新增费用信息
        		upFeeInfo:upFeeInfo,//修改
        		delFeeInfo:delFeeInfo//删除
        };
        return service;
        /**
         * 获取所有费用信息列表
         */
        function getData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costFee/getData', urlData);
        }
        /**
         * 新增费用信息
         */
        function addFeeInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costFee/addFeeInfo', urlData);
        }
        /**
         * 修改费用信息
         */
        function upFeeInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costFee/upFeeInfo', urlData);
        }
        /**
         * 删除费用信息
         */
        function delFeeInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costFee/delFeeInfo', urlData);
        }

    }
})();

(function () {
    app.controller("repositoryEmployeePerHisController", ['comService','$rootScope', '$scope','$state','$stateParams','$modal','historyRepositoryInfoService','inform','$window','Trans','AgreeConstant',
        function (comService,$rootScope, $scope,$state,$stateParams, $modal,historyRepositoryInfoService,inform,$window,Trans,AgreeConstant) {
            //查询输入框
            $scope.formRefer={};
            $scope.formRefer.repositoryid = '';
            // 初始化分页数据
            $scope.pages = inform.initPages();

            function getData() {
                var urlData = {
                    repositoryName: $scope.formRefer.repositoryid,
                    employeeId: $scope.formRefer.employeeNo,
                    repositoryType :     $scope.formRefer.repositoryType
                }
                historyRepositoryInfoService.getDocAuthorizationHis(urlData).then(function (data) {
                    $scope.showTable = 1;
                    if (data.code === AgreeConstant.code) {
                        $scope.tableDataList1 = data.data.historyPeopleStatus.nowPeopleList;
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
                historyRepositoryInfoService.getCodeAuthorizationHis(urlData).then(function (data) {
                    $scope.showTable = 1;
                    if (data.code === AgreeConstant.code) {
                        $scope.tableDataList2 = data.data;
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            $scope.backPage = function () {
                $state.go("app.office.personRepositoryDetail", {
                    'repositoryid': null,
                    'employeeNo': $scope.formRefer.employeeNo,
                    'isHistory': '1'
                });
            }

            $scope.$watch('$viewContentLoaded', function () {
                if ($stateParams.repositoryid) {
                    $scope.formRefer.repositoryid = $stateParams.repositoryid;
                }
                if ($stateParams.employeeNo) {
                    $scope.formRefer.employeeNo = $stateParams.employeeNo;
                }
                if ($stateParams.repositoryType) {
                    $scope.formRefer.repositoryType = $stateParams.repositoryType;
                }
                getData();
            });
        }]);
})();

(function() {
    'use strict';
    app.factory('workHoursLoadService', workHoursLoadService);
    workHoursLoadService.$inject=["HttpService",'$rootScope'];

    function workHoursLoadService(HttpService,$rootScope){

        var service={
            selectWorkHoursLoadData:selectWorkHoursLoadData,
            selectDetailList:selectDetailList,
            refreshEffortRate:refreshEffortRate,
            updateEstimate:updateEstimate,
            updateLeft:updateLeft,
            getProDeptChildren:getProDeptChildren,
            getZentaoProductLineList:getZentaoProductLineList,
            getProjectListByLine:getProjectListByLine,
            selectEmployeesByDepartment:selectEmployeesByDepartment

        };
        return service;

        /**
         * 查询工时负载信息
         */
        function selectWorkHoursLoadData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'zentaoTask/selectWorkHoursLoadData', urlData);
        }
        /**
         * 刷新工时负载率基础数据
         */
        function selectDetailList(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'zentaoTask/selectDetailList', urlData);
        }
        /**
         * 查询工时负载信息
         */
        function refreshEffortRate() {
            return HttpService.get($rootScope.getWaySystemApi + 'zentaoTask/refreshEffortRate');
        }
        /**
         * 更新预计工时
         */
        function updateEstimate (urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'zentaoTask/updateEstimate', urlData);
        }
        /**
         * 更新剩余工时
         */
        function updateLeft (urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'zentaoTask/updateLeft', urlData);
        }
        /**
         * 更新剩余工时
         */
        function updateLeft (urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'zentaoTask/updateLeft', urlData);
        }
        /**
         ** 获取禅道中的下属部门
         **/
        function getProDeptChildren(parentId){
            return HttpService.get($rootScope.getWaySystemApi + 'zentaoTask/getProDeptChildren',{'parentId':parentId});
        }
        /**
         ** 获取禅道中的产品线部门
         **/
        function getZentaoProductLineList(){
            return HttpService.post($rootScope.getWaySystemApi + 'zentaoTask/getZentaoProductLineList');
        }
        /**
         ** 获取禅道中的下属部门
         **/
        function getProjectListByLine(line){
            return HttpService.get($rootScope.getWaySystemApi + 'zentaoTask/getProjectListByLine',{'line':line});
        }
        /**
         ** 获取禅道中的下属部门
         **/
        function selectEmployeesByDepartment(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'zentaoTask/selectEmployeesByDepartment',urlData);
        }
    }
})();
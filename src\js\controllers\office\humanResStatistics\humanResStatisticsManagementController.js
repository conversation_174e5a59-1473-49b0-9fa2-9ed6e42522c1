
(function () {
    app.controller("humanResStatisticsManagement", [ '$scope','$rootScope','$stateParams','$state','LocalCache','inform','Trans','comService','humanResStatisticsService','AgreeConstant','$modal',
        function ($scope,$rootScope, $stateParams,$state,LocalCache,inform,Trans,comService,humanResStatisticsService,AgreeConstant,$modal) {

            $scope.type=1;
            if($stateParams.type){
                $scope.type = $stateParams.type;
            }
            //标记数据获取进度
            $scope.flag=0;
            //标记是否是在初始化时设置快捷按钮样式
            $scope.initFlag=0;
            $scope.initDataFlag=0;
            //是否点击了查询按钮
            $scope.selectFlag;
    		$scope.formRefer = {};
    		$scope.timeSelect=['前一月','前一季度','上半年','下半年','本年度','上年度'];
    		$scope.personOffInTypeList=[{
                'code':'03',
                'value':'入职'
    		},
    		{
                'code':'04',
                'value':'调出'
            },
            {
                'code':'05',
                'value':'离职'
            }];
    		$scope.initTime = initTime;
            initTime('本年度');
            initData();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.selectData = selectData;
            getData();

            //设置列表的高度
            function setDivHeight(){
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - 250;
                var clientWidth = document.body.clientWidth;
                var divWidth = clientWidth - 450;
                $("#divTBDis").height(divHeight);
                $("#divTBDis").width(divWidth);
                $("#subDivTBDis").height(divHeight);

                $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
                $("#buttonStyle").css({"width": 100+"px"});
            }
           /**
            * 当窗体大小变化时，修改图例大小
            */
           window.addEventListener("resize", function () {
               if ($scope.currentDepartmentAreaPersonChart) { $scope.currentDepartmentAreaPersonChart.resize(); }
               if ($scope.currentDepartmentActualTimeChart) { $scope.currentDepartmentActualTimeChart.resize(); }
               if ($scope.currentAreaActualTimeChart) { $scope.currentAreaActualTimeChart.resize(); }
               if ($scope.currentLineActualTimeChart) { $scope.currentLineActualTimeChart.resize(); }
               if ($scope.currentDepartmentEmpQuitChart) { $scope.currentDepartmentEmpQuitChart.resize(); }
               if ($scope.currentAreaEmpQuitChart) { $scope.currentAreaEmpQuitChart.resize(); }
           });
           //初始化数据
            function initData(){
                //初始化地区
                $scope.areaList = [];
                $scope.areaMap = {};
                $scope.areaMap1 = {};
                comService.getParamList('AREA_TYPE', 'AREA_TYPE').then(function (data) {
                    $scope.areaList = data.data;
                    angular.forEach($scope.areaList, function (area) {
                        $scope.areaMap[area.param_value]=area.param_code;
                        $scope.areaMap1[area.param_code]=area.param_value;
                    });
                    $scope.initDataFlag++;
                    getData();
                });
                //初始化部门
				$scope.departmentList = [];
				$scope.departmentMap = {};
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentList = comService.getDepartment(data.data);
                    angular.forEach($scope.departmentList, function (department) {
                        $scope.departmentMap[department.orgCode]=department.orgName;
                    });
                    $scope.initDataFlag++;
                    getData();
                });
                //初始化产品线
                $scope.productLineList = [];
                $scope.productLineMap = {};
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    $scope.productLineList = data.data;
                    angular.forEach($scope.productLineList, function (productLine) {
                        $scope.productLineMap[productLine.param_code]=productLine.param_value;
                    });
                    $scope.initDataFlag++;
                    getData();
                });
            }

            function timeout(){
                //表示图表信息全部获取完成
                if(4 === $scope.flag){
                    setTimeout(eChartForDepartmentAreaPerson,500);
                    setTimeout(eChartForActualTimePerson,500);
                    setTimeout(eChartForDepartmentEmpQuit,500);
                    setTimeout(eChartForAreaEmpQuit,500);
                }
            }

            function initTime(flag){
                $scope.butFlag = flag;
                var date = new Date();
                var y = date.getFullYear();  //当前年份
                //设置为1号，防止31号时获取到当月
                date.setDate(1);
                if('前一月'===$scope.butFlag){
                    date.setMonth(date.getMonth()-1);
                    $scope.formRefer.startTime = inform.format(date,"yyyy-MM");
                    $scope.formRefer.endTime = inform.format(date,"yyyy-MM");
                }
                if('前一季度'===$scope.butFlag){
                    //当前月份
                    var m = new Date().getMonth();
                    //当前季度
                    var q = parseInt(m / 3);
                    //上一季度的开始日期
                    $scope.formRefer.startTime = inform.format(new Date(y, (q - 1) * 3, 1),"yyyy-MM");
                    //上一季度的结束日期
                    $scope.formRefer.endTime = inform.format(new Date(y, q * 3, 0),"yyyy-MM");
                }
                if('上半年'===$scope.butFlag){
                    $scope.formRefer.startTime = y+'-01';
                    $scope.formRefer.endTime = y+'-06';
                }
                if('下半年'===$scope.butFlag){
                    $scope.formRefer.startTime = y+'-07';
                    $scope.formRefer.endTime = y+'-12';
                }
                if('本年度'===$scope.butFlag){
                    $scope.formRefer.startTime = y+'-01';
                    $scope.formRefer.endTime = y+'-12';
                }
                if('上年度'===$scope.butFlag){
                    $scope.formRefer.startTime = parseInt(y) - 1+'-01';
                    $scope.formRefer.endTime = parseInt(y) - 1+'-12';
                }
                if($scope.initFlag===0){
                    setTimeout(setButton,500);
                }else {
                    setButton();
                }
            }
            //设置按钮颜色
            function setButton(){
                $scope.initFlag=1;
                //获取所有快捷按钮
                var buttonBoxes = document.getElementsByName("buttons");
                angular.forEach(buttonBoxes, function (but) {
                    if($scope.butFlag === but.id){
                        $("#"+but.id).css("background-color", "#16a8f8");
                    }else{
                        $("#"+but.id).css("background-color", "#CDCDC1");
                    }
                });
            }

            //查询当前团队的首页信息
            function getData(){
                if($scope.initDataFlag!==3){
                    return;
                }
                //获取系研在职员工总数
                getEmpCountTotal();
                //获取部门区域员工统计
                getDepartmentAreaPersonInfo();
                //获取实时人力数据
                getActualTimePersonInfo();
                //查询
                selectData();
            }
            function selectData(flag){
                $scope.selectFlag=flag;
                //获取系研汇总数据：期初、期末、平均等
                getTotalData();
                //获取部门离职人力
                $scope.departmentEmpQuitList=[];
                //获取区域离职人力
                $scope.areaEmpQuitList=[];
            }
        //获取系研在职员工数量
        function getEmpCountTotal(){
            $scope.empCountTotal=[];
			humanResStatisticsService.getEmpCountTotal().then(function(data) {
				if (data.code===AgreeConstant.code) {
                    $scope.empCountTotal = data.data;
				} else {
					inform.common(data.message);
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
        }
        //获取部门区域员工统计
        function getDepartmentAreaPersonInfo(){
            $scope.departmentAreaPersonList=[];
			humanResStatisticsService.getDepartmentAreaPersonInfo().then(function(data) {
				if (data.code===AgreeConstant.code) {
                    $scope.departmentAreaPersonList = data.data;
                    var totalNum = 0;
                    angular.forEach($scope.departmentAreaPersonList, function (departmentAreaPerson) {
                        var weiHai = departmentAreaPerson.regionEmpCountMap[$scope.areaMap['威海']]==null ?0:departmentAreaPerson.regionEmpCountMap[$scope.areaMap['威海']];
                        var beiJing = departmentAreaPerson.regionEmpCountMap[$scope.areaMap['北京']]==null ?0:departmentAreaPerson.regionEmpCountMap[$scope.areaMap['北京']];
                        var xiAn = departmentAreaPerson.regionEmpCountMap[$scope.areaMap['西安']]==null ?0:departmentAreaPerson.regionEmpCountMap[$scope.areaMap['西安']];
                        var shenZhen = departmentAreaPerson.regionEmpCountMap[$scope.areaMap['深圳']]==null ?0:departmentAreaPerson.regionEmpCountMap[$scope.areaMap['深圳']];
                        totalNum = weiHai+beiJing+xiAn+shenZhen;
                        departmentAreaPerson.totalNum = totalNum;
                    });
				} else {
					inform.common(data.message);
				}
				$scope.flag++;
                timeout();
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
        }
        //获取实时人力数据
        function getActualTimePersonInfo(){
            $scope.actualTimePersonList=[];
			humanResStatisticsService.getActualTimePersonInfo().then(function(data) {
				if (data.code===AgreeConstant.code) {
                    $scope.actualTimePersonList = data.data;
				} else {
					inform.common(data.message);
				}
				$scope.flag++;
                timeout();
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
        }
        //获取系研汇总数据：期初、期末、平均等
        function getTotalData(){
            var urlData={
                'statisDateStart':$scope.formRefer.startTime,
                'statisDateEnd':$scope.formRefer.endTime
            }
            $scope.totalData=[];
			humanResStatisticsService.getTotalData(urlData).then(function(data) {
				if (data.code===AgreeConstant.code) {
                    $scope.totalData = data.data;
				} else {
					inform.common(data.message);
				}
                //获取部门离职人力
                getEmpQuitStatisInfo(1,deptEmpQuitData);
                //获取区域离职数据
                getEmpQuitStatisInfo(2,areaEmpQuitData);
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
        }

        //获取部门/区域离职统计
        function getEmpQuitStatisInfo(type,backFunction){
            var urlData={
                'dimensionType':type,
                'statisDateStart':$scope.formRefer.startTime,
                'statisDateEnd':$scope.formRefer.endTime
            }
			humanResStatisticsService.getEmpQuitStatisInfo(urlData).then(backFunction,
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
        }
        //处理部门离职数据
        function deptEmpQuitData(data){
            if (data.code===AgreeConstant.code) {
                $scope.departmentEmpQuitList = data.data;
                if($scope.selectFlag){
                    eChartForDepartmentEmpQuit();
                }
			} else {
				inform.common(data.message);
			}
			$scope.flag++;
            timeout();
		}

        //处理区域离职数据
        function areaEmpQuitData(data){
            if (data.code===AgreeConstant.code) {
                $scope.areaEmpQuitList = data.data;
                if($scope.selectFlag){
                    eChartForAreaEmpQuit();
                }
			} else {
				inform.common(data.message);
			}
			$scope.flag++;
            timeout();
		}

        //显示部门区域员工统计图
     	function eChartForDepartmentAreaPerson() {
     	    $scope.currentDepartmentAreaPersonChart = echarts.init(document.getElementById('departmentAreaPersonChart'));
     	    //处理部门区域员工数量获取到的返回值
            var xData=[];
            var weiHai=[];
            var beiJing=[];
            var xiAn=[];
            var shenZhen=[];
            angular.forEach($scope.departmentAreaPersonList, function (departmentAreaPerson) {
                xData.push($scope.departmentMap[departmentAreaPerson.deptCode]==null? departmentAreaPerson.deptCode:$scope.departmentMap[departmentAreaPerson.deptCode]
                        +"("+departmentAreaPerson.totalNum+"人)");
                weiHai.push(departmentAreaPerson.regionEmpCountMap[$scope.areaMap['威海']]);
                beiJing.push(departmentAreaPerson.regionEmpCountMap[$scope.areaMap['北京']]);
                xiAn.push(departmentAreaPerson.regionEmpCountMap[$scope.areaMap['西安']]);
                shenZhen.push(departmentAreaPerson.regionEmpCountMap[$scope.areaMap['深圳']]);
            });

            var labelOption = {
              show: true,
              position: 'insideBottom',
              distance: 15,
              align: 'center',
            //  formatter: '{c}人',
              // fontSize: 16,
              rich: {
                name: {}
              }
            };
            var option = {
              title:{
                  text:"系研区域员工统计",
                  textStyle:{
                      fontSize: 13,
                      color: '#333'
                  }
              },
              tooltip: {
                trigger: 'axis',

              },
              legend: {
                data: ['威海','北京','西安','深圳']
              },
              xAxis: [
                {
                  type: 'category',
                  axisTick: { show: false },
                  name: '部门',
                  data: xData,
                  axisLabel:{
                      rotate:20
                  },
                }
              ],
              yAxis: [
                {
                  type: 'value',
                  name: '人',
                }
              ],
              series: [
                  {
                    name: '威海',
                    type: 'bar',
                    barGap: 0,
                    label: labelOption,
                    data: weiHai
                  },
                  {
                    name: '北京',
                    type: 'bar',
                    barGap: 0,
                    label: labelOption,
                    data: beiJing
                  },
                  {
                    name: '西安',
                    type: 'bar',
                    barGap: 0,
                    label: labelOption,
                    data: xiAn
                  },
                  {
                    name: '深圳',
                    type: 'bar',
                    label: labelOption,
                    data: shenZhen
                  }
                ]
            };
            $scope.currentDepartmentAreaPersonChart.setOption(option, true);
     	}
        //员工分组统计图创建
        function eChartForActualTimePerson(){
            $scope.currentDepartmentActualTimeChart = echarts.init(document.getElementById('departmentActualTimeChart'));
            $scope.currentAreaActualTimeChart = echarts.init(document.getElementById('areaActualTimeChart'));
            $scope.currentLineActualTimeChart = echarts.init(document.getElementById('lineActualTimeChart'));
            //处理实时人力获取到的返回值
            angular.forEach($scope.actualTimePersonList, function (actualTimePerson) {
                var data=[];
                if('部门' ===actualTimePerson.statisDimensionTypeName){
                    for(var dempEmpInfo in actualTimePerson.empCountMap){
                        var dempEmp={value: actualTimePerson.empCountMap[dempEmpInfo], name: $scope.departmentMap[dempEmpInfo]==null? dempEmpInfo:$scope.departmentMap[dempEmpInfo]};
                        data.push(dempEmp);
                    }
                    eChartShowForActualTimePerson($scope.currentDepartmentActualTimeChart,'部门',data);
                }
                if('区域' ===actualTimePerson.statisDimensionTypeName){
                    for(var areaEmpCount in actualTimePerson.empCountMap){
                        var areaEmp={value: actualTimePerson.empCountMap[areaEmpCount], name: $scope.areaMap1[areaEmpCount]==null? areaEmpCount:$scope.areaMap1[areaEmpCount]};
                        data.push(areaEmp);
                    }
                    eChartShowForActualTimePerson($scope.currentAreaActualTimeChart,'区域',data);
                }
                if('产品线' ===actualTimePerson.statisDimensionTypeName){
                    for(var lineEmpCount in actualTimePerson.empCountMap){
                        var lineEmp={value: actualTimePerson.empCountMap[lineEmpCount], name: $scope.productLineMap[lineEmpCount]==null? lineEmpCount:$scope.productLineMap[lineEmpCount]};
                        data.push(lineEmp);
                    }
                    eChartShowForActualTimePerson($scope.currentLineActualTimeChart,'产品线',data);
                }
            });
        }
        //员工分组统计图显示
        function eChartShowForActualTimePerson(currentChart,type,data){
            var option = {
              title: {
                text: '各'+type+'实时人力数据',
                left: 'center'
              },
              tooltip: {
                trigger: 'item'
              },
              legend: {
                show:false,
                orient: 'vertical',
                left: 'left'
              },
              series: [
                {
                  name: type+'人力数据',
                  type: 'pie',
                  radius: '40%',
                  //重点
            　　label : {
            　　　　normal : {
            　　　　　　formatter: function(data){
                        return data.name+":"+data.value+"\n"+data.percent.toFixed(1)+"%"
                      },
            　　　　　　textStyle : {
            　　　　　　　　fontWeight : 'normal',
            　　　　　　　　fontSize : 11
            　　　　　　}
            　　　　}
            　　},
                  data: data,
                  emphasis: {
                    itemStyle: {
                      shadowBlur: 10,
                      shadowOffsetX: 0,
                      shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                  }
                }
              ]
            };
            currentChart.setOption(option, true);
        }
        //部门离职统计图显示
        function eChartForDepartmentEmpQuit(){
            $scope.currentDepartmentEmpQuitChart = echarts.init(document.getElementById('departmentEmpQuitChart'));
            //处理离职人力获取到的返回值
            var xData=[];
            //有效人力
            var effectiveEmpCount=[];
            //期末人数
            var endPeriodEmp=[];
            //离职人数
            var quitEmp=[];
            //离职率
            var quitEmpRate=[];
            var quitEmpRateBase=[];
            var mapData = $scope.departmentMap;
            angular.forEach($scope.departmentEmpQuitList, function (empQuit) {
                xData.push(mapData[empQuit.statisTargetName]===0? empQuit.statisTargetName:mapData[empQuit.statisTargetName]);
                effectiveEmpCount.push(empQuit.effectiveEmpCount===0? null:empQuit.effectiveEmpCount);
                endPeriodEmp.push(empQuit.endPeriodEmpCount===0? null:empQuit.endPeriodEmpCount);
                quitEmp.push(empQuit.quitEmpCount===0? null:empQuit.quitEmpCount);
                quitEmpRate.push(empQuit.quitRate);
                quitEmpRateBase.push($scope.totalData.quitEmpRate);
            });

            var option = {
              title:{
                  text:"系研人力统计",
                  textStyle:{
                      fontSize: 13,
                      color: '#333'
                  }
              },
              tooltip: {
                trigger: 'axis',
                formatter: formatterCall,
                axisPointer: {
                  type: 'cross',
                  crossStyle: {
                    color: '#999'
                  }
                }
              },
              legend: {
                data: ['期末人数', '离职人数','离职率','系研离职率','有效人力'],
                top:16
              },
              xAxis: [
                {
                  type: 'category',
                  data:xData,
                  axisPointer: {
                    type: 'shadow'
                  },
                  axisLabel:{
                      rotate:25
                  },
                }
              ],
              yAxis: [
                {
                  type: 'value',
                  name: '人数',
                  axisLabel: {
                    formatter: '{value}'
                  }
                },
                {
                  type: 'value',
                  name: '离职率',
                  axisLabel: {
                    formatter: '{value}%'
                  }
                }
              ],
              series: [
                {
                  name: '期末人数',
                  type: 'bar',
                  label: {
                    show: true,
                  },
                  data:endPeriodEmp
                },
                {
                  name: '离职人数',
                  type: 'bar',
                  label: {
                    show: true,
                  },
                  data:quitEmp
                },
                {
                  name: '离职率',
                  type: 'line',
                  yAxisIndex: 1,
                  label:{
                      show:true,
                      formatter: '{c}%'
                  },
                  data: quitEmpRate
                },
                {
                  name: '系研离职率',
                  type: 'line',
                  symbol:'none',
                  yAxisIndex: 1,
                //  smooth:false,
                  lineStyle:{
                      width:2,
                      type:'dotted'
                  },
                  data: quitEmpRateBase
                },
                {
                  name: '有效人力',
                  type: 'bar',
                  label: {
                    show: true,
                  },
                  data:effectiveEmpCount
                },
              ]
            };
            $scope.currentDepartmentEmpQuitChart.setOption(option, true);
        }

        //区域离职人力统计图显示
        function eChartForAreaEmpQuit(){
            $scope.currentAreaEmpQuitChart = echarts.init(document.getElementById('areaEmpQuitChart'));
            //处理离职人力获取到的返回值
            var xData=[];
            //期末人数
            var endPeriodEmp=[];
            //离职人数
            var quitEmp=[];
            //离职率
            var quitEmpRate=[];
            var quitEmpRateBase=[];
            var mapData = $scope.areaMap1;
            angular.forEach($scope.areaEmpQuitList, function (empQuit) {
                xData.push(mapData[empQuit.statisTargetName]===0? empQuit.statisTargetName:mapData[empQuit.statisTargetName]);
                endPeriodEmp.push(empQuit.endPeriodEmpCount===0? null:empQuit.endPeriodEmpCount);
                quitEmp.push(empQuit.quitEmpCount===0? null:empQuit.quitEmpCount);
                quitEmpRate.push(empQuit.quitRate);
                quitEmpRateBase.push($scope.totalData.quitEmpRate);
            });

            var option = {
              title:{
                  text:"区域离职统计",
                  textStyle:{
                      fontSize: 13,
                      color: '#333'
                  }
              },
              tooltip: {
                trigger: 'axis',
                formatter: formatterCall,
                axisPointer: {
                  type: 'cross',
                  crossStyle: {
                    color: '#999'
                  }
                }
              },
              legend: {
                data: ['期末人数', '离职人数','离职率','系研离职率'],
                top:16
              },
              xAxis: [
                {
                  type: 'category',
                  data:xData,
                  axisPointer: {
                    type: 'shadow'
                  },
                }
              ],
              yAxis: [
                {
                  type: 'value',
                  name: '人数',
                  axisLabel: {
                    formatter: '{value}'
                  }
                },
                {
                  type: 'value',
                  name: '离职率',
                  axisLabel: {
                    formatter: '{value}%'
                  }
                }
              ],
              series: [
                {
                  name: '期末人数',
                  type: 'bar',
                  label: {
                    show: true,
                  },
                  data:endPeriodEmp
                },
                {
                  name: '离职人数',
                  type: 'bar',
                  label: {
                    show: true,
                  },
                  data:quitEmp
                },
                {
                  name: '离职率',
                  type: 'line',
                  yAxisIndex: 1,
                  label:{
                      show:true,
                      formatter: '{c}%'
                  },
                  data: quitEmpRate
                },
                {
                  name: '系研离职率',
                  type: 'line',
                  symbol:'none',
                  yAxisIndex: 1,
                //  smooth:false,
                  lineStyle:{
                      width:2,
                      type:'dotted'
                  },
                  data: quitEmpRateBase
                }

              ]
            };
            $scope.currentAreaEmpQuitChart.setOption(option, true);
        }

        //自定义鼠标悬浮样式
		 function formatterCall (params, ticket, callback) {
            var htmlStr = '';
            for(var i=0;i<params.length;i++){
                var param = params[i];
                var xName = param.name;//x轴的名称
                var seriesName = param.seriesName;//图例名称
                var value = param.value;//y轴值
                var color = param.color;//图例颜色
                if(i===0){
                    htmlStr += xName + '<br/>';//x轴的名称
                }
                htmlStr +='<div>';
                //为了保证和原来的效果一样，这里自己实现了一个点的效果
                htmlStr += '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:'+color+';"></span>';
                //圆点后面显示的文本
                htmlStr += seriesName;
                htmlStr += '<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">';
                if(!value){
                    value="-";
                }
                if('离职率'===seriesName || '系研离职率'===seriesName){
                    htmlStr += value+ '%';
                }else{
                    htmlStr += value;
                }

                htmlStr += '</span>';
                htmlStr += '</div>';
            }
            return htmlStr;
         }

         $scope.reseatPersonOffInParams = function(timeFlag){
             if(timeFlag){
                 $scope.formRefer.personOffInStartTime=$scope.formRefer.startTime;
                 $scope.formRefer.personOffInEndTime=$scope.formRefer.endTime;
             }else {
                 var date = new Date();
                 date.setMonth(date.getMonth()-1);
                 $scope.formRefer.personOffInStartTime=inform.format(date,"yyyy-MM");
                 $scope.formRefer.personOffInEndTime=inform.format(new Date(),"yyyy-MM");
             }
             $scope.formRefer.department='';
             $scope.formRefer.area='';
         }

        //获取入离职人员明细
        $scope.getPersonOffIn = function(type,timeFlag){
            if(type){
                $scope.formRefer.personOffInType=type;
                $scope.reseatPersonOffInParams(timeFlag);
            }

            var urlData={
                'changeType':$scope.formRefer.personOffInType,
                'statisDateStart':$scope.formRefer.personOffInStartTime,
                'statisDateEnd':$scope.formRefer.personOffInEndTime,
                'deptCode':$scope.formRefer.department,
                'regionCode':$scope.formRefer.area
            }

            $scope.personInfoList=[];
			humanResStatisticsService.getPersonOffIn(urlData).then(function(data) {
				if (data.code===AgreeConstant.code) {
                    $scope.personInfoList = data.data;
				} else {
					inform.common(data.message);
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
        }
        //下载入离职明细
        $scope.downloadPersonOffIn = function(type){
             var text="入职明细表";
             if('2'===type){
                text="离职明细表";
             }
             inform.modalInstance("确定要下载"+text+"吗？").result.then(function() {
                var urlData={
                    'changeType':type,
                    'statisDateStart':$scope.formRefer.startTime,
                    'statisDateEnd':$scope.formRefer.endTime
                }
                 inform.downLoadFile('hr_download/for_emp_entry_quit',urlData,text+inform.format(new Date(),'yyyy-MM-dd')+".xlsx");
            });
        }
        //跳转到人力变动同比界面
        $scope.gotoHumanResChangeStatistics =function(){
            $state.go('app.office.humanResChangeStatistics');
        }

        //开始时间
        $scope.openSearchOnboardTime = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openSearchOnboardTime1 = true;
            $scope.openStopOnboardTime1 = false;
        };
        //截止时间
         $scope.openStopOnboardTime = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openSearchOnboardTime1 = false;
            $scope.openStopOnboardTime1 = true;
        };
        //跳转员工信息
        $scope.goToEmpInfo = function() {
            //保存当前页面查询条件
            LocalCache.setObject('humanResource_searchObject', $scope.formRefer);
            //跳转页面
            $state.go("app.office.staffInfoManagementController", {
                entrance: 'humanResStatistics'
            });
        }
      }]);
})();
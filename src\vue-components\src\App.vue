<template>
  <div id="app">
    <el-container>
      <el-header>
        <h1>Vue3 组件开发预览</h1>
      </el-header>
      <el-main>
        <el-tabs v-model="activeTab">
          <el-tab-pane label="考勤详情" name="attendance">
            <AttendanceDetail />
          </el-tab-pane>
          <el-tab-pane label="其他组件" name="other">
            <el-card>
              <h3>其他 Vue3 组件可以在这里预览</h3>
              <el-button type="primary">示例按钮</el-button>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import AttendanceDetail from './pages/AttendanceDetail.vue'

const activeTab = ref('attendance')
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

.el-header {
  background-color: #409eff;
  color: white;
  text-align: center;
  line-height: 60px;
}

.el-main {
  padding: 20px;
}
</style>

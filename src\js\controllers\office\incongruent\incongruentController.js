(function () {
    app.controller("incongruentManagement", ['comService', '$rootScope', '$stateParams', '$scope', 'incongruentService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
        function (comService, $rootScope, $stateParams, $scope, incongruentService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //项目状态
            $scope.projectStatusList = AgreeConstant.projectStatusList;
            //项目情况报告
            $scope.projectData = [];
            $scope.enter = $stateParams.enter;
            $scope.formRefer = {};
            if('menu' === $scope.enter){
                $scope.formRefer.projectStatus = '进行中';
            }else {
                //页面查询条件，从详情页返回时会保留原输入值
                $scope.formRefer = {
                    "cname":$stateParams.cname,
                    "productLine":$stateParams.productLine,
                    "type":$stateParams.type,
                    "qualityEngineer":$stateParams.qualityEngineer,
                    "department":$stateParams.department,
                    "twoDepartment":$stateParams.twoDepartment,
                    "projectStatus":$stateParams.projectStatus
                };
            }

            $scope.getTwoDepartment = getTwoDepartment;
            getTwoDepartment();

            //绑定文件控件改变事件
            $("#filesImg1").change(submitForm);
            $("#filesImg1").change(fileChangeEvent);

            //页面分页信息
            $scope.pages = {
                pageNum: '',	//分页页数
                size: '',		//分页每页大小
                total: ''		//数据总数
            };
            // 初始化分页数据
            $scope.pages = inform.initPages();

            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //开发模型下拉框数据源
            $scope.typeSelect = [{
                value: '瀑布',
                label: '瀑布'
            }, {
                value: '敏捷',
                label: '敏捷'
            }];
            //初始化页面信息
            initPages();
            $scope.getData = getData;
            getData($scope.pages.pageNum);
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
                $("#subDivTBDis4").height(divHeight-70);
            }

            /**
             * 页面初始化
             */
            function initPages() {
                //获取产品线
                $scope.productLineList = [];
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.projectLine = data.data;
                    }
                });

                //获取部门
                $scope.departmentList = [];
                comService.getOrgChildren('0002').then(function (data) {
                    if (data.data) {
                        $scope.departmentList = data.data;
                    }
                });
            }

            /**
             * 重置按钮
             */
            $scope.clear = function(){
                $scope.formRefer={};
                $scope.formRefer.projectStatus = "进行中";//项目状态默认进行中
                $scope.twoDepartmentList = [];
            };

            /**
             * 获取二级部门按钮
             */
            function getTwoDepartment(){
                //获取二级部门
                $scope.twoDepartmentList = [];
                if ($scope.formRefer.department){
                    comService.getOrgChildren($scope.formRefer.department).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.twoDepartmentList = data.data;
                        }
                    });
                }

            }

            /**
             * 获取项目
             */
            function getData(pageNum) {
                var urlData = {
                    'cname': $scope.formRefer.cname,
                    'qualityEngineer': $scope.formRefer.qualityEngineer,
                    'productLine': $scope.formRefer.productLine,
                    'type': $scope.formRefer.type,
                    //标识是查询不符合项问题,在后台查询项目信息时需要用到
                    'isInCongruentQuery': 'true',
                    'page': pageNum,
                    'pageSize': $scope.pages.size,
                    'department': $scope.formRefer.department,
                    'twoDepartment': $scope.formRefer.twoDepartment,
                    'projectStatus':$scope.formRefer.projectStatus
                };
                incongruentService.getProjectInfo(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            //项目报告
                            $scope.projectData = data.data.list;
                            // 分页信息设置
                            $scope.pages.total = data.data.total;           // 页面数据总数
                            $scope.pages.star = data.data.startRow;         // 页面起始数
                            $scope.pages.end = data.data.endRow;            // 页面结束数
                            $scope.pages.pageNum = data.data.pageNum;       //页号

                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            /**
             * 报表界面切换(详情)
             */
            $scope.details = function (item) {
                $state.go('app.office.incongruentDetailManagement', {
                    project: item.id,
                    cname: $scope.formRefer.cname,
                    productLine: $scope.formRefer.productLine,
                    type: $scope.formRefer.type,
                    qualityEngineer: $scope.formRefer.qualityEngineer,
                    department: $scope.formRefer.department,
                    twoDepartment: $scope.formRefer.twoDepartment,
                    projectStatus:$scope.formRefer.projectStatus
                });
            };

            /**
             * 更新项目信息
             */
            $scope.updateProjectInfo = function (projectData) {

                //判断通过项数是否大于总项数
                if(projectData.length > 0){
                    for (var i = 0; i < projectData.length ; i++) {
                        if(!Boolean(projectData[i].checknum)){
                            projectData[i].checknum = 0;
                        }
                        if(!Boolean(projectData[i].passnum)){
                            projectData[i].passnum = 0;
                        }
                        if(parseInt(projectData[i].checknum) < parseInt(projectData[i].passnum)){
                            inform.common("序号为" + ($scope.pages.size*($scope.pages.pageNum-1) + i + 1) + "的数据通过项数大于检查项数，请修改！");
                            return;
                        }
                    }
                }
                //更新操作
                incongruentService.updateProjectInfo(projectData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        inform.common(data.message);
                        $scope.getData(1);
                    } else {
                        inform.common(data.message);
                    }
                }, function () {
                    inform.common(Trans("tip.requestError"));
                });
            };

            /**
             * 判断总检查项和通过项的数量
             * @param m
             */
            $scope.checkNum = function(m){
                if(m.passnum - m.checknum > 0){
                    inform.common("通过项数量不可大于总检查项数");
                }else{
                    m.passnum = m.passnum - 0;
                    m.checknum = m.checknum - 0;
                }

            };

            //生成Excel模板
            $scope.toTemplateExcel = function() {

                inform.modalInstance("确定要下载吗?").result.then(function() {
                    inform.downLoadFile('incongruent/toTemplateExcel',{},"不符合项问题模板.xlsx");
                });

            };

            $scope.selectFile = function() {
                document.getElementById("filesImg1").click();
            };

            /**
             * 选择上传文件后事件
             */
            function fileChangeEvent(e){
                var fileName = "文件名称：" + e.currentTarget.files[0].name;
                $("#fileNameDis").text(fileName);
            }

            //上传文件
            function submitForm(){

                var aaa = document.getElementById("myForm");
                var formData = new FormData(aaa);//表单id  初始化表单值
                var file = document.querySelector('input[type=file]').files[0]; //获取文档中有类型为file的第一个input元素
                if(!file){
                    inform.common("请先选择文件!");
                    return false;
                }else if(file.size > 1048576 * 2){
                    inform.common("上传文件大小不得超过2M，请分割后重新上传!");
                    $("#myForm")[0].reset();
                    $("#fileNameDis").text("");
                    return false;
                }
                formData.append('fileName', file);
                var a = file.type;
                if(a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"){
                    inform.common("请选择.xlsx类型的文档进行上传!");
                    $("#myForm")[0].reset();
                    $("#fileNameDis").text("");
                    return false;
                } else {
                    inform.modalInstance("确定要上传文件吗?").result.then(function() {
                        inform.uploadFile('incongruent/uploadExcel',formData,function func(result){
                            if (result.code===AgreeConstant.code) {

                                // 关闭遮罩层
                                inform.closeLayer();

                                $modal.open({
                                    templateUrl: 'errorModel.html',
                                    controller: 'ModalInstanceCtrl',
                                    size: "lg",
                                    resolve: {
                                        items: function () {
                                            return result.message;
                                        }
                                    }
                                });

                            } else {
                                // 关闭遮罩层
                                inform.closeLayer();
                                inform.common("上传失败！");
                            }
                            $("#myForm")[0].reset();
                            $("#fileNameDis").text("");
                        });
                    });
                }
            }

            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */

        }]);
})();
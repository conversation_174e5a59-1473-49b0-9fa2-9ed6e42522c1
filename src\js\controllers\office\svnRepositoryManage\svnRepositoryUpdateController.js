
(function () {
    app.controller("svnRepositoryUpdateController", ['comService','companyProjectModule','$rootScope', '$scope','$state','$stateParams','$modal','svnRepositoryUpdateService','reportService','inform','Trans','AgreeConstant','LocalCache',
        function (comService,companyProjectModule,$rootScope, $scope,$state,$stateParams, $modal,svnRepositoryUpdateService,reportService,inform,Trans,AgreeConstant,LocalCache) {

        /**
         * *************************************************************
         *             初始化部分                                 开始
         * *************************************************************
         */
    	//仓库信息
    	$scope.formUpRepository ={};
		//项目列表
		$scope.projectList = [];
    	//用于判断是否已经点击新增仓库按钮
    	$scope.add=true;
		//按钮转换字符串
		$scope.textForButton="";
		//标题转换字符串
		$scope.textForTitle="";
		//新增和修改的区别标识
		$scope.operateType="";
		//是否解禁可编辑
		$scope.operateDisabled=false;
		$scope.repositoryTypeList = [
			{
				value: 'svn',
				label: 'svn'
			}, {
				value: 'git',
				label: 'git'
			}
		];
		$scope.repositoryStatusList = [
			{
				value: '进行中',
				label: '进行中'
			}, {
				value: '权限回收',
				label: '权限回收'
			}, {
				value: '权限维护',
				label: '权限维护'
			}
		];
		// 用来临时存储人员列表
		$scope.repositoryEmployees = {
			projectMembers: []
		}
		if (LocalCache.getObject('repository_updateObject').repositoryName) {
			//获取缓存
			$scope.repositoryDetails = LocalCache.getObject('repository_updateObject');
			$scope.repositoryDetails.projectId = parseInt($scope.repositoryDetails.projectId);
			$scope.repositoryDetails.otherProjectId = [];
			if(LocalCache.getObject('repository_updateObject').otherProjectId) {
                angular.forEach(LocalCache.getObject('repository_updateObject').otherProjectId.split(','),
                 function(item, index) {
                    $scope.repositoryDetails.otherProjectId.push(parseInt(item));
                });
			}
		}
    	//初始化页面信息

    	initPages();
    	//根据仓库id回填仓库信息
		if($scope.repositoryDetails){
			$scope.operateType="2"; //修改
			$scope.operateDisabled=false; //启用编辑
			$scope.textForButton="修改";
			$scope.textForTitle="修改仓库";
			//仓库名等不可选
			$scope.haveProject = 1;
			//对原缓存进行覆盖
			LocalCache.setObject("repository_updateObject", {});
    	}else{
			$scope.operateType="1"; //新增
			$scope.operateDisabled=true; //禁用编辑
			$scope.textForButton="新增";
			$scope.textForTitle="新增仓库";
			//设置状态默认进行中
			$scope.formUpRepository.repositoryStatus = '进行中';
			$scope.formUpRepository.otherProjectId = [];
			$scope.formUpRepository.projectId = "";
			$scope.formUpRepository.productLineId = "";
            $scope.repositoryEmployees.projectMembers = [];
			$scope.haveProject=0;
		}
		$scope.addRepository = addRepository;//添加仓库
    	$scope.saveRepository = saveRepository;//保存仓库修改信息
		//选择项目时，回填产品线项目经理等信息
        $scope.projectChange = projectChange;
		//匹配操作是新增还是修改
		$scope.choose = choose;
        /**
         * *************************************************************
         *              初始化部分                                 结束
         * *************************************************************
         */

        /**
         * *************************************************************
         *              方法声明部分                                 开始
         * *************************************************************
         */

        /**
    	 * 页面初始化
    	 */
    	function initPages() {
			//获取所有项目名称
			comService.getProjectsName().then(function (data) {
				$scope.projectList = angular.fromJson(data.data);
				if($scope.operateType === "2"){
					getRepositoryById($scope.repositoryDetails.repositoryId);
				}
			});
    		//获取地区
    		$scope.areaList = [];
    		comService.getParamList('AREA_TYPE','AREA_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.areaList =  data.data;
				}
            });
    		//获取产品线
    		$scope.productLinesList = [];
    		comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.productLinesList =  data.data;
        			var other = {
        				'param_code':'',
        				'param_value':'无'
        			};
        			$scope.productLinesList.push(other);
        		}
            });
    		//获取山东新北洋集团的下级部门信息
    		$scope.oneorgList = [];
    		comService.getOrgChildren('0002').then(function(data) {
        		if (data.data) {
        			$scope.oneorgList =  data.data;
        		}
            });
    		//获取员工信息
            $scope.employeeList = [];
            comService.getEmployeesByOrgId('').then(function(data) {
                if (data.data) {
                    $scope.employeeList = data.data;
				}
            });
            //获取部门
            $scope.orgList = [];
            comService.getOrgChildren('D010053').then(function(data) {
                if (data.data) {
                    $scope.orgList =  data.data;
                    var other = {
            			'orgCode':'',
            			'orgName':'其他'
            		};
            		$scope.orgList.push(other);
                }
            });
            //获取角色列表
			$scope.characterList = [
				{
					value: '项目经理',
					label: '项目经理'
				},
				{
					value: '测试人员',
					label: '测试人员'
				},
				{
					value: '开发人员',
					label: '开发人员'
				},
				{
					value: 'PPQA',
					label: 'PPQA'
				}
			]
    	}
    	// 根据产品线获取产品分类
		$scope.getProductTypeList = getProductTypeList;
		function getProductTypeList() {
			$scope.productTypeList = [];
			if ($scope.formUpRepository.productLineId){
				comService.getParamList('PRODUCT_TYPE', $scope.formUpRepository.productLineId).then(function (data) {
					if (data.data) {
						$scope.productTypeList = data.data;
					}
				});
			}
		}

        /***********************************新增仓库***********************************/

        /**
    	 * /新增仓库
    	 */
    	function addRepository() {

    	        var urlData={   //新增仓库信息
					'repositoryName':$scope.formUpRepository.repositoryName,//仓库名
					'remarks':$scope.formUpRepository.remarks,//备注
					'repositoryType':$scope.formUpRepository.repositoryType, //仓库类别
					'projectId':$scope.formUpRepository.projectId,//项目id
					'otherProjectId':null == $scope.formUpRepository.otherProjectId?null:$scope.formUpRepository.otherProjectId.join(','),//项目id
					'productLineId':$scope.formUpRepository.productLineId,//产品线
					'productTypeId':$scope.formUpRepository.productTypeId,//产品分类
					'departmentId':$scope.formUpRepository.departmentId,//所属部门
					'areaId':$scope.formUpRepository.areaId,//所属部门
					'projectManager':$scope.formUpRepository.projectManager,//项目经理
					'repositoryStatus':$scope.formUpRepository.repositoryStatus, //仓库状态
					'projectMembers':$scope.repositoryEmployees.projectMembers, //仓库人员列表
					'createUser':LocalCache.getSession('employeeId')//创建者
    	    	};
				var modalInstance = $modal.open({
    	    		templateUrl: 'myModalContent',
    	            controller: 'ModalInstanceCtrl',
    	            size: "sm",
    	            resolve: {
    	            	items: function() {
    	            		return "是否确定新增该仓库？";
    	            	}
    	            }
    	        });
    	    	modalInstance.result.then(function() {
    	    		svnRepositoryUpdateService.addRepository(urlData).then(function(data) {
    	            	if (data.code === AgreeConstant.code) {
    	            		inform.common("添加仓库成功");
    	    	        } else {
    	    	            inform.common(data.message);
    	    	        }
    	    		}, function(error) {
    	    			inform.common(Trans("tip.requestError"));
    	    		});
    	    	});
    	}

    	/***********************************回填仓库***********************************/

    	/**
         * 根据ID 获取仓库信息
         */
        function getRepositoryById(repositoryId) {
				$scope.formUpRepository = $scope.repositoryDetails;
				if (!$scope.formUpRepository.projectMembers){
					$scope.repositoryEmployees.projectMembers = [];
				} else {
					$scope.repositoryEmployees.projectMembers = $scope.formUpRepository.projectMembers;
				}

				//回填地区
				if(typeof($scope.formUpRepository.areaId) === "undefined" || $scope.formUpRepository.areaId === null){
					$scope.formUpRepository.areaId = null;
				}
                if(null ==$scope.formUpRepository.productLineId || $scope.formUpRepository.productLineId === '无'){
                    //默认产品线为“无”
                    $scope.formUpRepository.productLineId = "";
              	}
              	if(null ==$scope.formUpRepository.departmentId || $scope.formUpRepository.departmentId === '其他'){
              		//默认所属部门为其他
              		$scope.formUpRepository.departmentId = "";
              	}
              	getProductTypeList();
				//根据id获取仓库的人员信息
                getRepositoryEmployee(repositoryId);
		}

        /**
    	 *  根据ID 获取仓库的人员信息
    	 */
       	function getRepositoryEmployee(repositoryId) {
       		$scope.employeeRepository = [];//修改页面下仓库的人员信息
			$scope.projectMembers = [];
			$scope.repositoryMembers = [];
       		svnRepositoryUpdateService.getRepositoryEmployee(repositoryId).then(function(data) {
				$scope.repositoryMembers = data.data
                 if (data.code === AgreeConstant.code) {
                	 $scope.employeeRepository = angular.fromJson(data.data);
                	 angular.forEach($scope.employeeRepository, function(employee, i) {
						$scope.repositoryEmployees.projectMembers.push({
							employeeId: employee.employeeId,
							role: employee.role,
							authorization: employee.authorization
						});
                	 });
				 } else {
                    inform.common(data.message);
                 }
            }, function() {
           		inform.common(Trans("tip.requestError"));
            });
       	}

		/**
    	 *  选择是增加还是修改
    	 */
		function choose() {
			if($scope.operateType === "2"){
				saveRepository();
			}else{
				addRepository();
			}

		}

        /***********************************更新仓库***********************************/

       	/**
      	 *  修改仓库
      	 */
      	function saveRepository() {

 	        	var urlData={
      				'repositoryId':$scope.formUpRepository.repositoryId, //仓库id
      				'repositoryName':$scope.formUpRepository.repositoryName,//新仓库名
					'remarks':$scope.formUpRepository.remarks,//备注
      				'projectId':$scope.formUpRepository.projectId,//项目id
      				'otherProjectId':null == $scope.formUpRepository.otherProjectId?null:$scope.formUpRepository.otherProjectId.join(','),//项目id
					'productLineId':$scope.formUpRepository.productLineId,//产品线
					'repositoryType':$scope.formUpRepository.repositoryType, //仓库类别

					'departmentId':$scope.formUpRepository.departmentId,//所属部门
					'areaId':$scope.formUpRepository.areaId,//所属部门
					'projectManager':$scope.formUpRepository.projectManager,//项目经理
					'repositoryStatus':$scope.formUpRepository.repositoryStatus, //仓库状态
					'productTypeId':$scope.formUpRepository.productTypeId, //产品分类
					'projectMembers':$scope.repositoryEmployees.projectMembers //仓库人员列表
      			};
			console.log(urlData);
			var modalInstance = $modal.open({
 	        		templateUrl: 'myModalContent',
 	        		controller: 'ModalInstanceCtrl',
 	        		size: "sm",
 	        		resolve: {
 	        			items: function() {
 	        				return "是否确定修改该仓库？";
 	        			}
 	        		}
 	        	});
 	        	modalInstance.result.then(function() {
 	        		svnRepositoryUpdateService.saveRepository(urlData).then(function(data) {
 	        			if (data.code === AgreeConstant.code) {
                        	inform.common("修改仓库信息成功");
 	        			} else {
 	        				inform.common(data.message);
 	        			}
 	        		}, function() {
                      inform.common(Trans("tip.requestError"));
 	        		});
 	        	});
         }


        //选择项目时，回填产品线项目经理等信息
		function projectChange(teamId){
			if(typeof(teamId) === "undefined" || teamId === null || teamId === ''){
				$scope.formUpRepository.departmentId = "";
            	$scope.formUpRepository.productLineId = "";
            	$scope.formUpRepository.productTypeId = "";
				$scope.formUpRepository.teamLeaderId = "";
				$scope.formUpRepository.areaId = "";
				//放开可选
				$scope.haveProject = 0;
			}else{
				reportService.getProjectInfoById(teamId).then(function (data) {
					if (data.code !== AgreeConstant.code) {
						inform.common(data.message);
						return;
					}
					if(data.data !== null){
						//仓库名等选项设为不可选择
						$scope.haveProject = 1;
						//回填地区
						if(typeof(data.data.areaId) === "undefined" || data.data.areaId === null){
							$scope.formUpRepository.areaId = null;
						}else{
							angular.forEach($scope.areaList,function(area){
								if(area.param_value === data.data.areaId.substring(0,2)){
									$scope.formUpRepository.areaId = area.param_code;
								}
							});
						}
						if(data.data.productLine === null){
							$scope.formUpRepository.productLineId = ""
						}else{
							$scope.formUpRepository.productLineId = data.data.productLine;
						}

						$scope.formUpRepository.departmentId = data.data.department;
						$scope.formUpRepository.productLineId = data.data.productLine;
						$scope.formUpRepository.productTypeId = data.data.productClassification;
						$scope.formUpRepository.teamLeaderId = data.data.projectManagerId;
						$scope.formUpRepository.areaId = data.data.areaId;
						getProductTypeList();
					}
				}, function (error) {
					inform.common(Trans("tip.requestError"));
				});
			}

		}



        // 新增项目人员列表
		$scope.addEmployee = function addEmployee() {
			$scope.repositoryEmployees.projectMembers.push({
				employeeId: '',
				role: ''
			});
		}

		$scope.getEmployee = function () {
			console.log($scope.repositoryEmployees.projectMembers);
		}

		//删除项目人员列表
		$scope.deleteEmployee = function (index) {
			$scope.repositoryEmployees.projectMembers.splice(index, 1);
		}

		}]);
})();

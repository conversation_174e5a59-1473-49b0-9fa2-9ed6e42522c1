<template>
    <div class="simple-demo">
        <el-card class="demo-card" shadow="hover">
            <template #header>
                <div class="card-header">
                    <span>🎉 Vue3 + Element Plus 演示组件</span>
                </div>
            </template>

            <div class="demo-content">
                <p>这是一个简单的 Vue3 组件演示</p>

                <el-row :gutter="20" style="margin-bottom: 20px">
                    <el-col :span="12">
                        <el-input v-model="inputValue" placeholder="请输入内容" @input="handleInput" />
                    </el-col>
                    <el-col :span="12">
                        <el-button type="primary" @click="handleClick" :icon="Search"> 点击测试 </el-button>
                    </el-col>
                </el-row>

                <div v-if="message" class="message-display">
                    <el-alert :title="message" type="success" :closable="false" show-icon> </el-alert>
                </div>

                <div class="counter-section">
                    <p>
                        计数器: <strong>{{ counter }}</strong>
                    </p>
                    <el-button-group>
                        <el-button @click="counter--" :icon="Minus">减少</el-button>
                        <el-button @click="counter++" :icon="Plus">增加</el-button>
                    </el-button-group>
                </div>

                <div class="time-display">
                    <p>当前时间: {{ currentTime }}</p>
                </div>
            </div>
        </el-card>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { Search, Plus, Minus } from '@element-plus/icons-vue';

// 响应式数据
const inputValue = ref('');
const message = ref('');
const counter = ref(0);
const currentTime = ref('');

let timer = null;

// 方法
const handleInput = (value) => {
    console.log('输入内容:', value);
};

const handleClick = () => {
    if (inputValue.value.trim()) {
        message.value = `您输入了: ${inputValue.value}`;
    } else {
        message.value = '请先输入一些内容！';
    }

    // 3秒后清除消息
    setTimeout(() => {
        message.value = '';
    }, 3000);
};

const updateTime = () => {
    currentTime.value = new Date().toLocaleString();
};

// 生命周期
onMounted(() => {
    updateTime();
    timer = setInterval(updateTime, 1000);
    console.log('Vue3 组件已挂载');
});

onUnmounted(() => {
    if (timer) {
        clearInterval(timer);
    }
    console.log('Vue3 组件已卸载');
});

// 暴露给父组件的方法
defineExpose({
    reset: () => {
        inputValue.value = '';
        message.value = '';
        counter.value = 0;
    },
});
</script>

<style scoped>
.simple-demo {
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.demo-card {
    max-width: 600px;
    margin: 0 auto;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    font-size: 16px;
    background: linear-gradient(45deg, #409eff, #67c23a);
    color: white;
    padding: 15px 20px;
    margin: -20px -20px 20px -20px;
}

.demo-content {
    text-align: center;
    padding: 20px;
}

.message-display {
    margin: 20px 0;
}

.counter-section {
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(45deg, #f5f7fa, #c3cfe2);
    border-radius: 12px;
    border: 2px solid #409eff;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.time-display {
    margin-top: 20px;
    padding: 15px;
    background: linear-gradient(45deg, #ecf5ff, #e1f3d8);
    border-radius: 8px;
    color: #409eff;
    font-weight: bold;
    border: 1px solid #409eff;
    box-shadow: inset 0 2px 4px rgba(64, 158, 255, 0.1);
}

/* 确保 Element Plus 组件样式正确显示 */
:deep(.el-card) {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

:deep(.el-button) {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

:deep(.el-button:hover) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.el-input__wrapper) {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-alert) {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>

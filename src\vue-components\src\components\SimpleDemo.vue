<template>
    <div class="simple-demo">
        <el-card class="demo-card" shadow="hover">
            <template #header>
                <div class="card-header">
                    <span>🎉 Vue3 + Element Plus 演示组件</span>
                </div>
            </template>

            <div class="demo-content">
                <p>这是一个简单的 Vue3 组件演示</p>

                <el-row :gutter="20" style="margin-bottom: 20px">
                    <el-col :span="12">
                        <el-input v-model="inputValue" placeholder="请输入内容" @input="handleInput" />
                    </el-col>
                    <el-col :span="12">
                        <el-button type="primary" @click="handleClick" :icon="Search"> 点击测试 </el-button>
                    </el-col>
                </el-row>

                <div v-if="message" class="message-display">
                    <el-alert :title="message" type="success" :closable="false" show-icon> </el-alert>
                </div>

                <div class="counter-section">
                    <p>
                        计数器: <strong>{{ counter }}</strong>
                    </p>
                    <el-button-group>
                        <el-button @click="counter--" :icon="Minus">减少</el-button>
                        <el-button @click="counter++" :icon="Plus">增加</el-button>
                    </el-button-group>
                </div>

                <div class="time-display">
                    <p>当前时间: {{ currentTime }}</p>
                </div>
            </div>
        </el-card>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { Search, Plus, Minus } from '@element-plus/icons-vue';

// 响应式数据
const inputValue = ref('');
const message = ref('');
const counter = ref(0);
const currentTime = ref('');

let timer = null;

// 方法
const handleInput = (value) => {
    console.log('输入内容:', value);
};

const handleClick = () => {
    if (inputValue.value.trim()) {
        message.value = `您输入了: ${inputValue.value}`;
    } else {
        message.value = '请先输入一些内容！';
    }

    // 3秒后清除消息
    setTimeout(() => {
        message.value = '';
    }, 3000);
};

const updateTime = () => {
    currentTime.value = new Date().toLocaleString();
};

// 生命周期
onMounted(() => {
    updateTime();
    timer = setInterval(updateTime, 1000);
    console.log('Vue3 组件已挂载');
});

onUnmounted(() => {
    if (timer) {
        clearInterval(timer);
    }
    console.log('Vue3 组件已卸载');
});

// 暴露给父组件的方法
defineExpose({
    reset: () => {
        inputValue.value = '';
        message.value = '';
        counter.value = 0;
    },
});
</script>

<style scoped>
.simple-demo {
    background-color: #409eff;
    padding: 20px;
}

.demo-card {
    max-width: 600px;
    margin: 0 auto;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    font-size: 16px;
}

.demo-content {
    text-align: center;
}

.message-display {
    margin: 20px 0;
}

.counter-section {
    margin: 20px 0;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 8px;
}

.time-display {
    margin-top: 20px;
    padding: 10px;
    background-color: #ecf5ff;
    border-radius: 4px;
    color: #409eff;
}
</style>

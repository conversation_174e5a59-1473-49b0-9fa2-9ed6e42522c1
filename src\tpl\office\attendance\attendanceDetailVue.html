<!-- Vue3 版本的考勤明细页面 -->
<div class="animated fadeInUp">
    <!-- 导航条 -->
    <ul class="breadcrumb b-a">
        <li><i class="fa fa-home"></i><span translate="员工信息管理"></span></li>
        <li class="active" translate="员工考勤信息(Vue3版本)"></li>
    </ul>
    
    <!-- Vue3 组件容器 -->
    <div 
        vue-component="AttendanceDetail" 
        vue-props="vueProps"
        on-vue-event="handleVueEvent(eventName, data)"
        style="min-height: 600px;">
        <!-- 加载中显示 -->
        <div class="text-center" style="padding: 50px;">
            <i class="fa fa-spinner fa-spin fa-2x"></i>
            <p>正在加载 Vue3 组件...</p>
        </div>
    </div>
</div>

<style>
/* 确保 Vue 组件样式不冲突 */
.attendance-detail-vue {
    /* Vue 组件的根样式 */
}

/* Element Plus 样式兼容性调整 */
.el-card {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fff;
    overflow: hidden;
    color: #303133;
    transition: 0.3s;
}

.el-card__header {
    padding: 18px 20px;
    border-bottom: 1px solid #e4e7ed;
    box-sizing: border-box;
}

.el-card__body {
    padding: 20px;
}
</style>

/*
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date:   2019-01-10 15:20:05
 * @Last Modified by:   l<PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
    'use strict';
  app.factory('codeConfigService', codeConfigService);
  codeConfigService.$inject=["HttpService",'$rootScope'];

  function codeConfigService(HttpService,$rootScope){
    
    var service={
    		getAllProject : getAllProject,
    		getAllProjectProperty : getAllProjectProperty,
    		updateInfo : updateInfo,
        getProjectList:getProjectList,
        TEselectMap:TEselectMap,
        batchUpdateInfo:batchUpdateInfo,
        getTeamList:getTeamList
    };
    return service;

   
    
    function updateInfo(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'code/updateConfig', urlData);
    }
      /**
       * 查询所有项目（名称，id，部门，项目经理），用于下拉框
       */
    function getProjectList() {
        var urlData = {};
        return HttpService.post($rootScope.getWaySystemApi + 'projectmanagement/getProjectList', urlData);
    }

    
    /**
   * 查询所有团队（名称，id，部门，项目经理），用于下拉框
   */
    function getTeamList() {
      var urlData = {};
      return HttpService.post($rootScope.getWaySystemApi + 'projectmanagement/getTeamList', urlData);
    }

    /**
     * 根据查询条件获取项目
     * @param urlData 查询条件
     * */
    function getAllProject(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'code/getAllProject', urlData);
    }
    /**
     * 根据项目名集合获得项目的属性
     * @param projectNames 项目名集合
     * */
    function getAllProjectProperty(projectNames) {
    	return HttpService.post($rootScope.getWaySystemApi + 'code/getAllProjectProperty', projectNames);
    }

      /**
       * 获取技术评价相关下拉框
       * @param projectNames 项目名集合
       * */
      function TEselectMap(projectNames) {
          return HttpService.post($rootScope.getWaySystemApi + 'code/TEselectMap', projectNames);
      }
    /**
     * 批量更新配置信息
     * @param {*} urlData 
     * @returns 
     */
    function batchUpdateInfo(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'code/batchUpdateConfig', urlData);
    }

  }
})();

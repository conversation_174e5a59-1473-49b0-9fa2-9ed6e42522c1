
(function() {
    'use strict';
  app.factory('customerManagementService', customerManagementService);
  customerManagementService.$inject=["HttpService",'$rootScope'];

  function customerManagementService(HttpService,$rootScope){
    
    var service={
        getCustomerInfoList:getCustomerInfoList,
        updateCustomerInfo:updateCustomerInfo,
        addCustomerInfo:addCustomerInfo
    };
    return service;

    /**
     * 分页查询客户信息
     */
    function getCustomerInfoList(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'customerManagement/getCustomerInfoList', urlData);
    }
    /**
    * 修改客户信息
    */
    function updateCustomerInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'customerManagement/updateCustomerInfo', urlData);
    }

    //新增客户信息
    function addCustomerInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'customerManagement/addCustomerInfo', urlData);
    }

    }
})();

(function() {
    'use strict';
    app.factory('personRepositoryDetailService', personRepositoryDetailService);
    personRepositoryDetailService.$inject=["HttpService",'$rootScope'];

    function personRepositoryDetailService(HttpService,$rootScope){
        var service={
            findRepositoryListByPage:findRepositoryListByPage,
            callOutModuleEmployee:callOutModuleEmployee
        };
        function findRepositoryListByPage(urlData) {
            return HttpService.post($rootScope.getWaySystemApi+'personPermission/getRepositoryInfo',urlData);
        }
        function callOutModuleEmployee(urlData){
            return HttpService.post($rootScope.getWaySystemApi+'personPermission/personChange',urlData);
        }
        return service;
    }
})();

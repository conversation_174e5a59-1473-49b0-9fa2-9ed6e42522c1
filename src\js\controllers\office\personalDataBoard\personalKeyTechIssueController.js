(function() {
    app.controller("personalKeyTechIssueController", ['keyTechIssueService', '$state', 'comService', '$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$stateParams', '$modal', '$state', '$http',
        function(keyTechIssueService, state, comService, $rootScope, $scope, inform, Trans, AgreeConstant, LocalCache, $stateParams, $modal, $state, $http) {
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.pages = {
                pageNum: '1', //分页页数
                size: '100', //分页每页大小
                total: '0' //数据总数
            };
            //项目状态下拉框数据源
            $scope.statusMap = {
                "0": '待审核',
                "1": '审核拒绝',
                "2": '审核通过'
            };
            $scope.formRefer = {};

            //初始化页面信息
            initPages();
            $scope.initPages = initPages;
            $scope.flag = false;
            //获取数据
            $scope.getData = getData;
            //初始化时设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.itemList = [];
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 65);
            }
            /**
             * 页面初始化：部门下拉表、奖项下拉表、页面初始
             */
            function initPages() {
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ? $scope.sessionEmpId : $stateParams.empId;
                $scope.formRefer.startTime = inform.format(new Date(),"yyyy")+'-01-01';
                //页面初始化
                getData(1);
            }

            /**
             * 重置
             */
            $scope.reset = function() {
                $scope.formRefer.startTime = inform.format(new Date(),"yyyy")+'-01-01';
                $scope.formRefer.endTime = "" ;//时间止
            };

            /**
             * 获取数据以分页的形式
             */
            function getData(indexNum) {

                var urlData = {
                    'solvePerson': $scope.formRefer.empId, //解决人id
                    'reviewResult': "审批通过", //审核状态
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),
                    'page': indexNum,
                    'size': $scope.pages.size
                };
                keyTechIssueService.getKeyIssueInfo(urlData).then(function(data) {
                    if (data.code === '0000') {
                        if(null==data.data){
                            $scope.keyIssueData = {};
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages(); //初始化分页数据
                        }else {
                            //关键技术问题
                            $scope.keyIssueData = data.data.list;
                            // 分页信息设置
                            $scope.pages.total = data.data.total;
                            $scope.pages.star = data.data.startRow;
                            $scope.pages.end = data.data.endRow;
                            $scope.pages.pageNum = data.data.pageNum;
                        } }else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.formRefer.openedStart = true;
                $scope.formRefer.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = false;
                $scope.formRefer.openedEnd = true;
            };
        }
    ]);
})();

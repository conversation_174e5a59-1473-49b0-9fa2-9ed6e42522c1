/*
* @Author: fubaole
* @Date:   2018-02-23 10:39:02
* @Last Modified by:   fubaole
* @Last Modified time: 2018-02-23 15:07:33
*/

(function () {
    'use strict';
    angular.module('app')
        .directive('verifyPage', ['$timeout', function ($timeout) {
            return {
                restrict: 'A',
                link: function (scope, ele, att) {
                  // 事件
                  ele.bind("keyup", function () {
                    if(scope.pages.goNum){
                      scope.pages.goNum = String(scope.pages.goNum).replace(/[^\d]/g,"");// 非数字去掉
                      scope.pages.goNum = Number(scope.pages.goNum.substring(0, 9));// 限制长度9
                    }else{
                      scope.pages.goNum = null;
                    }
                    scope.$apply();
                  });
                }
            };

        }]);
})();

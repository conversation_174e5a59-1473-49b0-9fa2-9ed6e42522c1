(function () {
    app.controller("employeePermissionMain", ['comService', '$http', '$rootScope', '$state', '$scope', '$modal', 'employeePermissionMainService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache',
        function (comService, $http, $rootScope, $state, $scope, $modal, employeePermissionMainService, inform, Trans, AgreeConstant, LocalCache) {

            $scope.formRefer = LocalCache.getObject('formRefer') || {}; //仓库查询条件
            $scope.searchTitlePermission = '按人员条件查询';

            $scope.type = '1';
            $scope.changeType = function changeType(type, item) {
                $scope.type = type;
                if (type === '1') {
                    $scope.searchTitlePermission = '按人员条件查询';
                    $scope.formRefer = {};
                    LocalCache.setObject('formRefer', {});
                    getData();
                } else if (type === '2') {
                    $scope.searchTitlePermission = '按仓库条件查询';
                    $scope.formRefer = {};
                    LocalCache.setObject('formRefer', {});
                    getData();
                }
            }

            $scope.repositoryTypeList = [
                {
                    value: 'svn',
                    label: 'svn'
                }, {
                    value: 'git',
                    label: 'git'
                }];

            $scope.repositoryTotalList = [
                {
                    value: '5',
                    label: '5'
                },
                {
                    value: '10',
                    label: '10'
                },
                {
                    value: '15',
                    label: '15'
                },
                {
                    value: '20',
                    label: '20'
                },
                {
                    value: '25',
                    label: '25'
                },
                {
                    value: '30',
                    label: '30'
                },
                {
                    value: '35',
                    label: '35'
                },
                {
                    value: '40',
                    label: '40'
                },
                {
                    value: '45',
                    label: '45'
                },
                {
                    value: '50',
                    label: '50'
                },
                {
                    value: '55',
                    label: '55'
                },
                {
                    value: '',
                    label: '全部'
                }
            ]
            $scope.maintainerNumList = [
                {
                    value: '1',
                    label: '1人'
                },
                {
                    value: '2',
                    label: '2人'
                },
                {
                    value: '3',
                    label: '大于2人'
                }
            ]
            //查询页面
            $scope.getData = getData;
            // 初始化分页数据
            $scope.pages = inform.initPages();

            //刷新页面时就执行getData()方法
            getData()
            //判断按钮是否具有权限
            getButtonPermission();
            //初始化页面信息
            initPages();


            /**
             * 页面初始化
             */
            function initPages() {
                //获取产品线
                $scope.productLineList = [];
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.productLineList = data.data;
                    }
                });
                //获取部门
                $scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentList = comService.getDepartment(data.data);
                });
            }

            /**
             * 权限管理
             */
            function getButtonPermission(){
                var buttons = {
                    'Button-SvnRepositoryManage-upModule':'upModule' //维护模块
                };
                var urlData = {
                    'userId':LocalCache.getSession("userId"),
                    'parentPermission':'Button-SvnRepositoryManage',
                    'buttons':buttons
                };
                comService.getButtonPermission(urlData,$scope);
            }

            /**
             * 根据分页显示仓库
             */
            function getData() {

                //销毁表格
                $scope.showDataTable = 0;

                LocalCache.setObject('formRefer', $scope.formRefer);

                //返回的仓库信息列表
                $scope.repositoryList = [];

                // 查询条件
                var urlData = {};
                if ($scope.type === '1'){
                    if (LocalCache.getObject('formRefer')) {
                        $scope.formRefer = LocalCache.getObject('formRefer'); // 初始化查询数据
                    } else {
                        $scope.formRefer = {
                            name: '',//姓名
                            department: '',//部门
                            repositoryCount: '',//仓库总数
                            productline: '',//产品线
                            repositoryType: ''//仓库类型
                        }; // 初始化查询数据
                    }
                    //存查询条件
                    urlData = {
                        'employeeName': $scope.formRefer.name,//姓名
                        'department': $scope.formRefer.department,//部门
                        'count': $scope.formRefer.repositoryTotal,//仓库总数
                        'productLine': $scope.formRefer.productline,//产品线
                        'repositoryType': $scope.formRefer.repositoryType,//仓库类型
                        'currentPage': $scope.pages.pageNum,//当前页数
                        'pageSize': $scope.pages.size,
                    };
                }
                else if ($scope.type === '2'){
                    if (LocalCache.getObject('formRefer')) {
                        $scope.formRefer = LocalCache.getObject('formRefer'); // 初始化查询数据
                    } else {
                        $scope.formRefer = {
                            maintainerNum: ''//Maintainer人数
                        }; // 初始化查询数据
                    }
                    //存查询条件
                    urlData = {
                        'maintainerNum': $scope.formRefer.maintainerNum,//Maintainer人数
                        'currentPage': $scope.pages.pageNum,//当前页数
                        'pageSize': $scope.pages.size
                    };
                }

                // 获取数据
                if ($scope.type === '1'){
                    employeePermissionMainService.findRepositoryListByPage(urlData).then(function (data) {
                        //重新生成Table
                        $scope.showDataTable = 1;
                        if (data.code === AgreeConstant.code) {
                            $scope.repositoryList = angular.fromJson(data.data);
                            if ($scope.repositoryList.pageInfo.list.length === 0) {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages();
                            } else {
                                angular.forEach($scope.repositoryList.productLineCount, function (item, i) {
                                    var selfServiceTerminal = item.selfServiceTerminal ? item.selfServiceTerminal : 0;
                                    var printRecognition = item.printRecognition ? item.printRecognition : 0;
                                    var systemIntegration = item.systemIntegration ? item.systemIntegration : 0;
                                    var selfService = item.selfService ? item.selfService : 0;
                                    var automation = item.automation ? item.automation : 0;
                                    var smartCabinet = item.smartCabinet ? item.smartCabinet : 0;
                                    if (item.repositoryName.indexOf('git') !== -1){
                                        $scope.repositoryList.gitRepositoryCount =
                                            parseInt(selfServiceTerminal)+
                                            parseInt(printRecognition)+
                                            parseInt(systemIntegration)+
                                            parseInt(selfService)+
                                            parseInt(automation)+
                                            parseInt(smartCabinet)
                                    } else if (item.repositoryName.indexOf('svn') !== -1){
                                        $scope.repositoryList.svnRepositoryCount =
                                            parseInt(selfServiceTerminal)+
                                            parseInt(printRecognition)+
                                            parseInt(systemIntegration)+
                                            parseInt(selfService)+
                                            parseInt(automation)+
                                            parseInt(smartCabinet)
                                    }
                                });
                                $scope.repositoryList.repositoryCount = $scope.repositoryList.gitRepositoryCount + $scope.repositoryList.svnRepositoryCount;
                                //分页信息设置
                                $scope.pages.total = $scope.repositoryList.pageInfo.total;
                                $scope.pages.star = $scope.repositoryList.pageInfo.startRow;
                                $scope.pages.end = $scope.repositoryList.pageInfo.endRow;
                                $scope.pages.pageNum = $scope.repositoryList.pageInfo.pageNum;
                            }
                            setTimeout(showDataTable, 500);
                            setTimeout(showDataTableEmployee, 500);
                        } else {
                            inform.common(data.message);
                        }
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
                } else if ($scope.type === '2'){
                    employeePermissionMainService.getMaintainer(urlData).then(function (data) {
                        //重新生成Table
                        $scope.showDataTable = 1;
                        if (data.code === AgreeConstant.code) {
                            var jsonData = data.data;
                            $scope.maintainerPeopleList = angular.fromJson(data.data.list);
                            if ($scope.maintainerPeopleList.length === 0) {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages();
                            } else {
                                //分页信息设置
                                $scope.pages.total = jsonData.total;
                                $scope.pages.star = jsonData.startRow;
                                $scope.pages.end = jsonData.endRow;
                                $scope.pages.pageNum = jsonData.pageNum;
                            }
                        } else {
                            inform.common(data.message);
                        }
                        setTimeout(showDataTable, 500);
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
                }
            }

            /**
             *调用DataTable组件冻结表头和左侧及右侧的列
             */
            function showDataTable() {
                $('#fixedLeftAndTop').DataTable({
                    //可被重新初始化
                    retrieve: true,
                    //自适应高度
                    scrollY: false,
                    scrollX: true,
                    scrollCollapse: false,
                    //控制每页显示
                    paging: false,
                    //search框显示
                    searching: false,
                    //排序箭头
                    ordering: false,
                    //底部统计数据
                    info: false,
                });
            }
            function showDataTableEmployee() {
                $('#employeeTable').DataTable({
                    //可被重新初始化
                    retrieve: true,
                    //自适应高度
                    scrollY: 'calc(100vh - 370px)',
                    scrollX: true,
                    scrollCollapse: false,
                    //控制每页显示
                    paging: false,
                    //search框显示
                    searching: false,
                    //排序箭头
                    ordering: false,
                    //底部统计数据
                    info: false,
                });
            }
            /**
             * 下载Excel
             */
            $scope.excelData = function (repositoryType) {
                var urlData = {};
                if ($scope.type === '1'){
                    urlData = {
                        'name': $scope.formRefer.name,//姓名
                        'department': $scope.formRefer.department,//部门
                        'repositoryCount': $scope.formRefer.repositoryCount,//仓库总数
                        'productLine': $scope.formRefer.productline,//产品线
                        'repositoryType': $scope.formRefer.repositoryType//仓库类型
                    }
                } else if ($scope.type === '2'){
                    urlData = {
                        'maintainerNum': $scope.formRefer.maintainerNum//Maintainer人数
                    }
                }
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function () {
                            return "是否确定下载？";
                        }
                    }
                });
                modalInstance.result.then(function () {
                    if ($scope.type === '1'){
                        inform.downLoadFile('svnRepository/loadRepositoryToExcel', urlData, '人员权限信息表.xlsx');
                    } else if ($scope.type === '2'){
                        inform.downLoadFile('personPermission/loadMaintainerExcel', urlData, 'Maintainer人员信息表.xlsx');
                    }
                });
            };

            $scope.toPersonDetail = function (item) {
                $state.go("app.office.personRepositoryDetail", { repositoryid: item.repositoryId, employeeNo: item.employeeId });
            }
        }]);
})();

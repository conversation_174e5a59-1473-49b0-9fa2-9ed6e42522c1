/*
* @Author: fubaole
* @Date:   2017-09-05 10:05:00
* @Last Modified by:   fubaole
* @Last Modified time: 2018-02-24 13:45:13
*/
(function() {
    'use strict';
  app.factory('teamService', teamService);
    teamService.$inject=["HttpService",'$rootScope'];

  function teamService(HttpService,$rootScope){
    var service={
        insertInfo:insertInfo,
        getTeamData:getTeamData,
        updateInfo:updateInfo,
        deleteInfo:deleteInfo,
        selectTeamAndMemberInfo:selectTeamAndMemberInfo
    };
    return service;
    
    /**
     * 新增小组
     * 
     */
	function insertInfo(params) {
		return HttpService.post($rootScope.getWaySystemApi+'team/insertInfo',params);
	}
	
    /**
     * 获取小组信息
     * 
     */
	function getTeamData(params) {
		return HttpService.post($rootScope.getWaySystemApi+'team/getTeamData',params);
	}
    /**
     * 修改小组信息
     *
     */
	function updateInfo(params) {

		return HttpService.post($rootScope.getWaySystemApi+'team/updateInfo',params);
	}

      /**
       * 删除小组信息
       *
       */
      function deleteInfo(params) {

          return HttpService.post($rootScope.getWaySystemApi+'team/deleteInfo',params);
      }

      /**
       * 查询小组及成员信息
       *
       */
      function selectTeamAndMemberInfo(params) {

          return HttpService.post($rootScope.getWaySystemApi+'team/selectTeamAndMemberInfo',params);
      }

  }
})();
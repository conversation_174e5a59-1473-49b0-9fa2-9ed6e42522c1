/*
* @Author: fubaole
* @Date:   2017-09-21 13:50:54
* @Last Modified by:   fubaole
* @Last Modified time: 2017-12-05 19:43:40
* 字数太多用... 代替
* 使用方法: {{item.orgName | characters:20}}
*/

(function(){
  'use strict';
    app.filter('characters', function () {

        return charactersFilter;

        function charactersFilter(input, chars, breakOnWord) {
            if (isNaN(chars)) {
                return input;
            }
            if (chars <= 0) {
                return '';
            }
            if (input && input.length > chars) {
                input = input.substring(0, chars);

                if (!breakOnWord) {
                    var lastspace = input.lastIndexOf(' ');
                    // Get last space
                    if (lastspace !== -1) {
                        input = input.substr(0, lastspace);
                    }
                } else {
                    while (input.charAt(input.length-1) === ' ') {
                        input = input.substr(0, input.length - 1);
                    }
                }
                return input + '...';
            }
            return input;
        }
  });
})();
//# sourceURL=js/controllers/office/projectWeekly/projectWeeklyAddManagement.js
(function () {
    app.controller("projectWeeklyAddManagement", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','projectWeeklyService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope,comService,$scope,$state,$stateParams, $modal,projectWeeklyService,inform,Trans,AgreeConstant,LocalCache,$http) {
       	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		// 正则校验配置
    	$scope.limitList = AgreeConstant.limitList;
		//设置高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
    	
        //新增时人员列表
        $scope.team = [];
        $scope.paramInfo = {
        	projectId:'',
            team: [],
            delayRiskStatement:'【延期风险说明】：\n【预计延期周数】：'
        };
        
        //用于判断是否为新增，若为空，则为新增
        $scope.projectId = $stateParams.projectId;
        
        //用于判断初始化是否完成
        $scope.flag = 0;
        //初始化
        initProject();
        //项目状态下拉框数据源
        $scope.states = [{
            value: '0',
            label: '启用'
        }, {
            value: '1',
            label: '禁用'
        }];

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
        /**
         * 新增一个团队人员
         */
        $scope.addNewBind = function () {
            //团队人员
            var person = {
                'memberId': '',
                'area': '',
                'memberRole': '',
                'affiliationPerson': '0'
            };
            $scope.team.push(person);
        };
        /**
         * 取消一行
         */
        $scope.deleteNewBind = function (index) {
            if (index >= 0) {
                $scope.team.splice(index, 1);
            }
        };
		/**
		 * 初始化
		 */
    	function initProject() {
    		//获取项目下拉框
    		 $scope.projectIdList = [];
    		 comService.getProjectsByLineOffice('').then(function (data) {
                 $scope.projectIdList =data.data;
             });
            //项目阶段
     		$scope.phases = [];
     		comService.getParamList('WEEKLY_REPORT','PHASE').then(function(data) {
         		if (data.data) {
         			$scope.phases =  data.data;
         			$scope.flag++;
         			getData();
         		}
             });
     		//项目归属
     		$scope.affiliations = [];
     		comService.getParamList('WEEKLY_REPORT','AFFILIATION').then(function(data) {
         		if (data.data) {
         			$scope.affiliations =  data.data;
         			$scope.flag++;
         			getData();
         		}
             });
     		//获取员工信息
            $scope.employeeList = [];
            comService.getEmployeesByOrgId('').then(function(data) {
                if (data.data) {
                    $scope.employeeList = data.data;
                    $scope.flag++;
         			getData();
                }
            });
            //获取地区
    		$scope.areas = [];
    		comService.getParamList('AREA_TYPE','AREA_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.areas =  data.data;
        			$scope.flag++;
         			getData();
        		}
            });
    		//获取角色
    		$scope.roles = [{
				param_code: '1',
				param_value: '项目经理'
			},{
				param_code: '2',
				param_value: '研发'
			},{
				param_code: '3',
				param_value: '测试'
			},{
				param_code: '4',
				param_value: '项目助理'
			},{
				param_code: '5',
				param_value: '架构师'
			}];
    	}
    	/**
    	 *  向$scope.team中添加空对象
    	 */
    	function addTeam(one) {
            $scope.team.push({
        	    "area":one.area,//地区
        	    "memberRole":one.memberRole,//角色
        	    "memberId":one.memberId+"",//人员
        	    "affiliationPerson":one.affiliationPerson//人员归属
            });
    	}
    	/**
         * 若为修改，回填信息
         */
        function getData(pageNum) {
        	//初始化未完成
        	if($scope.flag !== 4){
                return;
            }
        	//为新增
            if ($scope.projectId==null){
            	return;
            }
            $scope.setModelCategorys();
        }
    	 /**
         * 根据项目id设置模型分类
         * @param m
         */
       $scope.setModelCategorys = function() {
          var urlData = {
              'projectId': $scope.projectId==null? $scope.paramInfo.projectId:$scope.projectId//项目名称
          };
		   projectWeeklyService.selectOne(urlData).then(function (data) {
               if($scope.projectId==null){
                    $scope.paramInfo.projectAttribution = data.data.projectAttribution;
                    $scope.paramInfo.modelCategory = data.data.modelCategory;
                    $scope.paramInfo.productName = data.data.productName;
                    $scope.paramInfo.expectedDelay = data.data.expectedDelay;
               }else {
                    $scope.paramInfo = data.data;
               }
                angular.forEach($scope.affiliations, function (projectAttribution, i) {//遍历项目归属
                       if (projectAttribution.param_code===$scope.paramInfo.projectAttribution) {
                           $scope.paramInfo.projectAttribution = projectAttribution.param_value;
                       }
                });
				$scope.team = [];
				//获取团队明细
				if(data.data.team.length !== 0){
                    data.data.team.forEach(function (one) {
                           addTeam(one);
                    });
				}
			   },
			   function () {
				   inform.common(Trans("tip.requestError"));
			   });
        };
       /**
        * 修改人员归属
        */
       $scope.checkInfo = function (m) {
       		m.area = (m.affiliationPerson === '0') ? '' : '外部';
       		m.memberId = '';
       		m.memberRole = '';
       };
       /**
        * 保存信息
        */
       $scope.saveInfo = function(){
           var urlData = {
               'projectId': $scope.paramInfo.projectId,
               'phase': $scope.paramInfo.phase,//阶段
               'affiliation': $scope.paramInfo.affiliation,//归属
               'version': $scope.paramInfo.version,//开发版本
               'delayRiskStatement': $scope.paramInfo.delayRiskStatement//里程碑预警
           };
           if ($scope.projectId==null){
        	 //新增
          	 addInfo(urlData);
           } else {
             //修改 
          	 upInfo(urlData)
          }
       };
       /**
        * 添加信息
        */
       function addInfo(urlData) {
    	   projectWeeklyService.addInfo(urlData).then(function (data) {
               if (data.code === AgreeConstant.code) {
                   layer.confirm(data.message,{
                       title:false,
                       btn:['确定']
                   },function(result){
                       layer.close(result);
                       $state.go("app.office.projectWeekly");
                   });
               } else {
                   inform.common(data.message);
               }
           }, function (error) {
               inform.common(Trans("tip.requestError"));
           });
        }
        /**
         * 修改信息
         * @param urlData
         */
        function upInfo(urlData) {
        	projectWeeklyService.updateInfo(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    layer.confirm(data.message,{
                        title:false,
                        btn:['确定']
                    },function(result){
                        layer.close(result);
                    });
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
         }
			/**
			 * 设置列表的高度
			 */
			function setDivHeight(){
				//网页可见区域高度
				var clientHeight = document.body.clientHeight;
				var clientWidth = document.body.clientWidth;
				$("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
			}
    	
	 		/**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */	
		
	}]);
})();
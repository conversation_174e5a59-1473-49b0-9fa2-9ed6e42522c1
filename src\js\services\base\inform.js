/*
* @Author: fubaole
* @Date:   2017-09-05 10:05:00
* @Last Modified by:   fubaole
* @Last Modified time: 2018-02-24 13:45:13
*/
(function() {
    'use strict';
  app.factory('inform', inform);
  inform.$inject=["AgreeConstant","$modal","$http","$rootScope","LocalCache","Trans"];

  function inform(AgreeConstant,$modal,$http,$rootScope,LocalCache,Trans){
    var service={
      showLayer:showLayer,
      closeLayer:closeLayer,
      common:common,
      autoHeight:autoHeight,
      toTreeData:toTreeData,
      formatDate:formatDate,
      formateGetRequest:formateGetRequest,
      unique:unique,
      arrayUnique:arrayUnique,
      initPages:initPages,
      format:format,
      dateToQuarter:dateToQuarter,
      stringToDate:stringToDate,
      hasClass:hasClass,
      removeClass:removeClass,
      addClass:addClass,
      downLoadFile:downLoadFile,
      modalInstance:modalInstance,
      uploadFile:uploadFile,
      getWeekDay:getWeekDay,
      getWeek:getWeek,
      toTemplateExcel:toTemplateExcel,
      getButtonStyle: getButtonStyle,
      formatMoney: formatMoney,
      resetMoney: resetMoney,
      removeZero: removeZero,
      monthBetween:monthBetween,
      commonByTime:commonByTime
    };

    return service;

      //判断element的class属性
      function hasClass(elements, cName) {
          return !!elements.className.match(new RegExp("(\\s|^)" + cName + "(\\s|$)"));
      };
      //添加element的class属性
      function addClass(elements, cName) {
          if (!hasClass(elements, cName)) {
              elements.className += " " + cName;
          };
      };
      //移除element的class属性
      function removeClass(elements, cName) {
          if (hasClass(elements, cName)) {
              elements.className = elements.className.replace(new RegExp("(\\s|^)" + cName + "(\\s|$)"), " ");
          }
      }
      //字符串转日期
      function stringToDate(dateStr,separator){
          if(!separator){
              separator="-";
          }
          var dateArr = dateStr.split(separator);
          var year = parseInt(dateArr[0]);
          var month;
          //判断月份如果是01去掉前面的0
          if(dateArr[1].indexOf("0")===0){
              month = parseInt(dateArr[1].substring(1));
          }else{
              month = parseInt(dateArr[1]);
          }
          var day = parseInt(dateArr[2]);
          var date = new Date(year,month -1,day);
          return date;
      }
      //根据date得到对应季度
      function dateToQuarter(month) {
          if (month >= 1 && month <= 3) {
              return "0";
          } else if (month >= 4 && month <= 6){
              return "1";
          } else if (month >= 7 && month <= 9){
              return "2";
          } else{
              return "3";
          }
      }

    // 显示遮罩层
    function showLayer(msg){
    	if(!msg) {
    		msg = '加载中...';
    	}
      return layer.load(1, {
        content:msg,
        shade: [0.6,'#000'],
        success: function(layero){
          layero.find('.layui-layer-content').css({'padding-top':'90px'});
        }
      });
    }
    // 关闭遮罩层
    function closeLayer(){
      layer.closeAll();
    }
    // 加载完成提示toast
    function common(txt){
      layer.msg(txt,
    		  {
    	  		time:3000
    		  }
      );
    }
    //根据时间展示提示
    function commonByTime(txt, time){
          layer.msg(txt, {
              time:time
          });
    }
    //获取浏览器窗口高度
    function autoHeight() {
      var winHeight=0;
      if (window.innerHeight){
        winHeight = window.innerHeight;
      }else if ((document.body) && (document.body.clientHeight)){
          winHeight = document.body.clientHeight;
      }
      if (document.documentElement && document.documentElement.clientHeight){
        winHeight = document.documentElement.clientHeight;
      }

      angular.element(document.querySelectorAll("#aside")).css({"height":winHeight-135+"px"});
    }

    // 树结构数据格式转化
    function toTreeData(data){
        var pos={};
        var tree=[];
        var i=0;
        while(data.length!=0){
            if(data[i].parent== "#"){
                tree.push({
                    id:data[i].id,
                    parent:data[i].parent,
                    text:data[i].text,
                    children:[]
                });
                pos[data[i].id]=[tree.length-1];
                data.splice(i,1);
                i--;
            }else{
                var posArr=pos[data[i].parent];
                if(posArr!=undefined){

                    var obj=tree[posArr[0]];
                    for(var j=1;j<posArr.length;j++){
                        obj=obj.children[posArr[j]];
                    }

                    obj.children.push({
                        id:data[i].id,
                        parent:data[i].parent,
                        text:data[i].text,
                        children:[]
                    });
                    pos[data[i].id]=posArr.concat([obj.children.length-1]);
                    data.splice(i,1);
                    i--;
                }
            }
            i++;
            if(i>data.length-1){
                i=0;
            }
        }
        return tree;
    }

    // 日期格式转换
    function formatDate(date,format) {
        var args = {
           "M+": date.getMonth() + 1,
           "d+": date.getDate(),
           "h+": date.getHours(),
           "m+": date.getMinutes(),
           "s+": date.getSeconds(),
           "q+": Math.floor((date.getMonth() + 3) / 3),
           "S": date.getMilliseconds()
        };
        if (/(y+)/.test(format))
          format = format.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var i in args) {
          var n = args[i];
          if (new RegExp("(" + i + ")").test(format))
            format = format.replace(RegExp.$1, RegExp.$1.length===1 ? n : ("00" + n).substr(("" + n).length));
        }
        return format;
    }

    // get请求拼接参数
    function formateGetRequest(urlData,valueTag,value){
      if(!urlData){
        urlData = "?"+valueTag+"="+value;
      }else {
        if(typeof value!=="undefined"&&value!==""&&value!==null){
        urlData+="&"+valueTag+"="+value;
        }
      }
        return urlData;
    }

    // 对象去重
    function unique(obj) {
      // n为hash表，r为临时数组
      var n = {}, r = [];
      for (var i = 0; i < obj.length; i++) {
          // 如果hash表中没有当前项
          if (!n[obj[i].id]) {
              // 存入hash表
              n[obj[i].id] = true;
              // 把当前数组的当前项push到临时数组里面
              r.push(obj[i]);
          }
      }
      return r;
    }

    // 数组去重
    function arrayUnique(arr){
       var res = [];
       var json = {};
       for(var i = 0; i < arr.length; i++){
        if(!json[arr[i]]){
         res.push(arr[i]);
         json[arr[i]] = 1;
        }
       }
       return res;
    }

    // 初始化分页
    function initPages() {
      var pages = {
        goNum:null, // 初始化跳转页码
        star:0, //开始条数
        end:0, //结束条数
        total:0, // 总条数
        size:AgreeConstant.pageSize, //每页条数
        pageNum:AgreeConstant.pageNum //默认页
      };
      return pages;
    }

    //时间格式化
    function format(time, format) {
        if(!time) {
            return null;
          }
        var t = new Date(time);
        var tf = function(i) {
            return (i < 10 ? '0' : '') + i
        };
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g,function(a) {
            switch (a) {
                case 'yyyy':
                    return tf(t.getFullYear());
                    break;
                case 'MM':
                    return tf(t.getMonth() + 1);
                    break;
                case 'mm':
                    return tf(t.getMinutes());
                    break;
                case 'dd':
                    return tf(t.getDate());
                    break;
                case 'HH':
                    return tf(t.getHours());
                    break;
                case 'ss':
                    return tf(t.getSeconds());
                    break;
            }
        })
    }

      /**
       * 提示框
       * @param mseeage  提示信息
       * @returns {*}
       */
      function modalInstance(mseeage) {
          var modalInstance = $modal.open({
              templateUrl: 'myModalContent.html',
              controller: 'ModalInstanceCtrl',
              size: "sm",
              resolve: {
                  items: function () {
                      return mseeage;
                  }
              }
          });
          return modalInstance;
      }

      /**
       * 下载文件
       * @param url  接口路径
       * @param params  接口参数
       * @param fileName  文件名（名字+后缀，eg：file.png）
       */
      function downLoadFile(url, params, fileName) {
          //开启遮罩层
          showLayer("下载中。。。。。。");
          $http.post(
              $rootScope.getWaySystemApi + url, params,
              {
                  headers: {
                      'Content-Type': 'application/json',
                      'Authorization': 'Bearer ' + LocalCache.getSession("token") || ''
                  },
                  responseType: 'blob'//
              }
          ).success(function (data) {
              //判断是否失败
              if(data.type === "text/html"){

                  // 关闭遮罩层
                  closeLayer();
                  var reader = new FileReader();
                  reader.readAsText(data,"utf-8");
                  reader.onload = function (e) {
                	  common(JSON.parse(e.target.result).message);
                  };
                  return;
              }

              //var blob = new Blob([data], {type: "application/zip"});
              var blob = new Blob([data]);

              //如果是IE浏览器
              if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                  window.navigator.msSaveOrOpenBlob(blob, fileName);
              }
              //google或者火狐浏览器
              else {
                  var objectUrl = URL.createObjectURL(blob);
                  var aForExcel = $("<a download='" + fileName + "'><span class='forExcel'></span></a>").attr("href", objectUrl);
                  $("body").append(aForExcel);
                  $(".forExcel").click();
                  aForExcel.remove();
              }
              // 关闭遮罩层
              closeLayer();
              common("下载成功!");
          });
      }


      /**
       * 上传文件
       * @param url 接口路径
       * @param formData  需上传的文件对象
       * @param func  上传成功时调用的方法
       */
      function uploadFile(url,formData,func) {
          //开启遮罩层
          showLayer("上传中。。。。。。");
          $.ajax({
              url: $rootScope.getWaySystemApi + url,
              type: 'POST',
              data: formData,
              processData: false,
              contentType: false,
              beforeSend: function (request) {
                  request.setRequestHeader("Authorization", 'Bearer ' + LocalCache.getSession("token") || '');
              },
              success: function (result) {
                  func(result);
              },
              error: function (error) {
                  common(Trans("tip.requestError"));
              }
          });
      }

      /**
       * 根据传入参数，获取对应的星期几的日期
       *
       * eg:getWeekDay('2020-02-02',3), 获取'2020-02-02'所在周的周三的时间，默认格式'yyyy-MM-dd'。
       *    getWeekDay('2020-02-02',8，'yyyy-MM-dd HH'), 获取'2020-02-02'所在周的周八的时间，格式'yyyy-MM-dd HH'。
       * 由于没有星期八，所以顺延为下一周的周一的时间。
       *    getWeekDay('2020-02-02',0，'yyyy-MM-dd HH'), 获取'2020-02-02'所在周的周零的时间，格式'yyyy-MM-dd HH'，
       * 由于没有星期零，所以为上一周的周日的时间。
       *
       * @param time 时间字符串
       * @param day  想要星期几的日期的星期数字
       * @param dateFormat  获取到的日期的时间格式，默认'yyyy-MM-dd'
       */
       function getWeekDay(time,day,dateFormat) {
             //根据传入时间，获取date类型时间
             var now = new Date(time);
             var nowTime = now.getTime() ;
             //获取今天是星期几
             var dayTime = now.getDay();
             var oneDayLong = 24*60*60*1000 ;
             //获取需要的星期day的时间
             var weekTime = (dayTime != 0)?(nowTime - (dayTime-day)*oneDayLong):(nowTime - (7-day)*oneDayLong);

             if(typeof (dateFormat) === 'undefined'){
                 dateFormat = 'yyyy-MM-dd';
             }
             //格式转换
             return format(new Date(weekTime),dateFormat);
       }

      /**
       * 根据传入参数，获取当前日期所在周是第几周
       * （周数所在年份，以定传入日期所在周日日期的年份确）
       *
       * eg：getWeek('2020-01-01'),返回1，因为'2020-01-01'为2020年第一周
       *     getWeek('2019-12-30'),返回1，因为'2019-12-30'所在周的周末为'2020-01-05'为2020年第一周
       *
       * @param time 日期字符串
       * @returns {number}    周数
       */

       //获取指定日期在当年的第几周
      function getWeek(time){
          //获取当前日期的星期日的日期
          time = getWeekDay(time,7);
          var  date1 = new Date(time);
          var date2 = new Date(time);
          //date2设置为今年一月1号
          date2.setMonth(0);
          date2.setDate(1);
          var oneDayLong = 24*60*60*1000 ;
          //计算相差的天数
          var days = Math.ceil((date1-date2)/oneDayLong);
          //计算时今年的第几周
          var num = Math.ceil(days/7);
          return num<10?("0"+num):num;
      }

      /**
       * 下载模板（只能下载xlsx文件）
       *
       * eg： ("员工绩效信息表模板","44")
       *
       * @param excelName 模板名称（不包含后缀）
       * @param excelNum 模板编号（数字）
       */
      function toTemplateExcel(excelName,excelCode) {
          modalInstance("确定要下载模板吗?").result.then(function() {
              var params = {
                  excelName:excelName,
                  excelCode:excelCode
              };
              downLoadFile('common/toTemplateExcel',params,excelName + ".xlsx");
          });
      }

      /*
      * 确认/返回按钮的css样式（动态高度和宽度）
      * @param
      * @param
      * */
      function getButtonStyle(clientHeight,clientWidth) {
          var buttonStyle={
              "top": clientHeight - 90 + "px",
              "position": "fixed",
              "left": clientWidth/2,
              "width": 220+"px",
              "height": 42+"px",
              "z-index": "999",
              "background": "rgba(75,75,75,.85)",
              "border-radius": 5 + "px",
              "display": "flex",
              "padding": "0"
          }
          return buttonStyle;
      }
      /**
      * 金额格式化
      * s表示待格式化的字符串或数字
      * n表示保留几位小数
      * */

      function formatMoney(s, n) {
          var x = '';
          s = s + "";
          if (s.substring(0, 1) === '-') {
              x = '-';
              s = s.substring(1, s.length);
          }
          if (!n && n !== 0) {
              n = 2;
          }
          n = n >= 0 && n <= 20 ? n : 2;
          s = parseFloat((s + "").replace(/[^\d.-]/g, "")).toFixed(n) + "";
          var l = s.split(".")[0].split("").reverse(), r = s.split(".")[1];
          var t = "";
          for (var i = 0; i < l.length; i++) {
              t += l[i] + ((i + 1) % 3 === 0 && (i + 1) !== l.length ? "," : "");
          }
          return n === 0 ? x + t.split("").reverse().join("") : x + t.split("").reverse().join("") + "." + r;
      }
      /**
      * 把格式化的金额还原
      * */
      function resetMoney(s) {
          s = s + '';
          return parseFloat(s.replace(/[^\d.-]/g, ""));
      }

      /**
       * 去除无用小数
       * */
      function removeZero(s) {
          return Math.round(parseFloat(s) * 100) / 100;
      }

      /**
       *
       * 返回两日期之间的月数差
       * @param date1 开始时间
       * @param date2 结束时间
       * @returns {number|*}
       */
      function monthBetween(date1, date2) {
          date1 = date1.split("-");
          date2 = date2.split("-");

          var year1 = parseInt(date1[0]);
          var month1 = parseInt(date1[1]);
          var year2 = parseInt(date2[0]);
          var month2 = parseInt(date2[1]);

          var months = (year2 - year1) * 12 + (month2 - month1) + 1;
          return months;
      }

  }
})();

(function() {
    'use strict';
    app.controller("staffTitleController", ['comService','$rootScope', '$stateParams', '$scope', 'staffTitleService','$modal','inform', 'Trans', 'AgreeConstant','$state', 'LocalCache', '$http',
        function(comService,$rootScope, $stateParams,$scope, staffTitleService,$modal,inform, Trans, AgreeConstant,$state, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            // 列表数据
            $scope.dataList = [];
            $scope.flag = false;
            //设置员工地区的访问权限
            $scope.areaCodeFlag = true;
            $scope.datepicker = {
               currentDate :  new Date()
            };
            //绑定文件控件改变事件
            $("#files").change(submitForm);
            $("#files").change(fileChangeEvent);
            //设置当前时间
            $scope.currentDate =  inform.format(new Date(),"yyyy-MM");

            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //新增数据对象
            $scope.addParam = {};
            //修改数据对象
            $scope.updateParam = {};
            getStaffList();//员工编号和姓名列表
            getAreaList();//地区列表
            $scope.getData = getData;
            $scope.pages = {
               pageNum:"1",
               size:"100"
            };
            $scope.searchObject = {};
            initPrimaryDeptList();//初始化一级部门列表
            $scope.searchObject.primaryDept = "";//初始化一级部门
            $scope.titleLevelList = AgreeConstant.titleLevelList;

            initPage();//初始化页面
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            //获取系研职称信息
            function getTitleList(primaryDept) {
              //获取系研职称信息
              $scope.titleList = [];
              comService.getParamList('STAFF_TITLE','NEW').then(function(data) {
                 $scope.titleList = data.data;
              });
            }


            //重置查询条件
            $scope.clearParams = function() {
                //区域权限
                if (!$scope.areaCodeFlag) {
                    $scope.searchObject.area = "";
                }
                if($scope.flag){
                   $scope.searchObject.department = "";
                   $scope.searchObject.primaryDept = "";
                }
                $scope.searchObject.employeeName = "";
                $scope.searchObject.companyTitle = "";
                $scope.searchObject.onboardingTime = "";
                $scope.searchObject.startTime = "";
                $scope.searchObject.endTime = "";
                $scope.searchObject.getWay = "";
            };



            /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function(data) {
                     if (data.data) {
                         $scope.primaryDeptList = data.data;
                     }
                });
            }
             /**
             * 初始化二级部门列表
             */
            function initSecDeptList() {
                //获取二级部门
                setDept();

            }
            /**
    		 * 文件选择事件
    		 */
    	     $scope.selectFile = function() {
    	     	document.getElementById("files").click();
    	     }
    	     /**
     		 * 选择上传文件后事件
     		 */
     		function fileChangeEvent(e){
     			var fileName = "文件名称：" + e.currentTarget.files[0].name;
     			$("#fileNameDis").text(fileName);
     		}
     		 /**
    	     * 上传文件
    	     */
    	    function submitForm(e){
    	    	var formData = new FormData(document.getElementById("form"));
    	    	var file = e.currentTarget.files[0]; //获取文档中有类型为file的第一个input元素
    	    	if(!file){
                    inform.common("请先选择文件!");
                    return false;
                }else if(file.size > AgreeConstant.fileSize){
                    inform.common("上传文件大小不得超过2M，请分割后重新上传!");
                    $("#myForm")[0].reset();
                    $("#fileNameDis").text("");
                    return false;
                }
    	    	formData.append('file', file);
    	    	var a = file.type;
    	    	if(a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"){
    	        	inform.common("请选择.xlsx类型的文档进行上传!");
    	        	return false;
    	        }
    	        inform.modalInstance("确定要上传文件吗？").result.then(function() {
    	            inform.uploadFile('staffTitle/uploadExcel',formData,function func(result){
    	                // 关闭遮罩层
    	                inform.closeLayer();
    	                $modal.open({
    	                    templateUrl: 'errorModel.html',
    	                    controller: 'ModalInstanceCtrl',
    	                    size: "lg",
    	                    resolve: {
    	                        items: function () {
    	                            return result.message;
    	                        }
    	                    }
    	                });
    	               $("#form")[0].reset();
    	               $("#fileNameDis").text("");
    	               initPage();
    	            });
    	        });
    	     }
    	    
    	    /**
    	     * 下载导入模板
    	     */
            $scope.toExcelModule = function () {
            	inform.modalInstance("确定要下载吗?").result.then(function () {
                	inform.downLoadFile('staffTitle/toExcelModule',{},'员工职称信息导入模板.xlsx');
            	});
            };
            function setDept(){
                //获取二级部门
                $scope.deptList = [];
                comService.getOrgChildren($scope.searchObject.primaryDept).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.deptList = data.data;
                    }
                });

            }

            //修改一级部门，二级部门进行联动
            $scope.changeDept = function(){
                setDept();
                getTitleList($scope.searchObject.primaryDept);
            };
            //获取地区
            function getAreaList(){
                $scope.areaList = [];
                comService.getParamList('AREA_TYPE','AREA_TYPE').then(function(data) {
                   $scope.areaList = data.data;
                });
            }

            /**
            ** 员工编号和姓名列表
            */
            function getStaffList(){
                comService.getStaffList().then(function(data) {
                  $scope.staffList = data.data;
                });
            }
            //新增职称信息选择姓名
            $scope.changeEmployee = function(){
        		 angular.forEach($scope.staffList, function (staff, index) {
        			 if (staff['employeeId']===$scope.addParam.employeeId){
                         $scope.addParam.paramType = staff.paramType;
        				 getTitleList(staff['primaryDept']);
        			 }
        		 });
            }

            //定义排序对象
            $scope.orderObj = {
                title: '$index',
                desc: true,
                order: function(str) {
                    $scope.orderObj.title = str;
                    $scope.orderObj.desc = !$scope.orderObj.desc;
                }
            };

           
            /**
        	 * 设置列表的高度
        	 */
     		function setDivHeight(){
     			//网页可见区域高度
     			 var clientHeight = document.body.clientHeight;
     			 var divHeight = clientHeight - (150 + 185);
     			 $("#divTBDis").height(divHeight);
     			 $("#subDivTBDis").height(divHeight - 65);
     		}

            //分页查询
            function getData(page) {
                if ($scope.searchObject.onboardingTime !== "" || $scope.searchObject.onboardingTime !== null ) {
                    $scope.searchObject.onboardingTime = inform.format($scope.searchObject.onboardingTime,'yyyy-MM-dd');
                }
                if ($scope.searchObject.startTime!== "" || $scope.searchObject.startTime !== null ) {
                    $scope.searchObject.startTime = inform.format($scope.searchObject.startTime,'yyyy-MM');
                }
                if ($scope.searchObject.endTime !== ""|| $scope.searchObject.endTime !== null) {
                    $scope.searchObject.endTime = inform.format($scope.searchObject.endTime,'yyyy-MM');
                }
                //拼装查询条件
                var params = {
                    area:$scope.searchObject.area,
                    primaryDept:$scope.searchObject.primaryDept,
                    department:$scope.searchObject.department,
                    employeeName:$scope.searchObject.employeeName,
                    onboardingTime:$scope.searchObject.onboardingTime,
                    companyTitle:$scope.searchObject.companyTitle,
                    startTime:$scope.searchObject.startTime,
                    endTime:$scope.searchObject.endTime,
                    getWay:$scope.searchObject.getWay,
                    page: page,
                    size: $scope.pages.size
                };
                //获取数据
                staffTitleService.selectTitleByParam(JSON.stringify(params)).then(function (result) {
                     if (result.code === '0000') {

                        $scope.dataList = result.data.list;
                        if (null ==result.data.list || result.data.list.length === 0) {
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages();

                        } else {
                            // 分页信息设置
                          $scope.pages.total = result.data.total;		// 页面总数
                          $scope.pages.star = result.data.startRow;  	//页面起始数
                          $scope.pages.end = result.data.endRow;  		//页面大小数
                          $scope.pages.pageNum = result.data.pageNum;  	//页面页数
                        }
                    } else {

                          inform.common(result.message);

                    }
                }, function (reason) {
                    console.log("error");
                });
            }

            //初始化页面Function
            function initPage() {
                //初始化获得方式列表
                $scope.titleGetTypeList = AgreeConstant.titleGetTypeList;
                getDepartmentCode();
                getData(1);

            }

            //获取部门权限码(先判断是否为中心办，如果不是，在判断是否为白名单的人员)
            function getDepartmentCode(){
                   //判断是否为中心办
                   comService.isCenterOffice().then(function (res) {
                      if(res.code === "0000" && res.data.code === '01'){
                        //01全部权限
                        $scope.flag = true;
                        getData(1);
                        return;
                      }else{
                        comService.validAuthentication("0004").then(function (result) {
                             if(result.code === '0000'){
                                 if(result.data.code === '00'){
                                     $state.go('app.office.unAuthority');
                                     return;
                                 }
                                 if(result.data.code === '01'){
                                     $scope.flag = true;
                                     getData(1);
                                     return;
                                 }
                                 if (result.data.code === '03') {
                                     $scope.flag = true;
                                     $scope.areaCodeFlag = true;
                                     $('#area').attr('disabled', true);
                                     $scope.searchObject.area = result.data.areaCode;
                                     getData(1);
                                     return;
                                 }
                                $scope.flagAuth = true;
                                $scope.searchObject.primaryDept = res.data.primaryDeptCode;
                                $("#primaryDeptName").attr("disabled","disabled");
                                initSecDeptList();
                                if(res.data.departmentCode){
                                    $scope.searchObject.department = res.data.departmentCode;
                                    $("#departmentName").attr("disabled","disabled");
                                }
                                getData(1);


                             }
                          });


                      }
                   });

            }



            //打开新增窗口
            $scope.openAddModal = function() {
                   $scope.addParam = {};
            };

            //保存新增数据
            $scope.saveAddData = function () {
                var param = {
                    "area":$scope.addParam.area,
                    "department":$scope.addParam.department,
                    "employeeName":$scope.addParam.employeeName,
                    "employeeId":$scope.addParam.employeeId,
                    "onboardingTime":inform.format($scope.addParam.onboardingTime,'yyyy-MM-dd')==='NaN-NaN-NaN'?null:inform.format($scope.addParam.onboardingTime,'yyyy-MM-dd'),
                    "companyTitle":$scope.addParam.companyTitle,
                    "getTitleTime":inform.format($scope.addParam.getTitleTime,'yyyy-MM')==='NaN-NaN'?null:inform.format($scope.addParam.getTitleTime,'yyyy-MM'),
                    "getWay":$scope.addParam.getWay,
                    "titleLevel":$scope.addParam.titleLevel,
                    "paramType":$scope.addParam.paramType
                };
                staffTitleService.addTitleByParam(param).then(function (result) {
                    if (result.code === '0000') {
                       $("#add_staffInfo").modal('hide');
                       layer.msg(result.message,
                         {
                           time:1000//1秒自动关闭
                         }, function () {
                             getData(1);
                             $scope.addParam = {};
                         }
                       );

                    }else{
                        inform.common(result.message);
                    }
                   
                });

            };
            //入职时间
            $scope.openOnboardingTime = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = true;    
                $scope.openedStart = false;
                $scope.openedEnd = false;
                $scope.openOnboardTime = false; 
                $scope.openGetTitleTime1 =  false;       
            };
            //开始时间
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;    
                $scope.openedStart = true;
                $scope.openedEnd = false;
                $scope.openOnboardTime = false;
                $scope.openGetTitleTime1 = false;
            };
            //结束时间
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;    
                $scope.openedStart = false;
                $scope.openedEnd = true;
                $scope.openOnboardTime = false;  
                $scope.openGetTitleTime1 = false;              
            };

            //
             $scope.openOnboardTime3 = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;    
                $scope.openedStart = false;
                $scope.openedEnd = false;
                $scope.openOnboardTime = true;
                $scope.openGetTitleTime1 = false;
                
            };
            //获得时间
             $scope.openGetTitleTime = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;    
                $scope.openedStart = false;
                $scope.openedEnd = false;
                $scope.openOnboardTime = false;
                $scope.openGetTitleTime1 = true;
            };
            //查看职称详情
            $scope.detail = function(item) {
                   getTitleList(item.primaryDeptCode);
                   staffTitleService.selectTitleDetail(item.employeeId).then(function (result) {
                       if ( result.code === '0000') {
                          $scope.detail.employeeId = item.employeeId;
                          $scope.detail.employeeName = item.employeeName;
                          $scope.titlePersonList = result.data;
                       }else{
                           inform.common(result.message);
                       }
                   });
            };

            //打开修改窗口
            $scope.openUpdateModal = function(item) {
                  //获取系研职称信息
                  $scope.titleList = [];
                  comService.getParamList('STAFF_TITLE','NEW').then(function(data) {
                     $scope.titleList = data.data;
                     $scope.updateParam = angular.copy(item);
                     $scope.updateParam.companyTitle = item.companyTitleCode;
                     $scope.updateParam.paramType = item.paramType;
                  });
            };

            //保存修改数据
            $scope.saveUpdateData = function () {
                if($scope.updateParam.getTitleTime == null || $scope.updateParam.getTitleTime===''){
                    inform.common("请选择获得职称时间");
                    return;
                }
                var param = {
                    "id":$scope.updateParam.id,
                    "area":$scope.updateParam.area,
                    "department":$scope.updateParam.department,
                    "employeeName":$scope.updateParam.employeeName,
                    "employeeId":$scope.updateParam.employeeId,
                    "onboardingTime":inform.format($scope.updateParam.onboardingTime,'yyyy-MM-dd')==='NaN-NaN-NaN'?null:inform.format($scope.updateParam.onboardingTime,'yyyy-MM-dd'),
                    "companyTitle":$scope.updateParam.companyTitle,
                    "getTitleTime":inform.format($scope.updateParam.getTitleTime,'yyyy-MM')==='NaN-NaN'?null:inform.format($scope.updateParam.getTitleTime,'yyyy-MM'),
                    "getWay":$scope.updateParam.getWay,
                    "titleLevel":$scope.updateParam.titleLevel,
                    "paramType":$scope.updateParam.paramType
                };
                staffTitleService.updateTitleByParam(param).then(function (result) {
                    if ( result.code === '0000') {
                       $("#update_staffInfo").modal('hide');
                       layer.msg(result.message,
                             {
                               time:1000//1秒自动关闭
                             }, function () {
                                 getData(1);
                             }
                       );
                    }else{
                        inform.common(result.message);
                    }
                });

            };


            //修改详情页的时间
            $scope.updateData = function(item){
                if(item.getTitleTime==null || item.getTitleTime===''){
                    inform.common("请选择获得职称时间");
                    return;
                }else{
                    var param = {
                        "id":item.id,
                        "employeeId":item.employeeId,
                        "companyTitle":item.companyTitle,
                        "getTitleTime":inform.format(item.getTitleTime,'yyyy-MM')==='NaN-NaN'?null:inform.format(item.getTitleTime,'yyyy-MM'),
                        "getWay":item.getWay
                    };
                    staffTitleService.updateTitleById(param).then(function (result) {
                        inform.common(result.message);
                    });

                }

            };

            // 删除确认
            $scope.deleteConfirm = function (id) {
                $("#detail_staffTitle").modal('hide');
                inform.modalInstance("确定要删除吗？").result.then(function () {
                    $scope.deleteByIds(id);
                });
            };

            //根据选中的id 删除数据
            $scope.deleteByIds = function (id) {
                staffTitleService.deleteTitleByIds(id)
                    .then(function (data) {
                        if (data.code === "0000") {
                            inform.common('删除成功');
                            getData(1);
                        } else {
                            inform.common(data.message);
                        }
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            };






             //生成Excel表格
            $scope.toExcel = function() {

                inform.modalInstance("确定要下载吗?").result.then(function() {

                    var params = {
                        area:$scope.searchObject.area,
                        primaryDept:$scope.searchObject.primaryDept,
                        department:$scope.searchObject.department,
                        employeeName:$scope.searchObject.employeeName,
                        onboardingTime:$scope.searchObject.onboardTime,
                        companyTitle:$scope.searchObject.companyTitle,
                        getWay:$scope.searchObject.getWay,
                        startTime:inform.format($scope.searchObject.startTime,'yyyy-MM')==='NaN-NaN'?null:inform.format($scope.searchObject.startTime,'yyyy-MM'),
                        endTime:inform.format($scope.searchObject.endTime,'yyyy-MM')==='NaN-NaN'?null:inform.format($scope.searchObject.endTime,'yyyy-MM')
                    };
                    inform.downLoadFile('staffTitle/toExcel',params,"员工职称信息表.xlsx");

                });

            };




 /**
         * *************************************************************
         *              方法声明部分                                结束
         * *************************************************************
         */
           

        }
    ]);
})();
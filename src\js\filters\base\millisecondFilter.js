/*
* @Author: fubaole
* @Date:   2017-11-23 18:08:16
* @Last Modified by:   fubaole
* @Last Modified time: 2017-12-05 19:46:55
* 毫秒转成日期格式
* 使用方法: {{item.createTime | FmtTime:'yyyy-MM-dd hh:mm:ss'}}
*/

(function () {
  'use strict';
  app.filter("FmtTime", function() {
      return function(str,fmt) {
          if(str){
            var date = new Date(str);
            var o = {
              "M+" : date.getMonth()+1, //月份
              "d+" : date.getDate(), //日
              "h+" : date.getHours(), //小时
              "m+" : date.getMinutes(), //分
              "s+" : date.getSeconds(), //秒
            };
            if(/(y+)/.test(fmt)){
              fmt=fmt.replace(RegExp.$1, (date.getFullYear()+"").substr(4 - RegExp.$1.length));

            }
            for(var k in o){
              if(new RegExp("("+ k +")").test(fmt))
              fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));
            }
            return fmt;
          }
      };
  });

})();
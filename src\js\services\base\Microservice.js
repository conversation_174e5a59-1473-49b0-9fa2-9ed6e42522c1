/*
 * @Author: fubaole
 * @Date:   2017-08-02 11:16:34
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-02-26 18:09:54
 */
(function() {
  'use strict';
  app.factory('Microservice', Microservice);
  Microservice.$inject = ['HttpService', '$rootScope'];

  function Microservice(HttpService, $rootScope) {
    var service = {
      getMetrics: getMetrics,
      threadDump: threadDump,
      getConfigprops: getConfigprops,
      getConfigenv: getConfigenv,
      gatewayRoutes: gatewayRoutes,
      getHealth: getHealth,
      getInstances: getInstances,
      getLogsAll: getLogsAll,
      searchLogs: searchLogs,

      // monitorAPI
      getRoutes :getRoutes,
      getAccount:getAccount,
      getEurekaApplications:getEurekaApplications,
      getEruekaStatus:getEruekaStatus,
      getAllHealth:getAllHealth
    };
    return service;

    function getMetrics(appName) {
      if (appName==="gateway") {
        return HttpService.get($rootScope.gateWayApi + 'management/metrics', {});
      } else if (appName==="register") {
        return HttpService.get($rootScope.registerApi + 'management/metrics', {});
      } else {
        return HttpService.get($rootScope.gateWayApi + appName + '/management/metrics', {});
      }
    }

    function threadDump(appName) {
      if (appName==="gateway") {
        return HttpService.get($rootScope.gateWayApi + 'management/dump', {});
      } else if (appName==="register") {
        return HttpService.get($rootScope.registerApi + 'management/dump', {});
      } else {
        return HttpService.get($rootScope.gateWayApi + appName + '/management/dump', {});
      }
    }

    function getCommon(appName, url) {
      if (appName==="gateway") {
        return HttpService.get($rootScope.gateWayApi + url, {});
      } else if (appName==="register") {
        return HttpService.get($rootScope.registerApi + url, {});
      } else {
        return HttpService.get($rootScope.gateWayApi + appName + '/' + url, {});
      }
    }

    // 获取config数据
    function getConfigprops(appName) {
      return getCommon(appName, 'management/configprops');
    }
    // 获取config数据
    function getConfigenv(appName) {
      return getCommon(appName, 'management/env');
    }

    // 获取网关数据
    function gatewayRoutes() {
      return HttpService.get($rootScope.gateWayApi + 'api/gateway/routes', {});
    }

    // 获取health数据
    function getHealth(appName) {
      return getCommon(appName, 'management/health');
    }

    // 获取instances数据
    function getInstances(appName) {
      return getCommon(appName, 'api/eureka/applications');
    }

    // 获取历史数据
    // function getHistory(){
    //     return HttpService.get($rootScope.gateWayApi+'api/eureka/lastn',{});
    // }

    // 获取日志信息
    function getLogsAll(appName) {
      return getCommon(appName, 'management/logs');
    }
    // 查询日志信息
    function searchLogs(param, appName) {
      if (appName==="gateway") {
        return HttpService.put($rootScope.gateWayApi + 'management/logs', param);
      } else if (appName==="register") {
        return HttpService.put($rootScope.registerApi + 'management/logs', param);
      } else {
        return HttpService.put($rootScope.gateWayApi + appName + '/management/logs', param);
      }
    }


    // 获取登录人数据
    function getAccount(){
      return HttpService.get($rootScope.gateWayApi+'uaa/api/account',{});
    }
    // 获取服务源数据
    function getRoutes(){
      return HttpService.get($rootScope.gateWayApi+'api/routes',{});
    }
    // 获取应用数据
    function getEurekaApplications(){
      return HttpService.get($rootScope.registerApi+'/api/eureka/applications',{});
    }
    // 获取数据
    function getEruekaStatus(){
      return HttpService.get($rootScope.registerApi+'api/eureka/status',{});
    }
    // 获取服务状态数据
    function getAllHealth(){
      return HttpService.get($rootScope.gateWayApi+'management/health',{});
    }

  }
})();
(function() {
    'use strict';
  app.factory('customerStoryService', customerStoryService);
  customerStoryService.$inject=["HttpService",'$rootScope'];

  function customerStoryService(HttpService,$rootScope){
    var service={
        getCustomerStoryData:getCustomerStoryData,
        manageRelation:manageRelation,
        getSplitWorkTimeByStoryId:getSplitWorkTimeByStoryId,
        getStoryInfo:getStoryInfo,
        getSonStoryInfo:getSonStoryInfo,
        getProductList:getProductList,
        getModuleList:getModuleList,
        getCustomerStoryName:getCustomerStoryName
    };
    return service;
    /**
     * 分页查询、查询某个用户需求信息
     */
    function getCustomerStoryData(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'customerStory/getCustomerStoryData', urlData);
    }
    /**
     * 维护信息
     */
     function manageRelation(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'customerStory/manageRelation', urlData);
     }
     /**
      * 根据用户需求查询拆分工时
      */
     function getSplitWorkTimeByStoryId(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'workingHoursSplit/getSplitWorkTimeByStoryId', urlData);
     }
     /**
      * 获取产品需求信息
      */
     function getStoryInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'customerStory/getStoryInfo', urlData);
     }
     /**
      * 获取子产品需求信息
      */
     function getSonStoryInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'customerStory/getSonStoryInfo', urlData);
     }
     /**
      * 获取产品线下产品名称列表
      */
     function getProductList(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'customerStory/getProductList', urlData);
     }
     /**
      * 获取产品下模块名称列表
      */
     function getModuleList(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'customerStory/getModuleList', urlData);
     }
     /**
      * 查询用户需求名称
      */
     function getCustomerStoryName(urlData) {
         return HttpService.post($rootScope.getWaySystemApi + 'customerStory/getCustomerStoryName', urlData);
     }
  }
})();
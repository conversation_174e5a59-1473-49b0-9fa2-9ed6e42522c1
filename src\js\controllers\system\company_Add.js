/*
 * @Author: fubaole
 * @Date:   2017-09-18 14:53:05
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-19 10:09:52
 */
(function() {
  'use strict';
  app.controller("company_Add", ['$state', '$scope', '$stateParams', 'SystemService', 'inform', 'Trans', 'AgreeConstant',
    function($state, $scope, $stateParams, SystemService, inform, Trans, AgreeConstant) {
      $scope.limitList = AgreeConstant.limitList; // 正则校验配置
      $scope.resultData = {}; // 存放公司信息
      $scope.companyTypeNew = []; //  存放选中的公司类型
      $scope.onSubmit = onSubmit; // 提交
      getCompanyType(); // 获取公司类型

      // 获取公司类型
      function getCompanyType() {
        SystemService.getDictValueListByDictTypeCode("company_type")
          .then(function(data) {
            if (data.code === AgreeConstant.resultCode) {
              $scope.companyTypeData = data.result;
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 验证新增公司
      function onSubmit() {
        // 校验输入的公司名称是够已经存在
        SystemService.vaildateCompanyNameIsUsed($scope.resultData.companyName, "")
          .then(function(data) {
            if (data.code === AgreeConstant.resultCode && !data.result) {
              setData();
            } else {
              inform.common(Trans("tip.nameIsUser"));
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 获取选中的公司类型
      function updataType() {
        $scope.companyTypeNew = [];
        angular.forEach($scope.companyTypeData, function(i) {
          if (i.selected) {
            $scope.companyTypeNew.push(i.valueCode);
          }
        });
        if ($scope.companyTypeNew.length === 0) {
          inform.common(Trans('company.placeholderType'));
          return false;
        }
        return true;
      }

      // 保存修改信息
      function setData() {
        if (updataType()) {
          SystemService.saveOrUpdateCompany($scope.resultData, $scope.companyTypeNew.toString())
            .then(function(data) {
              if (data.code === AgreeConstant.resultCode) {
                inform.common(Trans("tip.saveSuccess"));
                $state.go("app.system.company_Management");
              } else {
                inform.common(data.message);
              }
            }, function() {
              inform.common(Trans("tip.requestError"));
            });
        }
      }


    }
  ]);
})();
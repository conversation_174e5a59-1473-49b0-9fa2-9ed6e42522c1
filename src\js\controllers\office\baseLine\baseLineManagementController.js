(function () {
    app.controller("baseLineManagement", ['$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant','$state','baseLineService', 'LocalCache',
        function ($rootScope, $scope, inform, Trans, AgreeConstant, $state, baseLineService, LocalCache) {

            //获取缓存
            $scope.formInput = LocalCache.getObject('baseLine_formRefer');
            //对原缓存进行覆盖
            LocalCache.setObject("baseLine_formRefer",{});

            $scope.fromList = [{name:"svn"},{name:"git"}];
            $scope.flagList = [{name:"通过"},{name:"不通过"}];
            $scope.isCheckList = [{name:"开启检查",value:"1"},{name:"屏蔽检查",value:"0"}];

            //分页数据
            $scope.pages = {
                goNum:null, // 初始化跳转页码
                star:0, //开始条数
                end:0, //结束条数
                total:0, // 总条数
                size:"50", //每页条数
                pageNum:AgreeConstant.pageNum //默认页
            };

            //设置列表的高度
            setDivHeight();
            getData(1);
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            $scope.getData = getData;

            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 210);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 75);
            }

            //重置
            $scope.reset = function() {
                $scope.formInput = {};
            }

            function getData(pageNum) {
                var urlData = $scope.formInput;

                urlData.currentPage=pageNum;//当前页数
                urlData.pageSize=$scope.pages.size;//每页显示条数
                baseLineService.getBaseLine(urlData).then(function (data) {

                    if (data.code === AgreeConstant.code) {
                        if(null==data.data){
                            $scope.itemList = {};
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages();
                            $scope.pages.size = "50";
                        } else {

                            $scope.itemList = data.data.list;
                            //分页信息设置
                            $scope.pages.total = data.data.total;
                            $scope.pages.star = data.data.startRow;
                            $scope.pages.end = data.data.endRow;
                            $scope.pages.pageNum = data.data.pageNum;
                        }
                    } else {
                        inform.common(data.message);
                    }
                },function () {
                    inform.common(Trans("tip.requestError"));
                });
            }

            $scope.showErrorMessage = function(m) {
                inform.common(m.errorMessage);
            }

            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };

            $scope.changeIsChecked = function(m) {
                var urlData = {
                    baseLineId : m.id
                }
                if (m.isCheck === '1') {
                    urlData.isCheck = '0';
                } else {
                    urlData.isCheck = '1';
                }
                baseLineService.updateBaseLineStatus(urlData).then(function (data) {
                    inform.common(data.message);
                    getData($scope.formInput.currentPage);
                },function () {
                    inform.common(Trans("tip.requestError"));
                });
            }

            $scope.copyPath = function (m) {

                //获取标签url并组装数据，多个标签以回车换行符分隔
                var content = "";
                content = content + m.path;
                //创建隐藏域，执行copy命令
                var copyElement = document.createElement("textarea");
                copyElement.style.position = 'fixed';
                copyElement.style.opacity = '0';
                copyElement.textContent = content;  //为隐藏域赋值
                var body = document.getElementsByTagName('body')[0];
                body.appendChild(copyElement);
                copyElement.select();
                try{
                    document.execCommand('copy');
                    inform.common("拷贝成功");
                }catch (e) {
                    console.log("拷贝失败,"+e)
                    inform.common("拷贝失败");
                }
                //删除隐藏域
                body.removeChild(copyElement);
            }

            $scope.showBaseLineFile = function(m) {

                $state.go(
                    'app.office.baseLineFileManagement',
                    {"baseLine":JSON.stringify(m)}
                );
                LocalCache.setObject('baseLine_formRefer', $scope.formInput);
            }
        }]);
})();
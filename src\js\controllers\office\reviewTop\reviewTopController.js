(function () {
    app.controller("reviewTop", ['comService', '$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant','$state','reviewTopService', 'LocalCache',
        function (comService, $rootScope, $scope, inform, Trans, AgreeConstant, $state, reviewTopService, LocalCache) {
            //季度下拉框数据源
            $scope.quarterSelect = [
                {
                    value: '第一季度',
                    label: '第1季度'
                }, {
                    value: '第二季度',
                    label: '第2季度'
                }, {
                    value: '第三季度',
                    label: '第3季度'
                }, {
                    value: '第四季度',
                    label: '第4季度'
                }];

            $scope.titleSelect = [
                {
                    value: '初级',
                    label: '初级'
                }, {
                    value: '中级',
                    label: '中级'
                }, {
                    value: '高级',
                    label: '高级'
                }];

            $scope.formInput = {};
            //初始化一级部门列表
            initPrimaryDeptList();
            //初始化一级部门
            $scope.formInput.primaryDept = "";
            //分页数据
            $scope.pages = {
                // 初始化跳转页码
                goNum:null,
                //开始条数
                star:0,
                //结束条数
                end:0,
                // 总条数
                total:0,
                //每页条数
                size:"50",
                //默认页
                pageNum:AgreeConstant.pageNum
            };

            //设置列表的高度
            setDivHeight();
            initTime();
            getData(1);
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            $scope.getData = getData;
            $scope.changeStatus = changeStatus;

            /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function(data) {
                    if (data.data) {
                        $scope.primaryDeptList = data.data;
                    }
                });
            }

            //修改一级部门，二级部门进行联动
            $scope.changeDept = function(){
                //获取二级部门
                $scope.deptList = [];
                comService.getOrgChildren($scope.formInput.primaryDept).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.deptList = data.data;
                    }
                });
            };
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 210);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 75);
            }

            //重置
            $scope.reset = function() {
                $scope.formInput = {};
            }

            //更改上榜状态
            function changeStatus(id,flag) {
                if(flag === '0'){
                    flag = '1'
                }else{
                    flag = '0'
                }
                var urlData = {
                    'id': id,
                    'flag': flag
                };
                reviewTopService.changeStatus(urlData).then(function(data) {
                    getData(1);
                    inform.common(data.message);
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            function getData(indexNum) {
                var urlData = {
                    //'employeeName': $scope.searchObject.name,
                    'primaryDeptCode': $scope.formInput.primaryDept,
                    'departmentCode': $scope.formInput.department,
                    'quarter': $scope.formInput.quarter,
                    'year': $scope.formInput.years,
                    'employeeTitle': $scope.formInput.title,
                    'page': indexNum,
                    'size': $scope.pages.size
                };
                //获取评审榜单数据
                reviewTopService.getReviewTop(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.itemList = data.data.list;
                        if ($scope.itemList.length === 0) {
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages(); //初始化分页数据
                        } else {
                            // 分页信息设置
                            $scope.pages.total = data.data.total; // 页面数据总数
                            $scope.pages.star = data.data.startRow; // 页面起始数
                            $scope.pages.end = data.data.endRow; // 页面结束数
                            $scope.pages.pageNum = data.data.pageNum;     
                        }
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * 初始化检索条件年度与季度
             */
            function initTime(){
                //当前时间（Date类型）
                var date = new Date();
                //得到上一个季度的第一天
                var lastQuarterFirstDay = new Date(date.getFullYear() , date.getMonth() - 3 , 1);
                var day = inform.format(lastQuarterFirstDay, 'yyyy-MM-dd').split("-");
                $scope.formInput.years = day[0];
                //设置季度
                var quarter = Math.floor((day[1] - 1)/3 + 1);
                switch (quarter){
                    case 1:
                        quarter = '一';
                        break;
                    case 2:
                        quarter = '二';
                        break;
                    case 3:
                        quarter = '三';
                        break;
                    case 4:
                        quarter = '四';
                        break;
                    default:
                        break;
                }
                $scope.formInput.quarter = '第' + quarter + '季度';
            }

        }]);
})();
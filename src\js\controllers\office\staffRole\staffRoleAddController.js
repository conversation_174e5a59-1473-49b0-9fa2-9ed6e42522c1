
(function () {
    app.controller("staffRoleAddController", ['comService','$rootScope', '$scope', '$filter','$state','$stateParams','$modal','staffRoleAddService','inform','Trans','AgreeConstant','LocalCache',
        function (comService,$rootScope, $scope, $filter, $state, $stateParams, $modal,staffRoleAddService,inform,Trans,AgreeConstant,LocalCache) {

        /**
         * *************************************************************
         *             初始化部分                                 开始
         * *************************************************************
         */
		//设置列表的高度
		 setDivHeight();
		 //窗体大小变化时重新计算高度
		 $(window).resize(setDivHeight);
		//角色下拉框列表
		 $scope.rolesList = [];
		//获取方式下拉框列表
		$scope.getTypeList = [];
    	//角色列表
        $scope.roleList = [];
		//员工下拉框列表
		$scope.employeeList = [];
		//员工角色列表
		$scope.staffAndRoleList = [];
		//下方表格所用列表
		$scope.roleAddList = [];
		//角色
		$scope.formAddRole = {};
		//备注集合
		$scope.remarksList = [];
		
		
		//新增方法
		$scope.addStaffRole = addStaffRole;
		//获取备注
		$scope.getRemarks = getRemarks;
		//返回列表
		$scope.toList = toList;
		
		
    	//初始化页面信息
    	initPages();
        /**
         * *************************************************************
         *              初始化部分                                 结束
         * *************************************************************
         */

        /**
         * *************************************************************
         *              方法声明部分                                 开始
         * *************************************************************
         */

        /**
    	 * 页面初始化
    	 */
    	function initPages() {
			$scope.roleAddList = [];
    		//获取员工信息
			$scope.employeeList = [];
			comService.getEmployeesByOrgId('').then(function (data) {
				if (data.data) {
					$scope.employeeList = data.data;
				}
			});
			//获取备注集合
			getRemarks();
			//获取角色下拉框
			$scope.rolesList = AgreeConstant.roleList;
			//获取获取方式下拉框
			$scope.getTypeList = AgreeConstant.getTypeList;
    	}

		/**
    	 * 添加员工角色
    	 */
		function addStaffRole(){
			if($scope.roleAddList.length !== 0){
				//替换时间和添加角色、申请类型
				var staffNosList = [];
				angular.forEach($scope.roleAddList,function(item){
					//判断是否有重复员工
					staffNosList.push(item.employeeNo);
					item.role = $scope.formAddRole.role;
					item.applyType = '申请';
					//填充职称和职级
					angular.forEach($scope.employeeList,function(i){
						if(i.employeeNo === item.employeeNo){
							item.title = i.companyTitle;
							item.level = i.level;
						}
					});
				});
				//判断是否有重复员工
				if(Array.from(new Set(staffNosList)).length !== $scope.roleAddList.length){
					inform.common("存在重复的员工！");
					return;
				}
			}else{
				inform.common("请添加员工！");
				return;
			}

			var urlData = {
				'staffRoleList':$scope.roleAddList
			};
			staffRoleAddService.addStaffRole(urlData).then(function (data) {
				if (data.code === AgreeConstant.code) {
					inform.common("新增角色成功");
					setTimeout(toList,500);
				}else {
					inform.common(data.message);
				}
			}, function (error) {
				inform.common(Trans("tip.requestError"));
			});

			
		}
        
    
        


		/**
		* 申请时间
		*/
		$scope.openOnboardTime = function ($event,item) {
			$event.preventDefault();
			$event.stopPropagation();
			item.openOnboardTime = true;
		};

		/**
		* 设置日期为一号
		*/
		$scope.setDate = function (item) {
			 var date = $filter('date')(item.applyDate,'yyyy-MM-dd');
			 item.applyDate = date;
		};


		/**
         * 新增一个员工
         */
		 $scope.addNewRole = function () {
			if(typeof($scope.formAddRole.role) === "undefined" || $scope.formAddRole.role === ''){
				inform.common("请先选择角色！");
				return;
			}else{
				var person = {
					'employeeNo': '',
					'applyDate': '',
					'getType': '',
					'duty': '',
					'remarks': '',
					'disabled': false
				};
				$scope.roleAddList.push(person);
				div.scrollTop = div.scrollHeight;
				
			}
            
        };
        
        /**
         * 取消一行
         */
        $scope.deleteStaffRole = function (index) {
            if (index >= 0) {
                $scope.roleAddList.splice(index, 1);
            }
        };

		//获取所有人的备注
		function getRemarks(){
			$scope.remarksList = [];
			var urlData = {
				'remarks':'1'
			}
			staffRoleAddService.selectStaffRemarks(urlData).then(function (data) {
				if (data.code === AgreeConstant.code) {
					$scope.remarksList = angular.fromJson(data.data);
					if ($scope.remarksList.length === 0) {
						inform.common(Trans("tip.noData"));
					}
				}
			});
		}

		/**
         * 填充备注
         */
		 $scope.getStaffRemarks = function (staff) {
			staff.remarks = '';
			staff.disabled = false;
			angular.forEach($scope.remarksList,function(item){
				if(item.employeeNo === staff.employeeNo){
					staff.remarks = item.remarks;
					staff.disabled = true;
				}
			})
		 }


		 	/**
			   * 跳转至列表
			*/
			function toList() {
				$state.go("app.office.staffRole");
			}


			function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - 280;
                $("#divTBDis").height(divHeight);
                $("#roleAdd").height(divHeight - 40);
            }
    	
        
     	
     	
     	
	}]);
})();
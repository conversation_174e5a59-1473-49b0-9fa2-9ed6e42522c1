(function ($) {
  // 该文件为一个JQuery插件，用于控制树状表格的展开和收缩
  $.fn.dataTable.treeGrid = function (dt, options) {
    var table = dt.table();
    // 默认配置，可自定义
    var settings = $.extend(
      {
        expandIcon: '<span style="margin: 0 3px; cursor:pointer">▶</span>',
        collapseIcon: '<span style="margin: 0 3px; cursor:pointer">▼</span>',
        // 缩进/展开标志的宽度
        iconWidth: 6,
        // 每行缩进的宽度
        childIndent: 20,
        // 需要缩进的在第几列
        treeColumn: 0,
        // 是否全部展开
        expandAll: false,
      },
      options
    );

    // 获取实际数据数组的辅助函数
    function getDataArray(data) {
      return Array.isArray(data) ? data : data.toArray();
    }

    // 递归处理数据以添加深度和父级信息
    function processData(data, depth = 0, parent = null) {
      return getDataArray(data).map((item) => {
        item.depth = depth;
        item.parent = parent;
        item.isExpanded = settings.expandAll;
        if (item.children) {
          item.children = processData(item.children, depth + 1, item);
        }
        return item;
      });
    }

    // 将层级数据扁平化为单一数组
    function flattenData(data) {
      let result = [];
      data.forEach((item) => {
        result.push(item);
        if (item.children && item.isExpanded) {
          result = result.concat(flattenData(item.children));
        }
      });
      return result;
    }

    // 添加展开/收缩图标并设置缩进
    function drawTreeGrid() {
      $('td:nth-child(' + (settings.treeColumn + 1) + ')', table.body()).each(function () {
        var td = $(this);
        var row = dt.row(td.closest('tr'));
        var data = row.data();

        // 移除现有的元素
        td.find('.tree-control, .tree-control-placeholder').remove();

        if (data.children && data.children.length) {
          var icon = $(
            '<span class="tree-control">' + (data.isExpanded ? settings.collapseIcon : settings.expandIcon) + '</span>'
          );
          td.prepend(icon);
          icon.on('click', function (e) {
            e.stopPropagation();
            toggleChildren(row);
          });
        } else {
          td.prepend('<span class="tree-control-placeholder"></span>');
        }
        // 每行缩进加上图标的宽度
        var padding = data.depth * (settings.childIndent + settings.iconWidth);
        // 设置缩进
        td.css('padding-left', padding + 'px');
      });
    }

    // 切换子项可见性
    function toggleChildren(row) {
      var data = row.data();
      data.isExpanded = !data.isExpanded;
      updateFlatData();
      dt.draw(false);
    }

    // 根据展开/收缩状态更新扁平数据数组
    function updateFlatData() {
      var newData = flattenData(initialData);
      dt.clear();
      dt.rows.add(newData);
    }

    // 初始化树形表格
    var initialData = processData(table.data());

    var flatData = flattenData(initialData);
    dt.clear().rows.add(flatData).draw();

    // 替换为使用事件监听
    dt.on('draw.treeGrid', function (e) {
      drawTreeGrid();
    });

    // 初始化时执行一次绘制
    drawTreeGrid();

    // 公共方法
    return {
      // 全部展开
      expandAll: function () {
        initialData.forEach(function expandRecursive(item) {
          item.isExpanded = true;
          if (item.children) {
            item.children.forEach(expandRecursive);
          }
        });
        updateFlatData();
        dt.draw();
      },
      // 全部收缩
      collapseAll: function () {
        initialData.forEach(function collapseRecursive(item) {
          item.isExpanded = false;
          if (item.children) {
            item.children.forEach(collapseRecursive);
          }
        });
        updateFlatData();
        dt.draw();
      },
      // 添加自定义图标的方法
      setIcons: function (expandIcon, collapseIcon) {
        settings.expandIcon = expandIcon;
        settings.collapseIcon = collapseIcon;
        dt.draw();
      },
    };
  };
})(jQuery);

(function () {
    'use strict';
    app.factory('power_ManagementService', power_ManagementService);
    power_ManagementService.$inject = ["HttpService", '$rootScope'];

    function power_ManagementService(HttpService, $rootScope) {

        var service = {

            updateData: updateData,
            getData: getData

        };

        return service;

        /**
         * 修改权限菜单后端mapping
         */
        function updateData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'powerManagement/updateMapping', urlData);
        }

        /**
         * 查询权限菜单后端mapping
         */
        function getData(id) {
            return HttpService.get($rootScope.getWaySystemApi + 'powerManagement/getMappingById?id=' + id);
        }

    }
})();
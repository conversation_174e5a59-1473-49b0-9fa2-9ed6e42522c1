(function () {
    app.controller("personalBadgeController", [ '$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'personalBadgeService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function ( $rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, personalBadgeService, inform, Trans, AgreeConstant, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //页面数据
			$scope.formRefer = {};
            //标记是否是在初始化时设置快捷按钮样式
            $scope.initFlag=0;
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.getData = getData;
            $scope.timeSelect = ['本年度徽章','全部徽章'];
            $scope.orderList = ['一','二','三','四','五','六'];
            $scope.initTime = initTime;
            //初始化信息
            initData();

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 初始化
             */
            function initData() {
                //获取当前登录者的empId
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ?
                $scope.sessionEmpId : $stateParams.empId;
                //默认查看本年度数据
                initTime('本年度徽章');
                
            }

            //初始化时间
            function initTime(flag){
                $scope.butFlag = flag;
                var date = new Date();
                var year = date.getFullYear();  //当前年份
                var month = date.getMonth();
                if('本年度徽章' === $scope.butFlag){
                    $scope.formRefer.year = year;
                    $scope.formRefer.month = month;
                    $scope.formRefer.startDate = $scope.formRefer.year + "-01-01";
                    $scope.formRefer.endDate = $scope.formRefer.year + "-12-31";
                }
                if('全部徽章' === $scope.butFlag){
                    $scope.formRefer.year = '';
                    $scope.formRefer.month = '';
                    $scope.formRefer.startDate = '';
                    $scope.formRefer.endDate = '';
                }
                if($scope.initFlag === 0){
                    setTimeout(setButton,500);
                }else {
                    setButton();
                }
                getData();
            }

            function getData(){
                //查询条件
                $scope.urlData={
                    'employeeId':$scope.formRefer.empId,
                    // 'employeeId':"100059",
                    'year':$scope.formRefer.year,
                    'month':$scope.formRefer.month,
                    'startDate':$scope.formRefer.startDate,
                    'endDate':$scope.formRefer.endDate,
                }
                selectBadge();
                selectBadgeDetail();
            }

            //获取徽章数据
            function selectBadge(){
                personalBadgeService.selectBadge($scope.urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        $scope.badge = data.data;
                        getTotalNum($scope.badge);
                    } else {
                        inform.common(data.message);
                    }
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            //获取徽章总数
            function getTotalNum(data){
                $scope.totalNum = 0;
                angular.forEach(data, function (eachData) {
                    //获取月份，月排名
                    $scope.totalNum  += parseInt(eachData.badgeNum);
                });
            }
            //获取徽章明细数据
            function selectBadgeDetail(){
                personalBadgeService.selectBadgeDetail($scope.urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        $scope.badgeDetail = data.data;
                        //添加图片url属性
                        angular.forEach($scope.badgeDetail, function (eachData) {
                            getImg(eachData.badgeDetailList);
                           
                        });
                        //日期格式化
                        angular.forEach($scope.badgeDetail, function (eachData) {
                            dateFormat(eachData.badgeDetailList);
                        });
                        //质量徽章分成2部分
                        doQualityBadge($scope.badgeDetail);
                    } else {
                        inform.common(data.message);
                    }
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
                
                
            }

            //日期格式化
            function dateFormat(badgeDetailList){
                angular.forEach(badgeDetailList, function (eachData) {
                    eachData.badgeDate = inform.format(eachData.badgeDate,'yyyy年MM月dd日');
                });
            }

            //质量徽章
            function doQualityBadge(badgeDetail){
                //获取质量徽章数据
                angular.forEach(badgeDetail, function (eachData) {
                    if(eachData.badgeType === '质量徽章'){
                        $scope.qualityBadgeDetail = eachData.badgeDetailList;
                    }
                });
                //将质量徽章分成2部分
                angular.forEach($scope.qualityBadgeDetail, function (eachData) {
                    if(eachData.badgeCode === 'QUALITY_2_PROBLEM_0'){
                        $scope.qualityBadgeSplitIndex = eachData.pageIndex;
                    }
                });
            }

            //设置按钮颜色
            function setButton(){
                $scope.initFlag=1;
                //获取所有快捷按钮
                var buttonBoxes = document.getElementsByName("buttons");
                angular.forEach(buttonBoxes, function (but) {
                    if($scope.butFlag === but.id){
                    $("#"+but.id).css("background-color", "#0D0830");
                    $("#"+but.id).css("border", "1px solid #E0B06E");
                    $("#"+but.id).css("z-index", "1");
                    }else{
                    $("#"+but.id).css("background-color", "#6C707B");
                    $("#"+but.id).css("border", "none");
                    $("#"+but.id).css("z-index", "0");
                    }
                });
            }
            
            $scope.showBadgeInfo = function showBadgeInfo(badgeInfo){
                $scope.badgeInfo = badgeInfo;
            }
            //获取徽章图片路径
            function getImg(nameList){
                angular.forEach(nameList, function (eachData) {
                    //图片路径
                    eachData.imgUrl = badgeImgConfig[alreadyGet[eachData.badgeNum]][eachData.badgeCode];
                });
            }

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 210);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 40);
                $("#divTBDis1").height(divHeight);
                $("#subDivTBDis1").height(divHeight - 40);
            }

            $scope.goback = function () {
                $state.go('app.office.personKpi');
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

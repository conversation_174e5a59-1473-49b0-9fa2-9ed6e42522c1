(function() {
    'use strict';
    app.controller("model_ManagementDetail", ['$scope', '$rootScope', '$stateParams', 'inform', '$modal', 'SystemService', 'Trans', 'AgreeConstant',
        function($scope, $rootScope, $stateParams, inform, $modal, SystemService, Trans, AgreeConstant) {
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.getData = getData; // 初始化函数
            $scope.popModal = popModal; // 修改弹框
            $scope.onSubmit = onSubmit; // 保存修改字典值操作
            $scope.open = open; // 删除数据弹框
            getParentValue(); // 获取父级字典值

            // 获取父级字典值
            function getParentValue() {
                SystemService.getDictValueByMap({})
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.parentData = data.result;
                            $scope.getData();
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 根据TypeCode 获取字典值
            function getData() {
                SystemService.getDictValueListByDictTypeCode($stateParams.typeCode)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.resultData = data.result;
                            if ($scope.resultData.length===0) {
                                inform.common(Trans("tip.noData"));
                            }
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 修改弹框
            function popModal(item) {
                $scope.modal = angular.copy(item);
            }

            // 保存修改字典值操作
            function onSubmit() {
                if (!$scope.modal.parentId) {
                    $scope.modal.parentId = AgreeConstant.dicParentId;
                }
                SystemService.saveOrupdateDictValue($scope.modal)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            inform.common(Trans("tip.saveSuccess"));
                            $('#edit_modal').modal('hide');
                            getData();
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 删除数据弹框
            function open(item) {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function() {
                            return Trans('common.deleteTip');
                        }
                    }
                });

                modalInstance.result.then(function() {
                    if (item) {
                        SystemService.removeDictValue(item.valueId)
                            .then(function(data) {
                                if (data.code===AgreeConstant.resultCode) {
                                    getData();
                                } else {
                                    inform.common(data.message);
                                }
                            }, function(error) {
                                inform.common(Trans("tip.requestError"));
                            });

                    }
                });
            }

        }
    ]);
})();
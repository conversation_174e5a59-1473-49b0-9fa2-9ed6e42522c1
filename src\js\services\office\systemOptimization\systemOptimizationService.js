(function () {
    'use strict';
    app.factory('systemOptimizationService', systemOptimizationService);
    systemOptimizationService.$inject = ["HttpService", '$rootScope'];

    function systemOptimizationService(HttpService, $rootScope) {
        var service = {
            loadExcel: loadExcel,
            getInfoWithPage: getInfoWithPage,
            deleteInfo:deleteInfo,
            addOrUpdateInfo:addOrUpdateInfo
        };
        return service;

        /**
         * 根据参数查询体系优化信息
         * @param urlData 查询参数
         */
        function getInfoWithPage(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'systemOptimization/getInfoWithPage', urlData);
        }

        /**
         * 删除体系优化信息
         * @param urlData 查询参数
         */
        function deleteInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'systemOptimization/deleteInfo', urlData);
        }

        /**
         * 新增或修改体系优化信息
         * @param urlData 查询参数
         */
        function addOrUpdateInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'systemOptimization/addOrUpdateInfo', urlData);
        }

        /**
         * 生成表格
         * @param urlData 查询参数
         */
        function loadExcel(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'systemOptimization/loadExcel', urlData);
        }


    }
})();
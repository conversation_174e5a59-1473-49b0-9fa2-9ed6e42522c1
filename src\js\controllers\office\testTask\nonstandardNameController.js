(function () {
    app.controller("nonstandardNameController", ['testTaskService', '$rootScope', '$scope','$state', 'inform', 'Trans', 'AgreeConstant', 'comService', 'LocalCache',
        function (testTaskService, $rootScope, $scope,$state, inform, Trans, AgreeConstant,comService,LocalCache) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.formInput = {};
           //默认开始时间
            $scope.formInput.time = '2020-06-22';
            $scope.pages = {
                goNum:null, // 初始化跳转页码
                star:0, //开始条数
                end:0, //结束条数
                total:0, // 总条数
                size:'20', //每页条数
                pageNum:AgreeConstant.pageNum //默认页
            };
         // 分页相关函数
            $scope.getData = getData; 			
          //在刷新页面时调用该方法
            getData($scope.pages.pageNum);		
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
            };
            
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 150);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 65);
            }

            //重置
            $scope.rest = function () {
                $scope.formInput = {};
            };

            //获取所有数据以分页的形式
            function getData(pageNum) {
                $scope.itemList = [];
                var urlData = {
                    'startTime': inform.format($scope.formInput.time, 'yyyy-MM-dd'), //结束时间
                    'page': pageNum, 							// 分页页数
                    'pageSize': $scope.pages.size    					// 分页每页大小
                };
                testTaskService.getNonstandardInfo(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            var jsonData = data.data;
                            $scope.itemList = jsonData.list;
                            if ($scope.itemList.length === 0) {
                                $scope.pages = inform.initPages(); 			//初始化分页数据
                                $scope.pages.size = '20';
                            } else {
                                // 分页信息设置
                                $scope.pages.total = jsonData.total;		// 页面总数
                                $scope.pages.star = jsonData.startRow;  	//页面起始数
                                $scope.pages.end = jsonData.endRow;  		//页面大小数
                                $scope.pages.pageNum = jsonData.pageNum;  	//页面页数
                            }
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            $scope.back=function(){
            	$state.go("app.office.testTask");
            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
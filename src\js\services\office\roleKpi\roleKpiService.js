(function() {
    'use strict';
  app.factory('roleKpiService', roleKpiService);
  roleKpiService.$inject=["HttpService",'$rootScope'];

  function roleKpiService(HttpService,$rootScope){

	var service={
			selectData:selectData,
			upInfo:upInfo,
			selectOne:selectOne,
			getRoleKpiList:getRoleKpiList,
			delInfo:delInfo,
			addInfo:addInfo,
            saveRoleKpi: saveRoleKpi,
            selectPersonalData: selectPersonalData,
            save:save,
            resetData: resetData,
            getPersonDate: getPersonDate,
            selectOneRolePersonData: selectOneRolePersonData,
            upRolePersonData: upRolePersonData,
            selectPersonalKpi: selectPersonalKpi
	};
    return service;

      /**
       * 查询考核结果
       * @param urlData
       * @returns {*}
       */
    function selectPersonalKpi(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffPerform/selectPersonalKpi', urlData);
    }
    /**
     * 配置考核指标
     */
    function save(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'roleKpi/save', urlData);
    }
    /**
     * 获取所有的信息
     */
    function selectData(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'roleKpi/selectData', urlData);
    }

      /**
       *
       * 查询个人数据
       * @param urlData
       * @returns {*}
       */
    function selectPersonalData(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'roleKpi/selectPersonalData', urlData);
    }
    /**
     * 更新信息
     */
    function upInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'roleKpi/upInfo', urlData);
    }
    /**
     * 新增信息
     */
    function addInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'roleKpi/addInfo', urlData);
    }
    /**
     * 删除信息
     */
    function delInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'roleKpi/delInfo', urlData);
    }
    /**
     * 回填信息
     */
    function selectOne(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'roleKpi/selectOne', urlData);
    }
    /**
     * 获取指标
     */
    function getRoleKpiList(){
        return HttpService.post($rootScope.getWaySystemApi + 'roleKpi/getRoleKpiList');
    }
    /**
     * 更新信息
     */
    function saveRoleKpi(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'roleKpi/saveRoleKpi', urlData);
    }

    function resetData(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'roleKpi/resetRoleEmployee', urlData);
    }

    function getPersonDate(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'roleKpi/getPersonDate', urlData);
    }
    function selectOneRolePersonData(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'roleKpi/getOneRolePersonData', urlData);
    }
    function upRolePersonData(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'roleKpi/upRolePersonData', urlData);
    }
  }
})();

(function () {
    app.controller("depContribution", ['comService', '$rootScope', '$scope', 'depContributionService', '$state', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, depContributionService, $state, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //查询条件
            $scope.formRefer = {};
            //是否从我的星星跳转标志
		    $scope.formRefer.flag = '0';
		    //获取缓存
		    $scope.formRefer = LocalCache.getObject('departmentContribution_formRefer');
		    if($scope.formRefer.flag === '1'){
			    $scope.formRefer.startTime = $scope.formRefer.startDate;
			    $scope.formRefer.endTime = $scope.formRefer.endDate;
			    $scope.formRefer.employee = $scope.formRefer.employeeName;
			    console.log($scope.formRefer);
		    }
		    //清除缓存
		    LocalCache.setObject('departmentContribution_formRefer', {}); 	
            //部门贡献信息
            $scope.tableData = [];
            //分页
            $scope.pages = inform.initPages();
            $scope.pages.size = "20";
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            // 设置滚动条高度
            $scope.formRefer.subDivTBDisScrollTop = $('#subDivTBDis').scrollTop();
            //初始化
            initInfo();
            $scope.getData = getData;
            // 设置二级部门
            $scope.getTwoDepartment = getTwoDepartment;
            if (null != $scope.formRefer.oneDepartment && $scope.formRefer.oneDepartment !== '') {
                getTwoDepartment();
            }
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $("#divTBDisDetail").height(divHeight);
                $("#subDivTBDisDetail").height(divHeight - 35);
            }

            /**
             * 初始化
             */
            function initInfo() {
                if($scope.formRefer.flag !== '1'){
                    initTime();
                }
                //获取一级部门
                $scope.oneDepartmentList = [];
                comService.getOrgChildren('0002').then(function (data) {
                    if (data.data) {
                        $scope.oneDepartmentList = data.data;
                    }
                });
                getData();
            }

            function getTwoDepartment() {
                //获取二级部门
                $scope.departmentList = [];
                comService.getOrgChildren($scope.formRefer.oneDepartment).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.departmentList = data.data;
                    }
                });
            }

            /**
             * 获取信息
             */
            function getData(pageNum) {
                var urlData = $scope.formRefer;
                //当前页数
                urlData.currentPage = pageNum;
                //每页显示条数
                urlData.pageSize = $scope.pages.size;
                depContributionService.getData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null == data.data) {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages();
                                $scope.pages.size = "20";
                            } else {
                                //部门贡献信息
                                $scope.tableData = data.data.list;
                                //分页信息设置
                                $scope.pages.total = data.data.total;
                                $scope.pages.star = data.data.startRow;
                                $scope.pages.end = data.data.endRow;
                                $scope.pages.pageNum = data.data.pageNum;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            //开始时间
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;    //开始时间
                $scope.openedEnd = false;
                $scope.up = false;
                $scope.add = false; //新增时间
            };

            //结束时间
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;    //结束时间
                $scope.up = false;
                $scope.add = false; //新增时间
            };

            /**
             * 初始化检索条件开始时间
             */
            function initTime() {
                var date = new Date();
                //开始日期向前推3个月
                date.setMonth(date.getMonth() - 3);
                //对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
                $scope.formRefer.startTime = inform.format(date, 'yyyy-MM-dd');
            }

            /**
             * excel下载
             */
            $scope.toExcel = function () {
                inform.modalInstance("确定要下载吗?").result.then(function () {
                    inform.downLoadFile('contribution/toExcel', $scope.formRefer, '部门贡献信息.xlsx');
                });
            }

            /**
             * 修改信息弹框，str存在，就是新增
             */
            $scope.popModal = function (item, str) {
                if (str === "0") {
                    //新增
                    $scope.contributionInfoParam = {};
                } else {
                    //修改
                    $scope.contributionInfoParam = angular.copy(item);
                }
                $state.go('app.office.depContributionOperate', {
                    contributionInfoParam: JSON.stringify($scope.contributionInfoParam),
                    isAdd: str
                });
            };

            /**
             * 删除数据
             */
            $scope.removeData = function (item) {
                var id = {
                    'id': item.id
                };
                depContributionService.delData(id)
                    .then(function (data) {
                        if (data.code === "0000") {
                            inform.common(Trans("tip.delSuccess"));
                            getData();
                        } else {
                            inform.common(data.message);
                        }
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            };

            /**
             *删除弹框
             */
            $scope.open = function (item) {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function () {
                            return Trans("common.deleteTip");
                        }
                    }
                });
                modalInstance.result.then(function () {
                    if (item !== null && item !== "") {
                        $scope.removeData(item);
                    }
                });
            };

            /**
             * 重置
             */
            $scope.reset = function () {
                $scope.formRefer = {};
                initTime();
            };
            //页面跳转到我的星星--我的星星明细部门贡献详情页
		    $scope.getBack = function(){
			    $state.go('app.personal_star_department_detail');
		    }

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

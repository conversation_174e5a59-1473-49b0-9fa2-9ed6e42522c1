(function () {
    app.controller("wcpLoginController", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,inform,Trans,AgreeConstant,LocalCache,$http) {
       	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		// 显示遮罩层
        inform.showLayer("跳转中...")
    	var info={
    		'flag':'true'
    	}
    	//设置时间戳（获取第一次登录时间）
    	var timeStamp=new Date().getTime();
    	LocalCache.setSession('wcpLoginTime',timeStamp);
    	//设置登陆过
    	LocalCache.setObject("wcpManagement_wcpLoginFlag",info);
    	var urlVal = $rootScope.wcpSystemApi+"login/websubmit.do";
    	var userName = LocalCache.getSession('currentUserName');
    	$("#nameObj").val(userName);
    	var passwordVal = $rootScope.RSADecrypt(LocalCache.getSession('OAPassword'));
    	$("#pwdObj").val(passwordVal);
    	$("#wcpLoginForm").attr("action",urlVal);
    	$("#wcpLoginForm").submit();
    	//发送请求
    	var frame = document.getElementById('wcpLoginPage');
    	frame.onload= function(){
    		//根据返回路由的缓存，进行返回原先的界面
    		var goUrl = LocalCache.getObject('wcpManagement_return');
    		$state.go(goUrl);
    	};
//    	var urlVal = $rootScope.wcpSystemApi+"login/officesubmit.do?name="
//    	+LocalCache.getSession('currentUserName')
//    	+"&password="+encodeURIComponent('zzg@521P');
    	//var str = encodeURIComponent(urlVal);

    	//frame.src = urlVal;
    	//frame.src = $rootScope.wcpSystemApi+"login/officesubmit.do?name=sysadmin&password=e5abe6f0ebd707532e3d6c131f2e00f0";

    	/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */

        /**
	 	 * *************************************************************
	 	 *              方法声明部分                                 结束
	 	 * *************************************************************
	 	 */	
		
	}]);
})();
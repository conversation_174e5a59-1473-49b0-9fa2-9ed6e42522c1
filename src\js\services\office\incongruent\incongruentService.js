//# sourceURL=js/services/office/incongruent/incongruentService.js
(function() {
    'use strict';
  app.factory('incongruentService', incongruentService);
  incongruentService.$inject=["HttpService",'$rootScope'];

  function incongruentService(HttpService,$rootScope){
    
    var service={
    		getProjectInfo:getProjectInfo,
    		updateProjectInfo:updateProjectInfo,
    		getProjectDetailsInformation:getProjectDetailsInformation,
    		addProjectDetailInfo:addProjectDetailInfo,
    		updateProjectDetailInfo:updateProjectDetailInfo,
    		deleteProjectDetailInfo:deleteProjectDetailInfo,
    		toExcel:toExcel,
    		getProjectsDetailsInformation:getProjectsDetailsInformation
    };
    return service;
    /**
     * 获取项目列表
     */
    function getProjectInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'incongruent/getProjectInfo', urlData);
    }
   /**
    * 修改项目信息
    */
   function updateProjectInfo(urlData) {
       return HttpService.post($rootScope.getWaySystemApi + 'incongruent/updateProjectInfo', urlData);
   }
   /**
    * 获取项目下的问题
    */
   function getProjectDetailsInformation(urlData) {
       return HttpService.post($rootScope.getWaySystemApi + 'incongruent/getProjectDetailsInformation', urlData);
   }
   /**
    * 获取所有项目下的问题
    */
   function getProjectsDetailsInformation(urlData) {
       return HttpService.post($rootScope.getWaySystemApi + 'incongruent/getProjectsDetailsInformation', urlData);
   }
   /**
    * 新增项目下的问题
    */
   function addProjectDetailInfo(urlData) {
       return HttpService.post($rootScope.getWaySystemApi + 'incongruent/addProjectDetailInfo', urlData);
   }
   /**
    * 修改项目下的问题
    */
   function updateProjectDetailInfo(urlData) {
       return HttpService.post($rootScope.getWaySystemApi + 'incongruent/updateProjectDetailInfo', urlData);
   }
   /**
    * 删除项目下的问题
    */
   function deleteProjectDetailInfo(urlData) {
       return HttpService.post($rootScope.getWaySystemApi + 'incongruent/deleteProjectDetailInfo', urlData);
   }
   /**
    * 进行下载
    */
   function toExcel(urlData) {
   	return HttpService.post($rootScope.getWaySystemApi + 'incongruent/toExcel', urlData);
   }
  }
})();
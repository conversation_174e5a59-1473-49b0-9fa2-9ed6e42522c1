(function() {
    'use strict';
    app.factory('kpiAttendanceService', kpiAttendanceService);
    kpiAttendanceService.$inject=["HttpService",'$rootScope'];

    function kpiAttendanceService(HttpService,$rootScope){

        return {
            getPersonAttendanceChartInfo: getPersonAttendanceChartInfo,
            getPersonAttendanceList: getPersonAttendanceList,
        };

        function getPersonAttendanceChartInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'personalDataBoard/getPersonAttendanceChartInfo', urlData);
        }

        function getPersonAttendanceList(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'personalDataBoard/getPersonAttendanceList', urlData);
        }
    }
})();
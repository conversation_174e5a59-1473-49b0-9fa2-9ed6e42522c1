/**
** 参数配置新增页面
**/
(function(){
   'use strict'
   app.controller("paramAddController",['$scope','$state','paramService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','$stateParams','LocalCache', '$modal','$http',
   function ($scope,$state,paramService, $rootScope, inform, Trans, AgreeConstant,$stateParams,LocalCache, $modal,$http) {
   /**
     * *************************************************************
     *             初始化部分                                 开始
     * *************************************************************
     */
   $scope.limitList = AgreeConstant.limitList; // 正则校验配置

   //设置列表的高度
   setDivHeight();
   //窗体大小变化时重新计算高度
   $(window).resize(setDivHeight);


   //新增时明细列表
   $scope.jList = [];
   $scope.itemList = [];
   /**
     * *************************************************************
     *              初始化部分                                 结束
     * *************************************************************
     */

    /**
     * *************************************************************
     *              方法声明部分                                 开始
     * *************************************************************
     */

    /**
    * 设置列表的高度
    */
    function setDivHeight(){
        //网页可见区域高度
        var clientHeight = document.body.clientHeight;
        var divHeight = clientHeight - (150 + 180);
        $("#divTBDis").height(divHeight);
        $("#subDivTBDis").height(divHeight - 50);
    }




    //返回主页面
    $scope.goBack = function(){
        $state.go('app.office.paramController');
    }





    /**
     * 新增一个数据字典明细
     */
    $scope.addNewBind = function (index) {

        //参数明细
        let judge = {
            //'paramTypeName':'',//字典名称
            'paramName':'',
            'paramTypeCode': '',
            'paramCode': '',//参数编码
            'paramValue': '',//参数名称
            'paramDesc': '',//描述
            'sort': '', //排列顺序
            'paramStatus':"0",//参数状态 默认为0，启用
            'isSystem':"1",//是否为系统参数 默认为1，业务参数
            'createUser':LocalCache.getSession('currentUserName')
        };
        $scope.jList.push(judge);
        $scope.itemList.push(judge);

    };
    //取消一行
    $scope.deleteNewBind = function (index) {
        if (index >= 0) {
            $scope.jList.splice(index, 1);
            $scope.itemList.splice(index, 1);
        }
    };

    /**
      * 保存信息
      */
     $scope.saveInfo = function () {
         if ($scope.jList.length===0){
             inform.common("请输入字典明细");
             return;
         }
         var list = [];
         $scope.flagList = true;

         //循环明细，查看是否有重复信息
          angular.forEach($scope.jList, function (one, index) {
             one.paramName = $scope.add.paramCode;
             one.paramTypeCode = $scope.add.paramCode;
              var paramValue = one.paramValue;

              //查看list中不存在 子类型编码-排列顺序
              var num = list.indexOf(paramValue);
              if (num > -1){
                 $scope.flagList = false;
                 return;
              }

              list.push(paramValue);
          });
          //如果存在重复信息
          if ($scope.flagList===false){
              inform.common("明细中【子字典名称】存在重复信息,请修正。");
              return;
          }

         let urlData = {
             'paramCode': $scope.add.paramCode,
             'paramValue': $scope.add.paramValue,
             'paramDesc': $scope.add.paramDesc,
             'paramList':$scope.itemList
         };
         //新增
         addInfo(urlData);

     };

     /**
       * 添加信息
       */
      function addInfo(urlData) {
         paramService.addParam(urlData).then(function (data) {
              if (data.code === AgreeConstant.code) {
                  layer.confirm(data.message,{
                      title:false,
                      btn:['确定']
                  },function(result){
                      layer.close(result);
                      $scope.goBack();
                  });
              } else {
                  inform.common(data.message);
              }
         }, function (error) {
              inform.common(Trans("tip.requestError"));
         });
       }



    //计算赋值方法
    $scope.setNum = function(item,flag){
         if (flag==='+') {
             item.sort = parseInt(item.sort) + 1;
         } else if (flag==='-') {
             item.sort =(parseInt(item.sort) - 1)>0?(parseInt(item.sort) - 1):1;
         }
    }



    //保存新增参数
    $scope.saveAddData = function () {

        var param = {
            'paramCode': $scope.update.paramCode,
            'paramValue': $scope.update.paramValue,
            'paramTypeName': $scope.update.paramTypeName,
            'paramDesc': $scope.update.paramDesc,
            'sort': $scope.update.sort

        };
        paramService.addTitleByParam(param).then(function (result) {
            if (result.code==='0000') {
               $("#add_staffInfo").modal('hide');
               layer.msg(result.message,
                 {
                   time:1000,//1秒自动关闭
                 }, function () {
                     getData();
                     $scope.addParam = {};
                 }
               );

            }else{
                inform.common(result.message);
            }

        });

    }





   }
   ]);
})();
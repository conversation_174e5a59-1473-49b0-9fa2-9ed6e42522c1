var gulp = require('gulp');
var browserSync = require('browser-sync').create();
var cache = require('gulp-cache');

// 文件路径配置
var paths = {
    html: ['src/index.html', 'src/tpl/*.html', 'src/tpl/**/*.html'],
    css: 'src/css/**/*.css',
    js: ['src/js/**/*.js', 'src/js/*.js', 'src/js/**/**/*.js'],
    img: 'src/img/**/*',
    library: 'src/library/**/*',
    swaggerUi: 'src/swagger-ui/**/*',
    i18n: 'src/i18n/**/*',
    vueComponents: 'src/library/vue-components/**/*',
};

// 清除缓存任务
function clearCache(done) {
    return cache.clearAll(done);
}

// 启动服务器任务
function serve(done) {
    browserSync.init({
        server: {
            baseDir: 'src',
            directory: true,
        },
        port: 8000,
        host: '127.0.0.1',
        open: 'external',
        notify: false,
        startPath: '/index.dev.html',
    });
    done();
}

// 监视任务
function watchFiles() {
    gulp.watch(paths.html).on('change', browserSync.reload);
    gulp.watch(paths.css).on('change', browserSync.reload);
    gulp.watch(paths.js).on('change', browserSync.reload);
    gulp.watch([paths.img, paths.library, paths.swaggerUi, paths.i18n, paths.vueComponents]).on(
        'change',
        browserSync.reload
    );
}

// 默认任务
gulp.task('default', gulp.series(clearCache, serve, watchFiles));

/*
 * @Author: fubaole
 * @Date:   2017-09-18 14:53:05
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-01-15 17:20:32
 */
(function () {
    app.controller("codeManagement", ['comService', '$rootScope', '$scope', 'codeService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, codeService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {
    	$scope.isSetting = false;
    	$scope.scoreDetailLanguage = 'java';
    	//页面查询条件
        $scope.formInsert = {
            language: '',	//语言
            projectName: '',//项目名查询
            department: ''//部门名称
        };
        $scope.orderType = 'desc';
        $scope.ascColor = '';
        $scope.descColor = 'red';
    	//获取缓存
    	$scope.formInsert = LocalCache.getObject('code_formRefer');
    	//对原缓存进行覆盖
    	LocalCache.setObject("code_formRefer",{});
    	$scope.riskSelectFlag = false;
    	//风险选择框
    	$scope.riskFlagSelect=[
            {
                value: '1',
                label: '有风险'
            },{
                value: '0',
                label: '无风险'
            }];
           

            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 140);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 80);
            }

            $scope.languageList = ['java', 'C++', 'javaScript', 'Android', 'C#'];

            //页面分页信息
            $scope.pages = {
                pageNum: '',	//分页页数
                size: '',		//分页每页大小
                total: ''		//数据总数
            };
            //项目生成报告的类型
            $scope.excelType = ['java', 'C++', 'javaScript', 'Android', 'C#','无关联'];
            //初始化分页数据
            $scope.pages = inform.initPages();
            $scope.getData = getData;
            //保存项目信息集合
            $scope.projectList = [];
            $scope.languageList = [];
            $scope.number = $scope.pages.pageNum;
            $scope.avgScore = 0;
            //获取数据
            getData($scope.pages.pageNum);
            //判断是否具有权限
            getButtonPermission();

             /**
              * 获取按钮权限
              */
             function getButtonPermission(){
                 var buttons = {
                     'Button-codeController-riskSelectFlag':'riskSelectFlag'
                 };
                 var urlData = {
                     'userId':LocalCache.getSession("userId"),
                     'parentPermission':'ButtoncodeConfigController',
                     'buttons':buttons
                 };
                 comService.getButtonPermission(urlData,$scope);
             }
            //重置
            $scope.rest = function () {
                $scope.formInsert.language = "";
                $scope.formInsert.projectName = "";
                $scope.formInsert.pm = "";
                $scope.formInsert.department = "";
                $scope.formInsert.riskFlag = "";
            };
            $scope.goOrder = function(type){
                if(type === 'asc'){
                    $scope.ascColor = "red";
                    $scope.descColor = "";
                }else{
                    $scope.ascColor = "";
                    $scope.descColor = "red";
                }
                $scope.orderType = type;
                getData(1);
            }
            //被选择项目集合
            $scope.projectSelected = [];
            $scope.selectAll = selectAll;
            // 全选函数
            function selectAll() {
                if ($scope.select_all) {
                    $scope.projectSelected = [];
                    angular.forEach($scope.projectList, function (i) {
                        i.checked = true;
                        $scope.projectSelected.push(i.name + ',' + i.type);
                    });
                    console.log($scope.projectSelected.length);
                } else {
                    angular.forEach($scope.projectList, function (i) {
                        i.checked = false;
                    });
                    $scope.projectSelected = [];
                }
            }

            //单选项目
            $scope.selectOne = function (i) {
                $scope.select_all = false;
                if (i.type !== "") {
                    var index = $scope.projectSelected.indexOf(i.name + ',' + i.type);
                    if (index === -1 && i.checked) {
                        $scope.projectSelected.push(i.name + ',' + i.type);
                    } else if (index !== -1 && !i.checked) {
                        $scope.projectSelected.splice(index, 1);

                    }
                } else {
                    inform.common(Trans("请在配置页面配置开发模型！"));
                }

            };
            //更新模板类型
            $scope.updateOne = function (i) {
                angular.forEach($scope.projectSelected, function (obj, index) {

                    var projectName = obj.substring(0, obj.indexOf(","));
                    if ((i.name === projectName) && i.type !== "") {
                        $scope.projectSelected.splice(index, index + 1);
                        $scope.projectSelected.push(i.name + ',' + i.type);
                    } else {
                        inform.common(Trans("请在配置页面配置开发模型！"));
                    }
                })
            };
            $scope.condition = {};

            //计算得分
            $scope.toScore = function () {

                if ($scope.projectSelected.length !== 0) {
                    var modalInstance = $modal.open({
                        templateUrl: 'myModalContent.html',
                        controller: 'ModalInstanceCtrl',
                        size: "sm",
                        resolve: {
                            items: function () {
                                return "确定要更新吗！";
                            }
                        }
                    });
                    modalInstance.result.then(function () {
                        inform.showLayer("更新中。。。。。。");
                        $http.post(
                            $rootScope.getWaySystemApi + 'code/toScore',
                            $scope.projectSelected,
                            {
                                headers: {
                                    'Content-Type': 'application/json',
                                    'Authorization': 'Bearer' + LocalCache.getSession("token") || ''
                                },
                                responseType: 'json'
                            }
                        ).success(function (data) {
                            if (data.code === AgreeConstant.code) {
                                angular.forEach(data.data, function (res, index) {

                                    res.score = Math.round(res.score);
                                    res.scoreForSornar = Math.round(res.scoreForSornar);
                                    res.scoreForJudges = Math.round(res.scoreForJudges);
                                    angular.forEach($scope.projectList, function (rowObj, index) {
                                        //更新对应的分数
                                        if (rowObj.name === res.name) {
                                            rowObj.scoreForSornar = res.scoreForSornar;
                                            rowObj.scoreForJudges = res.scoreForJudges;
                                            rowObj.score = res.score;
                                            rowObj.bugs = res.bugs;
                                            rowObj.vulnerabilities = res.vulnerabilities;
                                            rowObj.smell = res.smell;
                                            rowObj.coverage = res.coverage;
                                            rowObj.duplicate = res.duplicate;
                                        }
                                    });

                                });
                                //更新成功后取消选中复选框
                                forEach($scope.projectList);
                                //被选择项目集合
                                $scope.projectSelected = [];

                                var urlData = {
                                    'type': $scope.formInsert.language ? $scope.formInsert.language : "",			// 语言类型
                                    'projectName': $scope.formInsert.projectName,		// 项目名称
                                    'pm': $scope.formInsert.pm,							// 项目经理名称
                                    'department': $scope.formInsert.department,			// 部门名称
                                    'currentPage': $scope.pages.pageNum, 						    // 分页页数
                                    'pageSize': $scope.pages.size    					// 分页每页大小
                                };
                                //获取平均分
                                getAvgScore(urlData);

                                // 关闭遮罩层
                                inform.closeLayer();
                                inform.common("分数更新成功!");
                            } else {
                                // 关闭遮罩层
                                inform.closeLayer();
                                inform.common(data.message);
                            }


                        });
                    });


                } else {
                    inform.common(Trans("请选择模块！"));
                }
            };

            function forEach(projectList){
                //更新成功后取消选中复选框
                angular.forEach(projectList, function (project, i) {//遍历人员
                    if (project.checked === true) {
                        project.checked = false;
                    }
                });
            }

            //下载模块明细(根据文件个数判断是否压缩)
            $scope.toExcel = function () {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function() {
                            return "确定要下载吗！";
                        }
                    }
                });
                modalInstance.result.then(function() {
                    if ($scope.projectSelected.length > 1) {
                       inform.downLoadFile('code/toExcelByRar',$scope.projectSelected,'office.zip');
                    }else if($scope.projectSelected.length === 1){
                        if($scope.projectSelected[0].split(",")[1] === 'null'){
                            inform.common(Trans("该项目模板类型为空！无法下载"));
                        }else {
                            inform.downLoadFile('code/toExcelByRar', $scope.projectSelected, $scope.projectSelected[0].split(",")[0] + '.xlsx');
                        }
                    }else {
                         inform.common(Trans("请选择模块！"));
                    }
                     //下载成功后取消选中
                    forEach($scope.projectList);
                    //被选择项目集合
                    $scope.projectSelected = [];
                    $scope.select_all = false;
                });
            };
            //下载模块一览
            $scope.toExcelOutline = function () {
            	 var urlData = {
            			 'type': $scope.formInsert.language ? $scope.formInsert.language : "",			// 语言类型
            	         'projectName': $scope.formInsert.projectName,		// 项目名称
            	         'pm': $scope.formInsert.pm,							// 项目经理名称
            	         'department': $scope.formInsert.department			// 部门名称
                     };
         			var modalInstance = $modal.open({
         				templateUrl: 'myModalContent.html',
         	            controller: 'ModalInstanceCtrl',
         	            size: "sm",
         	            resolve: {
         	            	items: function() {
         	            		return "确定要下载吗！";
         	                }
         	            }
         			});
         			modalInstance.result.then(function() {
         				//开启遮罩层
         				inform.showLayer("下载中。。。。。。");
         				$http.post(
         					$rootScope.getWaySystemApi+'code/toExcelOutline',
         					urlData,
         			        {headers: {
         						'Content-Type': 'application/json',
         						'Authorization':'Bearer ' + LocalCache.getSession("token")||''
         						},
         					 responseType: 'arraybuffer'//防止中文乱码
         					 }
         			     ).success(function(data){
         			         //如果是IE浏览器
         			         if (window.navigator && window.navigator.msSaveOrOpenBlob) {
         			             var csvData = new Blob([data], {type:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
         			             window.navigator.msSaveOrOpenBlob(csvData,'代码质量模块一览.xlsx');
         			         }
         			         //google或者火狐浏览器
         			         else{
         			            var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
         			    		var objectUrl = URL.createObjectURL(blob);
         			    		var aForExcel = $("<a download='代码质量模块一览.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href",objectUrl);
         			    		$("body").append(aForExcel);
         			    		$(".forExcel").click();
         			    		aForExcel.remove();
         			         }
         			         // 关闭遮罩层
         			 		 inform.closeLayer();
         			 		 inform.common("下载成功!");
         			      });
         			  });
            };
            //下载代码质量报告
            $scope.toExcelCodeReport = function () {
            	var urlData = {
           			 'type': "downloadCodeReport"	
                    };
            	inform.modalInstance("确定要下载吗！")
            		.result.then(function() {
            			inform.downLoadFile('code/toExcelOutline', urlData, '代码质量报告.xlsx');
         			  });
            };
            
            //获取项目
            function getData(pageNum) {
                $scope.projectList = [];
                //部门列表
                $scope.depType = [];
                $scope.select_all = false;
                var urlData = {
                    'type': $scope.formInsert.language ? $scope.formInsert.language : "",			// 语言类型
                    'projectName': $scope.formInsert.projectName,		// 项目名称
                    'pm': $scope.formInsert.pm,							// 项目经理名称
                    'department': $scope.formInsert.department,			// 部门名称
                    'riskFlag':$scope.formInsert.riskFlag,             //风险标识
                    'currentPage': pageNum, 						    // 分页页数
                    'pageSize': $scope.pages.size,    					// 分页每页大小
                    'orderType': $scope.orderType            //排序方式
                };

                //部门信息
                comService.getOrgChildren('D010053').then(function (data) {

                    var jsonData = comService.getDepartment(data.data);
                    angular.forEach(jsonData, function (res, index) {
                        $scope.depType.push(res.orgName);
                    })
                });
                //通过类型获取数据库中的项目名，项目经理，分数，开发模型
                //拿到项目名的列表再去Sonar上获取详细信息
                codeService.getProjectAllInformation(urlData).then(function (data) {

                        if (data.code === AgreeConstant.code) {
                            data.data = angular.fromJson(data.data);
                            angular.forEach(data.data.list, function (res, index) {

                                //四舍五入
                                res.score = Math.round(res.score);
                                res.scoreForSornar = Math.round(res.scoreForSornar);
                                res.scoreForJudges = Math.round(res.scoreForJudges);
                                var jsonNode = {

                                    checked: '',
                                    name: res.name,
                                    cname: res.cName,
                                    department: res.department,
                                    bugs: res.bugs,
                                    smell: res.smell,
                                    vulnerabilities: res.vulnerabilities,
                                    coverage: res.coverage,
                                    duplicate: res.duplicate,
                                    type: res.type,
                                    manager: res.manager,
                                    note: res.note,
                                    score: res.score
                                };
                                $scope.projectList.push(angular.extend(jsonNode, res));
                            });
                            $scope.projectSelected = [];

                            if ($scope.projectList.length === 0) {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages(); 	// 初始化分页数据
                            } else {
                                // 分页信息设置
                                $scope.pages.total = data.data.total; 			// 页面数据总数
                                $scope.pages.star = data.data.startRow; 			// 页面起始数
                                $scope.pages.end = data.data.endRow;	 		// 页面分页大小
                                $scope.pages.pageNum = data.data.pageNum;  		// 第几页
                            }
                            //获取平均分
                            getAvgScore(urlData);
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            //定义排序对象
            $scope.orderObj = {
                title: '$index',
                desc: true,
                order: function (str) {
                    $scope.orderObj.title = str;
                    $scope.orderObj.desc = !$scope.orderObj.desc;
                }
            };

            /**
             * 获取平均分数
             */
            function getAvgScore(urlData) {
                //获取平均分数
                codeService.getAvgScore(urlData).then(function (data) {
                        $scope.avgScore = '';
                        if (data.code === AgreeConstant.code) {
                            $scope.avgScore = '共' + $scope.pages.total + '个模块，' + '平均成绩：' + data.data;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans("平均分数获取失败"));
                    });
            }
            
            /**
    		* 前往历史信息页
    		*/
    		$scope.jobHistory = function (m) {
    			LocalCache.setObject('code_formRefer',$scope.formInsert);
    			$state.go('app.office.codeHistoryManagement',{
    				'name':m.name,
					'cName':m.cname,
					'cManager':m.manager,
					'cType':m.type,
					'cDepartment':m.department,
					'riskNote':m.riskNote
    			});
    		};

    		$scope.getScoreDetail = function(item){
    		    $scope.scoreDetailLanguage = item;
    		}

        }]);
})();
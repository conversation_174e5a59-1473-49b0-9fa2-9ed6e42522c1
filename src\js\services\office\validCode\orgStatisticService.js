(function() {
    'use strict';
    app.factory('orgStatisticService', orgStatisticService);
    orgStatisticService.$inject=["HttpService",'$rootScope'];

    function orgStatisticService(HttpService,$rootScope){

        // 获取部门提交人数统计
        function getOrgCommitUserNumDataList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getCommitTimesOfOrg',urlData);
        }

        // 获取部门提交次数统计
        function getOrgAvgCommitCntDataList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getCommitTimesOfOrg',urlData);
        }
       
        // 获取部门提交次数少于10次的人数
        function getOrgUserNumCommitLessData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getUserNumLessCommit',urlData);
        }

        // 获取部门提交代码数小于1000行的工程师人数
        function getOrgUserNumAddLessData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getUserNumLessAdd',urlData);
        }

        // leader提交人数统计
        function getOrgLeaderCommitInfoDataList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getOrgLeaderCommitInfo',urlData);
        }

        // leader代码数据
        function getOrgLeaderCodeInfoDataList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getOrgLeaderCodeInfo',urlData);
        }
        

        // 获取非工作日提交代码人数
        function getUserNumCommitOnHolidayData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getOrgCommitInfoOnHoliday',urlData);
        }
        
        
         // 获取凌晨提交代码人数
         function getUserNumCommitBeforeDawnData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getOrgCommitInfoBeforeDawn',urlData);
        }

        // 获取提交次数少于10次的用户信息
        function getUserLessCommit(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getUserInfoLessCommit',urlData);
        }

         // 获取有效代码数小于1000的用户信息
         function getUserLessAdd(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getUserInfoLessAdd',urlData);
        }

        // 获取leader提交明细
        function getLeaderCommitList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getLeaderCommitDetail',urlData);
        }

        // 获取非工作日提交明细
        function getCommitListOnHoliday(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getOrgCommitDetailOnHoliday',urlData);
        }

        
        // 获取凌晨提交明细
        function getCommitListBeforeDawn(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getOrgCommitDetailBeforeDawn',urlData);
        }

        // 获取部门平均merge次数
        function getOrgAvgMergeDataList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getMergeTimesOfOrg',urlData);
        }

        // 获取部门git提交次数占比
        function getOrgGitCommitTimesDataList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getGitCommitTimesOfOrg',urlData);
        }


        return {
            getOrgCommitUserNumDataList: getOrgCommitUserNumDataList,
            getOrgAvgCommitCntDataList: getOrgAvgCommitCntDataList,
            getOrgUserNumCommitLessData: getOrgUserNumCommitLessData,
            getOrgUserNumAddLessData: getOrgUserNumAddLessData,
            getOrgLeaderCommitInfoDataList: getOrgLeaderCommitInfoDataList,
            getOrgLeaderCodeInfoDataList: getOrgLeaderCodeInfoDataList,
            getLeaderCommitList: getLeaderCommitList,
            getUserNumCommitOnHolidayData: getUserNumCommitOnHolidayData,
            getUserNumCommitBeforeDawnData: getUserNumCommitBeforeDawnData,
            getCommitListOnHoliday: getCommitListOnHoliday,
            getCommitListBeforeDawn: getCommitListBeforeDawn,
            getUserLessCommit: getUserLessCommit,
            getUserLessAdd: getUserLessAdd,
            getOrgAvgMergeDataList:getOrgAvgMergeDataList,
            getOrgGitCommitTimesDataList:getOrgGitCommitTimesDataList,
        };
    }
})();
(function () {
    app.controller("costHrInputDetailController", ['$rootScope', 'comService', 'costInputService', '$scope', '$state', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http', function ($rootScope, comService, costInputService, $scope, $state, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {

        /**
         * *************************************************************
         *             初始化部分                                 开始
         * *************************************************************
         */

        // 获取上一页面传递的参数数据
        $scope.detailItem = JSON.parse($stateParams.detailItem);
        // 是否为项目经理
        $scope.isProjectManager = false;
        // 判断是否为项目经理
        checkProjectManager();
        //设置按钮样式
        setButtonCss();
        // 查询参数（多次用到，定义为全局）
        var urlData = {};
        // 人力成本投入详情
        $scope.costHrDetailData = [];
        // 工时类型
        $scope.hoursTypeList = [];
        // 获取页面信息
        getInitData();
        $scope.formRefer = {};
        //窗体大小变化时重新计算高度
        $(window).resize(setButtonCss);
        /**
         * *************************************************************
         *              初始化部分                                 结束
         * *************************************************************
         */

        /**
         * *************************************************************
         *              方法声明部分                                 开始
         * *************************************************************
         */
        function setButtonCss() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var clientWidth = document.body.clientWidth;
            var divHeight = clientHeight - (150 + 180);
            $("#costList").height(divHeight);
            $("#plmList").height(divHeight - 70);
            $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
            $("#buttonStyle").css({"width": 220+"px"});
        }
        /*
        * 判断登录人是否是项目经理
        * */
        function checkProjectManager() {
            if (JSON.parse(LocalCache.getSession('roleList')).find(function (item) {
                return item.roleName === '项目经理';
            })) {
                $scope.isProjectManager = true;
            }
        }
        /*
        * 获取页面信息
        * */
        function getInitData() {
            //获取该时间段内所有工时类型
            var urlDataType = {
                'projectId': $scope.detailItem.projectId,
                'startTime': $scope.detailItem.startDate,
                'endTime': $scope.detailItem.endDate,
                'statusList': $scope.detailItem.statusList,
                'week':$scope.detailItem.week,
                'plmUpgradeId': null
            };
            costInputService.getHoursTypeByTimePeriod(urlDataType).then(function (result) {
                if (result.code === AgreeConstant.code) {
                    $scope.hoursTypeList = angular.fromJson(result.data);
                    // 获取人力成本详细信息
                    getData();
                } else {
                    inform.common(result.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });

            //获取所有岗位信息
            $scope.costTitleMap = new Map();
            costInputService.getCostWeekTitle().then(function (result) {
                if (result.code === AgreeConstant.code) {
                    var jsonMap = angular.fromJson(result.data);
                    // 把map转为数组
                    for (var key in jsonMap) {
                        $scope.costTitleMap.set(key, jsonMap[key]);
                    }
                    // 对数组排序
                    $scope.costTitleMap = Array.from($scope.costTitleMap).sort(function (a, b) {
                        return parseInt(a[0]) - parseInt(b[0]);
                    });
                    // 获取每周成本信息
                    getData();

                    //查询plm人力详细
                    urlData = {
                        'week': $scope.detailItem.week,
                        'statusList': $scope.detailItem.statusList,
                        'projectId': $scope.detailItem.projectId,
                        'plmUpgradeId': "1"
                    };
                    if (!urlData.statusList[0]) {
                        urlData.statusList = [];
                    }
                    // 获取人力投入信息
                    getHrData();
                } else {
                    inform.common(result.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });

        }
        /*
        * 获取人力投入信息
        * */
        function getHrData() {
            costInputService.getProjectcostWeek(urlData).then(function (result) {
                if (result.code === AgreeConstant.code) {
                    $scope.costInputData = angular.fromJson(result.data);
                    // 若某岗位没有数据则赋值为0
                    $scope.costInputData.forEach(function (item) {
                        $scope.costTitleMap.forEach(function (titleItem) {
                            if (!item.projectcostWeekDetailList.find(function (findItem) {
                                return findItem.titleCode === titleItem[0].toString();
                            })) {
                                var element = {
                                    titleCode: titleItem[0].toString(),
                                    workload: "0"
                                };
                                item.projectcostWeekDetailList.push(element);
                            }
                        });
                        // 对赋值完的数组进行排序
                        item.projectcostWeekDetailList.sort(function (a, b) {
                            return parseInt(a.titleCode) - parseInt(b.titleCode);
                        });
                    });

                    $scope.costInputDataNotPlm = [];
                    // 拆分数组（是否为plm需求）

                    $scope.costInputDataNotPlm = $scope.costInputData;

                    $scope.costInputDataNotPlm.forEach(function (row) {
                        var sum = 0;
                        row.projectcostWeekDetailList.forEach(function (cell) {
                            sum = sum + Number(cell.workload);
                        });
                        row.sum = Number(sum).toFixed(2);
                    })

                } else {
                    inform.common(result.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        }
        /**
         * 获取项目成本信息
         */
        function getData() {
            // 设置页面中时间周期信息
            if ($scope.detailItem.startDate) {
                $('#titleSpan').html('时间周期：' + $scope.detailItem.startDate + '至' + $scope.detailItem.endDate + '（第' + $scope.detailItem.week + '周 所有投入工时明细（包括客户需求的））');
                if ($scope.detailItem.documentId) {
                    $('#titleSpan0').html('需求升级编号：' + $scope.detailItem.plmUpgradeId + '&emsp;文档编号：' + $scope.detailItem.documentId);
                }
            } else {
                $('#titleSpan').html('人力明细');
            }
            urlData = {
                'projectId': $scope.detailItem.projectId,
                'startTime': $scope.detailItem.startDate,
                'endTime': $scope.detailItem.endDate,
                'statusList': $scope.detailItem.statusList,
                'plmUpgradeId': null,
                'week': $scope.detailItem.week,
                'id': null
            };
            costInputService.getProjectcostHrDetail(urlData).then(function (result) {
                if (result.code === AgreeConstant.code) {
                    $scope.costHrDetailData = angular.fromJson(result.data);
                    // 汇总数据
                    var sumItem = {
                        account: '总计',
                        projectcostHrDetailList: [],
                        titleCode: '',
                        workload: 0,
                        sum: 0
                    };
                    // 汇总数据初始化
                    sumItem = dataFun(sumItem);
                    // 若某工时类型没有数据则工时赋值为0
                    $scope.costHrDetailData.forEach(function (item) {
                        item = dataFun(item);
                        // 对每一行进行汇总
                        item.sum = item.projectcostHrDetailList.reduce(function (prev, cur) {
                            return Number(cur.workHour) + prev;
                        }, 0);
                        //对每一列汇总（人天、总工时除外）
                        for (var i = 0; i < $scope.hoursTypeList.length; i++) {
                            sumItem.projectcostHrDetailList[i].workHour += Number(item.projectcostHrDetailList[i].workHour);
                        }
                    });
                    // 对每一列进行汇总（人天、总工时）
                    sumItem.workload = $scope.costHrDetailData.reduce(function (prev, cur) {
                        return Number(cur.workload) + prev;
                    }, 0);
                    sumItem.sum = $scope.costHrDetailData.reduce(function (prev, cur) {
                        return Number(cur.sum) + prev;
                    }, 0);
                    //添加汇总数据
                    $scope.costHrDetailData.push(sumItem);
                    // 数字格式化（保留小数点后两位）
                    $scope.costHrDetailData.forEach(function (item) {
                        item.sum = inform.removeZero(item.sum);
                        item.workload = inform.removeZero(item.workload);
                        item.projectcostHrDetailList.forEach(function (iItem) {
                            iItem.workHour = inform.removeZero(iItem.workHour);
                        });
                    });
                } else {
                    inform.common(result.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        }
        /*
        * 数据的整合与排序
        * */
        function dataFun(item) {
            // 工时类型不存在时，赋值为0
            $scope.hoursTypeList.forEach(function (ele) {
                if (!item.projectcostHrDetailList.find(function (findItem) {
                    return findItem.workType === ele.hourType;
                })) {
                    var element = {
                        workType: ele.hourType,
                        sort: ele.sort,
                        workHour: 0
                    };
                    item.projectcostHrDetailList.push(element);
                }
            });
            // 对赋值完的数组进行排序
            item.projectcostHrDetailList.sort(function (a, b) {
                return parseInt(a.sort) - parseInt(b.sort);
            });
            return item;
        }
        /**
         * 确认弹框
         */
        $scope.confirm = function (str, func) {
            var modalInstance = $modal.open({
                templateUrl: 'myModalContent.html',
                controller: 'ModalInstanceCtrl',
                size: "sm",
                resolve: {
                    items: function items() {
                        return Trans(str);
                    }
                }
            });
            modalInstance.result.then(function () {
                func();
            });
        };
        /*
        * 确认人力投入信息
        * */
        $scope.saveHrCostInfo = function () {
            $scope.confirm('确认后不允许修改,是否确认?', function () {
                //确认项目+plm的人力
                urlData.plmUpgradeId = null;
                costInputService.updateHrCost(urlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        inform.common('确认费用成本成功！');
                        window.history.go(-1);
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            });
        };
        /*
        * 同步
        * */
        $scope.showSync = function () {
            var syncUrlData = {
                'week': $scope.detailItem.week,
                'startDate': $scope.detailItem.startDate,
                'endDate': $scope.detailItem.endDate,
                'projectId': $scope.detailItem.projectId,
                'plmUpgradeId': $scope.detailItem.plmUpgradeId,
                'id': $scope.detailItem.id
            };
            $scope.confirm('将同步本周项目投入和所有的客户需求投入?', function () {
                costInputService.syncProjectcostHrInfo(syncUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.detailItem.id = result.data;
                        getInitData();
                        inform.common('同步成功！');
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            },'sm');
        };
        /*
        * 返回上一页
        * */
        $scope.goback = function () {
            window.history.go(-1);
        };
        /**
         * *************************************************************
         *              方法声明部分                                 结束
         * *************************************************************
         */
    }]);
})();

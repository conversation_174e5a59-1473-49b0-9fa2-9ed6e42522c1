/*
 * @Author: fubaole
 * @Date:   2017-09-18 14:53:05
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-01-15 17:20:32
 */
(function() {
    app.controller("paramManagement", ['comService', '$rootScope', '$scope', '$modal', 'paramManagementService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache',
        function(comService, $rootScope, $scope, $modal, paramManagementService, inform, Trans, AgreeConstant, LocalCache) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */

             // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //页面查询条件
            $scope.formRefer = {
                department: 'D010133', //所属部门
            };
            //绑定文件控件改变事件
            $("#filesImg1").change(submitForm);
            $scope.type = '5';
            //指标内容
            $scope.Param = {
                planFinishRate: '', //计划完成率目标值
                customerRequireFinishRate: '', //客户需求完成率目标值
                patentTotal: '', //专利目标值
                softwareTotal: '', //软件著作权目标值
                specialPassRate: '', //特殊放行目标值
                omitRate: '', //缺陷逃逸目标值
                onlineRemadeRate: '', //上线后返工比率目标值
                developRemadeRate: '', //开发过程返工比率目标值
                personBugTotal: '', //人均bug目标值
                oneExaminePassRate: '', //一次评审通过率目标值
                processQualityRate: '', //过程质量目标值
                codeQuality: '', //代码质量目标值
                knowledgeDocTotal: '', //知识库目标值
                outputTrainTotal: '', //输出培训目标值
                rationalSuggestTotal: '', //合理化建议目标值
                onlineFailRate: '', //上线失败率目标值
                failureTotal: '' //投诉、事故目标值
            };
            //中心办指标内容
            $scope.officeParam = {
            	optimizeItemQTY: '', //研发体系优化项数
            	optimizeDocumentQTY: '', //研发体系优化文件数
            	optimizeReportQTY: '', //管理优化报告次数
            	optimizeProblemQTY: '', //管理优化发现问题数
            	qualityReportQTY: '', //过程质量报告次数
            	officeProjectNum: ''//系研项目数
            };
            //测试部门指标内容
            $scope.testParam = {
                keyProjectCoverage: '', //系研重点项目覆盖率
                projectCoverage: '', //系研项目覆盖率
                commonCase: '', //公共用例梳理
                automatedTestProject: '' //接口自动化覆盖项目数
            };
            //KPI指标统计时间设置
            $scope.timeParam = {
                startTime: '', //开始时间
                endTime: '' //结束时间
            };
            //修改质量指标内容
            $scope.QualityParam = {
                targetValue: '', //目标值
                planWeight: '' //计划权重
            };
            //绑定文件控件改变事件
            $("#filesImg1").change(fileChangeEvent);
            //初始化页面信息
            initPages();
            $scope.getData = getData; //获取该部门指标
            $scope.upData = upData; //更新该部门指标
            $scope.upOfficeData =  upOfficeData;//更新中心办指标
            $scope.upTestData = upTestData; //更新测试部门指标
            $scope.upTimeData = upTimeData; //更新KPI指标统计时间
            $scope.getQualityParam = getQualityParam;
            getData("D010133"); //默认项目管理办公室
            getQualityParam();
            //
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 选择上传文件后事件
             */
            function fileChangeEvent(e) {
                var fileName = e.currentTarget.files[0].name;
                $("#fileNameDis").text(fileName);
            }
            /**
             * 页面初始化
             */
            function initPages() {
                //获取山东新北洋集团的下级部门信息
                $scope.orgList = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.orgList = comService.getDepartment(data.data);
                });
                //获取二级指标
                $scope.secondTarget = [];
                comService.getParamList('PROJECT_QUALITY', 'SECOND_TARGET').then(function(data) {
                    if (data.data) {
                        $scope.secondTarget = data.data;
                    }
                });
            }
            /**
             * 开始时间
             */
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true; //开始时间
                $scope.openedEnd = false;
            };
            /**
             *
             *  结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true; //结束时间
            };
            /**
             * 获取该部门指标
             */
            function getData(department) {
                var urlData;
                if ($scope.type === '3') {
                    urlData = 'testKPI';
                } else if ($scope.type === '4') {
                    urlData = 'KPI_TIME';
                } else if ($scope.type === '6') {
                    urlData = 'officeKPI';
                }else {
                    urlData = JSON.stringify(department);
                }
                //获取参数指标
                paramManagementService.getData(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        if ($scope.type==='3') {
                            $scope.testParam = angular.fromJson(data.data);
                        } else if ($scope.type === '4') {
                            $scope.timeParam = angular.fromJson(data.data);
                        } else if ($scope.type === '6') {
                            $scope.officeParam = angular.fromJson(data.data);
                        }else {
                            $scope.Param = angular.fromJson(data.data);
                        }
                    } else {
                        inform.common(data.message);
                    }
                }, function() {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 更新测试部门指标
             */
            function upTestData() {
                angular.forEach($scope.testParam, function(org, i) { //遍历参数
                    if ($scope.testParam[i]==null) {
                        $scope.testParam[i] = "";
                    }
                });
                var urlData = {
                    'department': 'testKPI', //所属部门
                    'kpi': $scope.testParam //指标
                };
                paramManagementService.upData(urlData).then(function(data) {
                    callBackFunction(data);
                }, function() {
                    inform.common(Trans("tip.requestError"));
                });
            }

            function callBackFunction(data){
                if (data.code === AgreeConstant.code) {
                    inform.common("更新数据成功!");
                } else {
                    inform.common(data.message);
                }
            }
            /**
             * 更新中心办指标
             */
            function upOfficeData() {
                angular.forEach($scope.officeParam, function(org, i) { //遍历参数
                    if ($scope.officeParam[i]==null) {
                        $scope.officeParam[i] = "";
                    }
                });
                var urlData = {
                    'department': 'officeKPI', //所属部门
                    'kpi': $scope.officeParam //指标
                };
                paramManagementService.upData(urlData).then(function(data) {
                    callBackFunction(data);
                }, function() {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 更新KPI指标统计时间
             */
            function upTimeData() {
                angular.forEach($scope.timeParam, function(org, i) { //遍历参数
                    if ($scope.timeParam[i]==null) {
                        $scope.timeParam[i] = "";
                    }
                });
                $scope.timeParam.startTime = inform.format($scope.timeParam.startTime, 'yyyy-MM-dd');
                $scope.timeParam.endTime = inform.format($scope.timeParam.endTime, 'yyyy-MM-dd');
                var urlData = {
                    'department': 'KPI_TIME', //所属部门
                    'kpi': $scope.timeParam //指标
                };
                paramManagementService.upData(urlData).then(function(data) {
                    callBackFunction(data);
                }, function() {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 更新该部门指标
             */
            function upData() {
                angular.forEach($scope.Param, function(org, i) { //遍历参数
                    if ($scope.Param[i]==null) {
                        $scope.Param[i] = "";
                    }
                });
                var urlData = {
                    'department': $scope.formRefer.department, //所属部门
                    'kpi': $scope.Param //指标
                };
                paramManagementService.upData(urlData).then(function(data) {
                    callBackFunction(data);
                }, function() {
                    inform.common(Trans("tip.requestError"));
                });
            }
            //选择文件
            $scope.selectFile = function() {
                document.getElementById("filesImg1").click();
            };
            //上传文件
            function submitForm() {
                var formData = new FormData(document.getElementById("form")); //表单id  初始化表单值
                var file = document.querySelector('input[type=file]').files[0]; //获取文档中有类型为file的第一个input元素
                if (!file) {
                    inform.common("请先选择文件!");
                    return false;
                }
                formData.append('fileName', file);
                var a = file.type;
                if (a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
                    inform.common("请选择.xlsx类型的文档进行上传!");
                    return false;
                } else {
                    var modalInstance = $modal.open({
                        templateUrl: 'myModalContent.html',
                        controller: 'ModalInstanceCtrl',
                        size: "sm",
                        resolve: {
                            items: function() {
                                return "确定要上传文件吗！";
                            }
                        }
                    });
                    modalInstance.result.then(function() {
                        //开启遮罩层
                        inform.showLayer("上传中。。。。。。");
                        $.ajax({
                            url: $rootScope.getWaySystemApi + 'parammanagement/uploadExcel',
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            beforeSend: function(request) {
                                request.setRequestHeader("Authorization", 'Bearer ' + LocalCache.getSession("token") || '');
                            },
                            success: function(result) {
                                // 关闭遮罩层
                                inform.closeLayer();
                                inform.common(result.message);
                            },
                            error: function(error) {
                                inform.common(Trans("tip.requestError"));
                            }
                        });
                    });
                }
            }
            /**
             *质量指标页面开始
             *
             */
            /**
             *设置列表的高度
             *
             */
            $scope.setDivHeight = function() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
            };
            //获取质量指标数据
            function getQualityParam() {
                $scope.itemList = [];
            }
            /**
             * 当计划权重发生变化时,计算总分
             */
            $scope.countChange = function() {
                var sum = 0.00;
                //遍历获取的数组
                angular.forEach($scope.itemList, function(m, index) {
                    if (null != m.planWeight && "" !== m.planWeight) {
                        sum = (sum * 1 + m.planWeight * 1).toFixed(0);
                    }
                });
                $scope.count = sum;
            };
            /**
             * 修改方法
             */
            $scope.upQualityParam = function() {
                if ($scope.count*1 !== 100) {
                    $scope.open();
                } else {
                    angular.forEach($scope.itemList, function(m, index) {
                        var urlData = {
                            'count': $scope.count,
                            'secondTarget': m.secondTarget, //二级指标
                            'targetValue': m.targetValue, //目标值
                            'planWeight': m.planWeight //计划权重
                        };
                        paramManagementService.upQualityParam(urlData).then(function(data) {
                            inform.common(data.message);
                        }, function() {
                            inform.common(Trans("tip.requestError"));
                        });
                    });
                }
            };
            // 删除弹框
            $scope.open = function(m) {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function() {
                            return ("权重加合不等于100请重新输入");
                        }
                    }
                });
                modalInstance.result.then(function() {});
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }
    ]);
})();
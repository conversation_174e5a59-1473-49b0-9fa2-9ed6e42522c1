(function() {
    'use strict';
    app.factory('departmentListService', departmentListService);
    departmentListService.$inject=["HttpService",'$rootScope'];

    function departmentListService(HttpService,$rootScope){

        function getDeptListIndex(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'deptList/getDeptListIndex',urlData);
        }

        return {
            getDeptListIndex: getDeptListIndex
        };
    }
})();
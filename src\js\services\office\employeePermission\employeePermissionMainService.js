(function() {
    'use strict';
    app.factory('employeePermissionMainService', employeePermissionMainService);
    employeePermissionMainService.$inject=["HttpService",'$rootScope'];

    function employeePermissionMainService(HttpService,$rootScope){
        var service={
            findRepositoryListByPage:findRepositoryListByPage,
            getMaintainer:getMaintainer,
        };
        function findRepositoryListByPage(urlData) {
            return HttpService.post($rootScope.getWaySystemApi+'personPermission/getPersonPermission',urlData);
        }
        function getMaintainer(urlData) {
            return HttpService.post($rootScope.getWaySystemApi+'personPermission/getMaintainer',urlData);
        }
        return service;
    }
})();

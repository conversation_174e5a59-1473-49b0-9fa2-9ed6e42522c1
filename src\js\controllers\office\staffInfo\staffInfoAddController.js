(function () {
    'use strict';
    app.controller('staffInfoAddController', [
        'comService',
        '$rootScope',
        '$stateParams',
        '$scope',
        'staffInfoService',
        '$modal',
        'inform',
        'Trans',
        'AgreeConstant',
        '$state',
        function (
            comService,
            $rootScope,
            $stateParams,
            $scope,
            staffInfoService,
            $modal,
            inform,
            Trans,
            AgreeConstant,
            $state
        ) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //获得当前时间
            $scope.datepicker = {
                currentDate: new Date(),
            };
            $scope.addParam = {};
            //设置列表的高度
            setDivHeight();

            getAreaList(); //地区
            getEthnicGroupList(); //获取系研族群列表
            getStateList(); //工作状态
            initPrimaryDeptList(); //初始化根据用户名获取一级部门列表
            $scope.isValid = false; //判断是否有二级部门
            var fileTmp = '';
            var formData = {};

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */

            //入职时间
            $scope.openOnboardTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardTime1 = true;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = false;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = false;
            };
            //毕业时间
            $scope.openGraduationTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = true;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = false;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = false;
            };
            //出生日期
            $scope.openBirthTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = true;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = false;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = false;
            };
            //转正日期
            $scope.openTurnPositiveDate = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = true;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = false;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = false;
            };
            //离职日期
            $scope.openLeavingDate = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = true;
                $scope.openGetTitleDate1 = false;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = false;
            };
            //获得职称时间
            $scope.openGetTitleDate = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = true;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = false;
            };
            //获得国家职称时间
            $scope.openGetNationalTitleTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = false;
                $scope.openNationalTitleTime1 = true;
                $scope.onDepartmentTime1 = false;
            };
            //入部门时间
            $scope.openOnDepartmentTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = false;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = true;
            };

            /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function (data) {
                    if (data.data) {
                        $scope.primaryDeptList = data.data;
                    }
                });
                //获取软件产品线
                $scope.softwareProductLineList = [];
                comService.queryEffectiveParam('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.softwareProductLineList = data.data;
                    }
                });
                //获取归属模块
                $scope.belongModuleList = [];
                comService.queryEffectiveParam('BELONG_MODULE', 'BELONG_MODULE').then(function (data) {
                    if (data.data) {
                        $scope.belongModuleList = data.data;
                    }
                });
                //获取人员分类
                $scope.employeeTypeList = [
                    { paramCode: '直接开发人员', paramValue: '直接开发人员' },
                    { paramCode: '开发共用人员', paramValue: '开发共用人员' },
                    { paramCode: '项目管理人员', paramValue: '项目管理人员' },
                    { paramCode: '开发试制人员', paramValue: '开发试制人员' },
                    { paramCode: '管理支撑人员', paramValue: '管理支撑人员' },
                ];
            }

            //修改一级部门，二级部门、职称、岗位、专业模块进行联动
            $scope.changeDept = function () {
                setDept();
                getProfessionalModuleList();
                getTitleList();
                getStaffTitleList();
            };
            //修改岗位联动职级
            $scope.changeTitle = function () {
                for (var i = 0; i < $scope.titleList.length; i++) {
                    if ($scope.addParam.turnTitle === $scope.titleList[i].param_code) {
                        $scope.addParam.titleLevel = $scope.titleList[i].param_desc;
                    }
                }
            };
            //获取二级部门
            function setDept() {
                $scope.deptList = [];
                comService.getOrgChildren($scope.addParam.primaryDept).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.deptList = data.data;
                        //清空二级部门
                        $scope.addParam.department = '';
                    }
                });
            }
            //获取细分产品线
            $scope.changeSoftwareProductLine = function () {
                $scope.subdivisionProductLine = [];

                // 根据产品线code查找对应的产品线名称
                const softwareProductLineName =
                    $scope.softwareProductLineList.find((i) => i.paramCode === $scope.addParam.softwareProductLine)
                        ?.paramValue || '';

                comService.getParamList('细分产品线', softwareProductLineName).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.subdivisionProductLine = data.data;
                        //清空细分产品线
                        $scope.addParam.subdivisionProductLine = '';
                    }
                });
            };
            //获取地区
            function getAreaList() {
                $scope.areaList = [];
                comService.getParamList('AREA_TYPE', 'AREA_TYPE').then(function (data) {
                    $scope.areaList = data.data;
                });
            }

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (170 + 65);
                var ss = document.getElementById('addFormId');
                ss.style.height = divHeight + 'px';
                var clientWidth = document.body.clientWidth;
                $('#buttonStyle').css(inform.getButtonStyle(clientHeight, clientWidth));
            }

            //获取系研专业模块列表
            function getProfessionalModuleList() {
                //获取专业模块信息
                $scope.professionalModuleList = [];
                comService.getParamList('STAFF_PROFESSIONAL_MODULE', 'NEW').then(function (data) {
                    $scope.professionalModuleList = data.data;
                    //清空专业模块
                    $scope.addParam.professionalModule = '';
                });
            }

            //获取系研职称信息
            function getTitleList() {
                //获取系研职称信息
                $scope.titleList = [];
                $scope.titleLevelList = [];
                comService.getParamList('STAFF_TITLE', 'NEW').then(function (data) {
                    $scope.titleList = data.data;
                    //清空职称
                    $scope.addParam.turnTitle = '';

                    for (var i = 0; i < $scope.titleList.length; i++) {
                        if ($scope.titleLevelList.indexOf($scope.titleList[i].param_desc) === -1) {
                            $scope.titleLevelList.push($scope.titleList[i].param_desc);
                        }
                    }
                });
            }

            //获取系研员工岗位信息
            function getStaffTitleList() {
                //获取员工岗位信息
                $scope.staffTitleList = [];
                comService.getParamList('STAFF_INFO_TITLE', 'NEW').then(function (data) {
                    $scope.staffTitleList = data.data;
                    //清空岗位
                    $scope.addParam.post = '';
                });
            }

            //获取系研族群列表
            function getEthnicGroupList() {
                //获取族群信息
                $scope.ethnicGroupList = [];
                comService.getParamList('STAFF_ETHNIC_GROUP', 'STAFF_ETHNIC_GROUP').then(function (data) {
                    $scope.ethnicGroupList = data.data;
                });

                //获取合同主体信息
                $scope.subjectOfContractList = [];
                comService.getParamList('SUBJECT_CONTRACT', 'SUBJECT_CONTRACT').then(function (data) {
                    $scope.subjectOfContractList = data.data;
                });
            }

            //获取系研族群列表
            function getStateList() {
                //获取系研职称信息
                $scope.stateList = [];
                comService.getParamList('STAFF_STATE', 'STAFF_STATE').then(function (data) {
                    removeData(data.data);
                    $scope.stateList = data.data;
                });
            }

            //遍历数组删除指定元素
            function removeData(data) {
                for (var i = 0; i < data.length; i++) {
                    if (data[i].param_code === '05') {
                        data.splice(i, 1);
                    }
                }
            }

            $scope.goBack = function () {
                $state.go('app.office.staffInfoManagementController');
            };

            $scope.updatePic = function () {
                $('#myFile').trigger('click');
            };

            //选择图片
            $scope.changePic = function () {
                var f = document.getElementById('myFile').files[0];
                var index_int = f.name.lastIndexOf('.');
                var lastArr = f.name.substring(index_int);
                formData = new FormData($('#formTmp')[0]);

                if (
                    lastArr !== '.bmp' &&
                    lastArr !== '.png' &&
                    lastArr !== '.gif' &&
                    lastArr !== '.jpg' &&
                    lastArr !== '.jpeg'
                ) {
                    event.target.value = '';
                    layer.confirm(
                        '只支持bmp、png、gif、jpg、jpeg格式上传。',
                        {
                            title: false,
                            btn: ['确定'],
                        },
                        function (result) {
                            if ('' === fileTmp) {
                                $('#prompt3').css('display', 'block');
                            }
                            layer.close(result);
                        }
                    );
                } else {
                    //上传文件大小不能超过1M (1024*1024 = 1048576)
                    if (f.size > 1048576) {
                        event.target.value = '';
                        inform.common('上传文件大小不能超过1M');
                        if ('' === fileTmp) {
                            $('#prompt3').css('display', 'block');
                        }
                        return;
                    }
                    if (f.size > 0) {
                        fileTmp = f;
                        $('#prompt3').css('display', 'none');
                        var reads = new FileReader();
                        reads.readAsDataURL(f);
                        reads.onload = function (e) {
                            document.getElementById('img_picture_view').src = this.result;
                            $('#img_picture_view').css('display', 'block');
                            $('#img_picture_del').css('display', 'block');
                            $('#img_picture_view').css('height', '128px');
                            $('#img_picture_view').css('width', '104px');
                        };
                    }
                }
            };

            //上传图片
            function uploadPic(employeeId) {
                var f = fileTmp;
                formData.append('file', f);
                formData.append('employeeId', employeeId);
                inform.uploadFile('picture/updatePicture', formData, function func(result) {});
            }

            //删除预览的图片
            $scope.img_del = function () {
                inform.modalInstance('确定要删除照片吗？').result.then(function () {
                    document.getElementById('img_picture_view').src = '';
                    $('#img_picture_del').css('display', 'none');
                    $('#prompt3').css('display', 'block');
                    $('#img_picture_view').css('display', 'none');

                    fileTmp = '';
                });
            };

            /**
             * 校验时间格式正确性，不正确返回空
             * @param date
             * @returns {*}
             */
            function timeAdjust(date) {
                date = inform.format(date, 'yyyy-MM-dd');
                if (date === 'NaN-NaN-NaN' || date === '' || date == null) {
                    return '';
                } else {
                    return date;
                }
            }

            //保存新增数据
            $scope.saveAddData = function () {
                $scope.addParam.birth = timeAdjust($scope.addParam.birth);
                $scope.addParam.graduationTime = timeAdjust($scope.addParam.graduationTime);
                $scope.addParam.getNationalTitleTime = timeAdjust($scope.addParam.getNationalTitleTime);
                $scope.addParam.turnPositiveDate = timeAdjust($scope.addParam.turnPositiveDate);
                $scope.addParam.onDepartmentTime = timeAdjust($scope.addParam.onDepartmentTime);

                var param = {
                    area: $scope.addParam.area,
                    primaryDept: $scope.addParam.primaryDept,
                    paramType: 'NEW',
                    department: $scope.addParam.department,
                    employeeName: $scope.addParam.employeeName,
                    employeeId: $scope.addParam.employeeId,
                    professionalModule: $scope.addParam.professionalModule,
                    onboardingTime: inform.format($scope.addParam.onboardingTime, 'yyyy-MM-dd'),
                    gender: $scope.addParam.gender,
                    graduates: $scope.addParam.graduates,
                    professional: $scope.addParam.professional,
                    education: $scope.addParam.education,
                    graduationTime: $scope.addParam.graduationTime,
                    englishLevel: $scope.addParam.englishLevel,
                    mentor: $scope.addParam.mentor,
                    state: $scope.addParam.state,
                    isRecentGraduates: $scope.addParam.isRecentGraduates,
                    title: $scope.addParam.title,
                    ethnicGroup: $scope.addParam.ethnicGroup,
                    birth: $scope.addParam.birth,
                    idNumber: $scope.addParam.idNumber,
                    national: $scope.addParam.national,
                    origin: $scope.addParam.origin,
                    currentAddress: $scope.addParam.currentAddress,
                    commonCall: $scope.addParam.commonCall,
                    emergencyCall: $scope.addParam.emergencyCall,
                    maritalStatus: $scope.addParam.maritalStatus,
                    politicalLandscape: $scope.addParam.politicalLandscape,
                    turnTitle: $scope.addParam.turnTitle, //转正职称
                    turnPositiveDate: $scope.addParam.turnPositiveDate,
                    leavingDate: $scope.addParam.leavingDate,
                    leavingReasons: $scope.addParam.leavingReasons,
                    note: $scope.addParam.note,
                    staffGroup: $scope.addParam.staffGroup,
                    emergencyContact: $scope.addParam.emergencyContact,
                    exitClassify: $scope.addParam.exitClassify,
                    classificationReasonsForLeaving: $scope.addParam.classificationReasonsForLeaving,
                    getNationalTitleTime: $scope.addParam.getNationalTitleTime,
                    employeeType: $scope.addParam.employeeType,
                    softwareProductLine: $scope.addParam.softwareProductLine,
                    subdivisionProductLine: $scope.addParam.subdivisionProductLine,
                    belongModule: $scope.addParam.belongModule,
                    softwareProduct: $scope.addParam.softwareProduct,

                    post: $scope.addParam.post,
                    subjectOfContract: $scope.addParam.subjectOfContract,
                    tweOneOneSchoolOrNot: $scope.addParam.tweOneOneSchoolOrNot,
                    sequence: $scope.addParam.sequence,
                    originalDuty: $scope.addParam.originalDuty,
                    onDepartmentTime: $scope.addParam.onDepartmentTime,
                    highestDegree: $scope.addParam.highestDegree,
                    titleLevel: $scope.addParam.titleLevel,
                };
                staffInfoService.addByParam(param).then(function (result) {
                    if (result.code === '0000') {
                        if (fileTmp.size > 0) {
                            //上传图片
                            uploadPic($scope.addParam.employeeId);
                        }
                        // 关闭遮罩层
                        inform.closeLayer();
                        layer.confirm(
                            result.message,
                            {
                                title: false,
                                btn: ['确定'],
                            },
                            function (result) {
                                layer.close(result);
                                $scope.addParam = {};
                                $scope.goBack();
                            }
                        );
                    } else {
                        inform.common(result.message);
                    }
                });
            };

            $scope.sequenceChange = function (ethnicGroup) {
                //获取序列信息
                $scope.sequenceList = [];
                comService.getParamList('SEQUENCE', ethnicGroup).then(function (data) {
                    $scope.sequenceList = data.data;
                });
            };

            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        },
    ]);
})();

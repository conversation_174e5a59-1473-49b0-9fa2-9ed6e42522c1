import { createApp } from 'vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';

// 导入所有页面组件
import AttendanceDetailVue from './pages/AttendanceDetail.vue';
import SimpleDemo from './components/SimpleDemo.vue';
import App from './App.vue';

// 创建 Vue 应用的工厂函数
function createVueApp(component, props = {}) {
    const app = createApp(component, props);

    // 注册 Element Plus
    app.use(ElementPlus);

    // 注册所有图标
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
        app.component(key, component);
    }

    return app;
}

// 挂载 Vue 组件到指定 DOM 元素的函数
function mountVueComponent(elementId, componentName, props = {}) {
    const element = document.getElementById(elementId);
    if (!element) {
        console.error(`Element with id "${elementId}" not found`);
        return null;
    }

    let component;
    switch (componentName) {
        case 'AttendanceDetail':
            component = AttendanceDetailVue;
            break;
        case 'SimpleDemo':
            component = SimpleDemo;
            break;
        default:
            console.error(`Component "${componentName}" not found`);
            return null;
    }

    const app = createVueApp(component, props);
    return app.mount(element);
}

// 卸载 Vue 组件
function unmountVueComponent(app) {
    if (app && app.$el) {
        app.unmount();
    }
}

// 全局暴露函数供 AngularJS 使用
window.VueComponents = {
    mount: mountVueComponent,
    unmount: unmountVueComponent,
    createApp: createVueApp,
    components: {
        AttendanceDetail: AttendanceDetailVue,
        SimpleDemo: SimpleDemo,
    },
};

// 开发环境下挂载 App 组件用于预览
if (import.meta.env.DEV && document.getElementById('app')) {
    const app = createVueApp(App);
    app.mount('#app');
}

export { mountVueComponent, unmountVueComponent, createVueApp, AttendanceDetailVue };

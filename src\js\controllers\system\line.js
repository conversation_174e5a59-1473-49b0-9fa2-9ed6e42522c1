/*
 * @Author: fubaole
 * @Date:   2018-01-03 11:50:11
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-30 10:00:40
 */

(function() {
  'use strict';
  app.controller("lineCtrl", ['$scope','$rootScope', 'inform', '$timeout', 'Trans', '$interval', '$location',
    function($scope,$rootScope, inform, $timeout, Trans, $interval, $location) {

      $scope.getData = getData;
      $scope.initConfig = initConfig;
      var option = {};
      initConfig();
      getData();

      function getData(){

        option = {
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['最高气温', '最低气温']
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} °C'
            }
          },
          series: [{
              name: '最高气温',
              type: 'line',
              data: [11, 11, 15, 13, 12, 13, 10],
              markPoint: {
                data: [
                  { type: 'max', name: '最大值' },
                  { type: 'min', name: '最小值' }
                ]
              },
              markLine: {
                data: [
                  { type: 'average', name: '平均值' }
                ]
              }
            },
            {
              name: '最低气温',
              type: 'line',
              data: [1, -2, 2, 5, 3, 2, 0],
              markPoint: {
                data: [
                  { name: '周最低', value: -2, xAxis: 1, yAxis: -1.5 }
                ]
              },
              markLine: {
                data: [
                  { type: 'average', name: '平均值' },
                  [{
                    symbol: 'none',
                    x: '90%',
                    yAxis: 'max'
                  }, {
                    symbol: 'circle',
                    label: {
                      normal: {
                        position: 'start',
                        formatter: '最大值'
                      }
                    },
                    type: 'max',
                    name: '最高点'
                  }]
                ]
              }
            }
          ]
        };

        //tip:接口数据成功后，调用此方法，渲染页面
        reloadPageData(option);
        
      }

      //接口获取的数据，执行数据渲染操作
      function reloadPageData(option){
        if($scope.$parent.screenFlag){
          $timeout(function(){
            $scope.fullBar.setOption(option,true);
          },0);
        }else{
          $timeout(function(){
            $rootScope.line.setOption(option,true);
          },0);
        }
      }

      // 全屏图表
      function initConfig(){
        if($scope.$parent.screenFlag){
        $('.fullscreen #line').width(document.body.clientWidth*0.85);
        $('.fullscreen #line').height(document.body.clientHeight*0.6);
        console.log($('.fullscreen #line').height());
        console.log($('.fullscreen #line').width());
        $timeout(function(){
          $scope.fullBar = echarts.init($('.fullscreen #line')[0]);
          $scope.fullBar.setOption(option,true);
        },0);
      }else{
        $timeout(function(){
          $rootScope.line = echarts.init(document.getElementById('line'));
          $rootScope.line.setOption(option,true);
        },0);
      }

      // 刷新该模块
      $scope.$on('reload', function(e, id) {
          console.log("父级传来的数据ID"+id+"根据ID重新加载该模块");
          //tip:根据接口给的contentId，判断if(contentId===id),则执行刷新操作,调用getData();
      });

      var timeout_upd = $interval(function(){
        if ($location.path() === '/app/index_bench' || $location.path().indexOf('preview_page')!== -1) {
          getData();
        }
      } ,16000);

      // 清除定时器
      $scope.$on('$destroy',function(){
        $interval.cancel(timeout_upd);
      });


    }  
      
    }
  ]);
})();
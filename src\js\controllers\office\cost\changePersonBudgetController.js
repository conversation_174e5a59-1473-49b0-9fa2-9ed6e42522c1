(function () {
    app.controller("changePersonBudgetController", ['budgetChangeApplyService','budgetService','comService','$rootScope', '$scope','$state','$stateParams','$modal','inform','Trans','AgreeConstant','LocalCache','$http','OfficeFileTool',
        function (budgetChangeApplyService,budgetService,comService,$rootScope, $scope,$state,$stateParams, $modal,inform,Trans,AgreeConstant,LocalCache,$http,OfficeFileTool) {
     	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		// 正则校验配置
        $scope.limitList = AgreeConstant.limitList;
        $scope.select={};
		$scope.select.managementLevel='-';
		$scope.select.ppqaLevel='-';
		$scope.select.frameworkLevel='-';
		$scope.select.productLevel='-';
		var num=0;
		//初始化人力预算
        $scope.initPersonBudget = '0';
    	$scope.projectName = $stateParams.projectName;
    	$scope.projectId = $stateParams.projectId;
    	$scope.version = $stateParams.version;
    	$scope.setData=setData;
		//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		 $(window).resize(setDivHeight);
         initPages();

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */

	    //设置列表的高度
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 180);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 70);
 		}
 		//返回
        $scope.goback = function (isProjectBudget) {
            $state.go("app.office.budgetController",{
            	isProjectBudget: '1'
            });
        };
        /**
         * 初始化页面
         */
        function initPages() {
            //获取所有级别，用作下拉框
            $scope.levelSelect=[];
            $scope.levelTypeMap = {};
            budgetChangeApplyService.getAllLevel().then(function (data) {
                if (data.data) {
                    $scope.levelSelect = data.data;
                    angular.forEach($scope.levelSelect,function (levelSelect) {
						$scope.levelTypeMap[levelSelect.code+"m"] = levelSelect.name;
					});
                    num++;
                    setData();
                }
            });
            var urlData ={
    				'projectId':$stateParams.projectId,
    				'plmUpgradeId':'0',
    				'version':$stateParams.version,
    				'changeApplyStatus':"0"
        	};
        	//获取变更记录
            $scope.changeLog = [];
            budgetChangeApplyService.getChangeLog(urlData).then(function (data) {
            if (data.data) {
                   $scope.changeLog = data.data;
                    num++;
                    setData();
                }
            });
            //获取所有岗位与级别
            $scope.titleLevelList = [];
            //级别集合-表头
            $scope.levelList=[];
            //级别集合-表值
            $scope.levelListNum=[];
            budgetChangeApplyService.getAllTitleLevelList(urlData).then(function (data) {
                if (data.data) {
                    $scope.titleLevelList = data.data;
                    for(var i = 0;i<$scope.titleLevelList.length;i++){
                        for(var j = 0;j<$scope.titleLevelList[i].level.length;j++){
                            $scope.levelListNum.push($scope.titleLevelList[i].level[j]);
                            if(i>=4){
                                $scope.levelList.push($scope.titleLevelList[i].level[j]);
                            }
                        }
                    }
                    num++;
                    setData();
                }
            });
            //获取阶段
            $scope.stageList = [];
            budgetChangeApplyService.getAllStageList().then(function (data) {
                if (data.data) {
                    $scope.stageList = data.data;
                    num++;
                    setData();
                }
            });

            //获取项目人力预算明细
            $scope.personBudgetInfo = [];
			 //查询人力投入
			budgetService.getPersonBudgetInfo(urlData).then(function(data){
				if(data.code===AgreeConstant.code){
					$scope.personBudgetInfo = data.data;
				}
				num++;
				setData();
		       });
            //按项目阶段获取人力预算总计
            $scope.personBudgetByStage = [];
            budgetChangeApplyService.getPersonBudgetByStage(urlData).then(function(data){
				if(data.code===AgreeConstant.code){
					$scope.personBudgetByStage = data.data;
				}
				num++;
				setData();
		       });
		    //获取费用预算
            $scope.feeBudgetInfo = [];
            //（项目费用预算）
			budgetService.getFeeBudgetInfo(urlData).then(function(data){
				if(data.code===AgreeConstant.code){
					$scope.feeBudgetInfo = data.data;
				}
				num++;
				setData();
	        });
		    //获取差旅费用明细预算
            $scope.travelFeeBudgetInfo = [];
            budgetService.travelFeeBudgetInfo(urlData).then(function(data){
				if(data.code===AgreeConstant.code){
					$scope.travelFeeBudgetInfo = data.data;
					$scope.travelFeeBudgetInfoOld = JSON.parse(JSON.stringify(data.data));
				}
				num++;
				setData();
	        });

            //获取费用类型
            $scope.feeList = [];
            $scope.feeTypeMap = {};
            comService.getParamList('COSTFEE_TYPE','COSTFEE_TYPE').then(function(data) {
            	if(data.data) {
            		$scope.feeList = data.data;
            		angular.forEach($scope.feeList,function (feeType) {
						$scope.feeTypeMap[feeType.param_code] = feeType.param_value;
					});
            	}
            	num++;
                setData();
            });
         }

        function setData () {
            if(num !== 8){
                return;
            }
            //给人力预算明细赋值
            setTimeout(setPersonDetailData,1100);
            //给人力预算汇总赋值(按阶段)
            setTimeout(setPersonBudgetByStage,1200);
        }
            function setPersonDetailData(){
				//遍历list，取到map的key，value，给控件赋值
				angular.forEach($scope.personBudgetInfo, function (personBudgetInfo, index) {
                    var id = personBudgetInfo['codes'];
					//1：项目经理；4：PPQA；5：架构设计师；7：产品工程师
					var codeArray = id.split("_");
					if(codeArray[1] === "1"){
					    document.getElementById("managementLevel").value = $scope.levelTypeMap[codeArray[2]+"m"]
					}else if(codeArray[1] === "4"){
					    document.getElementById("ppqaLevel").value = $scope.levelTypeMap[codeArray[2]+"m"]
					}else if(codeArray[1] === "5"){
					    document.getElementById("frameworkLevel").value = $scope.levelTypeMap[codeArray[2]+"m"]
                    }else if(codeArray[1] === "7"){
                        document.getElementById("productLevel").value = $scope.levelTypeMap[codeArray[2]+"m"]
                    }
					var value = personBudgetInfo['workload'];
					document.getElementById(id).value = inform.removeZero(value);
				});
			}
			//给人力预算汇总赋值(按阶段)
            function setPersonBudgetByStage(){
				//遍历list，取到map的key，value，给控件赋值
				angular.forEach($scope.personBudgetByStage, function (personBudgetByStage, index) {
                    var id = personBudgetByStage['codes'];
					var value = personBudgetByStage['workload'];
					document.getElementById(id).value = inform.removeZero(value);
				});
			}
            function gotoBudgetController(){
                $state.go('app.office.budgetController', {
                    isProjectBudget: '1'
                });
			}
		/**
		 * 拒绝变更
		 */
		$scope.refuseChange = function(){
            var urlData ={
    				'projectId':$scope.projectId,
    				'plmUpgradeId':'0',
    				'version':$scope.version,
    				'changeApplyStatus':"0"
        	};
            $scope.confirm('是否确认拒绝变更？', function () {
                budgetChangeApplyService.deleteChangeApply(urlData).then(function (data) {
                    layer.confirm("预算变更拒绝成功！",{
                        title:false,
                        btn:['确定']
                    },function(result){
                        layer.close(result);
                        gotoBudgetController();
                    });
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
		    });
		}

		//确认变更
        $scope.agreeChange = function(){
            var urlData ={
    				'projectId':$scope.projectId,
    				'plmUpgradeId':'0',
    				'version':$scope.version,
    				'changeApplyStatus':"0"
        	};
            $scope.confirm('是否确认同意变更？', function () {
                budgetChangeApplyService.agreeChange(urlData).then(function (data) {
                    layer.confirm(data.message,{
                        title:false,
                        btn:['确定']
                    },function(result){
                        layer.close(result);
                        gotoBudgetController();
                    });
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
		    });
        }
        /**
         * 确认弹框
         */
        $scope.confirm = function (str, func) {
            var modalInstance = $modal.open({
                templateUrl: 'myModalContent.html',
                controller: 'ModalInstanceCtrl',
                size: "sm",
                resolve: {
                    items: function items() {
                        return Trans(str);
                    }
                }
            });
            modalInstance.result.then(function () {
                func();
            });
        };
	     /**
	 	  * *************************************************************
	 	  *              方法声明部分                                 结束
	 	  * *************************************************************
	 	  */

	}]);
})();
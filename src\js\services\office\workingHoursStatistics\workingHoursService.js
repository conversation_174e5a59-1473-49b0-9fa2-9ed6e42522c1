(function () {
    'use strict';
    app.factory('workingHoursService', workingHoursService);
    workingHoursService.$inject = ["HttpService", '$rootScope'];

    function workingHoursService(HttpService, $rootScope) {
        var service = {
            getData: getData,
            getWorkingHoursInfoById: getWorkingHoursInfoById,
            getNoStoryWorkingHoursById: getNoStoryWorkingHoursById,
            getNoStoryWorkingHoursListById: getNoStoryWorkingHoursListById,
            saveProjectNoStoryWorkingHoursList: saveProjectNoStoryWorkingHoursList,
            getNoStoryTaskInfoList: getNoStoryTaskInfoList,
            deleteSplitWorkTime: deleteSplitWorkTime,
            saveSupportNoStoryWorkingHoursList: saveSupportNoStoryWorkingHoursList,
            workingHoursSync: workingHoursSync
        };
        return service;

        /**
         * 根据参数查询项目或者团队Kpi数据
         */
        function getData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'workingHoursSplit/getWorkingHoursList', urlData);
        }

        /**
         * 获取禅道项目的无需求工时
         */
        function getWorkingHoursInfoById(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'workingHoursSplit/getWorkingHoursInfoById', urlData);
        }

        /**
         * 获取项目类无需求工时和技术支持类无需求工时
         */
        function getNoStoryWorkingHoursById(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'workingHoursSplit/getNoStoryWorkingHoursById', urlData);
        }

        /**
         * 获取技术支持类无需求工时列表
         */
        function getNoStoryWorkingHoursListById(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'workingHoursSplit/getNoStoryWorkingHoursListById', urlData);
        }

        /**
         * 保存项目类无需求工时拆分列表
         */
        function saveProjectNoStoryWorkingHoursList(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'workingHoursSplit/saveProjectNoStoryWorkingHoursList', urlData);
        }

        /**
         * 获取无需求任务列表
         */
        function getNoStoryTaskInfoList(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'workingHoursSplit/getNoStoryTaskInfoList', urlData);
        }

        /**
         * 删除某项目下的工时拆分信息
         */
        function deleteSplitWorkTime(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'workingHoursSplit/deleteSplitWorkTime', urlData);
        }

        /**
         * 删除某项目下的工时拆分信息
         */
        function saveSupportNoStoryWorkingHoursList(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'workingHoursSplit/saveSupportNoStoryWorkingHoursList', urlData);
        }

        /**
         * 实时同步禅道工时
         */
        function workingHoursSync(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'workingHoursSplit/workingHoursSync', urlData);
        }
    }
})();

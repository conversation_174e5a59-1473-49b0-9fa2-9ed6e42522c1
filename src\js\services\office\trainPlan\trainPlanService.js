(function () {
    'use strict';
    app.factory('trainPlanService', trainPlanService);
    trainPlanService.$inject = ["HttpService", '$rootScope'];

    function trainPlanService(HttpService, $rootScope) {
        var service = {
            loadExcel: loadExcel,
            getInfoTarget: getInfoTarget,
            getInfo: getInfo,
            insertDetail: insertDetail,
            updateDetail: updateDetail,
            deleteDetail: deleteDetail,
            getTrainPlanById:getTrainPlanById,
            getParticipantInfo:getParticipantInfo,
            getPersonEvaluation:getPersonEvaluation,
            upEvaluates:upEvaluates,
            removePersonEvaluation:removePersonEvaluation,
            selectAvgInfo:selectAvgInfo,
            getTrainEvaluateByTrainingId:getTrainEvaluateByTrainingId
        };
        return service;
        /**
         * 查询总课时，平均得分
         * @param urlData 评价信息
         */
        function selectAvgInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'train/selectAvgInfo', urlData);
        }
        /**
         * 更新评价信息
         * @param urlData 评价信息
         */
        function upEvaluates(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'train/upEvaluates', urlData);
        }
        /**
         * 删除人员信息
         * @param urlData 培训ID
         */
        function removePersonEvaluation(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'train/removePersonEvaluation', urlData);
        }
        /**
         * 根据参数查询报表信息(学员模块)
         * @param urlData 查询参数
         */
        function getParticipantInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'train/getParticipantInfo', urlData);
        }
        /**
         * 根据参数查询报表信息
         * @param urlData 查询参数
         */
        function getInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'train/getInfo', urlData);
        }

        /**
         * 生成表格
         * @param urlData 查询参数
         */
        function loadExcel(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'peer/loadExcel', urlData);
        }

        /**
         * 获取详情页面综数据
         * @param urlData 查询参数
         */
        function getInfoTarget(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'train/getInfoTarget', urlData);
        }

        /**
         * 新增数据（Detail）
         * @param urlData 查询参数
         */
        function insertDetail(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'train/add', urlData);
        }

        /**
         * 修改数据（Detail）
         * @param urlData 查询参数
         */
        function updateDetail(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'train/update', urlData);
        }

        /**
         * 删除数据（Detail）
         * @param urlData 查询参数
         */
        function deleteDetail(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'train/delete', urlData);
        }

        /**
         * 根据id查询培训详细内容
         * @param urlData
         * @returns {*}
         */
        function getTrainPlanById(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'train/getTrainPlanById', urlData);
        }
        /**
         * 根据id查询人员评价详细内容
         * @param urlData
         * @returns {*}
         */
        function getPersonEvaluation(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'train/getPersonEvaluation', urlData);
        }
        /**
         * 根据id查询评价详情
         * @param urlData
         * @returns {*}
         */
        function getTrainEvaluateByTrainingId(urlData) {
            return HttpService.get($rootScope.getWaySystemApi + 'train/getTrainEvaluateByTrainingId', urlData);
        }
    }
})();
(function() {
    'use strict';
    app.factory('processDataForBoardService', processDataForBoardService);
    processDataForBoardService.$inject=["HttpService",'$rootScope'];

    function processDataForBoardService(HttpService,$rootScope){

        function getRateInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'ProcessData/getRateInfo',urlData);
        }

        function getReviewChart(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'ProcessData/getReviewChart',urlData);
        }

        function getReviewQuestion(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'ProcessData/getReviewQuestion',urlData);
        }

        function getReviewOutput(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'ProcessData/getReviewOutput',urlData);
        }

        function getReviewOutDocInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'ProcessData/getReviewOutDocInfo',urlData);
        }

        function getReviewContributionInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'ProcessData/getReviewContributionInfo',urlData);
        }

        return {
            getRateInfo: getRateInfo,
            getReviewChart: getReviewChart,
            getReviewQuestion: getReviewQuestion,
            getReviewOutput: getReviewOutput,
            getReviewOutDocInfo: getReviewOutDocInfo,
            getReviewContributionInfo: getReviewContributionInfo,
        };
    }
})();
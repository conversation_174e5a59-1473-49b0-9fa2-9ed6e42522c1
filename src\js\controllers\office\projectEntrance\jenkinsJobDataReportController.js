//# sourceURL=js/controllers/office/projectEntrance/jenkinsJobDataReportController.js
(function () {
    app.controller("jenkinsJobDataReportController", [ '$scope','$rootScope','$stateParams','$state','LocalCache','inform','Trans','teamEntranceDetailsService','AgreeConstant','$modal',
        function ($scope,$rootScope, $stateParams,$state,LocalCache,inform,Trans,teamEntranceDetailsService,AgreeConstant,$modal) {
             /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //分页
            $scope.pages = {
                goNum:null, // 初始化跳转页码
                star:0, //开始条数
                end:0, //结束条数
                total:0, // 总条数
                size:"5", //每页条数
                pageNum:AgreeConstant.pageNum //默认页
            };
            //sonar分页
            $scope.sonarPages = {
                goNum:null, // 初始化跳转页码
                star:0, //开始条数
                end:0, //结束条数
                total:0, // 总条数
                size:"5", //每页条数
                pageNum:AgreeConstant.pageNum //默认页
            };
            //code列表分页
            $scope.codePages = {
                goNum:null, // 初始化跳转页码
                star:0, //开始条数
                end:0, //结束条数
                total:0, // 总条数
                size:"5", //每页条数
                pageNum:AgreeConstant.pageNum //默认页
            };

            $scope.type = $stateParams.type;
            $scope. jobTotalNum = 0;
            $scope. abnormalJobNum= 0;
            $scope. runJobNum = 0;
            $scope. buildNum = 0;
            $scope. buildRate = 0;
            //sonar统计数据
            $scope. totalModuleNum = 0;
            $scope. unpassModuleNum= 0;
            $scope. totalBugNum = 0;
            $scope. totalVulnerablityNum = 0;
            $scope. totalSmellNum = 0;
            $scope. avgScore = 0;
            $scope. debtCost = 0;
            //code统计数据
            $scope.totalRepoNum = 0;
            $scope.totalCommitNum = 0;
            $scope.totalMergeNum = 0;
            $scope.teamMemberNum = 0;
            $scope.commitMoreThanThresholdNum = 0;
            $scope.commitMoreThanThresholdRatio = 0;
            $scope.totalUnstandardCommitNum = 0;
            $scope.unstandardCommitRatio = 0;


            
            //获取缓存
            $scope.formRefer = LocalCache.getObject('jenkinsJobData_formRefer');
            $scope.remark = '流水线总数：团队现有流水线总数 \n执行流水线数：指定时间段内执行的流水线数量 \n构建次数：指定时间段内构建的次数 \n构建频率：指定时间内有执行记录的流水线在一周内构建的次数（7天为一个单位，不足7天按7天算）\n异常流水线数：指定时间内构建失败率大于80%的流水线数'
            $scope.codeRemark = '代码仓库数量：团队代码仓库的数量总和 \n提交总次数（不包含merge）：团队工程师提交代码（不包含merge）的次数总和\nmerge总次数：团队工程师merge代码的次数总和\n提交代码人数（不包含merge）：团队提交代码（不包含merge）的工程师人数总和\n单次提交大于500行总次数：团队工程师单次提交代码（不包含merge）大于500行的次数总和\n单次提交大于500行占比：单次提交大于500行总次数/提交代码总次数（不包含merge）\ncommit message不合规提交次数：团队工程师提交代码（不包含merge）不符合提交规范的次数总和\ncommit message不合规提交占比：commit message不合规提交次数 /提交代码总次数（不包含merge） '
            $scope.sonarRemark = '模块总数：团队代码模块数量总和 \n不及格模块数量：团队代码模块分数不及格的模块数量总和 \nBug总数量：团队代码模块产生的Bug数量总和 \n漏洞总数量：团队代码模块产生的漏洞数量总和\n异味总数量：团队代码模块产生的异味数量总和\n平均分数：团队代码模块分数总和/团队代码模块数量总和\n预计债务返工成本（人/天）：团队代码模块的技术债务时长总和 / （60*8h）'
            
            //对原缓存进行覆盖
            // LocalCache.setObject('jenkinsJobData_formRefer', {});
            LocalCache.setObject('repoCommitData_formRefer', {});
            //初始化时间
            getSearchTime();             
            $scope.getData = getData; 			// 分页相关函数
            $scope.getSonarData = getSonarData; 
            $scope.getCodeDataList = getCodeDataList; 
            $scope.searchData = searchData;
            searchData();
         

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            function getSearchTime(){
                if ($scope.formRefer.startTime ==undefined || $scope.formRefer.startTime == null || $scope.formRefer.endTime == undefined || $scope.formRefer.endTime == null){
                   initTime();
                }
            }


            function initTime() {
                //设置默认时间
                if ($scope.formRefer.endTime==null){
                    var now = new Date();
                    var endDate = inform.format(now, 'yyyy-MM-dd');
                    var startDate = inform.format(now,"yyyy-MM-01");
                    //默认开始时间
                    $scope.formRefer.endTime = endDate;
                    $scope.formRefer.startTime = startDate;
                }
            }

            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (165 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 80);
            }

            function searchData() {
                LocalCache.setObject('jenkinsJobData_formRefer', $scope.formRefer);
                getData();
                getJenkinsStatistic();
                getSonarData();
                getCodeDataList();
                getCodetatistic();
                getSonarStatistic();
            }

           //获取所有数据以分页的形式
           function getData(pageNum) {
            $scope.save=false;
            $scope.pipeline_down = true;
            var urlData ={
                    'project': $scope.projectInfoParam.id,// 项目团队
                    'startTime': $scope.formRefer.startTime,
                    'endTime': $scope.formRefer.endTime,
                    'page': pageNum,//当前页数
                    'pageSize': $scope.pages.size//每页显示条数
                };
            teamEntranceDetailsService.getJenkinsData(urlData).then(function(data) {              
                if (data.code===AgreeConstant.code) {
                    // 项目详情
                    $scope.jenkinsJobDataList = data.data.list;
                    if ($scope.jenkinsJobDataList.length===0) {
                        inform.common(Trans("tip.noData"));
                        $scope.pages = inform.initPages();
                        $scope.pages.size = "5";
                    } else {
                        $scope. jobTotalNum = data.data.total;
                        // 分页信息设置
                        $scope.pages.total = data.data.total;
                        $scope.pages.star = data.data.startRow;
                        $scope.pages.end = data.data.endRow;
                        $scope.pages.pageNum = data.data.pageNum;
                    }
                } else {
                    inform.common(data.message);
                }
            },
            function(error) {
                inform.common(Trans("tip.requestError"));
            });
        }
  
        function getJenkinsStatistic(){
            var urlData ={
                'project': $scope.projectInfoParam.id,// 项目团队
                'startTime': $scope.formRefer.startTime,
                'endTime': $scope.formRefer.endTime
            };
            //查询统计数据
            teamEntranceDetailsService.getJenkinsStatistic(urlData).then(function (jenkinsStatisticData) {
                if (jenkinsStatisticData.code === AgreeConstant.code) {
                    jenkinsStatisticData.data = angular.fromJson(jenkinsStatisticData.data);
                    $scope.buildNum = jenkinsStatisticData.data.buildNum;
                    $scope.runJobNum = jenkinsStatisticData.data.runJobNum;
                    $scope.buildRate = jenkinsStatisticData.data.buildRate;
                    $scope.abnormalJobNum = jenkinsStatisticData.data.abnormalJobNum;
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        }


        //获取Sonar数据以分页的形式
        function getSonarData(pageNum) {
            $scope.save=false;
            $scope.sonar_down = true;
            var urlData ={
                    'project': $scope.projectInfoParam.id,// 项目团队
                    'startTime': $scope.formRefer.startTime,
                    'endTime': $scope.formRefer.endTime,
                    'page': pageNum,//当前页数
                    'pageSize': $scope.sonarPages.size//每页显示条数
                };
            teamEntranceDetailsService.getSonarData(urlData).then(function(data) {              
                if (data.code===AgreeConstant.code) {
                    // 项目详情
                    $scope.sonarDataList = data.data.list;
                    if ($scope.sonarDataList.length===0) {
                        inform.common(Trans("tip.noData"));
                        $scope.sonarPages = inform.initPages();
                        $scope.sonarPages.size = "5";
                    } else {
                        // 分页信息设置
                        $scope.sonarPages.total = data.data.total;
                        $scope.sonarPages.star = data.data.startRow;
                        $scope.sonarPages.end = data.data.endRow;
                        $scope.sonarPages.pageNum = data.data.pageNum;
                    }
                } else {
                    inform.common(data.message);
                }
            },
            function(error) {
                inform.common(Trans("tip.requestError"));
            });
        }

        function getSonarStatistic(){
            var urlData ={
                'project': $scope.projectInfoParam.id,// 项目团队
                'startTime': $scope.formRefer.startTime,
                'endTime': $scope.formRefer.endTime
            };
            //查询统计数据
            teamEntranceDetailsService.getSonarDataStatistic(urlData).then(function (sonarStatisticData) {
                if (sonarStatisticData.code === AgreeConstant.code) {
                    sonarStatisticData.data = angular.fromJson(sonarStatisticData.data);
                    $scope. totalModuleNum = sonarStatisticData.data.totalModuleNum;
                    $scope. unpassModuleNum=  sonarStatisticData.data.unpassModuleNum;
                    $scope. totalBugNum = sonarStatisticData.data.totalBugNum;
                    $scope. totalVulnerablityNum =  sonarStatisticData.data.totalVulnerablityNum;
                    $scope. totalSmellNum = sonarStatisticData.data.totalSmellNum;
                    $scope. avgScore =  sonarStatisticData.data.avgScore;
                    $scope. debtCost =  sonarStatisticData.data.debtCost;
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        }

         //获取Code数据以分页的形式
        function getCodeDataList(pageNum) {
            $scope.save=false;
            $scope.code_down = true;
            var urlData ={
                    'projectId': $scope.projectInfoParam.id,// 项目团队
                    'startTime': $scope.formRefer.startTime,
                    'endTime': $scope.formRefer.endTime,
                    'page': pageNum,//当前页数
                    'pageSize': $scope.codePages.size//每页显示条数
                };
            teamEntranceDetailsService.getCodeDataList(urlData).then(function(data) {              
                if (data.code===AgreeConstant.code) {
                    // 项目详情
                    $scope.codeDataList = data.data.list;
                    if ($scope.codeDataList.length===0) {
                        inform.common(Trans("tip.noData"));
                        $scope.codePages = inform.initPages();
                        $scope.codePages.size = "5";
                    } else {
                        // 分页信息设置
                        $scope.codePages.total = data.data.total;
                        $scope.codePages.star = data.data.startRow;
                        $scope.codePages.end = data.data.endRow;
                        $scope.codePages.pageNum = data.data.pageNum;
                    }
                } else {
                    inform.common(data.message);
                }
            },
            function(error) {
                inform.common(Trans("tip.requestError"));
            });
        }

        function getCodetatistic(){
            var urlData ={
                'projectId': $scope.projectInfoParam.id,// 项目团队
                'startTime': $scope.formRefer.startTime,
                'endTime': $scope.formRefer.endTime
            };
            //查询code统计数据
            teamEntranceDetailsService.getCodeDataStatistic(urlData).then(function (codeStatisticData) {
                if (codeStatisticData.code === AgreeConstant.code) {
                    codeStatisticData.data = angular.fromJson(codeStatisticData.data);
                    $scope.totalRepoNum = codeStatisticData.data.totalRepoNum;
                    $scope.totalCommitNum = codeStatisticData.data.totalCommitNum;
                    $scope.totalMergeNum = codeStatisticData.data.totalMergeNum;
                    $scope.teamMemberNum = codeStatisticData.data.teamMemberNum;
                    $scope.commitMoreThanThresholdNum = codeStatisticData.data.commitMoreThanThresholdNum;
                    $scope.commitMoreThanThresholdRatio = codeStatisticData.data.commitMoreThanThresholdRatio;
                    $scope.totalUnstandardCommitNum = codeStatisticData.data.totalUnstandardCommitNum;
                    $scope.unstandardCommitRatio = codeStatisticData.data.unstandardCommitRatio;
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        }
        

        function getDaysBetweenDates(start, end) {
            var startDate = new Date(start);
            var endDate = new Date(end);
            var diff = endDate.getTime()- startDate.getTime();
            var days = diff/(1000*60*60*24);  
            return days;
        }   
            /**
             * 重置
             */
            $scope.reset = function() {
                $scope.formRefer={};
                initTime();
            }


        //查看构建信息
        $scope.showBuildList = function(m){
            LocalCache.setObject("jenkinsJobData_formRefer",$scope.formRefer);
            $state.go("app.office.jobBuild", {jobName:m.jobName,
                startTime:$scope.formRefer.startTime, endTime:$scope.formRefer.endTime});
		};

        $scope.seeBuild = function(m) {
            var urlData ={
                'jobName': m.jobName,// job名称
                'startTime': $scope.formRefer.startTime,//开始时间
                'endTime': $scope.formRefer.endTime, //结束时间
                'runStatus':$scope.formRefer.runStatus, //执行状态

            };
            teamEntranceDetailsService.getBuildData(urlData).then(function (data) {
                if (data.code===AgreeConstant.code) {
                    // 项目详情
                    $scope.jobBuildDataList = data.data.list;
                    if ($scope.jobBuildDataList.length===0) {
                        inform.common(Trans("tip.noData"));
                        $scope.pages = inform.initPages();
                    } else {
                        // 分页信息设置
                        $scope.pages.total = data.data.total;
                        $scope.pages.star = data.data.startRow;
                        $scope.pages.end = data.data.endRow;
                        $scope.pages.pageNum = data.data.pageNum;
                    }
                } else {
                    inform.common(data.message);
                }
            },function () {
               inform.common(Trans("tip.requestError"));
            });
        }

          //查看仓库代码提交信息
          $scope.showCodeListDetail = function(m){
            LocalCache.setObject("repoCommitData_formRefer",$scope.formRefer);
            $state.go("app.office.repoCommitList", {repoName:m.repoName, projectId:$scope.projectInfoParam.id,
                startTime:$scope.formRefer.startTime, endTime:$scope.formRefer.endTime});
		};


        $scope.formatDeviation = formatDeviation;
        function formatDeviation(deviation){
            return Number(deviation*100).toFixed(0)+'%';
        }

        
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
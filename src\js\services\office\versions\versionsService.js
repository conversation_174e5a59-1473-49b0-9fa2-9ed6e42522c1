(function () {
    'use strict';
    app.factory('versionsService', versionsService);
    versionsService.$inject = ["HttpService", '$rootScope'];

    function versionsService(HttpService, $rootScope) {
        var service = {
        	selectData:selectData,
        	selectOne:selectOne,
        	updateInfo:updateInfo,
        	addInfo:addInfo,
        	delData:delData
        };
        return service;
        /**
         * 获取所有项目的版本信息
         */
        function selectData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'versions/selectData', urlData);
        }
        /**
         * 获取某个项目版本的具体信息
         */
        function selectOne(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'versions/selectOne', urlData);
        }
        /**
         * 修改项目版本信息
         */
        function updateInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'versions/updateInfo', urlData);
        }
        /**
         * 新增项目版本信息
         */
        function addInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'versions/addInfo', urlData);
        }
        /**
         * 删除项目版本信息
         */
        function delData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'versions/delData', urlData);
        }
    }
})();
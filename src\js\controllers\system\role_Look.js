/*
* @Author: fubaole
* @Date:   2018-02-06 11:15:32
* @Last Modified by:   fubaole
* @Last Modified time: 2018-02-24 17:49:07
*/

(function() {
  'use strict';
  app.controller("role_Look", ['$rootScope', '$scope', '$state', 'inform', 'SystemService','Trans','$stateParams','AgreeConstant',
    function($rootScope, $scope, $state, inform, SystemService,Trans,$stateParams,AgreeConstant) {

      // 树数据配置
      var leftTreeSetting = {
        data: { simpleData: { enable: true} }
      };
      $scope.getData = getData; // 初始化函数
      getData();

      // 根据ID 获取角色信息
      function getData(){
        SystemService.getRoleAndGrant($stateParams.roleId)
          .then(function(data){
            if(data.code===AgreeConstant.resultCode){
              $scope.role = data.result.role;
              $scope.grantGroup = data.result.grantGroup;
              $scope.grantRole = data.result.grantRole;
              $scope.grantOrg = data.result.grantOrg;
              $scope.grantPermission = data.result.grantPermission;
              getTreeData();
            }else{
              inform.common(data.message);
            }
          },function(){
          inform.common(Trans("tip.requestError"));
        });
      }

      // 渲染树结构数据
      function getTreeData(){
        angular.forEach($scope.grantPermission,function(res,index){
           var jsonTree = {
              "id": res.permissionId,
              "pId": res.parentId,
              "name": res.permissionName,
              "open":true
            };
            $scope.grantPermission[index] = angular.extend(jsonTree, res);
        });
        $.fn.zTree.init($("#leftTree"), leftTreeSetting, $scope.grantPermission);
      }

    }
  ]);
})();
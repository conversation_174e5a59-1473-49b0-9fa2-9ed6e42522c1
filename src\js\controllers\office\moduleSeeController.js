
(function () {
    app.controller("moduleSeeController", ['comService','$rootScope', '$scope','$state','$stateParams','$modal','repositoryService','inform','Trans','AgreeConstant',
        function (comService,$rootScope, $scope,$state,$stateParams, $modal,repositoryService,inform,Trans,AgreeConstant) {
    	/**
    	 * 条件
    	 */
    	//更新后的模块信息
    	$scope.formUpmodule ={
    			id:'',//模块id
    			modulename:'',//模块名
    			department:'',//所属部门
    			representative:'',//开发代表
    			representativeid:'',//开发代表id
    			modulefunction:'',//模块功能
    			relevantmodule:''//相关联模块
    				}
    	
    	/**
    	 * 结果
    	 */
    	
    	//修改页面模块的人员信息
    	$scope.employeeModule = [];

    	/**
    	 * 方法
    	 */
    	//初始化页面信息
    	initPages();
    	//根据仓库id回填仓库信息
    	upModule($stateParams.moduleid);
       	
    	 /**
    	 * 页面初始化
    	 */
    	function initPages() {
    		//获取系统集成研发中心的下级部门信息
    		$scope.orgList = [];
    		comService.getOrgChildren('D010053').then(function(data) {
        		if (data.data) {
        			$scope.orgList =  data.data;
        		}
            });
    	}
    	
    	 /**
      	  * / 根据id获取模块信息
      	  */
      	function upModule(moduleid) {
      		repositoryService.upModule(moduleid).then(function(data) {
                if (data.code===AgreeConstant.code) {
                   $scope.formUpmodule = angular.fromJson(data.data);
                   //根据模块id获取模块的人员信息
                   getModuleEmployee(moduleid);
                } else {
                   inform.common(data.message);
                }
             }, function() {
                inform.common(Trans("tip.requestError"));
             });
         }
    	
       /**
        * / 根据ID 获取模块的人员信息
        */
       function getModuleEmployee(moduleid) {
    	   $scope.employeeModule = [];//修改页面下模块的人员信息
    	   repositoryService.getModuleEmployee(moduleid).then(function(data) {
    		   if (data.code===AgreeConstant.code) {
    			    $scope.employeeModule = angular.fromJson(data.data);
               		angular.forEach($scope.employeeModule, function(employee, i) {//遍历人员
               			if(employee.state==="00"){
               				employee.state="正常";
               			} else if(employee.state==="01"){
               				employee.state="离职";
               			} else if(employee.state==="02"){
               				employee.state="调出";
               			}
               		});
                 } else {
                	 inform.common(data.message);
                 }
            }, function() {
                 inform.common(Trans("tip.requestError"));
            });
       	}
	}]);
})();
/*
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date:   2019-01-10 15:20:05
 * @Last Modified by:   liu<PERSON><PERSON>
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
    'use strict';
  app.factory('codeReviewService', codeReviewService);
  codeReviewService.$inject=["HttpService",'$rootScope'];

  function codeReviewService(HttpService,$rootScope){
    
    var service={
    		
    		
    		getData : getData,
        getProjectInfo:getProjectInfo,
        addCodeReview:addCodeReview,
        updateCodeReview:updateCodeReview,
        deleteCodeReview:deleteCodeReview,
        getProblem:getProblem,
        addProblem:addProblem,
        deleteProblem:deleteProblem
    };
    return service;
    
    /**
     * 删除代码走查问题信息
     */
    function deleteProblem(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'codeReview/deleteProblem', urlData);
    }
    
    /**
     * 查询代码走查问题信息
     */
    function getProblem(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'codeReview/getProblem', urlData);
    }

    /**
     * 处理问题信息
     */
    function addProblem(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'codeReview/addProblem', urlData);
    }
    /**
     * 查询代码走查信息
     */
    function getData(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'codeReview/getData', urlData);
    }

    /**
     * 查询项目信息
     */
    function getProjectInfo(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'codeReview/getProjectInfo', urlData);
    }

        /**
     * 新增项目信息
     */
    function addCodeReview(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'codeReview/addCodeReview', urlData);
    }

        /**
     * 修改项目信息
     */
    function updateCodeReview(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'codeReview/updateCodeReview', urlData);
    }

        /**
     * 删除项目信息
     */
    function deleteCodeReview(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'codeReview/deleteCodeReview', urlData);
    }
  }
})();

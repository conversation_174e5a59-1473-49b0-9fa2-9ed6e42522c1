(function () {
    app.controller("confrimBudgetController", ['budgetService','comService','$rootScope', '$scope','$state','$stateParams','$modal','inform','Trans','AgreeConstant','LocalCache','$http',
        function (budgetService,comService,$rootScope, $scope,$state,$stateParams, $modal,inform,Trans,AgreeConstant,LocalCache,$http) {
     	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */ 	
    	 // 正则校验配置           
    	$scope.limitList = AgreeConstant.limitList;
    	$scope.isProjectBudget=$stateParams.isProjectBudget;
    	$scope.projectId=$stateParams.projectId;
    	$scope.projectName=$stateParams.projectName;
    	$scope.plmUpgradeId=$stateParams.plmUpgradeId;
    	$scope.version=$stateParams.version;
    	$scope.isAddBudget=$stateParams.isAddBudget;
    	$scope.isDetail=$stateParams.isDetail;
    	//初始化人力预算
    	$scope.initPersonBudget = '0';
    	//初始化默认显示
    	$scope.detailOrAll = '2';
    	//初始化界面
    	initPages();
		$scope.getData = getData; 			
		//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		getData();	

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
	  	
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */	
		
	    //设置列表的高度
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 180);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 70);
 		    //设置返回按钮的样式
             var clientWidth = document.body.clientWidth;
             $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
             $("#buttonStyle").css({"width": 100+"px"});
 		}		
     	
 		//初始化
 		function initPages() {
            if($scope.isDetail === 'false'){
                document.getElementById("saveBudget").disabled=true;
                setTimeout(function(){
                    document.getElementById("saveBudget").disabled=false;
                },5000
                );
            }

 			// 获取缓存
 			$scope.reviewData = LocalCache.getObject('confirmBudgetData');
            // 清除缓存
            LocalCache.setObject("confirmBudgetData",{});
            
            //获取费用类型
            $scope.feeList = [];
            $scope.feeTypeMap = {};
            comService.getParamList('COSTFEE_TYPE','COSTFEE_TYPE').then(function(data) {
            	if(data.data) {
            		$scope.feeList = data.data;
            		angular.forEach($scope.feeList,function (feeType) {                            
						$scope.feeTypeMap[feeType.param_code] = feeType.param_value;                        
					});
            	}
            });
            //获取所有的岗位（汇总）
            $scope.costTitleMap = $scope.reviewData.costTitleMap;
            //获取所有的级别（汇总）
            $scope.costLevelMap = $scope.reviewData.costLevelMap;
            //获取指定项目包含的项目阶段
            $scope.firstRowList = $scope.reviewData.firstRowList;
            //获取岗位和级别
            $scope.titleLevelList = $scope.reviewData.titleLevelList;
            $scope.levelList=[];
            for(var i = 0;i<$scope.titleLevelList.length;i++){
            	for(var j = 0;j<$scope.titleLevelList[i].level.length;j++){
                	$scope.levelList.push($scope.titleLevelList[i].level[j]);
                }
            }
    	}
 		
		//获取所有数据
		function getData(){
			//获取人力投入
			$scope.personBudgetInfo = $scope.reviewData.personBudgetInfo;
			setTimeout(setData,1000);
			setTimeout(setPersionBudgetAllData,1100);
			
			function setData(){
				//遍历list，取到map的key，value，给控件赋值
				for(var i=0;i<$scope.personBudgetInfo.length;i++){
					var id = $scope.personBudgetInfo[i]['codes'];
					if ($scope.isProjectBudget !== '1') {
						id = 's'+id;
					}
					var value = $scope.personBudgetInfo[i]['workload'];
					document.getElementById(id).value = inform.removeZero(value);
            	}
			}
			
			//获取人力预算汇总
			$scope.personBudgetAll = $scope.reviewData.personBudgetAll;
			function setPersionBudgetAllData(){
				//遍历list，取到map的key，value，给控件赋值
				for(var i=0;i<$scope.personBudgetAll.length;i++){
					var id = $scope.personBudgetAll[i]['codes'];
					var value = $scope.personBudgetAll[i]['workload'];
					document.getElementById(id).value = inform.removeZero(value)==null? 0:inform.removeZero(value);
            	}
			}
			
			//获取人力预算费用明细
			$scope.amountDetail = $scope.reviewData.amountDetail;
			$scope.newAmountDetail = [];
			//将费用明细转换为对应级别表头的数组,-1是因为里面包含了‘总计’
			for(var i=0;i<$scope.costLevelMap.length-1;i++){
				var name = $scope.costLevelMap[i][1];
				$scope.newAmountDetail[i] = '0.00';
				for(var j=0;j<$scope.amountDetail.length;j++){
					if($scope.amountDetail[j].level === name){
						$scope.newAmountDetail[i] = inform.formatMoney($scope.amountDetail[j].budget);
						break;
					}
				}
        	}
			//获取人力预算费用汇总
			$scope.newAmountDetail[$scope.costLevelMap.length-1] = '0.00';
			$scope.amountAll = $scope.reviewData.amountAll;
			if($scope.amountAll.length !== 0){
				$scope.newAmountDetail[$scope.costLevelMap.length-1] = inform.formatMoney($scope.amountAll[0].budget);
			}

			//查询费用投入
			if ($scope.isProjectBudget !== '1') {
				return;
			}
			$scope.feeBudgetInfo = $scope.reviewData.feeBudgetInfo;
			//差旅费用投入明细
			$scope.travelFeeBudgetInfo = $scope.reviewData.travelFeeBudgetInfo;
			$scope.travelFeeBudgetInfoOld = JSON.parse(JSON.stringify($scope.travelFeeBudgetInfo));
		}
		//计算出差费用总计-按城市
        $scope.changeTravelFeeTotalByCity = function (m) {
            m.travelFeeTotal = m.travelPresonNum*m.everyoneTrafficFee+m.travelDays*m.everydayFee;
        };
		//获取差旅费用明细的修改
        $scope.getTravelFeeInfo = function () {
            $scope.travelFeeBudgetInfo = JSON.parse(JSON.stringify($scope.travelFeeBudgetInfoOld));
        };
        //确认差旅费用明细的修改
        $scope.changeTravelFeeTotal = function () {
            var sum = 0;
            //遍历明细，计算出总计
            for(var i=0;i<$scope.travelFeeBudgetInfo.length;i++){
        		sum = sum*1+$scope.travelFeeBudgetInfo[i].travelFeeTotal*1;
        	}
            //遍历费用集合，获取到差旅并给总计赋值
            for(var j=0;j<$scope.feeBudgetInfo.length;j++){
                if($scope.feeBudgetInfo[j].feeType==="COSTFEE_TYPE_1"){
                    $scope.feeBudgetInfo[j].feeAmount = sum;
                }
        	}
        	$scope.travelFeeBudgetInfoOld = JSON.parse(JSON.stringify($scope.travelFeeBudgetInfo));
            $("#travelFeeDetail_modal").modal("hide");
        };
		/**
         * 新增一条费用投入
         */
        $scope.addNewFee = function () {
            var judge = {
            	'feeType': '',
                'feeAmount': '',
                'feeDescription': ''
            };
            $scope.feeBudgetInfo.push(judge);
            var div = document.getElementById('add');
            div.scrollTop = div.scrollHeight;
        };
        
      //取消一行
        $scope.deleteNewFee = function (index) {
            if (index >= 0) {
                $scope.feeBudgetInfo.splice(index, 1);
            }
        };
		
		$scope.goback = function (isProjectBudget) {
            $state.go("app.office.budgetController",{
            	isProjectBudget: isProjectBudget
            });
        };
        
        //保存草稿/确认预算
        $scope.saveBudget = function(status){
        	//检验人力预算personBudget
        	$scope.personBudgetInfo = {};
        	var boxes;
        	if($scope.isProjectBudget === '1'){
        		boxes = document.getElementsByName("personBudgetPro");
        	}else{
        		boxes = document.getElementsByName("personBudgetPlm");
        	}
        	for(var i=0;i<boxes.length;i++){
        		if(boxes[i].value==null || boxes[i].value==='0' || boxes[i].value===''){
	   				continue;
	   			}
	   			var id = boxes[i].id;
	   			if ($scope.isProjectBudget !== '1') {
					id = id.substring(1);
				}
	   			$scope.personBudgetInfo[id] = boxes[i].value;
        	}
        	
        	if ($scope.isProjectBudget === '1') {
        		//检验费用预算
        		var list = [];
            	for(var item=0;item<$scope.feeBudgetInfo.length;item++){
            			if($scope.feeBudgetInfo[item].feeType==null || $scope.feeBudgetInfo[item].feeAmount==null){
            				layer.confirm("请选择费用类型并填写费用金额！",{
                                btn:['确定']
                            },function(result){
                                layer.close(result);
                            });
                			return;
            			}
            			//判断是否有重复的费用类型
            			var num = list.indexOf($scope.feeBudgetInfo[item].feeType);
	      	            if (num > -1){
	      	            	layer.confirm("费用类型有重复，请修正！",{
                                btn:['确定']
                            },function(result){
                                layer.close(result);
                            });
	      	               return;
	      	            }
	      	            list.push($scope.feeBudgetInfo[item].feeType);

            	}
                //判断是否有差旅费用，如果没有，则将差旅明细的值都设置为0
                if(list.indexOf("COSTFEE_TYPE_1") < 0){
                    for(var m=0;m<$scope.travelFeeBudgetInfo.length;m++){
                        $scope.travelFeeBudgetInfo[m].travelFeeTotal = 0;
                        $scope.travelFeeBudgetInfo[m].travelPresonNum = 0;
                        $scope.travelFeeBudgetInfo[m].travelDays = 0;
                    }
                }
			}
        	$scope.confirm('确认后不可修改，是否确认？', function () {
        		save(status);
            });
        };
        
        function save(status){
        	var data ={
    				'projectId':$scope.projectId,  			
    				'plmUpgradeId':$scope.plmUpgradeId,
    				'version':$scope.version,
    				'budgetStatus':status,
    				'changeApplyStatus':"1"
        	};
        	//后台操作（删除原有数据，插入新的数据，修改management表的预算状态，通过事务完成）
        	if ($scope.isProjectBudget !== '1') {
        		//plm预算，没有费用预算
        		$scope.feeBudgetInfo=null;
        		$scope.travelFeeBudgetInfo=null;
			}
        	var urlData = {
        			'personBudgetInfo':$scope.personBudgetInfo,
        			'feeBudgetInfo':$scope.feeBudgetInfo,
        			'travelFeeBudgetInfo':$scope.travelFeeBudgetInfo,
        			'budget':data,
    				'isAddBudget':$scope.isAddBudget
        	};
  
        	budgetService.saveBudget(urlData).then(function (data) {
                layer.confirm(data.message,{
                    title:false,
                    btn:['确定']
                },function(result){
                    layer.close(result);
                    $state.go('app.office.budgetController', {
                		isProjectBudget: $scope.isProjectBudget	
                    });
                });
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        }
        /**
         * 确认弹框
         */
        $scope.confirm = function (str, func) {
            var modalInstance = $modal.open({
                templateUrl: 'myModalContent.html',
                controller: 'ModalInstanceCtrl',
                size: "sm",
                resolve: {
                    items: function items() {
                        return Trans(str);
                    }
                }
            });
            modalInstance.result.then(function () {
                func();
            });
        };
        
        $scope.formatAmount=function (str) {
            return inform.formatMoney(str)
        };

	     /**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */	
	}]);
})();
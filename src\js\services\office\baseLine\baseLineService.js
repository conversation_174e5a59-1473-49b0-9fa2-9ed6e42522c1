/*
 * @Author: dongyinggang
 * @Date:   2019-06-13 17:50:05
 * @Last Modified by:   dongyinggang
 * @Last Modified time:  2019-06-13 17:50:05
 */
(function () {
    'use strict';
    app.factory('baseLineService', baseLineService);
    baseLineService.$inject = ["HttpService", '$rootScope'];

    function baseLineService(HttpService, $rootScope) {

        var service = {

            getBaseLine: getBaseLine,
            getBaseLineFile: getBaseLineFile,
            updateBaseLineStatus: updateBaseLineStatus

        };
        return service;

        /**
         * 分页查询
         */
        function getBaseLine(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'baseLine/getBaseLineData', urlData);
        }

        function getBaseLineFile(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'baseLine/getBaseLineFileData', urlData);
        }

        function updateBaseLineStatus(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'baseLine/updateBaseLineStatus', urlData);
        }

    }
})();

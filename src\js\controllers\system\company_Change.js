/*
 * @Author: fubaole
 * @Date:   2017-10-23 10:10:52
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-19 10:10:05
 */
(function() {
  'use strict';
  app.controller("company_Change", ['$scope', '$stateParams', 'inform', 'SystemService', '$state', 'Trans','AgreeConstant',
    function($scope, $stateParams, inform, SystemService, $state, Trans,AgreeConstant) {
      var companyName = "";
      $scope.companyTypeNew = []; // 存放选中的公司类型数据
      $scope.limitList = AgreeConstant.limitList; // 正则校验配置

      $scope.getData = getData;
      $scope.onSubmit = onSubmit; // 提交

      getCompanyType();
      getData();

      // 获取公司类型
      function getCompanyType() {
        SystemService.getDictValueListByDictTypeCode("company_type")
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.companyTypeData = data.result;
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 获取列表信息
      function getData() {
        SystemService.getCompanyById($stateParams.companyId)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.resultData = data.result;
              companyName = data.result.companyName;
              angular.forEach($scope.companyTypeData, function(i) {
                angular.forEach($scope.resultData.companyTypes, function(res) {
                  if (i.valueCode===res.companyType) {
                    i.selected = true;
                  }
                });
              });
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 判断公司是否被占用
      function onSubmit() {
        if (companyName != $scope.resultData.companyName) {
          SystemService.vaildateCompanyNameIsUsed($scope.resultData.companyName, $scope.resultData.companyId)
            .then(function(data) {
              if (data.code===AgreeConstant.resultCode && !data.result) {
                setData();
              } else {
                inform.common(Trans("tip.nameIsUser"));
                return;
              }
            }, function() {
              inform.common(Trans("tip.requestError"));
              return;
            });
        } else {
          setData();
        }
      }

      // 获取选中的公司类型
      function updataType() {
        $scope.companyTypeNew = [];
        angular.forEach($scope.companyTypeData, function(i) {
          if (i.selected) {
            $scope.companyTypeNew.push(i.valueCode);
          }
        });
        if ($scope.companyTypeNew.length === 0) {
          inform.common(Trans('company.placeholderType'));
          return false;
        }
        return true;
      }

      // 保存修改信息
      function setData() {
        if (updataType()) {
          SystemService.saveOrUpdateCompany($scope.resultData, $scope.companyTypeNew.toString())
            .then(function(data) {
              if (data.code===AgreeConstant.resultCode) {
                inform.common(Trans("tip.saveSuccess"));
                $state.go("app.system.company_Management");
              } else {
                inform.common(data.message);
              }
            }, function() {
              inform.common(Trans("tip.requestError"));
            });

        }
      }

    }
  ]);
})();
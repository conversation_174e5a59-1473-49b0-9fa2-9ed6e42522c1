(function() {
	'use strict';
	app.factory('projectQualityService', projectQualityService);
	projectQualityService.$inject = [ "HttpService", '$rootScope' ];

	function projectQualityService(HttpService, $rootScope) {
		var service = {
			getData : getData,
			getDetailData : getDetailData,
			upInfo : upInfo
		};
		return service;

		/**
		 * 根据参数查询项目
		 * @param urlData 查询参数 
		 */
		function getData(urlData) {
			return HttpService.post($rootScope.getWaySystemApi
					+ 'projectquality/getData', urlData);
		}
		/**
		 * 根据项目ID查询项目详情
		 * @param urlData 查询详情 
		 */
		function getDetailData(urlData) {
			return HttpService.post($rootScope.getWaySystemApi
					+ 'projectquality/getDetailData', urlData);
		}
		/**
		 * 同步详情数据
		 * @param urlData 同步数据 
		 */
		function upInfo(urlData) {
			return HttpService.post($rootScope.getWaySystemApi
					+ 'projectquality/upInfo', urlData);
		}

	}
})();

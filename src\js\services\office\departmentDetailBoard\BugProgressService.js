(function() {
    'use strict';
    app.factory('BugProgressService', BugProgressService);
    BugProgressService.$inject=["HttpService",'$rootScope'];

    function BugProgressService(HttpService,$rootScope){

        /**
         * 查询不同状态下bug数量
         */
        function getBugProgressCount(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'DeptBugProgress/getBugProgressCount',urlData);
        }

        /**
         * 获取bug进展详情
         */
        function getBugProgressDetail(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'DeptBugProgress/getBugProgress',urlData);
        }

        return {
            getBugProgressCount: getBugProgressCount,
            getBugProgressDetail: getBugProgressDetail,
        };
    }
})();
/*
 * @Author: liu<PERSON><PERSON>
 * @Date:   2020-12-08 10:37:05
 */
(function() {
    'use strict';
    app.factory('projectTeamService', projectTeamService);
    projectTeamService.$inject=["HttpService",'$rootScope'];

    function projectTeamService(HttpService,$rootScope){

        var service={
            getProjectTeam:getProjectTeam,
            getProjectTeamDetail:getProjectTeamDetail,
            save:save,
            syn:syn
        };
        return service;

        /**
         * 获取项目团队
         */
        function getProjectTeam(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'projectTeam/getAllProject', urlData);
        }
        /**
         * 获取项目团队详细
         */
        function getProjectTeamDetail(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'projectTeam/getProjectTeamInfo', urlData);
        }
        /**
         * 保存项目团队详细
         */
        function save(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'projectTeam/updateProjectTeam', urlData);
        }
        /**
         * 同步项目成员
         */
        function syn(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'projectTeam/synProjectTeam', urlData);
        }
    }
})();

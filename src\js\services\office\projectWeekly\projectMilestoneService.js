//# sourceURL=js/services/office/projectWeekly/projectMilestoneService.js
(function() {
    'use strict';
  app.factory('projectMilestoneService', projectMilestoneService);

    projectMilestoneService.$inject=['HttpService','$rootScope'];

  function projectMilestoneService(HttpService,$rootScope){
    var service={
      getProjectMilestoneInfo:getProjectMilestoneInfo,
      addProjectMilestoneInfo:addProjectMilestoneInfo,
      updateProjectMilestoneInfo:updateProjectMilestoneInfo,
      deleteProjectMilestoneInfo:deleteProjectMilestoneInfo,
      selectByProjectId:selectByProjectId,
      updateMilestoneInfo:updateMilestoneInfo
    };

    return service;
    /**
     * 获取所有的信息
     */
    function getProjectMilestoneInfo(urlData){
      return HttpService.post($rootScope.getWaySystemApi + 'projectMilestone/getProjectMilestoneInfo', urlData);
    }

    /**
     * 新增里程碑
     */
    function addProjectMilestoneInfo(urlData){
      return HttpService.post($rootScope.getWaySystemApi + 'projectMilestone/addMilestoneInfo', urlData);
    }

    /**
     * 修改里程碑
     */
    function updateProjectMilestoneInfo(urlData){
      return HttpService.post($rootScope.getWaySystemApi + 'projectMilestone/updateProjectMilestoneInfo', urlData);
    }

    /**
     * 删除里程碑
     */
    function deleteProjectMilestoneInfo(urlData){
      return HttpService.post($rootScope.getWaySystemApi + 'projectMilestone/deleteProjectMilestoneInfo', urlData);
    }
    /**
     * 根据项目id判断项目偏差报表是否被禁止，若被禁止，则为true
     */
    function selectByProjectId(urlData){
      return HttpService.post($rootScope.getWaySystemApi + 'projectMilestone/selectByProjectId', urlData);
    }
    /**
     * 修改里程碑信息(根据flag判断是否有修改权限，即在项目信息管理中已被设置禁止项目计划进度偏差统计的范围可以进行新增、修改、删除)
     */
    function updateMilestoneInfo(urlData){
      return HttpService.post($rootScope.getWaySystemApi + 'projectMilestone/updateMilestoneInfo', urlData);
    }
  }
})();
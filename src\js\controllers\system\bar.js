/*
 * @Author: fubaole
 * @Date:   2018-01-03 10:41:47
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-30 10:00:44
 */
(function() {
  'use strict';
  app.controller("barCtrl", ['$scope','$rootScope', 'inform', '$timeout', 'Trans', '$interval', '$location',
    function($scope,$rootScope, inform, $timeout, Trans, $interval, $location) {

      $scope.getData = getData;
      $scope.initConfig = initConfig;
      var option = {};
      initConfig();
      getData();

      function getData(){
        option = {
          color: ['#3398DB'],
          tooltip: {
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
              type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [{
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            axisTick: {
              alignWithLabel: true
            }
          }],
          yAxis: [{
            type: 'value'
          }],
          series: [{
            name: '直接访问',
            type: 'bar',
            barWidth: '60%',
            data: [10, 52, 200, 334, 390, 330, 220]
          }]
          };

          //tip:接口数据成功后，调用此方法，渲染页面
          reloadPageData(option);
      }

      //接口获取的数据，执行数据渲染操作
      function reloadPageData(option){
        if($scope.$parent.screenFlag){
       
            $timeout(function(){
              $scope.fullBar.setOption(option,true);
            },0);
          }else{
            $timeout(function(){
              $rootScope.bar.setOption(option,true);
            },0);
          }
      }
      
      function initConfig(){
        if($scope.$parent.screenFlag){
        // 全屏图表
        $('.fullscreen #bar').width(document.body.clientWidth*0.85);
        $('.fullscreen #bar').height(document.body.clientHeight*0.6);
        console.log($('.fullscreen #bar').height());
        console.log($('.fullscreen #bar').width());
        $timeout(function(){
          $scope.fullBar = echarts.init($('.fullscreen #bar')[0]);
          $scope.fullBar.setOption(option,true);
        },0);
      }else{
        $timeout(function(){
          $rootScope.bar = echarts.init(document.getElementById('bar'));
          $rootScope.bar.setOption(option,true);
        },0);
      }
      }

      $scope.$on('reload', function(e, id) {
          console.log("父级传来的数据ID"+id+"根据ID重新加载该模块");
          //tip:根据接口给的contentId，判断if(contentId===id),则执行刷新操作,调用getData();
      });

      var timeout_upd = $interval(function(){
        if ($location.path() === '/app/index_bench' || $location.path().indexOf('preview_page')!== -1) {
          getData();
        }
      } ,16000);

      // 清除定时器
      $scope.$on('$destroy',function(){
        $interval.cancel(timeout_upd);
      });
      
    }
  ]);
})();
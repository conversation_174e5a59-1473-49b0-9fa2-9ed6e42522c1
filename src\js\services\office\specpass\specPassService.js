/*
 * @Author: sun<PERSON>xina
 * @Date:   2019-05-16 16:37:05
 * @Last Modified by:   sunqixian
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
    'use strict';
  app.factory('specPassService', specPassService);
  specPassService.$inject=["HttpService",'$rootScope'];

  function specPassService(HttpService,$rootScope){
    
	var service={
			getLineInfo:getLineInfo,
			getDepInfo:getDepInfo,
			getProjectDetailsInfo:getProjectDetailsInfo,
			addInfo:addInfo,
			upInfo:upInfo,
			delInfo:delInfo,
      loadDataExcel:loadDataExcel
	};
    return service;
    
    /**
     * 获取产品线所有的信息
     */
    function getLineInfo(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'specpass/getLineInfo', urlData);
    }
    
    /**
     * 获取部门所有的信息
     */
    function getDepInfo(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'specpass/getDepInfo', urlData);
    }
    
    /**
     * 获取某个项目下的信息
     */
    function getProjectDetailsInfo(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'specpass/getProjectDetailsInfo', urlData);
    }
    
    /**
     * 添加信息
     */
    function addInfo(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'specpass/addInfo', urlData);
    }
    
    /**
     * 更新信息
     */
    function upInfo(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'specpass/upInfo', urlData);
    }
    
    /**
     * 删除信息
     */
    function delInfo(id) {
        return HttpService.get($rootScope.getWaySystemApi + 'specpass/delInfo/'+id);
    }

    /**
     * Excel导出
     */
     function loadDataExcel(urlData) {
    	return HttpService.get($rootScope.getWaySystemApi+'specpass/loadDataExcel',urlData);
     }

  }
})();

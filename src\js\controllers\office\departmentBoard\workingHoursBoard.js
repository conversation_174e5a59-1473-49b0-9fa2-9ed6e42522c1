(function () {
    app.controller("workingHoursBoard", ['$rootScope','comService', '$scope', '$stateParams', '$state',
        function ($rootScope,comService,$scope, $stateParams, $state) {
            // 顶部部门选择
            if($stateParams.typeSelect){
                $scope.typeSelect = $stateParams.typeSelect;
            } else {
                $scope.typeSelect = '1';
            }
            // 部门工时跳转到项目工时带过去的orgCode
            if($stateParams.orgCode){
                $scope.orgCode = $stateParams.orgCode;
            } else {
                $scope.orgCode = '';
            }
            // 系研工时top5跳转到项目工时传递的排序字段
            if($stateParams.sortType){
                $scope.sortType = $stateParams.sortType;
            } else {
                $scope.sortType = '';
            }

            $scope.toOther = function (typeSelect) {
                $state.go('app.office.workingHoursBoard', {
                    'typeSelect': typeSelect,
                    'orgCode': null,
                    'sortType': null
                });
            }
        }]);
})();
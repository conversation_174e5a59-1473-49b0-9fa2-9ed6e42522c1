(function () {
    'use strict';
    app.controller("projectQualityReportDetailController", ['$scope','$state','comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','$stateParams','projectQualityService',
        function ($scope,$state,comService, $rootScope, inform, Trans, AgreeConstant,$stateParams,projectQualityService) {
    	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	$scope.limitList = AgreeConstant.limitList; // 正则校验配置
    	
    	var param = $stateParams.projectId.split(",");
    	$scope.projectId = param[0];
    	$scope.type = param[1];
    	$scope.projectName = $stateParams.projectName;
    	$scope.getData = getData;
    	initPages();
    	//设置列表的高度
        setDivHeight();
        //窗体大小变化时重新计算高度
        $(window).resize(setDivHeight);
        
        /**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
    		
		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */	

        //初始化报表内容
        function initPages() {
        	$scope.tableData=[
        	    {"target1":"项目计划","fileList":[
                    {"id":1,"secondTarget":"计划完成率","targetValue":"","experssion":
                     "项目计划完成率=1-（里程碑1偏差率*（里程碑1计划完成时间-里程碑1计划开发时间 + 1）+里程碑2偏差率*（里程碑2计划完成时间-里程碑2计划开发时间 + 1）+......+里程碑n偏差率*（里程碑n计划完成时间-里程碑n计划开发时间 + 1））/项目总时长\n"
                        + "项目总时长： （瀑布类型）项目所有里程碑中最晚的计划结束时间-项目的开始时间\n                   （敏捷项目）项目所有里程碑中最晚的计划结束时间-项目第一个里程碑的开始时间 + 1",
                     "planWeight":"","mark":"95%＜计划完成率＜=100%，81-100分\n"
                     + "90%＜=计划完成率＜95%，60-80分\n"
                     + "80＜计划完成率＜90%，50-60分\n"
                     + "计划完成率＜80%，0-50分",
                     "actualWeight":"","score":"","scoreActual":"","leaderScore":"","leaderScoreActual":""
                    }
                 ]},
                 {"target1":"工程过程","fileList":[
                    {"id":2,"secondTarget":"代码质量","targetValue":"","experssion":"代码质量报告中定义",
                     "planWeight":"", "mark":"区间[91-100]:91-100\n"
                     + "区间[71-90]:71-90分\n"
                     +"区间[0-70]:0-70分",
                     "actualWeight":"","score":"","scoreActual":"","leaderScore":"","leaderScoreActual":""
                    },
                    {"id":3,"secondTarget":"开发过程返工工时","targetValue":"","experssion":"修正bug工时+修正评审问题工时+其他开发过程返工/总工时",
                     "planWeight":"","mark":"返工工时＜=3%,80-100分\n"
                     + "3%＜返工工时＜=5%,60-80分\n"
                     + "5%＜返工工时＜=10%,50-60分\n"
                     +"返工工时＞10%，0-50分",
                     "actualWeight":"","score":"","scoreActual":"","leaderScore":"","leaderScoreActual":""
                    },
                    {"id":4,"secondTarget":"特殊放行","targetValue":"","experssion":"特殊放行版本数/项目发布总版本数",
                     "planWeight":"","mark":"无特殊放行，100分\n"
                     + "特殊放行1次，80分\n"
                     + "特殊放行2次，60分\n",
                     "actualWeight":"","score":"","scoreActual":"","leaderScore":"","leaderScoreActual":""
                    },
                    {"id":5,"secondTarget":"上线后bug数量及严重程度","targetValue":"","experssion":"上线后bug数量及严重程度",
                     "planWeight":"","mark":"区间[1-5个]：80-100分\n"
                     + "区间[6-10个]：60-80分\n"
                     +"区间[10-15]：50-60分\n"
                     +"区间[15个以上]：0-50分\n"
                     +"其中，事故：0分，严重问题数量超过3个，0分",
                     "actualWeight":"","score":"","scoreActual":"","leaderScore":"","leaderScoreActual":""
                    },
                    {"id":6,"secondTarget":"上线后bug返工工时比率","targetValue":"","experssion":"线上问题处理工时/周期内总工时",
                     "planWeight":"","mark":"返工工时＜=3%,80-100分\n"
                     + "3%＜返工工时＜=5%,60-80分\n"
                     + "5%＜返工工时＜=10%，50-60分\n"
                     + "返工工时＞10%，0-50分",
                     "actualWeight":"","score":"","scoreActual":"","leaderScore":"","leaderScoreActual":""
                    }
                 ]},
                 {"target1":"质量保证","fileList":[
                     {"id":7,"secondTarget":"过程符合度","targetValue":"","experssion":"计算公式：1-（致命问题数*2+严重问题数*1.5+一般问题数*1.2+轻微问题数*1）/检查总项数\n"
                      +"（制定项目过程符合度计算模板）",
                      "planWeight":"","mark":"\n"
                      + "90%<过程符合度<=100%,80-100分\n"
                      + "80%<=过程符合度<=90%，60-80分\n"
                      + "70%<=过程符合度<80%，50-60分\n"
                      + "过程符合度<70%，0-50分\n",
//
                      "actualWeight":"","score":"","scoreActual":"","leaderScore":"","leaderScoreActual":""
                    }
                 ]},
                 {"target1":"配置管理","fileList":[
                     {"id":8,"secondTarget":"基线率","targetValue":"","experssion":"已建立完成基线/计划要建立的基线数",
                      "planWeight":"","mark":"95%＜基线率＜=100%,80-100分\n"
                      + "90%＜=基线率＜95%,60-80分\n"
                      + "80%＜=基线率＜90%,50-60分\n"
                      + "基线率＜80%，0-50分",
                      "actualWeight":"","score":"","scoreActual":"","leaderScore":"","leaderScoreActual":""
                     }
                 ]}, 
                 {"target1":"工时利用率","fileList":[
                     {"id":9,"secondTarget":"工时利用率","targetValue":"","experssion":"实际工时/计划工时",
                      "planWeight":"","mark":"90%＜=工时利用率＜=120%,60-100分\n"
                      + "80%＜=工时利用率＜90%，50-60分\n"
                      + "120%＜工时利用率＜=130%，50-60分\n"
                      + "工时利用率＜80%，或工时利用率>130%,0-50分",
                      "actualWeight":"","score":"","scoreActual":"","leaderScore":"","leaderScoreActual":""
                     }
                 ]}
            ];
        	//获取二级指标
        	$scope.secondTarget = [];
        	comService.getParamList('PROJECT_QUALITY', 'SECOND_TARGET').then(function (data) {
        		if (data.data) {
        			$scope.secondTarget = data.data;
        			getData();
        		}
        	});
        }   
		
        /**
         * 设置列表的高度
         */
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (150 + 80);
            $("#divTBDisDetail").height(divHeight);
            $("#subDivTBDisDetail").height(divHeight - 90);
        }
        /**
         * 分数发生变化时,计算该行计算得分
         */
        $scope.change = function (item) {
        	if (item.actualWeight==null || item.actualWeight === "" || item.leaderScore==null || item.leaderScore === ""){
        		item.leaderScoreActual = 0;
        	} else {
            	item.leaderScoreActual = ((item.actualWeight / 100) * item.leaderScore).toFixed(2);
        	}
        };
        /**
         * 分数发生变化时,重新计算总分
         */
        $scope.countChange = function () {
        	var sum = 0.00;
        	//遍历一级指标
        	angular.forEach($scope.tableData, function (target1, index1) {
        		//遍历一级指标下的二级指标
        		angular.forEach(target1.fileList, function (m, index) {
        			if (m.leaderScoreActual==null || m.leaderScoreActual === "" || m.actualWeight==null || m.actualWeight === ""){
        				sum = sum*1 + 0*1;
        			} else {
        				sum = (sum*1 + m.leaderScoreActual*1).toFixed(2);
        			}
        			
        		});
            });
        	$scope.count = sum;
        };
        /**
         * 权重发生变化时，计算总权重与总分
         */
        $scope.countChangePercent = function () {
        	var sum = 0.00;
        	var sumPercent = 0.00;
        	//遍历一级指标
        	angular.forEach($scope.tableData, function (target1, index1) {
        		//遍历一级指标下的二级指标
        		angular.forEach(target1.fileList, function (m, index) {
        			if (m.actualWeight==null || m.actualWeight === ""){
        				sumPercent = sumPercent*1 + 0*1;
        			} else {
        				sumPercent = (sumPercent*1 + m.actualWeight*1).toFixed(2);
        			}
        			if (m.leaderScoreActual==null || m.leaderScoreActual === "" || m.actualWeight==null || m.actualWeight === ""){
        				sum = sum*1 + 0*1;
        			} else {
        				sum = (sum*1 + m.leaderScoreActual*1).toFixed(2);
        			}
        			
        		});
            });
        	$scope.count = sum;
        	$scope.countPercent = sumPercent;
        };
        /**
         * 返回报表页面
         */
        $scope.goSee = function () {
        		$state.go('app.office.projectQualityReportController',{
                    cname:$stateParams.cname,
                    productLine:$stateParams.productLine,
                    department:$stateParams.department
        		});
        };
        /**
         * 返回管理页面
         */
        $scope.goUp = function () {
        	$state.go('app.office.projectQualityReportManagement',{
                cname:$stateParams.cname,
                productLine:$stateParams.productLine,
                department:$stateParams.department
    		});
        };
        
        /**
         * 同步方法
         */
        $scope.upInfo= function () {
        	//遍历一级指标
        	angular.forEach($scope.tableData, function (target1, index1) {
        		//遍历一级指标下的二级指标
        		angular.forEach(target1.fileList, function (m, index) {
        			var secondTarget = "";
        			//获取二级指标对应的code值
        			angular.forEach($scope.secondTarget, function (target) {
        				if (target.param_value === m.secondTarget){
        					secondTarget = target.param_code;
        				}
        			});
        			var urlData = {
                        'projectId': $scope.projectId,
                        'secondTarget': secondTarget,//二级指标
                        'actualWeight': m.actualWeight,//权重
                        'leaderScore': m.leaderScore,//分数
                        'leaderScoreActual': m.leaderScoreActual//计算得分
        			};
        			projectQualityService.upInfo(urlData).then(function (data) {
        				inform.common(data.message);
        			},
        			function () {
        				inform.common(Trans("tip.requestError"));
        			});
        		});
            });
        	//同步总分入数据库
        	var sum = {
        		'projectId':$scope.projectId,
        		'secondTarget': "0000",
        		'leaderScoreActual': $scope.count,//总分数
        		'actualWeight':$scope.countPercent//实际占比
        	};
        	projectQualityService.upInfo(sum).then(function (data) {
				inform.common(data.message);
			},
			function () {
				inform.common(Trans("tip.requestError"));
			});
        };
        
        /**
         * 获取项目的详情信息
         */
        function getData(){
        	$scope.itemList = [];
            var urlData ={
            	'projectId':$scope.projectId
            };
            projectQualityService.getDetailData(urlData).then(function(data){
                var jsonData = data.data;
                $scope.itemList = jsonData;  
                //遍历一级指标
            	angular.forEach($scope.tableData, function (target1) {
            		//遍历一级指标下的二级指标
            		angular.forEach(target1.fileList, function (m) {
            			//计算总权重
        				var sumCount = 0;
        				var sum = 0;
            			angular.forEach($scope.itemList, function (item) {
            				angular.forEach($scope.secondTarget, function (target) {
                                //code代码转换为汉字
                                if (target.param_code === item.secondTarget){
                                	item.secondTarget = target.param_value;
                                }
                            });
            				//二级指标综合评分项为0000，单独展示
            				if (item.secondTarget === "合计得分"){
            					$scope.count = item.leaderScoreActual;
            					$scope.sumcount = item.scoreActual;
            				} else {
            					sum = (sum*1 + item.actualWeight*1).toFixed(0);
                				sumCount = (sumCount*1 + item.planWeight*1).toFixed(0);
            				}
            				
            				if (m.secondTarget === item.secondTarget){
            					//赋值给页面显示
            					m.targetValue = item.targetValue;
            					m.planWeight= item.planWeight;
            					m.actualWeight = item.actualWeight;
            					m.score = item.score;
            					m.scoreActual= item.scoreActual;
            					if ($scope.type === "1"){
            						m.leaderScore = item.leaderScore;
            						m.leaderScoreActual = item.leaderScoreActual;
            					}
            					if ($scope.type === "2"){
            						if (!item.leaderScore > 0.00){
            							m.leaderScore = item.score.toFixed(0);
            							m.leaderScoreActual = (m.leaderScore * (item.actualWeight/100)).toFixed(2);
            						}else {
            							m.leaderScore = item.leaderScore.toFixed(0);
            							m.leaderScoreActual = (m.leaderScore * (item.actualWeight/100)).toFixed(2);
            						}
            					}
            					
            					if (m.secondTarget === "代码质量"){
            						m.actualValue = (item.actualValue*1).toFixed(2);
                				} else if (m.secondTarget === "上线后bug数量及严重程度"){
                					if ( null != item.actualValue && item.actualValue !== "" && item.actualValue !== "0"){
                						//1-事故 2-严重 3-一般 4-建议
                						var param = item.actualValue.split(",");
                						angular.forEach(param, function (one, i) {
                							var one1 = param[i];
                							var value = one1.split("-");
                							if (value[0] === '1'){
                								value[0] = "事故";
                							} else if (value[0] === '2'){
                								value[0] = "严重";
                							} else if (value[0] === '3'){
                								value[0] = "一般";
                							} else {
                								value[0] = "建议";
                							}
                							if (m.actualValue != null){
                								m.actualValue = m.actualValue + value[0] + ":" + value[1] + "个\n";
                							} else {
                								m.actualValue = value[0] + ":" + value[1] + "个\n";
                							}
                						});
                					} else {
                                        m.actualValue = "0";
                                    }
                				} else {
                					if (item.actualValue === ""){
                						m.actualValue = item.actualValue;
                					} else {
                						m.actualValue = (item.actualValue*1).toFixed(2) + "%";
                					}
                				}
            				}
                          });
            			$scope.countPercent = sum;
            			$scope.sumcountPercent = sumCount;

            			//计算评委打分总计
                        $scope.countChangePercent();
            		   });
        		    });
                });              
            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
         } 
    ]);
})();
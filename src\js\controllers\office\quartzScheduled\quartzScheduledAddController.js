(function () {
    'use strict';
    app.controller("quartzScheduledAddController", ['$scope','$state','comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','quartzScheduledService','$stateParams','LocalCache', '$modal','$http',
        function ($scope,$state,comService, $rootScope, inform, Trans, AgreeConstant,quartzScheduledService,$stateParams,LocalCache, $modal,$http) {
    	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */

		//获取全部定时任务信息
		quartzScheduledService.getTotalTask(0,'add').then(function (data) {
			$scope.tasksList = data.data;
		});
        $("div.input-group input").attr("disabled","disabled");

		//任务状态下拉框 disabled
		$scope.jobDisabledSelect = [{
			value: '0',
			label: '启用'
		},{
			value: '1',
			label: '禁用'
		}];


        // 设置侧边的高度,随窗口变动
        inform.autoHeight();
        window.onresize = inform.autoHeight;

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */
			/**
			 * 添加信息
			 */
	   	 	$scope.addInfo = function() {
				var afterTaskIdList = '';
				if(typeof($scope.formInsertin.afterTaskIdList) !== "undefined"){
					afterTaskIdList = ($scope.formInsertin.afterTaskIdList).join(",")
				}
	   	 		var cronValue = $('#cron').val();
	   	 		if(cronValue === ''){
	   	 			inform.common('cron表达式不能为空');
	   	 			return;
	   	 		}
	    	 	var urlData = {
	   				 'jobName':$scope.formInsertin.jobName,//任务名称
	   				 'disabled':$scope.formInsertin.disabled,//任务状态
	   				 'cronExpression': cronValue,//corn表达式
	   				 'remark':$scope.formInsertin.remark,//备注：
	   				 'beanName':$scope.formInsertin.beanName,//单例名称：
	   				 'methodName':$scope.formInsertin.methodName,//方法名称：
	   				 'params':$scope.formInsertin.params,//参数
					 'afterTaskIds':afterTaskIdList//后置任务Id
	   		 	};
	    	 	quartzScheduledService.insertScheduleJob(urlData).then(function(data){
	    	 		if(data.code === AgreeConstant.code) {
	    	 			inform.common(data.message);
	    	 			//添加问题内容
	    	 			$scope.formInsertin = {
	    	 				jobName:'', //任务名称
							disabled:'', //任务状态
	    	 				remark:'',//备注
	    	 				beanName:'', //单例名称
	    	 				methodName:'', //方法名称
	    	 				params:''//参数
	    	 			};
	    	 			$state.go('app.office.quartzScheduledController',null);
	    	 		} else{
	    	 			inform.common(data.message);
	    	 		}
	    	 	}, function(error) {
	    	 		inform.common(Trans("tip.requestError"));
	    	 	});
	   	 	};
	     /**
          * *************************************************************
          *              方法声明部分                                 结束
          * *************************************************************
          */
         } 
    ]);
})();

(function() {
    'use strict';
  app.factory('projectManagementService', projectManagementService);
  projectManagementService.$inject=["HttpService",'$rootScope'];

  function projectManagementService(HttpService,$rootScope){
    
    var service={
        getProjectInfoList:getProjectInfoList,
        deleteProjectInfo:deleteProjectInfo,
        updateProjectInfo:updateProjectInfo,
        addProjectInfo:addProjectInfo,
        addGroupInfo:addGroupInfo,
        getProductTypeNameList:getProductTypeNameList,
        getRelationByCompanyProjectId:getRelationByCompanyProjectId,
        getProjectInfoById:getProjectInfoById,
        getProjectInfoByCname:getProjectInfoByCname
     //   getCurrentProjectProduceType:getCurrentProjectProduceType
    };
    return service;

    /**
     * 分页查询项目或团队信息
     */
    function getProjectInfoList(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'projectmanagement/getProjectInfoList', urlData);
    }
    /*删除项目或团队信息*/
    function deleteProjectInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'projectmanagement/deleteProjectInfo', urlData);
    }
    /**
     * 修改项目或团队信息
     */
    function updateProjectInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'projectmanagement/updateProjectInfo', urlData);
    }
    /**
     * 项目导入
     */
    function addProjectInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'projectmanagement/addProjectInfo', urlData);
    }

    //团队导入
    function addGroupInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'projectmanagement/addGroupInfo', urlData);
    }
    //查询所有的产品类别及名称
    function getProductTypeNameList(productLineCode) {
        return HttpService.get($rootScope.getWaySystemApi + 'projectmanagement/getProductTypeNameList',{'productLineCode':productLineCode});
    }
    //按项目名查询指定项目信息
    function getProjectInfoByCname(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'projectmanagement/getProjectInfoByCname', urlData);
    }
    //按项目ID查询指定项目信息
    function getProjectInfoById(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'projectmanagement/getProjectInfoById', urlData);
    }
    //按项目名查询指定项目信息
    function getRelationByCompanyProjectId(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'projectmanagement/getRelationByCompanyProjectId', urlData);
    }
    //查询指定项目的产品类别及名称
  /*  function getCurrentProjectProduceType(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'projectmanagement/getCurrentProjectProduceType', urlData);
    }*/

  }
})();

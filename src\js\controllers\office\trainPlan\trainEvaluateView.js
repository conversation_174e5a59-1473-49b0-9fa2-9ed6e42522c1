/*
 * @Author: liyunmeng
 * @Date:   2020-08-03
 */
(function () {
    app.controller("trainEvaluateView", ['comService', '$rootScope', '$scope', 'trainPlanService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http','$stateParams',
        function (comService, $rootScope, $scope, trainPlanService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http,$stateParams) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.getData = getData;
            //获取初始信息
            getData();

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 获取产品线
             */
            function getData(){
                $scope.viewDetails=[];
                $scope.title = [];
                var urlData = {
                    'trainingId':$stateParams.id,
                    'paramName' :'TRAININGEVALUATE'
                };
                trainPlanService.getTrainEvaluateByTrainingId(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        var jsonData = data.data;
                        var itemList = jsonData.typeList;
                        $scope.title = jsonData.title;
                        var tmpArray = [];
                        angular.forEach(jsonData.typeList,function(item,i) {
                            var arrayTmp =[];
                            angular.forEach(jsonData.title,function(key,n){
                                var s = item[key];
                                if(null == s) {
                                    s = '';
                                }
                                arrayTmp.push(s);
                            });
                            tmpArray.push(arrayTmp);
                        });
                        $scope.score = tmpArray;
                        for(var i=0;i<$scope.score.length;i++) {
                            $scope.score[i].unshift(itemList[i].employeeName);
                        }
                    }
                });
            }
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight);
                $("#divTBDis1").height(divHeight);
                $("#subDivTBDis1").height(divHeight);
            }

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();

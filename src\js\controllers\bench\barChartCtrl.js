(function () {
    'use strict';
    app.controller("barChartCtrl", ['$scope', '$rootScope', 'inform', 'Trans','AgreeConstant',
        function ($scope, $rootScope, inform, Trans, AgreeConstant) {
    	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	//设置柱状图
    	barTypeOptionFun();
    	/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */

		 /**
		 * 用formatter回调函数自定义显示多项数据内容
		 **/
		 function formatterCall (params, ticket, callback) {

            var htmlStr = '';
            for(var i=0;i<params.length;i++){
                var param = params[i];
                var xName = param.name;//x轴的名称
                var seriesName = param.seriesName;//图例名称
                var value = param.value;//y轴值
                var color = param.color;//图例颜色
                if (!value) {
                    continue;
                }
                if(i===0){
                    htmlStr += xName + '<br/>';//x轴的名称
                }
                htmlStr +='<div>';
                //为了保证和原来的效果一样，这里自己实现了一个点的效果
                htmlStr += '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:'+color+';"></span>';

                // 文本颜色设置--2020-07-23(需要设置,请解注释下面一行)
                //htmlStr += '<span style="color:'+color+'">';

                //圆点后面显示的文本
                htmlStr += seriesName + '：' + value + '个';

                // 文本颜色设置--2020-07-23(需要设置,请解注释下面一行)
                //htmlStr += '</span>';

                htmlStr += '</div>';
            }
            return htmlStr;
         }

        /**
         * 设置柱状图数据
         */
        function barTypeOptionFun() {
        	$scope.myChartsBarType = $scope.myChartsBarType ? $scope.myChartsBarType : echarts.init(document.getElementById('barChart'));
        	$scope.barOption = {
        			//标题
            		title:{
            			text:'个人质量信息统计',
            		    top:'bottom',
            		    left:'center',
            		    textStyle:{
            		    	fontSize: 14,
            		        fontWeight: '',
            		        color: '#333'
            		    },
            		},
        		    tooltip: {
        		        trigger: 'axis',
        		        formatter: formatterCall,//用formatter回调函数显示多项数据内容
        		        axisPointer: {            // 坐标轴指示器，坐标轴触发有效
        		            type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
        		        },
        		        
        		    },
        		    legend: {
        		        data: ['未开始','进行中','已延期','按时完成','线上bug','开发过程bug','reopenBug','A类质量问题','B类质量问题']
        		    },
        		    grid: {
        		        left: '5%',
        		        right: '5%',
        		        top: '15%',
        		        containLabel: true
        		    },
        		    xAxis: [
        		        {
        		            type: 'category',
        		            data: ['任务数', 'bug数', '低级质量问题数']
        		        }
        		    ],
        		    yAxis: [
        		        {
        		            type: 'value'
        		        }
        		    ],
        		    series: [
        		        {
             		   		name: '未开始',
             		    	type: 'bar',
             		     	stack: '个人质量信息',
             		    	barWidth : 40,//柱图宽度
             		    	data: [2,null,null]
             		    },
        		        {
        		            name: '进行中',
        		            type: 'bar',
        		            stack: '个人质量信息',
        		            data: [5,null,null]
        		        },
        		        {
        		            name: '已延期',
        		            type: 'bar',
        		            stack: '个人质量信息',
        		            data: [2,null,null]
        		        },
        		        {
        		            name: '按时完成',
        		            type: 'bar',
        		            stack: '个人质量信息',
        		            data: [12,null,null]
        		        },
        		        {
        		            name: '线上bug',
        		            type: 'bar',
        		            stack: '个人质量信息',
        		            data: [null,1, null]
        		        },
        		        {
        		            name: '开发过程bug',
        		            type: 'bar',
        		            stack: '个人质量信息',
        		            data: [null,10, null]
        		        },
        		        {
        		            name: 'reopenBug',
        		            type: 'bar',
        		            stack: '个人质量信息',
        		            data: [null,2, null]
        		        },
        		        {
        		            name: 'A类质量问题',
        		            type: 'bar',
        		            stack: '个人质量信息',
        		            data: [null,null, 2]
        		        },
        		        {
        		            name: 'B类质量问题',
        		            type: 'bar',
        		            stack: '个人质量信息',
        		            data: [null,null, 3]
        		        }
        		    ]
           };
           $scope.myChartsBarType.setOption($scope.barOption, true);
       }
       /**
        * 当窗体大小变化时，修改图例大小
        */
       window.addEventListener("resize", function () {
           if ($scope.myChartsBarType) { $scope.myChartsBarType.resize(); }
       });
       /**
		 * *************************************************************
		 *              方法声明部分                                 结束
		 * *************************************************************
		 */	
        }
    ]);
})();
(function () {
    app.controller("personalDepartmentContributeController", ['$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'personalDataBoardService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function ($rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, personalDataBoardService, inform, Trans, AgreeConstant, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //页面数据
			$scope.formRefer = {};
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.getData = getData;
            $scope.reset = reset;
            //初始化信息
            initData();
            $scope.stateMap={'2':'待审核', '1':'开放'}
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 初始化
             */
            function initData() {
                //获取当前登录者的empId
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ? $scope.sessionEmpId : $stateParams.empId;
                //获取当前登录者
                $scope.formRefer.loginName = LocalCache.getSession('loginName');
                var person = LocalCache.getObject('personDataBoardEmployee');
                if(person.loginName){
                    $scope.formRefer.loginName = person.loginName;
                }
                $scope.formRefer.employeeName = LocalCache.getSession('employeeName');
                if(person.name){
                    $scope.formRefer.employeeName = person.name;
                }
                reset();
                getData();
            }

            /**
             * 获取数据
             */
            function getData() {
                //获取培训输出
                getTrainPlan();
                //获取一点改善
                getCorrect();
                //获取知识库数据
                getKnowledgeData();
                //获取合理化建议
                getAdvice();
            }

            function getKnowledgeData() {
                var urlData = {
                    'loginName': $scope.formRefer.loginName,
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),
                    'page': '1',
                    'size': '500'
                };

                $scope.knowledgeData = [];

                personalDataBoardService.getKnowledgeData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.knowledgeData = data.data.list;
                            } else {
                                inform.common(Trans("tip.noData"));
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            function getTrainPlan() {
            	var urlData = {
                    'trainer': $scope.formRefer.employeeName,
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),
                    'page': '1',
                    'size': '500'
                };
                $scope.trainPlanData = [];
                personalDataBoardService.getTrainPlanData(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        if (null !== data.data) {
                            $scope.trainPlanData = data.data.list;
                        } else {
							inform.common(Trans("tip.noData"));
                        }
                    } else {
                        inform.common(data.message);
                    }
                },
                function () {
                    inform.common(Trans("tip.requestError"));
                });
            }

            function getCorrect() {
                $scope.dataTableShow = 0;
            	var urlData = {
                    'empName': $scope.formRefer.employeeName,
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),
                    'notAccept': '不采纳',
                    'currentPage': '1',
                    'pageSize': '500'
                };
                $scope.correctData = [];
                personalDataBoardService.getCorrectData(urlData).then(function (data) {
                    $scope.dataTableShow = 1;
                    if (data.code === AgreeConstant.code) {
                        if (null !== data.data) {
                            $scope.correctData = data.data.list;
                        } else {
							inform.common(Trans("tip.noData"));
                        }
                    } else {
                        inform.common(data.message);
                    }
                },
                function () {
                    inform.common(Trans("tip.requestError"));
                });
            }

            function getAdvice(){
                var urlData = {
                    'initiator': $scope.formRefer.employeeName,
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),
                    'revProStatus': '已完成',
                    'currentPage': '1', 								// 分页页数
                    'pageSize': '100'    						// 分页每页大小
                };
                $scope.adviceData = [];
                personalDataBoardService.getAdviceData(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        if (null !== data.data) {
                            $scope.adviceData = data.data.list;
                        } else {
							inform.common(Trans("tip.noData"));
                        }
                    } else {
                        inform.common(data.message);
                    }
                },
                function () {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 210);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 40);
                $("#divTBDis1").height(divHeight);
                $("#subDivTBDis1").height(divHeight - 40);
            }

			function reset() {
                $scope.formRefer.startTime = inform.format(new Date(),"yyyy")+'-01-01';
                $scope.formRefer.endTime = '';
            }

            $scope.goback = function () {
                $state.go('app.office.personKpi');
            };

            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.formRefer.openedStart = true;
                $scope.formRefer.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = false;
                $scope.formRefer.openedEnd = true;
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

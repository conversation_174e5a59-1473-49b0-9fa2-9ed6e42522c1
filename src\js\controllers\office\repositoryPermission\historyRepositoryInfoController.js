(function () {
    app.controller("historyRepositoryInfoController", ['comService','$rootScope', '$scope','$state','$stateParams','$modal','historyRepositoryInfoService','inform','$window','Trans','AgreeConstant',
        function (comService,$rootScope, $scope,$state,$stateParams, $modal,historyRepositoryInfoService,inform,$window,Trans,AgreeConstant) {
            //查询输入框
            $scope.formRefer={};
            $scope.formRefer.repositoryid = '';
            $scope.formRefer.repositoryType = '';
            // 初始化分页数据
            $scope.pages = inform.initPages();

            function getData() {
                var urlData = {
                    repositoryName: $scope.formRefer.repositoryid,
                    repositoryType: $scope.formRefer.repositoryType
                }
                historyRepositoryInfoService.getDocAuthorizationHis(urlData).then(function (data) {
                    $scope.showTable = 1;
                    if (data.code === AgreeConstant.code) {
                        $scope.tableDataList1 = data.data.nowPeopleStatus.nowPeopleList;
                        $scope.tableDataList2 = data.data.historyPeopleStatus.nowPeopleList;
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
                historyRepositoryInfoService.getCodeAuthorizationHis(urlData).then(function (data) {
                    $scope.showTable = 1;
                    if (data.code === AgreeConstant.code) {
                        $scope.tableDataList3 = data.data;
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
                historyRepositoryInfoService.getNowProjectChangeInfo(urlData).then(function (data) {
                    $scope.showTable = 1;
                    if (data.code === AgreeConstant.code) {
                        $scope.tableDataList4 = data.data;
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
                historyRepositoryInfoService.getHisProjectChangeInfo(urlData).then(function (data) {
                    $scope.showTable = 1;
                    if (data.code === AgreeConstant.code) {
                        $scope.tableDataList5 = data.data;
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            $scope.backDetailInfo = function () {
                $state.go("app.office.repositoryPermissionDetail", {repositoryid: $scope.formRefer.repositoryid,repositoryType: $scope.formRefer.repositoryType});
            }

            $scope.$watch('$viewContentLoaded', function () {
                if ($stateParams.repositoryid) {
                    $scope.formRefer.repositoryid = $stateParams.repositoryid;
                    $scope.formRefer.repositoryType = $stateParams.repositoryType;
                }
                getData();
            });
        }]);
})();

(function() {
    'use strict';
  app.factory('hardwareModeModule', hardwareModeModule);
  hardwareModeModule.$inject=["comService","projectManagementService","hardwareModeManagementService","AgreeConstant","inform","Trans"];

  function hardwareModeModule(comService,projectManagementService,hardwareModeManagementService,AgreeConstant,inform,Trans){
    var service={
		 initModule:initModule
    };
    return service;

    //回调方法
    var callBackFun = null;
    //scope
    var moduleScope = null;
    //初始化传入的参数
    var initParams = [];
    /**
     * 初始化硬件型号弹框
     *  参数结构
     * dataInfo= {
                    productLine:'产品线id',
                    productType:'产品类别',
                    productName:'产品名称',
                    hardwardNO:'型号'
                    }
     */
    function initModule(dataInfo,scope,callback) {
        if(dataInfo.hardwareModeNo){
            document.getElementById("hardwareModeNo").value=dataInfo.hardwareModeNo;
        }

        scope.selectProductTypeList=selectProductTypeList;
        scope.selectProductName = selectProductName;
        //选择型号
        scope.selectHardwareMode = selectHardwareMode;
        //获取型号
        scope.getHardwareMode = getHardwareMode;
        initParams = dataInfo;
    	//查产品线
		scope.productLines = [];
        //回调方法
        callBackFun = callback;
        moduleScope = scope;
        moduleScope.productLine = dataInfo.productType;
        moduleScope.productType = dataInfo.productType;
        moduleScope.productName = dataInfo.productName;
        //初始化产品线
        comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function (data) {
			if (data.data) {

			    var productLineParam = dataInfo.productLine;
			    if(!productLineParam) {
			        productLineParam = data.data[0].param_code;
			    }
			    scope.productLines=data.data;
			    //初始化产品类型
			    selectProductTypeList(productLineParam);
			    //显示弹框
			    $("#hardwardMode").click();
			}
		});
    }

    /**
     * 获取产品线下所有的产品类别
     */
     function selectProductTypeList(productLine){
        moduleScope.productLine = productLine;
        //修改被选中的字体颜色
        moduleScope.productLines.forEach(function (ele) {ele.active=ele.param_code===productLine;})
		//产品类型
		moduleScope.productTypes = [];
        //获取产品类型及产品名称
        projectManagementService.getProductTypeNameList(productLine).then(function (data) {
			if (data.code===AgreeConstant.code) {
                moduleScope.productTypes = data.data;
            } else {
                inform.common(data.message);
            }
            selectProductName(moduleScope.productType,moduleScope.productName);
		});
     }

    /**
     * 选中产品名称触发的事件
     */
     function selectProductName(productType,productName){
         moduleScope.productType = productType;
         moduleScope.productName = productName;
        //修改被选中的字体颜色
            moduleScope.productTypes.forEach(function (ele) {
                ele.active=ele.code===productType;
                ele.productNameList.forEach(function (item) {
                    item.active=item.code===productName;
                });
            })
        //获取型号
        getHardwareMode();
     }
    /**
     * 根据根据产品线-产品类型-产品名称查询客户
     */
     function getHardwareMode(){
        var paramInfo={
            'productLine':moduleScope.productLine,
            'productType':moduleScope.productType,
            'productSubType':moduleScope.productName,
            'hardwareModeNo':document.getElementById("hardwareModeNo").value,
            'hardwareModeStatus':"0"
        }
        moduleScope.hardwareModeModuleData = [];
		//获取型号列表
        hardwareModeManagementService.getHardwareModeInfoList(paramInfo).then(function (data) {
            if (data.code===AgreeConstant.code) {
                angular.forEach(data.data.list, function(res, index) {
                    var json = {
                        name: res.hardwareModeNo,//型号名称
                        productLine: res.productLine,//产品线
                        productType: res.productType,//产品类别
                        productSubType: res.productSubType//产品名称
                    };
                    moduleScope.hardwareModeModuleData.push(json);
                    //设置焦点
                    setTimeout(setFocus,500);
                });
            } else {
                inform.common(data.message);
            }
        }, function() {
            inform.common(Trans("tip.requestError"));
		});
     }
     function setFocus(){
        document.getElementById("hardwareModeNo").focus()
     }
     /**
      * 选择型号
      */
      function selectHardwareMode(m){
        $("#hardwardModeModule").modal("hide");
        callBackFun(m);
      }
  }
})();
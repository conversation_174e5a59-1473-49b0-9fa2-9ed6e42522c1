(function() {
    app.controller("staffDailyActionManagementController", ['staffDailyActionService', '$state', 'comService', '$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$stateParams', '$modal', '$http', '$state',
        function(staffDailyActionService, state, comService, $rootScope, $scope, inform, Trans, AgreeConstant, LocalCache, $stateParams, $modal, $http, $state) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //获取缓存
            $scope.formRefer = LocalCache.getObject('staffDailyActionController_formRefer');
            $scope.formRefer.startTime = inform.format(new Date(),'yyyy-MM-dd').split("-")[0]+"-01"+"-01";
            //清除缓存
            LocalCache.setObject('staffDailyActionController_formRefer', {});
            $scope.createUser = LocalCache.getSession('currentUserName');
            //修改一级部门，二级部门进行联动
            $scope.changeDept = function(){
                //获取二级部门
                $scope.deptList = [];
                comService.getOrgChildren($scope.formRefer.primaryDept).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.deptList = data.data;
                    }
                });
            };
            //一级部门存在时 重新加载二级部门
            if ($scope.formRefer.primaryDept !== undefined) {
                $scope.changeDept();
            }
            $scope.oprateFlag = false;
            //初始化
            initPages();
            //初始化当前时间
            $scope.datepicker = {
             currentDate : new Date()
            };
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //页面分页信息
            $scope.pages = {
                pageNum: '', //分页页数
                size: '100', //分页每页大小
                total: '' //数据总数
            };
            $scope.add=false;
            $scope.up=false;
            //判断按钮是否具有权限
            getButtonPermission();
            //获取数据
            $scope.getData = getData;
            //初始化页面信息
            getData(1);
            $scope.staffDailyActionTypeList = ['技术贡献','价值引领','团队建设','流程建设','不符设计规范','不符模板要求','不符管理规定','测试、验证逃逸','源代码','笔误','沟通问题',
                            '实施问题','流程处理','共性问题重复','出差日报缺失','客户问题响应不及时','日常规范','日志规范','执行力','其他']
            //行为类型展示
            $scope.classificationMap = {
                "0":'正向行为',
            	"1":'负面行为'
            }
            //行为类型下拉框
            $scope.classificationSelect = [{
            	value: '0',
            	label: '正向行为'
            },{
            	value: '1',
            	label: '负面行为'
            }];
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 65);
            }
            //重置按钮
            $scope.reset = function(){
                $scope.formRefer={};
                $scope.formRefer.startTime = inform.format(new Date(),'yyyy-MM-dd').split("-")[0]+"-01"+"-01";
                $scope.deptList = [];
            }

             /**
             * 获取按钮权限
             */
            function getButtonPermission(){
                var buttons = {
                    'Button-StaffDailyActionManagement-add':'add',
                    'Button-StaffDailyActionManagement-up':'up'
                };
                var urlData = {
                    'userId':LocalCache.getSession("userId"),
                    'parentPermission':'StaffDailyActionManagement',
                    'buttons':buttons
                };
               comService.getButtonPermission(urlData,$scope);
            }


             /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function(data) {
                     if (data.data) {
                         $scope.primaryDeptList = data.data;
                     }
                });

            }

            /**
             * 页面初始化
             */
            function initPages() {
                initPrimaryDeptList();
                //获取山东新北洋集团的下级部门信息
                $scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentList = comService.getDepartment(data.data);
                });
                //问题等级
                $scope.staffDailyActionGradeList = [];
                comService.getParamList('LOW_QUALITY_GRADE', 'LOW_QUALITY_GRADE').then(function(data) {
                    $scope.staffDailyActionGradeList = data.data;
                });
                //获取员工信息
                $scope.employeesList = [];
                $scope.employeesMap = [];
                comService.getEmployeesByOrgId('').then(function(data) {
                    $scope.employeeList = data.data;
                    for (var i=0;i<$scope.employeeList.length;i++){
                        $scope.employeesMap[$scope.employeeList[i].loginName] = $scope.employeeList[i].realName;
                    }
                });
            }
            /**
             * 根据创建人权限修改修改操作颜色
             */
            $scope.judeUp = function(item) {
                if (typeof($scope.createUser)==='undefined' || $scope.createUser===item) {
                    return "green";
                }
                return "gray";
            };
            /**
             * 根据创建人权限修改删除操作颜色
             */
            $scope.judeDel = function(item) {
                if (typeof($scope.createUser)==='undefined' || $scope.createUser===item) {
                    return "red";
                }
                return "gray";
            };

            /**
             * 获取红黑事件
             *
             */
            function getData(pages) {
                var urlData = {
                    'primaryDept':$scope.formRefer.primaryDept,
                    'department':$scope.formRefer.department,
                    'personLiableName': $scope.formRefer.personLiable, //责任人员名称
                    'classification':$scope.formRefer.classification,//行为类型
                    'type': $scope.formRefer.type, //问题类别
                    'gradeCode': $scope.formRefer.grade, //问题等级
                    'createUser': $scope.formRefer.createUser, //创建人
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),
                    'page': pages,
                    'size': $scope.pages.size
                };
                staffDailyActionService.getStaffDailyActionInfo(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        //红黑事件问题
                        $scope.staffDailyActionData = data.data.list;
                        if ($scope.staffDailyActionData.length===0) {
                        	$scope.pages = inform.initPages(); 			//初始化分页数据
                        	inform.common(Trans("tip.noData"));
                        } else {
                        // 分页信息设置
                        	$scope.pages.total = data.data.total;           // 页面数据总数
                        	$scope.pages.star = data.data.startRow;         // 页面起始数
                        	$scope.pages.end = data.data.endRow;            // 页面结束数
                        	$scope.pages.pageNum = data.data.pageNum;       //页号
                        }
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**新增数据
            **/
            $scope.addItem = function(){
                 LocalCache.setObject('staffDailyActionController_formRefer', $scope.formRefer);
                 $state.go("app.office.staffDailyActionAdd", null);
            }
            /**
             * 跳转修改
             */
            $scope.go = function(m) {
                if (typeof($scope.createUser)!=='undefined'&& $scope.createUser!==m.createUser) {
                     inform.common("只能修改本人创建的数据");
                     return;
                }
                LocalCache.setObject('staffDailyActionController_formRefer', $scope.formRefer);
                $state.go("app.office.staffDailyActionUpdate", {
                    item: m.id
                });

            };
            /**
             * 删除弹框
             *
             */
            $scope.deleteItem = function(m) {
                if (typeof($scope.createUser)!=='undefined'&& $scope.createUser!==m.createUser) {
                     inform.common("只能删除本人创建的数据");
                     return;
                }
                inform.modalInstance("确定要删除吗?").result.then(function() {
                    $scope.deleteStaffDailyActionInfo(m);
                });
            };
            /**
             * 删除数据
             *
             */
            $scope.deleteStaffDailyActionInfo = function(item) {
                staffDailyActionService.deleteStaffDailyActionInfo(item).then(function(data) {
                    if (data.code==="0000") {
                        inform.common(Trans("tip.delSuccess"));
                        $scope.getData(AgreeConstant.pageNum);
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            };
            /**
             * excel下载
             */
            $scope.toExcel = function() {
                inform.modalInstance("确定要下载吗?").result.then(function() {
                    //拼装下载内容
                    var params = $scope.formRefer;
                    inform.downLoadFile('StaffDailyAction/toExcel', params, '员工日常行为记录.xlsx');
                });
            };

            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        }
    ]);
})();

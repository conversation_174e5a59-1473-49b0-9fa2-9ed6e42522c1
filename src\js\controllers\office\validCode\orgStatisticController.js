(function () {
    app.controller("orgStatisticController", ['$ocLazyLoad', '$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http', 'orgStatisticService','codeDataReportFactory',
        function ($ocLazyLoad, $rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http, orgStatisticService,codeDataReportFactory) {
               // 初始化
               codeDataReportFactory.init($scope, '2');

               // 重置部分
               $scope.resetParam = resetParam;
               function resetParam(){
                codeDataReportFactory.initTime($scope,'2');
               }
     

            $scope.userLessCommitInfoData = [];
            $scope.userLessAddInfoData = [];
            $scope.leaderCommitInfoData = [];
            $scope.commitOnHolidayInfoData = [];
            $scope.commitBeforeDawnInfoData = [];

            // 部门提交人数统计
            $scope.currentOgCommitUserCntInputChart = null;
            // 部门人均提交次数统计
            $scope.currentOrgAvgCommitCntInputChart = null;
            // 少于10次提交工程师人数统计
            $scope.currentOrgUserNumCommitLessInputChart = null;
            // 低于1000行月均有效代码工程师人数
            $scope.currentOrgUserNumAddLessInputChart = null;
            // leader提交人数统计
            $scope.currentOrgLeaderNumInputChart = null;
            // leader代码数据
            $scope.currentOrgLeaderCommitDataInputChart = null;
            // 非工作日提交人数统计
            $scope.currentUserNumCommitOnHolidayInputChart = null;
            // 凌晨提交人数统计
            $scope.currentUserNumCommitBeforeDawnInputChart = null;
            // 部门平均merge次数统计（总merge次数/提交代码人数）
            $scope.currentOrgAvgMergeInputChart = null;
            // 部门git提交数占比（git提交次数/总提交次数）
            $scope.currentOrgGitCommitPercentageInputChart = null;
            



            window.addEventListener("resize", chartResize);
            $scope.$on("$destroy", function() {
                window.removeEventListener('resize', chartResize);
            })
            function chartResize() {
                if ($scope.currentOgCommitUserCntInputChart) { $scope.currentOgCommitUserCntInputChart.resize(); }
                if ($scope.currentOrgAvgCommitCntInputChart) { $scope.currentOrgAvgCommitCntInputChart.resize(); }
                if ($scope.currentOrgUserNumCommitLessInputChart) { $scope.currentOrgUserNumCommitLessInputChart.resize(); }
                if ($scope.currentOrgUserNumAddLessInputChart) { $scope.currentOrgUserNumAddLessInputChart.resize(); }
                if ($scope.currentOrgLeaderNumInputChart) { $scope.currentOrgLeaderNumInputChart.resize(); }
                if ($scope.currentOrgLeaderCommitDataInputChart) { $scope.currentOrgLeaderCommitDataInputChart.resize(); }
                if ($scope.currentUserNumCommitOnHolidayInputChart) { $scope.currentUserNumCommitOnHolidayInputChart.resize(); }
                if ($scope.currentUserNumCommitBeforeDawnInputChart) { $scope.currentUserNumCommitBeforeDawnInputChart.resize(); }
                if ($scope.currentOrgAvgMergeInputChart ) { $scope.currentOrgAvgMergeInputChart .resize(); }
                if ($scope.currentOrgGitCommitPercentageInputChart) { $scope.currentOrgGitCommitPercentageInputChart.resize(); }
            }


            // 部门提交人数统计
            function getOrgCommitUserCntChartData () {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                codeDataReportFactory.chartHideClear($scope.currentOgCommitUserCntInputChart);
                codeDataReportFactory.chartShowLoading($scope.currentOgCommitUserCntInputChart);
                orgStatisticService.getOrgCommitUserNumDataList(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.orgCommitUserCntInputInfo = result.data;
                        codeDataReportFactory.chartHideLoading($scope.currentOgCommitUserCntInputChart);
                        codeDataReportFactory.showBarAndLine($scope.currentOgCommitUserCntInputChart,$scope.orgCommitUserCntInputInfo, {
                            title: '提交人数',
                            xType: 'orgName',
                            yType:'developerNum',
                            yTypeLine:'developerPercentage',
                            left:'left',
                            fontSize:'12',
                            israte: true
                        },[
                            '提交代码人数',
                            '提交代码人数占比'
                        ]);
                    } else {
                        inform.common(result.message);
                        codeDataReportFactory.chartHideLoading($scope.currentOgCommitUserCntInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    codeDataReportFactory.chartHideLoading($scope.currentOgCommitUserCntInputChart);
                });
            }
            
            //部门人均提交次数统计
            function getOrgAvgCommitCntChartData () {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                codeDataReportFactory.chartHideClear($scope.currentOrgAvgCommitCntInputChart);
                codeDataReportFactory.chartShowLoading($scope.currentOrgAvgCommitCntInputChart);
                orgStatisticService.getOrgAvgCommitCntDataList(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.orgAvgCommitCntInputChart = result.data;
                        codeDataReportFactory.chartHideLoading($scope.currentOrgAvgCommitCntInputChart);
                        codeDataReportFactory.showBar($scope.currentOrgAvgCommitCntInputChart,$scope.orgAvgCommitCntInputChart, {
                            title: '人均提交次数',
                            xType: 'orgName',
                            yType:'avgCommitTimes',
                            left:'left',
                            fontSize:'12'
                        });
                    } else {
                        inform.common(result.message);
                        codeDataReportFactory.chartHideLoading($scope.currentOrgAvgCommitCntInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    codeDataReportFactory.chartHideLoading($scope.currentOrgAvgCommitCntInputChart);
                });
            }

            //少于10次提交的工程师人数
            function getOrgUserNumCommitLessInputChartData () {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                codeDataReportFactory.chartHideClear($scope.currentOrgUserNumCommitLessInputChart);
                codeDataReportFactory.chartShowLoading($scope.currentOrgUserNumCommitLessInputChart);
                orgStatisticService.getOrgUserNumCommitLessData(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.orgUserNumCommitLessInputChart = result.data;
                        codeDataReportFactory.chartHideLoading($scope.currentOrgUserNumCommitLessInputChart);
                        codeDataReportFactory.showPie($scope.currentOrgUserNumCommitLessInputChart,$scope.orgUserNumCommitLessInputChart, {
                            title: '少于10次提交的工程师人数',
                            type: 'orgName',
                            value: 'employeeNumLessCommitTimes'
                        })
                    } else {
                        inform.common(result.message);
                        codeDataReportFactory.chartHideLoading($scope.currentOrgUserNumCommitLessInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    codeDataReportFactory.chartHideLoading($scope.currentOrgUserNumCommitLessInputChart);
                });
            }

            //少于1000行月均有效代码工程师人数
            function getOrgUserNumAddLessInputChartData () {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                codeDataReportFactory.chartHideClear($scope.currentOrgUserNumAddLessInputChart);
                codeDataReportFactory.chartShowLoading($scope.currentOrgUserNumAddLessInputChart);
                orgStatisticService.getOrgUserNumAddLessData(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.orgUserNumAddLessInputChart = result.data;
                        codeDataReportFactory.chartHideLoading($scope.currentOrgUserNumAddLessInputChart);
                        codeDataReportFactory.showPie($scope.currentOrgUserNumAddLessInputChart,$scope.orgUserNumAddLessInputChart, {
                            title: '低于1000行月均有效代码工程师人数',
                            type: 'orgName',
                            value: 'employeeNumLessAddLimit'
                        })
                    } else {
                        inform.common(result.message);
                        codeDataReportFactory.chartHideLoading($scope.currentOrgUserNumAddLessInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    codeDataReportFactory.chartHideLoading($scope.currentOrgUserNumAddLessInputChart);
                });
            }

            // leader提交人数统计
            function getOrgCommitLeaderCntChartData () {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime,
                    "leaderCommitTimeThreshold":5
                }
                codeDataReportFactory.chartHideClear($scope.currentOrgLeaderNumInputChart);
                codeDataReportFactory.chartShowLoading($scope.currentOrgLeaderNumInputChart);
                orgStatisticService.getOrgLeaderCommitInfoDataList(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.orgLeaderNumInputChart = result.data;
                        codeDataReportFactory.chartHideLoading($scope.currentOrgLeaderNumInputChart);
                        codeDataReportFactory.showLeaderCommitBarAndLine($scope.currentOrgLeaderNumInputChart,$scope.orgLeaderNumInputChart, {
                            title: 'leader提交人数',
                            xType: 'orgName',
                            yType: 'leaderNum',
                            yType1:'leaderDeveloperNum',
                            yTypeLine:'leaderMoreCommitPercentage',
                            left:'left',
                            fontSize:'12',
                            israte: true
                        },[
                            'leader人数',
                            '提交人数',
                            '超5次提交人数占比'
                        ]);
                    } else {
                        inform.common(result.message);
                        codeDataReportFactory.chartHideLoading($scope.currentOrgLeaderNumInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    codeDataReportFactory.chartHideLoading($scope.currentOrgLeaderNumInputChart);
                });
            }

            // leader代码数据
            function getOrgCodeDataChartData () {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                codeDataReportFactory.chartHideClear($scope.currentOrgLeaderCommitDataInputChart);
                codeDataReportFactory.chartShowLoading($scope.currentOrgLeaderCommitDataInputChart);
                orgStatisticService.getOrgLeaderCodeInfoDataList(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.orgLeaderCommitDataInputChart = result.data;
                        codeDataReportFactory.chartHideLoading($scope.currentOrgLeaderCommitDataInputChart);
                        codeDataReportFactory.showBarAndLine($scope.currentOrgLeaderCommitDataInputChart,$scope.orgLeaderCommitDataInputChart, {
                            title: 'leader代码数据',
                            xType: 'orgName',
                            yType: 'avgCommitTimes',
                            yTypeLine:'avgAddLimit',
                            left:'left',
                            fontSize:'12',
                            israte: true
                        },[
                            'leader人均提交次数',
                            'leader人均有效代码'
                        ]);
                    } else {
                        inform.common(result.message);
                        codeDataReportFactory.chartHideLoading($scope.currentOrgLeaderCommitDataInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    codeDataReportFactory.chartHideLoading($scope.currentOrgLeaderCommitDataInputChart);
                });
            }


            // 非工作日提交人数统计
            function getUserNumCommitOnHolidayInputChartData () {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                codeDataReportFactory.chartHideClear($scope.currentUserNumCommitOnHolidayInputChart);
                codeDataReportFactory.chartShowLoading($scope.currentUserNumCommitOnHolidayInputChart);
                orgStatisticService.getUserNumCommitOnHolidayData(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.userNumCommitOnHolidayInputChart = result.data;
                        codeDataReportFactory.chartHideLoading($scope.currentUserNumCommitOnHolidayInputChart);
                        codeDataReportFactory.showPie($scope.currentUserNumCommitOnHolidayInputChart,$scope.userNumCommitOnHolidayInputChart, {
                            title: '非工作日提交人数统计',
                            type: 'orgName',
                            value: 'devNumCommitOnHoliday'
                        })
                    } else {
                        inform.common(result.message);
                        codeDataReportFactory.chartHideLoading($scope.currentUserNumCommitOnHolidayInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    codeDataReportFactory.chartHideLoading($scope.currentUserNumCommitOnHolidayInputChart);
                });
            }

            //凌晨提交人数统计
            function getUserNumCommitBeforeDawnInputChartData () {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                codeDataReportFactory.chartHideClear($scope.currentUserNumCommitBeforeDawnInputChart);
                codeDataReportFactory.chartShowLoading($scope.currentUserNumCommitBeforeDawnInputChart);
                orgStatisticService.getUserNumCommitBeforeDawnData(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.userNumCommitBeforeDawnInputChart = result.data;
                        codeDataReportFactory.chartHideLoading($scope.currentUserNumCommitBeforeDawnInputChart);
                        codeDataReportFactory.showPie($scope.currentUserNumCommitBeforeDawnInputChart,$scope.userNumCommitBeforeDawnInputChart, {
                            title: '凌晨提交人数统计',
                            type: 'orgName',
                            value: 'devNumCommitBeforeDawn'
                        })
                    } else {
                        inform.common(result.message);
                        codeDataReportFactory.chartHideLoading($scope.currentUserNumCommitBeforeDawnInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    codeDataReportFactory.chartHideLoading($scope.currentUserNumCommitBeforeDawnInputChart);
                });
            }

            // 获取提交次数少于10的工程师明细
            $scope.getUserLessCommit = function () {
                $scope.modalTitle = "提交次数少于10次的工程师";
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime,
                    "commitTimeThreshold": 10
                }
                orgStatisticService.getUserLessCommit(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                
                        $scope.userLessCommitInfoData = result.data;

                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            // 获取有效代码小于1000的工程师明细
            $scope.getUserLessAdd = function () {
                $scope.modalTitle = "有效代码数低于1000行的工程师";
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime,
                    "addLimitThreshold": 1000
                }
                orgStatisticService.getUserLessAdd(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                
                        $scope.userLessAddInfoData = result.data;

                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

             // 获取leader提交明细
             $scope.getLeaderCommitList = function () {
                $scope.modalTitle = "leader提交明细";
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                orgStatisticService.getLeaderCommitList(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                
                        $scope.leaderCommitInfoData = result.data;

                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            // 获取非工作日提交明细
            $scope.getCommitListOnHoliday = function () {
                $scope.modalTitle = "非工作日提交明细";
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                orgStatisticService.getCommitListOnHoliday(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                
                        $scope.commitOnHolidayInfoData = result.data;

                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            // 获取凌晨提交明细
            $scope.getCommitListBeforeDawn = function () {
                $scope.modalTitle = "凌晨提交明细";
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                orgStatisticService.getCommitListBeforeDawn(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                
                        $scope.commitBeforeDawnInfoData = result.data;

                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            //人均有效代码按岗位统计
            function getMonthValidCodeChartData () {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                codeDataReportFactory.chartHideClear($scope.currentMonthValidCodeInputChart);
                codeDataReportFactory.chartShowLoading($scope.currentMonthValidCodeInputChart);
                aggregateStatisticService.getMonthCommitTimesList(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.monthValidCodeInputInfo = result.data;
                        codeDataReportFactory.chartHideLoading($scope.currentMonthValidCodeInputChart);
                        codeDataReportFactory.showValidCodeBar($scope.currentMonthValidCodeInputChart,$scope.monthValidCodeInputInfo, '人均有效代码按岗位统计',[
                            'java',
                            'android',
                            'c#',
                            '前端',
                            'c++'
                        ]);
                    } else {
                        inform.common(result.message);
                        codeDataReportFactory.chartHideLoading($scope.currentMonthValidCodeInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    codeDataReportFactory.chartHideLoading($scope.currentMonthValidCodeInputChart);
                });
            }
            // 部门平均merge次数统计
            function getOrgAvgMergeChartData () {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                codeDataReportFactory.chartHideClear($scope.currentOrgAvgMergeInputChart);
                codeDataReportFactory.chartShowLoading($scope.currentOrgAvgMergeInputChart);
                orgStatisticService.getOrgAvgMergeDataList(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.orgCommitUserCntInputInfo = result.data;
                        codeDataReportFactory.chartHideLoading($scope.currentOrgAvgMergeInputChart);
                        codeDataReportFactory.showBarAndLine($scope.currentOrgAvgMergeInputChart,$scope.orgCommitUserCntInputInfo, {
                            title: 'merge次数',
                            xType: 'orgName',
                            yType:'mergeTimes',
                            yTypeLine:'avgMergeTimes',
                            left:'left',
                            fontSize:'12',
                            israte: true
                        },[
                            'merge总次数',
                            '人均merge次数'
                        ]);
                    } else {
                        inform.common(result.message);
                        codeDataReportFactory.chartHideLoading($scope.currentOrgAvgMergeInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    codeDataReportFactory.chartHideLoading($scope.currentOrgAvgMergeInputChart);
                });
            }
            // 部门git提交占比统计
            function getOrgGitCommitPercentageChartData () {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                codeDataReportFactory.chartHideClear($scope.currentOrgGitCommitPercentageInputChart);
                codeDataReportFactory.chartShowLoading($scope.currentOrgGitCommitPercentageInputChart);
                orgStatisticService.getOrgGitCommitTimesDataList(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.orgCommitUserCntInputInfo = result.data;
                        codeDataReportFactory.chartHideLoading($scope.currentOrgGitCommitPercentageInputChart);
                        codeDataReportFactory.showBarAndLine($scope.currentOrgGitCommitPercentageInputChart,$scope.orgCommitUserCntInputInfo, {
                            title: 'git提交次数占比',
                            xType: 'orgName',
                            yType:'gitCommitTimes',
                            yTypeLine:'gitCommitPercentage',
                            left:'left',
                            fontSize:'12',
                            israte: true
                        },[
                            'GIT提交次数',
                            'GIT提交次数占比'
                        ]);
                    } else {
                        inform.common(result.message);
                        codeDataReportFactory.chartHideLoading($scope.currentOrgGitCommitPercentageInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    codeDataReportFactory.chartHideLoading($scope.currentOrgGitCommitPercentageInputChart);
                });
            }
           
            // 页面加载后触发
            $scope.getData = getData;
          

            function getData(){
                getOrgCommitUserCntChartData();
                getOrgAvgCommitCntChartData();
                getOrgUserNumCommitLessInputChartData();
                getOrgUserNumAddLessInputChartData();
                getOrgCommitLeaderCntChartData();
                getOrgCodeDataChartData();
                getUserNumCommitOnHolidayInputChartData();
                getUserNumCommitBeforeDawnInputChartData();
                getOrgAvgMergeChartData();
                getOrgGitCommitPercentageChartData();
            }
            $scope.loadSuccess = function (){
                $ocLazyLoad.load(
                    [
                        'library/component/echarts.min.js'
                    ]).then(function (){
                    $scope.currentOgCommitUserCntInputChart = echarts.init(document.getElementById('orgCommitUserCntDataInputChart'));
                    $scope.currentOrgAvgCommitCntInputChart = echarts.init(document.getElementById('orgAvgCommitCntInputChart'));
                    $scope.currentOrgUserNumCommitLessInputChart = echarts.init(document.getElementById('orgUserNumCommitLessInputChart'));
                    $scope.currentOrgUserNumAddLessInputChart = echarts.init(document.getElementById('orgUserNumAddLessInputChart'));
                    $scope.currentOrgLeaderNumInputChart = echarts.init(document.getElementById('orgLeaderNumInputChart'));
                    $scope.currentOrgLeaderCommitDataInputChart = echarts.init(document.getElementById('orgLeaderCommitDataInputChart'));
                    $scope.currentUserNumCommitOnHolidayInputChart = echarts.init(document.getElementById('userNumCommitOnHolidayInputChart'));
                    $scope.currentUserNumCommitBeforeDawnInputChart = echarts.init(document.getElementById('userNumCommitBeforeDawnInputChart'));
                    $scope.currentOrgAvgMergeInputChart = echarts.init(document.getElementById('orgAvgMergeInputChart'));
                    $scope.currentOrgGitCommitPercentageInputChart = echarts.init(document.getElementById('orgGitCommitPercentageInputChart'));
                    getData();
                    
                });
            }
        }]);
})();

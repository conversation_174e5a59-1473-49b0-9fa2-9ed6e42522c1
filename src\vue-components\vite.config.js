import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

export default defineConfig({
    plugins: [vue()],
    build: {
        lib: {
            entry: resolve(__dirname, 'src/main.js'),
            name: 'VueComponents',
            fileName: (format) => `vue-components.${format}.js`,
            formats: ['umd', 'es'],
        },
        rollupOptions: {
            external: [],
            output: {
                globals: {},
                // 确保 CSS 被内联到 JS 中
                inlineDynamicImports: false,
                assetFileNames: (assetInfo) => {
                    if (assetInfo.names && assetInfo.names[0] === 'style.css') {
                        return 'vue-components.css';
                    }
                    return assetInfo.names ? assetInfo.names[0] : 'asset.[ext]';
                },
            },
        },
        outDir: '../library/vue-components',
        // 确保 CSS 被提取
        cssCodeSplit: false,
    },
    define: {
        'process.env.NODE_ENV': JSON.stringify('production'),
    },
});

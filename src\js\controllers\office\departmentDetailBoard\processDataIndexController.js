(function () {
    app.controller("processDataIndexController", ['LocalCache', 'inform', 'Trans', '$ocLazyLoad', '$rootScope','comService', '$scope', '$stateParams', '$state', 'deptBoardFactory', 'AgreeConstant', 'processDataForBoardService',
        function (LocalCache, inform, Trans, $ocLazyLoad, $rootScope,comService,$scope, $stateParams, $state, deptBoardFactory, AgreeConstant, processDataForBoardService) {
            // 初始化
            deptBoardFactory.init($scope, '1');
            // 重置部分
            $scope.resetParam = resetParam;
            function resetParam(){
                deptBoardFactory.initTime($scope, '本年度');
            }
            $scope.getData = getData;
            function getData() {
                getStatisticsData();
                getReviewOutputData();
                getReviewProblemContributionData();
                getReviewOutputData2()
            }
            // 统计区域
            function getStatisticsData() {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                $scope.showStatisticsInfo = false;
                processDataForBoardService.getRateInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.statisticsList = result.data;
                        $scope.showStatisticsInfo = true;
                    } else {
                        inform.common(result.message);
                        $scope.showStatisticsInfo = true;
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    $scope.showStatisticsInfo = true;
                });
            }

            // 图表部分
            $scope.currentReviewOutputChart = null;
            $scope.currentReviewProblemContributionChart = null;
            $scope.currentReviewOutputChart2 = null;
            window.addEventListener("resize", chartResize);
            $scope.$on("$destroy", function() {
                window.removeEventListener('resize', chartResize);
            })
            function chartResize() {
                if ($scope.currentReviewOutputChart) { $scope.currentReviewOutputChart.resize(); }
                if ($scope.currentReviewProblemContributionChart) { $scope.currentReviewProblemContributionChart.resize(); }
                if ($scope.currentReviewOutputChart2) { $scope.currentReviewOutputChart2.resize(); }
            }
            function getReviewOutputData() {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                deptBoardFactory.chartHideClear($scope.currentReviewOutputChart);
                deptBoardFactory.chartShowLoading($scope.currentReviewOutputChart);
                processDataForBoardService.getReviewOutput(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.reviewOutputChartInfo = result.data;
                        deptBoardFactory.showPie($scope.currentReviewOutputChart,$scope.reviewOutputChartInfo, {
                            title: '评审物输出(按工程师级别统计)',
                            type: 'title',
                            value: 'total',
                            otherData: 'avg',
                            firstContent: '输出评审',
                            firstContentUnit: '次',
                            secondContent: '人均',
                            secondContentUnit: '个',
                        })
                        deptBoardFactory.chartHideLoading($scope.currentReviewOutputChart);
                    } else {
                        inform.common(result.message);
                        deptBoardFactory.chartHideLoading($scope.currentReviewOutputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    deptBoardFactory.chartHideLoading($scope.currentReviewOutputChart);
                });
            }

            function getReviewProblemContributionData() {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                deptBoardFactory.chartHideClear($scope.currentReviewProblemContributionChart);
                deptBoardFactory.chartShowLoading($scope.currentReviewProblemContributionChart);
                processDataForBoardService.getReviewQuestion(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.reviewProblemContributionChartInfo = result.data;
                        deptBoardFactory.showPie($scope.currentReviewProblemContributionChart,$scope.reviewProblemContributionChartInfo, {
                            title: '评审问题贡献(按工程师级别统计)',
                            type: 'title',
                            value: 'questionNUmber',
                            otherData: 'efficiency',
                            firstContent: '输出评审问题',
                            firstContentUnit: '个',
                            secondContent: '评审效率',
                            secondContentUnit: '个每次会议',
                        })
                        deptBoardFactory.chartHideLoading($scope.currentReviewProblemContributionChart);
                    } else {
                        inform.common(result.message);
                        deptBoardFactory.chartHideLoading($scope.currentReviewProblemContributionChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    deptBoardFactory.chartHideLoading($scope.currentReviewProblemContributionChart);
                });
            }

            function getReviewOutputData2() {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                deptBoardFactory.chartHideClear($scope.currentReviewOutputChart2);
                deptBoardFactory.chartShowLoading($scope.currentReviewOutputChart2);
                processDataForBoardService.getReviewChart(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.reviewOutput2ChartInfo = result.data;
                        deptBoardFactory.chartHideLoading($scope.currentReviewOutputChart2);
                        eChartShowForReviewOutputBarAndLine($scope.currentReviewOutputChart2,$scope.reviewOutput2ChartInfo, '评审物输出(按项目团队分布)', AgreeConstant.departmentBoard.reviewOutputLegendData);
                    } else {
                        inform.common(result.message);
                        deptBoardFactory.chartHideLoading($scope.currentReviewOutputChart2);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    deptBoardFactory.chartHideLoading($scope.currentReviewOutputChart2);
                });
            }
            function eChartShowForReviewOutputBarAndLine(currentChart,data,title, legendData){
                var xData = [];
                var developData = [];
                var testData = [];
                var otherData = [];
                var rateData = [];
                if (data.length) {
                    angular.forEach(data, function (eachData) {
                        xData.push(eachData.projectName);
                        developData.push({
                            value: eachData.developNumber
                        });
                        testData.push({
                            value: eachData.testNumber
                        });
                        otherData.push({
                            value: eachData.otherNumber
                        });
                        rateData.push({
                            value: parseFloat(eachData.rate) * 100
                        });
                    });
                    var option = {
                        title:{
                            text:title,
                            textStyle:{
                                fontSize: 18,
                                color: '#333'
                            }
                        },
                        grid:{
                            left:'2%',
                            right:'2%',
                            bottom:'0',
                            containLabel:true
                        },
                        legend: {
                            data: legendData
                        },
                        xAxis: [
                            {
                                type: 'category',
                                data: xData,
                                axisPointer: {
                                    type: 'shadow'
                                },
                                axisLabel:{
                                    rotate:20
                                }
                            }
                        ],
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'cross',
                                crossStyle: {
                                    color: '#999'
                                }
                            },
                            formatter: function (params, ticket, callback) {return deptBoardFactory.formatterCall(params, ticket, callback, '评审通过率', '次', true)}
                        },
                        yAxis: [
                            {
                                type: 'value',
                                axisLabel: {
                                    formatter: '{value}次'
                                }
                            },
                            {
                                type: 'value',
                                axisLabel: {
                                    formatter: '{value}%'
                                },
                                min: 60
                            }
                        ],
                        series: [
                            {
                                name: legendData[0],
                                type: 'bar',
                                barWidth: '20%',
                                yAxisIndex: 0,
                                data: developData,
                                show: false
                            },
                            {
                                name: legendData[1],
                                type: 'bar',
                                barWidth: '20%',
                                yAxisIndex: 0,
                                data: testData,
                                show: false
                            },
                            {
                                name: legendData[2],
                                type: 'bar',
                                barWidth: '20%',
                                yAxisIndex: 0,
                                data: otherData,
                                show: false
                            },
                            {
                                name: legendData[3],
                                type: 'line',
                                yAxisIndex: 1,
                                data: rateData
                            }
                        ]
                    };
                } else {
                    option = {
                        title: [{
                            text: title,
                            textStyle:{
                                fontSize: 12,
                                color: '#333'
                            },
                        },{
                            text: "暂无数据",
                            left: 'center',
                            top: 'center',
                            color: '#333',
                            textStyle: {
                                fontSize: 20
                            }
                        }]
                    }
                }

                currentChart.setOption(option, true);
            }

            $scope.toRecordData = function () {
                $state.go('app.office.report_0004');
            }
            $scope.reviewOutput = function (title) {
                $scope.modalTitle = title;
                $scope.type = 'reviewDate';
                $scope.sort = 'desc';
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                processDataForBoardService.getReviewOutDocInfo(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.reviewOutDocInfo = result.data;
                        $scope.sortData($scope.type, false, $scope.sort, $scope.reviewOutDocInfo);
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            $scope.employeeReviewContribution = function (title) {
                $scope.modalTitle = title;
                $scope.type = 'totalProblemNum';
                $scope.sort = 'desc';
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                processDataForBoardService.getReviewContributionInfo(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.reviewContributionInfo = result.data;
                        $scope.sortData($scope.type, true, $scope.sort, $scope.reviewContributionInfo);
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            // 排序
            function getDate(strDate){
                return Date.parse(strDate);
            }
            $scope.sortData = function (type, isNumber, sort,  dataList) {
                if (type === 'reviewDate') {
                    $scope.type = type;
                    $scope.sort = sort;
                    if (sort === 'asc'){
                        dataList.sort(function (a, b) {
                            return getDate(b[type]) - getDate(a[type]);
                        })
                    } else {
                        dataList.sort(function (a, b) {
                            return getDate(a[type]) - getDate(b[type]);
                        })
                    }
                }
                return deptBoardFactory.sortData($scope, type, isNumber, sort, dataList);
            }
            $scope.activeClass = function (type, sort){
                return deptBoardFactory.activeClass(type, sort, $scope.type, $scope.sort);
            }
            $scope.activeTitleClass = function (type) {
                return deptBoardFactory.activeTitleClass(type, $scope.type);
            }

            $scope.loadSuccess = function (){
                $ocLazyLoad.load(
                    [
                        'library/component/echarts.min.js'
                    ]).then(function (){
                    $scope.currentReviewOutputChart = echarts.init(document.getElementById('reviewOutputChart'));
                    $scope.currentReviewProblemContributionChart = echarts.init(document.getElementById('reviewProblemContributionChart'));
                    $scope.currentReviewOutputChart2 = echarts.init(document.getElementById('reviewOutputChart2'));
                    var localFormRefer = LocalCache.getObject('departmentList_formRefer');
                    if (Object.keys(localFormRefer).length > 0) {
                        $scope.formRefer = localFormRefer;
                        $scope.butFlag = localFormRefer.searchTimeString;
                    }
                    if ($stateParams.orgCode) {
                        $scope.formRefer.orgCode = $stateParams.orgCode;
                    }
                    getData();
                });
            }
        }]);
})();
(function () {
    app.controller("patentApplyController", ['intellectualService','comService','$rootScope', '$scope','$state','$stateParams','$modal','inform','Trans','AgreeConstant','LocalCache','$http',
        function (intellectualService,comService,$rootScope, $scope,$state,$stateParams, $modal,inform,Trans,AgreeConstant,LocalCache,$http) {
     	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */ 	
		//页面数据
		$scope.formRefer = {};
		$scope.limitList = AgreeConstant.limitList; // 正则校验配置
		$scope.formInput ={
				department:'',   //部门名称
				name:'',    	//专利中文名
				startDate:'',	//起始时间
				endDate:''		//结束时间
		};


		
    	$scope.formInsert = {
			 applyId:'',//发起人
			 name:'',//流程标题
			 model:'',//实例号
			 applyDate:'',//是否采纳
			 type:'',
			 applicants:'',
			 line:'',
			 department:'',
			 inventors:null,
			 legalStatus:''//法律状态
    	};
			$scope.changeParam = {
				applyId:'',//发起人
				name:'',//流程标题
				model:'',//实例号
				applyDate:'',//是否采纳
				type:'',
				applicants:'',
				line:'',
				department:'',
				inventors:null,
				legalStatus:''//法律状态
			};
    	
    	$scope.pages = {
				pageNum : '', 		// 分页页数
				size : '', 			// 分页每页大小
				total : '' 			// 数据总数
		};
    	$scope.pages = inform.initPages(); 	// 初始化分页数据
		$scope.getData = getData;
		//是否从我的星星跳转标志
		$scope.formRefer.flag = '0';
		//获取缓存
		$scope.formRefer = LocalCache.getObject('patentApplyController_formRefer');
		if($scope.formRefer.flag === '1'){
			$scope.formInput.startDate = $scope.formRefer.startDate;
			$scope.formInput.endDate = $scope.formRefer.endDate;
			$scope.formInput.employeeName = $scope.formRefer.employeeName;
			console.log($scope.formRefer);
		}
		//清除缓存
		LocalCache.setObject('correct_formRefer', {}); 	
		//初始化页面信息
    	initPages();
		initInsertPerson();
		getData($scope.pages.pageNum);		//在刷新页面时调用该方法
	
	  	$scope.addInfo = addInfo;           // 新增一条信息
	  	$scope.updateInfo = updateInfo;     // 修改一条信息

		$scope.setDraggableTime = setDraggableTime;
		//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
	  	
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */	
		
	    //设置列表的高度
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 180);
 			$("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight - 70);
 		}

		//重置
		$scope.reset = function() {
			$scope.formInput.department='';
			$scope.formInput.name='';
			$scope.formInput.startDate = '';
			$scope.formInput.endDate = ''
		}


			/**
			 * 初始化信息
			 */
			function initPerson() {
				// 发明人下拉列表
				$scope.employeeListUpCreation = [];
				$scope.employeeListChangeCreation = [];
				comService.getEmployeesByOrgId('','1').then(function (data) {
					if (data.data) {
						var _$scope$employeeListA;

						$scope.employeeListChangeCreation = data.data;
						// 数组拷贝
						_$scope$employeeListA = $scope.employeeListUpCreation;
						_$scope$employeeListA.push.apply(_$scope$employeeListA, _toConsumableArray($scope.employeeListChangeCreation));
						
						$scope.changeParam.inventors = $scope.changeParam.inventors == null ? [] : $scope.changeParam.inventors.split(',');
						$scope.employeeListChangeCreation = changeListOrder($scope.changeParam.inventors, $scope.employeeListChangeCreation);
						setDraggableTime();
					}
				});
			}

			function initInsertPerson() {
				// 发明人下拉列表
				$scope.employeeListUpCreation = [];
				$scope.employeeListChangeCreation = [];
				comService.getEmployeesByOrgId('','1').then(function (data) {
					if (data.data) {
						var _$scope$employeeListA;

						$scope.employeeListChangeCreation = data.data;
						// 数组拷贝
						_$scope$employeeListA = $scope.employeeListUpCreation;
						_$scope$employeeListA.push.apply(_$scope$employeeListA, _toConsumableArray($scope.employeeListChangeCreation));

						$scope.formInsert.inventors = $scope.formInsert.inventors == null ? [] : $scope.formInsert.inventors.split(',');
						$scope.employeeListUpCreation = changeListOrder($scope.formInsert.inventors, $scope.employeeListUpCreation);
						setDraggableTime();
					}
				});
			}
			/**
			 * 名单变化时延迟1秒修改li属性允许拖拽
			 */
			function setDraggableTime() {

				setTimeout(setDraggableOfli, 1000 * 1);
			}
			/**
			 * 修改li属性允许拖拽
			 */
			function setDraggableOfli() {

				var subNodes = document.querySelectorAll("ul.chosen-choices li.search-choice");

				for (var i = 0; i < subNodes.length; i++) {
					$(subNodes[i]).attr("draggable", true);
				}
			}
			/*
        * 让bigList按照 smallList 的顺序显示
        * */
			function changeListOrder(smallList, bigList) {
				/*
                * reverse() 数组倒叙
                * unshift() 插入元素至数组头部
                *
                * */
				smallList.reverse().forEach(function (copy) {
					// 在所有人员中取到抄送人
					var copyItem = bigList.find(function (item) {
						return item.loginName === copy;
					});
					// 在所有人员中删除抄送人
					bigList = bigList.filter(function (item) {
						return item.loginName !== copy;
					});
					// 把抄送人添加到数组头部
					bigList.unshift(copyItem);
				});
				return bigList;
			}
			// 数组复制
			function _toConsumableArray(arr) {
				if (Array.isArray(arr)) {
					for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) {
						arr2[i] = arr[i];
					}
					return arr2;
				} else {
					return Array.from(arr);
				}
			}
			
    	function initPages() {
    		//获取山东新北洋集团的下级部门信息
    		$scope.departmentList = [];
    		comService.getOrgChildren('D010053').then(function(data) {
    			$scope.departmentList = comService.getDepartment(data.data);
             });
    		
            $scope.lineList = [];
            comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
            	if(data.data) {
            		$scope.lineList = data.data;
            	}
            });

    	}

		
	     /**
         * 页面选中的修改信息复制
         */
     	$scope.update = function (m) {
     		$scope.changeParam = angular.copy(m);
			initPerson();
     		angular.forEach($scope.departmentList, function(der, i) {//遍历部门
            	if (der.orgName===$scope.changeParam.department){
            		$scope.changeParam.department = der.orgCode;
            	}
          	 });
     		angular.forEach($scope.lineList, function(line, i) {//遍历产品线
            	if (line.param_value===$scope.changeParam.line){
            		$scope.changeParam.line = line.param_code;
            	}
          	 });

     	}
     	//修改
     	function updateInfo(changeParam){
     		changeParam.applyDate = $("#applyDateUpdate").val();
		/*	if(!changeParam.applyId){
				inform.common(Trans("专利申请号不能为空！"));
				return false;
			} else*/
			if(!changeParam.name){
				inform.common(Trans("专利中文名称不能为空！"));
				return false;
			}
/*				else if(!changeParam.model){
				inform.common(Trans("产品型号不能为空！"));
				return false;
			} */
				else if(!changeParam.applyDate){
				inform.common(Trans("申请日期不能为空！"));
				return false;
			}
	/*			else if(!changeParam.type){
				inform.common(Trans("专利类型不能为空！"));
				return false;
			}*/
				else if(!changeParam.applicants){
				inform.common(Trans("全部申请人不能为空！"));
				return false;
			}else if(!changeParam.line){
				inform.common(Trans("产品线不能为空！"));
				return false;
			}else if(!changeParam.department){
				inform.common(Trans("部门不能为空！"));
				return false;
			}
	/*		else if(!changeParam.inventors){
				inform.common(Trans("全部发明人不能为空！"));
				return false;
			}else if(!changeParam.legalStatus){
				inform.common(Trans("当前法律状态不能为空！"));
				return false;
			}*/
			else {
				$scope.changeParam.inventors = $scope.changeParam.inventors.toString();
				var urlData = {
						 'id':changeParam.id,
						 'applyId':changeParam.applyId,
		    			 'name':changeParam.name,
		    			 'model':changeParam.model,
		    			 'applyDate':inform.format(changeParam.applyDate,'yyyy-MM-dd'),
		    			 'type':changeParam.type,
		    			 'applicants':changeParam.applicants,
		    			 'line':changeParam.line,
		    			 'department':changeParam.department,
		    			 'inventors':$scope.changeParam.inventors,
		    			 'legalStatus':changeParam.legalStatus
				};

				// var modalInstance = $modal.open({
				// 	  templateUrl: 'myModalContent.html',
	   //              controller: 'ModalInstanceCtrl',
	   //              size: "sm",
	   //              resolve: {
	   //                items: function() {
	   //                return "确定要修改吗！";
	   //                }
	   //             }
				// });
		        // modalInstance.result.then(function() {
		        	intellectualService.updatePatentApply(urlData).then(function(data){
		        		if(data.code===AgreeConstant.code){
			        		inform.common(Trans("专利申请信息修改成功！"));
		        		}else{
		        			inform.common(Trans(data.message));
		        		}
		        		$("#edit_modal").modal('hide');
		 				getData(1);
					},
					function(error) {
						inform.common(Trans("tip.requestError"));
					});
			    // });
			}
			
		
     	}
     	
		
		//新增
		function addInfo(){
			$scope.formInsert.applyDate = $("#applyDateInsert").val();
		/*	if(!$scope.formInsert.applyId){
				inform.common(Trans("专利申请号不能为空！"));
				return false;
			} else */
			if(!$scope.formInsert.name){
				inform.common(Trans("专利中文名称不能为空！"));
				return false;
			} 
	/*			else if(!$scope.formInsert.model){
				inform.common(Trans("产品型号不能为空！"));
				return false;
			} */
				else if(!$scope.formInsert.applyDate){
				inform.common(Trans("申请日期不能为空！"));
				return false;
			} 
		/*		else if(!$scope.formInsert.type){
				inform.common(Trans("专利类型不能为空！"));
				return false;
			}*/
				else if(!$scope.formInsert.applicants){
				inform.common(Trans("全部申请人不能为空！"));
				return false;
			}else if(!$scope.formInsert.line){
				inform.common(Trans("产品线不能为空！"));
				return false;
			}else if(!$scope.formInsert.department){
				inform.common(Trans("部门不能为空！"));
				return false;
			}
	/*		else if(!$scope.formInsert.inventors){
				inform.common(Trans("全部发明人不能为空！"));
				return false;
			}else if(!$scope.formInsert.legalStatus){
				inform.common(Trans("当前法律状态不能为空！"));
				return false;
			}*/
			else {
				$scope.formInsert.inventors = $scope.formInsert.inventors.toString();
				var urlData = {
		    			 'applyId':$scope.formInsert.applyId,
		    			 'name':$scope.formInsert.name,
		    			 'model':$scope.formInsert.model,
		    			 'applyDate':inform.format($scope.formInsert.applyDate,'yyyy-MM-dd'),
		    			 'type':$scope.formInsert.type,
		    			 'applicants':$scope.formInsert.applicants,
		    			 'line':$scope.formInsert.line,
		    			 'department':$scope.formInsert.department,
		    			 'inventors':$scope.formInsert.inventors,
		    			 'legalStatus':$scope.formInsert.legalStatus
		    			 
				};
				
				// var modalInstance = $modal.open({
				// 	templateUrl: 'myModalContent.html',
	   //              controller: 'ModalInstanceCtrl',
	   //              size: "sm",
	   //              resolve: {
	   //                items: function() {
	   //                return "确定要添加吗！";
	   //                }
	   //             }
				// });
		        // modalInstance.result.then(function() {
		        	intellectualService.insertPatentApply(urlData).then(function(data){
		        		if(data.code===AgreeConstant.code){
			        		inform.common(Trans("专利申请信息添加成功！"));
			        		$scope.formInsert={};
		        		}else{
		        			inform.common(Trans(data.message));
		        		}
		        		$("#add_modal").modal('hide');
		        		getData(1);
		        		
					},
				function(error) {
					inform.common(Trans("tip.requestError"));
				});	
	        // });

		}
		}
		
		//获取所有数据以分页的形式
		function getData(pageNum){
		   	$scope.itemList = [];
		   	var start = inform.format($scope.formInput.startDate,'yyyy-MM-dd');
			var end = inform.format($scope.formInput.endDate,'yyyy-MM-dd');
			if (start > end) {
				inform.common(Trans("申请的结束时间必须大于开始时间！"));
				return false;
			}	
        	var urlData ={
					'department':$scope.formInput.department,  			
					'name':$scope.formInput.name,
					'startDate':inform.format($scope.formInput.startDate,'yyyy-MM-dd'),
					'endDate':inform.format($scope.formInput.endDate,'yyyy-MM-dd'),
					'currentPage' : pageNum, 								// 分页页数
					'pageSize' : $scope.pages.size    						// 分页每页大小
        	};
        	intellectualService.getPatentApplyByMap(urlData).then(function(data){
				if(data.code===AgreeConstant.code){
					var jsonData = data.data;
					$scope.itemList = jsonData.list;
					if ($scope.itemList.length===0) {
							$scope.pages = inform.initPages(); 			//初始化分页数据
							inform.common(Trans("tip.noData"));
					} else {
						// 分页信息设置
						$scope.pages.total = jsonData.total;		// 页面总数
						$scope.pages.star = jsonData.startRow;  	//页面起始数
						$scope.pages.end = jsonData.endRow;  		//页面大小数
						$scope.pages.pageNum = jsonData.pageNum;  	//页面页数
					}
				}
        	},
        	function(error) {
				inform.common(Trans("tip.requestError"));
			});		

		}
	     // 删除弹框
	      $scope.open =function(m) {
	         var modalInstance = $modal.open({
	          templateUrl: 'myModalContent.html',
	          controller: 'ModalInstanceCtrl',
	          size: "sm",
	          resolve: {
	            items: function() {
	              return Trans("common.deleteTip");
	            }
	          }
	        });
	        modalInstance.result.then(function() {
	            if (m) {
	              $scope.delete(m);
	            }
	          });
	      };

	      //删除信息 
	     $scope.delete = function (m){
	        
	            var urlData = {
	                'id':m.id
	            };
	           intellectualService.deletePatentApplyById(urlData).then(function(data){
	             if(data.code===AgreeConstant.code) {
	                 inform.common(data.message);
	                 $scope.getData(1);
	             } else{
	                 inform.common(data.message);
	             }
	         }, function(error) {
	             inform.common(Trans("tip.requestError"));
	         });
	     }
		 //页面跳转到我的星星--我的星星明细部门贡献详情页
		 $scope.getBack = function(){
			$state.go('app.personal_star_department_detail');
		}
	     /**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */	
	
	}]);
})();
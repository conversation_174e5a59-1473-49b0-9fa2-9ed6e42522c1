/**
 * Vue3 组件构建脚本
 * 用于自动化构建 Vue3 组件并集成到 AngularJS 项目中
 */
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const VUE_COMPONENTS_DIR = path.join(__dirname, 'src', 'vue-components');
const OUTPUT_DIR = path.join(__dirname, 'src', 'library', 'vue-components');

console.log('🚀 开始构建 Vue3 组件...');

try {
    // 检查 Vue 组件目录是否存在
    if (!fs.existsSync(VUE_COMPONENTS_DIR)) {
        console.error('❌ Vue 组件目录不存在:', VUE_COMPONENTS_DIR);
        process.exit(1);
    }

    // 检查 package.json 是否存在
    const packageJsonPath = path.join(VUE_COMPONENTS_DIR, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
        console.error('❌ Vue 组件 package.json 不存在:', packageJsonPath);
        process.exit(1);
    }

    // 进入 Vue 组件目录
    process.chdir(VUE_COMPONENTS_DIR);

    // 检查是否已安装依赖
    const nodeModulesPath = path.join(VUE_COMPONENTS_DIR, 'node_modules');
    if (!fs.existsSync(nodeModulesPath)) {
        console.log('📦 安装 Vue3 组件依赖...');
        execSync('npm install', { stdio: 'inherit' });
    }

    // 构建 Vue3 组件
    console.log('🔨 构建 Vue3 组件...');
    execSync('npm run build', { stdio: 'inherit' });

    // 检查构建输出
    if (fs.existsSync(OUTPUT_DIR)) {
        const files = fs.readdirSync(OUTPUT_DIR);
        console.log('✅ Vue3 组件构建完成！');
        console.log('📁 输出文件:', files.join(', '));

        // 检查关键文件是否存在
        const umdFile = files.find(file => file.includes('vue-components.umd.js'));
        const cssFile = files.find(file => file.includes('vue-components.css'));

        if (umdFile) {
            console.log('✅ UMD 文件已生成:', umdFile);
        } else {
            console.warn('⚠️  警告: 未找到 UMD 文件');
        }

        if (cssFile) {
            console.log('✅ CSS 文件已生成:', cssFile);
        } else {
            console.warn('⚠️  警告: 未找到 CSS 文件');
        }
    } else {
        console.error('❌ 构建输出目录不存在:', OUTPUT_DIR);
        process.exit(1);
    }

    console.log('🎉 Vue3 组件构建完成！');
    console.log('💡 现在可以在 AngularJS 项目中使用 Vue3 组件了');
    console.log('🔗 访问: http://localhost:8000/#/app/office/attendanceDetailVue');

} catch (error) {
    console.error('❌ 构建失败:', error.message);
    process.exit(1);
} finally {
    // 回到项目根目录
    process.chdir(__dirname);
}

/*
* @Author: fubaole
* @Date:   2018-02-26 15:22:53
* @Last Modified by:   fubaole
* @Last Modified time: 2018-03-12 15:55:04
*/
(function() {
    'use strict';
  app.factory('ConfigService', ConfigService);

  ConfigService.$inject=[];

  function ConfigService(){
    var treeParam={
       "checkboxConfig":{ // 带有复选框树配置
          data: { simpleData: { enable: true } },
          callback: { onCheck: "onCheck" },
          check: {
            enable: true,
            autoCheckTrigger: true,
            chkStyle: "checkbox"
          }
        },
        "radioConfig":{ // 带有单选框树配置
          data: { simpleData: { enable: true } },
          callback: { onCheck: "onCheck" },
          check: {
            enable: true,
            autoCheckTrigger: true,
            chkStyle: "radio", //单选框
            radioType: "all" //对所有节点设置单选
          }
        },
        "onRightClick":{ // 树配置有回调，有右键菜单
            data: { simpleData: { enable: true } },
            callback: { beforeClick: "nodeSelect", beforeExpand: "zTreeBeforeExpand", onExpand: "zTreeOnExpand", onCollapse: "zTreeOnCollapse",onRightClick : "OnRightClick" }
        },
        "dataAndCb":{ // 树配置有回调
          data: { simpleData: { enable: true } },
          callback: { beforeClick: "nodeSelect", beforeExpand: "zTreeBeforeExpand", onExpand: "zTreeOnExpand", onCollapse: "zTreeOnCollapse" }
        },
        "dataConfig":{ // 树配置
          data: { simpleData: { enable: true } }
        }
    };

    return treeParam;

  }
})();

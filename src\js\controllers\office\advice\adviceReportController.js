(function () {
    app.controller("adviceReportController", ['adviceService','$rootScope', '$scope','$modal','inform','LocalCache','Trans','AgreeConstant','$http',
        function (adviceService,$rootScope, $scope,$modal,inform,LocalCache,Trans,AgreeConstant,$http) {
     	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */ 
		$scope.formInput ={
				startTime:'',//开始时间
				endTime:''//结束时间
		};
		initTime();
		$scope.getData = getData; 			// 分页相关函数          

		getData();		//在刷新页面时调用该方法
        $scope.datepicker = {};
        $scope.toggleMin = toggleMin;
        toggleMin();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
	  	
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
		//重置
		$scope.rest = function() {
			initTime();
		}
		/**
		 * 初始化检索条件开始时间 及 结束时间
		 */
        function initTime(endDate){
			if (endDate==null || endDate==="" ){
				$scope.formInput.endTime = inform.format(new Date(),'yyyy-MM-dd');//获取当前系统时间
			} 
			var time = $scope.formInput.endTime.split("-");
			var start = time[0]+"/01"+"/01";
			$scope.formInput.startTime = inform.format(start,'yyyy-MM-dd');
			//对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
        }
		/**
    	 * 查询开始时间
    	 */
		$scope.openDateStart = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.openedStart = true;
			$scope.openedEnd = false;
		};
		
		/**
		 * 查询结束时间
		 */
		$scope.openDateEnd = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.openedStart = false;    
			$scope.openedEnd = true;
		};

		//获取当前选定时间
        function toggleMin() {
            $scope.datepicker.currentDate = $scope.datepicker.currentDate ? null : new Date();
        }
		
		//获取所有数据以分页的形式
		function getData(){
		   	$scope.itemList = [];
        	var urlData ={
        			'startTime':inform.format($scope.formInput.startTime,'yyyy-MM-dd'),
        			'endTime':inform.format($scope.formInput.endTime,'yyyy-MM-dd')
        	};
        	adviceService.getAdviceStatisticsByMap(urlData).then(function(data){
				if(data.code===AgreeConstant.code){
					var jsonData = data.data;
					$scope.itemList = jsonData;
					if ($scope.itemList.length === 0) {
						inform.common(data.message);
					}
				}		
        	},
        	function(error) {
				inform.common(Trans("tip.requestError"));
			});		

		}

		//生成Excel表格
		$scope.toExcel = function() {
			var urlData ={
					'currentPage':1,
					'pageSize':20,
        			'startTime':inform.format($scope.formInput.startTime,'yyyy-MM-dd'),
        			'endTime':inform.format($scope.formInput.endTime,'yyyy-MM-dd')
        	};
        	adviceService.getAdviceByMap(urlData).then(function(data){
        		if(data.code === AgreeConstant.code){

        				var modalInstance = $modal.open({
						  templateUrl: 'myModalContent.html',
		                  controller: 'ModalInstanceCtrl',
		                  size: "sm",
		                  resolve: {
		                    items: function() {
		                    return "确定要下载吗！";
		                    }
		                 }
						});
				       	  modalInstance.result.then(function() {
							//开启遮罩层
							inform.showLayer("下载中。。。。。。");
							$http.post(
							$rootScope.getWaySystemApi+'advice/toExcel',
							{	
								'startTime':inform.format($scope.formInput.startTime,'yyyy-MM-dd'),
        						'endTime':inform.format($scope.formInput.endTime,'yyyy-MM-dd')
							},
		            		 {headers: {
										'Content-Type': 'application/json',
										'Authorization':'Bearer ' + LocalCache.getSession("token")||''
									},
							  responseType: 'arraybuffer'//防止中文乱码
							 }
		            		 ).success(function(data){
		            			//如果是IE浏览器
		            			if (window.navigator && window.navigator.msSaveOrOpenBlob) {
		            				var csvData = new Blob([data], {type:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
		            				window.navigator.msSaveOrOpenBlob(csvData,'合理化建议情况汇报.xlsx');		   
		            			}
		            			//google或者火狐浏览器
		            			else{
		            				var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
		    						var objectUrl = URL.createObjectURL(blob);
		    						var aForExcel = $("<a download='合理化建议情况汇报.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href",objectUrl);
		    						$("body").append(aForExcel);
		    						$(".forExcel").click();
		    						aForExcel.remove();
		            			}

		            			// 关闭遮罩层
		 						inform.closeLayer();
		 						inform.common("下载成功!");
		            		 });
		       			});

        		}
        	},
        	function(error) {
				inform.common(Trans("tip.requestError"));
			});
	    };

	     	/**
	  		 * *************************************************************
	  		 *              方法声明部分                                 结束
	  		 * *************************************************************
	  		 */	
	
	}]);
})();
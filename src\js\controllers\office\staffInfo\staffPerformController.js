(function () {
    'use strict';
    app.controller("staffPerformManagementController", ['comService', '$rootScope', '$stateParams', '$scope', 'staffPerformanceService', '$modal', 'inform', 'Trans', 'AgreeConstant', '$state', 'LocalCache', '$http',
        function (comService, $rootScope, $stateParams, $scope, staffPerformanceService, $modal, inform, Trans, AgreeConstant, $state, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            // 列表数据
            $scope.dataList = [];

            $scope.datepicker = {
                currentDate: new Date()
            };

            //设置当前时间
            $scope.currentDate = $scope.currentDate ? null : inform.format(new Date(), "yyyy");
            $scope.stateSearchList = [{
                'code': '0',
                'name': '在职',
            }, {
                'code': '1',
                'name': '离职',
            }]
            //绑定文件控件改变事件
            $("#filesImg1").change(submitForm);
            $("#filesImg1").change(fileChangeEvent);
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //新增数据对象
            $scope.addParam = {};
            $scope.currentTime = inform.format(new Date(), "yyyy");
            $scope.getData = getData;
            //修改数据对象
            $scope.updateParam = {};
            getAreaList();//地区
            $scope.flag = false;
            //设置员工地区的访问权限
            $scope.areaCodeFlag = true;
            //查询条件对象
            $scope.searchObject = {};
            $scope.pages = {
                pageNum: "1",
                size: "100"
            };
            getStaffList();//员工编号和姓名列表
            initPrimaryDeptList();//初始化一级部门列表
            $scope.searchObject.primaryDept = "";//初始化一级部门
            $scope.searchObject.staffState = '0';//初始化员工状态
            initPage();//初始化页面
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */

            //重置查询条件
            $scope.clearParams = function () {
                //区域权限
                if (!$scope.areaCodeFlag) {
                    $scope.searchObject.area = "";
                }
                if ($scope.flag) {
                    $scope.searchObject.department = "";
                    $scope.searchObject.primaryDept = "";
                }
                $scope.searchObject.staffState = '0';
                $scope.searchObject.employeeName = "";
                $scope.searchObject.companyTitle = "";
                $scope.searchObject.onboardingTime = "";
                $scope.searchObject.startTime = "";
                $scope.searchObject.endTime = "";
                $("#form")[0].reset();
                $("#fileNameDis").text("");
            };


            /**
             ** 员工编号和姓名列表
             */
            function getStaffList() {
                // '1'代表获取所有的员工（包括离职的）
                comService.getEmployeesByOrgId('','1').then(function (data) {
                    if (data.data) {
                        $scope.staffList = data.data;
                    }
                });
            }

            /**
             * 选择上传文件后事件
             */
            function fileChangeEvent(e) {
                var fileName = "文件名称：" + e.currentTarget.files[0].name;
                $("#fileNameDis").text(fileName);
            }


            //获取地区
            function getAreaList() {
                $scope.areaList = [];
                comService.getParamList('AREA_TYPE', 'AREA_TYPE').then(function (data) {
                    $scope.areaList = data.data;
                });
            }

            /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function (data) {
                    if (data.data) {
                        $scope.primaryDeptList = data.data;
                    }
                });
            }

            /**
             * 初始化二级部门列表
             */
            function initSecDeptList() {
                //获取二级部门
                setDept();

            }

            function setDept() {
                //获取二级部门
                $scope.deptList = [];
                comService.getOrgChildren($scope.searchObject.primaryDept).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.deptList = data.data;
                    }
                });

            }

            //修改一级部门，二级部门进行联动
            $scope.changeDept = function () {
                setDept();
            };


            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 65);
            }

            //分页查询
            function getData(page) {
                if (inform.format($scope.searchObject.onboardingTime, 'yyyy-MM-dd') === "NaN-NaN-NaN") {
                    $scope.searchObject.onboardingTime = '';
                } else {
                    $scope.searchObject.onboardingTime = inform.format($scope.searchObject.onboardingTime, 'yyyy-MM-dd');
                }
                if (inform.format($scope.searchObject.startTime, 'yyyy') === "NaN") {
                    $scope.searchObject.startTime = '';
                } else {
                    $scope.searchObject.startTime = inform.format($scope.searchObject.startTime, 'yyyy');
                }
                if (inform.format($scope.searchObject.endTime, 'yyyy') === "NaN") {
                    $scope.searchObject.endTime = '';
                } else {
                    $scope.searchObject.endTime = inform.format($scope.searchObject.endTime, 'yyyy');
                }
                //拼装查询条件
                var params = {
                    area: $scope.searchObject.area,
                    primaryDept: $scope.searchObject.primaryDept,
                    department: $scope.searchObject.department,
                    employeeName: $scope.searchObject.employeeName,
                    onboardingTime: $scope.searchObject.onboardingTime,
                    staffState: $scope.searchObject.staffState,
                    companyTitle: $scope.searchObject.companyTitle,
                    startTime: $scope.searchObject.startTime,
                    endTime: $scope.searchObject.endTime,
                    page: page,
                    size: $scope.pages.size
                };
                //获取数据
                staffPerformanceService.selectPerformanceByParam(JSON.stringify(params)).then(function (result) {
                    if (result.code === '0000') {
                        $scope.dataList = result.data.list;
                        if (null == result.data.list || result.data.list.length === 0) {
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages();

                        } else {
                            // 分页信息设置
                            $scope.pages.total = result.data.total;		// 页面总数
                            $scope.pages.star = result.data.startRow;  	//页面起始数
                            $scope.pages.end = result.data.endRow;  		//页面大小数
                            $scope.pages.pageNum = result.data.pageNum;  	//页面页数
                        }

                    } else {

                        inform.common(result.message);

                    }
                }, function (reason) {
                    console.log("error");
                });
            }

            //初始化页面Function
            function initPage() {

                getDepartmentCode();
            }

            function getDepartmentCode() {
                //判断是否为中心办
                comService.isCenterOffice().then(function (res) {
                    if (res.data.code === '01') {
                        //01全部权限
                        $scope.flag = true;
                        getData(1);
                        return;
                    } else {
                        comService.validAuthentication("0002").then(function (result) {
                            if (result.code === '0000') {
                                if (result.data.code === '00') {
                                    $state.go('app.office.unAuthority');
                                    return;
                                }
                                if (result.data.code === '01') {
                                    $scope.flag = true;
                                    getData(1);
                                    return;
                                }
                                if (result.data.code === '03') {
                                    $scope.flag = true;
                                    $scope.areaCodeFlag = true;
                                    $('#area').attr('disabled', true);
                                    $scope.searchObject.area = result.data.areaCode;
                                    getData(1);
                                    return;
                                }
                                $scope.flagAuth = true;
                                $scope.searchObject.primaryDept = res.data.primaryDeptCode;
                                $("#primaryDeptName").attr("disabled", "disabled");
                                initSecDeptList();
                                if (res.data.departmentCode) {
                                    $scope.searchObject.department = res.data.departmentCode;
                                    $("#departmentName").attr("disabled", "disabled");
                                }
                                getData(1);


                            }
                        });


                    }
                });

            }


            //打开新增窗口
            $scope.addStaff = function () {
                $scope.addParam = {};
                $scope.addParam.year = inform.format(new Date(), "yyyy");
            };
            //保存新增数据
            $scope.saveAddData = function () {
                var param = {
                    "employeeId": $scope.addParam.employeeNo,
                    "year": $scope.addParam.year,
                    "quarter": $scope.addParam.quarter,
                    "evaluationResult": $scope.addParam.evaluationResult
                };
                staffPerformanceService.addPerformanceByParam(param).then(function (result) {
                    if (result.code === '0000') {
                        $("#add_staffInfo").modal('hide');
                        layer.msg(result.message,
                            {
                                time: 1000//1秒自动关闭
                            }, function () {
                                $scope.getData(1);
                                $scope.addParam = {};
                            }
                        );
                    } else {
                        layer.msg(result.message,
                            {
                                time: 1000//1秒自动关闭
                            }, function () {
                                $scope.getData(1);
                                $scope.addParam = {};
                            }
                        );

                    }
                });

            };

            //入职时间
            $scope.openOnboardingTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = true;
                $scope.openedStart = false;
                $scope.openedEnd = false;
                $scope.openOnboardTime2 = false;
            };
            //开始时间
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;
                $scope.openedStart = true;
                $scope.openedEnd = false;
                $scope.openOnboardTime2 = false;
            };
            //结束时间
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;
                $scope.openedStart = false;
                $scope.openedEnd = true;
                $scope.openOnboardTime2 = false;
            };
            //入职时间
            $scope.openOnboardTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;
                $scope.openedStart = false;
                $scope.openedEnd = false;
                $scope.openOnboardTime2 = true;

            };


            //打开修改窗口
            $scope.openUpdateModal = function (item) {
                $scope.updateParam = angular.copy(item);
            };

            //保存修改数据
            $scope.saveUpdateData = function () {
                var param = {
                    "id": $scope.updateParam.id,
                    "employeeId": $scope.updateParam.employeeId,
                    "year": $scope.updateParam.year,
                    "q1": $scope.updateParam.q1,
                    "q2": $scope.updateParam.q2,
                    "q3": $scope.updateParam.q3,
                    "q4": $scope.updateParam.q4,
                    "q6": $scope.updateParam.q6,
                    "q7": $scope.updateParam.q7,
                    "assessYear": $scope.updateParam.assessYear
                };
                staffPerformanceService.updatePerformanceByParam(param).then(function (result) {
                    if (result.code === '0000') {
                        $("#update_staffInfo").modal("hide");
                        layer.msg("修改成功",
                            {
                                time: 1000//1秒自动关闭
                            }, function () {
                                getData(1);
                            }
                        );

                    } else {
                        inform.common(result.message);
                    }
                });

            };

            // 删除确认
            $scope.deleteConfirm = function (id) {
                inform.modalInstance("确认要删除吗？").result.then(function () {
                    $scope.deleteByIds(id);
                });

            };

            //根据选中的id 删除数据
            $scope.deleteByIds = function (id) {

                var param = {
                    "id": id
                };
                staffPerformanceService.deletePerformanceByIds(param)
                    .then(function (data) {
                        if (data.code === "0000") {
                            inform.common('删除成功');
                            getData(1);
                        } else {
                            inform.common(data.message);
                        }
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });

            };


            //生成Excel模板
            $scope.toTemplateExcel = function () {
                if ($scope.searchObject.startTime && !$scope.searchObject.endTime) {
                    inform.common("请选择结束时间");
                    return;
                }
                if (!$scope.searchObject.startTime && $scope.searchObject.endTime) {
                    inform.common("请选择开始时间");
                    return;
                }

                inform.modalInstance("确定要下载吗?").result.then(function () {

                    var params = {
                        startTime: inform.format($scope.searchObject.startTime, 'yyyy') === 'NaN' ? null : inform.format($scope.searchObject.startTime, 'yyyy'),
                        endTime: inform.format($scope.searchObject.endTime, 'yyyy') === 'NaN' ? null : inform.format($scope.searchObject.endTime, 'yyyy')
                    };
                    inform.downLoadFile('staffPerform/toTemplateExcel', params, "员工绩效信息表模板.xlsx");
                });

            };

            //生成Excel表格
            $scope.toExcel = function () {

                inform.modalInstance("确定要下载吗?").result.then(function () {

                    var params = {
                        area: $scope.searchObject.area,
                        primaryDept: $scope.searchObject.primaryDept,
                        department: $scope.searchObject.department,
                        employeeName: $scope.searchObject.employeeName,
                        staffState: $scope.searchObject.staffState,
                        onboardingTime: $scope.searchObject.onboardingTime,
                        companyTitle: $scope.searchObject.companyTitle,
                        startTime: inform.format($scope.searchObject.startTime, 'yyyy') === 'NaN' ? null : inform.format($scope.searchObject.startTime, 'yyyy'),
                        endTime: inform.format($scope.searchObject.endTime, 'yyyy') === 'NaN' ? null : inform.format($scope.searchObject.endTime, 'yyyy')
                    };
                    inform.downLoadFile('staffPerform/toExcel', params, "员工绩效信息表.xlsx");

                });

            };

            $scope.selectFile = function () {
                document.getElementById("filesImg1").click();
            };


            //上传文件
            function submitForm() {

                var formData = new FormData(document.getElementById("form"));//表单id  初始化表单值
                var file = document.querySelector('input[type=file]').files[0]; //获取文档中有类型为file的第一个input元素
                if (!file) {
                    inform.common("请先选择文件!");
                    return false;
                }
                formData.append('fileName', file);
                var a = file.type;
                if (a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
                    inform.common("请选择.xlsx类型的文档进行上传!");
                    return false;
                } else {
                    inform.modalInstance("确定要上传文件吗?").result.then(function () {
                        inform.uploadFile('staffPerform/uploadExcel', formData, function func(result) {
                            if (result.code === AgreeConstant.code) {
                                // 关闭遮罩层
                                inform.closeLayer();
                                $modal.open({
                                    templateUrl: 'errorModel.html',
                                    controller: 'ModalInstanceCtrl',
                                    size: "lg",
                                    resolve: {
                                        items: function () {
                                            return result.message;
                                        }
                                    }
                                });

                            } else {
                                // 关闭遮罩层
                                inform.closeLayer();
                                inform.common("上传失败！");
                            }
                            getData(1);
                            $("#form")[0].reset();
                            $("#fileNameDis").text("");
                        });
                    });
                }
            }

            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */


        }
    ]);
})();
<!-- 覆盖样式 -->
<style>
    td,
    th {
        text-align: center;
    }
</style>

<!-- 弹出框 -->
<script type="text/ng-template" id="myModalContent.html">
    <div ng-include="'tpl/common/modal.html'"></div>
</script>
<!-- 导航条  start-->
<div class="animated fadeInUp">
    <ul class="breadcrumb b-a">
        <li><i class="fa fa-home"></i><span translate="员工信息管理"></span></li>
        <li class="active" translate="员工考勤信息"></li>
    </ul>
</div>

<!-- Vue3 组件演示区域 -->
<div class="vue-demo-section" style="margin-bottom: 20px;">
    <div class="panel">
        <div class="panel-header" style="padding: 10px 15px; background-color: #f5f5f5; border-bottom: 1px solid #ddd;">
            <h4 style="margin: 0; color: #333;">
                <i class="fa fa-magic" style="color: #409eff;"></i>
                Vue3 + Element Plus 组件演示
                <small style="color: #666; margin-left: 10px;">（这是一个嵌入在 AngularJS 页面中的 Vue3 组件）</small>
            </h4>
        </div>
        <div class="panel-body">
            <!-- Vue3 组件容器 -->
            <div vue-component="SimpleDemo" style="min-height: 200px;">
                <!-- 加载中显示 -->
                <div class="text-center" style="padding: 50px;">
                    <i class="fa fa-spinner fa-spin fa-2x" style="color: #409eff;"></i>
                    <p style="margin-top: 10px; color: #666;">正在加载 Vue3 组件...</p>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 	 查询条件 -->
<div class="panel search">
    <span class="title" translate="common.searchHeader" style="text-align: left;"></span>
    <div class="search-list pull-right">
        <a ng-click="getData(1)"> <i class="fa fa-search"></i> <span translate="common.searchBtn"></span>
        </a> <a ng-click="reset()"> <i class="fa fa-repeat"></i> <span translate="common.resetBtn"></span>
        </a>
    </div>

    <div class="panel-body form-horizontal">
        <div class="row">
            <div class="col-sm-1 control-label">
                <label for="department">一级部门：</label>
            </div>
            <div class="col-sm-2">
                <select id="primaryDeptName" class="form-control"
                    ng-options="item.orgCode as item.orgName for item in primaryDeptList" ng-model="primaryDeptCode"
                    ng-change="changeDept()">
                    <option value="">请选择</option>
                </select>
            </div>
            <div class="col-sm-1 control-label">
                <label for="department">二级部门：</label>
            </div>
            <div class="col-sm-2">
                <select class="form-control" ng-options="item.orgCode as item.orgName for item in departmentList"
                    id="department" ng-model="departmentCode">
                    <option value="">请选择</option>
                </select>
            </div>

            <div class="col-sm-1 control-label">
                <label for="realName">姓名：</label>
            </div>
            <div class="col-sm-2">
                <input ng-enter="getData(1)" class="form-control" type="text" placeholder="请输入姓名" id="realName"
                    ng-model="formInput.realName">
            </div>


        </div>

        <div class="row">
            <div class="col-sm-1 control-label">
                <label for="region">地区：</label>
            </div>
            <div class="col-sm-2">
                <select class="form-control" id="region"
                    ng-options="item.param_code as item.param_value for item in areaList" ng-model="formInput.region">
                    <option value="">请选择</option>
                </select>
            </div>
            <div class="col-sm-1 control-label">
                <label for="startTime">统计时间：</label>
            </div>
            <div class="col-sm-2">
                <input class="form-control time" type="text" id="startTime"
                    onfocus="WdatePicker({dateFmt:'yyyy-MM',onpicked:function(){$(this).trigger('change')},maxDate:'#F{$dp.$D(\'endTime\')}'})"
                    ng-model="formInput.startTime" onChange="" placeholder="{{'请选择开始时间'|Trans}}" readonly>
            </div>

            <div class="col-sm-1 control-label">
                <label for="endTime">至&nbsp;&nbsp;&nbsp;</label>
            </div>
            <div class="col-sm-2">
                <input class="form-control time" type="text" id="endTime"
                    onfocus="WdatePicker({dateFmt:'yyyy-MM',onpicked:function(){$(this).trigger('change')},minDate:'#F{$dp.$D(\'startTime\')}'})"
                    ng-model="formInput.endTime" onChange="" placeholder="{{'请选择结束时间'|Trans}}" readonly>
            </div>

        </div>
    </div>

</div>

<!-- 表格操作图标 -->
<div class="panel search table-opt">
    <span class="title" translate="common.queryList" style="text-align: left;"></span>
    <span style="margin-left: 100px;"></span>
    <!--    <span translate="人均延迟打卡工时:" style="width:120px; font-weight:bold;"></span>
    <span ng-bind="avgOvertimeTotalHour" style="color:red;font-weight:bold;"/>

    <span style="margin-left: 50px;"></span>
    <span translate="人均平日延迟打卡工时:" style="width:120px;font-weight:bold;"></span>
    <span ng-bind="avgWorkOvertime" style="color:red;font-weight:bold;"/>

    <span style="margin-left: 50px;"></span>
    <span translate="人均周末延迟打卡工时:" style="width:120px;font-weight:bold;"></span>
    <span ng-bind="avgWeekendOvertime" style="color:red;font-weight:bold;"/>-->

    <div class="search-list pull-right " style="margin-left: 20px;">
        <div class="search-list pull-left " style="margin-right: 20px;" ng-show="flag">
            <a class="search-list pull-left" ng-click="toTemplateExcel()" style="margin-left: 20px;" ng-cancel>
                <i class="fa fa-file-excel-o"></i>
                <span>下载模板</span>
            </a>
        </div>
        <div class="search-list pull-left " style="margin-right: 20px;">
            <a ng-click="toExcel()">
                <i class="glyphicon glyphicon-arrow-down"></i>
                <span translate="下载Excel"></span>
            </a>
        </div>
        <div class="search-list pull-left " style="margin-right: 20px;">
            <span id="fileNameDis"></span>
        </div>
        <div class="search-list pull-left " style="margin-right: 20px;" ng-show="flag">
            <form enctype="multipart/form-data" id="uploadForm">
                <input style="width: 50px;display:none" type="file" id="filesImg" class="input-uploader"
                    accept="application/msexcel" />
                <a ng-click="selectFile()">
                    <i class="glyphicon glyphicon-arrow-up"></i>
                    <span translate="批量导入"></span>
                </a>
            </form>
        </div>
    </div>

</div>

<div ng-if="dataTableShow==1">
    <div class="report-box clear wrapperReport table-responsive" id="divTBDis"
        data-ng-include="'tpl/office/attendance/attendanceDataTable.html'">
    </div>
    <div data-ng-include="'tpl/blocks/pagination.html'"></div>
</div>


<script type="text/ng-template" id="authModel.html">
    <div class="modal-header bg-black">
        <h5 class="modal-title" translate="common.tips"></h5>
    </div>
    <div class="modal-body text-center m" >
        <div style="white-space:pre-line;text-align:center">{{items}}</div>
    </div>
</script>

<!-- 不符合条件弹框 -->
<script type="text/ng-template" id="errorModel.html">
    <div class="modal-header bg-black">
        <button type="button" class="close" ng-click="cancel()">
            &times;
        </button>
        <h5 class="modal-title" translate="common.tips"></h5>
    </div>
    <div class="modal-body text-center m">
        <div style="white-space:pre-line;text-align:left">{{items}}</div>
    </div>
</script>
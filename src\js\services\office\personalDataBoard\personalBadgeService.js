(function() {
    'use strict';
    app.factory('personalBadgeService', personalBadgeService);
    personalBadgeService.$inject = [ "HttpService", '$rootScope' ];

    function personalBadgeService(HttpService, $rootScope) {
        var service = {
            selectBadge:selectBadge,
            queryPersonalBadgeNum:queryPersonalBadgeNum,
            selectBadgeDetail:selectBadgeDetail
            
        };
        return service;

        //查询徽章数据
        function selectBadge(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'personalBadge/selectBadge', urlData);
        }
        //查询个人徽章数量
        function queryPersonalBadgeNum(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'personalBadge/queryPersonalBadgeNum', urlData);
        }
        //查询徽章明细数据
        function selectBadgeDetail(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'personalBadge/selectBadgeDetail', urlData);
        }
        
        


    }
})();

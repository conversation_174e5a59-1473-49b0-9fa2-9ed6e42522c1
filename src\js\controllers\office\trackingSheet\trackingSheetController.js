//# sourceURL=js/controllers/office/trackingSheet/trackingSheetController.js
(function () {
    app.controller('trackingSheetController', [
        '$rootScope',
        'comService',
        '$scope',
        '$state',
        '$stateParams',
        '$modal',
        'trackingSheetService',
        'proProjectModule',
        'projectManagementService',
        'inform',
        'Trans',
        'AgreeConstant',
        'LocalCache',
        '$http',
        'lowQualityService',
        function (
            $rootScope,
            comService,
            $scope,
            $state,
            $stateParams,
            $modal,
            trackingSheetService,
            proProjectModule,
            projectManagementService,
            inform,
            Trans,
            AgreeConstant,
            LocalCache,
            $http,
            lowQualityService
        ) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.formRefer = {};

            $scope.limitList = AgreeConstant.limitList;
            //获取项目信息
            $scope.loaclParam = LocalCache.getObject('project_detail');
            if ($scope.loaclParam.projectInfoParam) {
                $scope.projectInfoParam = JSON.parse($scope.loaclParam.projectInfoParam);
            }
            $scope.type = $stateParams.type;
            //分页
            $scope.pages = inform.initPages();
            $scope.pages.size = '50';
            $scope.notCommonProblem = $stateParams.notCommonProblem;
            //设置列表的高度
            setDivHeight();
            $scope.keep = false;
            $scope.del = false;
            $scope.bug = false;
            $scope.lowQuality = false;
            $scope.task = false;
            $scope.comfirmPerson = false;
            $scope.update = false;
            // 初始化
            $scope.taskInfo = {};
            $scope.bugInfo = {};
            $scope.lowQualityInfo = {};
            // 转任务时保存跟踪单编号
            var trackingSheetNum;
            //判断按钮是否具有权限
            getButtonPermission();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //初始化查询控件值
            initInfo();
            //审批状态下拉框数
            $scope.approvalStatusSelect = ['审批中', '完成', '被终止', '完成复核'];
            //审批结果下拉框数据
            $scope.approvalResultSelect = ['同意', '拒绝'];
            //天数查询条件
            $scope.timeSelect = [
                { name: '>2天', value: 2 },
                { name: '>5天', value: 5 },
                { name: '>10天', value: 10 },
                { name: '>15天', value: 15 },
            ];
            $scope.reopenList = [
                { name: '>=1次', value: 1 },
                { name: '>=2次', value: 2 },
            ];
            //各个阶段禅道任务ID
            var allStage = [
                'immediateDisposalZentaoTaskId',
                'technicalProposalZentaoTaskId',
                'testQualityZentaoTaskId',
                'maintenanceSecurityPlanZentaoTaskId',
            ];
            //各个阶段方案属性名
            var allStagePlan = ['immediateDisposalPlan', 'technicalProposal', 'testQuality', 'maintenanceSecurityPlan'];
            //各个阶段计划完成时间属性名
            var allStageTime = [
                'immediateDisposalPlanCompleteTime',
                'technicalProposalPlanCompleteTime',
                'testQualityPlanCompleteTime',
                'maintenanceSecurityPlanCompleteTime',
            ];
            //各个阶段人员属性名
            var allStagePerson = [
                'immediateDisposalPerson',
                'technicalProposalPerson',
                'testQualityPerson',
                'maintenanceSecurityPlanPerson',
            ];
            //各个阶段对应的中文名
            var allStageMap = {
                immediateDisposalZentaoTaskId: '立即解决方案',
                technicalProposalZentaoTaskId: '长期解决方案',
                testQualityZentaoTaskId: '测试质量保证',
                maintenanceSecurityPlanZentaoTaskId: 'reopen感知保障方案',
            };
            //对应的数据库字段
            var allStageColMap = {
                immediateDisposalZentaoTaskId: 'immediate_disposal_zentao_task_id',
                technicalProposalZentaoTaskId: 'technical_proposal_zentao_task_id',
                testQualityZentaoTaskId: 'test_quality_zentao_task_id',
                maintenanceSecurityPlanZentaoTaskId: 'maintenance_security_plan_zentao_task_id',
            };
            //获取缓存
            $scope.formRefer = LocalCache.getObject('trackingSheet_formRefer');
            if (null !== $scope.notCommonProblem) {
                $scope.formRefer.commonProblemsClassification = $scope.notCommonProblem;
            }
            $scope.select_all = false;

            //初始化默认日期
            if (!$scope.formRefer.startDateCurrent) {
                // 该controller用于三个页面：1.测试数据/线上bug/跟踪单信息 2.系研看板/项目看板--线上问题（页签）
                // 3.系研看板/团队看板--线上问题（页签）。
                // 其中第一个页面默认查询时间为本年，其余为4年前
                if ($state.current.url === '/trackingSheet') {
                    $scope.formRefer.startDateCurrent = inform.format(new Date(), 'yyyy') + '-01-01';
                } else {
                    $scope.formRefer.startDateCurrent =
                        inform.format(new Date().setMonth(new Date().getMonth() - 48), 'yyyy') + '-01-01';
                }
            }
            getData();
            //被选择跟踪单集合
            $scope.sheetSelected = [];
            $scope.getData = getData; // 分页相关函数
            $scope.getConfirmPerson = getConfirmPerson;
            $scope.saveConfirmPerson = saveConfirmPerson;
            $scope.addConfirmPerson = addConfirmPerson;
            $scope.deleteConfirmPerson = deleteConfirmPerson;
            $scope.updateReopen = updateReopen;
            //重置
            $scope.rest = function () {
                $scope.formRefer = {};
                $scope.formRefer.startDateCurrent = inform.format(new Date(), 'yyyy') + '-01-01';
            };
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight;
                if (null !== $scope.notCommonProblem) {
                    divHeight = clientHeight - (150 + 160);
                } else {
                    divHeight = clientHeight - (150 + 250);
                }
                $('#divTBDis').height(divHeight);
                $('#subDivTBDis').height(divHeight - 80);
                $('#subDivTBDis5').height(divHeight - 30);
            }
            //重置
            $scope.reset = function () {
                if (null !== $scope.notCommonProblem) {
                    $scope.formRefer = {};
                    $scope.formRefer.commonProblemsClassification = $scope.notCommonProblem;
                }
            };
            /**
             * 跳转修改
             */
            $scope.go = function (item, flag) {
                // 设置滚动条高度
                $scope.formRefer.subDivTBDisScrollTop = $('#fixedLeftAndTop').parent().scrollTop();
                LocalCache.setObject('trackingSheet_formRefer', $scope.formRefer);
                $state.go('app.office.trackingSheetManagement', {
                    item: item.approvalId,
                    flag: flag,
                    batchNum: item.batchNum,
                    activatedCount: item.activatedCount,
                    approvalNum: item.approvalNum,
                    startTime: item.startTime,
                    sponsorName: item.sponsorName,
                    productLine: $scope.formRefer.productLine,
                    department: $scope.formRefer.department,
                });
            };

            /**
             * 初始化
             */
            function initInfo() {
                //获取部门
                $scope.departmentList = ['应用研究室', '驱动研究室', '平台研究室', '软测研究室'];
                //获取产品线
                $scope.projectLine = [];
                comService.queryEffectiveParam('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.projectLine = data.data;
                    }
                });
                //获取员工信息
                $scope.employeeList = [];
                $scope.employeesMap = {};
                $scope.employeesIdMap = {};
                comService.getEmployeesByOrgId('').then(function (data) {
                    $scope.employeeList = data.data;
                    for (var i = 0; i < $scope.employeeList.length; i++) {
                        $scope.employeesMap[$scope.employeeList[i].realName] = $scope.employeeList[i].loginName;
                        $scope.employeesIdMap[$scope.employeeList[i].realName] = $scope.employeeList[i].employeeNo;
                    }
                });
                //获取平台项目信息
                $scope.projectList = [];
                var urlData = {
                    page: '1',
                    pageSize: '10000',
                };
                projectManagementService.getProjectInfoList(urlData).then(function (data) {
                    $scope.projectList = data.data.list;
                });
            }
            //设置发起时间选择条件
            function setSelectTime() {
                if ($scope.formRefer.startDateCurrent && $scope.formRefer.startDateCurrent.length === 10) {
                    $scope.formRefer.startDate = $scope.formRefer.startDateCurrent + ' 00:00:00';
                }
                if ($scope.formRefer.endDateCurrent && $scope.formRefer.endDateCurrent.length === 10) {
                    $scope.formRefer.endDate = $scope.formRefer.endDateCurrent + ' 23:59:50';
                }
            }
            /**
             * 获取所有跟踪单信息
             */
            function getData(pageNum) {
                //删除已加载冻结头部和部分列的HTML模板
                $scope.dataTableShow = 0;
                setSelectTime();
                var urlData = $scope.formRefer;

                urlData.currentPage = pageNum; //当前页数
                urlData.pageSize = $scope.pages.size; //每页显示条数
                if ($scope.projectInfoParam && $scope.type !== 'menu') {
                    urlData.officeProjectId = $scope.projectInfoParam.id;
                    urlData.currentPage = 1; //当前页数
                    urlData.pageSize = 500; //每页显示条数
                }
                trackingSheetService.getData(urlData).then(
                    function (data) {
                        //重新加载冻结头部和部分列的HTML模板
                        $scope.dataTableShow = 1;
                        if (data.code === AgreeConstant.code) {
                            if (null == data.data) {
                                $scope.sheetData = {};
                                inform.common(Trans('tip.noData'));
                                $scope.pages = inform.initPages();
                                $scope.pages.size = '50';
                            } else {
                                //跟踪单详情
                                $scope.sheetData = data.data.list;
                                angular.forEach($scope.sheetData, function (i) {
                                    i.checked = false;
                                });
                                //分页信息设置
                                $scope.pages.total = data.data.total;
                                $scope.pages.star = data.data.startRow;
                                $scope.pages.end = data.data.endRow;
                                $scope.pages.pageNum = data.data.pageNum;
                            }
                            //调用DataTable组件冻结表头和左侧及右侧的列
                            setTimeout(showDataTable, 300);
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            /**
             *调用DataTable组件冻结表头和左侧及右侧的列
             */
            function showDataTable() {
                $('#fixedLeftAndTop').DataTable({
                    //可被重新初始化
                    retrieve: true,
                    //自适应高度
                    scrollY: 'calc(100vh - 350px)',
                    scrollX: true,
                    scrollCollapse: true,
                    //控制每页显示
                    paging: false,
                    //冻结列（默认冻结左1）
                    fixedColumns: {
                        leftColumns: 3,
                        rightColumns: 1,
                    },
                    //search框显示
                    searching: false,
                    //排序箭头
                    ordering: false,
                    //底部统计数据
                    info: false,
                });

                // 获取到跟踪单信息之后滚动条跳至缓存中保存的scrollTop
                setTimeout(function () {
                    if ($scope.formRefer.subDivTBDisScrollTop) {
                        $('#fixedLeftAndTop')
                            .parent()
                            .animate({ scrollTop: $scope.formRefer.subDivTBDisScrollTop }, 10);
                    }
                }, 500);
            }
            /**
             * 如果没有选择就同步所有的跟踪单
             **/
            function selectAll() {
                if ($scope.sheetSelected.length === 0) {
                    angular.forEach($scope.sheetData, function (i) {
                        if (i.approvalStatus !== '完成' && i.approvalStatus !== '完成复核') {
                            $scope.sheetSelected.push(i.approvalId);
                        }
                    });
                }
            }

            //单选项目
            $scope.selectOne = function (i) {
                var index = $scope.sheetSelected.indexOf(i.approvalId);
                if (index === -1) {
                    $scope.sheetSelected.push(i.approvalId);
                } else if (index !== -1) {
                    $scope.sheetSelected.splice(index, 1);
                }
            };
            /**
             * 同步
             */
            $scope.syncTrackingSheetDetail = function () {
                //如果没有选择就同步所有的跟踪单
                selectAll();
                trackingSheetService.syncTrackingSheetDetail($scope.sheetSelected).then(
                    function (result) {
                        inform.common(result.message);
                        //清空已同步跟踪单
                        $scope.sheetSelected = [];
                        getData();
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            /**
             * 获取按钮权限
             */
            function getButtonPermission() {
                var buttons = {
                    'Button-trackingSheet-keep': 'keep',
                    'Button-trackingSheet-del': 'del',
                    'Button-trackingSheet-toBug': 'bug',
                    'Button-trackingSheet-toLowQuality': 'lowQuality',
                    'Button-trackingSheet-toTask': 'task',
                    'Button-trackingSheet-comfirmPerson': 'comfirmPerson',
                    'Button-trackingSheet-update': 'update',
                };
                var urlData = {
                    userId: LocalCache.getSession('userId'),
                    parentPermission: 'ButtonTrackingSheetManagement',
                    buttons: buttons,
                };
                comService.getButtonPermission(urlData, $scope);
            }
            /**
             * 存档
             */
            $scope.keepOnFile = function (id) {
                trackingSheetService.keepOnFile(id).then(
                    function (result) {
                        inform.common(result.message);
                        getData();
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            /**
             * 删除
             */
            $scope.delData = function (id) {
                inform.modalInstance('确定要删除吗？').result.then(function () {
                    trackingSheetService.delData(id).then(function (data) {
                        getData();
                    });
                });
            };
            /**
             *下载跟踪单信息
             */
            $scope.toExcel = function () {
                inform.modalInstance('确定要下载跟踪单信息表吗？').result.then(function () {
                    setSelectTime();
                    inform.downLoadFile(
                        'trackingSheet/toExcel',
                        $scope.formRefer,
                        '跟踪单信息表' + inform.format(new Date(), 'yyyy-MM-dd') + '.xlsx'
                    );
                });
            };
            /**
             * 转任务前置方法
             */
            $scope.toTaskBefore = function (item) {
                if (item.officeProjectId == null || item.officeProjectId === '') {
                    inform.common('未维护平台项目，无法操作。');
                    return;
                }
                getTaskStageList(item);
                if ($scope.taskStageList.length === 0) {
                    inform.common('该跟踪单所有阶段均转任务，无法重复操作。');
                    return;
                }
                //获取任务类型
                comService.getProLangList('task', 'typeList', 'zh-cn', '').then(function (data) {
                    if (data.data) {
                        $scope.taskTypeList = data.data;
                        $scope.taskInfo = item;
                        //弹窗
                        $('#task').click();
                    }
                });
            };
            /**
             * 获取任务可创建阶段及其对应责任人
             */
            function getTaskStageList(item) {
                //可选择的下拉框
                $scope.taskStageList = [];
                //下拉框与人员的对应关系
                $scope.personStageMap = {};
                //下拉框与计划的对应关系
                $scope.personStagePlanMap = {};
                //下拉框与时间的对应关系
                $scope.personStageTimeMap = {};
                for (var i = 0; i < allStage.length; i++) {
                    var str = allStage[i];
                    //放下拉框的内容
                    if (item[str] == null || item[str] === '' || item[str] * 1 === 0) {
                        $scope.taskStageList.push({
                            code: str,
                            name: allStageMap[str],
                        });
                    }
                    //放映射关系
                    $scope.personStageMap[str] = item[allStagePerson[i]];
                    $scope.personStagePlanMap[str] = item[allStagePlan[i]];
                    $scope.personStageTimeMap[str] = item[allStageTime[i]];
                }
                $scope.problemDescription = item.problemDescription;
            }
            /**
             * 获取阶段对应的责任人、方案、计划完成时间
             */
            $scope.getTaskInfo = function () {
                var stage = $scope.taskInfo.taskStage;
                $scope.taskInfo.assignedTo = $scope.personStageMap[stage];
                $scope.taskInfo.desc = $scope.personStagePlanMap[stage];
                $scope.taskInfo.taskName = '[' + allStageMap[stage] + ']' + $scope.problemDescription.substring(0, 170);
                $scope.taskInfo.taskType = 'online';
                $scope.taskInfo.taskStartTime = inform.format(new Date(), 'yyyy-MM-dd');
                var endTime = $scope.personStageTimeMap[stage];
                //如果计划完成时间小于当前时间
                if ($scope.taskInfo.taskStartTime > endTime || endTime == null) {
                    $scope.taskInfo.taskEndTime = inform.format(new Date(), 'yyyy-MM-dd');
                } else {
                    $scope.taskInfo.taskEndTime = inform.format(endTime, 'yyyy-MM-dd');
                }
            };
            /**
             * 初始化产品线与禅道项目弹框
             */
            $scope.initModule = function (flag) {
                var data = {
                    keyWord: $scope.taskInfo.officeProjectName,
                };
                if (flag) {
                    proProjectModule.initModule(data, $scope, setBugProProjectInfo);
                } else {
                    proProjectModule.initModule(data, $scope, setProProjectInfo);
                }
            };
            /**
             * 根据所选中的禅道项目回填信息
             */
            function setProProjectInfo(data) {
                $scope.taskInfo.proProjectName = data.name;
                $scope.taskInfo.proProjectId = data.id;
                $scope.taskInfo.parentId = data.parentId;
            }
            /**
             * 根据所选中的禅道项目回填信息(转bug)
             */
            function setBugProProjectInfo(data) {
                $scope.bugInfo.proProjectName = data.name;
                $scope.bugInfo.proProjectId = data.id;
                $scope.bugInfo.parentId = data.parentId;
            }
            /**
             * 转任务
             */
            $scope.toTask = function () {
                if ($scope.taskInfo.proProjectId == null || $scope.taskInfo.proProjectId === '') {
                    inform.common('请选择禅道项目。');
                    return;
                }
                var urlData = {
                    parentId: $scope.taskInfo.parentId,
                    project: $scope.taskInfo.proProjectId,
                    type: $scope.taskInfo.taskType,
                    name: $scope.taskInfo.taskName,
                    assignedTo: $scope.employeesMap[$scope.taskInfo.assignedTo],
                    openedBy: LocalCache.getSession('currentUserName'),
                    openedDate: inform.format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
                    assignedDate: inform.format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
                    desc: $scope.taskInfo.desc,
                    estimate: 0.5,
                    ...formatTime(),
                };
                trackingSheetService.toTask(urlData).then(
                    function (result) {
                        if (result.code === AgreeConstant.code) {
                            //转完任务，根据返回的ID去更新跟踪单信息
                            updateTrackingSheetZenTao(result.data);
                        } else {
                            inform.common('任务创建失败！');
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            /**
             * 开始时间没有值，默认是当前时间，
             * 结束时间没有值，默认是当前时间的5天之后
             */
            function formatTime() {
                let estStarted = $scope.taskInfo.taskStartTime;
                let deadline = $scope.taskInfo.taskEndTime;

                const now = new Date();
                if (!estStarted) estStarted = inform.format(new Date(), 'yyyy-MM-dd');
                if (!deadline) {
                    const date = now.setDate(now.getDate() + 5);
                    deadline = inform.format(date, 'yyyy-MM-dd');
                }

                return { estStarted, deadline };
            }
            /**
             * 根据返回ID更新跟踪单信息
             */
            function updateTrackingSheetZenTao(zenTaoId) {
                var urlData = {
                    key: allStageColMap[$scope.taskInfo.taskStage],
                    zentaoId: zenTaoId,
                    approvalId: $scope.taskInfo.approvalId,
                };
                trackingSheetService.updateTrackingSheetZenTao(urlData).then(
                    function (result) {
                        $('#toTask').modal('hide');
                        inform.common(result.message);
                        getData();
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            /**
             * 获取所有任务信息
             */
            $scope.seeTask = function (m) {
                var urlData = [];
                for (var i = 0; i < allStage.length; i++) {
                    var str = allStage[i];
                    //放下拉框的内容
                    if (m[str] != null || m[str] !== '' || m[str] * 1 !== 0) {
                        urlData.push(m[str]);
                    }
                }
                trackingSheetService.seeTask(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null == data.data) {
                                $scope.taskData = {};
                                inform.common(Trans('tip.noData'));
                            } else {
                                //任务详情
                                $scope.taskData = data.data;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            /**
             *
             *清空bug相关的信息
             **/
            function cleanBugInfo() {
                $scope.bugInfo.title = '';
                $scope.bugInfo.steps = '';
                $scope.bugInfo.assignedTo = {};
                $scope.bugInfo.type = {};
                // 设置默认值（严重程度为一般，优先级为3）
                $scope.bugInfo.severity = '3';
                $scope.bugInfo.pri = '3';
                $scope.taskInfo.officeProjectName = '';
            }
            /**
             * 转bug前置方法
             */
            $scope.toBug = function (item) {
                $scope.way = 0;
                if (item.zentaoBugId !== null && item.zentaoBugId !== '' && item.zentaoBugId * 1 !== 0) {
                    inform.common('该跟踪单已转bug，无法重复操作。');
                    return;
                }
                $scope.bugInfo.assignedTo = {};
                $scope.bugInfo.type = {};
                // bug标题默认为问题描述
                $scope.bugInfo.title = '【线上问题】' + item.problemDescription;
                $scope.bugInfo.steps = item.problemDescription;
                // 获取bug类型
                comService.getProLangList('bug', 'typeList', 'zh-cn', '0').then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.bugTypeList = data.data;
                    } else {
                        inform.common(data.message);
                    }
                });
                // 获取严重程度列表
                comService.getProLangList('bug', 'severityList', 'all', '1').then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.severityTypeList = data.data;
                    } else {
                        inform.common(data.message);
                    }
                });
                // 设置默认值（严重程度为一般，优先级为3）
                $scope.bugInfo.severity = getBugSeverity(item.problemLevel);
                $scope.bugInfo.pri = '3';
                // 保存跟踪单编号
                trackingSheetNum = item.approvalNum;
                //弹窗
                $scope.taskInfo.officeProjectName = item.officeProjectName;
                $('#bug').click();
            };
            /**
             *根据跟踪单级别转换对应bug严重级别
             **/
            function getBugSeverity(problemLevel) {
                if ('事件' === problemLevel) {
                    //致命的
                    return '1';
                }
                if ('严重线上问题' === problemLevel) {
                    //严重的
                    return '2';
                }
                // 设置默认值（严重程度为一般）
                return '3';
            }
            /**
             * 转低级质量前置方法
             */
            $scope.toLowQuality = function (item) {
                $scope.way = 0;
                $scope.approvalNum = item.approvalNum;
                // 设置默认值
                $scope.lowQualityInfo.currentDate = inform.format(new Date(), 'yyyy-MM-dd');
                $scope.lowQualityInfo.typeCode = 'new-06';
                $scope.lowQualityInfo.typeValue = '缺陷逃逸';
                $scope.lowQualityInfo.gradeCode = '0001';
                $scope.lowQualityInfo.gradeValue = 'A';
                $scope.lowQualityInfo.influence = '产生线上bug';
                $scope.lowQualityInfo.corrective = '四要素分析，按照要素完成执行';
                $scope.personLiableList = getPersonLiableList(item.approvalId);
                //摘要(跟踪单编号 + 责任人对应四要素内容)
                $scope.lowQualityInfo.summary = item.approvalNum;
                $('#lowQuality').click();
            };

            /**
             * 获取责任人
             */
            function getPersonLiableList(approvalId) {
                var personLiableList = [];
                var urlData = {
                    approvalId: approvalId,
                };
                trackingSheetService.getData(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            //项目详情
                            var projectInfo = data.data.list[0];
                            //四要素及信息
                            var immediateDisposalPerson = {
                                person: projectInfo.immediateDisposalPerson,
                                info: projectInfo.immediateDisposalPlan,
                            };
                            var technicalProposalPerson = {
                                person: projectInfo.technicalProposalPerson,
                                info: projectInfo.technicalProposal,
                            };
                            var testQualityPerson = {
                                person: projectInfo.testQualityPerson,
                                info: projectInfo.testQuality,
                            };
                            var maintenanceSecurityPlanPerson = {
                                person: projectInfo.maintenanceSecurityPlanPerson,
                                info: projectInfo.maintenanceSecurityPlan,
                            };
                            var leakPerson = { person: projectInfo.leakPerson, info: projectInfo.leakAnalysis };
                            if (immediateDisposalPerson.person !== '') {
                                immediateDisposalPerson.label = immediateDisposalPerson.person + '-立即处置';
                                personLiableList.push(immediateDisposalPerson);
                            }
                            if (technicalProposalPerson.person !== '') {
                                technicalProposalPerson.label = technicalProposalPerson.person + '-长期解决';
                                personLiableList.push(technicalProposalPerson);
                            }
                            if (testQualityPerson.person !== '') {
                                testQualityPerson.label = testQualityPerson.person + '-测试';
                                personLiableList.push(testQualityPerson);
                            }
                            if (maintenanceSecurityPlanPerson.person !== '') {
                                maintenanceSecurityPlanPerson.label = maintenanceSecurityPlanPerson.person + '-运维';
                                personLiableList.push(maintenanceSecurityPlanPerson);
                            }
                            if (leakPerson.person !== '') {
                                leakPerson.label = leakPerson.person + '-漏检';
                                personLiableList.push(leakPerson);
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
                return personLiableList;
            }
            $scope.getSummary = function (data) {
                if (data.info === null) {
                    data.info = '';
                }
                $scope.lowQualityInfo.summary = $scope.approvalNum + '\n' + data.info;
            };
            /**
             * 转低级质量
             */
            $scope.toLowQualityAction = function () {
                //责任人集合
                var detailedList = [];
                detailedList.push({
                    personLiable: $scope.employeesIdMap[$scope.lowQualityInfo.personLiable.person],
                    fine: 0,
                });
                var urlData = {
                    summary: $scope.lowQualityInfo.summary,
                    typeCode: $scope.lowQualityInfo.typeCode,
                    date: $scope.lowQualityInfo.currentDate,
                    influence: $scope.lowQualityInfo.influence,
                    corrective: $scope.lowQualityInfo.corrective,
                    gradeCode: $scope.lowQualityInfo.gradeCode,
                    fileList: detailedList,
                };
                //新增低级质量问题
                lowQualityService.addLowQualityInfo(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            inform.common('转低级质量成功');
                            $('#lowQualityCancel').click();
                            //清空缓存
                            cleanBugInfo();
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            /*
             * 禅道项目名称改变时
             * */
            $scope.$watch('bugInfo.proProjectName', function (newValue, oldValue) {
                if (newValue) {
                    //通过项目id获取所属产品
                    var urlData = {
                        project: Number($scope.bugInfo.proProjectId),
                    };
                    trackingSheetService.getProductByProjectId(urlData).then(
                        function (data) {
                            if (data.code === AgreeConstant.code) {
                                $scope.productList = data.data;
                                // 如果项目只有一个产品，默认选择此产品
                                if ($scope.productList.length === 1) {
                                    $scope.bugInfo.product = $scope.productList[0].id;
                                }
                            } else {
                                inform.common(data.message);
                            }
                        },
                        function () {
                            inform.common(Trans('tip.requestError'));
                        }
                    );
                }
            });
            /*
             * 关联禅道Bug时查询bug信息
             * */
            $scope.getBugInfo = function () {
                //通过bugId获取bug信息
                var urlData = {
                    id: Number($scope.bugInfo.id),
                };
                trackingSheetService.getBugDetailByBugId(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.bugInfo.assignedTo0 = data.data.assignedTo;
                            $scope.bugInfo.type0 = data.data.type;
                            $scope.bugInfo.title0 = data.data.title.substring(0, 180);
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            /*
             * 转bug
             * */
            $scope.toBugAction = function () {
                //通过bugId获取bug信息
                var urlData = {
                    trackingSheetNum: trackingSheetNum,
                    parentId: Number($scope.bugInfo.parentId),
                    project: Number($scope.bugInfo.proProjectId),
                    product: Number($scope.bugInfo.product),
                    assignedTo: $scope.bugInfo.assignedTo,
                    type: $scope.bugInfo.type,
                    title: $scope.bugInfo.title,
                    severity: Number($scope.bugInfo.severity),
                    pri: Number($scope.bugInfo.pri),
                    steps: $scope.bugInfo.steps,
                };
                if (Number($scope.way) === 1) {
                    urlData = {
                        trackingSheetNum: trackingSheetNum,
                        id: $scope.bugInfo.id,
                    };
                }
                trackingSheetService.addBugInfo(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            $('#bugCancel').click();
                            inform.common('转bug成功');
                            //清空缓存
                            cleanBugInfo();
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            /*
             * 查看bug信息详情
             * */
            $scope.getZentaoBugDetail = function (id) {
                //通过bugId获取bug信息
                var urlData = {
                    id: Number(id),
                };
                trackingSheetService.getZentaoBugDetail(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.bugDetail = data.data;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };

            //获取项目与确认人对应关系
            function getConfirmPerson() {
                $scope.confirmPersonProjectList = [];
                trackingSheetService.getConfirmPersonProjectList().then(function (data) {
                    $scope.confirmPersonProjectList = angular.fromJson(data.data);
                    angular.forEach($scope.confirmPersonProjectList, function (item) {
                        if (null == item.projectId) {
                            item.projectId = '';
                        }
                        var participants = item.projectId.slice(0, item.projectId.length);
                        item.projects = [];
                        item.projects = participants.split(',');
                    });
                });
            }

            //保存项目与确认人对应关系
            function saveConfirmPerson() {
                //校验
                for (var item = 0; item < $scope.confirmPersonProjectList.length; item++) {
                    if (
                        $scope.confirmPersonProjectList[item].confirmPersonId == null ||
                        $scope.confirmPersonProjectList[item].projects == null
                    ) {
                        layer.confirm(
                            '请选择确认人并关联项目！',
                            {
                                btn: ['确定'],
                            },
                            function (result) {
                                layer.close(result);
                            }
                        );
                        return;
                    }
                    $scope.confirmPersonProjectList[item].projectId =
                        $scope.confirmPersonProjectList[item].projects.join(',');
                }
                //保存数据
                trackingSheetService.saveConfirmPerson($scope.confirmPersonProjectList).then(
                    function (data) {
                        inform.common(data.message);
                        if (data.code === AgreeConstant.code) {
                            $('#confirmPerson_modal').modal('hide');
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            //新增一行确认人
            function addConfirmPerson() {
                //新增一行数据
                var item = {
                    confirmPersonId: '',
                    projectId: '',
                    projects: '',
                };
                $scope.confirmPersonProjectList.push(item);
                var div = document.getElementById('addConfirmPerson');
                div.scrollTop = div.scrollHeight;
            }
            /*
             * 删除一行确认人
             * */
            function deleteConfirmPerson(index) {
                if (index >= 0) {
                    $scope.confirmPersonProjectList.splice(index, 1);
                }
            }
            //下载任务详情信息表
            $scope.downloadTaskDetail = function () {
                inform.modalInstance('确定要下载任务详情信息表吗？').result.then(function () {
                    setSelectTime();
                    inform.downLoadFile(
                        'trackingSheet/downloadTaskDetail',
                        $scope.formRefer,
                        '任务详情表' + inform.format(new Date(), 'yyyy-MM-dd') + '.xlsx'
                    );
                });
            };

            $scope.changeReopen = function () {
                $scope.reopenFlag = true;
            };

            function updateReopen(id) {
                var urlData = {
                    approvalNum: id,
                    reopenReason: $scope.formRefer.changedReopen,
                };
                trackingSheetService.changeData(urlData).then(
                    function (message) {
                        if (message === '修改成功') {
                            inform.common(message);
                        } else {
                            inform.common('修改失败');
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        },
    ]);
})();

(function() {
    app.controller("lowQualityAddController", ['lowQualityService', '$rootScope', 'comService', '$scope', '$state', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function(lowQualityService, $rootScope, comService, $scope, $state, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.flag = $stateParams.flag;
            $scope.status = 'null';
            initPages();
            //新增时明细列表
            $scope.detailedList = [];
            //初始化当前时间
            $scope.datepicker = {
                currentDate: new Date()
            };
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            function initPages() {
                //低级错误类型
                $scope.lowQualityTypeList = [];
                comService.getParamList('LOW_QUALITY_TYPE', 'LOW_QUALITY_TYPE').then(function(data) {
                    $scope.lowQualityTypeList = data.data;
                });
                //低级错误等级
                $scope.lowQualityGradeList = [];
                comService.getParamList('LOW_QUALITY_GRADE', 'LOW_QUALITY_GRADE').then(function(data) {
                    $scope.lowQualityGradeList = data.data;
                });
                //获取员工信息
                $scope.employeesList = [];
                comService.getEmployeesByOrgId('').then(function(data) {
                    $scope.employeeList = data.data;
                });
                setValue();
            }

            /**
             * 若为更新 则赋值
             */
            function setValue() {
                if ($stateParams.item==null) {
                    $scope.signName = "新增低级质量问题";
                    return;
                }
                $scope.signName = "修改低级质量问题";
                if($scope.flag === 'verify'){
                    $scope.signName = "低级质量问题详情";
                }
                var urlData = {
                    'id': $stateParams.item
                };
                lowQualityService.getLowQualityInfo(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.changeParam = data.data.list[0];
                        //获取责任人、处罚金额明细
                        angular.forEach($scope.changeParam.fileList, function(m, index) {
                            //获取处罚金额明细
                            $scope.detailedList.push({
                                'personLiable': m.personLiable, //责任人
                                'fine': m.fine //处罚金额
                            });
                        });
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 保存信息
             */
            $scope.saveInfo = function() {
                if($scope.status==='verify'){
                    return;
                }
                //问题责任人明细 非空、重复校验
                if ($scope.detailedList.length===0) {
                    inform.common("问题责任人列表不可为空，请添加至少一条信息");
                    return;
                }
                var list = [];
                $scope.flagList = true;
                //循环明细，在同一个项目中，每人只能被处罚一次
                angular.forEach($scope.detailedList, function(one) {
                    var mains = one.personLiable;
                    //查看list中存不存在责任人
                    var num = list.indexOf(mains);
                    if (num > -1) {
                        $scope.personCode = mains;
                        $scope.flagList = false;
                        return;
                    }
                    list.push(mains);
                });
                //如果存在重复信息，进行提示
                if ($scope.flagList===false) {
                    var personName = '';
                    angular.forEach($scope.employeeList, function(one, index) {
                        if (one.employeeNo===$scope.personCode) {
                            personName = one.realName;
                        }
                    });
                    inform.common("问题责任人" + personName + "信息重复，请重新填写。");
                    return;
                }
                var urlData = {
                    'summary': $scope.changeParam.summary,
                    'typeCode': $scope.changeParam.typeCode,
                    'date': inform.format($scope.changeParam.date, 'yyyy-MM-dd'),
                    'influence': $scope.changeParam.influence,
                    'corrective': $scope.changeParam.corrective,
                    'gradeCode': $scope.changeParam.gradeCode,
                    'remark': $scope.changeParam.remark,
                    'fileList': $scope.detailedList
                };
                //新增
                if ($stateParams.item==null) {
                    addInfo(urlData);
                } else {
                    //修改
                    urlData.id = $scope.changeParam.id;
                    upInfo(urlData)
                }
            };
            /**
             * 新增信息
             */
            function addInfo(urlData) {
                lowQualityService.addLowQualityInfo(urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        inform.common(data.message);
                        $scope.goback();
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 新增一个责任人明细
             */
            $scope.addNewBind = function() {
                //责任人明细
                var detail = {
                    'personLiable': '',
                    'fine': ''
                };
                $scope.detailedList.push(detail);
            };
            //取消一行
            $scope.deleteNewBind = function(index) {
                $scope.detailedList.splice(index, 1);
            };
            /**
             * 修改信息
             * @param urlData
             */
            function upInfo(urlData) {
                lowQualityService.updateLowQualityInfo(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        layer.confirm(data.message, {
                            title: false,
                            btn: ['确定']
                        }, function(result) {
                            layer.close(result);
                            if($scope.flag==='verify'){
                                $scope.updateLowQualityStatus('1');
                            }else {
                                $scope.goback();
                            }
                        });
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 返回
             */
            $scope.goback = function() {
                if($scope.flag==='verify'){
                    $state.go("app.office.lowQualityVerify", {
                       flag:$scope.flag
                    });
                }else {
                    $state.go("app.office.lowQualityManagement", null);
                }

            };
            /**
             * 新增中的时间
             */
            $scope.openDate = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedEnd = true;
            };

            /**
             * 审核通过
             * @param urlData
             */
             $scope.updateLowQualityStatus = function(status) {
                $scope.status = 'verify';
                var urlData = {
                    'id': $scope.changeParam.id,
                    'status': status
                };
                lowQualityService.updateLowQualityStatus(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        layer.confirm(data.message, {
                            title: false,
                            btn: ['确定']
                        }, function(result) {
                            layer.close(result);
                            $scope.goback();
                        });
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }
    ]);
})();
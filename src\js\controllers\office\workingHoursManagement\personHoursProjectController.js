/*
 * @Author: liyunmeng
 * @Date:   2020-08-03
 */
(function () {
    app.controller("personHoursProjectController", ['comService', '$rootScope', '$scope', 'personHoursProjectService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http','$stateParams',
        function (comService, $rootScope, $scope, personHoursProjectService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http,$stateParams) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.getData = getData;
            //获取初始信息
            getData();

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 获取产品线
             */
            function getData(){
                $scope.viewDetails=[];
                //标题
                $scope.title = [];
                comService.queryEffectiveParam('TASK','TYPELIST').then(function(data) {
                    if (data.data) {
                        angular.forEach(data.data,function(one,n){
                            $scope.title.push(one.paramValue);
                        });
                        $scope.title.push('其他');
                        $scope.title.push('未在任务下消耗工时');
                    }
                    getviewDetails();
                });


            }
            function getviewDetails(){
                var urlData = {
                    'account': $stateParams.account,
                    'startTime': inform.format($stateParams.startTime, 'yyyy-MM-dd'),//开始时间
                    'endTime': inform.format($stateParams.endTime, 'yyyy-MM-dd'),//结束时间
                };
                personHoursProjectService.getviewDetails(urlData).then(function(data) {
                    var jsonData = data.data;
                    $scope.itemList = jsonData.dataList;
                    var tmpArray = [];
                    var tmpTotalArray1 = [];//纵列合计
                    angular.forEach(jsonData.dataList,function(time,i) {
                        var tmpTotal1 = 0;
                        var arrayTmp =[];
                        arrayTmp.push(time.person);
                        angular.forEach($scope.title,function(key,n){
                            var vals = 0;
                            var s = 0;
                            if (time[key]) {
                                vals = time[key];
                                s = vals.toFixed(1);
                            }
                            //四舍五入 保留整数
                            tmpTotal1 = tmpTotal1 + parseFloat(vals.toFixed(1));
                            arrayTmp.push(s);
                        });
                        tmpTotalArray1.push(tmpTotal1.toFixed(1));
                        tmpArray.push(arrayTmp);
                    });
                    //统计总和
                    $scope.score = tmpArray;
                    for(var i=0;i<$scope.score.length;i++) {
                        $scope.score[i][0] = $scope.itemList[i].project;
                    }
                    $scope.rowsTotal = tmpTotalArray1;//纵列合计
                });
            }
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (280);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight);
                $("#divTBDis1").height(divHeight);
                $("#subDivTBDis1").height(divHeight);
            }
            /**
             * 下载汇总信息
             */
            $scope.toExcel = function () {
                var urlData = {
                    'account': $stateParams.account,
                    'startTime': inform.format($stateParams.startTime, 'yyyy-MM-dd'),//开始时间
                    'endTime': inform.format($stateParams.endTime, 'yyyy-MM-dd'),//结束时间
                };
                inform.modalInstance("确定要下载吗?").result.then(function () {
                    inform.downLoadFile('personHours/viewDetailsToExcel',urlData,'项目投入详情工时统计.xlsx');
                });
            }

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();
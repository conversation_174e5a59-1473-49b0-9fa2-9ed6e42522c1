/**
 * 用于实现textarea自适应的指令
 * @example <textarea auto-resize></textarea>
 * 同时需要给textarea设置样式：'resize: none; overflow: hidden;'
 */
(function () {
  'use strict';
  angular.module('app').directive('autoResize', function ($timeout) {
    return {
      restrict: 'A',
      link: function (scope, element) {
        var updateHeight = function () {
          this.style.height = 'auto';
          this.style.height = this.scrollHeight + 'px';
        };

        element.on('input', function () {
          updateHeight.call(this);
        });

        // 初始化时调整高度,这里如果直接操作dom，会导致ng-model无法更新
        $timeout(function () {
          element.trigger('input');
        }, 0);
      },
    };
  });
})();

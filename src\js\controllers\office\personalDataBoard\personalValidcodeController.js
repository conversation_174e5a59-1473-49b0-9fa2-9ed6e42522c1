/*
 * @Description: 
 * @Version: 1.0
 * @Autor: yap
 * @Date: 2023-11-22 15:59:16
 * @LastEditors: yap
 * @LastEditTime: 2023-12-07 09:57:25
 */
app.controller('personalValidcodeController', ['ValidCodeService', 'LocalCache', '$scope', '$stateParams', 'inform', '$window', '$timeout', function (ValidCodeService, LocalCache, $scope, $stateParams, inform, $window, $timeout) {
    $scope.contactMessage = '有效代码查询主页面';
    $scope.formRefer = {};
    $scope.showFlag = false;
    var hcommitnum, hcommitrate, haddrow, hvalidcode, hvalidcodeperh, hbugperk, hcommitmsgstandardrate;
    $scope.timeSelect=['上月', '上年', '本月', '本年'];
    $scope.initTime = function (m){
        initTime(m)
    };
    initTime('本月');
    //初始化信息
    initData();


    /**
    * 时间段选择
    */
    function initTime(flag) {
        // 快捷键选项
        $scope.butFlag = flag;
        let date = new Date();
        let y = date.getFullYear(); //当前年份
        let lastMonth = date.getMonth() - 1; //上月
        let lastYear = y -1 ; //上年
        if ('上月' === $scope.butFlag) {  
            $scope.formRefer.startTime = inform.format(new Date(y, lastMonth), 'yyyyMM');
            $scope.formRefer.endTime = inform.format(new Date(y, lastMonth), 'yyyyMM');
        }
        if ('上年' === $scope.butFlag) {
            $scope.formRefer.startTime = inform.format(new Date(lastYear, 0), 'yyyy01');
            $scope.formRefer.endTime = inform.format(new Date(lastYear, 11), 'yyyy12');
        }
        if ('本年' === $scope.butFlag) {
            $scope.formRefer.startTime = inform.format(date, 'yyyy01');
            $scope.formRefer.endTime = inform.format(date, 'yyyyMM');
        }
        if ('本月' === $scope.butFlag) {
            $scope.formRefer.startTime = inform.format(date, 'yyyyMM');
            $scope.formRefer.endTime = inform.format(date, 'yyyyMM');
        }
    }

    function initData() {
        //获取当前登录者的empId
        $scope.sessionEmpId = LocalCache.getSession('employeeId');
        $scope.formRefer.empId = $stateParams.empId == null ? $scope.sessionEmpId : $stateParams.empId;
        //获取当前登录者的登录名
        $scope.formRefer.loginName = LocalCache.getSession('loginName');
        var person = LocalCache.getObject('personDataBoardEmployee');
        if(person.loginName){
            $scope.formRefer.loginName = person.loginName;
        }
    }


    $scope.readyinit =function () {
        $scope.formRefer.startTime = inform.format(new Date(),"yyyyMM");
        $scope.formRefer.endTime = $scope.formRefer.startTime;
        hcommitnum = echarts.init(document.getElementById('commitnum'));
        hcommitrate = echarts.init(document.getElementById('commitrate'));
        haddrow = echarts.init(document.getElementById('addrow'));
        hvalidcode = echarts.init(document.getElementById('validadd'));
        hvalidcodeperh = echarts.init(document.getElementById('validper'));
        hbugperk = echarts.init(document.getElementById('bugperk'));
        hcommitmsgstandardrate = echarts.init(document.getElementById('commitmsgstandardrate'))

        $scope.getReview();
        $scope.showtabledetail([]);

    };

    setTimeout($scope.readyinit, 500);

    // 监听窗口大小变化
    angular.element($window).bind('resize', function () {
        // 在窗口大小变化时重新绘制图表
        hcommitnum.resize();
        hcommitrate.resize();
        haddrow.resize();
        hvalidcode.resize();
        hvalidcodeperh.resize();
        hbugperk.resize();
        hcommitmsgstandardrate.resize();
    });

    $scope.getPermission = function () {
        var roleList = JSON.parse(LocalCache.getSession('roleList'));
        angular.forEach(roleList, function (item) {
            if (item.roleName === '系研领导' || item.roleName === '部门领导') {
                $scope.showFlag = true;
            }
        });
    };

    $scope.getPermission();

    $scope.showtabledetail = function (data) {
        $('#commitDetail').bootstrapTable('destroy');
        $('#commitDetail').bootstrapTable({
            classes: "table table-bordered table-hover table-striped",
            data: data,
            striped: true, //是否显示行间隔色
            // search : true,//开启搜索文本框
            // showRefresh : true,//刷新按钮
            // showColumns:false,
            pageNumber: 1, //初始化加载第一页
            pagination: true,//是否分页
            sidePagination: 'client',//server:服务器端分页|client：前端分页
            pageSize: 10,//单页记录数
            pageList: [10, 20, 50],//可选择单页记录数
            columns: [{
                title: '提交日期',
                field: 'commitdate',
                sortable: true
            }, {
                title: '仓库名称',
                field: 'reponame',
                sortable: true
            }, {
                title: '仓库类型',
                field: 'repotype',
                sortable: true
            }, {
                title: '原始新增行数',
                field: 'addrow',
                sortable: true
            }, {
                title: '去重新增行数',
                field: 'addvalid',
                sortable: true
            }, {
                title: '有效新增行数',
                field: 'addlimit',
                sortable: true
            }, {
                title: '删除代码行数',
                field: 'delrow',
                sortable: true
            }, {
                title: 'Commit Message',
                field: 'msg',
                sortable: false
            }, {
                title: '是否合规',
                field: 'isstandard',
                sortable: false,
                formatter: isStandard
            }, {
                title: '不合规原因',
                field: 'unstandardreason',
                sortable: false
            }],
            // 使用固定表头功能
            fixedColumns: true,
            fixedNumber: 1, // 设置固定的列数，这里假设只固定第一列
        });

    }; 

    function isStandard(value, row, index) {
        var result = '是';
        if (value !== '' && value === '0') {
            result = '否';
        }
        return result;
    }


    $scope.showtablemonth = function (data) {
        $('#commitMonth').bootstrapTable('destroy');
        $('#commitMonth').bootstrapTable({
            classes: "table table-bordered table-hover table-striped",
            data: data,
            striped: true, //是否显示行间隔色
            // search : true,//开启搜索文本框
            // showRefresh : true,//刷新按钮
            // showColumns:false,
            pageNumber: 1, //初始化加载第一页
            pagination: true,//是否分页
            sidePagination: 'client',//server:服务器端分页|client：前端分页
            pageSize: 10,//单页记录数
            pageList: [10, 20, 50],//可选择单页记录数
            columns: [{
                title: '仓库名称',
                field: 'reponame',
                sortable: true
            }, {
                title: '仓库类型',
                field: 'repotype',
                sortable: true
            }, {
                title: '提交次数',
                field: 'commitcount',
                sortable: true
            }, {
                title: '新增代码行数',
                field: 'addrow',
                sortable: true
            }, {
                title: '有效新增代码行数',
                field: 'addvalid',
                sortable: true
            }, {
                title: '删除代码行数',
                field: 'delrow',
                sortable: true
            }, {
                title: '有效删除代码行数',
                field: 'delvalid',
                sortable: true
            }, {
                title: '合规率',
                field: 'commitMsgStandardRate',
                sortable: true,
                formatter : getCommitMsgStandardRate
            }]
        });

    };

    function getCommitMsgStandardRate(value, row, index){
        return (value * 100).toFixed(1) + "%";
    }


    $scope.showMonDetailClick = function () {
        $scope.commitData = [];
        var urlData = {
            'user': $scope.formRefer.loginName, //解决人id
            'start': $scope.formRefer.startTime,
            'end': $scope.formRefer.endTime,
        };
        ValidCodeService.validCodeDetail(urlData).then(function (data) {
            if (data.code === '0000') {
                // 更新表格内容
                $scope.showtablemonth(data.data);
            }
        }, function (error) {
            inform.common(Trans("tip.requestError"));
        });
    };

    $scope.showDetailClick = function () {
        var urlData = {
            'user': $scope.formRefer.loginName, //解决人id
            'start': $scope.formRefer.startTime,
            'end': $scope.formRefer.endTime,
        };
        ValidCodeService.codeCommitDetail(urlData).then(function (data) {
            if (data.code === '0000') {
                // 更新表格内容
                $scope.showtabledetail(data.data);
            }
        }, function (error) {
            inform.common(Trans("tip.requestError"));
        });
    };

    $scope.showbar = function (handle_echart, title, key1, key2, var1, var2) {
        // 使用echarts库绘制柱状图

        var option = {
            title: {
                text: title,
                left: 'center',
                top: 20
            },
            legend: {
                data: [key1, key2],
                // top: '10%'
            },
            xAxis: {
                type: 'category',
                data: [key1, key2]
            },
            yAxis: {
                type: 'value'
            },
            series: [{
                data: [
                    { value: var1, itemStyle: { color: 'blue' }, label: { show: true, position: 'top', formatter: '{c}' } },
                    { value: var2, itemStyle: { color: 'green' }, label: { show: true, position: 'top', formatter: '{c}' } }
                ],
                type: 'bar'
            }]
        };
        handle_echart.setOption(option);
    };




    $scope.getReview = function () {
        if (!$scope.formRefer.startTime) {
            inform.common("请选择开始时间");
            return;
        }
        if (!$scope.formRefer.endTime) {
            inform.common("请选择结束时间");
            return;
        }

        $scope.reviewData = [];
        var urlData = {
            'user': $scope.formRefer.loginName, //解决人id
            'start': $scope.formRefer.startTime,
            'end': $scope.formRefer.endTime,
        };
        ValidCodeService.getValidCodeOverview(urlData).then(function (data) {
            if (data.code === '0000') {
                $scope.showbar(hcommitnum, '代码提交次数', '个人数据', '同岗位平均数', data.data.commitCount, data.data.avgCommitCount);
                $scope.showbar(hcommitrate, '代码提交频率（次/天）', '个人数据', '同岗位平均数', data.data.commitRate, data.data.avgCommitRate);
                $scope.showbar(haddrow, '新增代码数', '个人数据', '同岗位平均数', data.data.addRow, data.data.avgaddRow);
                $scope.showbar(hvalidcode, '新增有效代码数', '个人数据', '同岗位平均数', data.data.validCode, data.data.avgValidCode);
                $scope.showbar(hvalidcodeperh, '每小时有效代码数', '个人数据', '同岗位平均数', data.data.validCodePerH, data.data.avgValidCodePerH);
                $scope.showbar(hbugperk, '千行代码BUG数', '个人数据', '同岗位平均数', data.data.bugsPerKLOC, data.data.avgBugsPerKLOC);
                $scope.showbar(hcommitmsgstandardrate, '代码提交合规率', '个人数据', '同岗位平均数', data.data.commitMsgStandardRate, data.data.avgCommitMsgStandardRate);
                // 更新表格内容
                $scope.reviewData = [
                    { 'q1': '代码提交次数', 'q2': data.data.commitCount, 'q3': data.data.avgCommitCount },
                    { 'q1': '代码提交频率（次/天）', 'q2': data.data.commitRate, 'q3': data.data.avgCommitRate },
                    { 'q1': '新增代码（行）', 'q2': data.data.addRow, 'q3': data.data.avgaddRow },
                    { 'q1': '新增有效代码（行）', 'q2': data.data.validCode, 'q3': data.data.avgValidCode },
                    { 'q1': '每小时有效代码数', 'q2': data.data.validCodePerH, 'q3': data.data.avgValidCodePerH },
                    { 'q1': '千行代码BUG数', 'q2': data.data.bugsPerKLOC, 'q3': data.data.avgBugsPerKLOC },
                    { 'q1': '工时数据（小时）', 'q2': data.data.workingHour, 'q3': data.data.avgWorkingHour },
                    { 'q1': '开发工时数据（小时）', 'q2': data.data.rdHour, 'q3': data.data.avgRdHour },
                    { 'q1': '有效代码数据偏差', 'q2': (data.data.validAddDiff * 100).toFixed(1) + '%', 'q3': (data.data.avgValidAddDiff * 100).toFixed(1) + '%' },
                    { 'q1': '合规率', 'q2': (data.data.commitMsgStandardRate * 100).toFixed(1) + '%', 'q3': (data.data.avgCommitMsgStandardRate * 100).toFixed(1) + '%' }
                ];
            }
        }, function (error) {
            inform.common(Trans("tip.requestError"));
        });
    };

    $scope.reset = function () {
        initTime('本月');
    };


    $scope.openDateStart = function ($event) {
        $event.preventDefault();
        $event.stopPropagation();
        //仅查询中的开始时间显示控件内容
        $scope.formRefer.openedStart = true;
        $scope.formRefer.openedEnd = false;
    };
    /**
     * 查询条件中的结束时间
     */
    $scope.openDateEnd = function ($event) {
        $event.preventDefault();
        $event.stopPropagation();
        $scope.formRefer.openedStart = false;
        $scope.formRefer.openedEnd = true;
    };


}]);
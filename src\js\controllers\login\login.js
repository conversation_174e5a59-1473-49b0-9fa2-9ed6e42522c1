(function() {
  'use strict';

  angular.module('app').controller("login", ['$rootScope', '$scope', '$http', '$state', 'inform', 'LocalCache', 'LoginService', 'Trans','AgreeConstant','Md5',
    function($rootScope, $scope, $http, $state, inform, LocalCache, LoginService, Trans,AgreeConstant,Md5) {
      $scope.map ={};
      //如果cookie中有记住密码的信息，则选中记住密码的框
      var flag =getCookie('flag');
      if('true'===flag){
          $scope.rememberMe = true;
      }
      //判断密码是否被修改
      $scope.change = false;
      $scope.loginSubmit = loginSubmit;
      $scope.getCode = getCode;
      $scope.loginInfo = AgreeConstant.login;
      //设置登录信息
      setLogin();
      //$scope.getCode();
      var wrapper = document.getElementById('wrapper-login');
      // 禁止从外面复制东西黏贴到页面内
      wrapper.onkeydown = function(){
        if (event.ctrlKey && window.event.keyCode==86){
          return false;
        }
      };

      function loginSubmit() {
        if (!$scope.map.username) {
          inform.common(Trans('login.nameNull'));
          return false;
        }
        if (!$scope.map.password) {
          inform.common(Trans('login.psdNull'));
          return false;
        }
        submitData();
//        if(!validateCode()){
//          return false;
//        }
      }

      // 验证码校验
      function validateCode() {
        if (!$scope.map.checkCode) {
          inform.common(Trans('login.valNull'));
          return false;
        }
        // 验证coed
        LoginService.checkValidateCode($scope.map.checkCode,$scope.checkRes)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode && data.result=="true") {
              // 验证通过
              submitData();
              return true;
            } else {
              inform.common(Trans('login.valError'));
              return false;
            }
          }, function() {
            inform.common(Trans("login.requestError"));
          });
      }

        //设置session
        function setSession(data) {
            LocalCache.setSession('currentUserName', $scope.map.username);
            //wcp加密密码
            LocalCache.setSession('OAPassword', $scope.map.rsaPassword);
            //所属部门的上级部门
            LocalCache.setSession('superDepartment', $scope.depInfo.superDepartment);
            //所属部门
            LocalCache.setSession('department', $scope.depInfo.department);
            //所属产品线
            LocalCache.setSession('productLine', $scope.depInfo.productLine);
            //所属部门中文名
            LocalCache.setSession('departmentName', $scope.depInfo.departmentName);
            //考核角色
            LocalCache.setSession('kpiRoleCode', $scope.depInfo.kpiRoleCode);
            //员工id
            LocalCache.setSession('employeeId', $scope.depInfo.employeeId);
            //真实姓名
            LocalCache.setSession('employeeName', $scope.depInfo.employeeName);
            LocalCache.setSession('userId', data.userId);
            //登录姓名
            LocalCache.setSession('loginName', $scope.depInfo.loginName);
            //OA用户ID
            LocalCache.setSession('oaId', $scope.depInfo.oaId);
            //常用联系方式
            LocalCache.setSession('commonCall', $scope.depInfo.commonCall);
            // LocalCache.setObject('permissionJson', data.result.permissionJson);
            // LocalCache.setObject('permissionMap', data.result.permissionMap);
            if (data.roles && data.roles.length !== 0) {
                LocalCache.setSession('roleId', data.roles[0].roleId);
            }
            if (data.roles && data.roles.length !== 0) {
                LocalCache.setSession('roleList', JSON.stringify(data.roles));
            }
        }

        //设置cookie，进去系统
        function setCookieAndGoIndexBench() {
            //判断是否记住密码
            if ($scope.rememberMe) {
                //记住密码则将账户信息存入cookis
                setCookie($scope.map.username, $scope.map.password);
            } else {
                //不记住密码，则删除存好的cookie
                delCookie();
            }
            $state.go("app.index_bench");
        }

        //调用原本登录接口
        function getUserTokenByUserInfo(loginInfo) {
            LoginService.getUserTokenByUserInfo(loginInfo)
                .then(function (data) {
                    if (data.code === AgreeConstant.resultCode) {
                        //完善储存信息
                    	getDepByLogName(data.result);
                    } else {
                        getCode(data.message);
                        $scope.map = {};
                    }
                }, function (error) {
                    inform.common(Trans('login.loginError'));
                });
        }
        //完善储存信息：根据登陆用户名 获取登录者部门
        function getDepByLogName(session){
        	LocalCache.setSession('token', session.token);
        	LoginService.getUserByLoginName()
            .then(function (data) {
                if (data.code === AgreeConstant.code) {
                	$scope.depInfo = data.data;
                    //设置session
                    setSession(session);
                    //设置cookie，进去系统
                    setCookieAndGoIndexBench();
                } else {
                    getCode(data.message);
                    $scope.map = {};
                }
            }, function (error) {
                inform.common(Trans('login.loginError'));
            });
        }

        function submitData() {
            //如果不是回填的数据，则认为是明文，否则加密
            if(!$scope.change){
              $scope.map.password =$rootScope.RSAEncrypt($scope.map.password);
            }

            LoginService.getUserTokenByUserInfoByLdap($scope.map)
              .then(function(data) {
                if (data.code === AgreeConstant.code || data.code === '0003') {
                    $scope.map.rsaPassword = data.data.rsaPassword;
                	//完善储存信息
                	getDepByLogName(data.data);
                } else {
                  getCode(data.message);
                  //密码错误保留用户名 不保留输入密码
                  $scope.map.password ="";
                }
              }, function(error) {
                inform.common(Trans('login.loginError'));
              });
      }

      //回填登录信息
      function setLogin(){
        if($scope.rememberMe){
          //从cookie中读取信息
          var un = getCookie('username');
          if(null == un){
              return;
          }
         $scope.map.username = un;
         var psd = getCookie('password');
         if(null ==psd){
             return;
         }
          $scope.map.password = psd;
          //设置判断密码是否被修改的标志
          $scope.change = true;
        }
      }


      //设置setCookie
      function setCookie(username,password){
          //有效期180天
          var expires = 6 * 30 * 24 * 60 * 60 * 1000;
          var date = new Date(+new Date()+expires);
          document.cookie = "username="+ username + ";expires=" + date.toGMTString();
          document.cookie = "password=" + password + ";expires=" + date.toGMTString();
          document.cookie = "flag=" + $scope.rememberMe + ";expires=" + date.toGMTString();
      }

      //删除cookies
      function delCookie() {
          var exp = new Date();
          exp.setTime(exp.getTime() - 1);
          //通过用户名读取cookie
          var cval=getCookie("username");
          var pwd=getCookie("password");
          if(cval!==null)
              document.cookie = "username="+cval + ";expires="+exp.toGMTString();
              document.cookie = "password="+pwd + ";expires="+exp.toGMTString();
              document.cookie = "flag=false" + ";expires="+exp.toGMTString();
      }

      //获取cookie
      function getCookie(name) {
          var arr,reg=new RegExp("(^| )"+ name +"=([^;]*)(;|$)");

          if(arr=document.cookie.match(reg))
              return unescape(arr[2]);
          else
              return null;
      }

      // 获取验证码
      function getCode(msg) {
        LoginService.getValidateCode()
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.checkRes = data.result.result;
              $scope.codeImg = data.result.img;
              if(msg){
                inform.common(msg);
              }
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("login.requestError"));
          });
      }

    }
  ]);
})();

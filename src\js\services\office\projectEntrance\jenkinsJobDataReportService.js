
(function() {
    'use strict';
  app.factory('jenkinsJobDataReportService', jenkinsJobDataReportService);
  jenkinsJobDataReportService.$inject=["HttpService",'$rootScope'];

  function jenkinsJobDataReportService(HttpService,$rootScope){

    var service={
        getJenkinsData:getJenkinsData
    };
    return service;

    /**
     * 获取当前团队的迭代版本集合
     */
    function getJenkinsData(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'jenkinsData/getJenkinsData', urlData);
    }
  
    
  }
})();

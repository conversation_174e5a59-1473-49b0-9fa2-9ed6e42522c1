//# sourceURL=js/services/office/validCode/validCodeManagementService.js
(function () {
    'use strict';
    app.factory('validCodeManagementService', validCodeManagementService);
    validCodeManagementService.$inject = ["HttpService", '$rootScope'];

    function validCodeManagementService(HttpService, $rootScope) {
        var service = {
            getPersonalValidCode:getPersonalValidCode
        };
        return service;
        /**
         * 获取所有人员的有效代码
         */
        function getPersonalValidCode(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'validCodeData/getPersonalValidCode', urlData);
        }
       
    }
})();
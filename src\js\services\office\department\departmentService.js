(function() {
    'use strict';
  app.factory('departmentService', departmentService);
  departmentService.$inject=["HttpService",'$rootScope'];

  function departmentService(HttpService,$rootScope){
    
	var service={
			getOrgChildren:getOrgChildren,
			upInfo:upInfo
	};
    return service;

    /**
     * 获取所有系研下的部门信息
     */
    function getOrgChildren(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'department/getOrgChildren', urlData);
    }
    /**
     * 更新部门信息
     */
    function upInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'department/upInfo', urlData);
    }

  }
})();
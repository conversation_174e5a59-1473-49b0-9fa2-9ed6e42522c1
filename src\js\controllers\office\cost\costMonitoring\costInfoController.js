(function () {
    'use strict';
    app.controller("costInfoController", ['$scope','$state','comService','costChartModule', '$rootScope', 'inform', 'Trans', 'AgreeConstant','costMonitoringService','$stateParams','LocalCache', '$modal','$http',
        function ($scope,$state,comService, costChartModule,$rootScope, inform, Trans, AgreeConstant,costMonitoringService,$stateParams,LocalCache, $modal,$http) {
    	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	var status=[];
    	var seclist=[];
    	var clientHeight = document.body.clientHeight;
		var clientWidth = document.body.clientWidth;
		$("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
		$("#buttonStyle").css({"width": 100+"px"});
		var flags = LocalCache.getObject('costInfo_flag');
		if (flags.type!=null){
			//清除缓存
	        LocalCache.setObject('costInfo_flag', {});
	        $scope.type=flags.type;
			$scope.functionType = flags.functionType;
			$scope.projectId = flags.projectId;
		} else {
			$scope.type=1;
			$scope.functionType = $stateParams.functionType;
			$scope.projectId = $stateParams.projectId;
		}
    	if($scope.functionType==='costInfo'){
    		//项目未结算(0-待确认/1-确认/2-已结算)
    		status=['1','2'];
    		seclist=['预算','开发','超出预算'];
    	}else if ($scope.functionType==='costCloseInfo'){
    		//项目已结算(2-已结算)
    		status=['2']
    		seclist=['预算','开发','超出预算'];
    	}else if($scope.functionType==='maintainInfo'){
    		$scope.funtype=true;
    		//项目维护(0-待确认/3-维护成本)
    		status=['2','3']
    		seclist=['开发','维护','总投入'];
    	}
    	//人员级别
    	$scope.getRelatedLevel = getRelatedLevel;
    	$scope.getSum = getSum;
    	if ($scope.type===1){
    		getSum();
    	} else {
    		getRelatedLevel('0')
    	}
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */
    	/**
    	 * 获取人员级别
    	 */
    	function getRelatedLevel(plmLog){
    		var urlData = {
            	'plm': plmLog,
            	'projectId': $scope.projectId,
            	'status': status
            };
    		costMonitoringService.getRelatedLevel(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
					if (plmLog!=='plm'){
						//项目表头
						$scope.levelList = data.data;
						$scope.secLevelList=[];
						angular.forEach($scope.levelList, function (detail, i) {
							for (var j=0;j<seclist.length;j++){
								$scope.secLevelList.push(seclist[j]);
							}
			    		 })
			    		 //合计
						 for (var d=0;d<seclist.length-1;d++){
							 $scope.secLevelList.push(seclist[d]);
						 }
						 if ($scope.funtype!==true){
							 $scope.secLevelList.push('偏差比例');
			    		 }
                    } else {
                    	//PLM表头
                    	$scope.plmLevelList = data.data;
                    	$scope.plmSecLevelList=[];
                    	angular.forEach($scope.plmLevelList, function (detail, i) {
                    		for (var j=0;j<seclist.length;j++){
								$scope.plmSecLevelList.push(seclist[j]);
							}
                    	});
                    	//合计
						for (var b=0;b<seclist.length-1;b++){
						    $scope.plmSecLevelList.push(seclist[b]);
						}
					    if ($scope.funtype!==true){
							$scope.plmSecLevelList.push('偏差比例');
			    		 }
                    }
					//获取人员岗位
					getRelatedTitle(plmLog);
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
    	}
    	/**
    	 * 获取人员岗位
    	 */
    	function getRelatedTitle(plmLog){
    		var urlData = {
            	'plm': plmLog,
            	'projectId': $scope.projectId,
            	'status': status
            };
    		costMonitoringService.getRelatedTitle(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
					if (plmLog!=='plm'){
						//项目行
						$scope.titleList = data.data;
                    } else {
                    	//PLM行
                    	$scope.plmTitleList = data.data;
                    }
					if($scope.functionType==='maintainInfo'){
						//项目维护单元格（再单元格后添加调用费用）
						getMaintainHr(plmLog);
						return;
					}
					//获取单元格（再单元格后添加调用费用）
					getHrDetail(plmLog);
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
    	}
    	/**
    	 * 获取单元格
    	 */
    	function getHrDetail(plmLog){
    		var urlData = {
            	'plm': plmLog,
             	'projectId': $scope.projectId,
            	'status': status
            };
    		costMonitoringService.getHrDetail(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
					if (plmLog!=='plm'){
						//项目行
						$scope.infoList=[];
						packageInfo(data.data,$scope.titleList,$scope.levelList,$scope.infoList,seclist);
						//人力费用
						getAmountDetail(plmLog,$scope.infoList);
						//项目费用
						getProjectFee()
                    } else {
                    	//PLM行
                    	$scope.plmInfoList = [];
                    	packageInfo(data.data,$scope.plmTitleList,$scope.plmLevelList,$scope.plmInfoList,seclist);
                    	//人力费用
                    	getProAllPlmAmountDetail(plmLog,$scope.plmInfoList);
                    }
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
    	}
    	/**
    	 * 获取项目维护单元格
    	 */
    	function getMaintainHr(plmLog){
    		var s = ['3'];
    		var urlData = {
    			'plm': plmLog,
                'projectId': $scope.projectId,
                'status':s
            };
        	costMonitoringService.getMaintainHr(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                	if (plmLog!=='plm'){
						$scope.infoList=[];
						packageInfo(data.data,$scope.titleList,$scope.levelList,$scope.infoList,seclist);
						//人力费用
						getAmountDetail(plmLog,$scope.infoList);
						//项目维护费用
						getFreeMaintain();
                	 } else {
                     	//PLM行
                     	$scope.plmInfoList = [];
                     	packageInfo(data.data,$scope.plmTitleList,$scope.plmLevelList,$scope.plmInfoList,seclist);
                     	//人力费用
                     	getProAllPlmAmountDetail(plmLog,$scope.plmInfoList);
                	 }
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
    	}
    	/**
    	 * 获取PLM人力费用
    	 */
    	function getProAllPlmAmountDetail(plmLog,finList){
    		costMonitoringService.getProAllPlmAmountDetail($scope.projectId).then(function (data) {
    			getAmountReturn(data,plmLog,finList);
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
    	}
    	/**
    	 * 获取人力费用
    	 */
    	function getAmountDetail(plmLog,finList){
    		costMonitoringService.getAmountDetail($scope.projectId,plmLog).then(function (data) {
    			getAmountReturn(data,plmLog,finList);
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
    	}
    	/**
    	 * 获取人力费用回调
    	 */
    	var getAmountReturn = function (data,plmLog,finList){
    		 if (data.code === AgreeConstant.code) {
             	var infoList = data.data
             	if (infoList.length===0){
             		return;
             	}
             	getAmounDetailSum(plmLog,infoList,finList);
             } else {
                 inform.common(data.message);
             }
    	}
    	/**
    	 * 费用求合计
    	 */
    	function getAmounDetailSum(plmLog,infoList,finList){
    		var levels = [];
        	if (plmLog!=='plm'){
				//项目表头
        		levels = $scope.levelList;
            } else {
            	//PLM表头
            	levels = $scope.plmLevelList;
            }
        	var info = {
				title: '成本合计',
				level: []
			};
        	//计算合计
        	var sumAll=[0,0,0];
        	//级别
        	angular.forEach(levels, function (detail, i) {
        		var falg=false;
        		for (var s=0;s<infoList.length;s++){
        			var one = infoList[s];
        			if (one.level===detail){
        				falg = true;
        				//如果是开发
        				if ($scope.funtype!==true){
        					sumAll[0]=(sumAll[0]*1+one.budget*1).toFixed(2);
        					info.level.push(inform.formatMoney(one.budget));
        					sumAll[1]=(sumAll[1]*1+one.develop*1).toFixed(2);
        					info.level.push(inform.formatMoney(one.develop));
        					sumAll[2]=(sumAll[2]*1+one.exceedBudget*1).toFixed(2);
        					info.level.push(inform.formatMoney(one.exceedBudget));
        					continue;
        				}
        				//如果是维护
        				sumAll[0]=(sumAll[0]*1+one.develop*1).toFixed(2);
        				info.level.push(inform.formatMoney(one.develop));
        				sumAll[1]=(sumAll[1]*1+one.maintain*1).toFixed(2);
    					info.level.push(inform.formatMoney(one.maintain));
    					sumAll[2]=(sumAll[2]*1+one.allPutInTo*1).toFixed(2);
    					info.level.push(inform.formatMoney(one.allPutInTo));
        			}
        		}
        		if (falg===false){
        			info.level.push(0);
        			info.level.push(0);
        			info.level.push(0);
        		}
    		 })
    		var sumAllMoney = [];
    		for (var x=0;x<sumAll.length;x++){
    			sumAllMoney[x] = inform.formatMoney(sumAll[x]);
    		}
    		//如果是开发
				if ($scope.funtype!==true){
					//计算行合计
					getLineSum(sumAll,info.level,sumAllMoney);
    		 } else {
    			info.level.push(sumAllMoney[0]);
    			info.level.push(sumAllMoney[1]);
    		 }
        	finList.push(info);
    	}
    	/**
    	 * 组装数据
    	 */
    	function packageInfo (list,titles,levels,finList,secs){
    		//岗位
			angular.forEach(titles, function (title, i) {
				//一个岗位一条记录
				var info = {
					title: title,
					level: []
				};
				//记录行合计
				var sumOne=[0,0,0];
				//级别
				angular.forEach(levels, function (level, i) {
					//状态
					angular.forEach(secs, function (sec, i) {
						//默认无匹配
						var flag=false;
						for(var j=0;j<list.length;j++){
							var one = list[j];
							if(one.title === title){
								if (one.level === level){
									if(one.type===sec){
										info.level.push(inform.removeZero(one.workload));
										sumOne[i] = inform.removeZero(sumOne[i]*1+one.workload*1);
										//如果有匹配的
										flag=true;
										continue;
									} 
								}
							}
						}
						if (flag===false){
							info.level.push("0");
						}
					});
			     });
				if ($scope.funtype!==true){
					getLineSum(sumOne,info.level)
				} else {
					info.level.push(sumOne[0]);
					info.level.push(sumOne[1]);
				}
				finList.push(info);
	    	});
			//记录列合计
    		var sumAll = {
    				title: '人力合计',
    				level: []
    		};
    		if (finList!=null&&finList.length!==0){
    			getCellSum(sumAll,finList)
    		}
    	}
    	/**
    	 * 获取行合计
    	 */
    	function getLineSum (sumOne,info,sumAllMoney){
    		//计算行合计
			for (var i = 0;i<sumOne.length;i++){
				if (i===2){
					//计算偏差比例
					var num = sumOne[0]*1;
					if (num===0){
						num=1;
					}
					//如果预计为0则应该(x-0)/1
					var test = (((sumOne[0]*1-sumOne[1]*1)/num)*100).toFixed(0);
					info.push(test+'%');
				}else{
					if (sumAllMoney!=null){
						info.push(sumAllMoney[i]);
					}else{
						info.push(sumOne[i]);
					}
				}
			}
    	}
    	/**
    	 * 获取列合计
    	 */
    	function getCellSum(sumAll,finList){
    		//每个岗位的
    		for (var i=0;i<finList.length;i++){
    			var one = finList[i];
    			//第几位数据   为维护时不存在百分比
    			var size = one.level.length
    			if ($scope.funtype!==true){
    				size = one.level.length-1;
    			}
    			for (var j=0;j<size;j++){
    				if (sumAll.level[j]==null){
    					sumAll.level[j] = inform.removeZero(one.level[j]*1);
    				}else {
    					sumAll.level[j] = inform.removeZero(sumAll.level[j]*1 + one.level[j]*1);
    				}
    			}
    		}
    		if ($scope.funtype!==true){
    			//重新计算超出预算且不为维护时（维护需要计算总投入可以直接相加）
        		for (var x=0;x<sumAll.level.length;x++){
        			var num = (x+1)%3;
    				if (num==0){
    					if (sumAll.level[x-2]>sumAll.level[x-1]){
    						sumAll.level[x] = 0;
    					}else {
    						sumAll.level[x] = inform.removeZero(sumAll.level[x-1]-sumAll.level[x-2]);
    					}
    					
    				}
        		}
    			//不为维护时才会计算比例
    			var allSum = [];
        		allSum.push(sumAll.level[sumAll.level.length-2]);
        		allSum.push(sumAll.level[sumAll.level.length-1]);
        		allSum.push(0)
        		var sum = [];
        		getLineSum(allSum,sum);
        		sumAll.level.push(sum[2]);
			}
    		finList.push(sumAll);
    	}
    	/**
    	 * 获取项目费用
    	 */
    	function getProjectFee(){
    		var urlData = {
            	'plm': "0",
            	'projectId': $scope.projectId,
            	'status': status
            };
    		costMonitoringService.getProjectFee(urlData).then(returnFunction, function (error) {
                inform.common(Trans("tip.requestError"));
    		});
    	}
    	/**
    	 * 获取维护项目费用
    	 */
    	function getFreeMaintain(){
    		var s = ['3'];
    		var urlData = {
                'projectId': $scope.projectId,
                'status': s
            };
    		costMonitoringService.getFreeMaintain(urlData).then(returnFunction, function (error) {
                inform.common(Trans("tip.requestError"));
            });
    	}
    	/**
    	 * 费用的回调方法
    	 */
    	var returnFunction = function (data) {
            if (data.code === AgreeConstant.code) {
            	$scope.feeList = data.data;
            	var fee ={
            			'feeType':'合计',
            			'feeCost':0,
            			'feePre':0,
            			'feeDiff':0
            	};
            	for (var i=0;i<$scope.feeList.length;i++){
            		var one=$scope.feeList[i];
            		fee.feeCost = (fee.feeCost*1+one.feeCost*1).toFixed(2);
            		fee.feePre = (fee.feePre*1+one.feePre*1).toFixed(2);
            		fee.feeDiff = (fee.feeDiff*1+one.feeDiff*1).toFixed(2);
            		one.feeCost = inform.formatMoney(one.feeCost);
            		one.feePre = inform.formatMoney(one.feePre);
            		one.feeDiff = inform.formatMoney(one.feeDiff);
            	}
            	//不为维护则需计算比例
            	if ($scope.funtype!==true){
            		var test = 0;
					//计算超出比率
					if (fee.feeCost*1>fee.feePre*1){
						var num = fee.feePre*1;
						if (num===0){
							num=1;
						}
						//如果预计为0则应该(x-0)/1
						test = (((fee.feeCost*1-fee.feePre*1)/num)*100).toFixed(0);
					}
					fee.feeDiff =test+'%';
            	} else {
            		fee.feeDiff = inform.formatMoney(fee.feeDiff);
            	}
            	fee.feeCost = inform.formatMoney(fee.feeCost);
				fee.feePre = inform.formatMoney(fee.feePre);
            	$scope.feeList.push(fee);
				//获取PLM表头
            	getRelatedLevel("plm");
            } else {
                inform.common(data.message);
            }
        }
    	/**
    	 * 人力明细
    	 */
    	$scope.goHrDetails = function (){
    		LocalCache.setObject("costInfo_flag",{
    			'type':2,
        		'functionType' : $scope.functionType,
        		'projectId' : $scope.projectId
    		});
    		var s = status;
    		if($scope.functionType==='maintainInfo'){
    			s = ['3'];
			}
    		var item={
    			projectId:$scope.projectId,
    			plmUpgradeId: '0',
    			statusList: s
    		};
    		$state.go('app.office.costHrInputDetail', {detailItem: JSON.stringify(item)});
    	}
    	/**
    	 * 费用明细
    	 */
    	$scope.goFreeDetails = function (){
    		LocalCache.setObject("costInfo_flag",{
    			'type':2,
        		'functionType' : $scope.functionType,
        		'projectId' : $scope.projectId
    		});
    		var s = status;
    		if($scope.functionType==='maintainInfo'){
    			s = ['3'];
			}
    		$state.go("app.office.freeDetails", {functionType:'freeDetails',projectId:$scope.projectId,status:s});
    	}
    	/**
    	 * PLM人力明细
    	 */
    	$scope.goPlmHrDetails = function (){
    		LocalCache.setObject("costInfo_flag",{
    			'type':2,
        		'functionType' : $scope.functionType,
        		'projectId' : $scope.projectId
    		});
    		var s = status;
    		if($scope.functionType==='maintainInfo'){
    			s = ['3'];
			}
    		var item={
        		projectId:$scope.projectId,
        		plmUpgradeId: '1',
        		statusList: s
        	};
        	$state.go('app.office.costHrInputDetail', {detailItem: JSON.stringify(item)});
    	}
    	//------------------------------------汇总---------------------------------------
    	function getSum(){
    		var urlData={
    			'projectId':$scope.projectId,
    			'plm':'0',
    			'status':[$scope.functionType]
    		}
    		costMonitoringService.getSum(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                	$scope.amountList = data.data
                	for (var i=0;i<$scope.amountList.length;i++){
                		var one = $scope.amountList[i];
                		one.budget = inform.formatMoney(one.budget);
                		one.develop = inform.formatMoney(one.develop);
                		one.maintain = inform.formatMoney(one.maintain);
                		one.allPutInTo = inform.formatMoney(one.allPutInTo);
                	}
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
    	}

    	//折线图
    	$scope.initCostChartModule = function(){
    	    var data={
                'projectId':$scope.projectId
            };
            costChartModule.initModule(data,$scope);
    	}

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */
	     /**
          * *************************************************************
          *              方法声明部分                                 结束
          * *************************************************************
          */
         } 
    ]);
})();
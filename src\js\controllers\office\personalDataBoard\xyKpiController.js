(function () {
    app.controller('xyKpiController', [
        '$rootScope',
        'comService',
        '$scope',
        '$state',
        '$timeout',
        '$stateParams',
        '$modal',
        'xyKpiService',
        'inform',
        'Trans',
        'AgreeConstant',
        'LocalCache',
        '$http',
        function (
            $rootScope,
            comService,
            $scope,
            $state,
            $timeout,
            $stateParams,
            $modal,
            xyKpiService,
            inform,
            Trans,
            AgreeConstant,
            LocalCache,
            $http
        ) {
            /**
             *             初始化部分                               开始
             * *************************************************************
             */
            //页面数据
            $scope.formRefer = {};
            //标记数据获取进度
            $scope.flag = 0;
            //季度下拉框数据源
            $scope.remark =
                '半年总结周期一般是上年11月1号到本年6月15号。\n年度总结周期一般是上年11月1号到本年10月31号。';
            $scope.quarterSelect = [
                {
                    value: '8',
                    label: '半年总结',
                },
                {
                    value: '10',
                    label: '年度总结',
                },
                {
                    value: '6',
                    label: '自然年上半年',
                },
                {
                    value: '7',
                    label: '自然年下半年',
                },
                {
                    value: '5',
                    label: '自然年全年',
                },
            ];
            $scope.getData = getData;
            $scope.reset = reset;
            //初始化信息
            initData();

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 初始化
             */
            function initData() {
                //获取当前登录者的empId
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ? $scope.sessionEmpId : $stateParams.empId;

                //初始化查询时间,根据当前月份判断
                var date = new Date();
                var month = date.getMonth() + 1;
                var year = date.getFullYear();
                $scope.formRefer.kpiYear = year;
                if (month >= 7 && month <= 10) {
                    $scope.formRefer.kpiQuarter = '5';
                } else {
                    $scope.formRefer.kpiQuarter = '8';
                }
                getData();
            }

            // 监听选择的内容变化，自动查询
            $scope.$watch(
                'formRefer',
                function (newValue, oldValue) {
                    if (newValue !== oldValue && !angular.equals(newValue, oldValue)) {
                        getData();
                    }
                },
                true
            ); // 添加 true 参数进行深度监听

            function timeout() {
                //表示图表信息全部获取完成
                if (3 === $scope.flag) {
                    setTimeout(eChartForProjectPlanCompleteRate, 500);
                    setTimeout(eChartForPlmFinishRate, 500);
                    setTimeout(eChartForWorkHoursRate, 500);
                }
            }
            /**
             * 获取所有指标关联信息
             */
            function getData(flag) {
                if (3 === $scope.flag) {
                    $scope.flag = 0;
                }
                if ($scope.formRefer.kpiYear == null || $scope.formRefer.kpiYear === '') {
                    inform.common('请选择年度');
                    return;
                }
                if ($scope.formRefer.kpiQuarter == null || $scope.formRefer.kpiQuarter === '') {
                    inform.common('请选择考核周期');
                    return;
                }
                //获取表格数据
                getTableData();
                //获取项目计划完成率
                getProjectPlanCompleteRate();
                //获取PLM需求完成情况
                getPlmFinishRate();
                //获取工时占比
                getWorkHoursRate();
            }

            //获取表格数据
            function getTableData() {
                $scope.tableData = [];
                xyKpiService.getXYKpiTableData($scope.formRefer).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.tableData = data.data;
                            } else {
                                inform.common(Trans('tip.noData'));
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            //获取项目计划完成率
            function getProjectPlanCompleteRate() {
                $scope.projectPlanCompleteRate = [];
                xyKpiService.getProjectPlanCompleteRate($scope.formRefer).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.projectPlanCompleteRate = data.data;
                            } else {
                                inform.common(Trans('tip.noData'));
                            }
                        } else {
                            inform.common(data.message);
                        }
                        $scope.flag++;
                        timeout();
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }

            //获取PLM需求完成情况
            function getPlmFinishRate() {
                $scope.plmFinishRate = [];
                xyKpiService.getPlmFinishRate($scope.formRefer).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.plmFinishRate = data.data;
                            } else {
                                inform.common(Trans('tip.noData'));
                            }
                        } else {
                            inform.common(data.message);
                        }
                        $scope.flag++;
                        timeout();
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            //获取工时占比
            function getWorkHoursRate() {
                $scope.workHoursRate = [];
                xyKpiService.getWorkHoursRate($scope.formRefer).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.workHoursRate = data.data;
                            } else {
                                inform.common(Trans('tip.noData'));
                            }
                        } else {
                            inform.common(data.message);
                        }
                        $scope.flag++;
                        timeout();
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            //项目计划完成率
            function eChartForProjectPlanCompleteRate() {
                $scope.currentProjectPlanCompleteRateChart = echarts.init(
                    document.getElementById('projectPlanCompleteRateChart')
                );
                //处理项目计划完成率获取到的返回值
                var xData = [];
                //变更次数
                var changeNum = [];
                //计划完成率
                var planCompleteRate = [];
                //计划完成率最小显示值
                var planCompleteRateMin = 100;
                //计划完成率基线
                var basePlanCompleteRate = 88;
                angular.forEach($scope.projectPlanCompleteRate, function (projectPlanCompleteRate) {
                    xData.push(projectPlanCompleteRate.projectName);
                    changeNum.push(projectPlanCompleteRate.changeNum);
                    planCompleteRate.push(projectPlanCompleteRate.planCompleteRate);
                    if (planCompleteRateMin > projectPlanCompleteRate.planCompleteRate * 1) {
                        planCompleteRateMin = projectPlanCompleteRate.planCompleteRate * 1;
                    }
                });
                if (planCompleteRateMin >= basePlanCompleteRate) {
                    planCompleteRateMin = basePlanCompleteRate - 5;
                } else if (planCompleteRateMin < basePlanCompleteRate && planCompleteRateMin - 5 >= 0) {
                    planCompleteRateMin = planCompleteRateMin - 5;
                }

                var option = {
                    title: {
                        text: '项目计划完成率',
                        textStyle: {
                            fontSize: 13,
                            color: '#333',
                        },
                    },
                    dataZoom: {
                        type: 'slider', //slider是出现滚动条，inside是隐藏滚动条，通过鼠标进行放大缩小以及左右滑动
                        zoomLock: true,
                        startValue: 0,
                        endValue: 16,
                        bottom: 7,
                        height: '18',
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: formatterCall,
                        axisPointer: {
                            type: 'cross',
                            crossStyle: {
                                color: '#999',
                            },
                        },
                    },
                    legend: {
                        data: ['计划变更次数', '项目计划完成率'],
                        top: 16,
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: xData,
                            axisPointer: {
                                type: 'shadow',
                            },
                            axisLabel: {
                                rotate: 16,
                            },
                        },
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            name: '变更次数',
                            axisLabel: {
                                formatter: '{value}',
                            },
                        },
                        {
                            type: 'value',
                            name: '计划完成率',
                            axisLabel: {
                                formatter: '{value}%',
                            },
                            min: planCompleteRateMin,
                        },
                    ],
                    series: [
                        {
                            name: '计划变更次数',
                            type: 'bar',
                            label: {
                                show: true,
                            },
                            lineStyle: {
                                color: 'blue',
                            },
                            data: changeNum,
                        },
                        {
                            name: '项目计划完成率',
                            type: 'line',
                            yAxisIndex: 1,
                            label: {
                                show: true,
                                formatter: '{c}%',
                                textStyle: {
                                    color: 'black',
                                },
                            },
                            lineStyle: {
                                color: 'green',
                            },
                            data: planCompleteRate,
                            markLine: {
                                symbol: 'none',
                                lineStyle: {
                                    normal: {
                                        color: 'green', // 这儿设置安全基线颜色
                                    },
                                },
                                data: [
                                    {
                                        yAxis: basePlanCompleteRate, //这儿定义基准线的数值为多少
                                    },
                                ],
                            },
                        },
                    ],
                };
                $scope.currentProjectPlanCompleteRateChart.setOption(option, true);
            }

            //plm完成率
            function eChartForPlmFinishRate() {
                $scope.currentPlmFinishRateChart = echarts.init(document.getElementById('plmFinishRateChart'));
                //处理plm完成情况获取到的返回值
                var xData = [];
                //plm按时完成数量
                var plmFinishOnTimeNum = [];
                //plm延期完成数量
                var plmFinishDelayTimeNum = [];
                //plm临期数量
                var plmNotDueNum = [];
                //plm按时完成率
                var plmOnTimeCompleteRate = [];
                //plm按时完成率最小显示值
                var plmFinishRateMin = 100;
                //plm按时完成率基线
                var basePlmFinishRate = 92;
                angular.forEach($scope.plmFinishRate, function (plmFinishRate) {
                    if ('项目管理办公室' !== plmFinishRate.department) {
                        xData.push(plmFinishRate.department);
                        plmFinishOnTimeNum.push(plmFinishRate.plmFinishOnTimeNum);
                        plmFinishDelayTimeNum.push(plmFinishRate.plmFinishDelayTimeNum);
                        plmNotDueNum.push(plmFinishRate.plmNotDueNum);
                        plmOnTimeCompleteRate.push(plmFinishRate.plmOnTimeCompleteRate);
                        if (plmFinishRateMin > plmFinishRate.plmOnTimeCompleteRate * 1) {
                            plmFinishRateMin = plmFinishRate.plmOnTimeCompleteRate * 1;
                        }
                    }
                });
                if (plmFinishRateMin >= basePlmFinishRate) {
                    plmFinishRateMin = basePlmFinishRate - 5;
                } else if (plmFinishRateMin < basePlmFinishRate && plmFinishRateMin - 5 >= 0) {
                    plmFinishRateMin = plmFinishRateMin - 5;
                }

                var option = {
                    title: {
                        text: '客户需求按时完成率',
                        left: 20,
                        textStyle: {
                            fontSize: 13,
                            color: '#333',
                        },
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: formatterCall,
                        axisPointer: {
                            type: 'cross',
                            crossStyle: {
                                color: '#999',
                            },
                        },
                    },
                    legend: {
                        data: ['按时完成数量', '延期数量', '临期数量', 'PLM按时完成率'],
                        top: 16,
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: xData,
                            axisPointer: {
                                type: 'shadow',
                            },
                            axisLabel: {
                                rotate: 25,
                            },
                        },
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            name: '数量',
                            axisLabel: {
                                formatter: '{value}',
                            },
                        },
                        {
                            type: 'value',
                            name: '按时完成率',
                            axisLabel: {
                                formatter: '{value}%',
                            },
                            min: plmFinishRateMin,
                        },
                    ],
                    series: [
                        {
                            name: '按时完成数量',
                            type: 'bar',
                            label: {
                                show: true,
                            },
                            data: plmFinishOnTimeNum,
                        },
                        {
                            name: '延期数量',
                            type: 'bar',
                            label: {
                                show: true,
                            },
                            data: plmFinishDelayTimeNum,
                        },
                        {
                            name: '临期数量',
                            type: 'bar',
                            label: {
                                show: true,
                            },
                            data: plmNotDueNum,
                        },
                        {
                            name: 'PLM按时完成率',
                            type: 'line',
                            yAxisIndex: 1,
                            label: {
                                show: true,
                                formatter: '{c}%',
                                textStyle: {
                                    color: 'black',
                                },
                            },
                            data: plmOnTimeCompleteRate,
                            markLine: {
                                symbol: 'none',
                                lineStyle: {
                                    normal: {
                                        color: 'red', // 这儿设置安全基线颜色
                                    },
                                },
                                data: [
                                    {
                                        yAxis: basePlmFinishRate, //这儿定义基准线的数值为多少
                                    },
                                ],
                            },
                        },
                    ],
                };
                $scope.currentPlmFinishRateChart.setOption(option, true);
            }
            //工时占比
            function eChartForWorkHoursRate() {
                $scope.currentWorkHoursRateChart = echarts.init(document.getElementById('workHoursRateChart'));
                //处理工时占比获取到的返回值
                var xData = [];
                //技术支持占比
                var supportRate = [];
                //部门工作占比
                var departmentWorkRate = [];
                angular.forEach($scope.workHoursRate, function (workHoursRate) {
                    if ('项目管理办公室' !== workHoursRate.department) {
                        xData.push(workHoursRate.department);
                        supportRate.push(workHoursRate.supportRate);
                        departmentWorkRate.push(workHoursRate.departmentWorkRate);
                    }
                });

                var option = {
                    title: {
                        text: '工时占比',
                        left: 20,
                        textStyle: {
                            fontSize: 13,
                            color: '#333',
                        },
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: formatterCall,
                        axisPointer: {
                            type: 'cross',
                            crossStyle: {
                                color: '#999',
                            },
                        },
                    },
                    legend: {
                        data: ['技术支持占比', '部门工作占比'],
                        top: 16,
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: xData,
                            axisPointer: {
                                type: 'shadow',
                            },
                            axisLabel: {
                                rotate: 25,
                            },
                        },
                    ],
                    yAxis: [
                        {
                            type: 'value',
                        },
                    ],
                    series: [
                        {
                            name: '技术支持占比',
                            type: 'bar',
                            label: {
                                show: true,
                            },
                            data: supportRate,
                            lineStyle: {
                                color: 'blue',
                            },
                            markLine: {
                                symbol: 'none',
                                lineStyle: {
                                    normal: {
                                        color: 'blue', // 这儿设置安全基线颜色
                                    },
                                },
                                data: [
                                    {
                                        yAxis: 15, //这儿定义基准线的数值为多少
                                    },
                                ],
                            },
                        },
                        {
                            name: '部门工作占比',
                            type: 'bar',
                            label: {
                                show: true,
                            },
                            lineStyle: {
                                color: 'green',
                            },
                            data: departmentWorkRate,
                            markLine: {
                                symbol: 'none',
                                lineStyle: {
                                    normal: {
                                        color: 'green', // 这儿设置安全基线颜色
                                    },
                                },
                                data: [
                                    {
                                        yAxis: 10, //这儿定义基准线的数值为多少
                                    },
                                ],
                            },
                        },
                    ],
                };
                $scope.currentWorkHoursRateChart.setOption(option, true);
            }
            //自定义鼠标悬浮样式
            function formatterCall(params, ticket, callback) {
                var htmlStr = '';
                for (var i = 0; i < params.length; i++) {
                    var param = params[i];
                    var xName = param.name; //x轴的名称
                    var seriesName = param.seriesName; //图例名称
                    var value = param.value; //y轴值
                    var color = param.color; //图例颜色
                    if (i === 0) {
                        htmlStr += xName + '<br/>'; //x轴的名称
                    }
                    htmlStr += '<div>';
                    //为了保证和原来的效果一样，这里自己实现了一个点的效果
                    htmlStr +=
                        '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
                        color +
                        ';"></span>';
                    //圆点后面显示的文本
                    htmlStr += seriesName;
                    htmlStr += '<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">';
                    if (!value) {
                        value = '-';
                    }
                    if (
                        '计划变更次数' === seriesName ||
                        '按时完成数量' === seriesName ||
                        '延期数量' === seriesName ||
                        '临期数量' === seriesName
                    ) {
                        htmlStr += value;
                    } else {
                        htmlStr += value + '%';
                    }

                    htmlStr += '</span>';
                    htmlStr += '</div>';
                }
                return htmlStr;
            }
            /**
             * 当窗体大小变化时，修改图例大小
             */
            window.addEventListener('resize', function () {
                if ($scope.currentProjectPlanCompleteRateChart) {
                    $scope.currentProjectPlanCompleteRateChart.resize();
                }
                if ($scope.currentCostRateChart) {
                    $scope.currentCostRateChart.resize();
                }
                if ($scope.currentPlmFinishRateChart) {
                    $scope.currentPlmFinishRateChart.resize();
                }
                if ($scope.currentWorkHoursRateChart) {
                    $scope.currentWorkHoursRateChart.resize();
                }
            });
            /**
             * 初始化检索条件年度与季度
             */
            function initTime() {
                //当前时间（Date类型）
                var date = new Date();
                //得到上一个季度的第一天
                var lastQuarterFirstDay = new Date(date.getFullYear(), date.getMonth() - 3, 1);
                var day = inform.format(lastQuarterFirstDay, 'yyyy-MM-dd').split('-');
                $scope.formRefer.kpiYear = null != $stateParams.years ? $stateParams.years : day[0];
                //设置季度
                var month = lastQuarterFirstDay.getMonth() + 1;
                $scope.formRefer.kpiQuarter =
                    null != $stateParams.quarter ? $stateParams.quarter : inform.dateToQuarter(month) * 1 + 1 + '';

                if ($scope.formRefer.kpiQuarter === '1' || $scope.formRefer.kpiQuarter === '2') {
                    $scope.formRefer.kpiQuarter = '6';
                }
                if ($scope.formRefer.kpiQuarter === '3' || $scope.formRefer.kpiQuarter === '4') {
                    $scope.formRefer.kpiQuarter = '7';
                }
            }
            function reset() {
                initTime();
            }

            $scope.goback = function () {
                $state.go('app.office.personKpi');
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        },
    ]);
})();

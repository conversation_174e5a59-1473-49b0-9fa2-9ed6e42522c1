(function() {
    app.controller("staffDailyActionAddController", ['staffDailyActionService', '$rootScope', 'comService', '$scope', '$state', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function(staffDailyActionService, $rootScope, comService, $scope, $state, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置

         //初始化页面信息
            initPages();
            //初始化当前时间
            $scope.datepicker = {
                currentDate: new Date()
            };
            //问题类别
            $scope.staffDailyActionTypeList = [];
            $scope.classifType = 0;
            $scope.tag = "效益";
            //行为类型
            $scope.classificationList = [{
            	value: '0',
            	label: '正向行为'
            }, {
            	value: '1',
            	label: '负面行为'
            }];
            $scope.changeClassify = changeClassify;
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            function initPages() {
                //等级
                $scope.lowQualityGradeList = [];
                comService.getParamList('LOW_QUALITY_GRADE', 'LOW_QUALITY_GRADE').then(function(data) {
                    $scope.lowQualityGradeList = data.data;
                });
                //获取员工信息
                $scope.employeesList = [];
                comService.getEmployeesByOrgId('').then(function(data) {
                    $scope.employeeList = data.data;
                });
                setValue();
            }

            //改变行为类型
            function changeClassify(){
                if($scope.changeParam.classification==='0'){
                    $scope.classifType = 0;
                    $scope.tag = "效益";
                    //正面类型
                    $scope.staffDailyActionTypeList = ['技术贡献','价值引领','团队建设','流程建设']
                }else if($scope.changeParam.classification==='1'){
                    $scope.classifType = 1;
                    $scope.tag = "影响";
                    //负面类型
                    $scope.staffDailyActionTypeList = ['不符设计规范','不符模板要求','不符管理规定','测试、验证逃逸','源代码','笔误','沟通问题',
                         '实施问题','流程处理','共性问题重复','出差日报缺失','客户问题响应不及时','日常规范','日志规范','执行力','其他']
                }else {
                    $scope.staffDailyActionTypeList = [];
                }
            }

            /**
             * 若为更新 则赋值
             */
            function setValue() {
                if ($stateParams.item==null) {
                    $scope.signName = "新增日常行为";
                    return;
                }
                $scope.signName = "修改日常行为";
                var urlData = {
                    'id': $stateParams.item
                };
                staffDailyActionService.getStaffDailyActionInfo(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.changeParam = data.data.list[0];
                        var participants = $scope.changeParam.personLiableId.slice(0, $scope.changeParam.personLiableId.length);
                        $scope.personLiable = [];
                        $scope.personLiable = participants.split(',');
                        $scope.changeClassify();
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 保存信息
             */
             $scope.saveInfo = function() {
                var urlData = {
                    'classification':$scope.changeParam.classification,
                    'date': inform.format($scope.changeParam.date, 'yyyy-MM-dd'),
                    'type': $scope.changeParam.type,
                    'gradeCode': $scope.changeParam.gradeCode,
                    'summary': $scope.changeParam.summary,//行为描述
                    'influence': $scope.changeParam.influence,
                    'remark': $scope.changeParam.remark,
                    'personLiableId': $scope.personLiable.join(',')
                };
                //新增
                if ($stateParams.item==null) {
                    addInfo(urlData);
                } else {
                    //修改 
                    urlData.id = $scope.changeParam.id;
                    upInfo(urlData)
                }
            };
            /**
             * 新增信息
             */
            function addInfo(urlData) {
                staffDailyActionService.addStaffDailyActionInfo(urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        inform.common(data.message);
                        $scope.goback();
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * 修改信息
             * @param urlData
             */
            function upInfo(urlData) {
                staffDailyActionService.updateStaffDailyActionInfo(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        layer.confirm(data.message, {
                            title: false,
                            btn: ['确定']
                        }, function(result) {
                            layer.close(result);
                            $scope.goback();
                        });
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 返回
             */
            $scope.goback = function() {
                $state.go("app.office.staffDailyActionManagement", null);
            };
            /**
             * 新增中的时间
             */
            $scope.openDate = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedEnd = true;
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }
    ]);
})();
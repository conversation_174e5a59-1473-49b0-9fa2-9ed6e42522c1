(function () {
  app.controller('personalDevelopBugsController', [
    '$rootScope',
    'comService',
    '$scope',
    '$state',
    '$timeout',
    '$stateParams',
    '$modal',
    'personalDataBoardService',
    'inform',
    'Trans',
    'AgreeConstant',
    'LocalCache',
    '$http',
    function (
      $rootScope,
      comService,
      $scope,
      $state,
      $timeout,
      $stateParams,
      $modal,
      personalDataBoardService,
      inform,
      Trans,
      AgreeConstant,
      LocalCache,
      $http
    ) {
      /**
       * *************************************************************
       *             初始化部分                                 开始
       * *************************************************************
       */
      //页面数据
      $scope.formRefer = {};
      //解决方案列表
      $scope.solutionList = {};
      //设置列表的高度
      setDivHeight();
      //reopen次数
      $scope.reopenCount = [
        {
          value: '0',
          label: '0次',
        },
        {
          value: '1',
          label: '>=1次',
        },
        {
          value: '2',
          label: '>=2次',
        },
        {
          value: '3',
          label: '>=3次',
        },
      ];
      //窗体大小变化时重新计算高度
      $(window).resize(setDivHeight);
      $scope.getData = getData;
      $scope.reset = reset;
      //初始化信息
      initData();

      /**
       * *************************************************************
       *              初始化部分                                 结束
       * *************************************************************
       */

      /**
       * *************************************************************
       *              方法声明部分                                 开始
       * *************************************************************
       */

      /**
       * 初始化
       */
      function initData() {
        //获取当前登录者的empId
        $scope.sessionEmpId = LocalCache.getSession('employeeId');
        $scope.formRefer.empId = $stateParams.empId == null ? $scope.sessionEmpId : $stateParams.empId;
        //获取当前登录者
        $scope.formRefer.loginName = LocalCache.getSession('loginName');
        var person = LocalCache.getObject('personDataBoardEmployee');
        if (person.loginName) {
          $scope.formRefer.loginName = person.loginName;
        }
        reset();
        InitSolutions();
        getData();
      }
      /**
       * 初始化解决方案列表
       **/
      function InitSolutions() {
        personalDataBoardService.getBugSolutionList().then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              if (null !== data.data) {
                $scope.solutionList = data.data;
              } else {
                inform.common(Trans('tip.noData'));
              }
            } else {
              inform.common(data.message);
            }
          },
          function () {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      /**
       * 获取开发过程中bug数据
       */
      function getData() {
        let personName = LocalCache.getSession('employeeName');
        var person = LocalCache.getObject('personDataBoardEmployee');
        if (person.name) {
          personName = person.name;
        }
        var urlData = {
          loginName: $scope.formRefer.loginName,
          personName,
          startTime: inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
          endTime: inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间
          reopenCount: $scope.formRefer.reopenCount,
          solution: $scope.formRefer.solution,
        };
        $scope.tableData = [];
        $scope.reopenBugNum = 0;
        personalDataBoardService.getPersonalDevelopBugsData(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              if (null !== data.data) {
                $scope.tableData = data.data;
                if (data.data.length > 0) {
                  angular.forEach($scope.tableData, function (item) {
                    if (item.reopenCount !== 0) {
                      $scope.reopenBugNum = $scope.reopenBugNum + 1;
                    }
                  });
                }
              } else {
                inform.common(Trans('tip.noData'));
              }
            } else {
              inform.common(data.message);
            }
          },
          function () {
            inform.common(Trans('tip.requestError'));
          }
        );
        getBugNumbers();
      }
      /**
       * 获取产生bug的数据
       */
      function getBugNumbers() {
        let personName = LocalCache.getSession('employeeName');
        var person = LocalCache.getObject('personDataBoardEmployee');
        if (person.name) {
          personName = person.name;
        }
        var urlData = {
          loginName: $scope.formRefer.loginName,
          personName,
          startTime: inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
          endTime: inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间
          reopenCount: $scope.formRefer.reopenCount,
          solution: $scope.formRefer.solution,
        };
        // 解决bug数
        $scope.solveBugNumbers = 0;
        // 产生bug数
        $scope.createBugNumbers = 0;
        personalDataBoardService.getBugNumbers(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              if (data.data !== null) {
                $scope.solveBugNumbers = data.data.solveBugNum;
                $scope.createBugNumbers = data.data.createBugNum;
              } else {
                inform.common(Trans('tip.noData'));
              }
            } else {
              inform.common(data.message);
            }
          },
          function () {
            inform.common(Trans('tip.requestError'));
          }
        );
      }

      /**
       * 设置列表的高度
       */
      function setDivHeight() {
        //网页可见区域高度
        var clientHeight = document.body.clientHeight;
        var divHeight = clientHeight - (150 + 210);
        $('#divTBDis').height(divHeight);
        $('#subDivTBDis').height(divHeight - 40);
        $('#divTBDis1').height(divHeight);
        $('#subDivTBDis1').height(divHeight - 40);
      }

      function reset() {
        $scope.formRefer.startTime = inform.format(new Date(), 'yyyy') + '-01-01';
        $scope.formRefer.endTime = '';
        $scope.formRefer.reopenCount = '';
        $scope.formRefer.solution = '';
      }

      $scope.goback = function () {
        $state.go('app.office.personKpi');
      };

      /**
       * 查询条件中的开始时间
       */
      $scope.openDateStart = function ($event) {
        $event.preventDefault();
        $event.stopPropagation();
        //仅查询中的开始时间显示控件内容
        $scope.formRefer.openedStart = true;
        $scope.formRefer.openedEnd = false;
      };
      /**
       * 查询条件中的结束时间
       */
      $scope.openDateEnd = function ($event) {
        $event.preventDefault();
        $event.stopPropagation();
        $scope.formRefer.openedStart = false;
        $scope.formRefer.openedEnd = true;
      };
      /**
       * *************************************************************
       *              方法声明部分                                 结束
       * *************************************************************
       */
    },
  ]);
})();

//# sourceURL=js/services/office/projectRelease/projectReleaseService.js
(function () {
    'use strict';
    app.factory('projectReleaseService', projectReleaseService);
    projectReleaseService.$inject = ["HttpService", '$rootScope'];

    function projectReleaseService(HttpService, $rootScope) {
        var service = {
            selectData:selectData,
            selectOne:selectOne,
            updateInfo:updateInfo
        };
        return service;
        /**
         * 获取所有项目的版本信息
         */
        function selectData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'projectRelease/selectData', urlData);
        }
        /**
         * 获取某个项目版本的具体信息
         */
        function selectOne(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'projectRelease/selectOne', urlData);
        }
        /**
         * 修改项目版本信息
         */
        function updateInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'projectRelease/updateInfo', urlData);
        }
    }
})();
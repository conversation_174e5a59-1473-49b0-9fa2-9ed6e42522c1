(function () {
  app.controller('departmentSprintIndexController', [
    'LocalCache',
    'Trans',
    'inform',
    '$ocLazyLoad',
    '$rootScope',
    'comService',
    '$scope',
    '$stateParams',
    '$state',
    'deptBoardFactory',
    'departmentSprintService',
    'AgreeConstant',
    function (
      LocalCache,
      Trans,
      inform,
      $ocLazyLoad,
      $rootScope,
      comService,
      $scope,
      $stateParams,
      $state,
      deptBoardFactory,
      departmentSprintService,
      AgreeConstant
    ) {
      // 初始化
      deptBoardFactory.init($scope, '10');
      // 重置部分
      $scope.resetParam = resetParam;
      function resetParam() {
        deptBoardFactory.initTime($scope, '本年度');
      }

      function getStatisticsData() {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
        };
        $scope.showStatisticsInfo = false;
        departmentSprintService.getDeptSprintTotalInfo(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.departmentSprintStatisticsData = result.data;
              $scope.showStatisticsInfo = true;
            } else {
              inform.common(result.message);
              $scope.showStatisticsInfo = true;
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
            $scope.showStatisticsInfo = true;
          }
        );
      }

      // top5标签上面数字
      function getTop5Number() {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
        };
        departmentSprintService.getDeptSprintCountForTable(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.top5 = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // top5部分
      $scope.changeType = function changeType(type) {
        $scope.type = type;
        if (type === '10') {
          getResultWaitingData('冲刺结论待定');
        } else if (type === '11') {
          getSprintFailData('冲刺失败');
        } else if (type === '12') {
          getSprintSuccessData('冲刺成功');
        } else if (type === '13') {
          getSprintDoingData('进行中冲刺');
        }
      };
      function getSprintDoingData(finishState) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          status: finishState,
        };
        departmentSprintService.getDeptSprintInfoForTable(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.sprintDoingData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      function getResultWaitingData(finishState) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          status: finishState,
        };
        departmentSprintService.getDeptSprintInfoForTable(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.resultWaitingData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      function getSprintFailData(finishState) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          status: finishState,
        };
        departmentSprintService.getDeptSprintInfoForTable(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.sprintFailData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      function getSprintSuccessData(finishState) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          status: finishState,
        };
        departmentSprintService.getDeptSprintInfoForTable(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.sprintSuccessData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }

      $scope.getData = getData;
      function getData() {
        getStatisticsData();
        getTeamScheduleChartData();
        // 获取top5的数据
        getTop5Number();
        $scope.changeType($scope.type);
      }

      // 图表部分
      $scope.currentTeamScheduleChart = null;
      window.addEventListener('resize', chartResize);
      $scope.$on('$destroy', function () {
        window.removeEventListener('resize', chartResize);
      });
      function chartResize() {
        if ($scope.currentTeamScheduleChart) {
          $scope.currentTeamScheduleChart.resize();
        }
      }

      // 获取部门工时图表数据
      function getTeamScheduleChartData() {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
        };
        deptBoardFactory.chartHideClear($scope.currentTeamScheduleChart);
        deptBoardFactory.chartShowLoading($scope.currentTeamScheduleChart);
        departmentSprintService.getDeptSprintTotalInfoByTeam(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.teamScheduleChartInfo = result.data;
              deptBoardFactory.chartHideLoading($scope.currentTeamScheduleChart);
              eChartShowForTeamScheduleBarAndLine(
                $scope.currentTeamScheduleChart,
                $scope.teamScheduleChartInfo,
                '团队冲刺',
                AgreeConstant.departmentBoard.teamScheduleLegendData
              );
            } else {
              inform.common(result.message);
              deptBoardFactory.chartHideLoading($scope.currentTeamScheduleChart);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
            deptBoardFactory.chartHideLoading($scope.currentTeamScheduleChart);
          }
        );
      }

      //部门工时图表显示
      function eChartShowForTeamScheduleBarAndLine(currentChart, data, title, legendData) {
        var xData = [];
        var sprintTotalData = [];
        var sprintSuccess = [];
        var sprintFailData = [];
        var sprintWaitData = [];
        var sprintingData = [];
        var sprintRateData = [];
        if (data.length) {
          angular.forEach(data, function (eachData) {
            xData.push(eachData.team);
            sprintTotalData.push({
              value: eachData.sprintTotalData,
            });
            sprintSuccess.push({
              value: eachData.sprintSuccess,
            });
            sprintFailData.push({
              value: eachData.sprintFailData,
            });
            sprintWaitData.push({
              value: eachData.sprintWaitData,
            });
            sprintingData.push({
              value: eachData.sprintingData,
            });
            sprintRateData.push({
              value: eachData.sprintRateData,
            });
          });
          var option = {
            title: {
              text: title,
              textStyle: {
                fontSize: 18,
                color: '#333',
              },
            },
            grid: {
              left: '2%',
              right: '2%',
              bottom: '0',
              containLabel: true,
            },
            legend: {
              data: legendData,
            },
            xAxis: [
              {
                type: 'category',
                data: xData,
                axisPointer: {
                  type: 'shadow',
                },
                axisLabel: {
                  rotate: 30,
                },
              },
            ],
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'cross',
                crossStyle: {
                  color: '#999',
                },
              },
              formatter: function (params, ticket, callback) {
                return deptBoardFactory.formatterCall(params, ticket, callback, '冲刺成功率');
              },
            },
            yAxis: [
              {
                type: 'value',
                axisLabel: {
                  formatter: '{value}次',
                },
              },
              {
                type: 'value',
                axisLabel: {
                  formatter: '{value}%',
                },
              },
            ],
            series: [
              {
                name: legendData[0],
                type: 'bar',
                barWidth: '10%',
                yAxisIndex: 0,
                data: sprintTotalData,
                show: false,
              },
              {
                name: legendData[1],
                type: 'bar',
                barWidth: '10%',
                yAxisIndex: 0,
                data: sprintSuccess,
                itemStyle: {
                  color: '#0064f0',
                },
              },
              {
                name: legendData[2],
                type: 'bar',
                barWidth: '10%',
                yAxisIndex: 0,
                data: sprintFailData,
                itemStyle: {
                  color: '#FF4500',
                },
              },
              {
                name: legendData[3],
                type: 'bar',
                barWidth: '10%',
                yAxisIndex: 0,
                data: sprintWaitData,
                itemStyle: {
                  color: '#FFD700',
                },
              },
              {
                name: legendData[4],
                type: 'bar',
                barWidth: '10%',
                yAxisIndex: 0,
                data: sprintingData,
                itemStyle: {
                  color: '#98FB98',
                },
              },
              {
                name: legendData[5],
                type: 'line',
                yAxisIndex: 1,
                data: sprintRateData,
                itemStyle: {
                  color: '#87CEFA',
                },
              },
            ],
          };
        } else {
          option = {
            title: [
              {
                text: title,
                textStyle: {
                  fontSize: 12,
                  color: '#333',
                },
              },
              {
                text: '暂无数据',
                left: 'center',
                top: 'center',
                color: '#333',
                textStyle: {
                  fontSize: 20,
                },
              },
            ],
          };
        }

        currentChart.setOption(option, true);
      }

      $scope.versionManager = function () {
        $state.go('app.office.sprintVersions');
      };

      $scope.loadSuccess = function () {
        $ocLazyLoad.load(['library/component/echarts.min.js']).then(function () {
          $scope.currentTeamScheduleChart = echarts.init(document.getElementById('teamScheduleChart'));
          var localFormRefer = LocalCache.getObject('departmentList_formRefer');
          if (Object.keys(localFormRefer).length > 0) {
            $scope.formRefer = localFormRefer;
            $scope.butFlag = localFormRefer.searchTimeString;
          }
          if ($stateParams.orgCode) {
            $scope.formRefer.orgCode = $stateParams.orgCode;
          }
          getData();
        });
      };

      $scope.title = '';
      $scope.desc = true;
      // 排序
      $scope.order = (str) => {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      };
    },
  ]);
})();

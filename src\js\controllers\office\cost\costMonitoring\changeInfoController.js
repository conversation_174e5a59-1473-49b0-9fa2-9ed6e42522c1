(function () {
    'use strict';
    app.controller("changeInfoController", ['$scope','$state','comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','costMonitoringService','$stateParams','LocalCache', '$modal','$http',
        function ($scope,$state,comService, $rootScope, inform, Trans, AgreeConstant,costMonitoringService,$stateParams,LocalCache, $modal,$http) {
    	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */ 
    	if($stateParams.functionType==='changeInfo'){
    		//调用项目变更的方法
    		getChangeInfo();
    	}else if($stateParams.functionType==='freeDetails'){
    		//调用费用明细的方法
    		getFreeDetails();
    	}else if($stateParams.functionType==='changeDetails'){
    		var clientHeight = document.body.clientHeight;
    		var clientWidth = document.body.clientWidth;
    		$("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
    		$("#buttonStyle").css({"width": 100+"px"});
    		//调用变更明细的方法
    		getChangeLevel();
    	}
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */
    	/**
    	 * 获取费用明细
    	 */
    	function getFreeDetails(){
    		var urlData = {
            	'projectId': $stateParams.projectId,
            	'statuses': $stateParams.status.split(',')
            };
    		costMonitoringService.getFreeDetails(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                	 $scope.freeList = data.data;
                     if ($scope.freeList.length === 0) {
                         inform.common("无符合条件的费用信息");
                     } else {
                    	 for (var i=0;i<$scope.freeList.length;i++){
                    		 var one = $scope.freeList[i];
                    		 one.amount = inform.formatMoney(one.amount);
                    	 }
                     }
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
    	}
    	/**
    	 * 获取变更记录
    	 */
    	function getChangeInfo(){
    		var urlData = {
            	'projectId': $stateParams.projectId,
            };
    		costMonitoringService.getChangeInfo(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                	 $scope.changeTitleList = data.data.title
                	 $scope.changeList = data.data.data;
                     if ($scope.changeList==null||$scope.changeList.length === 0) {
                         inform.common("无符合条件的变更信息");
                     }
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
    	}
    	/**
    	 * 获取变更明细
    	 */
    	$scope.getChangDetails = function (m){
    		$state.go("app.office.changeDetails", {functionType:'changeDetails',projectId:$stateParams.projectId,
    											   nowVersion:m.version,comperVersion:m.compareVersion});
    	}
    	/**
    	 * 获取变更等级
    	 */
    	function getChangeLevel(){
    		var urlData = {
                'projectId': $stateParams.projectId,
                'version': $stateParams.nowVersion,
                'compareVersion':$stateParams.comperVersion
            };
    		costMonitoringService.getChangeLevel(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
					//表头
                    $scope.levelList = data.data;
                    $scope.secLevelList=[];
                    angular.forEach($scope.levelList, function (detail, i) {
						$scope.secLevelList.push('变更后');
			    		$scope.secLevelList.push('变更前');
			    		$scope.secLevelList.push('变更');
                    });
					//获取变更岗位
                    getChangeTitle(urlData);
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
    	}
    	/**
    	 * 获取变更岗位
    	 */
    	function getChangeTitle(urlData){
    		costMonitoringService.getChangeTitle(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
					//项目行
					$scope.titleList = data.data;
					//获取变更单元格（再单元格后添加调用费用）
					getChangeDetail(urlData);
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
    	}
    	/**
    	 * 获取变更单元格
    	 */
    	function getChangeDetail(urlData){
    		costMonitoringService.getChangeDetail(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
					//项目行
					var list = data.data;
					$scope.infoList=[];
					packageInfo(list,$scope.titleList,$scope.levelList,$scope.infoList,['变更后','变更前','变更']);
					//项目变更费用
					getChangeFee(urlData)
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
    	}
    	/**
    	 * 组装数据
    	 */
    	function packageInfo (list,titles,levels,finList,secs){
    		//岗位
			angular.forEach(titles, function (title, i) {
				//一个岗位一条记录
				var info = {
					title: title,
					level: []
				};
				//级别
				angular.forEach(levels, function (level, ji) {
					//状态
					angular.forEach(secs, function (sec, zi) {
						//默认无匹配
						var flag=false;
						for(var j=0;j<list.length;j++){
							var one = list[j];
							if(one.title === title){
								if (one.level === level){
									if(one.type===sec){
										info.level.push(inform.removeZero(one.workload));
										//如果有匹配的
										flag=true;
										continue;
									} 
								}
							}
						}
						if (flag===false){
							info.level.push("0");
						}
					});
			     });
				finList.push(info);
	    	});
    	}
    	/**
    	 * 获取变更费用
    	 */
    	function getChangeFee(urlData){
    		costMonitoringService.getChangeFee(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                	$scope.feeList = data.data;
                	 for (var i=0;i<$scope.feeList.length;i++){
                		 var one = $scope.feeList[i];
                		 one.afterFeeCost = inform.formatMoney(one.afterFeeCost);
                		 one.befoeFeePre = inform.formatMoney(one.befoeFeePre);
                		 one.feeDiff = inform.formatMoney(one.feeDiff);
                	 }
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
    	}
	     /**
          * *************************************************************
          *              方法声明部分                                 结束
          * *************************************************************
          */
         } 
    ]);
})();
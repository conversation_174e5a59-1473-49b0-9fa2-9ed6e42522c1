{"name": "dataplatform", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "gulp", "build:vue": "node build-vue.js", "dev:vue": "cd src/vue-components && npm run dev", "install:vue": "cd src/vue-components && npm install", "setup:vue": "npm run install:vue && npm run build:vue"}, "repository": {"type": "git"}, "author": "", "license": "ISC", "devDependencies": {"browser-sync": "^2.27.9", "gulp": "^4.0.2", "gulp-cache": "^1.1.3", "gulp-connect": "^5.7.0", "gulp-watch": "^5.0.1"}}
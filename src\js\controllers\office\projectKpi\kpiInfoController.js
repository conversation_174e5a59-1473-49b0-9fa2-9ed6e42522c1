(function () {
    'use strict';
    app.controller("kpiInfoController", ['$scope','$state','comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','kpiInfoService','$stateParams','LocalCache', '$modal','$http',
        function ($scope,$state,comService, $rootScope, inform, Trans, AgreeConstant,kpiInfoService,$stateParams,LocalCache, $modal,$http) {

		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		 // 路由中传参数，区分是团队还是项目信息
         $scope.type = $stateParams.type;
         //查询条件 从缓存中读取
         $scope.formRefer = LocalCache.getObject('kpiInfo_formRefer');
         if($scope.formRefer.year==null&&$scope.type==='1'){
             //默认当前年度
             $scope.formRefer.year = inform.format(new Date(),"yyyy");
         }
         LocalCache.setObject('kpiInfo_formRefer',{});
         // 初始化分页数据
         $scope.pages = inform.initPages();
         //季度下拉框数据源
	        $scope.quarterSelect = [
            {
                value: '6',
                label: '上半年'
            }, {
                value: '7',
                label: '下半年'
            }, {
	            value: '5',
                label: '年度'
            } ,{
                value: '1',
                label: '第1季度'
	        }, {
	            value: '2',
	            label: '第2季度'
	        }, {
	            value: '3',
	            label: '第3季度'
	        }, {
	            value: '4',
	            label: '第4季度'
	        }];
         //设置列表的高度
         setDivHeight();
         //窗体大小变化时重新计算高度
         $(window).resize(setDivHeight);
         //初始化查询
         initPages();
         //获取数据
         $scope.getData = getData;
         getData();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */
        /**
         * 重置
         */
        $scope.reset = function() {
            $scope.formRefer = {};
            if($scope.type==='1'){
                //默认当前年度
                $scope.formRefer.year = inform.format(new Date(),"yyyy");
            }
        }
        /**
         * 设置列表的高度
         */
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (150 + 185);
            $("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight - 50);
        }
        /**
         * 初始化
         */
        function initPages() {
        	//获取产品线
            $scope.projectLine = [];
            comService.queryEffectiveParam('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                if (data.data) {
                    $scope.projectLine = data.data;
                }
            });
            //获取部门信息
          	$scope.departmentSelect = [];
          	comService.getOrgChildren('D010053').then(function(data) {
      			$scope.departmentSelect = comService.getDepartment(data.data);
            });
        }
        /**
         * 获取kpi查询信息
         */
        function getData(pageNum) {
            var urlData = {
                'productLine': $scope.formRefer.productLine,
                'department': $scope.formRefer.department,
                'type':$scope.type,
                'cname': $scope.formRefer.cname,
                'pm':$scope.formRefer.pm,
                'year':$scope.formRefer.year,
                'quarter':$scope.formRefer.quarter,
                'currentPage': pageNum,
                'pageSize': $scope.pages.size,
            };
            kpiInfoService.getData(urlData).then(function (data) {
                 if (data.code === AgreeConstant.code) {
                    //信息
                    $scope.infoList = data.data.list;
                    // 分页信息设置
                    $scope.pages.total = data.data.total;           // 页面数据总数
                    $scope.pages.star = data.data.startRow;         // 页面起始数
                    $scope.pages.end = data.data.endRow;            // 页面结束数
                    $scope.pages.pageNum = data.data.pageNum;       //页号
                } else {
                    inform.common(data.message);
                }
            },
            function () {
                inform.common(Trans("tip.requestError"));
            });
        }

        /**
         * 刷新方法
         */
        $scope.refresh = function (kpiId){
            var urlData = {
                'kpiId': kpiId,
                'type':$scope.type
            };
            kpiInfoService.refresh(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    getData($scope.pages.pageNum);
                } else {
                    inform.common(data.message);
                }
            },
            function () {
                inform.common(Trans("tip.requestError"));
            });
        }

        /**
         * 详情方法
         */
        $scope.toDetails = function (m){
            var periodMap = {
                '5':'年度',
                '6':'上半年',
                '7':'下半年',
                '1':'Q1',
                '2':'Q2',
                '3':'Q3',
                '4':'Q4'
            };
            //名称、领导、时间 放缓存
            var topInfo = {
                'name':m.cname,
                'pm':m.pm,
                'score':m.kpi,
                //团队周期
                'period':m.year+'-'+periodMap[m.quarter],
                'state':m.state
            }
            LocalCache.setObject('kpiInfo_topInfo', topInfo);
            //查询条件放缓存
            LocalCache.setObject('kpiInfo_formRefer',$scope.formRefer);
            if ($scope.type==='0'){
            //项目
                $state.go("app.office.projectKpiDetail", {kpiId: m.kpiId});
            } else {
            //团队
                $state.go("app.office.teamKpiDetail", {kpiId: m.kpiId});
            }
        }
	     /**
          * *************************************************************
          *              方法声明部分                                 结束
          * *************************************************************
          */
         }
    ]);
})();
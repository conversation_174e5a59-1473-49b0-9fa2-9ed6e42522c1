/*
 * @Author: fubaole
 * @Date:   2017-09-18 14:53:05
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-01-15 17:20:32
 */
(function() {
	app.controller("codeReviewController", ['comService', '$rootScope', '$scope', 'reportService','codeReviewService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
		function(comService, $rootScope, $scope,reportService, codeReviewService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {
		
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		$scope.limitList = AgreeConstant.limitList; // 正则校验配置

    	$scope.projectData = [];//项目情况报告
        $scope.projectLine = [];//产品线情况
        $scope.type = 2;
        //页面分页信息
        $scope.pages = {
            pageNum : 1,   //分页页数
            size : '20',      //分页每页大小
            total : ''      //数据总数
        };
         //页面查询条件
        $scope.formRefer = {
                cname : '',  //项目名称
                productLine : '',//产品线
                quarter : ''//季度
        };
        //季度下拉框数据源
        $scope.quarterSelect = [{
            value: '0',
            label: '第1季度'
        },{
            value: '1',
            label: '第2季度'
        },{
            value: '2',
            label: '第3季度'
        },{
            value: '3',
            label: '第4季度'
        }];

        //季度展示Map
        $scope.quarterMap = {
            "0":'第1季度',
            "1":'第2季度',
            "2":'第3季度',
            "3":'第4季度'
        };
        $scope.add=false;
        $scope.up=false;
        $scope.del=false;
        $scope.uplodarFlag = false;
        //设置列表的高度
		setDivHeight();
		//获取缓存
        $scope.formRefer = LocalCache.getObject('codeReviewManagement_formRefer');
        //清除缓存
        LocalCache.setObject('codeReviewManagement_formRefer', {});
		//绑定文件控件改变事件
        $("#files").change(fileChangeEvent);
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
        //获取数据
		getProductLine();
		//判断按钮是否具有权限
		getButtonPermission();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */

    	 /**
         * 选择项目时，回填产品线项目经理、项目助理信息
         * @param projectId
         */
        $scope.projectChange = function(projectId,type){
            reportService.getProjectInfoById(projectId).then(function (data) {
                if (data.code !== AgreeConstant.code) {
                    inform.common(data.message);
                    return;
                }
                data.data = angular.fromJson(data.data);
                if (data.data.length === 0) {
                    inform.common("该项目不存在")
                }else{
                	var manager = data.data.projectManager;
                	if (type==='add'){
                		$scope.spec.projectManager = manager;
                	} 
                	if (type==='up'){
                		$scope.changeParam.projectManager = manager;
                	}
                	if (type==='uplodar'){
                		$scope.uplodar.projectManager = manager;
                		$scope.uplodarFlag = true;
                	}
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        }
    	/**
    	 * 确定选中的项目
    	 */
    	$scope.onSelectProject = function () {
			var treeObj = $.fn.zTree.getZTreeObj("projectRightTreeObj");
			//获取选中的节点集合
			var nodes = treeObj.getCheckedNodes(true);
			$scope.spec.name = nodes[0].name;
			$scope.spec.projectId = nodes[0].id;
			$("#projectRightTree").modal('hide');
    	};
    	
        /**
         * 获取产品线
         */
        function getProductLine(){         
                $scope.lineMap=[];
                comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
                    $scope.projectLine = angular.fromJson(data.data);
                    angular.forEach($scope.projectLine, function(res, index) {
                        $scope.lineMap[res.param_code] = res.param_value;
                    });
                    //获取项目数据
                    $scope.getProjectInfo();
                });
        }
        /**
         * 获取按钮权限
         */
        function getButtonPermission(){
        	var buttons = {
            	'Button-CodeReviewManagement-add':'add',
                'Button-CodeReviewManagement-up':'up',
                'Button-CodeReviewManagement-del':'del'
            };
            var urlData = {
                'userId':LocalCache.getSession("userId"),
                'parentPermission':'ButtonCodeReviewManagement',
                'buttons':buttons
            };
           comService.getButtonPermission(urlData,$scope);
        }
        /**
         * 获取项目信息
         */
        $scope.getProjectInfo = function(){  
                codeReviewService.getProjectInfo().then(function(data) {
                    $scope.projectInfo = angular.fromJson(data.data);
                    //获取数据
                    $scope.getData('1');
                });
        };

		/**
		 * 获取项目
		 */
		 $scope.getData =function(pageNum) {
			var urlData ={
			    'cname':$scope.formRefer.cname,//项目名称
                'productLine':$scope.formRefer.productLine,//产品线名
                'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                'endTime':inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                'page':pageNum,
                'size':$scope.pages.size
			};
			codeReviewService.getData(urlData).then(function(data) {
				if (data.code === AgreeConstant.code) {
                    //项目报告
                    $scope.projectData = data.data.list;
                    // 分页信息设置
                    $scope.pages.total = data.data.total;           // 页面数据总数
                    $scope.pages.star = data.data.startRow;         // 页面起始数
                    $scope.pages.end = data.data.endRow;            // 页面结束数
                    $scope.pages.pageNum = data.data.pageNum;       //页号

				} else {
					inform.common(data.message);
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
		};
		/**
    	 * 设置列表的高度
    	 */
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 200);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 50);
 		}
 		//开始时间
        $scope.openDateStart = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = true;    //开始时间
            $scope.openedEnd = false;
        };

        //结束时间
        $scope.openDateEnd = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = false;
            $scope.openedEnd = true;    //结束时间
        };
		/**
		 * 修改信息
		 */
       $scope.editInfo = function() {
    		 var urlData = {
                'id':$scope.changeParam.id,
    			'cname': $scope.changeParam.cname,
                'date': format($scope.changeParam.date,'yyyy-MM-dd'),
                'defectDensity':$scope.changeParam.defectDensity,
                'efficiency': $scope.changeParam.efficiency,
                'judgesNum': ($scope.changeParam.judges==null||$scope.changeParam.judges==="")?0:$scope.changeParam.judges.split('、').length,
                'judges':$scope.changeParam.judges,
                'participants': $scope.changeParam.participants,
                'productLine': $scope.changeParam.productLine,
                'projectId': $scope.changeParam.projectId,
                'projectManager': $scope.changeParam.projectManager,
                'projectVersion': $scope.changeParam.projectVersion,
                'quarter': $scope.changeParam.quarter,
                'questionNum': $scope.changeParam.questionNum,
                'responsiblePerson': $scope.changeParam.responsiblePerson,
                'reviewContent': $scope.changeParam.reviewContent,
                'rowNum': $scope.changeParam.rowNum,
                'useTime': $scope.changeParam.useTime,
                'workload': $scope.changeParam.workload
             };
    		 codeReviewService.updateCodeReview(urlData).then(function(data){
    			 if(data.code === AgreeConstant.code) {
    				 inform.common(data.message);
    				 $scope.getData('1');
                     $("#edit_modal").modal("hide");
    			 } else{
    				 inform.common(data.message);
    			 }
    		 }, function(error) {
    			 inform.common(Trans("tip.requestError"));
    		 });
    	 };
    	 
    	 /**
    	  * 添加信息
    	  */
    	$scope.addInfo = function() {
            if($scope.spec.date === 'undefined' || null == $scope.spec.date){
            return inform.common('请输入走查日期');
            }
            if($scope.spec.projectId === 'undefined' || null == $scope.spec.projectId){
                return inform.common('请选择项目');
            }
    	    var urlData = {
                'date': format($scope.spec.date,'yyyy-MM-dd'),
                'defectDensity':$scope.spec.defectDensity,
                'efficiency': $scope.spec.efficiency,
                'judgesNum': ($scope.spec.judges==null||$scope.spec.judges==="")?0:$scope.spec.judges.split('、').length,
                'judges':$scope.spec.judges,
                'participants': $scope.spec.participants,
                'productLine': $scope.spec.productLine,
                'projectId': $scope.spec.projectId,
                'projectManager': $scope.spec.projectManager,
                'projectVersion': $scope.spec.projectVersion,
                'quarter': $scope.spec.quarter,
                'questionNum': $scope.spec.questionNum,
                'responsiblePerson': $scope.spec.responsiblePerson,
                'reviewContent': $scope.spec.reviewContent,
                'rowNum': $scope.spec.rowNum,
                'useTime': $scope.spec.useTime,
                'workload': $scope.spec.workload
             };
    	        codeReviewService.addCodeReview(urlData).then(function(data){
    			 if(data.code === AgreeConstant.code) {
    				 inform.common(data.message);
    				 $scope.getData('1');
    				 $("#add_modal").modal("hide");
    			 } else{
    				 inform.common(data.message);
    			 }
    		 }, function(error) {
    			 inform.common(Trans("tip.requestError"));
    		 });
    	 };


       $scope.cleamModal = function(){
    	   $scope.uplodar = {
    			   'projectId':'',
    			   'projectVersion':''
    	   }
    	   $("#form")[0].reset();
           $("#fileNameDis").text("");
       }

	   /**
	    * 修改信息弹框，str存在，就是新增
	    */ 
       $scope.popModal = function (item,str){
        if(str){
          $scope.spec = {
			    'cname':'',
                'projectId':'',
                'quarter':'',
                'productLine':'',
                'projectVersion':'',
                'reviewContent':'',
                'responsiblePerson':'',
                'projectManager':'',
                'questionNum':'',
                'rowNum':'',
      			'useTime':'',
      			'judgesNum':'',
      			'defectDensity':'',
      			'workload':'',
				'efficiency':'',
                'participants':'',
                'date':''
          };
        }else{
          $scope.changeParam = angular.copy(item);
        }
      };

        //当新增数据时选择日期，自动将季度回填
       $scope.dateToQuarter = function() {
            var month = $scope.spec.date.getMonth() + 1;
            $scope.spec.quarter = inform.dateToQuarter(month);
       };
        //当修改数据时选择日期，自动将季度回填
      $scope.dateToQuarterUpdate = function() {
            var month = $scope.changeParam.date.getMonth() + 1;
            $scope.changeParam.quarter = inform.dateToQuarter(month);
      };

      /**
       * 删除数据
       */ 
      $scope.removeCustomer = function (item) {
         var id = {
                'id':item.id
          };
        codeReviewService.deleteCodeReview(id)
              .then(function(data) {     
                if (data.code === "0000") {
                  inform.common(Trans("tip.delSuccess"));
                  $scope.getData(AgreeConstant.pageNum);
                  }else{
                     inform.common(data.message);
                  }
              }, function(error) {
                inform.common(Trans("tip.requestError"));
              });
          };

          /**
           * 删除弹框
           */ 
          $scope.open = function (item) {
              var modalInstance = $modal.open({
                templateUrl: 'myModalContent.html',
                controller: 'ModalInstanceCtrl',
                size: "sm",
                resolve: {
                  items: function() {
                    return Trans("common.deleteTip");
                  }
                }
              });
              modalInstance.result.then(function() {
                if (item !== null && item !== "") {
                  $scope.removeCustomer(item);
                }
              });
          };


          /**
         * excel下载
         */
        $scope.toExcel = function() {
            var modalInstance = $modal.open({
                 templateUrl: 'myModalContent.html',
                 controller: 'ModalInstanceCtrl',
                 size: "sm",
                 resolve: {
                     items: function() {
                         return "确定要下载吗！";
                     }
                 }
            });
            modalInstance.result.then(function() {
                //开启遮罩层
                inform.showLayer("下载中。。。。。。");
                //拼装下载内容
                var urlData={
                    'cname':$scope.formRefer.cname,//项目名称
                    'productLine':$scope.formRefer.productLine,//产品线名
                    'quarter':$scope.formRefer.quarter//季度
                };
                $http.post(
                    $rootScope.getWaySystemApi+'codeReview/toExcel',
                    urlData,
                    {headers: {
                        'Content-Type': 'application/json',
                        'Authorization':'Bearer ' + LocalCache.getSession("token")||''
                    },
                    responseType: 'arraybuffer'//防止中文乱码
                    }
                 ).success(function(data){
                    var objectUrl;
                    var aForExcel;
                    //如果是IE浏览器
                    if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                        var csvData = new Blob([data], {type:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
                        window.navigator.msSaveOrOpenBlob(csvData);
                        aForExcel = $("<a download='代码走查信息报告.xlsx'><span class='forExcel'>下载excel</span></a>");
                        $("body").append(aForExcel);
                        $(".forExcel").click();
                        aForExcel.remove();
                    }
                    //google或者火狐浏览器
                    else{
                        var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
                        objectUrl = URL.createObjectURL(blob);
                        aForExcel = $("<a download='代码走查信息报告.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href",objectUrl);
                        $("body").append(aForExcel);
                        $(".forExcel").click();
                        aForExcel.remove();
                    }
                    // 关闭遮罩层
                    inform.closeLayer();
                    inform.common("下载成功!");
                    });
               });
          };
          /**
           * 跳转至问题列表页
           * @param m 记录相关信息
           */
          $scope.viewProblem = function (m) {
        	  LocalCache.setObject("codeReviewManagement_formRefer",$scope.formRefer);
              $state.go('app.office.codeReviewProblemController', {
                  id: m.id,
                  add: $scope.add//操作权限
              });
          };

        
		/**
		 * 走查日期
		 */ 
		$scope.codeReviewDate = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.reviewDate = true; 
		};
		
        /**
         * 时间格式化
        */
        function format(time, format) {
            if(!time) {
                return null;
              }
            var t = new Date(time);
            var tf = function(i) {
                return (i < 10 ? '0' : '') + i
            };
            return format.replace(/yyyy|MM|dd|HH|mm|ss/g,function(a) {
                switch (a) {
                    case 'yyyy':
                        return tf(t.getFullYear());
                        break;
                    case 'MM':
                        return tf(t.getMonth() + 1);
                        break;
                    case 'mm':
                        return tf(t.getMinutes());
                        break;
                    case 'dd':
                        return tf(t.getDate());
                        break;
                    case 'HH':
                        return tf(t.getHours());
                        break;
                    case 'ss':
                        return tf(t.getSeconds());
                        break;
                }
            })
        }
        /**
		 * 文件选择事件
		 */
	     $scope.selectFile = function() {
	     	document.getElementById("files").click();
	     }
		/**
		 * 选择上传文件后事件
		 */
		function fileChangeEvent(e){
			var fileName = "文件名称：" + e.currentTarget.files[0].name;
			$("#fileNameDis").text(fileName);
		}
	    /**
	     * 上传文件
	     */
		$scope.submitForm = function (){
	    	var formData = new FormData(document.getElementById("form"));
	    	var file = document.querySelector('input[type=file]').files[0]; //获取文档中有类型为file的第一个input元素
	    	if(!file){
                inform.common("请先选择文件!");
                return false;
            }else if(file.size > AgreeConstant.fileSize){
                inform.common("上传文件大小不得超过2M，请分割后重新上传!");
                $("#form")[0].reset();
                $("#fileNameDis").text("");
                return false;
            }
	    	formData.append('file', file);
	    	var a = file.type;
	    	if(a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"){
	        	inform.common("请选择.xlsx类型的文档进行上传!");
	        	return false;
	        }
	    	formData.append('projectId', $scope.uplodar.projectId);
	    	formData.append('projectManager', $scope.uplodar.projectManager);
	    	formData.append('projectVersion', $scope.uplodar.projectVersion);
	    	$("#uploader_modal").modal("hide");
	        inform.uploadFile('codeReview/uploadExcel',formData,function func(result){
	                // 关闭遮罩层
	                inform.closeLayer();
	                $modal.open({
	                    templateUrl: 'errorModel.html',
	                    controller: 'ModalInstanceCtrl',
	                    size: "lg",
	                    resolve: {
	                        items: function () {
	                            return result.message;
	                        }
	                    }
	                });
	               $("#form")[0].reset();
	               $("#fileNameDis").text("");
	               $scope.getData(1);
	            });
	     }
	    /**
	     * 下载模板信息
	     */
        $scope.toExcelModule = function () {
        	inform.modalInstance("确定要下载吗?").result.then(function () {
            	inform.downLoadFile('codeReview/toExcelModule',null,'代码走读会议纪要.xlsx');
        	});
        };
        /**
	     * *************************************************************
	     *              方法声明部分                                结束
	     * *************************************************************
	     */	
	
		} ]);
})();
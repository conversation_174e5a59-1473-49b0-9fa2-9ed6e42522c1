(function () {
    app.controller("toOnlineServerEventController", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','feedbackProblemService','projectManagementService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,feedbackProblemService,projectManagementService,inform,Trans,AgreeConstant,LocalCache,$http) {

		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		$scope.limitList = AgreeConstant.limitList;
		$scope.projectName = $stateParams.projectName;
        $scope.checkedAppArry = [];
        initInfo();
        //被选中项目的集合
        $scope.proSelected=[];

        //问题来源
        $scope.problemSourceSelect = ['客户','市场','测试组','项目组','产品组','售前','售后','运维','荣鑫保障','下游项目组','其他'];
        //事故分类
        $scope.eventTypeSelect = ['程序','中间件','配置','数据库','基础设施'];
        //严重程度
        $scope.problemSeveritySelect = ['事件','轻微线上问题','一般线上问题','严重线上问题'];


		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */

		/**
		 * 初始化
		 */
    	function initInfo() {
            //获取山东新北洋集团的下级部门信息
            $scope.departmentList = [];
            $scope.departmentMap = {};
            comService.getOrgChildren('D010053').then(function(data) {
            	$scope.departmentList = comService.getDepartment(data.data);
            	 for(var j = 0; j < data.data.length; j++) {
                     $scope.departmentMap[data.data[j].orgName] = data.data[j].orgCode;
                 }
             });
            //获取员工信息
            $scope.employeeList = [];
            comService.getEmployeesByOrgId('').then(function(data) {
                if (data.data) {
                    $scope.employeeList = data.data;
                }
            });

            setTimeout(getProblemInfo,500);
    	}

    	/**
         * 获取某个反馈问题的详情
         */
        function getProblemInfo() {

        	 var urlData = {
                'problemId':$stateParams.item
        	 };
        	 feedbackProblemService.getFeedbackProblemById(urlData).then(function (data) {
             	if (data.code === AgreeConstant.code) {
             	    var itemData = data.data;
             	    //问题诊断人转换为数组
             	    var problemDiagnostician = $.trim(itemData.problemDiagnostician);
             	    //"、“替换为”，“
             	    problemDiagnostician=problemDiagnostician.replace(/、/g,",");
             	    itemData.problemDiagnostician = problemDiagnostician.split(",");
             	    //
             	    itemData.problemSeverity =itemData.problemSeverity;
                    //反馈问题详情
                    $scope.item = itemData;
                    $scope.item.productPM = "";
                    getProductPMName($scope.item.officeProjectId);
                    //影响的应用
                    $scope.checkedApplicationData = itemData.affectAppList;
                
                    $scope.proSelected = itemData.affectAppList;
                } else {
                    inform.common(data.message);
                }
            }, function () {
                inform.common(Trans("tip.requestError"));
            });
        }


        /**
         * 获取某个反馈问题的对应的Product Owner名称
         */
        function getProductPMName(projectId) {

            if(projectId ==='') {
                return;
            }
             var urlData = {
                'id':projectId
             };
             projectManagementService.getProjectInfoById(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    $scope.item.productPM = data.data.productManagerName;
                } else {
                    inform.common(data.message);
                }
            }, function () {
                inform.common(Trans("tip.requestError"));
            });

        }

        $scope.popModal = function() {
            $("#application_modal").click();
            getSoftwares();
            getApplications();
        };

        function getSoftwares(){
            var urlData = [];
             //获取软件产品
            $scope.softwareList = [];
            feedbackProblemService.getSoftwares(urlData).then(function (data) {
                if (data.data) {
                    $scope.softwareList = data.data;
                }
            });
        }

        
        $scope.resetSoftwareParams = function () {
            $scope.formRefer.searchSoftware = '';
            $scope.formRefer.searchAppName = '';
            $scope.formRefer.searchAppDesc = '';
        };
      

        function getApplications(){
            // var urlData = []
            var urlData = {
                'softwareId':'',
                'applicationName':'',
                'applicationDesc':''
             };
            feedbackProblemService.getApplications(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    if(null==data.data){
                        $scope.applicationData = {};
                        inform.common(Trans("tip.noData"));
                    } else {
                        //应用
                        $scope.applicationData = data.data;
                        angular.forEach($scope.applicationData, function (i) {    
                        angular.forEach($scope.checkedApplicationData,function(item){
                            if(item.applicationId == i.applicationId){
                                i.checked=true;
                            }
                            
                        }
                        )
                     
                        });
                    }
                } else {
                   inform.common(data.message);
                }
            },function () {
               inform.common(Trans("tip.requestError"));
            });
        }


        $scope.checkApps = function(){
            if ($scope.item.eventType = '程序'){
                if(!$scope.proSelected || $scope.proSelected.length <=0){
                    inform.common('请选择影响应用！');
                return;
                }
                $("#new_application_modal").modal('hide');
                let newarry = $scope.proSelected.slice();
                $scope.checkedApplicationData = newarry;
            }
        }

        $scope.deleterow = function(index){
            this.checkedApplicationData.splice(index,1);
            this.rows.removeAt(item);
        }

        $scope.searchAppData = function (){
            var urlData = {
                'softwareId': $scope.formRefer.searchSoftware,
                'applicationName': $scope.formRefer.searchAppName,
                'applicationDesc': $scope.formRefer.searchAppDesc
             };
            feedbackProblemService.getApplications(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    if(null==data.data){
                        $scope.applicationData = {};
                        inform.common(Trans("tip.noData"));
                    } else {
                        //应用
                        $scope.applicationData = data.data;
                        angular.forEach($scope.applicationData, function (i) {
                            angular.forEach($scope.proSelected,function(item){
                                if(item.applicationId == i.applicationId){
                                    i.checked=true;
                                }
                                
                            }

                        )
                        });
                    }
                } else {
                   inform.common(data.message);
                }
            },function () {
               inform.common(Trans("tip.requestError"));
            });
        }
      

        $scope.selectAll=function () {
            if ($scope.select_all) {
                $scope.proSelected=[];
                angular.forEach($scope.applicationData, function (i) {
                    i.checked=true;
                    $scope.proSelected.push(i);
                });
            } else {
                angular.forEach($scope.applicationData, function (i) {
                    i.checked=false;
                });
                $scope.proSelected=[];
            }
        }
        /*** 单选项目*/
        $scope.selectOne=function (i) {
            $scope.select_all=false;
            var flag=checkItemExist(i);
            if (!flag && i.checked) {
                $scope.proSelected.push(i);
            } else if (flag && !i.checked) {
                var index=$scope.proSelected.indexOf(i);
                $scope.proSelected.splice(index, 1);
            }
        };

        function checkItemExist(item){
            var flag = false;
            if ($scope.proSelected.length > 0){
                for (var i=0; i<$scope.proSelected.length; i++){
                    if($scope.proSelected[i].applicationId==item.applicationId){
                        flag = true;
                        break;
                    }
                }
            }
            return flag;
        }

        $scope.select=function (i) {
            $scope.select_all=false;
            $scope.selectAll();
            var tagNameArr = [];
            var index=$scope.proSelected.indexOf(i);
            if (index===-1) {
                $scope.proSelected.push(i);
                //tag名按照-分组
                tagNameArr = i.tagName.split("-");

                if (tagNameArr[0].match("^SV")) {
                    tagNameArr[0] = tagNameArr[0].replace("SV","V");
                }

                $scope.changeParam.name = "";
                $scope.changeParam.ext = "";
                for (var j = 0; j < tagNameArr.length; j++) {
                    //找到日期位置
                    if (tagNameArr[j].match("^[0-9]{8}$")) {
                        //从开始位置到日期位置前一位 作为版本号
                        for (var k = 0; k < j - 1; k++) {
                            if (k !== 0 && k !== j - 1) {
                                $scope.changeParam.name = $scope.changeParam.name + "-";
                            }
                            $scope.changeParam.name = $scope.changeParam.name + tagNameArr[k];
                        }
                        //从日期位置后一位到最后 作为附加信息
                        for (var m = j + 1; m < tagNameArr.length; m++) {
                            $scope.changeParam.ext = $scope.changeParam.ext + tagNameArr[m];
                            if (m !== tagNameArr.length - 1) {
                                $scope.changeParam.ext = $scope.changeParam.ext + "-";
                            }
                        }
                        break;
                    }
                }

            }
        };
        //取消所选项目
        $scope.cancel=function () {
            $scope.proSelected = [];
        };

        /**
         * 获取问题关联的停机事件
         */
        function getData(pageNum) {
            var urlData = $scope.formRefer;
            urlData.currentPage=pageNum;//当前页数
            urlData.pageSize=$scope.pages.size;//每页显示条数
            feedbackProblemService.getData(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                        if(null==data.data){
                            $scope.tableData = {};
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages();
                        } else {
                            //项目详情
                            $scope.tableData = data.data.list;
                           //分页信息设置
                           $scope.pages.total = data.data.total;
                           $scope.pages.star = data.data.startRow;
                           $scope.pages.end = data.data.endRow;
                           $scope.pages.pageNum = data.data.pageNum;
                       }
                    } else {
                        inform.common(data.message);
                    }
                },
                function () {
                    inform.common(Trans("tip.requestError"));
                });
       }

        /**
         * 提交详情信息,存在就更新，不存在就新增
         */
        $scope.submitEventFun = function (){

            var urlData = $scope.item;
            urlData.projectName = $scope.projectName;
            //问题诊断人员
            urlData.problemDiagnostician=$scope.item.problemDiagnostician.toString();
            //事故分类
            var eventType = $scope.item.eventType;
            var affectApps = '';
            for(var i = 0; i < $scope.proSelected.length; i++) {
                affectApps = affectApps +  $scope.proSelected[i].applicationId +','
            }
            if(eventType == "程序"){
                if (affectApps == ""){
                    inform.common('请选择影响的应用');
                    return;
                }
            }
            urlData.affectApp = affectApps;
      
            feedbackProblemService.saveOnlineServerEvent(urlData).then(function (result) {
                if (result.code === AgreeConstant.code) {
                        inform.common('生成线上停机事故成功！');
                        window.history.go(-1);
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
        }
	 		/**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */

	}]);
})();
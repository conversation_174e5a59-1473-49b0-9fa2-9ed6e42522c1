(function () {
    'use strict';
    app.factory('deptBoardFactory', deptBoardFactory);

    deptBoardFactory.$inject=['inform', '$stateParams', '$state', 'AgreeConstant'];

    function deptBoardFactory(inform, $stateParams, $state, AgreeConstant) {
        return {
            init: init,
            initTime: initTime,
            chartShowLoading: chartShowLoading,
            chartHideLoading: chartHideLoading,
            chartHideClear: chartHideClear,
            formatterCall: formatterCall,
            showPie: showPie,
            showBar: showBar,
            sortData: sortData,
            activeClass: activeClass,
            activeTitleClass: activeTitleClass,
        };

        // 初始化数据
        function init(scope, type) {
            type = type || '1';
            // 参数对象
            scope.formRefer = {};

            // 时间段选择部分
            scope.timeSelect=['前一月至今', '前一季度', '上半年', '下半年', '本年度', '上一年度'];
            scope.initTime = function (m){
                initTime(scope, m)
            };
            initTime(scope, '本年度');

            scope.type=type;
            if($stateParams.type){
                scope.type = $stateParams.type;
            }
        }

        // 时间段选择
        function initTime(scope, flag){
            scope.butFlag = flag;
            var date = new Date();
            var y = date.getFullYear();  //当前年份
            //设置为1号，防止31号时获取到当月
            date.setDate(1);
            if('前一月至今' === scope.butFlag){
                scope.formRefer.endTime = inform.format(date,"yyyy-MM");
                date.setMonth(date.getMonth()-1);
                scope.formRefer.startTime = inform.format(date,"yyyy-MM");
            }
            if('前一季度' === scope.butFlag){
                //当前月份
                var m = new Date().getMonth();
                //当前季度
                var q = parseInt(m / 3);
                //上一季度的开始日期
                scope.formRefer.startTime = inform.format(new Date(y, (q - 1) * 3, 1),"yyyy-MM");
                //上一季度的结束日期
                scope.formRefer.endTime = inform.format(new Date(y, q * 3, 0),"yyyy-MM");
            }
            if('上半年'===scope.butFlag){
                scope.formRefer.startTime = y+'-01';
                scope.formRefer.endTime = y+'-06';
            }
            if('下半年'===scope.butFlag){
                scope.formRefer.startTime = y+'-07';
                scope.formRefer.endTime = y+'-12';
            }
            if('本年度'===scope.butFlag){
                scope.formRefer.startTime = y+'-01';
                scope.formRefer.endTime = y+'-12';
            }
            if('上一年度'===scope.butFlag){
                scope.formRefer.startTime = parseInt(y) - 1+'-01';
                scope.formRefer.endTime = parseInt(y) - 1+'-12';
            }
        }

        // 图表显示Loading
        function chartShowLoading(chart) {
            chart.showLoading({
                text: "数据加载中...",
                color: "#3174F2",
                textColor: "#000",
                maskColor: "rgba(255, 255, 255, 0)",
                zlevel: 0,
            });
        }

        // 图表隐藏Loading
        function chartHideLoading(chart) {
            chart.hideLoading();
        }

        // 清除图表
        function chartHideClear(chart) {
            chart.clear();
        }

        function formatterCall(params, ticket, callback, differentName, unit, reverse) {
            unit = unit || '次';
            reverse = reverse || false;
            var htmlStr = '';
            for(var i=0;i<params.length;i++){
                var param = params[i];
                var xName = param.name;//x轴的名称
                var seriesName = param.seriesName;//图例名称
                var value = param.value;//y轴值
                if(i===0){
                    htmlStr += xName + '<br/>';//x轴的名称
                }
                if (value !== undefined && value !== null) {
                    htmlStr +='<div>';
                    htmlStr += param.marker;
                    //圆点后面显示的文本
                    htmlStr += seriesName;
                    htmlStr += '<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:700">';
                    if (!reverse){
                        if(differentName !== seriesName){
                            htmlStr += +value + unit;
                        }else{
                            htmlStr += +value+ '%';
                        }
                    } else {
                        if(differentName === seriesName){
                            htmlStr += +value + '%';
                        }else{
                            htmlStr += + value + unit;
                        }
                    }
                    htmlStr += '</span>';
                    htmlStr += '</div>';
                }
            }
            return htmlStr;
        }

        function getPieDefaultSetting() {
            return {
                title: '默认标题',
                titleFontSize: 16,
                type:'name',
                value: 'value',
                otherData: 'perPerson',
                firstContent: '输出评审',
                firstContentUnit: '次',
                secondContent: '人均',
                secondContentUnit: '个',
                needSecondContent: true
            }
        }
        // 图表饼图显示
        function showPie(currentChart,data, mySetting){
            var setting = Object.assign(getPieDefaultSetting(), mySetting);
            var dealData = [];
            var option = {};
            if (data.length) {
                angular.forEach(data, function (eachData) {
                    dealData.push({
                        name: eachData[setting.type],
                        value: eachData[setting.value],
                        perPerson: eachData[setting.otherData]
                    })
                });
                option = {
                    title:{
                        text:setting.title,
                        textStyle:{
                            fontSize: setting.titleFontSize,
                            color: '#333'
                        },
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: function (data) {
                            if (setting.needSecondContent){
                                return (
                                    data.name +
                                    '<br/>' +
                                    setting.firstContent +
                                    data.value +
                                    setting.firstContentUnit +
                                    '<br/>' +
                                    setting.secondContent +
                                    data.data.perPerson +
                                    setting.secondContentUnit
                                );
                            }
                            return (
                                data.name +
                                '<br/>' +
                                setting.firstContent +
                                data.value +
                                setting.firstContentUnit
                            );
                        }
                    },
                    series: [
                        {
                            name: 'Access From',
                            type: 'pie',
                            radius: '50%',
                            label: {
                                normal: {
                                    formatter: function (data) {
                                        if (setting.needSecondContent){
                                            return (
                                                data.name +
                                                '\n' +
                                                setting.firstContent +
                                                data.value +
                                                setting.firstContentUnit +
                                                '\n' +
                                                setting.secondContent +
                                                data.data.perPerson +
                                                setting.secondContentUnit
                                            );
                                        }
                                        return (
                                            data.name +
                                            '\n' +
                                            setting.firstContent +
                                            data.value +
                                            setting.firstContentUnit
                                        );
                                    },
                                    textStyle: {
                                        fontWeight: 'normal',
                                        fontSize: 12
                                    }
                                }
                            },
                            data: dealData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };
            } else {
                option = {
                    title: [{
                        text: setting.title,
                        textStyle:{
                            fontSize: 12,
                            color: '#333'
                        },
                    },{
                        text: "暂无数据",
                        left: 'center',
                        top: 'center',
                        color: '#333',
                        textStyle: {
                            fontSize: 20
                        }
                    }]
                }
            }
            currentChart.setOption(option, true);
        }

        function getBarDefaultSetting() {
            return {
                title: '默认标题',
                xType: 'type',
                yType: 'value',
                fontSize: 12,
                left: 'center',
                grid: {
                    gridLeft: '6%',
                    gridRight: '6%',
                },
                needCustomSeries: false,
                needDataZoom: false,
                unit: '人年',
                minInterval: 0
            }
        }
        // 图表柱状图显示
        function showBar(currentChart,data, mySetting){
            var setting = Object.assign(getBarDefaultSetting(), mySetting);
            var xData=[];
            var yData=[];
            var option = {};
            if (data.length) {
                angular.forEach(data, function (eachData) {
                    xData.push(eachData[setting.xType]);
                    if (setting.needCustomSeries) {
                        eachData.value = eachData[setting.yType]
                        yData.push(eachData);
                    } else {
                        yData.push(eachData[setting.yType]);
                    }
                });
                option = {
                    title:{
                        text:setting.title,
                        textStyle:{
                            fontSize: setting.fontSize,
                            color: '#333'
                        },
                        left: setting.left
                    },
                    grid:{
                        left:setting.grid.gridLeft,
                        right:setting.grid.gridRight,
                        bottom:'0',
                        containLabel:true
                    },
                    xAxis: [{
                        type: 'category',
                        data: xData,
                        axisPointer: {
                            type: 'shadow'
                        },
                        axisLabel: {
                            rotate: 25
                        }
                    }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            axisLabel:{formatter:'{value} '+ setting.unit}
                        }
                    ],
                    series: [
                        {
                            data: yData,
                            type: 'bar',
                            barMaxWidth: '30%',
                            label: {
                                show: true,
                                position: 'inside'
                            }
                        }

                    ]
                };
                if (setting.needDataZoom) {
                    var zoom = 60;
                    option.dataZoom = [
                        {
                            type: 'slider',
                            showDetail: false,
                            height: 12,
                            bottom: 8,
                            start: 0,
                            end: zoom,
                            minSpan: 30
                        }
                    ]
                }
                if (setting.needCustomSeries){
                    option.series[0].label.formatter = function (params) {
                        if (params.data.rate){
                            if (params.data.rate.indexOf('%') !== -1){
                                return params.value + setting.unit + '\n' + params.data.rate;
                            }
                            return params.value + setting.unit + '\n' + params.data.rate + '%';
                        }
                        return params.value + setting.unit;
                    }
                    option.series[0].label.position = 'top';
                }
                if (setting.minInterval) {
                    option.yAxis[0].minInterval = setting.minInterval;
                }
            } else {
                option = {
                    title: [{
                        text: setting.title,
                        textStyle:{
                            fontSize: setting.fontSize,
                            color: '#333'
                        },
                    },{
                        text: "暂无数据",
                        left: 'center',
                        top: 'center',
                        color: '#333',
                        textStyle: {
                            fontSize: 20
                        }
                    }]
                }
            }
            currentChart.setOption(option, true);
        }

        /**
            scope: 当前作用域
            dataList: 要处理的列表数据数组
            type: 当前点击排序的属性
            sort: 当前点击的排序的方式
         */
        function sortData (scope, type, isNumber, sort, dataList) {
            scope.type = type;
            scope.sort = sort;
            if (sort === 'asc'){
                if (!isNumber) {
                    dataList.sort(function (a, b) {
                        return a[type].localeCompare(b[type])
                    })
                } else {
                    dataList.sort(function (a, b) {
                        return parseFloat(a[type]) - parseFloat(b[type]);
                    })
                }
            } else {
                if (!isNumber) {
                    dataList.sort(function (a, b) {
                        return b[type].localeCompare(a[type])
                    })
                } else {
                    dataList.sort(function (a, b) {
                        return parseFloat(b[type]) - parseFloat(a[type]);
                    })
                }
            }
        }
        /**
            type: 单元格的列对应的属性
            sort: 排序方式
            currentType: 当前点击排序的属性
            currentSort: 当前点击的排序的方式
         */
        function activeClass (type, sort, currentType, currentSort){
            if(currentType === type && currentSort === sort){
                return "red";
            }
            return "";
        }
        /**
            type: 单元格的列对应的属性
            currentType: 当前点击排序的属性
         */
        function activeTitleClass (type, currentType) {
            if (currentType === type) {
                return 'red';
            }
            return '';
        }
    }
})();

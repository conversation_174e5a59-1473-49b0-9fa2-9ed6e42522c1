(function () {
    app.controller("baseLineFileManagement", ['$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant','$state','$stateParams','baseLineService',
        function ($rootScope, $scope, inform, Trans, AgreeConstant,$state,$stateParams, baseLineService) {
            $scope.formInput={};
            $scope.stageList = [
                {name:"立项阶段", value:"立项基线"},
                {name:"需求阶段", value:"需求基线"},
                {name:"设计阶段", value:"设计基线"},
                {name:"测试阶段", value:"测试基线"},
                {name:"发布阶段", value:"发布基线"}
            ];
            $scope.keyList = [{name:"是", value:"1"},{name:"否", value:"0"}];
            $scope.isKeyMap =  {
                "1": '是',
                "0": '否'
            };
            $scope.stageMap = {
                "立项基线" : "立项阶段",
                "需求基线" : "需求阶段",
                "设计基线" : "设计阶段",
                "测试基线" : "测试阶段",
                "发布基线" : "发布阶段"
            }
            $scope.formInput.isKey = "1";
            $scope.baseLine = JSON.parse($stateParams.baseLine);

            //分页数据
            $scope.pages = {
                goNum:null, // 初始化跳转页码
                star:0, //开始条数
                end:0, //结束条数
                total:0, // 总条数
                size:"50", //每页条数
                pageNum:AgreeConstant.pageNum //默认页
            };


            getData(1);
            $scope.getData = getData;

            function getData(pageNum) {
                var urlData = $scope.formInput;
                urlData.baseLineId = $scope.baseLine.id;
                urlData.currentPage=pageNum;//当前页数
                urlData.pageSize=$scope.pages.size;//每页显示条数
                baseLineService.getBaseLineFile(urlData).then(function (data) {

                    if (data.code === AgreeConstant.code) {
                        if(null==data.data){
                            $scope.itemList = {};
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages();
                            $scope.pages.size = "50";
                        } else {

                            $scope.itemList = data.data.list;
                            //分页信息设置
                            $scope.pages.total = data.data.total;
                            $scope.pages.star = data.data.startRow;
                            $scope.pages.end = data.data.endRow;
                            $scope.pages.pageNum = data.data.pageNum;
                        }
                    } else {
                        inform.common(data.message);
                    }
                },function () {
                    inform.common(Trans("tip.requestError"));
                });
            }

            $scope.goBack = function () {
                $state.go('app.office.baseLineManagement');
            }

            //重置
            $scope.reset = function() {
                $scope.formInput = {};
            }
        }]);
})();
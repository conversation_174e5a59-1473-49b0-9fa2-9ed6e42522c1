/*
 * @Author: fubaole
 * @Date:   2017-10-16 10:26:06
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-19 10:12:02
 */

(function() {
    'use strict';
    app.controller("personnel_Change", ['$rootScope', '$scope', '$state', '$stateParams', 'inform', 'Trans', 'SystemService', 'AgreeConstant',
        function($rootScope, $scope, $state, $stateParams, inform, Trans, SystemService, AgreeConstant) {
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.openDate = openDate; // 日期插件
            $scope.toggleMin = toggleMin;
            $scope.onSubmit = onSubmit; // 修改保存操作
            getData(); // 获取人员信息
            $scope.toggleMin();

            // 根据ID 获取人员信息
            function getData() {
                SystemService.getEmployeeById($stateParams.employeeId)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.person = data.result;
                        } else {
                            inform.common(data.message);
                        }
                    }, function() {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 日期插件
            function openDate($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.opened = true;
            }

            $scope.dateOptions = {
                formatYear: 'yy',
                class: 'datepicker',
                showWeeks: false
            };

            function toggleMin() {
                $scope.currentDate = $scope.currentDate ? null : new Date();
            }

            // 保存人员信息修改
            function onSubmit() {
                // if ($scope.person.isUser===1) { $scope.person.isUserStr = '是'; }
//                if ($scope.person.birthday.toString().indexOf("-")===-1) {
//                    $scope.person.birthday = inform.formatDate($scope.person.birthday, "yyyy-MM-dd");
//                }
                SystemService.saveOrUpdateEmployee($scope.person)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            inform.common(Trans("tip.saveSuccess"));
                            $state.go('app.system.personnel_Management');
                        } else {
                            inform.common(data.message);
                        }
                    }, function() {
                        inform.common(Trans("tip.requestError"));
                    });
            }

        }
    ]);
})();
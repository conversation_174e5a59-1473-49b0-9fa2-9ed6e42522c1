(function() {
  'use strict';
  app.controller("resources", ['$interval', '$rootScope', '$scope', '$state', 'Microservice', 'inform', '$timeout', '$stateParams','Trans',
    function($interval, $rootScope, $scope, $state, Microservice, inform, $timeout, $stateParams,Trans) {

      $scope.getRoutes = getRoutes;
      $scope.getMetrics = getMetrics;
      $scope.toDumpPage = toDumpPage;
      $scope.changeRouteInfo = changeRouteInfo;
      $scope.timer = 0;
      $scope.servicesStats = {};
      $scope.getRoutes();
      $scope.appName = "register";
      $scope.result = [{ "appName": "register" }];


      $scope.$watch('$scope.timer', function(newValue, oldValue) {
        if (newValue !== oldValue) {
          $interval.cancel($scope.interval);
          if (newValue !== '0') {
            $scope.interval = $interval(function() {
              // console.log($scope.timer);
              $scope.getRoutes();
              $scope.getMetrics();
            }, $scope.timer);
          }
        }
      });


      $scope.$on('$destroy', function() {
        $interval.cancel($scope.interval);
        //$interval.stop(interval);
      });

      function toDumpPage() {
        $state.go('app.service.resources-detail', { "appName": $scope.appName });
      }

      function changeRouteInfo() {
        getMetrics();
      }

      function getRoutes() {
        Microservice.getRoutes()
          .then(function(data) {
            for (var i = 0; i < data.length; i++) {
              $scope.result.push(data[i]);
            }

            console.log(data);
            $scope.getMetrics();
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      function getMetrics() {
        Microservice.getMetrics($scope.appName)
          .then(function(data) {
            console.info(data);
            $scope.metrics = data;
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      $scope.$watch('$scope.metrics', function(newValue) {
        $scope.servicesStats = {};
        if (newValue) {
          angular.forEach(newValue.timers, function(value, key) {
            if (key.indexOf('web.rest') !== -1 || key.indexOf('service') !== -1) {
              $scope.servicesStats[key] = value;
            }
          });
        }
      });

    }
  ]);

})();
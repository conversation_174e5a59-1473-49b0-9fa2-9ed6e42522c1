(function() {
    'use strict';
    app.factory('PLMActiveService', PLMActiveService);
    PLMActiveService.$inject=["HttpService",'$rootScope'];

    function PLMActiveService(HttpService,$rootScope){

        /**
         * 查询PLM项目活动统计数据
         */
        function getPLMBoardTotal(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'DeptPLMActive/getPLMBoard',urlData);
        }

        /**
         * 查询不同状态下PLM项目活动数量
         */
        function getDeptPLMCount(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'DeptPLMActive/getPLMActiveCount',urlData);
        }

        /**
         * 获取PLM项目活动详情
         */
        function getDeptPLMInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'DeptPLMActive/getPLMActiveData',urlData);
        }

        return {
            getPLMBoardTotal: getPLMBoardTotal,
            getDeptPLMCount: getDeptPLMCount,
            getDeptPLMInfo: getDeptPLMInfo,
        };
    }
})();
/*
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date:   2019-01-10 15:20:05
 * @Last Modified by:   liu<PERSON><PERSON>
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
    'use strict';
  app.factory('codeService', codeService);
  codeService.$inject=["HttpService",'$rootScope'];

  function codeService(HttpService,$rootScope){
    
    var service={
        getAllProject : getAllProject,
        getAllProjectProperty : getAllProjectProperty,
        toExcel : toExcel,
        getAllProjectConfig : getAllProjectConfig,
        codeManagementTimerTask:codeManagementTimerTask,
        getProjectNameByConfig:getProjectNameByConfig,
        getProjectAllInformation : getProjectAllInformation,
        getAvgScore : getAvgScore,
        getTEInfo:getTEInfo,
        saveAddDataTE:saveAddDataTE,
        getHistoryInfo:getHistoryInfo,
        closeRisk:closeRisk,
        updateRiskNote:updateRiskNote
    };
    return service;
    /**
     * 获取平均分数
     */
    function getAvgScore(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'code/getAvgScore', urlData);
    }
    /**
     * 从配置中获取项目，再去Sonar上查询具体属性
     * @param urlData 查询参数
     */
    function getProjectNameByConfig(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'code/getProjectNameByConfig', urlData);
    }
    /**
     * 从配置中获取项目全部属性
     * @param urlData 查询参数
     */
    function getProjectAllInformation(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'code/getProjectAllInformation', urlData);
    }

    function codeManagementTimerTask() {
        return HttpService.post($rootScope.getWaySystemApi + 'code/codeManagementTimerTask');
    }
    
    /**
     * 将选中项目生成表格
     * @param projectNames 被选中项目集合
     * */
    function toExcel(projectNames) {
    	return HttpService.post($rootScope.getWaySystemApi + 'code/toExcelByRar', projectNames);
    }

    /**
     * 根据查询条件获取项目
     * @param urlData 查询条件
     * */
    function getAllProject(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'code/getAllProject', urlData);
    }
    /**
     * 根据项目名集合获得项目的属性
     * @param projectNames 项目名集合
     * */
    function getAllProjectProperty(projectNames) {
    	return HttpService.post($rootScope.getWaySystemApi + 'code/getAllProjectProperty', projectNames);
    }
    /**
     * 根据查询条件获取项目
     * 查询条件中存在项目类别，类别存在于数据库配置中，需要先获取数据库中的数据库
     * 
     */
    function getAllProjectConfig(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'code/getAllProjectConfig', urlData);
    }
      /**
       * 获取技术评价信息
       *
       */
      function getTEInfo(urlData) {
          return HttpService.post($rootScope.getWaySystemApi + 'code/getTEInfo', urlData);
      }

      /**
       * 保存技术评价信息
       *
       */
      function saveAddDataTE(urlData) {
          return HttpService.post($rootScope.getWaySystemApi + 'code/saveAddDataTE', urlData);
      }
      
      /**
       * 查询历史信息
       */
      function getHistoryInfo(urlData){
    	  return HttpService.post($rootScope.getWaySystemApi + 'code/getCodeHistoryInfo', urlData);
      }
      //关闭风险
      function closeRisk(urlData){
          return HttpService.post($rootScope.getWaySystemApi + 'code/closeRisk', urlData);
      }
      //更新风险描述
      function updateRiskNote(urlData){
          return HttpService.post($rootScope.getWaySystemApi + 'code/updateRiskNote', urlData);
      }
  }
})();

(function () {
    app.controller("personalReviewExportController", ['$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'personalDataBoardService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function ($rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, personalDataBoardService, inform, Trans, AgreeConstant, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //页面数据
			$scope.formRefer = {};
            //设置列表的高度
            setDivHeight();
            $scope.resList = [
                {param_code:"0", param_value:"有条件通过"},
                {param_code:"1", param_value:"通过"},
                {param_code:"2", param_value:"不通过"},
                {param_code:"3", param_value:"未评审"}
            ];
            //是否跟踪展示Map
            $scope.trackMap = {
                "0": '正式评审',
                "1": '预评审'
            };
            //评审方式展示Map
            $scope.reviewTypeMap = {
                "0": '邮件',
                "1": '会议'
            };
            //评审结果展示Map
            $scope.resMap = {
                "0": '有条件通过',
                "1": '通过',
                "2": '不通过',
                "3": '未评审'
            };
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.getData = getData;
            $scope.reset = reset;
            //初始化信息
            initData();

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 初始化
             */
            function initData() {
                //获取当前登录者的empId
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ? $scope.sessionEmpId : $stateParams.empId;
                //获取当前登录者的中文名
                $scope.formRefer.employeeName = LocalCache.getSession('employeeName');
                var person = LocalCache.getObject('personDataBoardEmployee');
                if(person.name){
                    $scope.formRefer.employeeName = person.name;
                }
                reset();
                getData();
            }

            /**
             * 获取同行评审关联信息
             */
            function getData() {
                var urlData = {
                    'liablePerson':$scope.formRefer.employeeName,
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间
                    'reviewTheme':$scope.formRefer.reviewTheme,
                    'reviewType':$scope.formRefer.reviewType,
                    'isReview':$scope.formRefer.isReview,
                    'reviewResult':$scope.formRefer.reviewResult
                }
                $scope.tableData = [];
                $scope.allReviewNum = 0;
                $scope.passNum = 0;
                $scope.passRate = 0;
                personalDataBoardService.getPersonalReviewExport(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.tableData = data.data;
                                $scope.allReviewNum = $scope.tableData.length;
                                if($scope.allReviewNum>0){
                                    angular.forEach($scope.tableData,function(item){
                                      if(item.reviewResult==='0' || item.reviewResult==='1' ){
                                        $scope.passNum = $scope.passNum+1;
                                      }
                                    });
                                    $scope.passRate = ($scope.passNum/$scope.allReviewNum*100).toFixed(2)
                                }
                            } else {
								inform.common(Trans("tip.noData"));
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 210);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 40);
                $("#divTBDis1").height(divHeight);
                $("#subDivTBDis1").height(divHeight - 40);
            }

			function reset() {
                $scope.formRefer.startTime = inform.format(new Date(),"yyyy")+'-01-01';
                $scope.formRefer.endTime = '';
                $scope.formRefer.reviewType = '1';
                $scope.formRefer.isReview = '0';
                $scope.formRefer.reviewTheme = '';
                $scope.formRefer.reviewResult = '';
            }

            $scope.goback = function () {
                $state.go('app.office.personKpi');
            };

            //评审时间 -开始
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = true;
                $scope.formRefer.openedEnd = false;
            };
            //评审时间 -结束
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = false;
                $scope.formRefer.openedEnd = true;
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

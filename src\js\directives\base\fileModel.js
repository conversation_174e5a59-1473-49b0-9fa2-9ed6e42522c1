/*
* @Author: fubaole
* @Date:   2017-10-23 10:10:52
* @Last Modified by:   fubaole
* @Last Modified time: 2017-11-02 09:42:30
*/

(function () {
    'use strict';
    app.directive('fileModel', ['$parse', function ($parse) {
        return {
            restrict: 'A',
            link: function (scope, element, attrs, ngModel) {
                var model = $parse(attrs.fileModel);
                var modelSetter = model.assign;
                element.bind('change', function (event) {
                    scope.$apply(function () {
                        modelSetter(scope, element[0].files[0]);
                    });
                    //附件预览
                    scope.file = (event.srcElement || event.target).files[0];
                    scope.getFile();
                });
            }
        };
    }]);

})();
(function () {
    'use strict';
    app.controller("eChartForProjectCtrl", ['$scope', '$rootScope', 'inform', 'Trans','AgreeConstant',
        function ($scope, $rootScope, inform, Trans, AgreeConstant) {
    	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	//设置饼状图
		eChartForProjectFun();
    	/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
        /**
         * 设置饼状图数据
         */
        function eChartForProjectFun() {
        	$scope.eChartForProjectType = $scope.eChartForProjectType ? $scope.eChartForProjectType : echarts.init(document.getElementById('eChartForProject'));
        	$scope.payOption = {
        		//标题
        		title:{
        			text:'团队KPI信息',
        		    top:'bottom',
        		    left:'center',
        		    textStyle:{
        		    	fontSize: 14,
        		        fontWeight: '',
        		        color: '#333'
        		    },
        		},
        		//提示框，鼠标悬浮交互时的信息提示
            	tooltip: {
                	trigger: 'item',
                	formatter: '{a} <br/>{b}: {c}  ({d}%) '
            	},
            	//图例属性，以饼状图为例，用来说明饼状图每个扇区，data与下边series中data相匹配
               legend: {
                    orient: 'vertical',
                    left: 10,
                    data: ['交付能力','交付质量','交付过程质量','部门贡献']
                },
                series: [
                    {
						name: '一级指标',
						type: 'pie',
						selectedMode: 'single',
						radius: [0, '45%'],

						label: {
							normal: {
								position: 'inner'
							}
						},
						labelLine: {
							show: false
						},
                    	data: [
                    		{value: 350, name: '交付能力', selected: true},
                    		{value: 250, name: '交付质量'},
                    		{value: 150, name: '交付过程质量'},
                    		{value: 250, name: '部门贡献'}
                    	]
                    }, {
						name: '二级指标',//tooltip提示框中显示内容
						type: 'pie',//图形类型，如饼状图，柱状图等
						radius: ['55%', '80%'],//饼图的半径，数组的第一项是内半径，第二项是外半径。支持百分比。
						center: ["50%", "50%"], //这个属性调整图像的位置
						//饼图图形上的文本标签，可用于说明图形的一些数据信息，比如值，名称等。
						label: {
							formatter: '{a|{a}}{abg|}\n{hr|}\n  {b|{b}：}{c}  {per|{d}%}  ',
							backgroundColor: '#eee',
							borderColor: '#aaa',
							borderWidth: 1,
							borderRadius: 4,
							rich: {
								a: {
									color: '#999',
									lineHeight: 22,
									align: 'center'
								},
								hr: {
									borderColor: '#aaa',
									width: '100%',
									borderWidth: 0.5,
									height: 0
								},
								b: {
									fontSize: 16,
									lineHeight: 33
								},
								per: {
									color: '#eee',
									backgroundColor: '#334455',
									padding: [2, 4],
									borderRadius: 2
								}
							}
						},
						data: [
							{value: 180, name: '冲刺成功率'},
							{value: 170, name: '团队能力评价'},
							{value: 250, name: '线上问题数量及严重程度'},
							{value: 50, name: '开发过程bug的reopen率'},
							{value: 50, name: '过程评价'},
							{value: 50, name: '代码评价'},
							{value: 250, name: '部门贡献'}
						]
					}
                ]
           };
           $scope.eChartForProjectType.setOption($scope.payOption, true);
			$scope.eChartForProjectType.on('click', function (params) {
				alert(params.data.name);
			});
       }
       /**
        * 当窗体大小变化时，修改图例大小
        */
       window.addEventListener("resize", function () {
           if ($scope.eChartForProjectType) { $scope.eChartForProjectType.resize(); }
       });
       /**
		 * *************************************************************
		 *              方法声明部分                                 结束
		 * *************************************************************
		 */	
        }
    ]);
})();
(function() {
    'use strict';
    app.controller("qualityAccidentManagementController", ['comService', '$rootScope', '$stateParams', '$scope', 'qualityAccidentManagementService','$modal','inform', 'Trans', 'AgreeConstant','$state', 'LocalCache', '$http',
        function(comService, $rootScope, $stateParams,$scope, qualityAccidentManagementService,$modal,inform, Trans, AgreeConstant,$state, LocalCache, $http) {
    	
    	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	// 引入正则校验配置
        $scope.limitList = AgreeConstant.limitList;
        $scope.time = {};
        // 列表数据
        $scope.pageData = [];
        //新增数据对象
        $scope.addParam = {};
        //修改数据对象
        $scope.updateParam = {};
        //事故严重程度下拉框
        $scope.severityList = [{
                'id':'1',
                'name':'轻微'
            },{
                'id':'2',
                'name':'一般'
            },{
                'id':'3',
                'name':'严重'
            }];
        //查询条件对象
        $scope.searchObject = {};
         
          //设置列表的高度
          setDivHeight();
    	  //窗体大小变化时重新计算高度
    	  $(window).resize(setDivHeight);
    	  
    	  //初始化部门信息
    	  getDeptList();
    	  
    	  //初始化分页数据
  		  $scope.pages = inform.initPages();
  		  $scope.number = $scope.pages.pageNum;
  		  //初始化页面
  		  $scope.getData = getData;
  		  getData($scope.pages.pageNum);
  		  //初始化当前时间
            $scope.datepicker = {
                currentDate: new Date()
            };
  		  //定义排序对象
          $scope.orderObj = {
              title: '$index',
              desc: true,
              order: function(str) {
                  $scope.orderObj.title = str;
                  $scope.orderObj.desc = !$scope.orderObj.desc;
              }
          };
    	  /**
  		   * *************************************************************
  		   *              初始化部分                                 结束
  		   * *************************************************************
  		   */	
  	  	
  		  /**
  		   * *************************************************************
  		   *              方法声明部分                                 开始
  		   * *************************************************************
  		   */	

          /**
           * 获取系研二级部门信息
           */
          function getDeptList() { 
            //获取系统集成研发中心的下级部门信息
            $scope.deptList = [];
            $scope.oneorgList = [];
            comService.getOrgChildren('D010053').then(function(data) {
                $scope.deptList = comService.getDepartment(data.data);
            });

            //获取员工信息
            $scope.employeeList = [];
            $scope.employeeMap = [];
            comService.getEmployeesByOrgId('').then(function(data) {
              if (data.data) {
                  $scope.employeeList = data.data;
                  angular.forEach($scope.employeeList,function (employee) {
                      $scope.employeeMap[employee.employeeNo] = employee.realName;
                  });
              }
            });
            //获取项目下拉框
    		 $scope.projectIdList = [];
    		 comService.getProjectsByLineOffice('').then(function (data) {
                 $scope.projectIdList =data.data;
             });
            //事故类型
            $scope.accidentTypeObject = [];
            $scope.accidentTypeMap = [];
            comService.queryEffectiveParam('accident_type', 'accident_type').then(function (data) {
              if (data.data) {
                  $scope.accidentTypeObject = data.data;
                  angular.forEach($scope.accidentTypeObject,function (res) {
                      $scope.accidentTypeMap[res.paramCode] = res.paramValue;
                  });
              }
            });

            //事故原因
            $scope.accidentCauseObject = [];
            $scope.accidentCauseMap = [];
            comService.queryEffectiveParam('accident_cause', 'accident_cause').then(function (data) {
              if (data.data) {
                  $scope.accidentCauseObject = data.data;
                  angular.forEach($scope.accidentCauseObject,function (res) {
                      $scope.accidentCauseMap[res.paramCode] = res.paramValue;
                  });
              }
            });

          }

          /**
           * 查询条件中的开始时间
           */
          $scope.openDateStart = function($event) {
              $event.preventDefault();
              $event.stopPropagation();
              //仅查询中的开始时间显示控件内容
              $scope.time.openedStart = true;
              $scope.time.openedEnd = false;
              $scope.time.occurTimeAdd = false;
              $scope.time.occurTimeUpdate = false;
          };

          /**
           * 查询条件中的结束时间
           */
          $scope.openDateEnd = function($event) {
              $event.preventDefault();
              $event.stopPropagation();
              $scope.time.openedStart = false;
              $scope.time.openedEnd = true;
              $scope.time.occurTimeAdd = false;
              $scope.time.occurTimeUpdate = false;
          };

          /**
           * 新增弹框中的时间
           */
          $scope.openOccurTimeAdd = function($event) {
              $event.preventDefault();
              $event.stopPropagation();
              $scope.time.openedStart = false;
              $scope.time.openedEnd = false;
              $scope.time.occurTimeAdd = true;
              $scope.time.occurTimeUpdate = false;
          };

          /**
           * 修改弹框中的时间
           */
          $scope.openOccurTimeUpdate = function($event) {
              $event.preventDefault();
              $event.stopPropagation();
              $scope.time.openedStart = false;
              $scope.time.openedEnd = false;
              $scope.time.occurTimeAdd = false;
              $scope.time.occurTimeUpdate = true;
          };

          /**
           * 重置查询条件
           */
          $scope.clearParams = function() {
              $scope.searchObject = {};
          };
          
          /**
      	   * 设置列表的高度
      	   */
   		  function setDivHeight(){
   			  //网页可见区域高度
   			  var clientHeight = document.body.clientHeight;
   			  var divHeight = clientHeight - (150 + 185);
   			  $("#divTBDis").height(divHeight);
   			  $("#subDivTBDis").height(divHeight - 100);
   		  }
   		  
   		  /**
   		   * 分页查询
   		   */
          function getData(pageNum) {
              //拼装查询条件
              var params = {
                  deptName:$scope.searchObject.deptName,
                  accidentTitle:$scope.searchObject.accidentTitle,
                  accidentType:$scope.searchObject.accidentType,
                  accidentCause:$scope.searchObject.accidentCause,
                  occurTimeStart:inform.format($scope.searchObject.occurTimeStart,'yyyy-MM-dd HH:mm:ss'),
                  occurTimeEnd:inform.format($scope.searchObject.occurTimeEnd,'yyyy-MM-dd HH:mm:ss'),
                  respPerson:$scope.searchObject.respPerson,
                  proCname:$scope.searchObject.proCname,
                  page: pageNum,// 分页页数
                  size: $scope.pages.size// 分页每页大小
              };
              //获取数据
              qualityAccidentManagementService.getForPages(JSON.stringify(params)).then(function (result) {
                  if (result.code === '0000') {
                	  var jsonData = result.data;
  					  $scope.pageData = jsonData.list;
  					  angular.forEach($scope.pageData,function (data) {
  					      if(null != data.accidentCause) {
                              var array = data.accidentCause.split(',');
                              for(var res in array) {
                                  array[res] = $scope.accidentCauseMap[array[res]];
                              }
                              data.accidentCauseName = array.join(',');
                          }
                      });
                      if (null == $scope.pageData || $scope.pageData.length === 0) {
                          inform.common(Trans("tip.noData"));
                          $scope.pages = inform.initPages();
                      } else {
                    	  // 分页信息设置
  						  $scope.pages.total = jsonData.total; 			// 页面数据总数
  						  $scope.pages.star = jsonData.startRow; 		// 页面起始数
  						  $scope.pages.end = jsonData.endRow;	 		// 页面分页大小
  						  $scope.pages.pageNum = jsonData.pageNum;  		// 第几页
                      }
                  } else {
                      inform.common(result.message);
                  }
              }, function (reason) {
                  console.log("error");
              });
          }
          
          /**
           * 打开新增窗口
           */
          $scope.openAddModal = function() {
              $scope.addParam = {};
              $scope.addParam.respPerson = [];
              $("#add_accident").modal("show");
              $scope.addForm.$setPristine();
              $scope.addForm.$setUntouched();
          };
          
          /**
           * 保存新增数据
           */
          $scope.saveAddData = function () {
              //校验jList中是否存在相同的责任人
              if (!verifyRepPersonList($scope.addParam.respPerson)) {
                  return;
              }
              var param = {
                  "deptName":$scope.addParam.deptName,
                  "projectId":$scope.addParam.projectId,
                  "severity":$scope.addParam.severity,
                  "accidentTitle":$scope.addParam.accidentTitle,
                  "accidentType":$scope.addParam.accidentType,
                  "accidentCause":null == $scope.addParam.accidentCause? null:$scope.addParam.accidentCause.join(','),
                  "respPerson": JSON.stringify($scope.addParam.respPerson),
                  "accidentOccurTime":inform.format($scope.addParam.accidentOccurTime,'yyyy-MM-dd'),
                  "accidentDescribe":$scope.addParam.accidentDescribe
              };
              qualityAccidentManagementService.add(param).then(function (result) {
                  if (result && result.code && result.code==='0000') {
                      inform.common("质量事故新增成功");
                      $("#add_accident").modal('hide');
                      getData(1);
                  }else{
                      inform.common(result.message);
                  }
              });
          };

          /**
           * 打开修改窗口
           */
          $scope.openUpdateModal = function(item) {
              $("#update_accident").modal("show");
              $scope.updateParam = {
                  "id":item.id,
                  "deptName":item.deptName,
                  "respPerson":item.respPerson,
                  "accidentTitle":item.accidentTitle,
                  "accidentType":item.accidentType,
                  "accidentCause":null == item.accidentCause? null: item.accidentCause.split(','),
                  "accidentOccurTime":item.accidentOccurTime,
                  "projectId":item.projectId*1,
                  "severity":item.severity,
                  "accidentDescribe":item.accidentDescribe
              };
              //查询负责人信息
              qualityAccidentManagementService.selectRepPerson(item.id).then(function (result) {
                  if (result && result.code && result.code==='0000') {
                      $scope.updateParam.respPerson = result.data;

                      //遍历部门，将部门Name值转化为Code值
                      angular.forEach($scope.deptList, function(dep, i) {
                          if (dep.orgName===$scope.updateParam.deptName){
                              $scope.updateParam.deptName = dep.orgCode;
                          }
                      });
                      $scope.updateForm.$setPristine();
                      $scope.updateForm.$setUntouched();
                      //这两个方法可以被调用去除’ng-dirty’类,并将该表单设置为原始状态(ng-pristine类),此方法也将传播到此表单中包含的所有控件
                  }else{
                      inform.common(result.message);
                  }
              });

          };
          
          /**
           * 保存修改数据
           */
          $scope.saveUpdateData = function () {
              //校验jList中是否存在相同的责任人
              if (!verifyRepPersonList($scope.updateParam.respPerson)) {
                  return;
              }
              var param = {
                  "id":$scope.updateParam.id,
                  "deptName":$scope.updateParam.deptName,
                  "projectId":$scope.updateParam.projectId,
                  "severity":$scope.updateParam.severity,
                  "accidentTitle":$scope.updateParam.accidentTitle,
                  "accidentType":$scope.updateParam.accidentType,
                  "accidentCause":null == $scope.updateParam.accidentCause? null:$scope.updateParam.accidentCause.join(','),
                  "accidentOccurTime":inform.format($scope.updateParam.accidentOccurTime,'yyyy-MM-dd'),
                  "respPerson": JSON.stringify($scope.updateParam.respPerson),
                  "accidentDescribe":$scope.updateParam.accidentDescribe
              };
              qualityAccidentManagementService.update(param).then(function (result) {
                  if (result && result.code && result.code==='0000') {
                      inform.common("质量事故修改成功");
                      $("#update_accident").modal("hide");
                      getData(1);
                  }else{
                      inform.common(result.message);
                  }
              });
          };

            /**
             * 校验责任人列表中是否存在重复
             * 规则：
             * 1.重复则提示"责任人×××,×××重复,请修改"并返回false
             * 2.无重复则返回true
             * @returns {boolean}
             */
            function verifyRepPersonList(param) {
                var verifyList = [];
                var duplicate = "";
                if(param.length <= 0){
                    //若未选择负责人，则提示
                    inform.common("请选择责任人！");
                    return false;
                }
                for (var i = 0; i <param.length; i++) {
                    if (verifyList.indexOf( param[i].employeeNo) > -1
                        && duplicate.indexOf(param[i].employeeNo) < 0) {
                        duplicate = duplicate.concat($scope.employeeMap[param[i].employeeNo]).concat(",");
                    }
                    verifyList.push(param[i].employeeNo);
                }
                //如果为空,说明无重复则返回true;
                if (!duplicate) {
                    return true;
                }
                //不为空,则提示哪些责任人重复,并返回false
                inform.common("责任人" + duplicate.substring(0, duplicate.length - 1) + "存在重复,请修改");
                return false;
            }

          /**
           * 删除确认弹框
           */ 
          $scope.deleteConfirm = function (id) {
              var modalInstance = $modal.open({
                  templateUrl: 'myModalContent.html',
                  controller: 'ModalInstanceCtrl',
                  size: "sm",
                  resolve: {
                      items: function () {
                          return Trans("common.deleteTip");
                      }
                  }
              });
              modalInstance.result.then(function () {
                  $scope.deleteByIds(id);
              });
          };

          /**
           * 根据选中的id 删除数据
           */
          $scope.deleteByIds = function (id) {
              qualityAccidentManagementService.deleteById(id)
                  .then(function (data) {
                      if (data.code === "0000") {
                          inform.common('质量事故删除成功');
                          getData(1);
                      } else {
                          inform.common(data.message);
                      }
                  }, function (error) {
                      inform.common(Trans("tip.requestError"));
                  });
          };


            //新增一行责任人
            $scope.addBind = function (addFlag) {
                var judgeVO = {
                    "employeeNo": "",
                    "liability": "",
                    "penaltyAmount": "0",
                    "liabilityLevel": '主要'
                };
                if(addFlag){
                    $scope.addParam.respPerson.push(judgeVO);
                }else {
                    $scope.updateParam.respPerson.push(judgeVO);
                }
            };


            /**
             * 执行页面的删除责任人操作
             * @param index
             */
            $scope.deleteBind = function(index,addFlag) {
                if (index >= 0) {
                    if(addFlag){
                        $scope.addParam.respPerson.splice(index, 1);
                    }else {
                        $scope.updateParam.respPerson.splice(index, 1);
                    }

                }
            };
          /**
           * *************************************************************
           *              方法声明部分                                 结束
           * *************************************************************
           */    
        }
    ]);
})();
(function () {
    app.controller('personalAssessController', [
        '$rootScope',
        'comService',
        '$scope',
        '$state',
        '$timeout',
        '$stateParams',
        '$modal',
        'personalDataBoardService',
        'kpiRelationService',
        'inform',
        'Trans',
        'AgreeConstant',
        'LocalCache',
        '$http',
        function (
            $rootScope,
            comService,
            $scope,
            $state,
            $timeout,
            $stateParams,
            $modal,
            personalDataBoardService,
            kpiRelationService,
            inform,
            Trans,
            AgreeConstant,
            LocalCache,
            $http
        ) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //页面数据
            $scope.formRefer = {};
            $scope.quarterSelect = [
                {
                    value: '第一季度',
                    label: '第一季度',
                },
                {
                    value: '第二季度',
                    label: '第二季度',
                },
                {
                    value: '第三季度',
                    label: '第三季度',
                },
                {
                    value: '第四季度',
                    label: '第四季度',
                },
                {
                    value: '上半年',
                    label: '上半年',
                },
                {
                    value: '全年',
                    label: '全年',
                },
            ];
            //初始化信息
            initData();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 210);
                $('#divTBDisAssess').height(divHeight);
                $('#subDivAssess').height(divHeight - 50);
            }
            /**
             * 初始化
             */
            function initData() {
                //获取当前登录者的empId
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ? $scope.sessionEmpId : $stateParams.empId;
                var date = new Date();
                //默认开始时间为三年前
                $scope.formRefer.startYear = date.getFullYear() - 3;
                $scope.getData = getData;
                getData();
            }

            //获取表格数据 获取考核结果和自评详情
            function getData() {
                var urlData = {
                    employeeId: $scope.formRefer.empId,
                    startYear: $scope.formRefer.startYear,
                    endYear: $scope.formRefer.endYear,
                    quarter: $scope.formRefer.quarter,
                };
                personalDataBoardService.selectPersonalAssessData(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.dataList = data.data;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            //查看考核详情弹窗
            $scope.selectAssessDetail = function (m) {
                getAssessDetailData(m);
                $('#assessDetail').click();
            };
            //查询考核详情
            function getAssessDetailData(m) {
                var urlData = {
                    year: m.year,
                    quarter: m.quarter,
                    employeeId: m.employeeId,
                };
                kpiRelationService.getAssessDetailData(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.assessDetail = [];
                            //考核详情中只包括 考核人1 考核人2 和 资源考核人
                            angular.forEach(data.data, function (index) {
                                if (index.evaluateType !== '上次考核评价' && index.evaluateType !== '自评') {
                                    $scope.assessDetail.push(index);
                                }
                            });
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            //重置
            $scope.reset = function () {
                $scope.formRefer = {};
                //获取当前登录者的empId
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ? $scope.sessionEmpId : $stateParams.empId;
                var date = new Date();
                //默认开始时间为三年前
                $scope.formRefer.startYear = date.getFullYear() - 3;
            };
            //返回
            $scope.goback = function () {
                $state.go('app.office.personKpi');
            };
            // 跳转至绩效列表页
            $scope.toPerformancePage = function () {
                const emplyeeId = $stateParams?.empId;

                // 获取绩效列表页的查询条件
                const queryInfo = {
                    department: '',
                    employeeId: '',
                    assessmentCycle: '',
                    employeeName: '',
                    departmentId: '',
                    teamIds: [],
                    areaId: '',
                    recordType: '',
                    communicateStartDate: '',
                    communicateEndDate: '',
                    communicater: '',
                    ...LocalCache.getObject('performanceLogManagement'),
                };
                // 姓名（汉字+域账号）如果外部传入用户id
                const name = emplyeeId
                    ? LocalCache.getObject('personDataBoardEmployee')?.name
                    : LocalCache.getSession('employeeName');

                // 部门ID
                const departmentId = LocalCache.getSession('department');
                queryInfo.employeeName = name;
                queryInfo.departmentId = departmentId;
                // 对绩效列表页的查询条件进行赋值
                LocalCache.setObject('performanceLogManagement', queryInfo);
                // 判断是否为领导从该页面跳转回去，如果不是领导，会禁用绩效列表页的部分查询条件
                const isLeader = $stateParams?.empId;
                let param = isLeader ? { type: 'edit' } : { type: 'view' };

                $state.go('app.office.performanceLogManagement', param);
            };
            // 跳转至沟通详情页面
            $scope.toCommunicationPage = async function (row) {
                const { year, quarter } = row;
                const emplyeeId = $stateParams?.empId;
                // 域账号
                const nameInfo = emplyeeId
                    ? LocalCache.getObject('personDataBoardEmployee').loginName
                    : LocalCache.getSession('loginName');
                const params = {
                    assessmentYear: year,
                    assessmentCycle: quarter,
                    assessedAccount: nameInfo,
                };
                // 获取沟通详情页面id
                kpiRelationService
                    .getCommunicationId(params)
                    .then((res) => {
                        if (res.code === '0000') {
                            if (!res.data) {
                                inform.common('暂无数据');
                                return;
                            }
                            $state.go('app.office.communicationLogUpdate', {
                                type: 'view',
                                id: res.data,
                            });
                        } else {
                            inform.common(res.message);
                        }
                    })
                    .catch(() => {
                        inform.common(Trans('tip.requestError'));
                    });
                return;
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        },
    ]);
})();

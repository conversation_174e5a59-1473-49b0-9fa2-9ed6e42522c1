(function() {
    'use strict';
    app.factory('mailService', mailService);
    mailService.$inject = ["HttpService", '$rootScope'];

    function mailService(HttpService, $rootScope) {
        var service = {
        	packageMail: packageMail,
        	sendMail:sendMail,
        	queryContacts: queryContacts,
        	queryContactDep:queryContactDep,
        	addContact: addContact,
        	upContact: upContact,
        	deleteContact: deleteContact,
        	querySign: querySign,
        	addSign: addSign,
            getPersonList:getPersonList
        };
        return service;
        /**
         * 组装会议内容
         */
        function packageMail(urlData){
        	return HttpService.post($rootScope.getWaySystemApi + 'mail/packageMail',urlData);
        }
        /**
         * 发送会议通知
         */
        function sendMail(urlData){
        	return HttpService.post($rootScope.getWaySystemApi + 'mail/sendMail',urlData);
        }
        /**
         * 查询联系人
         */
        function queryContacts(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'mail/queryContacts', urlData);
        }
        /**
         * 查询联系人下拉框
         */
        function queryContactDep(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'mail/queryContactDep', urlData);
        }
        /**
         * 查询联系人下拉框
         */
        function getPersonList(urlData) {
            return HttpService.get($rootScope.getWaySystemApi + 'common/getPersonList', urlData);
        }
        /**
         * 添加联系人
         */
        function addContact(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'mail/addContact', urlData);
        }
        /**
         * 更新联系人
         */
        function upContact(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'mail/upContact', urlData);
        }
        /**
         * 删除联系人
         */
        function deleteContact(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'mail/deleteContact', urlData);
        }
        /**
         * 查询签名
         */
        function querySign(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'mail/querySign', urlData);
        }
        /**
         * 添加签名
         */
        function addSign(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'mail/addSign', urlData);
        }
    }
})();
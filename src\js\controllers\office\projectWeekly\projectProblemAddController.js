//# sourceURL=js/controllers/office/projectWeekly/projectProblemAddController.js
(function() {
    app.controller("projectProblemAddController", ['projectProblemService', '$state', 'comService', '$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$stateParams', '$modal', '$http', '$state',
        function(projectProblemService, state, comService, $rootScope, $scope, inform, Trans, AgreeConstant, LocalCache, $stateParams, $modal, $http, $state) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //初始化页面信息
            initPages();
            $scope.getData = getData;
            //用于判断初始化是否完成
            $scope.type = $stateParams.type;
            var flag = 0;
            //责任人
            $scope.changeParam = {
            	chargePerson: []
            };
            //问题内容下载标注拉框数据源
            $scope.problemContentDownloadFlagSelect = [{
                value: '0',
                label: '下载'
            }, {
                value: '1',
                label: '不下载'
            }];
            //获取数据
            getData();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */


            function initPages() {
            	//获取项目下拉框
       		 	$scope.projectIdList = [];
       		 	comService.getProjectsByLineOffice('').then(function (data) {
                    $scope.projectIdList =data.data;
                    flag++;
         			getData();
                });
                //问题状态
                $scope.problemList = [];
                comService.getParamList('NEED_SUPPORT', 'NEED_SUPPORT').then(function(data) {
                    if (data.data) {
                        $scope.problemList = data.data;
                        flag++;
             			getData();
                    }
                });
                //问题类别
                $scope.typeList = [];
                comService.getParamList('NEED_SUPPORT', 'PROBLEM_TYPE').then(function(data) {
                    if (data.data) {
                        $scope.typeList = data.data;
                        flag++;
             			getData();
                    }
                });
                //问题等级
                $scope.gradeList = [];
                comService.getParamList('NEED_SUPPORT', 'PROBLEM_GRADE').then(function(data) {
                    if (data.data) {
                        $scope.gradeList = data.data;
                        flag++;
             			getData();
                    }
                });
                //获取员工信息
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function(data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                        flag++;
             			getData();
                    }
                });
            }
            /**
             * 修改查询项目信息
             */
            function getData() {
            	//初始化未完成
            	if(flag !== 5){
                    return;
                }
            	//为新增
            	if ($stateParams.type !== "up"){
            		$scope.changeParam = {
                    	problemState: '0001',
                    	problemPriority: '低',
                    	problemGrade: '0002',
                    	difficultGrade: '低',
                    	problemContentDownloadFlag:'0'
                    }; 
            		return;
            	}
                var urlData = {
                    'id': LocalCache.getObject('projectProblem_item'),
                    'projectId': $stateParams.projectId //项目名称
                };
                projectProblemService.getUpdateInfo(urlData).then(function(data) {
                    $scope.changeParam = data.data;
                    //原有数据产品ID为null，转化会失败，导致页面无法赋值，所以添加初值处理
                    if (null == $scope.changeParam.productId){
                    	$scope.changeParam.productId = '';
                    } else {
                    	$scope.changeParam.productId = parseInt($scope.changeParam.productId);
                    }
                    //获取责任人
                    var chargePersons = [];
                	angular.forEach($scope.changeParam.chargePersons, function (one, index) {
                		chargePersons.push(one.personId);
            		});
                	$scope.changeParam.chargePerson = chargePersons;
                }, function() {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 判断日期不得小于反馈时间
             */
            $scope.judgTime = function(){
            	if($scope.changeParam.problemResolutionDate !== '' && $scope.changeParam.feedbackTime !== ''
            		&& null != $scope.changeParam.feedbackTime &&
            		inform.format($scope.changeParam.problemResolutionDate, 'yyyy-MM-dd') < inform.format($scope.changeParam.feedbackTime, 'yyyy-MM-dd')){
            		$scope.changeParam.problemResolutionDate = '';
            	}
            	if($scope.changeParam.completionTime !== '' && $scope.changeParam.feedbackTime !== ''
            		&& null != $scope.changeParam.feedbackTime &&
            		inform.format($scope.changeParam.completionTime, 'yyyy-MM-dd') < inform.format($scope.changeParam.feedbackTime, 'yyyy-MM-dd')){
            		$scope.changeParam.completionTime = '';
            	}
            };
            /**
             * 保存信息
             */
            $scope.saveInfo = function(){
            	var chargePersons = [];
            	angular.forEach($scope.changeParam.chargePerson, function (one, index) {
            		chargePersons.push({
        				"personId":one//责任人
        			});
        		});
            	 var urlData = {
                         'projectId': $stateParams.projectId,
                         'problemContent': $scope.changeParam.problemContent,
                         'problemSubject': $scope.changeParam.problemSubject,
                         'problemState': $scope.changeParam.problemState,
                         'problemContentDownloadFlag':$scope.changeParam.problemContentDownloadFlag,//风险内容下载标志
                         'problemResolutionDate': inform.format($scope.changeParam.problemResolutionDate, 'yyyy-MM-dd'),
                         'chargePersons': chargePersons,
                         'countermeasure': $scope.changeParam.countermeasure,
                         'completionTime': inform.format($scope.changeParam.completionTime, 'yyyy-MM-dd'),
                         'difficultGrade' :$scope.changeParam.difficultGrade,
                 //        'productId' : $scope.changeParam.productId,
                 //        'relevanceModule': $scope.changeParam.relevanceModule,
                 //        'hardware': $scope.changeParam.hardware,
                 //        'sceneVersion': $scope.changeParam.sceneVersion,
                         'problemPriority': $scope.changeParam.problemPriority,
                         'feedbackPerson': $scope.changeParam.feedbackPerson,
                         'feedbackTime': inform.format($scope.changeParam.feedbackTime, 'yyyy-MM-dd'),
                 //        'problemType': $scope.changeParam.problemType,
                         'problemGrade': $scope.changeParam.problemGrade,
                         'remark': $scope.changeParam.remark
                     };
                //新增
                if ($scope.type == null){
               	 addInfo(urlData);
                } else {
                //修改 
                updateInfo(urlData)
               }
            };
            /**
             * 添加信息
             */
            function addInfo(urlData) {
                projectProblemService.addProjectProblemInfo(urlData).then(function(data) {
                    callBackFunction(data);
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            
            /**
             * 修改信息
             */
            function updateInfo(urlData) {
                urlData.id = $scope.changeParam.id;
                projectProblemService.updateProjectProblemInfo(urlData).then(function(data) {
                    callBackFunction(data);
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            function callBackFunction(data){
                if (data.code === AgreeConstant.code) {
                    common(data.message, function() {
                        $scope.back();
                    });
                } else {
                    inform.common(data.message);
                }

            }
            
            //计划按钮
            $scope.identifyTimeUp = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.identifyTimeUp.identifyTime = true;
                $scope.requestTimeUp.requestTime = false;
                $scope.identifyTimeUp.feedbackTime = false;
            };
            // 实际完成时间按钮
            $scope.requestTimeUp = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.identifyTimeUp.identifyTime = false;
                $scope.requestTimeUp.requestTime = true;
                $scope.identifyTimeUp.feedbackTime = false;
            };
            //反馈时间 按钮
            $scope.feedbackTimeUp = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.identifyTimeUp.identifyTime = false;
                $scope.requestTimeUp.requestTime = false;
                $scope.identifyTimeUp.feedbackTime = true;
            };
            
            /**
             * 提示信息
             * @param str  提示信息
             * @param func 确定时执行的函数
             */
            function common(str, func) {
                layer.confirm(str, {
                    title: false,
                    btn: ['确定']
                }, function(result) {
                    layer.close(result);
                    if (typeof(func) !== 'undefined') {
                        func();
                    }
                });
            }
            
            /**
             * 返回
             */
            $scope.back = function(){
            	$state.go("app.office.projectWeeklyDetail", {projectId: $stateParams.projectId,type:'3'});
            };
            //问题状态修改，联动修改问题内容下载标志
            $scope.changeProblemState = function(){
                if($scope.changeParam.problemState === '0004'){
                    $scope.changeParam.problemContentDownloadFlag = '1';
                }
            }
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        }
    ]);
})();
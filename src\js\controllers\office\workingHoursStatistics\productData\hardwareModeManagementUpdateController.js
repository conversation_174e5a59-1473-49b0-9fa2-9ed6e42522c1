
(function () {
	app.controller("hardwareModeManagementUpdate", ['comService', '$rootScope', '$scope', 'hardwareModeManagementService','productTypeNameModule','$stateParams', 'inform', 'Trans', '$modal','AgreeConstant','LocalCache', '$state',
	function (comService, $rootScope, $scope, hardwareModeManagementService, productTypeNameModule,$stateParams, inform, Trans, $modal,AgreeConstant, LocalCache,$state) {
		/**
   * *************************************************************
   *             初始化部分                                 开始
   * *************************************************************
   */
		// 正则校验配置
		$scope.limitList = AgreeConstant.limitList;
		//增加还是修改0：增加，1：修改
		$scope.isAdd = $stateParams.isAdd;
        //状态下拉框数
		$scope.hardwareModeStatusSelect = [{
			value: '0',
			label: '启用'
		}, {
			value: '1',
			label: '停用'
		}];
        $scope.param = JSON.parse($stateParams.hardwareModeInfoParam);
        if($scope.isAdd === '0'){
            $scope.param.hardwareModeStatus = '0';
        }
        //初始化页面信息
        initPages();
		//设置列表的高度
		setDivHeight();
		$(window).resize(setDivHeight); //窗体大小变化时重新计算高度
		/**
   * *************************************************************
   *              初始化部分                                 结束
   * *************************************************************
   */

		/**
   * *************************************************************
   *              方法声明部分                                开始
   * *************************************************************
   */

		/**
    	 * 设置列表的高度
    	 */
		function setDivHeight() {
			//网页可见区域高度
			var clientHeight = document.body.clientHeight;
			var divHeight = clientHeight - (150 + 185);
			$("#divTBDis").height(divHeight);
			$("#subDivTBDis").height(divHeight - 50);
			var clientWidth = document.body.clientWidth;
			$("#buttonStyle").css(inform.getButtonStyle(clientHeight, clientWidth));
		}
        function initPages() {
				//获取产品线-产品类别-产品名称集合
                $scope.productLineAndType = [];
                $scope.productLineAndTypeMap = {};
                comService.getParamList('PRODUCT_TYPE','').then(function (data) {
					if (data.data) {
						$scope.productLineAndType = data.data;
                        angular.forEach($scope.productLineAndType,function (item) {
						    $scope.productLineAndTypeMap[item["param_code"]] = item["param_value"];
					    });
					}
				});
			}
		/**
       * 修改信息
       */
		$scope.updateInfo  = function () {
		    if (!$scope.param.hardwareModeNo) {
            	inform.common("请填写硬件型号名称！");
            }else if(!$scope.param.productLine || !$scope.param.productType){
                inform.common("请选择产品类别及名称！");
            }else {
				var urlData = {
				    'hardwareModeNo': $scope.param.hardwareModeNo,
				    'productLine': $scope.param.productLine,
					'productType': $scope.param.productType,
					'productSubType': $scope.param.productSubType,
					'hardwareModeStatus':$scope.param.hardwareModeStatus,
                    'note': $scope.param.note //备注

				};
				if($scope.isAdd==="0"){
                    hardwareModeManagementService.addHardwareModeInfo(urlData).then(function (data) {
                        judgeData(data);
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
				}else {
                    hardwareModeManagementService.updateHardwareModeInfo(urlData).then(function (data) {
                        judgeData(data);
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
				}

            }
		};
		function judgeData(data){
            if (data.code === AgreeConstant.code) {
                inform.common(data.message);
                $scope.goback();
            } else {
                //弹框提示
                inform.common(data.message);
            }
		}
		/**
   * 返回硬件型号信息
   */
		$scope.goback = function () {
			$state.go('app.office.hardwareModeManagement');
		};

        $scope.initModule = function (){
            var data={
                'productLine': $scope.param.productLine,
                'productType': $scope.param.productType,
                'productName': $scope.param.productSubType
            };
            productTypeNameModule.initModule(data,$scope,setProductTypeNameInfo);
        }
        /**
         * 根据所选中的产品分类及名称回填信息
         */
        function setProductTypeNameInfo(data){
            $scope.param.productLine = data.productLine;
            $scope.param.productType = data.productType;
            $scope.param.productSubType = data.productName;
        }

		/**
     * *************************************************************
     *              方法声明部分                                结束
     * *************************************************************
     */
	}]);
})();

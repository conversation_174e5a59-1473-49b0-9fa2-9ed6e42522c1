/*
 * @Author: sun<PERSON><PERSON>a
 * @Date:   2019-05-23 17:50:05
 * @Last Modified by:   sunqixian
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function () {
    'use strict';
    app.factory('customerDataService', customerDataService);
    customerDataService.$inject = ["HttpService", '$rootScope'];

    function customerDataService(HttpService, $rootScope) {

        var service = {

            getAllMessage: getAllMessage,
        //    addMessage: addMessage,
            updateMessage: updateMessage,
            selectNconsistentNamingSpecification: selectNconsistentNamingSpecification,
            //新增方法定义 add by zhangyegong
            viewConsumed:viewConsumed

        };
        return service;


        /**
         * 获取产品线下所有的信息
         */
        function getAllMessage(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'customerDemand/getAllMessage', urlData);
        }

        /**
         * 添加信息
         */
 /*       function addMessage(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'customerDemand/addInfo', urlData);
        }*/


        /**
         * 修改信息
         */
        function updateMessage(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'customerDemand/updateInfo', urlData);
        }

        /**
         * 查询不符合命名规范的PLM客户需求
         */
        function selectNconsistentNamingSpecification() {
            return HttpService.post($rootScope.getWaySystemApi + 'plmCustomerDemand/selectNconsistentNamingSpecification');
        }

        //获取工时信息
        //add by zhangyegong 20211022
        function viewConsumed(param) {
            return HttpService.post($rootScope.getWaySystemApi + "customerDemand/get_consumed_by_upgradeid",param);
        }

    }
})();

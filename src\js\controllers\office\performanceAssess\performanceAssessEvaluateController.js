(function () {
    app.controller("performanceAssessEvaluateController", ['comService','kpiRelationService','roleKpiService','$rootScope', '$state','$scope','$stateParams', '$modal','inform','LocalCache','Trans','AgreeConstant','$http',
        function (comService,kpiRelationService,roleKpiService,$rootScope,$state, $scope,$stateParams,$modal,inform,LocalCache,Trans,AgreeConstant,$http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.limitList = AgreeConstant.limitList;
            //查询条件
            $scope.formRefer = {};
            $scope.assessDetail = [];
            $scope.sourceAssessDetail= {};
            //页面分页信息
            $scope.pages = {
                pageNum : '',   //分页页数
                size : '',      //分页每页大小
                total : ''      //数据总数
            };
            // 初始化分页数据
            $scope.pages = inform.initPages();
            $scope.changeParam = JSON.parse($stateParams.jsonResult);
            //如果传递的最终结果为空，则使用理论参考权重代替
            if($scope.changeParam.finalResult == null){
                $scope.changeParam.finalResult = $scope.changeParam.hrGuideline;
            }
            $scope.pages.size = '50';
            $scope.levelList = [
                {
                    value: 'A',
                    label: 'A'
                },{
                    value: 'B',
                    label: 'B'
                },{
                    value: 'C',
                    label: 'C'
                },{
                    value: 'D',
                    label: 'D'
                },{
                    value: 'E',
                    label: 'E'
                }];
            $scope.scoreLevelMap = [{
                score: 5,
                level: 'A'
            },{
                score: 4,
                level: 'B'
            },{
                score: 3,
                level: 'C'
            },{
                score: 2,
                level: 'D'
            },{
                score: 1,
                level: 'E'
            },{
                score: 0,
                level: ''
            }
            ]
            $scope.quarterSelect = [{
                value: '6', label: '上半年'
            }, {
                value: '5', label: '全年'
            }];

            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.getData = getData;
            getData();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 250);
                var clientWidth = document.body.clientWidth;
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 80);
                $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
            }
            function getData() {
                getAssessDetailData();
                selectKpiData();
            }
            //根据分数获取相应等级
            function getLevelByScore(score){
                for(var i = 0;i < $scope.scoreLevelMap.length;i++){
                    if($scope.scoreLevelMap[i].score === score){
                        return $scope.scoreLevelMap[i].level;
                    }
                }
                return '';
            }
            //根据结果获取分数
            function getScoreByResult(result){
                switch (result) {
                    case "A":
                        return 5;
                    case "B":
                        return 4;
                    case "C":
                        return 3;
                    case "D":
                        return 2;
                    case "E":
                        return 1;
                    default:
                        return 0;
                }
            }

            /**
             * 获取考核周期名称
             */
            function getQuarter(label){
                for(var i = 0; i < $scope.quarterSelect.length; i ++){
                    if($scope.quarterSelect[i].label === label){
                        return $scope.quarterSelect[i].value;
                    }
                }
                return '';
            }
            /**
             * 获取所有指标关联信息
             */
            function selectKpiData(){
                    var urlData ={
                        'years':$scope.changeParam.year,
                        'quarter':getQuarter($scope.changeParam.quarter),
                        'empId': $scope.changeParam.employeeId
                    }
                    roleKpiService.selectPersonalData(urlData).then(function (data) {
                            if (data.code === AgreeConstant.code) {
                                if (null !== data.data) {
                                    $scope.developerData = data.data.kpiDeveloper;
                                    $scope.teamLeaderData = data.data.kpiTeamLeader;
                                    $scope.projectManagerData = data.data.kpiProjectManager;
                                } else {
                                    inform.common(Trans("tip.noData"));
                                }
                            } else {
                                inform.common(data.message);
                            }
                        },
                        function () {
                            inform.common(Trans("tip.requestError"));
                        });
                    //查询考核结果
                    roleKpiService.selectPersonalKpi(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.personalKpi = data.data;
                            }
                        }
                    });
            }
            //查询考核详情
           function getAssessDetailData() {
                var urlData = {
                    'year':$scope.changeParam.year,
                    'quarter':$scope.changeParam.quarter,
                    'employeeId': $scope.changeParam.employeeId
                };
                kpiRelationService.getAssessDetailData(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                            angular.forEach(data.data,function (index) {
                                if(index.evaluateType === '资源考核人') {
                                    $scope.sourceAssessDetail = index;
                                }else if(index.evaluateType === '上次考核评价') {
                                    $scope.lastAssess = index;
                                }else{
                                    $scope.assessDetail.push(index);
                                }
                            });
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            function updateGuidelineAndDeviation(){
                var score = (
                    ((getScoreByResult($scope.changeParam.personResult1) * $scope.changeParam.percent1) || 0) +
                    ((getScoreByResult($scope.changeParam.personResult2) * $scope.changeParam.percent2) || 0) +
                    ((getScoreByResult($scope.sourceAssessDetail.result) * $scope.changeParam.percent3) || 0)
                ) / 100;
                //分数向下取整，然后通过getLevelByScore获取对应等级
                $scope.changeParam.hrGuideline = getLevelByScore(Math.floor(score));
                var urlData = {
                    'year':$scope.changeParam.year,
                    'quarter':$scope.changeParam.quarter,
                    'employeeId': $scope.changeParam.employeeId,
                    'person3':$scope.sourceAssessDetail.result,
                    'finalResult':$scope.changeParam.finalResult,
                    'selfEvaluateResult':$scope.changeParam.selfEvaluateResult,
                    'hrGuideline':$scope.changeParam.hrGuideline,
                    'systemRecommendResult':$scope.changeParam.systemRecommendResult
                };
                kpiRelationService.updateGuidelineAndDeviation(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                            inform.common('更新成功');
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            //更新考核详情
           $scope.updateAssessResult =  function () {
                if($scope.changeParam.flag !== '1'){
                    inform.common("该评价不可更改");
                    return;
                }
                var urlData = {
                    'year':$scope.changeParam.year,
                    'quarter':$scope.changeParam.quarter,
                    'employeeId': $scope.changeParam.employeeId,
                    'lack':$scope.sourceAssessDetail.lack,
                    'growing':$scope.sourceAssessDetail.growing,
                    'resultExplain':'',
                    'result':$scope.sourceAssessDetail.result,
                    'finalResult':$scope.changeParam.finalResult
                };
                if($scope.changeParam.finalResult === 'D' || $scope.changeParam.finalResult === 'E'){
                    urlData.resultExplain = $scope.sourceAssessDetail.resultExplain;
                }
                kpiRelationService.updateAssessResult(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                            updateGuidelineAndDeviation();
                            inform.common('保存成功');
                            $state.go('app.office.performanceAssessSection');
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            //如果考核结果为空，修改结果3也会也改变最终结果
           $scope.result3ChangeHrGuideline = function (){
                var score = (
                    ((getScoreByResult($scope.changeParam.personResult1) * $scope.changeParam.percent1) || 0) +
                    ((getScoreByResult($scope.changeParam.personResult2) * $scope.changeParam.percent2) || 0) +
                    ((getScoreByResult($scope.sourceAssessDetail.result) * $scope.changeParam.percent3) || 0)
                ) / 100;
                var result = getLevelByScore(Math.floor(score));
                $scope.changeParam.finalResult = result;
                $scope.changeParam.hrGuideline = result;
           }
            /**
             * 重置
             */
            $scope.clearParams = function() {
                $scope.formRefer = {};
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
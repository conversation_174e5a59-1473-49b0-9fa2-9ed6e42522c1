(function() {
    'use strict';
    app.controller("param_Management", ['$scope', '$rootScope', 'inform', '$http', '$stateParams', '$modal', 'Trans', 'SystemService', 'AgreeConstant',
        function($scope, $rootScope, inform, $http, $stateParams, $modal, Trans, SystemService, AgreeConstant) {
            var interfaceMap = {};
            $scope.map = {}; //条件
            $scope.getData = getData; // 初始化函数
            $scope.pages = inform.initPages(); // 初始化分页数据
            $scope.getData($scope.pages.pageNum); // 初始化请求数据
            $scope.searchData = searchData; // 查询
            $scope.getTypeCode = getTypeCode; // 获取参数类型值
            $scope.getTypeCode();
            $scope.open = open; // 删除弹框显示

            // 排序
            $scope.title = 'paramId';
            $scope.desc = true;
            $scope.order = order;

            // 排序
            function order(str) {
                $scope.title = str;
                $scope.desc = !$scope.desc;
            }

            // 查询
            function searchData() {
                interfaceMap = angular.copy($scope.map);
                getData(AgreeConstant.pageNum);
            }

            // 获取参数类型值
            function getTypeCode() {
                SystemService.getTypeCode()
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.typeCode = data.result;
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 获取表格数据
            function getData(num) {
                if (!num) { inform.common(Trans('tip.pageNumTip')); return; }
                SystemService.getParamByMap(JSON.stringify(interfaceMap), parseInt(num), $scope.pages.size)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.pages.goNum = null;
                            var jsonData = angular.fromJson(data.result);
                            $scope.resultData = jsonData.list;
                            if ($scope.resultData.length===0) {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages();
                            } else {
                                $scope.pages.total = jsonData.total;
                                $scope.pages.star = (jsonData.pageNum - 1) * jsonData.pageSize + 1;
                                $scope.pages.end = jsonData.pageNum * jsonData.pageSize;
                                $scope.pages.pageNum = jsonData.pageNum;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 删除弹框显示
            function open(item) {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function() {
                            return Trans('common.deleteTip');
                        }
                    }
                });

                modalInstance.result.then(function() {
                    if (item) {
                        SystemService.deleteParamById(item.paramId)
                            .then(function(data) {
                                if (data.code===AgreeConstant.resultCode) {
                                    interfaceMap = {};
                                    $scope.map = {};
                                    getData(1);
                                    inform.common(Trans("tip.delSuccess"));
                                } else {
                                    inform.common(data.message);
                                }
                            }, function() {
                                inform.common(Trans("tip.requestError"));
                            });
                    }
                });
            }

        }
    ]);
})();
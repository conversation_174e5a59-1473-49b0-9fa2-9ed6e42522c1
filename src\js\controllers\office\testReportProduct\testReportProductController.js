(function () {
    app.controller("testReportProductController", ['testReportProductService','LocalCache','$stateParams','comService', '$rootScope', '$scope', '$state', '$modal', 'inform', 'Trans', 'AgreeConstant', '$http',
        function (testReportProductService,LocalCache,$stateParams,comService, $rootScope, $scope, $state, $modal, inform, Trans, AgreeConstant, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
    		var name = $stateParams.functionName;
    		$scope.flag = $stateParams.flag;
    		$scope.getData=getData;
    		$scope.getProUser=getProUser;
    		//产品状态
    		$scope.productStatus=[
    			{
    				'paramCode':'normal',
    				'paramValue':'正常'
    			},{
    				'paramCode':'closed',
    				'paramValue':'结束'
    			}
    		];
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //初始化页面信息
            initPages();
            //用于存放时间
            $scope.one={};
            $scope.formRefer = {};
            // 初始化分页数据
        	$scope.pages = inform.initPages();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 160);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight-50);
            }

            //重置查询条件
            $scope.reset = function() {
                $scope.formRefer.startTime = inform.format(new Date(),"yyyy")+'-01-01';
                $scope.formRefer.endTime = '';
                $scope.formRefer.productLine = '';
                $scope.formRefer.employee = null;
            }
            
            //开始时间
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.one.openedStart = true;    //开始时间
                $scope.one.openedEnd = false;
            };

            //结束时间
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.one.openedStart = false;
                $scope.one.openedEnd = true;    //结束时间
            };
            
            //初始化页面
            function initPages() {
            	//获取产品线
                comService.getParamList('PRO_PRODUCT_TYPE', 'PRO_PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.productLine = data.data;
                        $scope.productLine.push({
                        	'param_value':'其它',
                        	'param_code':'0'
                        })
                    }
                });
                //获取部门
                comService.getProDepartmant().then(function(data){
                	if (data.data) {
                        $scope.departmant = data.data;
                    }
                });
                
            	 //详情数据
                $scope.jsonFunction =reportListConfig;
                $scope.itemInfo = $scope.jsonFunction[name];
                //引入页面
                $scope.htmlFile = $scope.itemInfo.htmlFile;
                //查询条件初始化成功后调用
                $scope.getSubData=getSubData;
            }
           /**
            * 查询条件初始化成功后调用
            */
           function getSubData() {
               setTimeout(initParam,600);
           }
           
           /**
            * 给查询条件赋值
            */
           function initParam() {
        	   //获取缓存
        	   $scope.formRefer = LocalCache.getObject('testReportProduct_formRefer');
        	   if ($scope.formRefer.departmant==null){
        		   $scope.formRefer.departmant = $stateParams.department;
        	   }
        	   if ($scope.formRefer.startTime==null){
               	   $scope.formRefer.startTime = inform.format(new Date(),"yyyy")+'-01-01';
               }
        	   //清除缓存
        	//    LocalCache.setObject('testReportProduct_formRefer', {});
               getProUser();
               //查询汇总信息
               getData();
           }
         //获取员工姓名
           function getProUser(){
        	   var data  = $scope.formRefer.departmant==null? "":$scope.formRefer.departmant;
               comService.getProUser(data).then(function(data) {
                   if (data.data) {
                       $scope.employeeList = data.data;
                   }
               });
           }
            /**
             * 查询汇总信息
             */
           function getData(indexNum){
                $scope.formRefer.startTime = inform.format($scope.formRefer.startTime, 'yyyy-MM-dd');
                $scope.formRefer.endTime = inform.format($scope.formRefer.endTime, 'yyyy-MM-dd');
      		 	//拼装下载内容
				var urlData={
					'param': $scope.formRefer,//参数
	            	'className': $scope.itemInfo.className,//类名
	            	'function': $scope.itemInfo.getData, //方法名
	            	'page': indexNum
				};
				if ($scope.itemInfo.getData[0].indexOf('sortRule')>-1){
					urlData.size=10000;
					$scope.pageHelper=false;
				} else {
					urlData.size=$scope.pages.size;
				}
                
				if ($scope.itemInfo.calculateSum!=null){
					urlData.calculateSum = $scope.itemInfo.calculateSum;
				}
				if ($scope.itemInfo.calculateRate!=null){
					urlData.calculateRate = $scope.itemInfo.calculateRate;
				}
				testReportProductService.getData(urlData).then(function(data) {
					if (data.code===AgreeConstant.code) {
						angular.forEach(data.data, function (detail, i) {
							//根据放入的名称不同，赋值给不同的变量
							$scope.tableList = detail.list;
							 // 分页信息设置
		                    $scope.pages.total = detail.total;           // 页面数据总数
		                    $scope.pages.star = detail.startRow;         // 页面起始数
		                    $scope.pages.end = detail.endRow;            // 页面结束数
		                    $scope.pages.pageNum = detail.pageNum;       //页号
		      		 	});
					} else {
						inform.common(data.message);
					}
					//设置列表的高度
		            setDivHeight();
				},
				function(error) {
					inform.common(Trans("tip.requestError"));
				});
           }
           /**
            * 跳转至明细
            */
           $scope.goDetails = function (id,index,account) {
                $scope.formRefer.startTime = inform.format($scope.formRefer.startTime, 'yyyy-MM-dd');
                $scope.formRefer.endTime = inform.format($scope.formRefer.endTime, 'yyyy-MM-dd');
        	   LocalCache.setObject("testReportProduct_formRefer",$scope.formRefer);
        	   $state.go("app.office.testReportProductDetails",
        			{
        		   		product:id,
		        		functionName:name,
		        		index:index,
		        		param:JSON.stringify($scope.formRefer),
		        		account:account
		        	}
        	   )
           }
    	    /**
    	     * 下载
    	     */
    	    $scope.toExcel = function () {
    	    	var modalInstance = $modal.open({
    				templateUrl: 'myModalContent.html',
    				controller: 'ModalInstanceCtrl',
    				size: "sm",
    				resolve: {
    					items: function() {
    						return "确定要下载吗！";
    					}
    				}
    			});
    	    	modalInstance.result.then(function() {
           		 	var head = $scope.itemInfo.headMap;
           		 	for (var i=0;i<$scope.itemInfo.subHeadMap.length;i++){
           		 		head.push($scope.itemInfo.subHeadMap[i]);
           		 	}
           		 	$scope.formRefer.startTime = inform.format($scope.formRefer.startTime, 'yyyy-MM-dd');
                    $scope.formRefer.endTime = inform.format($scope.formRefer.endTime, 'yyyy-MM-dd');
    				//拼装下载内容
    				var urlData={
    						 'param': $scope.formRefer,//参数
    	                     'className': $scope.itemInfo.className,//类名
    	                     'functionName':  $scope.itemInfo.functionName, //方法名
    	                     'detailName':  $scope.itemInfo.detailName, //sheet名
    	                     'fileName': $scope.itemInfo.name,//文件名
    	                     'headMap': head,//表头
    	                     'mergedRegion':$scope.itemInfo.mergedRegion,
    				};
    				if ($scope.itemInfo.calculateSum!=null){
    					urlData.calculateSum = $scope.itemInfo.calculateSum;
    				}
    				if ($scope.itemInfo.calculateRate!=null){
    					urlData.calculateRate = $scope.itemInfo.calculateRate;
    				}
    				inform.downLoadFile ('testReport/toExcel',urlData,urlData.fileName+'.xlsx');
    			});
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();

(function () {
    app.controller("svnModelUpdateController", ['comService','$rootScope', '$scope','$state','$stateParams','$modal','svnModelUpdateService','inform','$window','Trans','AgreeConstant','LocalCache',
        function (comService,$rootScope, $scope,$state,$stateParams, $modal,svnModelUpdateService,inform,$window,Trans,AgreeConstant,LocalCache) {

        /**
         * *************************************************************
         *             初始化部分                                 开始
         * *************************************************************
         */
    	//添加的仓库信息
    	$scope.formInsertrepository ={};
    	//添加的模块信息
    	$scope.formInsertmodule ={};
    	//更新后的仓库信息
    	$scope.formUprepository ={};
    	//更新后的模块信息
    	$scope.formUpmodule ={};
		//模块现有的人员对象
		$scope.employeeForModule = {
			employeeNos: []
		}
		//为新增模块保存人员的对象
		$scope.employeeForNewModule = {
			employeeNos: []
		}
    	//保存修改页面中所有模块以及其开发者的集合
    	$scope.moduleAndEmployeeList = [];
		//临时存储的集合 用于查询
		$scope.tempModuleAndEmployeeList = [];
    	//添加仓库返回的仓库ID
    	$scope.repositoryreturnid="";
		//新增时的模块id-人员id对象集合
		$scope.moduleEmployeesList = [
			{
				moduleid: '',
				employeeno: ''
			}
		]
    	//添加模块返回的模块id
    	$scope.modulereturnid="";
		//查询输入框
    	$scope.formRefer={};
    	$scope.formRefer.moduleName = '';
    	//初始化页面信息
    	initPages();
		$scope.tempFormUpmodule = {};
		$scope.showTable = 1;
		//根据仓库id回填模块信息
    	if ($stateParams.repositoryid !== null){
    		getModulesAndEmployees($stateParams.repositoryid);
    	}
    	$scope.repositoryid = $stateParams.repositoryid;
    	$scope.repositoryName = $stateParams.repositoryName;
    	$scope.repositoryType = $stateParams.repositoryType;
    	$scope.addModule = addModule;//添加模块
    	$scope.getModule = getModule;//根据id回填模块信息
    	$scope.saveModule = saveModule;//保存模块修改信息
		$scope.getModulesAndEmployees = getModulesAndEmployees;
		$scope.searchModelByName = searchModelByName; //模糊匹配模块名称查找
        /**
         * *************************************************************
         *              初始化部分                                 结束
         * *************************************************************
         */

        /**
         * *************************************************************
         *              方法声明部分                                 开始
         * *************************************************************
         */

        /**
    	 * 页面初始化
    	 */
		//延时设置表格格式
		setTimeout(tryResetPage, 500);

    	function initPages() {
    		//获取员工信息
            $scope.employeeList = [];
            comService.getEmployeesByOrgId('').then(function(data) {
                if (data.data) {
                    $scope.employeeList = data.data;
                }
            });
			$scope.permissionList = [
				{
					label: '只读',
					value: '只读'
				},
				{
					label: '读写',
					value: '读写'
				}
			]
			//获取角色列表
			if($stateParams.repositoryType==="svn"){
				$scope.characterList =[
					{
						value: '//',
						label: '//'
					}
				]
			}else {
				$scope.characterList = [
					{
						value: 'guest',
						label: 'guest'
					},
					{
						value: 'reporter',
						label: 'reporter'
					},
					{
						value: 'developer',
						label: 'developer'
					},
					{
						value: 'maintainer',
						label: 'maintainer'
					}
				]
			}

		}

    	/***********************************新增模块***********************************/
		$scope.popModal = function (item,str){
			$scope.currentEmployeeList = [];
		};
    	/**
    	 * 新增模块信息判断
    	 */
    	function addModule() {
         	 //实际新增模块
  	         addModuleInfo($scope.repositoryid,$scope.repositoryName);

    	}

		// 判断数组对象中某个字段是否重复
		function isRepeat(arr, key) {
			var obj = {};
			for (var i = 0; i < arr.length; i ++) {
				if (obj[arr[i][key]]) {
					return false;    // 存在
				} else {
					obj[arr[i][key]] = arr[i];
				}
			}
			return true;
		}
    	/**
    	 * 新增模块
    	 */
    	function addModuleInfo(repositoryid,repositoryName){
			console.log($scope.currentEmployeeList);
			if (!isRepeat($scope.currentEmployeeList, 'employeeId')){
				$("#checkDialog").modal('show');
				return;
			}
			$scope.modulereturnid="";
	        	var urlData={  //新增模块信息
				'moduleName':$scope.formInsertmodule.modulename,//模块名
				'moduleFunction':$scope.formInsertmodule.modulefunction,//模块功能
				'relatedModule':$scope.formInsertmodule.relatedModule,//相关联模块
				'regenerator':LocalCache.getSession('userId'),//更新者
				'createuser':LocalCache.getSession('userId'),//创建者
				'currentEmployeeList':$scope.currentEmployeeList,//创建者
				'repositoryId':repositoryid,  //仓库id
				'repositoryName':repositoryName,
				'repositoryType':$scope.repositoryType
			}
			svnModelUpdateService.addModule(urlData).then(function(data) {
				if (data.code === AgreeConstant.code) {
					inform.common("新增模块信息成功");
					$("#add_Module").modal('hide');
					//刷新页面
					getModulesAndEmployees($stateParams.repositoryid);
					$scope.modulereturnid = data.data;
					//每次打开清空其中
					$scope.formInsertmodule.modulename = '';
					$scope.formInsertmodule.modulefunction = '';

				} else {
					inform.common(data.message);
				}
				$scope.showTable = 1;
			}, function(error) {
				inform.common(Trans("tip.requestError"));
			});
    	}

     	/***********************************回填模块***********************************/
    	/**
         *  根据id获取仓库模块以及开发者信息
         */
		 function getModulesAndEmployees(repositoryid) {
     	   $scope.moduleAndEmployeeList = [];
     	   var urlData = {
			   repositoryId: $stateParams.repositoryid,
			   moduleName: $scope.formRefer.moduleName
		   }
     	   svnModelUpdateService.getModulesAndEmployees(urlData).then(function(data) {
     		   	if (data.code === AgreeConstant.code) {
					//修改页面下模块的人员信息
     		   		$scope.moduleAndEmployeeList = angular.fromJson(data.data);
					//将模块的人员进行拼接
					angular.forEach($scope.moduleAndEmployeeList,function(data){
						data.employeesString = '';
						angular.forEach(data.employeeList,function(employee){
							data.employeesString = data.employeesString + employee.employeeName + ','
							employee.modulename = data.modulename;
							employee.modulefunction = data.modulefunction;
						})
						data.employeesString = data.employeesString.substring(0,data.employeesString.length - 1)
					})
					$scope.tempModuleAndEmployeeList = angular.fromJson(data.data);
					setTimeout(tryResetPage,200);
                } else {
                 	 inform.common(data.message);
                }
             }, function() {
                  inform.common(Trans("tip.requestError"));
             });
        }
     	/**
      	 * 根据id获取模块信息
      	 */
      	function getModule(m) {
			$scope.currentEmployeeList = [];
			$scope.employeeForModule.employeeNos = []
			$scope.formUpmodule.id = m.moduleId;
			$scope.formUpmodule.modulename = m.moduleName;
			$scope.formUpmodule.modulefunction = m.moduleFunction;
			$scope.formUpmodule.relatedModule = m.relatedModule;
			angular.forEach(m.employeeList,function(employee){
				$scope.employeeForModule.employeeNos.push(employee.employeeNo);
				$scope.currentEmployeeList.push(employee);
			});
         }
        /***********************************更新模块***********************************/

        $scope.addCurrentModuleEmployee = function () {
			$scope.currentEmployeeList.push({
				employeeName: '',
				role:'//',
				permission: ''
			});
		}
		$scope.deleteCurrentModuleEmployee = function (index) {
			$scope.currentEmployeeList.splice(index, 1);
		}
		$scope.notSelect = function () {
		};
		$scope.notSelectForNew = function () {
	   };

        /**
      	 * 保存模块信息修改判断
      	 */
      	function saveModule() {
   	         //实际更新模块信息
         	 saveModuleInfo();
      	}

      	/**
      	 * 更新模块信息 修改是删除改模块的所有人 然后再新增
      	 */
      	function saveModuleInfo(){
			$scope.moduleEmployeesList = [];
			console.log($scope.currentEmployeeList);
			//整理数据
			angular.forEach($scope.employeeForModule.employeeNos,function(employeeNo){
				var tempList = {
					moduleid: $scope.formUpmodule.id,
					employeeno: employeeNo
				}
				if(tempList.moduleid !== ''){
					$scope.moduleEmployeesList.push(tempList)
				}
			});
			if (!isRepeat($scope.currentEmployeeList, 'employeeId')){
				$("#checkDialog").modal('show');
				return;
			}
      		var urlData={  //更新模块信息
   	        		'id':$scope.formUpmodule.id,//模块ID
					'moduleName':$scope.formUpmodule.modulename,//模块名
					'moduleFunction':$scope.formUpmodule.modulefunction,//模块功能
					'relatedModule':$scope.formUpmodule.relatedModule,//关联模块
					'regenerator':LocalCache.getSession('userId'),//更新者
					'currentEmployeeList':$scope.currentEmployeeList,//更新者
					'moduleEmployeesList': $scope.moduleEmployeesList, //模块和员工关系
					'repositoryId': $scope.repositoryid, //仓库id
					'repositoryName': $scope.repositoryName //仓库名
   	        	};
			svnModelUpdateService.saveModule(urlData).then(function(data) {
				if (data.code === AgreeConstant.code) {
					inform.common("修改模块信息成功");
					$("#edit_Module").modal('hide');
					//刷新页面
					getModulesAndEmployees($stateParams.repositoryid);
				} else {
					inform.common(data.message);
				}
			}, function() {
				inform.common(Trans("tip.requestError"));
			});
      	}



		 /**
     	 * 根据模块名称模糊匹配查询
     	 */
		 function searchModelByName(){
			 var newModuleList = [];
				 if($scope.tempModuleAndEmployeeList.length > 0){
					angular.forEach($scope.tempModuleAndEmployeeList, function(module, i) {
						if(module.modulename.indexOf($scope.formRefer.moduleName) >= 0){
							newModuleList.push(module);
						}
                	 });
					$scope.moduleAndEmployeeList = newModuleList;
				 }
		 }

		//设置Table格式
		 function tryResetPage(){
			$('#fixedLeftAndTop').DataTable( {
				//可被重新初始化
				retrieve:       true,
				//自适应高度
				scrollY:        'calc(100vh - 350px)',
				scrollX:        true,
				scrollCollapse: false,
				//控制每页显示
				paging:         false,
				//冻结列（默认冻结左1）
				fixedColumns:   {
					leftColumns: 0,
					rightColumns: 0
				},
				//search框显示
				searching:      false,
				//排序箭头
				ordering:       false,
				//底部统计数据
				info:           false
			} );
		}

		$scope.cancel = function () {
			$('#checkDialog').modal('hide');
		}

	}]);
})();

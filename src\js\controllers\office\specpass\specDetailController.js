
(function() {
	app.controller("specDetailManagement", ['comService', '$rootScope', '$stateParams', '$scope', 'specPassService', 'inform', 'Trans', 'AgreeConstant', '$modal',
		function(comService, $rootScope,$stateParams,  $scope, specPassService, inform, Trans, AgreeConstant, $modal) {
		
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
        // 正则校验配置
		$scope.limitList = AgreeConstant.limitList;

		//季度下拉框数据源
        $scope.quartersSelect = [{
            value: '1',
            label: '第1季度'
        },{
            value: '2',
            label: '第2季度'
        },{
            value: '3',
            label: '第3季度'
        },{
            value: '4',
            label: '第4季度'
        }];
        //是与否下拉框
        $scope.select = [{
            value: '0',
            label: '是'
        },{
            value: '1',
            label: '否'
        }];
		//添加问题内容
		$scope.formInsertin = {
			project:'', //所属项目
			projectid:'', //所属项目id
			quarters:'', //季度
			versions:'',//版本号
			baselineName:'', //基线名称
			specpass:'', //是否特殊放行
			baselineType:'', //基线类型
			baselineCreater:'', //基线建立人
			baselineCreatetime:'', //建立时间
			baselineVersions:'', //基线版本
			statusReport:'', //是否完成状态报告
			auditReport:'', //是否完成审计报告
			issue:'' //是否发布
		};
		//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
    	$scope.pages = inform.initPages(); // 初始化分页数据

    	initPages();//初始化页面信息
    	$scope.reset = reset;
		$scope.getData = getData; 
		reset();
    	getData("");
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */	
    	//当新增数据时选择日期，自动将季度回填
        $scope.dateToQuarter = function() {
            var month = $scope.formInsertin.baselineCreatetime.getMonth() + 1;
            $scope.formInsertin.quarters = (inform.dateToQuarter(month)*1+1)+"";
        };
      //当修改数据时选择日期，自动将季度回填
        $scope.upDateToQuarter = function() {
            var month = $scope.changeParam.baselineCreatetime.getMonth() + 1;
            $scope.changeParam.quarters = (inform.dateToQuarter(month)*1+1)+"";
        };
    	/**
		  * 设置列表的高度
		  */
		function setDivHeight(){
			//网页可见区域高度
			var clientHeight = document.body.clientHeight;
			var divHeight = clientHeight - (150 + 120);
			$("#divTBDis").height(divHeight);
			$("#subDivTBDis").height(divHeight);
		}
		/**
		 * 重置
		 */
		function reset() {
			$scope.formRefer = {
				project:'',//所属项目
				baselineName:'',//基线名称
				baselineCreater:'',//基线建立人
				baselineType:'',//基线类别
				startTime:'',//开始时间
				endTime:''//结束时间
			}
		}
		
		/**
		 * 新增日期时间按钮
		 */ 
		$scope.addTime = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.openedStart = false;
	        $scope.openedEnd = false;
			$scope.addtime = true; 
			$scope.uptime = false; 
		};
		/**
		 * 修改日期时间按钮
		 */ 
		$scope.upTime = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.openedStart = false;
	        $scope.openedEnd = false;
			$scope.addtime = false; 
			$scope.uptime = true; 
		};
		/**
         * 查询开始时间
         */
        $scope.openDateStart = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = true;
            $scope.openedEnd = false;
            $scope.addtime = false;
            $scope.uptime = false;
        };

        /**
         * 查询结束时间
         */
        $scope.openDateEnd = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = false;
            $scope.openedEnd = true;
            $scope.addtime = false;
            $scope.uptime = false;
        };
    	/**
    	 * 页面初始化
    	 */
    	function initPages() {
            //获取基线类型
    		$scope.baselineType = [];
    		comService.getParamList('BASELINE_TYPE','SPECBASELINE_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.baselineType =  data.data;
        		}
        		//获取所有项目名称
                $scope.projectList = [];
                comService.getProjectsName().then(function (dataParam) {
                    $scope.project = angular.fromJson(dataParam.data);
                    angular.forEach($scope.project, function(res, index) {
                        $scope.projectList.push(res);
                    })
                });
            });
    	}
		/**
		 * 获取特殊放行问题
		 */
		function getData(pageNum) {
			var urlData ={
					'project':$scope.formRefer.project,//所属项目
					'baselineName':$scope.formRefer.baselineName,//基线名称
					'baselineCreater':$scope.formRefer.baselineCreater,//基线建立人
					'baselineType':$scope.formRefer.baselineType,//基线类别
					'startTime':inform.format($scope.formRefer.startTime,'yyyy-MM-dd'),//开始时间
					'endTime':inform.format($scope.formRefer.endTime,'yyyy-MM-dd'),//结束时间
	                'page':pageNum,//当前页数
		          	'pageSize':$scope.pages.size//每页显示条数
				};
			specPassService.getProjectDetailsInfo(urlData).then(function(data) {
					if (data.code === AgreeConstant.code) {
						//项目详情
						$scope.jsonData = data.data.list;
						if ($scope.jsonData.length === 0) {
		                    inform.common(Trans("tip.noData"));
	    	                $scope.pages = inform.initPages();
		                } else {
		                    //分页信息设置
		                    $scope.pages.total = data.data.total;
		                    $scope.pages.star = data.data.startRow;
		                    $scope.pages.end = data.data.endRow;
		                    $scope.pages.pageNum = data.data.pageNum;
		                }
					} else {
						inform.common(data.message);
					}
				},
				function(error) {
					inform.common(Trans("tip.requestError"));
				});
		}
		/**
		 * 添加信息
		 */
   	 	$scope.addInfo = function() {
   	 		if("" === $scope.formInsertin.project || null == $scope.formInsertin.project){
	 			inform.common("请选择项目名");
	 		}else if("" === $scope.formInsertin.baselineCreatetime || null == $scope.formInsertin.baselineCreatetime){
   	 			inform.common("请填写建立日期");
   	 		}else {
   	 			if ($scope.formInsertin.specpass==null){
    	 			$scope.formInsertin.specpass = '';
    	 			//由于数据库中该字段不可为空 所以 若页面不输入 页默认为添加空字符串
    	 		}
   	 			if ($scope.formInsertin.statusReport==null){
    	 			$scope.formInsertin.statusReport = '';
    	 		}
   	 			if ($scope.formInsertin.auditReport==null){
    	 			$scope.formInsertin.auditReport = '';
    	 		}
   	 			if ($scope.formInsertin.issue==null){
    	 			$scope.formInsertin.issue = '';
    	 		} 
    	 		var urlData = {
   				 'project':$scope.formInsertin.project,//所属项目
   				 'quarters':$scope.formInsertin.quarters,//季度
   				 'versions':$scope.formInsertin.versions,//版本
   				 'baselineName':$scope.formInsertin.baselineName,//基线名称
   				 'specpass':$scope.formInsertin.specpass,//是否特殊放行
   				 'baselineType':$scope.formInsertin.baselineType,//基线类型
   				 'baselineCreater':$scope.formInsertin.baselineCreater,//基线建立者
   				 'baselineCreatetime':inform.format($scope.formInsertin.baselineCreatetime,'yyyy-MM-dd'),
   				 'baselineVersions':$scope.formInsertin.baselineVersions,//基线版本
   				 'statusReport':$scope.formInsertin.statusReport,//是否完成状态报告
   				 'auditReport':$scope.formInsertin.auditReport,//是否完成评审报告
   				 'issue':$scope.formInsertin.issue//是否发布
   		 	};
    	 	specPassService.addInfo(urlData).then(function(data){
    	 		if(data.code === AgreeConstant.code) {
    	 			inform.common(data.message);
    	 			getData();
    	 			$("#add_modal").modal("hide");
    	 			//添加问题内容
    	 			$scope.formInsertin = {
    	 				project:'', //所属项目
    	 				projectid:'', //所属项目id
    	 				quarters:'', //季度
    	 				versions:'',//版本号
    	 				baselineName:'', //基线名称
    	 				specpass:'', //是否特殊放行
    	 				baselineType:'', //基线类型
    	 				baselineCreater:'', //基线建立人
    	 				baselineCreatetime:'', //建立时间
    	 				baselineVersions:'', //基线版本
    	 				statusReport:'', //是否完成状态报告
    	 				auditReport:'', //是否完成审计报告
    	 				issue:'' //是否发布
    	 			};
    	 		} else{
    	 			inform.common(data.message);
    	 		}
    	 	}, function(error) {
    	 		inform.common(Trans("tip.requestError"));
    	 	});
   	 	}
   	 };
   	 	
   	 	/**
   	 	 * 修改信息弹框
   	 	 */
        $scope.popModal = function (item){
            $scope.changeParam = angular.copy(item);
            $scope.changeParam.projectId = $scope.changeParam.projectId*1;
            angular.forEach($scope.quartersSelect, function(quarters, i) {//遍历季度
            	if (quarters.label === $scope.changeParam.quarters){
            		$scope.changeParam.quarters = quarters.value;
            	}
          	 });
            angular.forEach($scope.baselineType, function(baselineType, i) {//遍历基线类型
            	if (baselineType.param_value === $scope.changeParam.baselineType){
            		$scope.changeParam.baselineType = baselineType.param_code;
            	}
          	 });
            angular.forEach($scope.select, function(flag, i) {
            	if (flag.label === $scope.changeParam.specpass){//遍历是否特殊放行
            		$scope.changeParam.specpass = flag.value;
            	}
            	if (flag.label === $scope.changeParam.statusReport){//遍历是否完成《配置状态报告》
            		$scope.changeParam.statusReport = flag.value;
            	}
            	if (flag.label === $scope.changeParam.auditReport){//遍历是否完成《配置审计报告》
            		$scope.changeParam.auditReport = flag.value;
            	}
            	if (flag.label === $scope.changeParam.issue){//遍历基线是否发布
            		$scope.changeParam.issue = flag.value;
            	}
          	 });
        };
        /**
         * 修改信息
         */
        $scope.updateInfo = function() {
        	if ($scope.changeParam.specpass==null){
            	$scope.changeParam.specpass = '';
            }
            if ($scope.changeParam.statusReport==null){
            	$scope.changeParam.statusReport = '';
            }
            if ($scope.changeParam.auditReport==null){
            	$scope.changeParam.auditReport = '';
            }
            if ($scope.changeParam.issue==null){
            	$scope.changeParam.issue = '';
            }
            var urlData = {
            	 'projectId':$scope.changeParam.projectId,//所属项目
            	 'versions':$scope.changeParam.versions,
            	 'baselineName':$scope.changeParam.baselineName,
            	 'specpass':$scope.changeParam.specpass,
            	 'statusReport':$scope.changeParam.statusReport,
            	 'auditReport':$scope.changeParam.auditReport,
            	 'issue':$scope.changeParam.issue
             };
             specPassService.upInfo(urlData).then(function(data){
            	 if(data.code === AgreeConstant.code) {
            		 inform.common(data.message);
            		 getData();
            		 $("#edit_Module").modal("hide");
            	 } else{
            		 inform.common(data.message);
            	 }
             }, function(error) {
            	 inform.common(Trans("tip.requestError"));
             });
    	 };
     /**
      * 删除弹框
      */
        $scope.open = function (item) {
            var modalInstance = $modal.open({
              templateUrl: 'myModalContent.html',
              controller: 'ModalInstanceCtrl',
              size: "sm",
              resolve: {
                items: function() {
                  return Trans("common.deleteTip");
                }
              }
            });
            modalInstance.result.then(function() {
              if (item !== null && item !== "") {
                $scope.remove(item);
              }
            });
        };
     /**
      * 删除数据 JSON.stringify(removeParam)
      */
        $scope.remove = function (item) {
        	 var id = item.id;
        	 specPassService.delInfo(id)
                .then(function(data) {     
                  if (data.code === "0000") {
                    inform.common(Trans("tip.delSuccess"));
                    getData(AgreeConstant.pageNum);
                    }else{
                       inform.common(data.message);
                    }
                }, function(error) {
                  inform.common(Trans("tip.requestError"));
                });
        };

		/**
			  * 下载历史数据Excel
			  */
		 $scope.excelDataExcel = function () {
			//拼装查询条件
			var urlData ={
				'project':$scope.formRefer.project,//所属项目
				'baselineName':$scope.formRefer.baselineName,//基线名称
				'baselineCreater':$scope.formRefer.baselineCreater,//基线建立人
				'baselineType':$scope.formRefer.baselineType,//基线类别
				'startTime':inform.format($scope.formRefer.startTime,'yyyy-MM-dd'),//开始时间
				'endTime':inform.format($scope.formRefer.endTime,'yyyy-MM-dd')
			};
			var modalInstance = $modal.open({
				templateUrl: 'myModalContent.html',
				controller: 'ModalInstanceCtrl',
				size: "sm",
				resolve: {
					items: function () {
						return "是否确定下载？";
					}
				}
			});
			modalInstance.result.then(function () {
				inform.downLoadFile('specpass/loadDataExcel', urlData, '项目基线信息情况.xlsx');
			});
		}
	    /**
	     * *************************************************************
	     *              方法声明部分                                结束
	     * *************************************************************
	     */	
	      
		}]);
})();
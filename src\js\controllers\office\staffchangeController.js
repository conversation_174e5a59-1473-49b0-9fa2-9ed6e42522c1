/*
 * @Author: fubaole
 * @Date:   2017-09-18 14:53:05
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-01-15 17:20:32
 */
(function() {
	app.controller("staffchangeManagement",['$rootScope','$scope','staffchangeService','inform','Trans','AgreeConstant','$http','LocalCache',
		function($rootScope, $scope, staffchangeService,inform, Trans, AgreeConstant,$http,LocalCache) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.formInsert = {
				startTime : '2020-01-01', 	// 开始时间
				endTime : '', 		// 技术时间
				moduleName : '', 	// 模块名称
				employeeName : '' ,	// 员工真实姓名
				repositoryName: '' //仓库名称	
			};
			$scope.pages = inform.initPages(); 	// 初始化分页数据
			$scope.getData = getData; 			// 分页相关函数         				
			$scope.staffList = [];				// 保存所有信息的集合                   
			$scope.stateMap = {    //类别
				"00" : '新增',
				"01" : '离职',
				"02" : '调出',
				"03" : '删除',
				"04" : '修改'
			};							
			// 获取数据
			getData($scope.pages.pageNum);		//在刷新页面时调用该方法
			
			//设置列表的高度
			setDivHeight();
			//窗体大小变化时重新计算高度
			$(window).resize(setDivHeight);

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */

		    //设置列表的高度
	 		function setDivHeight(){
	 			//网页可见区域高度
	 			var clientHeight = document.body.clientHeight;
	 			var divHeight = clientHeight - (150 + 190);
	 			$("#divTBDis").height(divHeight);
	 			$("#subDivTBDis").height(divHeight - 50);
	 		}

            //重置
            $scope.rest = function() {
                $scope.formInsert = {};
            };

			/**
			 * 获取数据以分页的形式
			 */
			function getData(pageNum) {
				$scope.staffList = [];
				var startTimeNew = inform.format($scope.formInsert.startTime,'yyyy-MM-dd HH:mm:ss'); //定义起始时间
				var endTimeNew = inform.format($scope.formInsert.endTime,'yyyy-MM-dd HH:mm:ss');// 定义结束时间
				var urlData = {
					'startTime' : startTimeNew, 							// 开始时间
					'endTime' : endTimeNew, 								// 截止时间
					'moduleName' : $scope.formInsert.moduleName, 			// 模块名称
					'employeeName' : $scope.formInsert.employeeName,		// 员工名称
					'repositoryName':$scope.formInsert.repositoryName,		// 仓库名称
					'currentPage' : pageNum, 								// 分页页数
					'pageSize' : $scope.pages.size    						// 分页每页大小
				};
				staffchangeService.getAllInfosByPage(urlData).then(function(data) {
					if (data.code===AgreeConstant.code) {
						var jsonData = data.data;
                        $scope.pages.goNum = null;
						$scope.staffList = jsonData.list;
						if ($scope.staffList.length===0) {
							inform.common(Trans("tip.noData"));
							$scope.pages = inform.initPages(); 	// 初始化分页数据
						} else {
							// 分页信息设置
							$scope.pages.total = jsonData.total; 			// 页面数据总数
							$scope.pages.star = jsonData.startRow; 			// 页面起始数
							$scope.pages.end = jsonData.endRow;	 			// 页面分页大小
							$scope.pages.pageNum = jsonData.pageNum; 		// 页面页数
						}
					} else {
						inform.common(data.message);
					}
				},
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
			}

            /**
             * 下载Excel
             */
            $scope.excelData=function () {
                //拼装查询条件
                var urlData={
                    'repositoryname':$scope.formInsert.repositoryName,//查询的仓库名
                    'employeeName':$scope.formInsert.employeeName,//员工真是姓名
                    'moduleName':$scope.formInsert.moduleName,//模块名
                    'endTime':inform.format($scope.formInsert.endTime,'yyyy-MM-dd HH:mm:ss'),//结束时间
                    'startTime':inform.format($scope.formInsert.startTime,'yyyy-MM-dd HH:mm:ss')//开始时间
                };
                inform.modalInstance("确定要下载吗！").result.then(function() {
                    //开启遮罩层
                    inform.showLayer("下载中。。。。。。");
                    //发送下载Excel请求
                    $http.post(
                        $rootScope.getWaySystemApi+'staffManage/staffChangeToExcel',
                        urlData,
                        {headers: {
                                'Content-Type': 'application/json',
                                'Authorization':'Bearer ' + LocalCache.getSession("token")||''
                            },
                            responseType: 'arraybuffer'//防止中文乱码
                        }
                    ).success(function(data){
                        var aForExcel;
                        //如果是IE浏览器
                        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                            var csvData = new Blob([data], {type:'application/vnd.ms-excel'});
                            window.navigator.msSaveOrOpenBlob(csvData,'SVN权限变更记录.xlsx');

                            URL.createObjectURL(csvData);
                            aForExcel = $("<a download='SVN权限变更记录.xlsx'><span class='forExcel'>下载SVN权限变更记录</span></a>");
                            $("body").append(aForExcel);
                            $(".forExcel").click();
                            aForExcel.remove();
                        }
                        //google或者火狐浏览器
                        else{
                            var blob = new Blob([data], {type: "application/vnd.ms-excel"});
                            var objectUrl = URL.createObjectURL(blob);
                            aForExcel = $("<a download='SVN权限变更记录.xlsx'><span class='forExcel'>下载SVN权限变更记录</span></a>").attr("href",objectUrl);
                            $("body").append(aForExcel);
                            $(".forExcel").click();
                            aForExcel.remove();
                        }
                        // 关闭遮罩层
                        inform.closeLayer();
                        inform.common("下载成功!");
                    });
                });
            };

			/**
			 * 开始时间
			 */ 
			$scope.openDateStart = function($event) {
				$event.preventDefault();
				$event.stopPropagation();
				$scope.openedStart = true;    //开始时间
				$scope.openedEnd = false;
			};
			
			/**
			 *  结束时间
			 */
			$scope.openDateEnd = function($event) {
				$event.preventDefault();
				$event.stopPropagation();
				$scope.openedStart = false;    
				$scope.openedEnd = true;    //结束时间
			};
		}]);
	})();
(function () {
    app.controller("mailManagement", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','mailService','inform','Trans','AgreeConstant','LocalCache','$http','OfficeFileTool',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,mailService,inform,Trans,AgreeConstant,LocalCache,$http,OfficeFileTool) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //时间选择框下拉框数据源
            $scope.timeSelect = [];
            //新增联系人列表
            $scope.list = [];
            //设置时间选择框
            setTimeSelect();
            $scope.meetingRequest=true;
            $scope.queryContacts = queryContacts;
            $scope.querySign = querySign;
            $scope.sendMail = sendMail;
            const E = window.wangEditor;
            const editor = new E("#editorDiv");
            //创建文件上传组件
            var paramObj ={listId:'thelist',
                removeCall:function (id) {
                    var index = $scope.spec.attachmentAddressID.indexOf(id);
                    $scope.spec.attachmentAddress.splice(index,1);
                    $scope.spec.attachmentSize.splice(index,1);
                    $scope.spec.attachmentAddressID.splice(index,1);
                },
                getFilePathCall:function (fileId) {
                    var index = $scope.spec.attachmentAddressID.indexOf(fileId);
                    var filePath = $scope.spec.attachmentAddress[index];
                    return filePath;
                },
                getSizeOfFiles:function () {
                    var size = 0;
                    for (var i = 0; i <  $scope.spec.attachmentSize.length; i++) {
                        size = size + parseInt($scope.spec.attachmentSize[i]);
                    }
                    return size;
                },
                uploadSuccess:function (file,response) {
                    $scope.spec.attachmentAddress.push(response.data);
                    $scope.spec.attachmentAddressID.push(file.id);
                    $scope.spec.attachmentSize.push(file.size);
                }
            };
            var uploader = OfficeFileTool.createUploader(paramObj,'mail');
            var draging = null;
            setTimeout(initULObject,1000*6);
            $scope.setDraggableTime = setDraggableTime;
            getData();
            init();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 收件人、抄送人、密送人名单变化时延迟1秒修改li属性允许拖拽
             */
            function setDraggableTime() {

                setTimeout(setDraggableOfli,1000*1);
            }
            function init(){
                // editor.config.menus 配置菜单栏，删减菜单，调整顺序    需要显示的菜单
                //使用 editor.config.excludeMenus 当只需剔除少数菜单的时候，直接设置不需要的菜单
                //【注意】不要同时使用 editor.config.menus 和 editor.config.excludeMenus，以免引起冲突和混乱
                editor.config.menus = [
                    'head',
                    'bold',
                    'fontSize',
                    'fontName',
                    'italic',
                    'underline',
                    'strikeThrough',
                    'indent',
                    'lineHeight',
                    'foreColor',
                    'backColor',
                    //    'link',
                    'list',
                    'justify',
                    //    'quote',
                    //    'emoticon',
                    //    'image',
                    //    'video',
                    //    'table',
                    //    'code',
                    'splitLine',
                    'undo',
                    'redo',
                ]
                editor.create();

            }
            /**
             * 修改li属性允许拖拽
             */
            function setDraggableOfli() {

                var subNodes = document.querySelectorAll("ul.chosen-choices li.search-choice");

                for (var i = 0; i <  subNodes.length; i++) {
                    $(subNodes[i]).attr("draggable",true);
                }

            }
            /**
             * 初始化ul，允许li元素支持拖拽
             */
            function initULObject() {

                setDraggableOfli();
                var nodes = document.querySelectorAll("ul.chosen-choices");
                //$("div#intro .head")	id="intro" 的 <div> 元素中的所有 class="head" 的元素
                //$("ul li:first")	每个 <ul> 的第一个 <li> 元素
                for (var i = 0; i < nodes.length; i++) {
                    var node = nodes[i];
                    //使用事件委托，将li的事件委托给ul
                    node.ondragstart = dragstartOfli;
                    node.ondragover = ondragoverOfli;
                }
            }

            /**
             * 将li的事件委托给ul
             */
            function dragstartOfli(event) {
                //firefox设置了setData后元素才能拖动！！！！
                if (!!window.ActiveXObject || "ActiveXObject" in window){
                    // ie和其他浏览器不一样。。。
                    event.dataTransfer.setData("Text", event.target.innerText || event.target.textContent);
                } else {
                    event.dataTransfer.setData("te", event.target.innerText || event.target.textContent); //不能使用text，firefox会打开新tab
                }
                draging = event.target;
                if (draging.nodeName === "SPAN") {
                    draging = draging.parentNode;
                }
            }

            /**
             * 将li的事件委托给ul
             */
            function ondragoverOfli(event) {
                event.preventDefault();
                var target = event.target.parentNode;
                //因为dragover会发生在ul上，所以要判断是不是li
                if (target.nodeName === "LI" && target !== draging) {
                    //_index是实现的获取index
                    if (_index(draging) < _index(target)) {
                        target.parentNode.insertBefore(draging, target.nextSibling);
                    } else {
                        target.parentNode.insertBefore(draging, target);
                    }
                }
            }


            /**
             * 获取li元素的下标
             */
            function _index(el) {
                var index = 0;
                if (!el || !el.parentNode) {
                    return -1;
                }
                while (el && el.previousElementSibling) {
                    el = el.previousElementSibling;
                    index++;
                }
                return index;
            }
            /*
            * 让bigList按照 smallList 的顺序显示
            * */
            function changeListOrder(smallList, bigList) {
                /*
                * reverse() 数组倒叙
                * unshift() 插入元素至数组头部
                *
                * */
                smallList.reverse().forEach(function (copy) {
                    // 在所有人员中取到抄送人
                    var copyItem = bigList.find(function (item) {
                        return item.mailAddr === copy;
                    });
                    // 在所有人员中删除抄送人
                    bigList = bigList.filter(function (item) {
                        return item.mailAddr !== copy;
                    });
                    // 把抄送人添加到数组头部
                    bigList.unshift(copyItem);
                });
                return bigList;
            }

            /**
             * 获取会议信息
             */
            function getData(){
                $scope.spec = LocalCache.getObject('mail_info');
                $scope.spec.attachmentAddress = [];
                $scope.spec.attachmentSize = [];
                if(null != $scope.spec.accessory && '' !== $scope.spec.accessory && ($scope.spec.accessory.indexOf(',')>-1)) {
                    $scope.spec.attachmentAddress = $scope.spec.accessory.split(',');
                }
                if (null != $scope.spec.accessory && '' !== $scope.spec.accessory && ($scope.spec.accessory.indexOf(',')<0)){
                    $scope.spec.attachmentAddress.push($scope.spec.accessory);
                }
                if(null != $scope.spec.accessorySize && '' !== $scope.spec.accessorySize && ($scope.spec.accessorySize.indexOf(',')>-1)) {
                    $scope.spec.attachmentSize = $scope.spec.accessorySize.split(',');
                }
                if (null != $scope.spec.accessorySize && '' !== $scope.spec.accessorySize && ($scope.spec.accessorySize.indexOf(',')<0)){
                    $scope.spec.attachmentSize.push($scope.spec.accessorySize);
                }
                $scope.spec.attachmentAddressID=[];

                //创建回显的文件列表 返回文件id集合
                var fileIdList = uploader.initShowFileList($scope.spec.attachmentAddress);
                if (fileIdList.length > 0) {
                    $scope.spec.attachmentAddressID = fileIdList;
                }

                $scope.spec.serialNumber = $stateParams.serialNumber;
                //查询下拉框
                queryContactDep();
                //查询签名
                querySign();
                //查询联系人
                queryContacts('initHand');
            }
            /**
             * 发送会议信息
             */
            function sendMail(){
                var richText = editor.txt.html();
                // 通过dom节点获取收件人
                var addressSelect = document.getElementById('addressSelect').nextElementSibling
                    .childNodes[0].childNodes;
                var addressList = [];
                angular.forEach(addressSelect,function (item) {
                    var str = item.innerText || item.textContent;
                    var subStr = str.substring(str.indexOf('(') + 1, str.indexOf(')'));
                    if (subStr !== '') {
                        addressList.push(subStr + $rootScope.snbcMailSuffix);
                    }
                })
                // 通过dom节点获取抄送人
                var copyTosSelect = document.getElementById('copyTosSelect').nextElementSibling
                    .childNodes[0].childNodes;
                var ccList = [];
                angular.forEach(copyTosSelect,function (item) {
                    var str = item.innerText || item.textContent;
                    var subStr = str.substring(str.indexOf('(') + 1, str.indexOf(')'));
                    if (subStr !== '') {
                        ccList.push(subStr + $rootScope.snbcMailSuffix);
                    }
                })
                // 数组去重
                addressList = Array.from(new Set(addressList))
                var urlData = {
                    'readingReceipt':$scope.readingReceipt?'0':'1',
                    'meetingRequest':$scope.meetingRequest?'0':'1',
                    'startTime':inform.format($scope.spec.meetingDay,'yyyy-MM-dd')+' '+$scope.spec.startTime+':00',
                    'endTime':inform.format($scope.spec.meetingDay,'yyyy-MM-dd')+' '+$scope.spec.endTime+':00',
                    'remindTime':$scope.spec.remindTime,
                    'location':$scope.spec.location,
                    'title':$scope.spec.theme,
                    'richText':richText,
                    'mail':addressList.join(),
                    'cc': ccList.join(),
                    'bcc':$scope.spec.blindCarbonCopys==null?'':$scope.spec.blindCarbonCopys.join(),
                    'attachmentAddress':$scope.spec.attachmentAddress,
                    'meetingId':$scope.spec.serialNumber
                };
                mailService.sendMail(urlData).then(function (data) {

                    if (data.code === AgreeConstant.code) {
                        inform.common(data.message);
                        $scope.back();
                    } else {
                        inform.commonByTime(data.message, 8000);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 查询联系人
             */
            function queryContacts(initHand) {
                var urlData = LocalCache.getSession('currentUserName');
                mailService.queryContacts(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.person = data.data;
                        //初始化调用，且上个页面存在手输人员
                        if(initHand != null&&$scope.spec.addresseesHand.length!==0){
                            var noName=[];
                            var nameList = [];
                            //所有联系人姓名
                            for(var i=0;i<$scope.person.length;i++){
                                nameList.push($scope.person[i].contactName);
                            }
                            for (var j=0;j<$scope.spec.addresseesHand.length;j++){
                                //如果手输人员姓名在联系人中存在，则添加至收件人
                                var indexName = nameList.indexOf($scope.spec.addresseesHand[j]);
                                if(indexName>-1){
                                    $scope.spec.addressees.push($scope.person[indexName].mailAddr);
                                } else {
                                    noName.push($scope.spec.addresseesHand[j]);
                                }
                            }
                            if (noName.length !== 0){
                                var modalInstance = $modal.open({
                                    templateUrl: 'errorModel.html',
                                    controller: 'ModalInstanceCtrl',
                                    size: "lg",
                                    resolve: {
                                        items: function () {
                                            return '未有 '+noName.join('、')+' 相关信息，请至联系人管理中进行维护';
                                        }
                                    }
                                });
                                modalInstance.result.then(function () {
                                    $("#upContactA").click();
                                });
                            }
                        }
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 查询联系人下拉框
             */
            function queryContactDep() {
                var urlData = {
                    loginName : LocalCache.getSession('currentUserName')
                };
                mailService.getPersonList(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.personList = data.data;
                    }
                    changeListOrder($scope.spec.addressees, $scope.personList);
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * 更新联系人
             */
            $scope.upContact = function (m) {
                if(m.contactName == null || m.contactName ===''){
                    inform.common("姓名不可为空");
                    return;
                }
                if(m.mailAddr == null || m.mailAddr ===''){
                    inform.common("邮箱地址不可为空");
                    return;
                }
                var urlData = {
                    'id':m.id,
                    'loginName':LocalCache.getSession('currentUserName'),
                    'contactName':m.contactName,
                    'mailAddr':m.mailAddr
                };
                mailService.upContact(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        layer.confirm(data.message,{
                            title:false,
                            btn:['确定']
                        },function(result){
                            layer.close(result);
                            queryContacts();
                            queryContactDep();
                        });
                    } else {
                        inform.common(data.message);
                        queryContacts();
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            };
            /**
             * 新增一个联系人
             */
            $scope.addNewBind = function (index) {
                $scope.flag = true;
                //参数明细
                var judge = {
                    'contactName':'',
                    'mailAddr': '',
                    'loginName':LocalCache.getSession('currentUserName')
                };
                $scope.list.push(judge);
            };
            //取消一行
            $scope.deleteNewBind = function (index) {
                if (index >= 0) {
                    $scope.list.splice(index, 1);
                }
            };
            /**
             * 保存信息
             */
            $scope.saveInfo = function () {
                if ($scope.list.length === 0){
                    inform.common("请输入联系人信息");
                    return;
                }
                var list = [];
                $scope.flagList = true;
                //循环明细，查看是否有重复信息
                angular.forEach($scope.list, function (one, index) {
                    var mail = one.mailAddr;
                    //查看list中不存在 邮件地址
                    var num = list.indexOf(mail);
                    if (num > -1){
                        $scope.flagList = false;
                        return;
                    }
                    list.push(mail);
                });
                //如果存在重复信息
                if (!$scope.flagList){
                    inform.common("明细中存在重复邮箱地址,请修正。");
                    return;
                }
                //新增
                addContact($scope.list);
            };
            /**
             * 新增联系人
             */
            function addContact(urlData) {
                mailService.addContact(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        layer.confirm(data.message,{
                            title:false,
                            btn:['确定']
                        },function(result){
                            layer.close(result);
                            $scope.list = [];
                            $("#add_person").modal('hide');
                            $scope.spec.contactName = '';
                            $scope.spec.mailAddr = '';
                            queryContacts();
                            queryContactDep();
                        });
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 删除联系人
             */
            $scope.deleteContact = function (index,m) {
                if (index >= 0 && m.id != null){
                    var urlData = {
                        'id': m.id
                    };
                    mailService.deleteContact(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            queryContacts();
                            queryContactDep();
                        }
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
                }
            };
            /**
             * 查询签名
             */
            function querySign() {
                var urlData = LocalCache.getSession('currentUserName');
                mailService.querySign(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        var one = data.data;
                        var head = '各位领导、同事，大家好，';
                        var foot = '--------------------------------------<br>祝工作愉快!<br>  ';
                        $scope.spec.head = one.signHead==null?head:one.signHead;
                        $scope.spec.root = one.signFoot==null?foot:one.signFoot;
                        $scope.spec.url = "此邮件由研发数据管理平台发送，研发数据管理平台可在一个系统内完成会议室预定、邮件标准化和发送邮件，" +
                            "并可到研发数据管理平台查询个人会议信息，欢迎大家使用。<a href='http://plouto.xtjc.net/' >"
                            +"<u><font color='blue'>http://plouto.xtjc.net</font></u></a><br>";
                        $scope.spec.richText = $scope.spec.head+$scope.spec.content+$scope.spec.url+$scope.spec.root;
                        var pVal = "<p style='font-size: 16px;font-weight: bolder;line-height: 25px;font-family: 等线;'>"+$scope.spec.richText+"</p>";

                        editor.txt.html(pVal);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 添加签名
             */
            $scope.addSign = function(){
                var urlData = {
                    'loginName':LocalCache.getSession('currentUserName'),
                    'signHead':$scope.spec.head,
                    'signFoot':$scope.spec.root
                };
                mailService.addSign(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        querySign();
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            };
            /**
             * 设置时间选择框
             */
            function setTimeSelect() {
                for(var i = 6; i < 24 ; i++){
                    //获取小时
                    var hour;
                    if(i < 10){
                        hour = '0' + i;
                    }else{
                        hour = i;
                    }
                    for(var j = 0; j < 12 ; j++){
                        //获取分钟（每10分钟，取一次）
                        var minutes;
                        if(j < 2){
                            minutes = '0' + j*5;
                        }else{
                            minutes = j*5;
                        }
                        var value = {
                            value:hour + ':' + minutes
                        };
                        //存入数组
                        $scope.timeSelect.push(value);
                    }
                }
            }
            /**
             * 返回
             */
            $scope.back = function (){
                $state.go($stateParams.root,{serialNumber:$stateParams.serialNumber,dataInfo:$stateParams.dataInfo});
            };
            /**
             * 修改开始时间后，默认结束时间退后一小时
             */
            $scope.changeEnd = function(){
                var startTimeArray = $scope.spec.startTime.split(':');
                var hour = parseInt(startTimeArray[0])+1;
                var before = (hour < 10) ? '0' : '';
                $scope.spec.endTime = before + hour+':'+startTimeArray[1];
            };
            /**
             * 判断会议时间是否正常
             * 时间不正常，弹出提示信息，并清空最后选择的选择框的值
             * @param flag startTime：开始时间选择框  endTime：结束时间选择框
             */
            $scope.timeChange = function(flag){
                if($scope.spec.startTime !== '' && $scope.spec.endTime  !== ''
                    && typeof ($scope.spec.startTime) !== 'undefined' && typeof ($scope.spec.endTime)  !== 'undefined'){
                    var endTimeArray = $scope.spec.endTime.split(':');
                    var startTimeArray = $scope.spec.startTime.split(':');
                    if(startTimeArray[0] > endTimeArray[0]
                        || (startTimeArray[0] === endTimeArray[0] && startTimeArray[1] >= endTimeArray[1])){
                        inform.common('会议结束时间必须大于会议开始时间，请重新选择！！！');
                        if(flag === 'startTime'){
                            $scope.spec.startTime = '';
                        }else{
                            $scope.spec.endTime = '';
                        }
                    }
                }
            };
            /**
             * 会议日期时间
             */
            $scope.meetingDayOpen = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.meetingDayOne = true;
            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();
(function () {
	app.controller("shareKnowledgeController", ['comService','SystemService','tvService','$rootScope', '$location', '$scope','$stateParams','$modal','AgreeConstant','$http',"LocalCache",
		function (comService,SystemService,tvService,$rootScope,$location,$scope,$stateParams, $modal,AgreeConstant,$http,LocalCache) {

			/**
			 * *************************************************************
			 *             初始化部分                                 开始
			 * *************************************************************
			 */
			// 捕捉鼠标事件并反馈给父页面
			document.addEventListener('click',function () {
				$('#iframeDiv',window.parent.document).click();
			},false);
			var params = $location.search();
			// 时间参数数组
			var timeList = params.time.split('、').map(item => item.split('-'));
			//要显示的pdf文件
			var url = params.pdfDoc;
			// 获取页面组件数据
			var pdfDoc = null,
				pageNum = 1
			// canvas = document.getElementById('the-canvas'),
			// ctx = canvas.getContext('2d');
			var timeOutFun;
			var hidden, visibilityChange;
			// 页面被隐藏就停止播放
			var isShow = true;
			load();
			showPlay();
			/**
			 * *************************************************************
			 *              初始化部分                                 结束
			 * *************************************************************
			 */

			/**
			 * *************************************************************
			 *              方法声明部分                                 开始
			 * *************************************************************
			 */

			/*
            * 用户最小化触发的事件
            * */
			function load()
			{
				if (typeof document.hidden !== "undefined") {
					hidden = "hidden";
					visibilityChange = "visibilitychange";
				} else if (typeof document.mozHidden !== "undefined") {
					hidden = "mozHidden";
					visibilityChange = "mozvisibilitychange";
				} else if (typeof document.msHidden !== "undefined") {
					hidden = "msHidden";
					visibilityChange = "msvisibilitychange";
				} else if (typeof document.webkitHidden !== "undefined") {
					hidden = "webkitHidden";
					visibilityChange = "webkitvisibilitychange";
				}
				document.addEventListener(visibilityChange, function () {
					if (document[hidden]){
						console.log('页面被隐藏了');
						isShow = false;
						clearTimeout(timeOutFun);
					} else {
						console.log('页面被打开了');
						timeOutFun = setTimeout(onNextPage,playTime(pageNum)*1000);
						isShow = true;
					}
				}, false);
			}

			/*
            * 重第一页开始播放
            * */
			$scope.isShow = function(){
				// 页面被隐藏就停止播放
				if (!isShow) {
					return;
				}
				clearTimeout(timeOutFun);
				pageNum = 1;
				renderPage(pageNum);
				timeOutFun = setTimeout(onNextPage,playTime(pageNum)*1000);
			}
			/*
            * 确定播放间隔
            * */
			function playTime(pageNumItem){
				let itemTime = 0;
				for (let i = 1;i<timeList.length;i++) {
					if(parseInt(timeList[i][0]) === pageNumItem) {
						itemTime = parseInt(timeList[i][1]);
					}
				}
				if (itemTime === 0) {
					itemTime = parseInt(timeList[0][0]);
				}
				return itemTime;
			}
			/*
            * 页面隐藏的时候停止播放
            * */
			$scope.isHide = function(){
				clearTimeout(timeOutFun);
			}

			/*
            * 自动滚动
            * */
			function showPlay() {
				pdfjsLib.getDocument(url).promise.then(function(pdfDoc_) {
					pdfDoc = pdfDoc_;
					//用 promise 获取页面
					var id = '';
					var idTemplate = 'cw-pdf-';
					//根据页码创建画布
					for (var j = 1; j <= pdfDoc.numPages; j++) {
						id = idTemplate + j;
						var pdfContainer = document.getElementById('pdf-container');
						var canvasNew = document.createElement('canvas');
						canvasNew.id = id;
						canvasNew.className = 'pdfClass';
						pdfContainer.appendChild(canvasNew);
					}
					//将pdf渲染到画布上去
					renderPage(pageNum);
					timeOutFun = setTimeout(onNextPage,playTime(pageNum)*1000);
				});
			}

			/*
            * 获取pdf数据
            *
            * */
			function renderPage(num) {
				pdfDoc.getPage(num).then(function(page) {
					var viewport = page.getViewport({ scale: 1, });
					var canvas = document.getElementById('cw-pdf-'+num);
					canvas.style.display = 'block';
					var ctx = canvas.getContext('2d');
					var desiredWidth = document.body.clientWidth;
					var scale = desiredWidth / viewport.width;
					var scaledViewport = page.getViewport({ scale: scale, });
					canvas.height = scaledViewport.height;
					canvas.width = scaledViewport.width;
					var renderContext = {
						canvasContext: ctx,
						viewport: scaledViewport
					};
					for (var i = 1; i < pdfDoc.numPages; i++) {
						if (i !== num){
							var otherCanvas = document.getElementById('cw-pdf-'+ i );
							otherCanvas.style.display = 'none';
						}
					}
					page.render(renderContext);
				});
			}
			//翻页方法
			function queueRenderPage(num) {
				renderPage(num);
			}

			//下一页
			function onNextPage() {
				if (pageNum >= pdfDoc.numPages) {
					pageNum = 0;
				}
				pageNum++;
				queueRenderPage(pageNum);
				timeOutFun = setTimeout(onNextPage,playTime(pageNum)*1000);
			}

			/**
			 * *************************************************************
			 *              方法声明部分                                 结束
			 * *************************************************************
			 */

		}]);
})();

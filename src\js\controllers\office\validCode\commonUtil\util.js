(function () {
    'use strict';
    app.factory('codeDataReportFactory', codeDataReportFactory);

    codeDataReportFactory.$inject=['inform', '$stateParams', '$state', 'AgreeConstant'];

    function codeDataReportFactory(inform, $stateParams, $state, AgreeConstant) {
        return {
            init: init,
            initTime: initTime,
            showPie: showPie,
            showBar: showBar,
            showCommitTimesBar: showCommitTimesBar,
            showValidCodeBar: showValidCodeBar,
            showBarAndLine: showBarAndLine,
            showLeaderCommitBarAndLine: showLeaderCommitBarAndLine,
            chartShowLoading: chartShowLoading,
            chartHideLoading: chartHideLoading,
            chartHideClear: chartHideClear,
            xyAndDeptFormatterCall: xyAndDeptFormatterCall,
            dealBgc: dealBgc,
            activeTitleClass: activeTitleClass,
            sortData: sortData,
            activeClass: activeClass,
            toMore: toMore,
        };

        // 初始化数据
        function init(scope , type) {
            type = type || '1';
            // 参数对象
            scope.formRefer = {};

            scope.initTime = function (){
                initTime(scope ,type)
            };
            initTime(scope, type);

            scope.type=type;
            if($stateParams.type){
                scope.type = $stateParams.type;
            }

        }

        // 时间段选择
        function initTime(scope,type){
            var date = new Date();
            //设置为1号，防止31号时获取到当月
            date.setDate(1);
            
            // 部门数据 默认查前一个月的，其他数据默认查前一年的
            if (type === "2"){
                date.setMonth(date.getMonth()-1);
                scope.formRefer.startTime = inform.format(date,"yyyy-MM");
                scope.formRefer.endTime = inform.format(date,"yyyy-MM");

            }else{
                scope.formRefer.endTime = inform.format(date,"yyyy-MM");
                date.setFullYear(date.getFullYear()-1);
                scope.formRefer.startTime = inform.format(date,"yyyy-MM");
            }

        }

        function toMore(scope, tabIndex, sortType, orgCode) {
            $state.go('app.office.workingHoursBoard', {
                'typeSelect': tabIndex,
                'orgCode': orgCode,
                'sortType': sortType
            });
        }

        function getPieDefaultSetting() {
            return {
                title: '默认标题',
                type:'name',
                value: 'value',
                unit: '人年'
            }
        }
        // 图表饼图显示
        function showPie(currentChart,data, mySetting){
            var setting = Object.assign(getPieDefaultSetting(), mySetting);
            var dealData = [];
            var option = {};
            if (data.length) {
                angular.forEach(data, function (eachData) {
                    dealData.push({
                        name: eachData[setting.type],
                        value: eachData[setting.value]
                    })
                });
                option = {
                    title:{
                        text:setting.title,
                        textStyle:{
                            fontSize: 12,
                            color: '#333'
                        },
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: function (data) {
                            return (
                                data.name +
                                '<br/>' +
                                data.value
                            );
                        }
                    },
                    series: [
                        {
                            name: 'Access From',
                            type: 'pie',
                            radius: '50%',
                            label: {
                                normal: {
                                    formatter: function (data) {
                                        return (
                                            data.name +
                                            '\n' +
                                            data.value
                                        );
                                    },
                                    textStyle: {
                                        fontWeight: 'normal',
                                        fontSize: 12
                                    }
                                }
                            },
                            data: dealData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };
            } else {
                option = {
                    title: [{
                        text: setting.title,
                        textStyle:{
                            fontSize: 12,
                            color: '#333'
                        },
                    },{
                        text: "暂无数据",
                        left: 'center',
                        top: 'center',
                        color: '#333',
                        textStyle: {
                            fontSize: 20
                        }
                    }]
                }
            }
            currentChart.setOption(option, true);
        }

        function getBarDefaultSetting() {
            return {
                title: '默认标题',
                xType: 'type',
                yType: 'value',
                yTypeLine: 'value',
                fontSize: 12,
                left: 'center',
                grid: {
                    gridLeft: '6%',
                    gridRight: '6%',
                },
                needCustomSeries: false,
                needDataZoom: false,
                unit: ''
            }
        }
        // 图表柱状图显示
        function showBar(currentChart,data, mySetting){
            var setting = Object.assign(getBarDefaultSetting(), mySetting);
            var xData=[];
            var yData=[];
            var option = {};
            if (data.length) {
                angular.forEach(data, function (eachData) {
                    xData.push(eachData[setting.xType]);
                    if (setting.needCustomSeries) {
                        eachData.value = eachData[setting.yType]
                        yData.push(eachData);
                    } else {
                        yData.push(eachData[setting.yType]);
                    }
                });
                option = {
                    title:{
                        text:setting.title,
                        textStyle:{
                            fontSize: setting.fontSize,
                            color: '#333'
                        },
                        left: setting.left
                    },
                    grid:{
                        left:setting.grid.gridLeft,
                        right:setting.grid.gridRight,
                        bottom:'0',
                        containLabel:true
                    },
                    xAxis: [{
                        type: 'category',
                        data: xData,
                        axisPointer: {
                            type: 'shadow'
                        },
                        axisLabel: {
                            rotate: 25
                        }
                    }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            axisLabel:{formatter:'{value} '+ setting.unit}
                        }
                    ],
                    series: [
                        {
                            data: yData,
                            type: 'bar',
                            barMaxWidth: '30%',
                            label: {
                                show: true,
                                position: 'inside'
                            }
                        }

                    ]
                };
                if (setting.needDataZoom) {
                    var zoom = 60;
                    option.dataZoom = [
                        {
                            type: 'slider',
                            showDetail: false,
                            height: 12,
                            bottom: 8,
                            start: 0,
                            end: zoom,
                            minSpan: 30
                        }
                    ]
                }
                if (setting.needCustomSeries){
                    option.series[0].label.formatter = function (params) {
                        if (params.data.rate){
                            if (params.data.rate.indexOf('%') !== -1){
                                return params.value + setting.unit + '\n' + params.data.rate;
                            }
                            return params.value + setting.unit + '\n' + params.data.rate + '%';
                        }
                        return params.value + setting.unit;
                    }
                    option.series[0].label.position = 'top';
                }
            } else {
                option = {
                    title: [{
                        text: setting.title,
                        textStyle:{
                            fontSize: setting.fontSize,
                            color: '#333'
                        },
                    },{
                        text: "暂无数据",
                        left: 'center',
                        top: 'center',
                        color: '#333',
                        textStyle: {
                            fontSize: 20
                        }
                    }]
                }
            }
            currentChart.setOption(option, true);
        }

        // 图表显示Loading
        function chartShowLoading(chart) {
            chart.showLoading({
                text: "数据加载中...",
                color: "#3174F2",
                textColor: "#000",
                maskColor: "rgba(255, 255, 255, 0)",
                zlevel: 0,
            });
        }
        
        // 图表隐藏Loading
        function chartHideLoading(chart) {
            chart.hideLoading();
        }
        
        // 清除图表
        function chartHideClear(chart) {
            chart.clear();
        }


         //提交次数按岗位统计
         function showCommitTimesBar(currentChart,data,title, legendData){
            var xData = [];
            var javaData = [];
            var androidData = [];
            var csData = [];
            var jsData = [];
            var cData = [];
            if (data.length) {
                angular.forEach(data, function (eachData) {
                    xData.push(eachData.month) ;
                    var professionalModule = eachData.professionalModule;
                    if (professionalModule === "Java工程师"){
                        javaData.push({
                            value: eachData.totalCommitTimes,
                            month: eachData.month 
                        })
                    }
                    if (professionalModule === "C/C++工程师"){
                        cData.push({
                            value: eachData.totalCommitTimes,
                            month: eachData.month 
                        })
                    }
                    if (professionalModule === "C#工程师"){
                        csData.push({
                            value: eachData.totalCommitTimes,
                            month: eachData.month 
                        })
                    }
                    if (professionalModule === "Android工程师"){
                        androidData.push({
                            value: eachData.totalCommitTimes,
                            month: eachData.month 
                        })
                    }
                    if (professionalModule === "前端工程师"){
                        jsData.push({
                            value: eachData.totalCommitTimes,
                            month: eachData.month 
                        })
                    }
                    
                });
                var option = {
                    title:{
                        text:title,
                        textStyle:{
                            fontSize: 18,
                            color: '#333'
                        }
                    },
                    grid:{
                        left:'2%',
                        right:'2%',
                        bottom:'0',
                        containLabel:true
                    },
                    legend: {
                        data: legendData
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data:  [...new Set(xData)],
                            axisPointer: {
                                type: 'shadow'
                            }
                        }
                    ],
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            crossStyle: {
                                color: '#999'
                            }
                        },
                      
                    },
                    yAxis: [
                        {
                            type: 'value',
                            axisLabel: {
                                formatter: '{value}'
                            }
                        }
                    ],
                    series: [
                        {
                            name: legendData[0],
                            type: 'bar',
                            data: javaData,
                            // itemStyle:{
                            //     color:"#FF4500"
                            // }
                        },
                        {
                            name: legendData[1],
                            type: 'bar',
                            data: androidData,
                            // itemStyle:{
                            //     color:"#77b606"
                            // }
                        },
                        {
                            name: legendData[2],
                            type: 'bar',
                            data: csData,
                            // itemStyle:{
                            //     color:"#fab70c"
                            // }
                        },
                        {
                            name: legendData[3],
                            type: 'bar',
                            data: jsData,
                            // itemStyle:{
                            //     color:"#0000CD"
                            // }
                        },
                        {
                            name: legendData[4],
                            type: 'bar',
                            data: cData,
                            // itemStyle:{
                            //     color:"#74c2f5"
                            // }
                        }
                    ]
                };
            } else {
                option = {
                    title: [{
                        text: title,
                        textStyle:{
                            fontSize: 12,
                            color: '#333'
                        },
                    },{
                        text: "暂无数据",
                        left: 'center',
                        top: 'center',
                        color: '#333',
                        textStyle: {
                            fontSize: 20
                        }
                    }]
                }
            }

            currentChart.setOption(option, true);
        }


          //人均有效代码按岗位统计
          function showValidCodeBar(currentChart,data,title, legendData){
            var xData = [];
            var javaData = [];
            var androidData = [];
            var csData = [];
            var jsData = [];
            var cData = [];
            if (data.length) {
                angular.forEach(data, function (eachData) {
                    xData.push(eachData.month) ;
                    var professionalModule = eachData.professionalModule;
                    if (professionalModule === "Java工程师"){
                        javaData.push({
                            value: eachData.avgAddLimit,
                            month: eachData.month 
                        })
                    }
                    if (professionalModule === "C/C++工程师"){
                        cData.push({
                            value: eachData.avgAddLimit,
                            month: eachData.month 
                        })
                    }
                    if (professionalModule === "C#工程师"){
                        csData.push({
                            value: eachData.avgAddLimit,
                            month: eachData.month 
                        })
                    }
                    if (professionalModule === "Android工程师"){
                        androidData.push({
                            value: eachData.avgAddLimit,
                            month: eachData.month 
                        })
                    }
                    if (professionalModule === "前端工程师"){
                        jsData.push({
                            value: eachData.avgAddLimit,
                            month: eachData.month 
                        })
                    }
                    
                });
                var option = {
                    title:{
                        text:title,
                        textStyle:{
                            fontSize: 18,
                            color: '#333'
                        }
                    },
                    grid:{
                        left:'2%',
                        right:'2%',
                        bottom:'0',
                        containLabel:true
                    },
                    legend: {
                        data: legendData
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data:  [...new Set(xData)],
                            axisPointer: {
                                type: 'shadow'
                            }
                        }
                    ],
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            crossStyle: {
                                color: '#999'
                            }
                        },
                      
                    },
                    yAxis: [
                        {
                            type: 'value',
                            axisLabel: {
                                formatter: '{value}'
                            }
                        }
                    ],
                    series: [
                        {
                            name: legendData[0],
                            type: 'bar',
                            data: javaData,

                        },
                        {
                            name: legendData[1],
                            type: 'bar',
                            data: androidData,

                        },
                        {
                            name: legendData[2],
                            type: 'bar',
                            data: csData,

                        },
                        {
                            name: legendData[3],
                            type: 'bar',
                            data: jsData,

                        },
                        {
                            name: legendData[4],
                            type: 'bar',
                            data: cData,

                        }
                    ]
                };
            } else {
                option = {
                    title: [{
                        text: title,
                        textStyle:{
                            fontSize: 12,
                            color: '#333'
                        },
                    },{
                        text: "暂无数据",
                        left: 'center',
                        top: 'center',
                        color: '#333',
                        textStyle: {
                            fontSize: 20
                        }
                    }]
                }
            }

            currentChart.setOption(option, true);
        }

        // 图表柱状图+折线图显示
        function showBarAndLine(currentChart,data, mySetting,legendData){
            var setting = Object.assign(getBarDefaultSetting(), mySetting);
            var xData=[];
            var yDataBar=[];
            var yDataLine=[];
            var option = {};
            if (data.length) {
                angular.forEach(data, function (eachData) {
                    xData.push(eachData[setting.xType]);
                    if (setting.needCustomSeries) {
                        eachData.value = eachData[setting.yType]
                        yDataBar.push(eachData);
                        yDataLine.push(eachData[setting.yTypeLine]);
                    } else {
                        yDataBar.push(eachData[setting.yType]);
                        yDataLine.push(eachData[setting.yTypeLine]);
                    }
                });
                option = {
                    legend:{
                        data:legendData
                    },
                    tooltip:{trigger:'axis'},
                    title:{
                        text:setting.title,
                        textStyle:{
                            fontSize: setting.fontSize,
                            color: '#333'
                        },
                        left: setting.left
                    },
                    grid:{
                        left:'2%',
                        right:'2%',
                        bottom:'0',
                        containLabel:true
                    },
                    xAxis: [{
                        type: 'category',
                        data: xData,
                        axisPointer: {
                            type: 'shadow'
                        },
                        axisLabel: {
                            rotate: 25
                        }
                    }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            axisLabel:{formatter:'{value} '+ setting.unit}
                        },
                        {
                            type: 'value',
                            axisLabel:{formatter:'{value} '+ setting.unit}
                        }
                    ],
                    series: [
                        {
                            name:legendData[0],
                            data: yDataBar,
                            yAxisIndex: 0,
                            type: 'bar',
                            barMaxWidth: '30%'

                        },
                        {
                            name: legendData[1],
                            data: yDataLine,
                            yAxisIndex: 1,
                            type: 'line'
                        }

                    ]
                };

                if (setting.needCustomSeries){
                    option.series[0].label.formatter = function (params) {
                        if (params.data.rate){
                            if (params.data.rate.indexOf('%') !== -1){
                                return params.value + setting.unit + '\n' + params.data.rate;
                            }
                            return params.value + setting.unit + '\n' + params.data.rate + '%';
                        }
                        return params.value + setting.unit;
                    }
                    option.series[0].label.position = 'top';
                }
            } else {
                option = {
                    title: [{
                        text: setting.title,
                        textStyle:{
                            fontSize: setting.fontSize,
                            color: '#333'
                        },
                    },{
                        text: "暂无数据",
                        left: 'center',
                        top: 'center',
                        color: '#333',
                        textStyle: {
                            fontSize: 20
                        }
                    }]
                }
            }
            currentChart.setOption(option, true);
        }


        function showLeaderCommitBarAndLine(currentChart,data, mySetting,legendData){
            var setting = Object.assign(getBarDefaultSetting(), mySetting);
            var xData=[];
            var yDataBar=[];
            var yDataBar1=[];
            var yDataLine=[];
            var option = {};
            if (data.length) {
                angular.forEach(data, function (eachData) {
                    xData.push(eachData[setting.xType]);
                    if (setting.needCustomSeries) {
                        eachData.value = eachData[setting.yType]
                        yDataBar.push(eachData);
                        yDataBar1.push(eachData[setting.yType1]);
                        yDataLine.push(eachData[setting.yTypeLine]);
                    } else {
                        yDataBar.push(eachData[setting.yType]);
                        yDataBar1.push(eachData[setting.yType1]);
                        yDataLine.push(eachData[setting.yTypeLine]);
                    }
                });
                option = {
                    legend:{
                        data:legendData
                    },
                    tooltip:{trigger:'axis'},
                    title:{
                        text:setting.title,
                        textStyle:{
                            fontSize: setting.fontSize,
                            color: '#333'
                        },
                        left: setting.left
                    },
                    grid:{
                        left:'2%',
                        right:'2%',
                        bottom:'0',
                        containLabel:true
                    },
                    xAxis: [{
                        type: 'category',
                        data: xData,
                        axisPointer: {
                            type: 'shadow'
                        },
                        axisLabel: {
                            rotate: 25
                        }
                    }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            axisLabel:{formatter:'{value} '+ setting.unit}
                        },
                        {
                            type: 'value',
                            axisLabel:{formatter:'{value} '+ setting.unit}
                        }
                    ],
                    series: [
                        {
                            name:legendData[0],
                            data: yDataBar,
                            yAxisIndex: 0,
                            type: 'bar',
                            barMaxWidth: '20%'

                        },
                        {
                            name:legendData[1],
                            data: yDataBar1,
                            yAxisIndex: 0,
                            type: 'bar',
                            barMaxWidth: '20%'

                        },
                        {
                            name: legendData[2],
                            data: yDataLine,
                            yAxisIndex: 1,
                            type: 'line'
                        }

                    ]
                };

                if (setting.needCustomSeries){
                    option.series[0].label.formatter = function (params) {
                        if (params.data.rate){
                            if (params.data.rate.indexOf('%') !== -1){
                                return params.value + setting.unit + '\n' + params.data.rate;
                            }
                            return params.value + setting.unit + '\n' + params.data.rate + '%';
                        }
                        return params.value + setting.unit;
                    }
                    option.series[0].label.position = 'top';
                }
            } else {
                option = {
                    title: [{
                        text: setting.title,
                        textStyle:{
                            fontSize: setting.fontSize,
                            color: '#333'
                        },
                    },{
                        text: "暂无数据",
                        left: 'center',
                        top: 'center',
                        color: '#333',
                        textStyle: {
                            fontSize: 20
                        }
                    }]
                }
            }
            currentChart.setOption(option, true);
        }

        // 系研和二级部门formatterCall 自定义鼠标悬浮样式
        function xyAndDeptFormatterCall(params, ticket, callback, differentName, unit) {
            unit = unit || '人年'
            var htmlStr = '';
            for(var i=0;i<params.length;i++){
                var param = params[i];
                var xName = param.name;//x轴的名称
                var seriesName = param.seriesName;//图例名称
                var value = param.value;//y轴值
                if(i===0){
                    htmlStr += xName + '<br/>';//x轴的名称
                    if (param.data.developTestRate !== null && param.data.developTestRate !== undefined){
                        htmlStr += '开发测试比'
                            + '<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:700">'
                            + param.data.developTestRate
                            + '</span>';// 开发测试比
                    }
                }
                if (value !== undefined && value !== null) {
                    htmlStr +='<div>';
                    htmlStr += param.marker;
                    //圆点后面显示的文本
                    htmlStr += seriesName;
                    htmlStr += '<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:700">';
                    if(differentName===seriesName){
                        htmlStr += +value + unit;
                    }else{
                        htmlStr += +value+ '%';
                    }
                    htmlStr += '</span>';
                    htmlStr += '</div>';
                }
            }
            return htmlStr;
        }

       /**
       * 项目工时处理td背景颜色相关
       *    number: 对应单元格的数值
       *    index: 第几个单元格 0开始
       *     property: 对应的列属性
       *     dataList: 要处理的列表数据数组
       *     type: 当前点击排序的属性
       *     sort: 当前点击的排序的方式
         */
        function dealBgc (number, index, property, dataList, type, sort) {
            if (type === property) {
                var styleObj = {
                    'font-weight': 700
                };
                if (sort === 'desc') {
                    if (index <= 4) {
                        styleObj['background-color'] = AgreeConstant.workingHoursBoard.sortColorList[index];
                    } else {
                        styleObj['background-color'] = AgreeConstant.workingHoursBoard.sortColorList[5];
                    }
                    styleObj['width'] = index === 0 ? '100%' : (parseFloat(number) / parseFloat(dataList[0][property])) * 100 + '%'
                } else {
                    if (index >= dataList.length - 5 && index <= dataList.length - 1) {
                        styleObj['background-color'] = AgreeConstant.workingHoursBoard.sortColorList[dataList.length - 1 - index];
                    } else {
                        styleObj['background-color'] = AgreeConstant.workingHoursBoard.sortColorList[5];
                    }
                    styleObj['width'] = index === dataList.length - 1 ? '100%' : (parseFloat(number) / parseFloat(dataList[dataList.length - 1][property])) * 100 + '%'
                }
                return styleObj;
            } else {
                return {}
            }
        }

        /**
        *    scope: 当前作用域
        *    type: 当前点击排序的属性
        *    sort: 当前点击的排序的方式
         *   dataList: 要处理的列表数据数组
         */
        function sortData (scope, type, sort, dataList) {
            scope.type = type;
            scope.sort = sort;
            if (sort === 'asc'){
                dataList.sort(function (a, b) {
                    return parseFloat(a[type]) - parseFloat(b[type]);
                })
            } else {
                dataList.sort(function (a, b) {
                    return parseFloat(b[type]) - parseFloat(a[type]);
                })
            }
        }
        /**
         *   type: 单元格的列对应的属性
         *   sort: 排序方式
         *   currentType: 当前点击排序的属性
         *   currentSort: 当前点击的排序的方式
         */
        function activeClass (type, sort, currentType, currentSort){
            if(currentType === type && currentSort === sort){
                return "red";
            }
            return "";
        }
        /**
          *  type: 单元格的列对应的属性
          *  currentType: 当前点击排序的属性
         */
        function activeTitleClass (type, currentType) {
            if (currentType === type) {
                return 'red';
            }
            return '';
        }
    }
})();

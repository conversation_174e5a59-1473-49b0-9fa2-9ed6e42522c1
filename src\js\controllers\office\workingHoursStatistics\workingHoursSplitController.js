(function () {
  'use strict';
  app.controller('workingHoursSplitController', [
    'companyProjectModule',
    'hardwareModeModule',
    'customerModule',
    'productTypeNameModule',
    '$scope',
    '$state',
    'comService',
    '$rootScope',
    'inform',
    'Trans',
    'AgreeConstant',
    'workingHoursService',
    'customerStoryService',
    '$stateParams',
    'LocalCache',
    '$modal',
    '$http',
    function (
      companyProjectModule,
      hardwareModeModule,
      customerModule,
      productTypeNameModule,
      $scope,
      $state,
      comService,
      $rootScope,
      inform,
      Trans,
      AgreeConstant,
      workingHoursService,
      customerStoryService,
      $stateParams,
      LocalCache,
      $modal,
      $http
    ) {
      /**
       * *************************************************************
       *             初始化部分                                 开始
       * *************************************************************
       */
      // 正则校验配置
      $scope.limitList = AgreeConstant.limitList;
      // 路由中传参数，区分是团队还是项目信息
      $scope.type = $stateParams.type;
      //查询条件 从缓存中读取
      $scope.formRefer = LocalCache.getObject('workingHoursSplit_formRefer');
      // 整理缓存中的数据
      setData();
      // 初始化分页数据
      $scope.pages = inform.initPages();
      // 任务状态
      $scope.statusMap = {
        closed: '已关闭',
        doing: '进行中',
        wait: '未开始',
        suspended: '已挂起',
      };
      //设置列表的高度
      setDivHeight();
      //窗体大小变化时重新计算高度
      $(window).resize(setDivHeight);
      //初始化
      initInfo();
      // 全局变量，用于保存调用公共组件时的对象
      var objectElement = {};
      $scope.workingHoursSync = workingHoursSync;
      /**
       * *************************************************************
       *              初始化部分                                 结束
       * *************************************************************
       */

      /**
       * *************************************************************
       *              方法声明部分                                开始
       * *************************************************************
       */
      /**
       * 设置列表的高度
       */
      function setDivHeight() {
        //网页可见区域高度
        var clientHeight = document.body.clientHeight;
        var divHeight = clientHeight - (150 + 185);
        $('#divTBDis').height(divHeight);
        $('#subDivTBDis').height(divHeight - 50);
      }

      /*
       * 整理缓存中的数据
       * */
      function setData() {
        //设置任务工时
        $scope.formRefer.taskConsumed = (
          Number($scope.formRefer.consumed) - Number($scope.formRefer.noStoryTaskConsumed)
        ).toFixed(1);
        //将未关联需求的工时重置 如果<=0,则统一设置为0
        $scope.formRefer.noStoryTaskConsumed =
          $scope.formRefer.noStoryTaskConsumed > 0 ? $scope.formRefer.noStoryTaskConsumed : 0;
      }

      /**
       * 初始化
       */
      function initInfo() {
        var count = 0;
        //获取项目归属
        $scope.projectAttributionList = [];
        $scope.projectAttributionMap = {};
        comService.queryEffectiveParam('WEEKLY_REPORT', 'AFFILIATION').then(function (data) {
          $scope.projectAttributionList = data.data;
          angular.forEach($scope.projectAttributionList, function (item) {
            $scope.projectAttributionMap[item['paramCode']] = item['paramValue'];
          });
          count++;
          if (count === 2) {
            // 同步数据
            workingHoursSync();
          }
        });
        //获取行业
        $scope.professionListSplit = ['金融', '物流', '新零售', '新兴'];
        //产品线、产品分类与产品名称
        $scope.productLineAndType = [];
        $scope.productLineAndTypeMap = {};
        comService.getParamList('PRODUCT_TYPE', '').then(function (data) {
          if (data.data) {
            $scope.productLineAndType = data.data;
            angular.forEach($scope.productLineAndType, function (item) {
              $scope.productLineAndTypeMap[item['param_code']] = item['param_value'];
            });
            count++;
            if (count === 2) {
              // 同步数据
              workingHoursSync();
            }
          }
        });
      }

      /*
       * 实时同步禅道工时
       * */
      function workingHoursSync() {
        var urlData = {
          proProjectId: $scope.formRefer.proProjectId,
        };
        workingHoursService.workingHoursSync(urlData).then(
          function (data) {
            inform.common(data.message);
            if (data.code === AgreeConstant.code) {
              getData();
            }
          },
          function () {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      /**
       * 获取禅道项目的无需求工时
       */
      function getData() {
        var urlData = {
          proProjectId: $scope.formRefer.proProjectId,
        };
        // 根据标志判断是否执行getData0函数
        var count = 0;
        // 获取项目类无需求工时列表
        workingHoursService.getWorkingHoursInfoById(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              //信息
              $scope.projectInfoList = data.data;
              $scope.projectInfoList.forEach(function (item) {
                item.taskWorkingHours = item.workingHoursSplitItemList.reduce(function (prev, cur) {
                  return Number((Number(cur.taskConsumed) + prev).toFixed(1));
                }, 0);
                item.allTaskWorkingHours = (Number(item.splitWorkingHours) + Number(item.taskWorkingHours)).toFixed(1);
              });
              count++;
              // 根据标志判断是否执行getData0函数
              if (count === 2) {
                getData0(urlData);
              }
            } else {
              inform.common(data.message);
            }
          },
          function () {
            inform.common(Trans('tip.requestError'));
          }
        );
        // 获取技术支持类无需求工时列表
        workingHoursService.getNoStoryWorkingHoursListById(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              $scope.noStoryList = data.data;
              $scope.noStoryList.forEach(function (item) {
                item.product =
                  (item.productLine ? $scope.productLineAndTypeMap[item.productLine] + '、' : '') +
                  (item.productType ? $scope.productLineAndTypeMap[item.productType] + '、' : '') +
                  (item.productSubType ? $scope.productLineAndTypeMap[item.productSubType] : '');
                item.profession = item.profession ? item.profession.split(',')[0] : '';
                item.dataFlag = item.companyProjectId !== 0;
              });
              count++;
              // 根据标志判断是否执行getData0函数
              if (count === 2) {
                getData0(urlData);
              }
              //信息
            } else {
              inform.common(data.message);
            }
          },
          function () {
            inform.common(Trans('tip.requestError'));
          }
        );
      }

      /*
       * 获取项目类无需求工时和技术支持类无需求工时
       * */
      function getData0(urlData) {
        // 获取项目类无需求工时和技术支持类无需求工时
        workingHoursService.getNoStoryWorkingHoursById(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              //信息
              $scope.projectConsumed = Number(data.data.projectConsumed);
              $scope.supportConsumed = Number(data.data.supportConsumed);
              // 计算数据
              dataFun('project');
              dataFun('support');
            } else {
              inform.common(data.message);
            }
          },
          function () {
            inform.common(Trans('tip.requestError'));
          }
        );
      }

      /*
       * 新增一行
       * */
      $scope.addNoStoryList = function () {
        //新增一行数据 默认为公司立项项目
        var item = {
          dataFlag: true,
        };
        $scope.noStoryList.push(item);
      };
      /**
       * 项目类输入拆分工时时调用的方法
       */
      $scope.projectSplitTimeChange = function (item) {
        if (isNaN(item.splitWorkingHours) || Number(item.splitWorkingHours) < 0) {
          layer.msg('请输入正确的数字！', { time: 1000 });
          return;
        }
        item.allTaskWorkingHours = Number(item.splitWorkingHours) + Number(item.taskWorkingHours);
        // 计算数据
        dataFun('project');
      };
      /**
       * 技术支持类输入拆分工时时调用的方法
       */
      $scope.supportSplitTimeChange = function (item) {
        if (isNaN(item.splitWorkTime) || Number(item.splitWorkTime) < 0) {
          layer.msg('请输入正确的数字！', { time: 1000 });
          return;
        }
        // 计算数据
        dataFun('support');
      };

      /*
       * 数据重新计算
       * */
      function dataFun(flag) {
        if (flag === 'project') {
          $scope.projectSplitConsumed = $scope.projectInfoList.reduce(function (prev, cur) {
            return Number((Number(cur.splitWorkingHours) + Number(prev)).toFixed(1));
          }, 0);
          $scope.projectWaitSplitConsumed = ($scope.projectConsumed - $scope.projectSplitConsumed).toFixed(1);
          if ($scope.projectWaitSplitConsumed < 0) {
            layer.msg('拆分工时过大，待拆分工时已为负值，请调整！', { time: 3000 });
          }
        } else {
          $scope.supportSplitConsumed = $scope.noStoryList.reduce(function (prev, cur) {
            // 处理新增一行但是没有值的情况
            const pre = Number.isNaN(prev) ? 0 : prev;
            return Number((Number(cur.splitWorkTime) + Number(pre)).toFixed(1));
          }, 0);
          $scope.supportWaitSplitConsumed = ($scope.supportConsumed - $scope.supportSplitConsumed).toFixed(1);
          if ($scope.supportWaitSplitConsumed < 0) {
            layer.msg('拆分工时过大，待拆分工时已为负值，请调整！', { time: 3000 });
          }
        }
      }

      /*
       * 查看无需求任务列表
       * */
      $scope.getTaskInfo = function () {
        var urlData = {
          proProjectId: $scope.formRefer.proProjectId,
        };
        workingHoursService.getNoStoryTaskInfoList(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              $scope.noStoryTaskInfoList = data.data;
            } else {
              inform.common(data.message);
            }
          },
          function () {
            inform.common(Trans('tip.requestError'));
          }
        );
      };
      /*
       * 保存数据
       * */
      $scope.saveInfo = function () {
        if ($scope.projectWaitSplitConsumed < 0) {
          inform.common('项目类拆分工时过大，待拆分工时已为负值，请调整！');
          return;
        }
        if ($scope.supportWaitSplitConsumed < 0) {
          inform.common('技术支持类拆分工时过大，待拆分工时已为负值，请调整！');
          return;
        }
        if (
          $scope.noStoryList.find(function (item) {
            return !(item.companyProject || item.product);
          })
        ) {
          inform.common('技术支持类工时拆分时必须选择公司立项项目！');
          return;
        }
        var urlData = $scope.projectInfoList.filter(function (item) {
          return item.splitWorkingHours > 0;
        });
        urlData.forEach(function (item) {
          item.proProjectId = $scope.formRefer.proProjectId;
          item.projectId = $scope.formRefer.projectId;
        });
        // 如果urlData没有数据就说明没有拆分数据，就只执行删除方法和添加技术支持类的拆分工时
        if (urlData.length < 1) {
          var urlData1 = {
            proProjectId: $scope.formRefer.proProjectId,
          };
          workingHoursService.deleteSplitWorkTime(urlData1).then(
            function (data) {
              if (data.code === AgreeConstant.code) {
                addSupportSplit();
              } else {
                inform.common('保存失败！');
              }
            },
            function () {
              inform.common(Trans('tip.requestError'));
            }
          );
          return;
        }
        workingHoursService.saveProjectNoStoryWorkingHoursList(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              addSupportSplit();
            } else {
              inform.common(data.message);
            }
          },
          function () {
            inform.common(Trans('tip.requestError'));
          }
        );
      };

      /*
       * 新增技术支持类拆分工时数据
       * */
      function addSupportSplit() {
        var urlData2 = $scope.noStoryList.filter(function (item) {
          return item.splitWorkTime > 0;
        });
        urlData2.forEach(function (item) {
          item.proProjectId = $scope.formRefer.proProjectId;
          item.projectId = $scope.formRefer.projectId;
          item.profession = item.profession instanceof Array ? item.profession.join(',') : item.profession;
          item.companyProjectId = item.companyProjectId ? item.companyProjectId : 0;
        });
        if (urlData2.length > 0) {
          workingHoursService.saveSupportNoStoryWorkingHoursList(urlData2).then(
            function (data) {
              inform.common(data.message);
              if (data.code === AgreeConstant.code) {
                history.back(-1);
              }
            },
            function () {
              inform.common(Trans('tip.requestError'));
            }
          );
          return;
        } else {
          inform.common('保存成功！');
          history.back(-1);
        }
      }

      /*
       * 点击删除按钮
       * */
      $scope.deleteItem = function (index) {
        $scope.noStoryList.splice(index, 1);
      };

      /*
       * 更改是否为公司立项项目
       * */
      $scope.checkboxChange = function (item) {
        if (!item.dataFlag) {
          item.companyProject = '';
          item.companyProjectId = 0;
        }
      };

      // 点击型号的初始化方法
      $scope.hardwareModeModuleInitModule = function (item) {
        objectElement = item;
        var data = {
          productLine: item.productLine,
          productType: item.productType,
          productName: item.productSubType,
          hardwareModeNo: item.hardwareModeNo,
        };
        hardwareModeModule.initModule(data, $scope, setHardwareModeInfo);
      };

      // 点击产品线的初始化方法
      $scope.productTypeNameModuleInitModule = function (item) {
        objectElement = item;
        var data = {
          productLine: item.productLine,
          productType: item.productType,
          productName: item.productSubType,
        };
        productTypeNameModule.initModule(data, $scope, setProductTypeNameInfo);
      };

      // 点击客户信息的初始化方法
      $scope.customerModuleInitModule = function (item) {
        objectElement = item;
        var data = {
          keyWord: item.customerName,
        };
        customerModule.initModule(data, $scope, setCustomerInfo);
      };
      /**
       * 公司立项项目
       */
      $scope.initCompanyProjectModule = function (item) {
        objectElement = item;
        var data = {
          projectName: '',
        };
        companyProjectModule.initModule(data, $scope, setCompanyProjectInfo);
      };

      /**
       * 根据所选中的公司立项项目回填信息
       */
      function setCompanyProjectInfo(data) {
        objectElement.companyProject = data.projectName;
        objectElement.companyProjectId = data.projectId;
        objectElement.customerName = data.customerName;
        objectElement.customerId = data.customerId;
        objectElement.profession = data.xqsshy === null ? '' : data.xqsshy.split(',')[0];
        objectElement.projectAttribution = data.xmgs;
        objectElement.productLine = data.productLine;
        objectElement.productType = data.productType;
        objectElement.productSubType = data.productSubType;
        objectElement.product =
          $scope.productLineAndTypeMap[objectElement.productLine] +
          '、' +
          $scope.productLineAndTypeMap[objectElement.productType] +
          '、' +
          (objectElement.productSubType ? $scope.productLineAndTypeMap[objectElement.productSubType] : '');
      }

      /**
       * 根据所选中的客户回填信息
       */
      function setCustomerInfo(data) {
        objectElement.customerName = data.name;
        objectElement.customerId = data.id;
      }

      /**
       * 根据所选中的产品线回填信息
       */
      function setProductTypeNameInfo(data) {
        objectElement.productLine = data.productLine;
        objectElement.productType = data.productType;
        objectElement.productSubType = data.productName;
        objectElement.product =
          $scope.productLineAndTypeMap[objectElement.productLine] +
          '、' +
          $scope.productLineAndTypeMap[objectElement.productType] +
          '、' +
          $scope.productLineAndTypeMap[objectElement.productSubType];
      }

      /**
       * 根据所选中的硬件型号回填信息
       */
      function setHardwareModeInfo(data) {
        // 选择型号后不更新产品线数据，以项目带出的为准
        // objectElement.productLine = data.productLine;
        // objectElement.productType = data.productType;
        // objectElement.productSubType = data.productSubType;
        // objectElement.product = $scope.productLineAndTypeMap[objectElement.productLine] + "、"
        //     + $scope.productLineAndTypeMap[objectElement.productType] + "、"
        //     + (objectElement.productSubType ? $scope.productLineAndTypeMap[objectElement.productSubType] : '');
        objectElement.hardwareModeNo = data.name;
      }

      /**
       * 获取产品需求子需求
       */
      $scope.getSonStoryInfo = function (id) {
        customerStoryService.getSonStoryInfo(id).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              //子产品需求
              $scope.sonStoryData = data.data;
            } else {
              inform.common(data.message);
            }
          },
          function () {
            inform.common(Trans('tip.requestError'));
          }
        );
      };

      /**
       * *************************************************************
       *              方法声明部分                                 结束
       * *************************************************************
       */
    },
  ]);
})();

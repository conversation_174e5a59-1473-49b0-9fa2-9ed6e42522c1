/*
 * @Author: lixiang
 * @Date:   2020-01-15 14:06:19
 */
 (function() {
  'use strict';
  angular.module('app')
  .controller("PersonalContributionCtrl", ['$rootScope', '$scope', 'inform',
    function($rootScope, $scope, inform) {
      /**
      **初始化开始
      */
      //首屏渲染
      showOfficeInfo();
      //设置页面可见区域高度
      setDivHeight();
      /**
       **初始化结束
       */

      /**
      **方法声明部分
      */

      /**
       * 设置列表的高度
       */
       function setDivHeight() {
          //网页可见区域高度
          var clientHeight = document.body.clientHeight;
          var divHeight = clientHeight+200;
          $("#svg").height(divHeight);
       }


        function showOfficeInfo(){


        }






      }
      ]);
})();
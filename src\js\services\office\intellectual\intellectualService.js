/*
 * @Author: dongyinggang
 * @Date:   2019-06-13 17:50:05
 * @Last Modified by:   dongyinggang
 * @Last Modified time:  2019-06-13 17:50:05
 */
(function() {
    'use strict';
  app.factory('intellectualService', intellectualService);
  intellectualService.$inject=["HttpService",'$rootScope'];

  function intellectualService(HttpService,$rootScope){
    
	var service={
			
			getIntelReportByMap:getIntelReportByMap,
			getPatentApplyByMap:getPatentApplyByMap,
			getSoftRightByMap:getSoftRightByMap,
			insertPatentApply:insertPatentApply,
            updatePatentApply:updatePatentApply,
            deletePatentApplyById:deletePatentApplyById,
            insertSoftRight:insertSoftRight,
            updateSoftRight:updateSoftRight,
            deleteSoftRightById:deleteSoftRightById,
            getDeptList:getDeptList
			
	};
    return service;
    

    
    /**
     * 分页查询知识产权情况报告
     */
    function getIntelReportByMap(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'intellectual/getIntelReportByMap', urlData);
    }
    
    /**
     * 分页查询申请专利明细
     */
    function getPatentApplyByMap(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'intellectual/getPatentApplyByMap', urlData);
    }

    /**
     * 分页查询知识产权情况报告
     */
    function getSoftRightByMap(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'intellectual/getSoftRightByMap', urlData);
    }
    /**
     * 添加申请专利明细
     */
    function insertPatentApply(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'intellectual/insertPatentApply', urlData);
    }
    
    
    /**
     * 修改申请专利明细
     */
    function updatePatentApply(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'intellectual/updatePatentApply', urlData);
    }
    
    /**
     * 删除申请专利明细
     */
    function deletePatentApplyById(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'intellectual/deletePatentApplyById', urlData);
    }
    /**
     * 添加软著明细
     */
    function insertSoftRight(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'intellectual/insertSoftRight', urlData);
    }
    
    
    /**
     * 修改软著明细
     */
    function updateSoftRight(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'intellectual/updateSoftRight', urlData);
    }
    
    /**
     * 删除软著明细
     */
    function deleteSoftRightById(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'intellectual/deleteSoftRightById', urlData);
    }
    /**
     * 获取系研及其下属二级部门
     * @return {[type]} [description]
     */
    function getDeptList(){
        return HttpService.get($rootScope.getWaySystemApi+'advice/getDeptList',
                {});
    }

  }
})();
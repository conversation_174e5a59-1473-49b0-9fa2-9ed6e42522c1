(function() {
    app.controller("personalRewardController", ['employeeRewardService', '$state', 'comService', '$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$stateParams', '$modal', '$state', '$http',
        function(employeeRewardService, state, comService, $rootScope, $scope, inform, Trans, AgreeConstant, LocalCache, $stateParams, $modal, $state, $http) {
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.pages = {
                pageNum: '1', //分页页数
                size: '100', //分页每页大小
                total: '0' //数据总数
            };
            $scope.formRefer = {};

            //初始化页面信息
            initPages();
            $scope.initPages = initPages;
            $scope.flag = false;
            //获取数据
            $scope.getData = getData;
            //初始化时设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.itemList = [];
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 65);
            }
            /**
             * 页面初始化：部门下拉表、奖项下拉表、页面初始
             */
            function initPages() {
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ? $scope.sessionEmpId : $stateParams.empId;
                $scope.formRefer.startTime = inform.format(new Date(),"yyyy")+'-01-01';
                //奖项列表
                $scope.rewardList = [];
                comService.getParamList('EMPLOYEE_REWARD', 'EMPLOYEE_REWARD').then(function(data) {
                    $scope.rewardList = angular.fromJson(data.data);
                });
                //页面初始化
                getData(1);
            }

            /**
             * 重置
             */
            $scope.reset = function() {
                $scope.formRefer.startTime = inform.format(new Date(),"yyyy")+'-01-01';
                $scope.formRefer.endTime = "" ;//时间止
            };

            function checkIsMyself() {
                $scope.employeeName = LocalCache.getSession('employeeName');
                if($state.params.empId !== LocalCache.getSession("employeeId")) {
                    var person = LocalCache.getObject('personDataBoardEmployee');
                    if(person.name) {
                        $scope.employeeName = person.name;
                    }
                }
            }

            /**
             * 获取数据以分页的形式
             */
            function getData(indexNum) {
                checkIsMyself();
                var urlData = {
                    'name': $scope.employeeName,
                    'startTime': $scope.formRefer.startTime,
                    'endTime': $scope.formRefer.endTime,
                    'page': indexNum,
                    'pageSize': $scope.pages.size
                };
                //获取数据
                employeeRewardService.getRewardInfo(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.itemList = data.data.list;
                        if ($scope.itemList.length === 0) {
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages(); //初始化分页数据
                        } else {
                            // 分页信息设置
                            $scope.pages.total = data.data.total; // 页面数据总数
                            $scope.pages.star = data.data.startRow; // 页面起始数
                            $scope.pages.end = data.data.endRow; // 页面结束数
                            $scope.pages.pageNum = data.data.pageNum;
                        }
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.formRefer.openedStart = true;
                $scope.formRefer.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = false;
                $scope.formRefer.openedEnd = true;
            };
        }
    ]);
})();

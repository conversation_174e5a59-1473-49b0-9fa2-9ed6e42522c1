(function() {
    'use strict';
  app.factory('kpiService', kpiService);
  kpiService.$inject=["HttpService",'$rootScope'];

  function kpiService(HttpService,$rootScope){
    
	var service={
			getKpiByMap:getKpiByMap,
			getKpiHistory:getKpiHistory
	};
    return service;
    
    /**
     *查询当月KPI值
     */
    function getKpiByMap(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'KPI/getKPIResult', urlData);
    }
    /**
     * 查询选择月份KPI值
     */
    function getKpiHistory(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'KPI/getKpiHistory', urlData);
    }

  }
})();
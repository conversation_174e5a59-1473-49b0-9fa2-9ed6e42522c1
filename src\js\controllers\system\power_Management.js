(function () {
    'use strict';
    app.controller("power_Management", ['$rootScope', '$scope', 'power_ManagementService', 'inform', '$modal', 'SystemService', 'Trans', 'AgreeConstant',
        function ($rootScope, $scope, power_ManagementService, inform, $modal, SystemService, Trans, AgreeConstant) {
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.title = 'orderBy';
            $scope.treeData = []; // 存放树结构数据
            $scope.treeNode = []; // 存放可拖拽树结构数据

            $scope.order = order; // 排序函数
            $scope.getData = getData; // 获取数据
            $scope.getDndData = getDndData; // 获取可拖拽数据
            $scope.open = open; // 删除数据
            $scope.popModal = popModal; // 修改弹框
            $scope.onSubmitModal = onSubmitModal; // 提交修改数据
            // 左侧树配置
            var Leftsetting = {
                edit: {
                    enable: true,
                    showRemoveBtn: false,
                    showRenameBtn: false
                },
                data: {
                    simpleData: {
                        enable: true
                    }
                },
                callback: {
                    beforeClick: "nodeSelect",
                    beforeExpand: "zTreeBeforeExpand",
                    onExpand: "zTreeOnExpand",
                    onCollapse: "zTreeOnCollapse",
                    beforeDrag: "beforeDrag",
                    beforeDrop: "beforeDrop",
                    onDrop: "onDrop",
                    beforeDragOpen: "beforeDragOpen"
                }
            };
            Leftsetting.callback.beforeClick = nodeSelect; // 点击节点前回调
            Leftsetting.callback.beforeExpand = zTreeBeforeExpand; // 展开节点前回调
            Leftsetting.callback.onExpand = zTreeOnExpand; // 展开节点回调
            Leftsetting.callback.onCollapse = zTreeOnCollapse; // 折叠节点回调
            Leftsetting.callback.beforeDrag = beforeDrag; // 节点拖拽前操作
            Leftsetting.callback.beforeDrop = beforeDrop; // 节点放下前操作
            Leftsetting.callback.onDrop = onDrop; // 节点移动后操作
            Leftsetting.callback.beforeDragOpen = beforeDragOpen; // 节点放下前展开节点操作

            getData(AgreeConstant.treeRootNode);

            // 设置侧边的高度,随窗口变动
            inform.autoHeight();
            window.onresize = inform.autoHeight;

            // 排序
            function order(str) {
                $scope.title = str;
                $scope.desc = !$scope.desc;
            }

            /**
             * 拖拽节点前触发的事件
             * @param treeId
             * @param treeNodes
             * @returns {boolean}
             */
            function beforeDrag(treeId, treeNodes) {
                //“菜单权限”节点的id为3
                var menuId = "3";
                return adjustId(treeNodes[0], menuId);
            }

            /**
             * 判断是当前节点及其所有父节点的id是否存在id为3的节点
             * @param treeNodes 节点
             * @param pId       需要匹配的节点的id
             * @param flag      判断是否允许匹配当前节点（true允许）
             * @returns {boolean} 成功/失败
             */
            function adjustId(treeNodes, pId, flag) {
                //节点为空，直接返回失败
                if (treeNodes == null) return false;
                //节点直接为想要匹配的父节点，返回成功
                if (treeNodes.id.toString() === pId && true === flag) return true;
                //节点存在父节点，则使用其父节点重新调用此方法
                var pNode = treeNodes.getParentNode();
                if (pNode != null) {
                    return adjustId(pNode, pId, true);
                }
                return false;
            }


            /**
             * 节点防下前触发的事件
             * @param treeId
             * @param treeNodes
             * @param targetNode
             * @param moveType  判断拖拽的类型：inner成为其子节点，next成为其下一个节点，prev成为其上一个节点
             * @returns {boolean} 成功/失败
             */
            function beforeDrop(treeId, treeNodes, targetNode, moveType) {
                //“菜单权限”节点的id为3,
                var menuId = "3";
                //非“菜单权限”下的叶子节点不允许拖拽
                var falg = adjustId(targetNode, menuId);
                if (!falg) return false;

                //当拖拽节点成为a节点的子节点时，若a节点不存在子节点
                if ("inner" === moveType && targetNode.isParent === false) {
                    //如果是“部门KPi看板”则不允许添加子节点
                    if (targetNode.permissionUrl.indexOf("KpiTv") >= 0) return false;
                    //如果是一个页面，则不允许添加子节点
                    if (targetNode.permissionUrl.indexOf("app.office.") >= 0) return false;
                }

                return true;
            }

            function beforeDragOpen() {
                return true;
            }

            /**
             * 拖拽节点后，节点被放下触发的事件
             * @param event
             * @param treeId  节点id
             * @param treeNodes 节点
             */
            function onDrop(event, treeId, treeNodes, targetNode) {
                //更新结构树数据
                onDropAfterGetData(treeNodes);
                //更新右侧表单数据
                getRightData(targetNode.id);
            }

            /**
             * 拖拽节点后，节点被放下触发的事件
             * @param treeNodes 节点
             */
            function onDropAfterGetData(treeNodes) {
                //获取当前节点的父节点的所有子节点
                var nodes = treeNodes[0].getParentNode().children;
                if (nodes && nodes.length > 0) {
                    var num = 1;
                    //遍历树中的数据
                    angular.forEach(nodes, function (res) {
                        res.orderBy = num;
                        res.parentId = res.pId;
                        //清空其子节点，提升接口调用效率
                        var child = res.children;
                        res.children = [];
                        //更新数据
                        updateOrderBy(res);
                        //回填子节点数据，防止拖拽后子节点消失
                        res.children = child;
                        num++;
                    });
                }
            }


            /**
             * 更新排序数据
             * @param node 节点数据
             */
            function updateOrderBy(node) {
                SystemService.saveOrupdatePermission(node)
                    .then(function (data) {
                        if (data.code !== AgreeConstant.resultCode) {
                            inform.common(data.message);
                        }
                    }, function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            /**
             * 根据parentId 获取字节点数据
             * @param parentId 父节点id
             * @param flag 是否开启深度遍历 默认开启
             */
            function getData(parentId, flag) {
                SystemService.getPermissionListByParentId(parentId)
                    .then(function (data) {
                        if (data.code == AgreeConstant.resultCode) {
                            if (data.result.length) {
                                if (flag !== true) {
                                    $scope.result = data.result; // 当前父节点下的子节点数据
                                }
                                angular.forEach(data.result, function (res, index) {
                                    var jsonTree = {
                                        "id": res.permissionId,
                                        "pId": res.parentId,
                                        "name": res.permissionName,
                                        "open": res.parentId == null ? true : false,
                                        "isParent": res.hasChild
                                    };
                                    data.result[index] = angular.extend(jsonTree, res);
                                    $scope.treeData.push(data.result[index]);
                                    if (flag !== false && res.hasChild) {
                                        getData(res.permissionId, true);
                                    }
                                });
                                $scope.treeData = inform.unique($scope.treeData);
                                $.fn.zTree.init($("#leftTree"), Leftsetting, $scope.treeData);

                            }
                        } else {
                            inform.common(data.message);
                        }
                    }, function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            /**
             * 根据parentId 获取字节点数据（右侧列表）
             * @param parentId 父节点id
             */
            function getRightData(parentId) {
                SystemService.getPermissionListByParentId(parentId)
                    .then(function (data) {
                        if (data.code == AgreeConstant.resultCode) {
                            if (data.result.length) {
                                $scope.result = data.result; // 当前父节点下的子节点数据
                            }
                        } else {
                            inform.common(data.message);
                        }
                    }, function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 根据ID 查询权限信息
            function isLastData(id) {

                SystemService.getPermission(id)
                    .then(function (res) {
                        if (res.code == AgreeConstant.resultCode) {
                            $scope.result = [];
                            power_ManagementService.getData(id)
                                .then(function (data) {
                                    if (data.code === AgreeConstant.code) {
                                        res.result.permissionMapping = data.data.permissionMapping;
                                    }
                                });
                            $scope.result.push(res.result);
                        } else {
                            inform.common(res.message);
                        }
                    }, function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 获取选中左侧树节点
            function nodeSelect(treeId, treeNode) {
                isLastData(treeNode.permissionId);
            }

            // 展开前获取数据操作
            function zTreeBeforeExpand(treeId, treeNode) {
                getRightData(treeNode.permissionId);
            }

            // 展开操作
            function zTreeOnExpand(event, treeId, treeNode) {
                angular.forEach($scope.treeData, function (res, index) {
                    if (res.permissionId === treeNode.permissionId) {
                        res.open = true;
                    }
                });
            }

            // 折叠操作
            function zTreeOnCollapse(event, treeId, treeNode) {
                angular.forEach($scope.treeData, function (res, index) {
                    if (res.permissionId === treeNode.permissionId) {
                        res.open = false;
                    }
                });
            }

            // 修改弹框信息
            function popModal(item, str) {
                getResourceType();
                if (str) {
                    $scope.parent = item;
                    $scope.flag = false;
                    $scope.perData = {
                        permissionName: "",
                        permissionCode: "",
                        permissionDesc: "",
                        permissionUrl: "",
                        permissionMapping: ""
                    };
                } else {
                    $scope.perData = angular.copy(item);
                    $scope.parent = null;
                    $scope.flag = true;
                    power_ManagementService.getData(item.permissionId)
                        .then(function(data){
                            $scope.perData.permissionMapping = data.data.permissionMapping;
                        });
                }
            }

            // 获取资源类型
            function getResourceType() {
                SystemService.getDictValueListByDictTypeCode("resource_type")
                    .then(function (data) {
                        if (data.code === AgreeConstant.resultCode) {
                            $scope.resourceTypeData = data.result;
                        } else {
                            inform.common(data.message);
                        }
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 修改权限提交
            function onSubmitModal() {
                var urlData = {
                    'permissionCode': $scope.perData.permissionCode,
                    'permissionMapping': $scope.perData.permissionMapping
                };

                if ($scope.parent) {
                    $scope.perData.parentId = $scope.parent.permissionId;
                }
                SystemService.saveOrupdatePermission($scope.perData)
                    .then(function (data) {
                        if (data.code == AgreeConstant.resultCode) {
                            //调用后端powerManagementAction中updateData方法修改后端mapping地址
                            power_ManagementService.updateData(urlData)
                                .then(function (data) {
                                    if (data.code === AgreeConstant.code) {
                                        inform.common(Trans("tip.saveSuccess"));
                                        $("#edit_Power").modal("hide");
                                        $scope.treeData = [];
                                    }else{
                                        inform.common(data.message);
                                    }
                                });
                            getData(AgreeConstant.treeRootNode);
                        } else {
                            inform.common(data.message);
                        }
                    }, function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 删除数据
            function deleteItem(item) {
                SystemService.removePermission(item.permissionId)
                    .then(function (data) {
                        if (data.code == AgreeConstant.resultCode) {
                            inform.common(Trans("tip.delSuccess"));
                            angular.forEach($scope.treeData, function (res, index) {
                                if (res.id === item.permissionId) {
                                    $scope.treeData.splice(index, 1);
                                }
                            });
                            // isLastData(item.parentId);
                            $scope.treeData = [];
                            getData(AgreeConstant.treeRootNode);
                        } else {
                            inform.common(data.message);
                        }
                    }, function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 根据父ID 查询子节点，先看有没有子级节点
            function removeCheckedData(item) {
                SystemService.getPermissionListByParentId(item.permissionId)
                    .then(function (res) {
                        if (res.code == AgreeConstant.resultCode) {
                            // 最后一级
                            if (res.result.length == 0) {
                                deleteItem(item);
                            } else {
                                inform.common(Trans("common.deleteChild"));
                            }
                        } else {
                            inform.common(res.message);
                        }
                    }, function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 删除弹框
            function open(item) {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function () {
                            return Trans("common.deleteTip");
                        }
                    }
                });
                modalInstance.result.then(function () {
                    if (item) {
                        removeCheckedData(item);
                    }
                });
            }

            // 获取拖拽树结构
            function getDndData() {
                SystemService.getAllPermission()
                    .then(function (data) {
                        if (data.code == AgreeConstant.resultCode) {
                            angular.forEach(data.result, function (res, index) {
                                var jsonTree = {
                                    "id": res.permissionId,
                                    "icon": res.hasChild ? 'fa fa-folder-open' : 'fa fa-file',
                                    "parent": res.parentId == null ? "#" : res.parentId,
                                    "text": res.permissionName,
                                    "state": {
                                        "opened": true,
                                        "disabled": false,
                                        "selected": false
                                    },
                                    "child": true
                                };
                                data.result[index] = angular.extend(jsonTree, res);
                                $scope.treeNode.push(data.result[index]);
                            });
                            $scope.treeNode = inform.unique($scope.treeNode);
                            $scope.treeNode = inform.toTreeData($scope.treeNode);
                        } else {
                            inform.common(data.message);
                        }
                    }, function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

        }
    ]);
})();
(function () {
    app.controller("report_0004InAdvanceUpdateController", ['reviewProblemService', 'comService', '$http', 'LocalCache', '$rootScope', '$state', '$stateParams', '$scope', '$modal', 'reportService', 'inform', 'Trans', 'AgreeConstant',
        function (reviewProblemService, comService, $http, LocalCache, $rootScope, $state, $stateParams, $scope, $modal, reportService, inform, Trans, AgreeConstant ) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.employeeMap = {};
            //控制评审时间字段是否必填
            $scope.planCompletionDatFlag =false;
            //校验变量
            $scope.limitList = AgreeConstant.limitList;
            //页面分页信息
            $scope.pages = {
                pageNum: '',	//分页页数
                size: '',		//分页每页大小
                total: ''		//数据总数
            };
            //评审主题Map
            $scope.themeMap = reviewThemeListConfig;

            //是否跟踪下拉框数据源
            $scope.typeMap = [{
                value: '0',
                label: '邮件'
            }, {
                value: '1',
                label: '会议'
            }];

            //评审级别下拉框数据源
            $scope.levelSelect = [{
                value: '0',
                label: '一级'
            }, {
                value: '1',
                label: '二级'
            }, {
                value: '2',
                label: '三级'
            }];

            //是否跟踪下拉框数据源
            $scope.trackSelect = [{
                value: '0',
                label: '是'
            }, {
                value: '1',
                label: '否'
            }];

            //评审结果下拉框数据源
            $scope.resSelect = [{
                value: '0',
                label: '有条件通过'
            }, {
                value: '1',
                label: '通过'
            }, {
                value: '2',
                label: '不通过'
            }, {
                value: '3',
                label: '未评审'
            }];

            $scope.projectChange = projectChange;

            //初始化页面信息
            initPages();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
            }


            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            //当修改数据时选择日期，自动将季度回填
            $scope.dateToQuarter = function () {
                if(typeof ($scope.changeParam.PLAN_COMPLETION_DATE) === 'undefined'
                    || '' === $scope.changeParam.PLAN_COMPLETION_DATE || null == $scope.changeParam.PLAN_COMPLETION_DATE){
                    $scope.changeParam.QUARTER = '';
                    return;
                }
                var month = $scope.changeParam.PLAN_COMPLETION_DATE.getMonth() + 1;
                $scope.changeParam.QUARTER = inform.dateToQuarter(month);
            };

            /**
             * 页面初始化
             */
            function initPages() {
                //获取所有项目名称
                $scope.projectList = [];
                //获取最近关闭或者进行中的所有项目名称
                var urlData = {flag: 'doing'};
                comService.getProjectsNameByParams(urlData).then(function (data) {
                    $scope.projectList = angular.fromJson(data.data);
                    //修改时查询页面传来的Json
                    $scope.changeParam = JSON.parse($stateParams.jsonResult);
                    $scope.changeParam.TEM_NAME = parseInt($scope.changeParam.TEM_NAME);
                    //获取评委列表
                    getJudgeByReview($scope.changeParam.ID);
                    //查询项目信息
                    projectChange($scope.changeParam.TEM_NAME);
                });
                //获取员工信息
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function(data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                        $scope.employeeMap = {};
                        for(var i = 0;i < $scope.employeeList.length; i++) {
                            $scope.employeeMap[$scope.employeeList[i].realName] = $scope.employeeList[i].companyTitleLevel;
                        }
                    }
                });

            }

            /**
             * 查询评委信息
             * @param reviewId
             */
            function getJudgeByReview(reviewId) {
                var param = {'reviewId': reviewId};
                reportService.getJudgeByReview(param).then(function (data) {
                    $scope.changeParam.judgeList = angular.fromJson(data.data);
                });
            }

            /**
             * 当修改评审类型变化为邮件时
             */
            $scope.reviewTypeAdd = function () {
                if ($scope.changeParam.REVIEW_TYPE === '0') {
                    //类型为邮件类型,将会议持续时间置为0
                    $scope.changeParam.REVIEW_MEETING_TIME = '';
                }
                $scope.duringChange();
            };

            /**
             * 根据关键角色名称，获取其等级
             * @param item
             */
            $scope.setJudgeGrade = function(item){
                if(typeof (item) !== 'undefined') {
                    item.judgeGrade = $scope.employeeMap[item.judgeName];
                }
            };

           /**
             * 根据关键角色名称，遍历以获取其编号
             * @param item
             */
            $scope.setJudgeNo = function(item){
                for(var i = 0;i <$scope.employeeList.length;i++){
                    if($scope.employeeList[i].realName === item.judgeName){
                        item.employeeId = $scope.employeeList[i].employeeNo;
                    }
                }
            };


            /**
             * 校验评委列表中是否存在重复
             * 规则：
             * 1.重复则提示"评委×××,×××重复,请修改"并返回false
             * 2.无重复则返回true
             * @returns {boolean}
             */
            function verifyJudgeList() {
                var verifyList = [];
                var duplicate = "";
                for (var i = 0; i < $scope.changeParam.judgeList.length; i++) {
                    if (verifyList.indexOf($scope.changeParam.judgeList[i].judgeName) > -1
                        && duplicate.indexOf($scope.changeParam.judgeList[i].judgeName) < 0) {
                        duplicate = duplicate.concat($scope.changeParam.judgeList[i].judgeName).concat(",");
                    }
                    verifyList.push($scope.changeParam.judgeList[i].judgeName);
                }
                //如果为空,说明无重复则返回true;
                if (!duplicate) {
                    return true;
                }
                //不为空,则提示哪些评委重复,并返回false
                inform.common("评委" + duplicate.substring(0, duplicate.length - 1) + "存在重复,请修改");
                return false;
            }

            //修改信息
            $scope.updateInfo = function () {
                //校验jList中是否存在相同的评委
                if (!verifyJudgeList()) {
                    return;
                }
                var urlData = {
                    'excelName': 'app.office.report_0004',
                    'ID': $scope.changeParam.ID,
                    'QUARTER': $scope.changeParam.QUARTER,
                    'PLAN_COMPLETION_DATE': inform.format($scope.changeParam.PLAN_COMPLETION_DATE, 'yyyy-MM-dd'),
                    'PRODUCT_LINE': $scope.changeParam.productLine,
                    'TEM_NAME': $scope.changeParam.TEM_NAME,
                    'REVIEW_LEVEL': $scope.changeParam.REVIEW_LEVEL,
                    'IS_TRACK_ONZENTAO': $scope.changeParam.IS_TRACK_ONZENTAO,
                    'DOC_PAGES': $scope.changeParam.DOC_PAGES,
                    'PARTICIPANTS': $scope.changeParam.PARTICIPANTS,
                    'REVIEW_CONTENT': $scope.changeParam.REVIEW_CONTENT,
                    'REVIEW_MEETING_TIME': $scope.changeParam.REVIEW_MEETING_TIME,
                    'LIABLE_PERSON': null == $scope.changeParam.LIABLE_PERSON?null:$scope.changeParam.LIABLE_PERSON.join(','),
                    'PROJECT_MANAGER': $scope.changeParam.PROJECT_MANAGER,
                    'PROJECT_ADMINISTRATORS': $scope.changeParam.PROJECT_ADMINISTRATORS,
                    'COMMENTS': $scope.changeParam.COMMENTS,
                    'VERSION': $scope.changeParam.VERSION,
                    'REVIEW_TYPE': $scope.changeParam.REVIEW_TYPE,
                    'REVIEW_THEME': $scope.changeParam.REVIEW_THEME,
                    'judgeList': JSON.stringify($scope.changeParam.judgeList),
                    'REVIEW_RESULT': $scope.changeParam.REVIEW_RESULT,
                    'REVIEW_PROBLEM_NUMBER': $scope.changeParam.REVIEW_PROBLEM_NUMBER
                };

                reportService.verifyReviewExist(urlData).then(function (data) {
                    //请求失败则弹出后台返回信息
                    if (data.code !== AgreeConstant.code) {
                        inform.common(data.message);
                        return;
                    }
                    //结果大于0,则提示已存在
                    if (data.data > 0) {
                        inform.common("该评审已存在,请勿重复添加");
                        return;
                    }
                    //为0则进行更新
                    updateReviewInfo(urlData);
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            };

            function updateReviewInfo(urlData) {
                reportService.updateReportInfo(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        layer.confirm(data.message, {
                            title: false,
                            btn: ['确定']
                        }, function (result) {
                            layer.close(result);
                            $state.go('app.office.report_0004',{flag:'notMenu'});
                        });
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * 选择项目时，回填产品线项目经理、项目助理信息
             * @param projectId
             */
            function projectChange(projectId){
                reportService.getProjectInfoById(projectId).then(function (data) {
                    if (data.code !== AgreeConstant.code) {
                        inform.common(data.message);
                        return;
                    }
                    data.data = angular.fromJson(data.data);
                    if (data.data.length === 0) {
                        inform.common("该项目不存在")
                    }else{
                        $scope.changeParam.productLineName = data.data.productLineName;
                        $scope.changeParam.productLine = data.data.productLine;
                        $scope.changeParam.PROJECT_MANAGER = data.data.projectManager;
                        $scope.changeParam.PROJECT_ADMINISTRATORS = data.data.projectAssistant;
                    }

                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }


            //修改页面新增一行
            $scope.addBind = function () {
                var judgeVO = {
                    "reviewId": $scope.changeParam.ID,
                    'employeeId':'',
                    "judgeName": "",
                    "judgeGrade": "",
                    "judgeWorkLoad": '0',
                    'judgeJoin':'0'
                };
                $scope.changeParam.judgeList.push(judgeVO);
                //不为邮件评审，则更新评委投入工作量
                if($scope.changeParam.REVIEW_TYPE !== '0'){
                    $scope.duringChange();
                }
            };

            /**
             * 修改：持续时间变化时,说明是会议
             *
             * 各评委工作量 = 持续时间
             * 评审工作量 = 各评委工作量相加
             * 评审效率 = 问题 / 评审工作量
             */
            $scope.duringChange = function () {
                //评委工作量赋值
                for (var i = 0; i < $scope.changeParam.judgeList.length; i++) {
                    ///若评委不参加，直接置为0
                    if($scope.changeParam.judgeList[i].judgeJoin === '1'){
                        //评委工作量是会议时长
                        $scope.changeParam.judgeList[i].judgeWorkLoad = '0';
                    }else {
                        //评委工作量是会议时长
                        $scope.changeParam.judgeList[i].judgeWorkLoad = $scope.changeParam.REVIEW_MEETING_TIME;
                    }
                }

            };

            /**
             * 修改页面取消一行,若有关联问题,则弹出提示框,否则直接删除
             * @param index
             */
            $scope.deleteBind = function (index) {
                var urlData = {
                    'reviewId': $scope.changeParam.ID,
                    'employeeId':$scope.changeParam.judgeList[index].employeeId,
                    'judge': $scope.changeParam.judgeList[index].judgeName
                };
                reviewProblemService.selectByReviewAndJudge(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        if (data.data === 0) {
                            deleteJudge(index);
                        } else {
                            var deleteModal = $modal.open({
                                templateUrl: 'myModal',
                                controller: 'ModalInstanceCtrl',
                                size: "sm",
                                resolve: {
                                    items: function () {
                                        return "该评委关联了" + data.data + "条问题,\n" +
                                            "确认删除么？";
                                    }
                                }
                            });
                            deleteModal.result.then(function () {
                                deleteJudge(index);
                            })
                        }
                    }
                });

            };

            /**
             * 执行页面的删除评委操作
             * @param index
             */
            function deleteJudge(index) {
                if (index >= 0) {
                    $scope.changeParam.judgeList.splice(index, 1);
                }
            }

            /**
             *  修改评审时间
             */
            $scope.updateDateOne = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.updateTimeOne = true;
            };

            /**
             * 评委是否参与选项改变后触发的事件
             */
            $scope.judgeJoinChange = function(item){
                //状态为参加
                if(item.judgeJoin === '1'){
                    item.judgeWorkLoad = '0';
                }else{
                    //若为会议类型，则为工作量复制，否则清空
                    if($scope.changeParam.REVIEW_TYPE !== '0'){
                        item.judgeWorkLoad = $scope.changeParam.REVIEW_MEETING_TIME
                    }else{
                        item.judgeWorkLoad = '';
                    }
                }
            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();
/*
* @Author: fubaole
* @Date:   2016-09-13 15:41:14
* @Last Modified by:   fubaole
* @Last Modified time: 2017-10-13 13:30:39
*/

(function() {
    'use strict';
    app.factory('LocalCache',['$window',function($window){
      return{
        setSession:function(key,value){
          $window.sessionStorage[key]=value;
        },
        getSession:function(key,defaultValue){
          return  $window.sessionStorage[key] || defaultValue;
        },
        //存储单个属性
        set :function(key,value){
          $window.localStorage[key]=value;
        },        //读取单个属性
        get:function(key,defaultValue){
          return  $window.localStorage[key] || defaultValue;
        },        //存储对象，以JSON格式存储
        setObject:function(key,value){
          $window.sessionStorage[key]=JSON.stringify(value);
        },        //读取对象
        getObject: function (key) {
          return JSON.parse($window.sessionStorage[key] || '{}');
        }, // 清空缓存
        clearDate:function(){
           $window.sessionStorage.clear();
        }

      };
  }]);
})();
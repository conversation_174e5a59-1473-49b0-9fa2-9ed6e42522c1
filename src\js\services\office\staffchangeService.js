
  /*
* @Author: fubaole
* @Date:   2017-09-05 10:05:00
* @Last Modified by:   fubaole
* @Last Modified time: 2018-02-24 13:45:13
*/
(function() {
    'use strict';
  app.factory('staffchangeService', staffchangeService);
  staffchangeService.$inject=["HttpService",'$rootScope'];

  function staffchangeService(HttpService,$rootScope){
    var service={
      getAllInfosByPage:getAllInfosByPage

    };
    return service;

    /**
     * 获取所有的信息以分页的形式
     *	urlData = {
     * 'startTime' : startTimeNew, // 开始时间
     * 'endTime' : endTimeNew, // 截止时间
     * 'moduleName' : $scope.formInsert.moduleName, // 模块名称
     * 'employeeName' : $scope.formInsert.employeeName,// 员工名称
     * 'currentPage' : $scope.pages.pageNum, // 分页页数
     * 'pageSize' : $scope.pages.size    // 分页每页大小										
     * 	};
   */
	function getAllInfosByPage(urlData) {			
		return HttpService.post($rootScope.getWaySystemApi+'staffManage/selectChangeByMore',urlData);
	}
	
  }
})();

(function() {
    'use strict';
    app.factory('projectWorkingDetailService', projectWorkingDetailService);
    projectWorkingDetailService.$inject=["HttpService",'$rootScope'];

    function projectWorkingDetailService(HttpService,$rootScope){
        function getGatherHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'projectWorkingHours/getProGatherHoursInfo',urlData);
        }
        function getTitleHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'projectWorkingHours/getTitleHoursInfo',urlData);
        }
        function getDeptHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'projectWorkingHours/getDeptHoursInfo',urlData);
        }
        function getHourTypeHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'projectWorkingHours/getHourTypeHoursInfo',urlData);
        }
        function getPersonHoursInfoList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'personWorkingHours/getPersonHoursInfoList',urlData);
        }
        return {
            getGatherHoursInfo: getGatherHoursInfo,
            getTitleHoursInfo: getTitleHoursInfo,
            getDeptHoursInfo: getDeptHoursInfo,
            getHourTypeHoursInfo: getHourTypeHoursInfo,
            getPersonHoursInfoList: getPersonHoursInfoList,
        };
    }
})();
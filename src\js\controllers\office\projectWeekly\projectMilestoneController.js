//# sourceURL=js/controllers/office/projectWeekly/projectMilestoneController.js
(function() {
    app.controller("projectMilestoneController", ['projectMilestoneService','sprintVersionsService','$state', 'comService', '$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$stateParams',
        function(projectMilestoneService,sprintVersionsService,state, comService, $rootScope, $scope, inform, Trans, AgreeConstant, LocalCache, $stateParams) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.typeFlag = $stateParams.projectTypeFlag;
            $scope.add = {};
            //季度展示Map
            $scope.planSVMap = {
                "是":'延期',
                "否":'正常'
            };

            //季度下拉框数据源
            $scope.quarterSelect = [{
                value: '是',
                label: '延期'
            },{
                value: '否',
                label: '正常'
            }];

            //是否影响后续里程碑进度下拉框数据源
            $scope.affectSelect = [{
                value: '是',
                label: '是'
            },{
                value: '否',
                label: '否'
            }];
            //页面分页信息
            $scope.pages = inform.initPages();
            //获取数据
            $scope.getData = getData;
            //初始化页面信息
            initProject();

            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            function initProject(){
                //项目范围
                $scope.scopes = [];
                comService.getParamList('WEEKLY_REPORT','SCOPE').then(function(data) {
                    if (data.data) {
                        $scope.scopes =  data.data;
                    }
                });
                //调用查询方法
                getData(AgreeConstant.pageNum);
            }

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#subDivTBDisForContext1").height(divHeight);
                $("#subDivTBDisForContext2").height(divHeight);
            }

            /**
             * 获取项目
             */
            function getData(page) {
                if($scope.typeFlag==='P'){
                    //项目，获取里程碑信息
                    var urlData = {
                        cname:  $stateParams.projectId,
                        page: page,
                        pageSize: $scope.pages.size
                    };
                    projectMilestoneService.getProjectMilestoneInfo(urlData).then(function(data) {
                        setItemList(data);
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
                }else {
                    //团队，获取冲刺版本信息
                    var urlDataTeam ={
                        'page':page,// 当前页数
                        'pageSize':$scope.pages.size,// 每页显示条数
                        'projectId':$stateParams.projectId
                    };
            	    sprintVersionsService.getSprintVersionInfo(urlDataTeam).then(function(data) {
        				setItemList(data);
        			},
        			function(error) {
        				inform.common(Trans("tip.requestError"));
        			});
                }
            }

            function setItemList(data){
                if (data.code === AgreeConstant.code) {
                    //详情
                    $scope.itemList = data.data.list;
                    if ($scope.itemList.length===0) {
                        $scope.pages = inform.initPages();
                    } else {
                        // 分页信息设置
                        $scope.pages.total = data.data.total;
                        $scope.pages.star = data.data.startRow;
                        $scope.pages.end = data.data.endRow;
                        $scope.pages.pageNum = data.data.pageNum;
                        setDivHeight();
                    }
                } else {
                    inform.common(data.message);
                }
            }

            /**
             * 判断是新增还是修改
             * @param item 不存在则新增，存在则修改
             */
            $scope.popModal = function(item) {

                if (!item) {
                    $scope.add = {};
                } else {
                    $scope.changeParam = angular.copy(item);
                    $scope.update = angular.copy(item);
                }
            };

            /**
             * 修改信息
             */
            $scope.updateInfo = function() {
            	//当里程碑状态为延期修改时，延期主要原因和延期解决措施也应为必填项
            	if ($scope.changeParam.planSVLevel==="是"){
            		if ($scope.changeParam.delayReason===""||$scope.changeParam.delayReason==null||$scope.changeParam.countermeasure===""||$scope.changeParam.countermeasure==null){
            			inform.common("当前里程碑已延期，请填写延期主要原因与延期解决措施。");
                        return;
            		}
            	}
                var urlData = {
                    'cname': $stateParams.projectId,
                    'id': $scope.changeParam.id,
                    'taskName': $scope.changeParam.taskName,
                //    'planEndTime': inform.format($scope.changeParam.planEndTime, 'yyyy-MM-dd'),
                //    'endTime': inform.format($scope.changeParam.endTime, 'yyyy-MM-dd'),
                    'delayAffect': $scope.changeParam.delayAffect,
                    'delayReason': $scope.changeParam.delayReason,
                    'countermeasure': $scope.changeParam.countermeasure
                };
                projectMilestoneService.updateProjectMilestoneInfo(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        common(data.message,function () {
                            getData(AgreeConstant.pageNum);
                            $("#edit_Module").modal("hide");
                        });
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            };

          /*********************************************************************************************************/



            /**
             * 提示信息
             * @param str  提示信息
             * @param func 确定时执行的函数
             */
            function  common(str,func){
                layer.confirm(str,{
                    title:false,
                    btn:['确定']
                },function(result){
                    layer.close(result);
                    if(typeof (func) !== 'undefined'){
                        func();
                    }
                });
            }

            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        }
    ]);
})();
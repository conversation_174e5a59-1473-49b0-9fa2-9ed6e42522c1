(function () {
    'use strict';

    /**
     * Config for the router
     */
    angular
        .module('app')
        .run([
            '$rootScope',
            '$state',
            '$stateParams',
            function ($rootScope, $state, $stateParams) {
                $rootScope.$state = $state;
                $rootScope.$stateParams = $stateParams;
            },
        ])
        .config([
            '$stateProvider',
            '$urlRouterProvider',
            function ($stateProvider, $urlRouterProvider) {
                $urlRouterProvider.otherwise('/login/index');
                $stateProvider
                    .state('app', {
                        abstract: true,
                        url: '/app',
                        templateUrl: 'tpl/app.html',
                        resolve: {
                            translatePartialLoader: [
                                '$translate',
                                '$translatePartialLoader',
                                function ($translate, $translatePartialLoader) {
                                    $translatePartialLoader.addPart('global');
                                    return $translate.refresh();
                                },
                            ],
                        },
                    })
                    .state('page404', {
                        url: '/page404',
                        templateUrl: 'tpl/blocks/page404.html',
                    })
                    .state('KpiTv', {
                        url: '/KpiTv',
                        templateUrl: 'tpl/tv/tvn.html',
                    })
                    // 登录
                    .state('login', {
                        url: '/login',
                        template: '<div ui-view class="fade-in-right-big smooth"></div>',
                        resolve: {
                            translatePartialLoader: [
                                '$translate',
                                '$translatePartialLoader',
                                function ($translate, $translatePartialLoader) {
                                    $translatePartialLoader.addPart('login');
                                    return $translate.refresh();
                                },
                            ],
                        },
                    })
                    .state('login.index', {
                        url: '/index',
                        cache: false,
                        templateUrl: 'tpl/login/login.html',
                        controller: 'login',
                        resolve: {
                            deps: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('Encrypt');
                                },
                            ],
                        },
                    })
                    .state('login.edit_password', {
                        url: '/edit_password',
                        cache: false,
                        templateUrl: 'tpl/login/edit_password.html',
                        controller: 'edit_password',
                        resolve: {
                            deps: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('Encrypt');
                                },
                            ],
                        },
                    })
                    //首页
                    .state('app.index_bench', {
                        url: '/index_bench',
                        templateUrl: 'tpl/bench/index_bench.html',
                        controller: 'index_bench',
                        params: {
                            empId: null,
                            years: null,
                            quarter: null,
                            tagType: null,
                        },
                        resolve: {
                            loadMyCtrlKpi: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load([
                                        'js/controllers/office/personalDataBoard/personalKpiController.js',
                                        'js/controllers/office/personalDataBoard/personalReviewQuestionsController.js',
                                        'js/controllers/office/personalDataBoard/personalReviewExportController.js',
                                        'js/controllers/office/personalDataBoard/personalDevelopBugsController.js',
                                        'js/controllers/office/personalDataBoard/personalWorkingHoursController.js',
                                        'js/controllers/office/personalDataBoard/personalAssessController.js',
                                        'js/controllers/office/personalDataBoard/personalDepartmentContributeController.js',
                                        'js/controllers/office/personalDataBoard/xyKpiController.js',
                                        'js/controllers/office/kpi/kpiRelationController.js',
                                        'js/controllers/office/kpi/kpiAttendanceController.js',
                                        'js/controllers/office/personalDataBoard/personalQualityController.js',
                                        'js/controllers/office/personalDataBoard/personalRewardController.js',
                                        'js/controllers/office/personalDataBoard/personalKeyTechIssueController.js',
                                        'js/controllers/office/personalDataBoard/personalStarController.js',
                                        'js/controllers/office/personalDataBoard/personalBadgeController.js',
                                        'js/controllers/office/personalDataBoard/personalValidcodeController.js',
                                        'js/controllers/office/personalDataBoard/taskViewController.js',
                                        'js/controllers/office/personalDataBoard/personalOpsDataController.js',
                                        'js/controllers/office/personalDataBoard/personalQaDataController.js',
                                    ]);
                                },
                            ],
                            loadMyService: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load([
                                        'js/services/office/roleKpi/roleKpiService.js',
                                        'js/services/office/personalDataBoard/personalDataBoardService.js',
                                        'js/services/office/personalDataBoard/xyKpiService.js',
                                        'js/services/office/personalDataBoard/personalStarService.js',
                                        'js/services/office/personalDataBoard/personalBadgeService.js',
                                        'js/services/office/personalDataBoard/personalValidcodeService.js',
                                        'js/services/office/personalDataBoard/personalOpsDataService.js',
                                        'js/services/office/personalDataBoard/personalQaDataService.js',
                                        'js/services/office/kpi/kpiRelationService.js',
                                        'js/services/office/kpi/kpiAttendanceService.js',
                                        'js/services/office/lowQualityProblem/lowQualityService.js',
                                        'js/services/office/blackEvent/blackEventService.js',
                                        'js/services/office/qualityAccident/quality_accident_management_service.js',
                                        'js/services/office/employeeReward/employeeRewardService.js',
                                        'js/services/office/keyTechIssue/keyTechIssueService.js',
                                        'js/services/office/kpi/kpiRelationService.js',
                                        'js/services/office/workingHoursManagement/taskView.js',
                                    ]);
                                },
                            ],
                            loadMyFactory: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load(
                                        'js/controllers/office/personalDataBoard/commonUtil/util.js'
                                    );
                                },
                            ],
                        },
                    })
                    //首页--我的星星--我的星数明细--部门贡献获星详情页
                    .state('app.personal_star_department_detail', {
                        url: '/personal_star_department_detail',
                        templateUrl: 'tpl/office/personalDataBoard/personalStarDepartmentContribute.html',
                        controller: 'personalStarDepartmentContributeController',
                        params: {
                            empId: null,
                            year: null,
                            orderRange: null,
                            scoreOrder: null,
                            totalScore: null,
                            starNum: null,
                        },
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load(
                                        'js/controllers/office/personalDataBoard/personalStarDepartmentContributeController.js'
                                    );
                                },
                            ],
                            loadMyService: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load(
                                        'js/services/office/personalDataBoard/personalStarDepartmentContributeService.js'
                                    );
                                },
                            ],
                        },
                    })
                    .state('app.index_edit', {
                        url: '/index_edit/:pageId/:isDefault',
                        templateUrl: 'tpl/bench/index_edit.html',
                        controller: 'index_edit',
                        resolve: {
                            deps: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('angular-sortable-view');
                                },
                            ],
                            showImgsFiles: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('angularFileUpload');
                                },
                            ],
                        },
                    })

                    // 系统管理
                    .state('app.system', {
                        abstract: true,
                        url: '/system',
                        template: '<div ui-view></div>',
                    })

                    //公司管理
                    .state('app.system.company_Management', {
                        url: '/company_Management',
                        templateUrl: 'tpl/system/company_Management.html',
                        controller: 'company_Management',
                    })
                    // 新增公司
                    .state('app.system.company_Add', {
                        url: '/company_Add',
                        templateUrl: 'tpl/system/company_Add.html',
                        controller: 'company_Add',
                        resolve: {
                            deps: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('ui.select');
                                },
                            ],
                        },
                    })
                    // 修改公司
                    .state('app.system.company_Change', {
                        url: '/company_Change/:companyId',
                        templateUrl: 'tpl/system/company_Change.html',
                        controller: 'company_Change',
                        resolve: {
                            deps: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('ui.select');
                                },
                            ],
                        },
                    })
                    // 公司详情
                    .state('app.system.company_Look', {
                        url: '/company_Look/:companyId',
                        templateUrl: 'tpl/system/company_Look.html',
                        controller: 'company_Look',
                    })

                    //人员管理
                    .state('app.system.personnel_Management', {
                        url: '/personnel_Management',
                        templateUrl: 'tpl/system/personnel_Management.html',
                        controller: 'personnel_Management',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/system/personnel_Management.js');
                                },
                            ],
                            deps: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('angularFileUpload');
                                },
                            ],
                        },
                    })
                    //人员添加
                    .state('app.system.personnel_Add', {
                        url: '/personnel_Add/:orgId',
                        templateUrl: 'tpl/system/personnel_Add.html',
                        controller: 'personnel_Add',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/system/personnel_Add.js');
                                },
                            ],
                        },
                    })
                    //人员修改
                    .state('app.system.personnel_Change', {
                        url: '/personnel_Change/:employeeId',
                        templateUrl: 'tpl/system/personnel_Change.html',
                        controller: 'personnel_Change',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/system/personnel_Change.js');
                                },
                            ],
                        },
                    })
                    // 人员详情
                    .state('app.system.personnel_Look', {
                        url: '/personnel_Look/:employeeId',
                        templateUrl: 'tpl/system/personnel_Look.html',
                        controller: 'personnel_Look',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/system/personnel_Look.js');
                                },
                            ],
                        },
                    })
                    //用户管理
                    .state('app.system.user_Management', {
                        url: '/user_Management',
                        templateUrl: 'tpl/system/user_Management.html',
                        controller: 'user_Management',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/system/user_Management.js');
                                },
                            ],
                        },
                    })
                    //用户组管理
                    .state('app.system.userGroup_Management', {
                        url: '/userGroup_Management',
                        templateUrl: 'tpl/system/userGroup_Management.html',
                        controller: 'userGroup_Management',
                    })
                    //新增用户组
                    .state('app.system.userGroup_Add', {
                        url: '/userGroup_Add?type',
                        templateUrl: 'tpl/system/userGroup_Add.html',
                        controller: 'userGroup_Add',
                    })
                    //修改用户组
                    .state('app.system.userGroup_Change', {
                        url: '/userGroup_Change/:groupId',
                        templateUrl: 'tpl/system/userGroup_Change.html',
                        controller: 'userGroup_Change',
                    })
                    //用户组详情
                    .state('app.system.userGroup_Look', {
                        url: '/userGroup_Look/:groupId',
                        templateUrl: 'tpl/system/userGroup_Look.html',
                        controller: 'userGroup_Look',
                    })
                    // 新增用户
                    .state('app.system.user_Add', {
                        url: '/user_Add/:userId/:pageName',
                        templateUrl: 'tpl/system/user_Add.html',
                        controller: 'user_Add',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/system/user_Add.js');
                                },
                            ],
                        },
                    })
                    // 用户详情
                    .state('app.system.user_Look', {
                        url: '/user_Look/:userId',
                        templateUrl: 'tpl/system/user_Look.html',
                        controller: 'user_Look',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/system/user_Look.js');
                                },
                            ],
                        },
                    })
                    // 权限管理
                    .state('app.system.power_Management', {
                        url: '/power_Management',
                        templateUrl: 'tpl/system/power_Management.html',
                        controller: 'power_Management',
                        resolve: {
                            deps: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('ui.tree');
                                },
                            ],
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/system/power_Management.js');
                                },
                            ],
                            loadMyService: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/services/office/system/power_ManagementService.js');
                                },
                            ],
                        },
                    })
                    // 组织机构管理
                    .state('app.system.orgin_Management', {
                        url: '/orgin_Management',
                        templateUrl: 'tpl/system/orgin_Management.html',
                        controller: 'orgin_Management',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/system/orgin_Management.js');
                                },
                            ],
                            deps: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('angularFileUpload');
                                },
                            ],
                        },
                    })
                    //角色管理
                    .state('app.system.role_Management', {
                        url: '/role_Management',
                        templateUrl: 'tpl/system/role_Management.html',
                        controller: 'role_Management',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/system/role_Management.js');
                                },
                            ],
                        },
                    })
                    //角色添加
                    .state('app.system.role_Add', {
                        url: '/role_Add',
                        templateUrl: 'tpl/system/role_Add.html',
                        controller: 'role_Add',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/system/role_Add.js');
                                },
                            ],
                        },
                    })
                    //角色修改
                    .state('app.system.role_Change', {
                        url: '/role_Change/:roleId',
                        templateUrl: 'tpl/system/role_Change.html',
                        controller: 'role_Change',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/system/role_Change.js');
                                },
                            ],
                        },
                    })
                    //角色详情
                    .state('app.system.role_Look', {
                        url: '/role_Look/:roleId',
                        templateUrl: 'tpl/system/role_Look.html',
                        controller: 'role_Look',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/system/role_Look.js');
                                },
                            ],
                        },
                    })
                    //区域管理
                    .state('app.system.area_Management', {
                        url: '/area_Management',
                        templateUrl: 'tpl/system/area_Management.html',
                        controller: 'area_Management',
                        resolve: {
                            deps: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('angularFileUpload');
                                },
                            ],
                        },
                    })
                    //配置管理
                    .state('app.system.param_Management', {
                        url: '/param_Management',
                        templateUrl: 'tpl/system/param_Management.html',
                        controller: 'param_Management',
                    })
                    //配置管理编辑页面
                    .state('app.system.param_Change', {
                        url: '/param_Change/:paramId',
                        templateUrl: 'tpl/system/param_Change.html',
                        controller: 'param_Change',
                    })
                    //配置管理添加页面
                    .state('app.system.param_add', {
                        url: '/param_add/:pid/:areaName',
                        templateUrl: 'tpl/system/param_add.html',
                        controller: 'param_add',
                    })
                    //字典管理
                    .state('app.system.model_Management', {
                        url: '/model_Management',
                        templateUrl: 'tpl/system/model_Management.html',
                        controller: 'model_Management',
                    })
                    // 字典管理详情页面
                    .state('app.system.model_ManagementDetail', {
                        url: '/model_ManagementDetail/:typeCode',
                        templateUrl: 'tpl/system/model_ManagementDetail.html',
                        controller: 'model_ManagementDetail',
                    })
                    //日志管理
                    .state('app.system.log_Management', {
                        url: '/log_Management',
                        templateUrl: 'tpl/system/log_Management.html',
                        controller: 'log_Management',
                    })
                    //区块管理
                    .state('app.system.blocks_Management', {
                        url: '/blocks_Management',
                        templateUrl: 'tpl/system/blocks_Management.html',
                        controller: 'blocks_Management',
                    })
                    .state('app.system.blocks_Add', {
                        url: '/blocks_Add',
                        templateUrl: 'tpl/system/blocks_Add.html',
                        controller: 'blocks_Add',
                    })
                    .state('app.system.blocks_Change', {
                        url: '/blocks_Change/:blockContentId',
                        templateUrl: 'tpl/system/blocks_Change.html',
                        controller: 'blocks_Change',
                    })
                    .state('app.system.template_bench', {
                        url: '/template_bench/:pageId/:isDefault',
                        templateUrl: 'tpl/system/template_bench.html',
                        controller: 'template_bench',
                        resolve: {
                            deps: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('angular-sortable-view');
                                },
                            ],
                            showImgsFiles: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('angularFileUpload');
                                },
                            ],
                        },
                    })
                    .state('app.system.work_configurine', {
                        url: '/work_configurine',
                        templateUrl: 'tpl/system/work_configurine.html',
                        controller: 'work_configurine',
                    })
                    // 预览工作台配置页面
                    .state('app.system.preview_page', {
                        url: '/preview_page/:pageId',
                        templateUrl: 'tpl/system/preview_page.html',
                        controller: 'preview_page',
                    })

                    /*服务管理*/
                    .state('app.service', {
                        abstract: true,
                        url: '/service',
                        template: '<div ui-view></div>',
                    })
                    //实时监控
                    .state('app.service.monitor', {
                        url: '/monitor',
                        templateUrl: 'tpl/service/monitor.html',
                        controller: 'monitor',
                    })

                    .state('app.service.service-state', {
                        //服务状态
                        url: '/service-state',
                        templateUrl: 'tpl/service/service-state.html',
                        controller: 'service-state',
                    })
                    .state('app.service.config', {
                        //配置
                        url: '/config',
                        templateUrl: 'tpl/service/config.html',
                        controller: 'config',
                    })
                    .state('app.service.api', {
                        //API
                        url: '/api',
                        templateUrl: 'tpl/service/api.html',
                        controller: 'api',
                    })
                    .state('app.service.gateway', {
                        //网关
                        url: '/gateway',
                        templateUrl: 'tpl/service/gateway.html',
                        controller: 'gateway',
                    })
                    .state('app.service.real-time-status', {
                        //实时状态
                        url: '/real-time-status',
                        templateUrl: 'tpl/service/real-time-status.html',
                        controller: 'real-time-status',
                    })
                    .state('app.service.logs', {
                        //日志
                        url: '/logs',
                        templateUrl: 'tpl/service/logs.html',
                        controller: 'logs',
                    })
                    .state('app.service.resources', {
                        //资源监控
                        url: '/resources',
                        templateUrl: 'tpl/service/resources.html',
                        controller: 'resources',
                    })
                    .state('app.service.resources-detail', {
                        //资源监控线程详情
                        url: '/resources-detail/:appName',
                        templateUrl: 'tpl/service/resources-detail.html',
                        controller: 'resources-detail',
                    })

                    .state('app.service.ssh', {
                        //资源监控
                        url: '/ssh',
                        templateUrl: 'tpl/service/ssh.html',
                        controller: 'ssh',
                    })
                    .state('app.service.springCloud', {
                        //分布式
                        url: '/springCloud',
                        templateUrl: 'tpl/service/springCloud.html',
                        controller: 'springCloud',
                    })
                    .state('app.service.instances', {
                        //索引
                        url: '/instances',
                        templateUrl: 'tpl/service/instances.html',
                        controller: 'instances',
                    })
                    .state('app.service.history', {
                        //历史记录
                        url: '/history',
                        templateUrl: 'tpl/service/history.html',
                        controller: 'history',
                    })
                    .state('app.service.replicas', {
                        //备份
                        url: '/replicas',
                        templateUrl: 'tpl/service/replicas.html',
                        controller: 'replicas',
                    })

                    // 消息管理
                    .state('app.news', {
                        abstract: true,
                        url: '/news',
                        template: '<div ui-view></div>',
                    })

                    // 消息模板
                    .state('app.news.news_template', {
                        url: '/news_template',
                        templateUrl: 'tpl/news/news_template.html',
                        controller: 'news_template',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/news/news_template.js');
                                },
                            ],
                        },
                    })
                    // 新增消息模板
                    .state('app.news.template_Add', {
                        url: '/template_Add',
                        templateUrl: 'tpl/news/template_Add.html',
                        controller: 'template_Add',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/news/template_Add.js');
                                },
                            ],
                        },
                    })
                    // 编辑消息模板
                    .state('app.news.template_Change', {
                        url: '/template_Change/:messageTemplateId',
                        templateUrl: 'tpl/news/template_Change.html',
                        controller: 'template_Change',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/news/template_Change.js');
                                },
                            ],
                        },
                    })
                    // 发送消息
                    .state('app.news.send_news', {
                        url: '/send_news',
                        templateUrl: 'tpl/news/send_news.html',
                        controller: 'send_news',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/news/send_news.js');
                                },
                            ],
                        },
                    })
                    // 发送消息详情
                    .state('app.news.send_newsDetail', {
                        url: '/send_newsDetail/:messageId',
                        templateUrl: 'tpl/news/send_newsDetail.html',
                        controller: 'send_newsDetail',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/news/send_newsDetail.js');
                                },
                            ],
                        },
                    })
                    // 接收消息
                    .state('app.news.receive_news', {
                        url: '/receive_news',
                        templateUrl: 'tpl/news/receive_news.html',
                        controller: 'receive_news',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/news/receive_news.js');
                                },
                            ],
                        },
                    })
                    // 接收消息详情
                    .state('app.news.receive_newsDetail', {
                        url: '/receive_newsDetail/:messageId',
                        templateUrl: 'tpl/news/receive_newsDetail.html',
                        controller: 'receive_newsDetail',
                        resolve: {
                            loadMyCtrl: [
                                '$ocLazyLoad',
                                function ($ocLazyLoad) {
                                    return $ocLazyLoad.load('js/controllers/news/receive_newsDetail.js');
                                },
                            ],
                        },
                    });
            },
        ]);
})();

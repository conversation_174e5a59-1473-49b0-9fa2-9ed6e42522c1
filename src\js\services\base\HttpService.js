(function() {
  	'use strict';
	app.factory('HttpService', HttpService);

	HttpService.$inject=["$q","$http","$rootScope","LocalCache","inform"];

	function HttpService($q,$http,$rootScope,LocalCache,inform){
		var service={
			postLogin:postLogin,
			post:post,
      get :get,
			put :put,
      delete:deleteReq,
			jsonp:jsonp
		};

		return service;

    //  GET请求
		function get(url,params){
			var deferred = $q.defer();
      inform.showLayer();
			$http({
					method : 'GET',
					url: url,
					params :params,
					headers: {
						'Content-Type': 'application/json',
						'Authorization':'Bearer ' + LocalCache.getSession("token")||''
					}
			})
			.success(function(data) {
        inform.closeLayer();
				deferred.resolve(data);
			})
			.error(function(data) {
        inform.closeLayer();
        //inform.common("失败");
				deferred.reject(data);
      });
			return deferred.promise;
		}

    //  PUT请求
    function put(url,params){
      var deferred = $q.defer();
      inform.showLayer();
      $http({
          method : 'PUT',
          url: url,
          data :params,
          headers: {
            'Content-Type': 'application/json',
            'Authorization':'Bearer ' + LocalCache.getSession("token")||''
          }
      })
      .success(function(data) {
        inform.closeLayer();
        deferred.resolve(data);
      })
      .error(function(data) {
        inform.closeLayer();
        //inform.common("失败");
        deferred.reject(data);
      });
      return deferred.promise;
    }
    // Delete 请求
    function deleteReq(url,params){
      var deferred = $q.defer();
      inform.showLayer();
      $http({
          method : 'DELETE',
          url: url,
          data :params,
          headers: {
            'Content-Type': 'application/json',
            'Authorization':'Bearer ' + LocalCache.getSession("token")||''
          }
      })
      .success(function(data) {
        inform.closeLayer();
        deferred.resolve(data);
      })
      .error(function(data) {
        inform.closeLayer();
        deferred.reject(data);
      });
      return deferred.promise;
    }
    //  POST请求
		function post(url,postData){
			var deferred = $q.defer();
      inform.showLayer();
			$http({
				method: 'POST',
				url: url,
				data: postData,
				headers: {
					'Content-Type': 'application/json',
					'Authorization':'Bearer ' + LocalCache.getSession("token")||''
				},
			})
			.success(function(data) {
        inform.closeLayer();
				deferred.resolve(data);
			})
			.error(function(data) {
        inform.closeLayer();
        //inform.common("失败");
				deferred.reject(data);
			});
		  return deferred.promise;
		}

    // 登录post提交
    function postLogin(url,postData){
      var deferred = $q.defer();
      inform.showLayer();
      $http({
          method: 'POST',
          url: url,
          data: postData,
          headers: {
              'Content-Type': 'application/json',
              'Authorization':'Basic d2ViX2FwcDo='
          },
      })
      .success(function(data) {
          inform.closeLayer();
          deferred.resolve(data);
      })
      .error(function(data) {
          inform.closeLayer();
          //inform.common("失败");
          deferred.reject(data);
      });
      return deferred.promise;
    }

    // 跨域请求
		function jsonp(url,params){
			params.callback='JSON_CALLBACK';
			var deferred = $q.defer();
      var index = layer.load(1, {
        content:'加载中...',
        shade: [0.6,'#000'],
        success: function(layero){
          layero.find('.layui-layer-content').css({'padding-top':'70px'});
        }
      });
			console.log("params.callback:"+params.callback);
			$http({
					method : 'jsonp',
					url : url,
					params :params,
					responseType:'json'
			})
			.success(function(data) {
				console.log("jsonpData:"+data);
				deferred.resolve(data);
			}).error(function(data) {
				console.log("errorjsonpData:"+data);
				deferred.reject(data);
			});
      layer.closeAll();
			return deferred.promise;
		}



	}
})();
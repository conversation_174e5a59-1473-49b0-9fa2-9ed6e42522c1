(function () {
    app.controller("plmPlantDataController", ['plmService','$rootScope', '$scope', '$modal', 'inform', 'LocalCache', 'Trans', 'AgreeConstant', '$http','$state', 'proProjectModule',
        function (plmService, $rootScope, $scope, $modal, inform, LocalCache, Trans, AgreeConstant, $http,$state, proProjectModule) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */

            //绑定文件控件改变事件
            $("#filesImg").change(submitForm);
            /**
             * 获取没有关闭的禅道项目清单
             */
            queryProjects();


            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            $scope.selectFile = function() {
                document.getElementById("filesImg").click();
            };

            /**
             * 获取没有关闭的禅道项目清单
             */
            function queryProjects(){
                //获取没有关闭的禅道项目清单
                plmService.queryProjects().then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.projects = data.data;
                    }else{
                        inform.common(data.message);
                    }
                },
                function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 选择上传文件后事件
             */
            function fileChangeReset(){
                //通过表单元素的reset方法实现选择文件的重置
                $("#uploadForm")[0].reset();
            }


            /**
             * 上传文件
             */
             function submitForm() {
                if (!$scope.projectName || $scope.projectName === '') {
                    inform.common("请选择禅道项目!");
                    fileChangeReset();
                    return false;
                }
                //表单id  初始化表单值
                var formData = new FormData();
                //获取文档中有类型为file的第一个input元素
                var file = document.querySelector('input[type=file]').files[0];
                if (!file) {
                    inform.common("请先选择文件!");
                    return false;
                }
                if(file.size >AgreeConstant.fileSize){
                    inform.common("上传文件大小不能超过2M");
                    fileChangeReset();
                    return false;
                }
                formData.append('file', file);
                formData.append('projectName',$scope.projectName);
                if (!file.name.endsWith(".pppx")) {
                    inform.common("请选择.pppx类型的文档进行上传!");
                    return false;
                }

                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function () {
                            return "确定要上传文件吗！";
                        }
                    }
                });
                var uploadUrl =  $rootScope.getWaySystemApi + 'plm/uploadPlantData';

                modalInstance.result.then(function () {
                    //开启遮罩层
                    inform.showLayer("上传中。。。。。。");
                    $.ajax({
                        url: uploadUrl,
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        beforeSend: function (request) {
                            request.setRequestHeader("Authorization", 'Bearer ' + LocalCache.getSession("token") || '');
                        },
                        success: function (result) {
                            // 关闭遮罩层
                            inform.closeLayer();
                            inform.common(result.message);
                            //移除文件名称
                            fileChangeReset();
                        },
                        error: function (error) {
                            inform.common(Trans("tip.requestError"));
                        }
                    });
                });


            }

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
(function () {
    app.controller("versionsAddManagement", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','versionsService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope,comService,$scope,$state,$stateParams, $modal,versionsService,inform,Trans,AgreeConstant,LocalCache,$http) {
       	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	
    	$scope.limitList = AgreeConstant.limitList; // 正则校验配置
    	
        //新增时文件列表
        $scope.fileList = [];
        
        //用于判断是否为新增，若为空，则为新增
        $scope.versionId = $stateParams.versionId;
        //设置列表的高度
        setDivHeight();
        //窗体大小变化时重新计算高度
        $(window).resize(setDivHeight);
        
        //初始化
        initProject();
       
        //是否下拉框数据源
        $scope.points = [{
            value: '0',
            label: '是'
        }, {
            value: '1',
            label: '否'
        }];

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
        /**
         *  发布时间按钮
         */
        $scope.setOffTime_input = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.returnTime_input1 = false;
            $scope.setOffTime_input1 = true;
        };
        /**
         * 新增一个文件
         */
        $scope.addNewBind = function () {
            var file = {
            	'fileName':'',
            	'fileVersion':'',
            	'alterDesc':'',
            	'alterVersion':'',
            	'relevanceVersion':''
            };
            $scope.fileList.push(file);
        };
        /**
         * 取消一行
         */
        $scope.deleteNewBind = function (index) {
            if (index >= 0) {
                $scope.fileList.splice(index, 1);
            }
        };
		/**
		 * 初始化
		 */
    	function initProject() {
    		//获取项目下拉框
    		 $scope.projectIdList = [];
    		 comService.getProjectsByLineOffice('').then(function (data) {
                 $scope.projectIdList =data.data;
                 getOne();
             });
    	}
    	/**
         * 若为修改，回填信息
         */
        function getOne() {
        	//为新增
            if (null ==$scope.versionId){
            	return;
            } 
            var urlData = {
                'id': $scope.versionId,//项目版本id
            };
            versionsService.selectOne(urlData).then(function (data) {
            	$scope.paramInfo = data.data;
            	//获取文件明细
        		angular.forEach($scope.paramInfo.fileList, function (one, index) {
        			$scope.fileList.push({
        				"fileName":one.fileName,
        	    		"fileVersion":one.fileVersion,
        	    		"alterDesc":one.alterDesc,
        	    		"alterVersion":one.alterVersion,
        	    		"relevanceVersion":one.relevanceVersion
        			});
        		});
             },
             function () {
                 inform.common(Trans("tip.requestError"));
             });
        }
        /**
         * 保存信息
         */
        $scope.saveInfo = function(){
     	  if ($scope.fileList.length===0){
       			inform.common("请添加发布文件");
       			return;
       	  }
     	  if (null ==$scope.paramInfo.projectId || ''===$scope.paramInfo.projectId){
        		inform.common("请选择所属项目");
        		return;
     	  }
     	 var list = []; 
    	 $scope.flagList = true;
    	 //循环文件明细，查看是否有重复信息
    	 angular.forEach($scope.fileList, function (one, index) {
    		 var mains = one.fileName+"-"+one.fileVersion;
    		 //查看list中不存在 文件名-版本
    		 var num = list.indexOf(mains);
    		 if (num > -1){
    			$scope.falseNum = mains;
    			$scope.flagList = false;
    			return;
    		 }
    		 list.push(mains);
    	 });
    	 //如果存在重复信息
    	 if ($scope.flagList===false){
    		 var mains = $scope.falseNum.split("-");
    		 //将进行提示
    		 inform.common("发布文件中"+mains[0]+"版本"+mains[1]+"重复,请修正。");
    		 return;
    	 }
          var urlData = {
                'issueTime':inform.format( $scope.paramInfo.issueTime, 'yyyy-MM-dd'),//发布时间
                'isBaseLine': $scope.paramInfo.isBaseLine,//是否发布基线
                'firmware': $scope.paramInfo.firmware,//固件版本
                'plm': $scope.paramInfo.plm,//PLM编号
                'versionNumber':$scope.paramInfo.versionNumber,//项目版本号
                'baseLine': $scope.paramInfo.baseLine,//发布基线
                'isPlm': $scope.paramInfo.isPlm,//是否PLM发布
                'hardware': $scope.paramInfo.hardware,//硬件型号
                'remark': $scope.paramInfo.remark,//备注
                'fileList':$scope.fileList,//发布文件
            };
            if ($scope.versionId==null){
            	//新增
            	urlData.projectId = $scope.paramInfo.projectId;
           	 	addInfo(urlData);
            } else {
            	//修改 
            	urlData.id = $scope.versionId;
           	 	upInfo(urlData)
            }
        }
        /**
         * 添加信息
         */
        function addInfo(urlData) {
        	versionsService.addInfo(urlData).then(function (data) {
                callBackFunction(data);
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
         }
         /**
          * 修改信息
          * @param urlData
          */
         function upInfo(urlData) {
        	 versionsService.updateInfo(urlData).then(function (data) {
                 callBackFunction(data);
             }, function (error) {
                 inform.common(Trans("tip.requestError"));
             });
          }

          function callBackFunction(data){
             if (data.code === AgreeConstant.code) {
                  layer.confirm(data.message,{
                      title:false,
                      btn:['确定']
                  },function(result){
                      layer.close(result);
                      $state.go("app.office.versionsManagement");
                  });
              } else {
                  inform.common(data.message);
              }

          }
            /**
             * 设置列表的高度
             */
            function setDivHeight(){
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var clientWidth = document.body.clientWidth;
                $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
            }
	 		/**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */	
		
	}]);
})();
(function () {
  app.controller('trainPlanManagementDetails', [
    'OfficeFileTool',
    'comService',
    '$rootScope',
    '$scope',
    'trainPlanService',
    '$state',
    '$stateParams',
    '$modal',
    'inform',
    'Trans',
    'AgreeConstant',
    'LocalCache',
    '$http',
    function (
      OfficeFileTool,
      comService,
      $rootScope,
      $scope,
      trainPlanService,
      $state,
      $stateParams,
      $modal,
      inform,
      Trans,
      AgreeConstant,
      LocalCache,
      $http
    ) {
      /**
       * *************************************************************
       *             初始化部分                                 开始
       * *************************************************************
       */
      //初始化过程flag,变为2时证明初始化结束,可以给修改页面赋值
      var initFlag = 0;
      //修改页面传过来的培训id
      var trainId = $stateParams.item;
      // 正则校验配置
      $scope.limitList = AgreeConstant.limitList;
      //新增对象
      $scope.specDetail = {
        participants: [],
      };
      //培训级别
      $scope.trainLevel = [
        { code: '0', value: '一级部门' },
        { code: '1', value: '二级部门' },
        { code: '2', value: '项目/小组' },
      ];
      $scope.trainDurationList = [
        { code: '0.5', value: '0.5' },
        { code: '1.0', value: '1.0' },
        { code: '1.5', value: '1.5' },
        { code: '2.0', value: '2.0' },
        { code: '2.5', value: '2.5' },
        { code: '3.0', value: '3.0' },
        { code: '3.5', value: '3.5' },
        { code: '4.0', value: '4.0' },
      ];
      $scope.specDetail.trainDuration = $scope.trainDurationList[0].value;
      //培训管理人员权限控制
      personalController();
      //培训文件上传初始化
      $scope.param = {};
      $scope.param.attachmentAddress = [];
      $scope.param.currentAddress = [];
      $scope.param.attachmentSize = [];
      $scope.param.attachmentAddressID = [];
      //创建文件上传组件
      var paramObj = {
        listId: 'thelist',
        removeCall: function (id) {
          var index = $scope.param.attachmentAddressID.indexOf(id);
          $scope.$apply();
          $scope.param.currentAddress.splice(index, 1);
          $scope.param.attachmentAddress.splice(index, 1);
          $scope.param.attachmentSize.splice(index, 1);
          $scope.param.attachmentAddressID.splice(index, 1);
        },
        getFilePathCall: function (fileId) {
          var index = $scope.param.attachmentAddressID.indexOf(fileId);
          var filePath = $scope.param.attachmentAddress[index];
          return filePath;
        },
        getSizeOfFiles: function () {
          var size = 0;
          for (var i = 0; i < $scope.param.attachmentSize.length; i++) {
            size = size + parseInt($scope.param.attachmentSize[i]);
          }
        },
        uploadSuccess: function (file, response) {
          $scope.param.attachmentAddress.push(response.data);
          $scope.param.currentAddress.push(response.data);
          $scope.param.attachmentAddressID.push(file.id);
          $scope.param.attachmentSize.push(file.size);
        },
      };
      var uploader = OfficeFileTool.createUploader(paramObj, 'train');
      //初始化页面信息
      initPages();
      /**
       * *************************************************************
       *              初始化部分                                 结束
       * *************************************************************
       */

      /**
       * *************************************************************
       *              方法声明部分                                 开始
       * *************************************************************
       */

      function initPages() {
        initDepart();
        initEmployee();
      }

      /**
       * 重置讲师姓名
       */
      $scope.resetTrainer = function (item) {
        item.trainer = '';
      };
      /**
       * 初始化部门列表
       */
      function initDepart() {
        //获取一级部门
        $scope.departmentList = [];
        comService.getOrgChildren('0002').then(function (data) {
          if (data.code === AgreeConstant.code) {
            $scope.departmentList = data.data;
            //获取后,
            initFlag++;
          }
          initChangeParam();
        });
      }
      $scope.getTwoDepartment = function (oneDepartment) {
        //获取二级部门
        $scope.twoDepartmentList = [];
        comService.getOrgChildren(oneDepartment).then(function (data) {
          if (data.code === AgreeConstant.code) {
            $scope.twoDepartmentList = data.data;
          }
        });
      };
      /**
       * 初始化员工列表，并且将传过来的详情值赋给changeParamDetail
       */
      function initEmployee() {
        $scope.employeeList = [];
        comService.getEmployeesName().then(function (data) {
          if (data.code === AgreeConstant.code) {
            $scope.employeeList = data.data;
            initFlag++;
            initChangeParam();
          }
        });
      }

      /**
       * 如果trainId存在并且两个初始化结束,则根据修改传入id查询
       */
      function initChangeParam() {
        if (trainId && initFlag === 2) {
          var param = {
            id: trainId,
          };
          trainPlanService.getTrainPlanById(param).then(function (data) {
            if (data.code === AgreeConstant.code) {
              // 培训级别
              $scope.specDetail.trainLevel = data.data.level;
              $scope.changeParamDetail = data.data;
              if (data.data.participantlist) {
                $scope.changeParamDetail.participantlist = data.data.participantlist;
              }
              if ('内训' === $scope.changeParamDetail.trainForm && null != $scope.changeParamDetail.trainer) {
                $scope.changeParamDetail.trainer = $scope.changeParamDetail.trainer.split('、');
              }
              getOneDep();
              //上传培训文件
              $scope.param.attachmentAddressID = [];
              $scope.param.currentAddress = [];
              $scope.param.attachmentAddress = [];
              $scope.param.attachmentSize = [];
              if (null != data.data.trainFilePath && '' !== data.data.trainFilePath) {
                $scope.param.flag = '0';
                angular.forEach(data.data.trainFilePath.split(','), function (res, index) {
                  $scope.param.attachmentAddress.push(res);
                  $scope.param.currentAddress.push(res);
                });
                //创建回显的文件列表 返回文件id集合
                var fileIdList = uploader.initShowFileList($scope.param.attachmentAddress);
                if (fileIdList.length > 0) {
                  $scope.param.attachmentAddressID = fileIdList;
                }
              }
            }
          });
        }
      }
      /**
       * 根据二级部门 获取一级部门
       */
      function getOneDep() {
        //一级部门
        var department = [];
        angular.forEach($scope.departmentList, function (res) {
          department.push(res.orgCode);
        });
        //如果是一级部门 直接获取其下二级部门
        if (department.indexOf($scope.changeParamDetail.affiliatedGroup) > -1) {
          $scope.getTwoDepartment($scope.changeParamDetail.affiliatedGroup);
        }
        //如果不是一级部门   获取所有部门
        var deptList = [];
        comService.getOrgChildren('').then(function (res) {
          if (res.code === AgreeConstant.code) {
            deptList = res.data;
            //查看一级部门里有没有，如果一级部门里没有部门
            if (department.indexOf($scope.changeParamDetail.affiliatedGroup) < 0) {
              //那就说明保存的部门是二级部门
              $scope.changeParamDetail.twoAffiliatedGroup = $scope.changeParamDetail.affiliatedGroup;
              //遍历所有部门 ，找到该二级部门的父级部门
              angular.forEach(deptList, function (dep) {
                //第一次遍历 找到对应的二级部门
                if (dep.orgCode === $scope.changeParamDetail.twoAffiliatedGroup) {
                  angular.forEach(deptList, function (one) {
                    //第二次遍历 根据对应二级部门的父级ID找到对应的一级部门的Code
                    if (dep.parentId === one.orgId) {
                      $scope.changeParamDetail.affiliatedGroup = one.orgCode;
                      $scope.getTwoDepartment($scope.changeParamDetail.affiliatedGroup);
                      return;
                    }
                  });
                }
              });
            }
          }
        });
      }

      /**
       * 获取讲师字符串
       */
      function getTrainer(item) {
        if (item.trainForm === '内训') {
          return null == item.trainer ? null : item.trainer.join('、');
        } else {
          item.trainerId = '';
          return item.trainer;
        }
      }
      /**
       * 添加信息Detail页面
       */
      $scope.addDetail = function () {
        var department = '';
        //如果二级部门为空，存入一级部门，不然存入二级部门
        if ($scope.specDetail.twoAffiliatedGroup === '' || null == $scope.specDetail.twoAffiliatedGroup) {
          department = $scope.specDetail.affiliatedGroup;
        } else {
          department = $scope.specDetail.twoAffiliatedGroup;
        }
        var urlData = {
          trainType: $scope.specDetail.trainType,
          trainForm: $scope.specDetail.trainForm,
          temName: $scope.specDetail.temName,
          affiliatedGroup: department,
          trainer: getTrainer($scope.specDetail),
          trainDuration: $scope.specDetail.trainDuration,
          planCompletionDate: inform.format($scope.specDetail.planCompletionDate, 'yyyy-MM-dd'),
          scope: $scope.specDetail.scope,
          comments: $scope.specDetail.comments,
          level: $scope.specDetail.trainLevel,
          trainFilePath: $scope.param.currentAddress.join(','),
        };
        trainPlanService.insertDetail(urlData).then(
          function (data) {
            callBackFunction(data);
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      };

      /**
       * 修改信息Detail页面
       */
      $scope.updateDetail = function () {
        var department = '';
        //如果二级部门为空，存入一级部门，不然存入二级部门
        if ($scope.changeParamDetail.twoAffiliatedGroup === '' || null == $scope.changeParamDetail.twoAffiliatedGroup) {
          department = $scope.changeParamDetail.affiliatedGroup;
        } else {
          department = $scope.changeParamDetail.twoAffiliatedGroup;
        }
        var urlData = {
          id: $scope.changeParamDetail.id,
          trainType: $scope.changeParamDetail.trainType,
          trainForm: $scope.changeParamDetail.trainForm,
          temName: $scope.changeParamDetail.temName,
          affiliatedGroup: department,
          trainer: getTrainer($scope.changeParamDetail),
          trainerId: $scope.changeParamDetail.trainerId,
          trainDuration: $scope.changeParamDetail.trainDuration,
          planCompletionDate: inform.format($scope.changeParamDetail.planCompletionDate, 'yyyy-MM-dd'),
          scope: $scope.changeParamDetail.scope,
          comments: $scope.changeParamDetail.comments,
          trainFilePath: $scope.param.currentAddress.join(','),
          level: $scope.specDetail.trainLevel,
        };
        trainPlanService.updateDetail(urlData).then(
          function (data) {
            callBackFunction(data);
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      };

      /**
       * 培训管理人员权限控制
       */
      function personalController() {
        //获取当前登录人员的角色
        var staffList = JSON.parse(LocalCache.getSession('roleList'));
        var codeList = [];
        for (var i = 0; i < staffList.length; i++) {
          //将角色编码添加进list
          codeList.push(staffList[i].roleCode);
        }
        //判断角色中是否包括培训管理员
        if (!codeList.includes('PXGLRY')) {
          //不包含的话 培训级别默认是项目/小组
          $scope.specDetail.trainLevel = '项目/小组';
          $scope.isEdit = true;
        }
      }

      function callBackFunction(data) {
        if (data.code === AgreeConstant.code) {
          layer.confirm(
            data.message,
            {
              title: false,
              btn: ['确定'],
            },
            function (result) {
              layer.close(result);
              $state.go('app.office.trainPlanManagement');
            }
          );
        } else {
          inform.common(data.message);
        }
      }

      //更新时间
      $scope.openDateup = function ($event) {
        $event.preventDefault();
        $event.stopPropagation();
        $scope.up = true; //更新时间
      };

      //新增时间
      $scope.openDateadd = function ($event) {
        $event.preventDefault();
        $event.stopPropagation();
        $scope.add = true; //新增时间
      };

      $scope.goback = function () {
        $state.go('app.office.trainPlanManagement');
      };
      /**
       * *************************************************************
       *              方法声明部分                                 结束
       * *************************************************************
       */
    },
  ]);
})();

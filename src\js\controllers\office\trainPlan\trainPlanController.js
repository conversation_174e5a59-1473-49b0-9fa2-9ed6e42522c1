(function () {
    app.controller("trainPlanController", [ '$scope','comService', 'trainPlanService', 'inform', 'Trans', 'AgreeConstant',
        function ($scope, comService,trainPlanService, inform, Trans, AgreeConstant) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */

            $scope.dataList = {};

            $scope.datepicker = {
                currentDate: new Date()
            };
            initDepart();
            $scope.getDataDetail = getDataDetail;
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            //重置
            $scope.reset = function () {
            	initTime();
            };
            /**
    		 * 初始化检索条件开始时间 及 结束时间
    		 */
            function initTime(endDate){
    			if (endDate==null || endDate==="" ){
    				$scope.endTime = inform.format(new Date(),'yyyy-MM-dd');//获取当前系统时间
    			} 
    			var time = $scope.endTime.split("-");
    			var start = time[0]+"/01"+"/01";
    			$scope.startTime = inform.format(start,'yyyy-MM-dd');
    			//对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
    			$scope.department = 'D010053';
    			getDataDetail();
            }
            /**
             * 初始化部门列表
             */
            function initDepart() {
            	 //获取一级部门
                $scope.departmentList = [];
                comService.getOrgChildren('0002').then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.departmentList = data.data;
                        initTime();
                    }
                });
            }
            /**
             * 获取统计信息
             */
            function getDataDetail() {
                var urlData = {
                    'startTime': inform.format($scope.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.endTime, 'yyyy-MM-dd'),
                    'oneDep':$scope.department
                };
                trainPlanService.getInfoTarget(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.dataList = data.data;
                            var targetSum = 0;
                            var numberSum = 0;
                            angular.forEach($scope.dataList, function (data, i) {//遍历信息做汇总
                                targetSum = targetSum + parseInt(data.target, 10);
                                numberSum = numberSum + parseInt(data.number, 10);
                            });
                            var sum = {
                                'affiliatedGroup': '汇总',
                                'target': targetSum,
                                'number': numberSum
                            };
                            $scope.dataList.push(sum);
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            //生成Excel表格
            $scope.toExcel = function () {
                comService.getOrgChildren($scope.department).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        var dep =  [];
                    	//循环一级部门下的二级部门，放入所有code值
                		angular.forEach(data.data, function (one) {
                			dep.push(one.orgCode);
                        });
                		//放入一级部门本身
                		dep.push($scope.department);
                        var urlData = {
                            'startTime': inform.format($scope.startTime, 'yyyy-MM-dd'),
                            'endTime': inform.format($scope.endTime, 'yyyy-MM-dd'),
                            'oneDep':$scope.department,
                            'affiliatedGroups': dep,
                        };
                        inform.modalInstance("确定要下载吗?").result.then(function () {
                            inform.downLoadFile('train/toExcel',urlData,'培训组织报表.xlsx');
                        });
                    }
                });
            	
            };

            //开始时间
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;    //开始时间
                $scope.openedEnd = false;
            };

            //结束时间
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;    //结束时间
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
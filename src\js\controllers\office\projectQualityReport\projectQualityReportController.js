(function () {
    'use strict';
    app.controller("projectQualityReportController", ['$scope','$state','comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','projectQualityService','$stateParams','LocalCache', '$modal','$http',
        function ($scope,$state,comService, $rootScope, inform, Trans, AgreeConstant,projectQualityService,$stateParams,LocalCache, $modal,$http) {
    	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	//区分是报表页面还是管理页面
    	/*var typeBefor = JSON.stringify($stateParams.type);
    	var types = typeBefor.split(":");
    	var typesec = types[1].split("}");*/
    	$scope.type = $stateParams.type[0];
    	// 初始化查询数据
        $scope.formRefer = {
            //项目、产品线、部门
            cname : '', 
            productLine : '',
            department:'',
        };
        //页面分页信息
        $scope.pages = {
            pageNum: '',	//分页页数
            size: '',		//分页每页大小
            total: ''		//数据总数
        };
        //页面从详情信息跳转回来时，保留查询条件
        if ($scope.formRefer.cname==='' ) {
            $scope.formRefer.cname = $stateParams.cname;
        }
        if ($scope.formRefer.productLine==='' ) {
            $scope.formRefer.productLine = $stateParams.productLine;
        }
        if ($scope.formRefer.department==='' ) {
            $scope.formRefer.department = $stateParams.department;
        }
        // 初始化分页数据
        $scope.pages = inform.initPages();
        $scope.pages.size = '50';
        $scope.flagScore = false;
        $scope.flag = false;
        //设置列表的高度
        setDivHeight();
        //窗体大小变化时重新计算高度
        $(window).resize(setDivHeight);
        //初始化报表
        initPages();
        
        $scope.getData = getData;
        getData($scope.pages.pageNum);
       
        
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
    		
		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */	
        /**
         * 根据评分排序
         */
        $scope.order = function(orderby){
            //根据点击次数判别排序规则
            if(!$scope.flag){
            	$scope.orderby = orderby + " desc";
            } else {
            	$scope.orderby = orderby + " asc";
            }
            $scope.flag = !$scope.flag;
            getData("1");
        }
        /**
         * 根据计算得分排序
         */
        $scope.orderScore = function(orderby){
            //根据点击次数判别排序规则
            if($scope.flagScore){
                //时间字符串转时间格式
            	$scope.orderby = orderby + " desc";
            } else {
            	$scope.orderby = orderby + " asc";
            }
            $scope.flagScore = !$scope.flagScore;
            getData("1");
        }
		
        /**
         * 设置列表的高度
         */
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (150 + 185);
            $("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight - 50);
        }

        /**
         * 重置
         */
        $scope.rest = function() {
            $scope.formRefer.cname='';         //项目名
            $scope.formRefer.productLine='';//产品线
            $scope.formRefer.department = ''; 
        }

        /**
         * 初始化
         */
        function initPages() {
        	 //获取产品线
            $scope.projectLine = [];
            comService.queryEffectiveParam('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                if (data.data) {
                    $scope.projectLine = data.data;
                }
            });
            //获取部门信息
          	$scope.departmentSelect = [];
          	comService.getOrgChildren('D010053').then(function(data) {
      			$scope.departmentSelect = comService.getDepartment(data.data);
            });
        }
        /**
         * 进入详情页面
         */
        $scope.goDetail = function(item) {
        	var param = item.projectId + "," + $stateParams.type;
        	$state.go("app.office.projectQualityDetail", {
        		 projectId: param,
        		 cname:$scope.formRefer.cname,
                 productLine:$scope.formRefer.productLine,
                 department:$scope.formRefer.department,
                 projectName:item.projectName
        	});
        }
        /**
         * 获取项目
         */
        function getData(pageNum) {
            //默认根据负责人评分倒序排列
            if(null ==$scope.orderby){
            	$scope.orderby ="quailty.score_actual desc";
                $scope.flagScore = false;
            }
            var urlData = {
                'projectName': $scope.formRefer.cname,
                'productLine': $scope.formRefer.productLine,
                'department': $scope.formRefer.department,
                'currentPage': pageNum,
                'pageSize': $scope.pages.size,
                'orderByParam': $scope.orderby
            };
            projectQualityService.getData(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    //项目报告
                    $scope.paramList1 = data.data.list;
                    // 分页信息设置
                    $scope.pages.total = data.data.total;           // 页面数据总数
                    $scope.pages.star = data.data.startRow;         // 页面起始数
                    $scope.pages.end = data.data.endRow;            // 页面结束数
                    $scope.pages.pageNum = data.data.pageNum;       //页号
                } else {
                    inform.common(data.message);
                }
            },
            function () {
                inform.common(Trans("tip.requestError"));
            });
        }
        
      //生成Excel表格
		$scope.toExcel = function() {
			//页面从详情信息跳转回来时，保留查询条件
			if ($scope.formRefer.cname==='' ) {
                $scope.formRefer.cname = $stateParams.cname;
            }
            if ($scope.formRefer.productLine==='' ) {
                $scope.formRefer.productLine = $stateParams.productLine;
            }
            if ($scope.formRefer.department==='' ) {
                $scope.formRefer.department = $stateParams.department;
            }
            var urlData = {
                'projectName': $scope.formRefer.cname,
                'productLine': $scope.formRefer.productLine,
                'department': $scope.formRefer.department
            };
			var modalInstance = $modal.open({
				templateUrl: 'myModalContent.html',
	            controller: 'ModalInstanceCtrl',
	            size: "sm",
	            resolve: {
	            	items: function() {
	            		return "确定要下载吗！";
	                }
	            }
			});
			modalInstance.result.then(function() {
				//开启遮罩层
				inform.showLayer("下载中。。。。。。");
				$http.post(
					$rootScope.getWaySystemApi+'projectquality/toExcel',
					urlData,
			        {headers: {
						'Content-Type': 'application/json',
						'Authorization':'Bearer ' + LocalCache.getSession("token")||''
						},
					 responseType: 'arraybuffer'//防止中文乱码
					 }
			     ).success(function(data){
			         //如果是IE浏览器
			         if (window.navigator && window.navigator.msSaveOrOpenBlob) {
			             var csvData = new Blob([data], {type:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
			             window.navigator.msSaveOrOpenBlob(csvData,'项目质量报表.xlsx');		   
			         }
			         //google或者火狐浏览器
			         else{
			            var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
			    		var objectUrl = URL.createObjectURL(blob);
			    		var aForExcel = $("<a download='项目质量报表.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href",objectUrl);
			    		$("body").append(aForExcel);
			    		$(".forExcel").click();
			    		aForExcel.remove();
			         }
			         // 关闭遮罩层
			 		 inform.closeLayer();
			 		 inform.common("下载成功!");
			      });
			  });
	     };
	     /**
          * *************************************************************
          *              方法声明部分                                 结束
          * *************************************************************
          */
         } 
    ]);
})();
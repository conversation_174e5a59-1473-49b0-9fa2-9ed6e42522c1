
(function () {
    app.controller("projectHoursDetailsController", ['comService','$rootScope', '$scope','$state','$stateParams','$modal','projectHoursService','inform','Trans','AgreeConstant','LocalCache',
        function (comService,$rootScope, $scope,$state,$stateParams, $modal,projectHoursService,inform,Trans,AgreeConstant,LocalCache) {
    	
       	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */ 
    	$scope.isSprintVersion=$stateParams.isSprintVersion;
    	$scope.flag=$stateParams.flag;
		//保存查询出的项目信息
    	$scope.itemList = [];
		//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		init();
	  	/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
	  	
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */	
		/**
		 * 页面初始化
		 */
		function init(){
			//标题
			$scope.title=[];
    		comService.queryEffectiveParam('TASK','TYPELIST').then(function(data) {
        		if (data.data) {
   				  angular.forEach(data.data,function(one,n){
   					  $scope.title.push(one.paramValue);
 				  });
 				 $scope.title.push('其他');
   				 $scope.title.push('未在任务下消耗工时');
        		//点击查看详情获取项目名称
            	viewDetails();
        			
        		}
            });
		}
		/**
	     * 设置列表的高度
	     */
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 180);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 50);
 		}

    	 /**
     	  * / 根据项目名称获取项目信息
     	  */
     	function viewDetails() {
     		var formRefer = LocalCache.getObject('projectHours_formRefer');
     		$scope.begin = inform.format(formRefer.startTime, 'yyyy-MM-dd');
     		$scope.end = inform.format(formRefer.endTime, 'yyyy-MM-dd');
     		var urlData = {};
     		//项目查询
     		if ($stateParams.flag==null){
     			urlData = {
     				'projectId':$stateParams.id,			//项目id
     	    		'startTime':$scope.begin,				//开始时间
     	    		'endTime':$scope.end					//结束时间
     			};
     		} else {
     		//禅道项目查询
     			urlData = {
         				'proProjectId':$stateParams.id,			//版本id
         	    		'startTime':$scope.begin,				//开始时间
         	    		'endTime':$scope.end					//结束时间
         			};
     		}
     		projectHoursService.viewDetails(urlData).then(function(data) {
     			  if (data.code===AgreeConstant.code) {
     				  var jsonData = data.data;
     				  //显示内容
     				  $scope.itemList = jsonData.dataList;
     				  //项目名（看是不是禅道项目，如果不是，取平台项目名）
     				  $scope.name=$scope.itemList[0].proProject==null?$scope.itemList[0].project:$scope.itemList[0].proProject;
     				  
     				  var tmpArray = [];//显示的数据
     	        	  var tmpTotalArray1 = [];//纵列合计
     	        	  var tmpTotalArray2 = [];//横列合计
     	        	  
     				  //初始化纵列合计
     				  angular.forEach($scope.title,function(key,n){
     					  tmpTotalArray2.push(0);
     				  });
     				  
     				  //将标题与内容对应
     				  angular.forEach(jsonData.dataList,function(time,i) {
     					  var tmpTotal1 = 0;
     					  //每个人一条记录
     					  var arrayTmp =[];
     					  arrayTmp.push(time.person);
     					  angular.forEach($scope.title,function(key,n){
     						  var vals = 0;
     						  var s = 0;
     						  if (time[key]) {
     							  vals = time[key];
     							  s = vals.toFixed(1);
     						  }
     						  //四舍五入 保留整数
     						  tmpTotal1 = tmpTotal1 + parseFloat(vals.toFixed(1));
     						  tmpTotalArray2[n] = tmpTotalArray2[n]+ parseFloat(vals.toFixed(1));
     						  arrayTmp.push(s);
     					  });
     					  tmpTotalArray1.push(tmpTotal1.toFixed(1));
     					  tmpArray.push(arrayTmp);
     				  });
     				  
     				  
     				  //统计总和
     				  var tmpTotal3 = 0;
     				  var a = 0;
     				  angular.forEach(tmpTotalArray2,function(val,n){
     					  tmpTotal3 = tmpTotal3 + val;
     					  a = tmpTotal3.toFixed(1);
     				  });
     				  $scope.score = tmpArray;
     				  angular.forEach(tmpTotalArray2,function(val,n){
     					  tmpTotalArray2[n]=tmpTotalArray2[n].toFixed(1);	
     				  });
     				  $scope.rowsTotal = tmpTotalArray1;//横行合计
     				  $scope.colsTotal = tmpTotalArray2;//纵列合计
     				  $scope.rowscolsTotal = a;//总和
                } else {
               	 inform.common(data.message);
                }
     		});
        }
     	 /**
         * 下载详情信息
         */
        $scope.toExcel = function () {
        	var formRefer = LocalCache.getObject('projectHours_formRefer');
     		$scope.begin = inform.format(formRefer.startTime, 'yyyy-MM-dd');
     		$scope.end = inform.format(formRefer.endTime, 'yyyy-MM-dd');
     		var urlData = {};
     		//项目查询
     		if ($stateParams.flag==null){
     			urlData = {
     				'projectId':$stateParams.id,			//项目id
     	    		'startTime':$scope.begin,				//开始时间
     	    		'endTime':$scope.end					//结束时间
     			};
     		} else {
     		//禅道项目查询
     			urlData = {
         				'proProjectId':$stateParams.id,			//版本id
         	    		'startTime':$scope.begin,				//开始时间
         	    		'endTime':$scope.end					//结束时间
         			};
     		}
        	inform.modalInstance("确定要下载吗?").result.then(function () {
            	inform.downLoadFile('projectHours/toDetailExcel',urlData,'项目工时统计详情.xlsx');
        	});
        }
    	/**
  		 * *************************************************************
  		 *              方法声明部分                                 结束
  		 * *************************************************************
  		 */
     	
	}]);
})();
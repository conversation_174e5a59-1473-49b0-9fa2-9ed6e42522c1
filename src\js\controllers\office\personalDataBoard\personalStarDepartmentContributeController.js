(function () {
    app.controller("personalStarDepartmentContributeController", ['$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'personalStarDepartmentContributeService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function ($rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, personalStarDepartmentContributeService, inform, Trans, AgreeConstant, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //页面数据
            $scope.formRefer = {};
            initData();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            getData();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            //初始化
            function initData(){
                $scope.formRefer = LocalCache.getObject("personal_star_formRefer");
            }
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
                var clientWidth = document.body.clientWidth;
                var buttonStyle = inform.getButtonStyle(clientHeight,clientWidth);
                buttonStyle.width = "110px";
                $("#buttonStyle").css(buttonStyle);
            }

            //查询我的星数明细--部门贡献详情页信息
            function getData(){
                //查询条件
                $scope.urlData={
                    'employeeNo':$scope.formRefer.empId,
                    'year':$scope.formRefer.year,
                    'month':$scope.formRefer.month,
                    'startDate':$scope.formRefer.year + "-01-01",
                    'endDate': $scope.formRefer.year + "-12-31"
                }
                //查询评审贡献星星明细
                selectDepartmentContributeScore();
            }

            //查询部门贡献积分情况
            function selectDepartmentContributeScore(){
                $scope.departmentContributeScore=[];
                personalStarDepartmentContributeService.selectDepartmentContributeScore($scope.urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        $scope.departmentContributeScore = data.data;
                    } else {
                        inform.common(data.message);
                    }
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            //返回首页--我的星星
            $scope.getBack = function(){
                LocalCache.setObject("personal_star_formRefer",$scope.formRefer);
                $state.go('app.index_bench',{
                    empId: $scope.formRefer.empId,
                    years:$scope.formRefer.year
                });
            }

            //获取积分明细
            $scope.getDetail = function(type){
                $scope.formRefer.flag = 1;
                switch(type){
                    case '技术类培训':
                        $scope.formRefer.trainType = "技术类培训";
                        LocalCache.setObject('trainPlanManagement_formRefer', $scope.formRefer);
                        $state.go('app.office.trainPlanManagement');
                        break;
                    case '一点改善':
                        LocalCache.setObject('correct_formRefer', $scope.formRefer);
                        $state.go('app.office.correct');
                        break;
                    case '知识库':
                        LocalCache.setObject('knowledgeMessage_formRefer', $scope.formRefer);
                        $state.go('app.office.knowledgeMessage');
                        break;
                    case '规范制定及修订':
                        $scope.formRefer.categories = "03";
                        LocalCache.setObject('systemOptimizationController_formRefer', $scope.formRefer);
                        $state.go('app.office.systemOptimizationController');
                        break;
                    case '模板制定及修订':
                        $scope.formRefer.categories = "04";
                        LocalCache.setObject('systemOptimizationController_formRefer', $scope.formRefer);
                        $state.go('app.office.systemOptimizationController');
                        break;
                    case '软件著作权':
                        LocalCache.setObject('softRightController_formRefer', $scope.formRefer);
                        $state.go('app.office.softRightController');
                        break;
                    case '专利':
                        LocalCache.setObject('patentApplyController_formRefer', $scope.formRefer);
                        $state.go('app.office.patentApplyController');
                        break;
                    case '申报材料编写':
                        $scope.formRefer.type = "申报材料编写";
                        LocalCache.setObject('departmentContribution_formRefer', $scope.formRefer);
                        $state.go('app.office.departmentContribution');
                        break;
                    case '管理实践分享':
                        $scope.formRefer.type = "管理实践分享";
                        LocalCache.setObject('departmentContribution_formRefer', $scope.formRefer);
                        $state.go('app.office.departmentContribution');
                        break;
                    case '沙龙分享':
                        $scope.formRefer.type = "沙龙分享";
                        LocalCache.setObject('departmentContribution_formRefer', $scope.formRefer);
                        $state.go('app.office.departmentContribution');
                        break;
                    case '开源模块':
                        $scope.formRefer.type = "开源模块";
                        LocalCache.setObject('departmentContribution_formRefer', $scope.formRefer);
                        $state.go('app.office.departmentContribution');
                        break;
                }
            }

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

/*
* @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
* @Date:   2019-06-08 10:05:00
* @Last Modified by:   z<PERSON>zhen<PERSON>o
* @Last Modified time: 2019-06-24 13:45:13
*/
(function() {
    'use strict';
  app.factory('projectTree', projectTree);
  projectTree.$inject=["comService","AgreeConstant","inform","Trans"];

  function projectTree(comService,AgreeConstant,inform,Trans){
    var service={
		 initTree:initTree
    };

    return service;

/**
 * **************************************
 * 支持关键字查询 并执行一次性选中多个项目  开始
 * **************************************
 */
    //计算器 0表示zTree加载完毕
    var counter = 0;

    var timeoutObj = null;
    var checkedNodes = [];
    /**
     * 用于捕获 checkbox 被勾选 或 取消勾选的事件回调函数
     */
    function onCheckNode(e, treeId, treeNode) {
    	
    	var _keywords = $("#SearchKey").val();
    	if(_keywords && _keywords != ''){
	    	checkedNodes = [];
			var treeObj = $.fn.zTree.getZTreeObj("projectRightTreeObj");
			//获取选中的节点集合
			var nodes = treeObj.getCheckedNodes(true);
			angular.forEach(nodes, function(obj, i) {
				checkedNodes.push(obj);
	        });
    	}
    }
    
    /**
     * 初始化Tree前对treeNOdes对象数据做处理的函数
     */
    function beforeInitTree(treeNOdes) {
    	
        angular.forEach(treeNOdes, function(res, index) {
        	for (var node in checkedNodes) {
        		if (node.id===res.id) {
        			res.checked = true;
        			break;
        		}
        	}

        });   
    }    
    /**
     * 初始化项目结构树 支持关键字查询
     * 
     */
    function initTree(nodeList,flag) {
 
    	$("#SearchKey").val("");
    	if(nodeList) {
    		checkedNodes = nodeList;
    	}
    	var treeData = [];
    	var setting = { // 带有复选框树配置
			   data: { simpleData: { enable: true } },
			   callback: { 
				   onCheck: "onCheck",
				   beforeInitTree: "beforeInitTree"
			   },
			   check: {
			     enable: true,
			     autoCheckTrigger: false,
			     chkStyle: "checkbox"
			   }
		 };
    	//覆盖onCheck
    	setting.callback.onCheck=onCheckNode;
    	//覆盖beforeInitTree
    	setting.callback.beforeInitTree=beforeInitTree;
    	//获取所有产品线名称
    	if (flag==null){
    		comService.getLines().then(function(data) {
    			if (data.code===AgreeConstant.code) {
    				angular.forEach(data.data, function(res, index) {
    					var jsonTree = {
    							id: res.id,//产品线ID
    							pId: null,//
    							name: res.name,//产品线名称
    							nocheck:false,//节点前显示checkBox
    							isParent:true,//设置为父节点
    							open: false
    					};
    					treeData.push(jsonTree);
    				});
    				$.fn.zTree.init($("#projectRightTreeObj"), setting, treeData);
    				var treeObj = $.fn.zTree.getZTreeObj("projectRightTreeObj");
    				//循环加载各个节点的子节点
    				angular.forEach(treeObj.getNodes(), function(node, index) {
    					loadChildrenNodes(node);
    				});
    			} else {
    				inform.common(data.message);
    			}
    		}, function() {
    			inform.common(Trans("tip.requestError"));
    		});
    	} else {
    		comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
                if (data.code===AgreeConstant.code) {
                    angular.forEach(data.data, function(res, index) {
                        var jsonTree = {
                            id: res.param_code,//产品线ID
                            pId: null,//
                            name: res.param_value,//产品线名称
                            nocheck:true,//节点前不显示checkBox
                            isParent:true,//设置为父节点
                            open: false
                        };
                        treeData.push(jsonTree);
                    });
                    $.fn.zTree.init($("#projectRightTreeObj"), setting, treeData);
                    var treeObj = $.fn.zTree.getZTreeObj("projectRightTreeObj");
                    //循环加载各个节点的子节点
                    angular.forEach(treeObj.getNodes(), function(node, index) {
                    	loadChildrenNodesOffice(node);
                    });
                    
                } else {
                    inform.common(data.message);
                }
            }, function() {
                inform.common(Trans("tip.requestError"));
            });
    	}
		timeoutObj = setTimeout(function() {
			funLazy(); //lazy load funLazy function 
		}, 1000);
    }
    
    /**
     * 待ZTree加载完毕后再执行fuzzySearch
     */
	function funLazy() {
		
		if (timeoutObj) { 
			//clear pending task
			clearTimeout(timeoutObj);
		}
		if (counter > 0) {
			
			timeoutObj = setTimeout(function() {
				funLazy(); //lazy load ztreeFilter function 
			}, 500);
			return;
		}

		fuzzySearch('projectRightTreeObj','#SearchKey'); //lazy load ztreeFilter function 
	}   
    /**
     * 通过递归方式一次性加载所有子节点数据
     * 显示产品线下所有的项目名称
     * 
     */
    function loadChildrenNodes(treeNode) {

    	counter++;
    	//获取该产品线下所有的项目名称信息
    	comService.getProjectsByLine(treeNode.id)
            .then(function(data) {
                if (data.code===AgreeConstant.code) {
                    var nodeS = [];
                    //查询无结果直接退出
                    if (!data.data || data.data.length <= 0) {
                    	return;
                    }
                    angular.forEach(data.data, function(res, index) {
                    	
                    	
                        var jsonTree = {
                            id: res.id,//项目ID
                            pId: treeNode.id,//产品线ID
                            name: res.name,//项目名称
                            nocheck:false//节点前显示checkBox
                        };
                    	
                    	for (var node in checkedNodes) {
                    		if (node.id===jsonTree.id) {
                    			jsonTree.checked = true;
                    			break;
                    		}
                    	}
                        nodeS.push(jsonTree);
                    });                       

                    var treeObj = $.fn.zTree.getZTreeObj("projectRightTreeObj");
                    treeObj.addNodes(treeNode,nodeS,true);

                } else {
                    inform.common(data.message);
                }
                counter--;
            }, function(error) {
            	counter--;
                inform.common(Trans("tip.requestError"));
            });

    	}
    
  
  /**
   * 通过递归方式一次性加载所有子节点数据
   * 显示office项目中产品线下所有的项目名称
   * 
   */
  function loadChildrenNodesOffice(treeNode) {

  	counter++;
  	//获取该产品线下所有的项目名称信息
  	comService.getProjectsByLineOffice(treeNode.id)
          .then(function(data) {
              if (data.code===AgreeConstant.code) {
                  var nodeS = [];
                  //查询无结果直接退出
                  if (!data.data || data.data.length <= 0) {
                  	return;
                  }
                  angular.forEach(data.data, function(res, index) {
                  	
                  	
                      var jsonTree = {
                          pId: treeNode.id,//产品线ID
                          name: res.cname,//项目名称
                          id: res.id,//项目ID
                          nocheck:false//节点前显示checkBox
                      };
                  	
                  	for (var node in checkedNodes) {
                  		if (node.name===jsonTree.name) {
                  			jsonTree.checked = true;
                  			break;
                  		}
                  	}
                      nodeS.push(jsonTree);
                  });                       

                  var treeObj = $.fn.zTree.getZTreeObj("projectRightTreeObj");
                  treeObj.addNodes(treeNode,nodeS,true);

              } else {
                  inform.common(data.message);
              }
              counter--;
          }, function(error) {
          	counter--;
              inform.common(Trans("tip.requestError"));
          });

  	}
  }
/**
 * ***************************************
 * 功能扩展 支持关键字查询 并执行一次性选中多个员工   结束
 * ***************************************
 */  
})();
(function() {
    'use strict';
  app.factory('staffDimissionService', staffDimissionService);
  staffDimissionService.$inject=["HttpService",'$rootScope'];

  function staffDimissionService(HttpService,$rootScope){
    
    var service={
        selectDimissionByParam:selectDimissionByParam,
        addDimissionByParam:addDimissionByParam,
        updateDimissionByParam:updateDimissionByParam,
        deleteDimissionByIds:deleteDimissionByIds,
        getStaffAllName : getStaffAllName,
        getStaffId:getStaffId
    };
    return service;
  
    /************************************************************************************************/
    /**
     * 员工绩效信息
     */
    function selectDimissionByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffDimission/seleteByParam', urlData);
    }

    function addDimissionByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffDimission/addStaffDimission', urlData);
    }
  

    function updateDimissionByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffDimission/updateStaffDimission', urlData);
    }

    function deleteDimissionByIds(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffDimission/delStaffDimission', urlData);
    }

    function getStaffAllName(){
        return HttpService.post($rootScope.getWaySystemApi + 'staffInfo/getAllStaffName');
    }
    function getStaffId(employeeName){
        return HttpService.post($rootScope.getWaySystemApi + 'staffInfo/selectStaffId',employeeName);
    }

  }
})();
(function() {
    app.controller("dataPermissionGroup", ['dataPermissionGroupService', 'comService', '$rootScope', '$scope', '$state', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function(dataPermissionGroupService, comService, $rootScope, $scope, $state, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.specDetail = {
                participants: []
            };
            $scope.jsonData = {};
            $scope.spec = {};
            $scope.getData = getData;
            //页面分页信息
            $scope.pages = {
                pageNum: 1, //分页页数
                size: 50, //分页每页大小
                total: 0 //数据总数
            };
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //初始化页面信息
            getData(1);
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight);
                $("#divTBDisDetail").height(divHeight);
                $("#subDivTBDisDetail").height(divHeight - 90);
            }
            //重置
            $scope.rest = function() {
                $scope.name = '';
            };
            // 数据访问权限组信息分页查询,获取所有数据以分页的形式
            function getData(indexNum) {
                var urlData = {
                    'name': $scope.name ? $scope.name : null,
                    'page': indexNum,
                    'size': $scope.pages.size
                };
                dataPermissionGroupService.getInfo(urlData).then(function(data) {
                     if (data.code === '0002'){
                        $state.go('app.office.unAuthority');
                    }else if (data.code === '0000') {
                        $scope.jsonData = data.data.list;
                        // 分页信息设置
                        $scope.pages.total = data.data.total; // 页面数据总数
                        $scope.pages.star = data.data.startRow; // 页面起始数
                        $scope.pages.end = data.data.endRow; // 页面结束数
                        $scope.pages.pageNum = data.data.pageNum; //页号
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            // 新增：修改详情信息弹框，str存在，就是新增
            $scope.popModalDetail = function(item, str) {
                if (!str) {
                    $scope.changeParam = angular.copy(item);
                    $state.go("app.office.dataPermissionGroupUp", {
                        item: JSON.stringify($scope.changeParam)
                    });
                } else {
                    //新增时，将其声明为数组
                    $scope.specDetail = {
                        participants: []
                    };
                }
            };
            // 删除数据 JSON.stringify(removeParam)
            $scope.deleteById = function(item) {
                var urlData = {
                    'id': item.id
                };
                $("#edit_modal").modal('hide');
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function() {
                            return "确定要刪除吗！";
                        }
                    }
                });
                modalInstance.result.then(function() {
                    dataPermissionGroupService.deleteById(urlData).then(function(data) {
                        inform.common(data.message);
                        $scope.getData(1);
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
                });
            };
           
            // 验证用户查询权限verifyPermission
            $scope.verifyPermission = function(item) {
                var urlData = {
                    'id': item.id
                };
                dataPermissionGroupService.verifyPermission(urlData).then(function(data) {
                    if (data.code === "0000") {
                        inform.common("查询成功");
                        $scope.getData(AgreeConstant.pageNum);
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }
    ]);
})();
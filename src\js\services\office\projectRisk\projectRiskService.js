//# sourceURL=js/services/office/projectRisk/projectRiskService.js
(function() {
    'use strict';
    app.factory('projectRiskService', projectRiskService);
    projectRiskService.$inject = ["HttpService", '$rootScope'];

    function projectRiskService(HttpService, $rootScope) {
        var service = {
            getProjectRiskInfo: getProjectRiskInfo,
            getUpdateInfo: getUpdateInfo,
            deleteProjectRiskInfo: deleteProjectRiskInfo,
            addProjectRiskInfo: addProjectRiskInfo,
            updateProjectRiskInfo: updateProjectRiskInfo
        };
        return service;
        /**
         * 分页查询风险信息
         */
        function getProjectRiskInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectRisk/getProjectRiskInfo', urlData);
        }
        /**
         * 根据id重新查询低级质量问题
         */
        function getUpdateInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectRisk/getUpdateInfo', urlData);
        }
        /**
         * 删除低级质量问题
         */
        function deleteProjectRiskInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectRisk/deleteProjectRiskInfo', urlData);
        }
        /**
         * 新增低级质量问题
         */
        function addProjectRiskInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectRisk/addProjectRiskInfo', urlData);
        }
        /**
         * 修改低级质量问题
         */
        function updateProjectRiskInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectRisk/updateProjectRiskInfo', urlData);
        }
    }
})();
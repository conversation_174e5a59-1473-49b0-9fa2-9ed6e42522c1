/*
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date:   2019-01-10 15:20:05
 * @Last Modified by:   l<PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
    'use strict';
  app.factory('projectHoursService', projectHoursService);
  projectHoursService.$inject=["HttpService",'$rootScope'];

  function projectHoursService(HttpService,$rootScope){
    var service={
    	getData: getData,
    	viewDetails: viewDetails
    };
    return service;
    /**
     * 汇总查询
     */
    function getData(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'projectHours/getData', urlData);
    }
    /**
     * 查看详情
     */
    function viewDetails(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'projectHours/viewDetails', urlData);
    }
  }
})();

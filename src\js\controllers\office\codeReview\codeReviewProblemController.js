/*
 * @Author: fubaole
 * @Date:   2017-09-18 14:53:05
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-01-15 17:20:32
 */
(function() {
	app.controller("codeReviewProblemController", ['comService', '$rootScope', '$scope', '$stateParams','codeReviewService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
		function(comService, $rootScope, $scope,$stateParams, codeReviewService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {
		
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		$scope.limitList = AgreeConstant.limitList; // 正则校验配置
        $scope.add = $stateParams.add;
        $scope.codeReviewId = $stateParams.id;
        //初始化页面信息
        initPages();
        //设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
        //获取数据
        $scope.getData=getData;
		getData();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */
		
        /**
         * 初始化页面信息
         */
        function initPages(){
        	//问题类别
        	$scope.typeList = [];
        	$scope.typeMap = [];
    		comService.queryEffectiveParam('CODEPROBLEMTYPE','CODEPROBLEMTYPE').then(function(data) {
        		if (data.data) {
        			$scope.typeList =  data.data;
        			angular.forEach($scope.typeList, function(res, index) {
                    	$scope.typeMap[res.paramCode] = res.paramValue;
                 	});
        		}
            });
        	//问题级别
    		$scope.gradeList = [];
    		$scope.gradeMap = [];
    		comService.queryEffectiveParam('SEVERITY','SEVERITY').then(function(data) {
        		if (data.data) {
        			$scope.gradeList =  data.data;
        			angular.forEach($scope.gradeList, function(res, index) {
                    	$scope.gradeMap[res.paramCode] = res.paramValue;
                 	});
        		}
            });
        }
		/**
		 * 获取项目
		 */
		 function getData() {
			var urlData ={
			    'codeReviewId': $scope.codeReviewId//记录ID
			};
			codeReviewService.getProblem(urlData).then(function(data) {
				if (data.code === AgreeConstant.code) {
                    $scope.problemtData = data.data;
				} else {
					inform.common(data.message);
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
		}
		/**
    	 * 设置列表的高度
    	 */
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 200);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 50);
 		}

		/**
		 * 修改信息
		 */
       $scope.editInfo = function() {
    		 var urlData = [{
                'codeReviewId':$scope.codeReviewId,
    			'problemContent': $scope.changeParam.problemContent,
                'introducer': $scope.changeParam.introducer,
                'problemType':$scope.changeParam.problemType,
                'problemGrade': $scope.changeParam.problemGrade,
                'countermeasure': $scope.changeParam.countermeasure,
                'chargePerson': $scope.changeParam.chargePerson,
                'timeRequire': inform.format($scope.changeParam.timeRequire, 'yyyy-MM-dd')
             }];
    		 codeReviewService.addProblem(urlData).then(function(data){
    			 if(data.code === AgreeConstant.code) {
    				 inform.common(data.message);
    				 $scope.getData();
                     $("#edit_modal").modal("hide");
    			 } else{
    				 inform.common(data.message);
    			 }
    		 }, function(error) {
    			 inform.common(Trans("tip.requestError"));
    		 });
    	 };
    	 
    	 /**
    	  * 添加信息
    	  */
    	$scope.addInfo = function() {
    		var urlData = [{
                    'codeReviewId':$scope.codeReviewId,
        			'problemContent': $scope.spec.problemContent,
                    'introducer': $scope.spec.introducer,
                    'problemType':$scope.spec.problemType,
                    'problemGrade': $scope.spec.problemGrade,
                    'countermeasure': $scope.spec.countermeasure,
                    'chargePerson': $scope.spec.chargePerson,
                    'timeRequire': inform.format($scope.spec.timeRequire, 'yyyy-MM-dd')
                 }];
        		 codeReviewService.addProblem(urlData).then(function(data){
        			 if(data.code === AgreeConstant.code) {
        				 inform.common(data.message);
        				 $scope.getData();
                         $("#add_modal").modal("hide");
        			 } else{
        				 inform.common(data.message);
        			 }
        		 }, function(error) {
        			 inform.common(Trans("tip.requestError"));
        		 });
    	 };

	   /**
	    * 修改信息弹框，str存在，就是新增
	    */ 
        $scope.popModal = function (item,str){
        	if(str){
        		$scope.spec = {
        				'problemContent':'',
        				'introducer':'',
        				'problemType':'',
        				'problemGrade':'',
        				'countermeasure':'',
        				'chargePerson':'',
        				'timeRequire':''
        		};
        	}else{
        		$scope.changeParam = angular.copy(item);
        	}
       };

      /**
       * 删除数据
       */ 
      $scope.removeCustomer = function (item) {
        var urlData = {
                'codeReviewId':item.codeReviewId,
                'problemContent':item.problemContent
          };
        codeReviewService.deleteProblem(urlData)
              .then(function(data) {     
                if (data.code === "0000") {
                  inform.common(Trans("tip.delSuccess"));
                  $scope.getData();
                  }else{
                     inform.common(data.message);
                  }
              }, function(error) {
                inform.common(Trans("tip.requestError"));
              });
          };

          /**
           * 删除弹框
           */ 
          $scope.open = function (item) {
              var modalInstance = $modal.open({
                templateUrl: 'myModalContent.html',
                controller: 'ModalInstanceCtrl',
                size: "sm",
                resolve: {
                  items: function() {
                    return Trans("common.deleteTip");
                  }
                }
              });
              modalInstance.result.then(function() {
                if (item !== null && item !== "") {
                  $scope.removeCustomer(item);
                }
              });
          };
          /**
           * 新增
           */
          $scope.addTime = function ($event) {
              $event.preventDefault();
              $event.stopPropagation();
              $scope.timeAdd = true;
              $scope.timeUp = false;
          };
          /**
           * 修改
           */
          $scope.upTime = function ($event) {
              $event.preventDefault();
              $event.stopPropagation();
              $scope.openedStart = false;
              $scope.timeUp = true;
          };

          /**
           * 跳转至问题列表页
           * @param m 记录相关信息
           */
          $scope.viewProblem = function (m) {
        	  $state.go("app.office.codeReviewManagement");
          };
        /**
	     * *************************************************************
	     *              方法声明部分                                结束
	     * *************************************************************
	     */	
	
		} ]);
})();
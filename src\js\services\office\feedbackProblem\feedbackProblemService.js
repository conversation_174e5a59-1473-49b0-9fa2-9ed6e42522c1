(function () {
    'use strict';
    app.factory('feedbackProblemService', feedbackProblemService);
    feedbackProblemService.$inject = ["HttpService", '$rootScope'];

    function feedbackProblemService(HttpService, $rootScope) {

        var service = {
            getData:getData,
            createTrackingSheet:createTrackingSheet,
            manageDetail:manageDetail,
            getApplications:getApplications,
            getSoftwares:getSoftwares,
            saveOnlineServerEvent:saveOnlineServerEvent,
            getFeedbackProblemById:getFeedbackProblemById 

        };
        return service;
        /**
         * 获取所有的信息
         */
        function getData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'feedbackProblem/getData', urlData);
        }
        /**
         * 维护反馈问题信息
         */
        function manageDetail(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'feedbackProblem/manageDetail', urlData);
        }
        /**
         * 生成钉钉跟踪单流程
         */
        function createTrackingSheet(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'feedbackProblem/createTrackingSheet', urlData);
        }

        /**
         * 获取应用
         */
        function getApplications(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'feedbackProblem/getApplications', urlData);
        }

        /**
         * 获取软件产品
         */
        function getSoftwares(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'feedbackProblem/getSoftwares', urlData);
        }

         /**
         * 生成线上停机事故
         */
         function saveOnlineServerEvent(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'feedbackProblem/saveOnlineServerEvent', urlData);
        }
        
         /**
         * 获取反馈问题详情
         */
         function getFeedbackProblemById(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'feedbackProblem/getFeedbackProblemById', urlData);
        }

        

        
    }
})();

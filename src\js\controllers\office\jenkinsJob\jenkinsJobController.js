/*
 * @Author: liyunmeng
 * @Date:   2020-08-20
 */
(function () {
    app.controller("jenkinsJobController", ['comService', '$rootScope', '$scope', 'jenkinsJobService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, jenkinsJobService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //获取缓存
            $scope.formRefer = LocalCache.getObject('jenkinsJob_formRefer');

            //对原跳转缓存进行覆盖
            LocalCache.setObject("jenkinsJob_formRefer",{});
            $scope.pages = {
                pageNum : '', 		// 分页页数
                size : '', 			// 分页每页大小
                total : '' 			// 数据总数
            };
            // 初始化分页数据
            $scope.pages = inform.initPages();
            // 分页相关函数
            $scope.getData = getData;
            // 重置查询数据
            $scope.reset = reset;
            //在刷新页面时调用该方法
            getData($scope.pages.pageNum);
            //初始化下拉框信息
            getInitData();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //开发语言
            $scope.programLanguageList = [{
                value: 'java',
                label: 'java'
            },{
                value: 'C#',
                label: 'C#'
            },{
                value: 'C++',
                label: 'C++'
            },{
                value: 'Android',
                label: 'Android'
            },{
                value: 'javaScript',
                label: 'javaScript'
            },{
                value: '无关联',
                label: '无关联'
            }]
            //是否下拉框数据源
            $scope.points = [{
                value: '已关联',
                label: '已关联'
            }, {
                value: '未关联',
                label: '未关联'
            }];
            //初始化分页数据
            $scope.projectSelected = [];
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /*
            * 获取下拉框信息
            * */
            function getInitData() {
                //获取产品线
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.projectLine = data.data;
                    }
                });
                //获取系统集成研发的下级部门信息
                $scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentList = comService.getDepartment(data.data);
                });
            }
            /**
             * 获取jenkins作业信息
             */
            function getData(pageNum){
                $scope.jenkinsJobList = [];
                var urlData = {
                    'cname': $scope.formRefer.projectName,
                    'orgCode': $scope.formRefer.orgCode,
                    'productLine': $scope.formRefer.productLine,
                    'jobName': $scope.formRefer.jobName,
                    'programLanguage': $scope.formRefer.programLanguage,
                    'correlationStatus': $scope.formRefer.correlationStatus,
                    'currentPage' : pageNum, 								// 分页页数
                    'pageSize' : $scope.pages.size    						// 分页每页大小
                };
                jenkinsJobService.selectData(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.jenkinsJobList = angular.fromJson(data.data.list);
                        // 分页信息设置
                        $scope.pages.total =  data.data.total; 			// 页面数据总数
                        $scope.pages.star =  data.data.startRow; 		// 页面起始数
                        $scope.pages.end =  data.data.endRow;	 		// 页面结束数
                        $scope.pages.pageNum =  data.data.pageNum;      //页号
                    }
                },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            /**
             * excel下载
             */
            $scope.toExcel = function() {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function() {
                            return "确定要下载吗！";
                        }
                    }
                });
                modalInstance.result.then(function() {
                    //拼装下载内容
                    var urlData = {
                        'cname': $scope.formRefer.projectName,
                        'orgCode': $scope.formRefer.orgCode,
                        'productLine': $scope.formRefer.productLine,
                        'jobName': $scope.formRefer.jobName,
                        'programLanguage': $scope.formRefer.programLanguage,
                        'correlationStatus': $scope.formRefer.correlationStatus,
                    };
                    inform.downLoadFile ('jenkinsJob/downloadExcel',urlData,'Jenkins作业信息表.xlsx');

                });
            }
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (330);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 72);
            }
            /**
             * 跳转修改
             */
            $scope.go = function(item) {
                LocalCache.setObject('jenkinsJob_formRefer',$scope.formRefer);
                $state.go("app.office.jenkinsJobUp",{id: item.id, projectId: item.projectId});
            };
            /*
            * 重置查询数据
            * */
            function reset() {
                $scope.formRefer={};
            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();

(function () {
	app.controller("svnRepositoryManage", ['comService', '$http', '$rootScope', '$state', '$scope', '$modal', 'svnRepositoryManageService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$stateParams',
		function (comService, $http, $rootScope, $state, $scope, $modal, svnRepositoryManageService, inform, Trans, AgreeConstant, LocalCache, $stateParams) {

			$scope.formRefer = LocalCache.getObject('formRefer') || {}; //仓库查询条件

			$scope.type = '1';
			$scope.changeType = function changeType(type, item) {
				$scope.type = type;
				if (type === '1') {
					$scope.formRefer = {};
					LocalCache.setObject('formRefer', {});
					$state.go("app.office.svnRepositoryManage", { type: '1', repositoryName: '' });
				} else if (type === '2') {
					$scope.formRefer = {};
					LocalCache.setObject('formRefer', {});
					$state.go("app.office.svnRepositoryManage", { type: '2', repositoryName: '' });
				} else if (type === '3') {
					$scope.formRefer = {
					   excludedOperator: ['Administrator'],//排除操作人
                       startTime: inform.format(new Date().getTime()-90*24*60*60*1000,"yyyy-MM-dd"),//开始时间
                       endTime: inform.format(new Date(), 'yyyy-MM-dd')//结束时间
					};
					LocalCache.setObject('formRefer', $scope.formRefer);
					$state.go("app.office.svnRepositoryManage", { type: '3', repositoryName: '' });
				}
			}

			$scope.repositoryTypeList = [
				{
					value: 'svn',
					label: 'svn'
				}, {
					value: 'git',
					label: 'git'
				}];

			$scope.repositoryStatusList = [
				{
					value: '进行中',
					label: '进行中'
				}, {
					value: '权限回收',
					label: '权限回收'
				}, {
					value: '权限维护',
					label: '权限维护'
				}];

			$scope.dealStatusList = [
				{
					value: '已处理',
					label: '已处理'
				}, {
					value: '未处理',
					label: '未处理'
				}];
			//初始化按钮权限
			$scope.addRepository = false;//新增仓库
			$scope.upRepository = false;//修改仓库
			$scope.upModule = false;//维护模块
			$scope.leaveEmployees = false;//离职人员
			$scope.callOutEmployees = false;//调出人员
			/**
			 * 结果
			 */
			//被勾选的集合
			$scope.selecteds = [];
			//仓库现有的人员对象
			$scope.employeeForRepository = {
				employeeNos: []
			}
			//调出需要的人员对象
			$scope.employeeForRepositoryCallOut = {
				employeeNos: []
			}
			//全选状态
			$scope.select_all = false;
			// 初始化分页数据
			$scope.pages = inform.initPages();
			//查询页面
			$scope.getData = getData;
			//全选方法
			$scope.selectAll = selectAll;
			//单选方法
			$scope.selectOne = selectOne;
			//离职
			$scope.leave = leave;
			//离职确认按钮
			$scope.leaveEmployee = leaveEmployee;
			//调出
			$scope.callOut = callOut;
			//调出确认按钮
			$scope.callOutEmployee = callOutEmployee;

			setDivHeight();//设置列表的高度

			$(window).resize(setDivHeight);//窗体大小变化时重新计算高度
			$(window).resize(showDataTable);
			//判断按钮是否具有权限
			getButtonPermission();
			//初始化页面信息
			initPages();


			/**
			 * 页面初始化
			 */
			function initPages() {
				$scope.selecteds = [];
				//初始化选中状态为非选中
				angular.forEach($scope.repositoryList, function (r) {
					r.checked = false
				});
				//获取地区
				$scope.areaList = [];
				comService.getParamList('AREA_TYPE', 'AREA_TYPE').then(function (data) {
					if (data.data) {
						$scope.areaList = data.data;
					}
				});
				//获取员工信息
				$scope.employeeList = [];
				comService.getEmployeesByOrgId('').then(function (data) {
					if (data.data) {
						$scope.employeeList = data.data;
					}
				});
				//获取产品线
				$scope.productLineList = [];
				comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
					if (data.data) {
						$scope.productLineList = data.data;
					}
				});
				//获取部门
				$scope.departmentList = [];
				comService.getOrgChildren('D010053').then(function(data) {
					$scope.departmentList = comService.getDepartment(data.data);
				});

				// 获取排除操作人
				$scope.excludedOperatorList = [{
					excludedOperatorCode: '姜帅',
					excludedOperatorName: '姜帅'
				},{
					excludedOperatorCode: '刘玉婷',
					excludedOperatorName: '刘玉婷'
				},{
					excludedOperatorCode: 'Administrator',
					excludedOperatorName: 'Administrator'
				}
				];
			}

			$scope.getProductTypeList = getProductTypeList;
			function getProductTypeList() {
				$scope.productTypeList = [];
				comService.getParamList('PRODUCT_TYPE', $scope.formRefer.productline).then(function (data) {
					if (data.data) {
						$scope.productTypeList = data.data;
					}
				});
			}

			//设置列表的高度
			setDivHeight();
			//窗体大小变化时重新计算高度
			$(window).resize(setDivHeight);
			//设置列表的高度
			function setDivHeight(){
				//网页可见区域高度
				var clientHeight = document.body.clientHeight;
				var divHeight = clientHeight - (150 + 180);
				$("#divTBDis").height(divHeight);
				$("#subDivTBDis").height(divHeight - 85);
			}

			/**
			 * 权限管理
			 */
			function getButtonPermission(){
				var buttons = {
					'Button-SvnRepositoryManage-addRepository':'addRepository',//新增仓库
					'Button-SvnRepositoryManage-upRepository':'upRepository', //修改仓库
					'Button-SvnRepositoryManage-upModule':'upModule', //维护模块
					'Button-SvnRepositoryManage-leave':'leaveEmployees', //离职人员
					'Button-SvnRepositoryManage-callOut':'callOutEmployees' //调出人员
				};
				var urlData = {
					'userId':LocalCache.getSession("userId"),
					'parentPermission':'Button-SvnRepositoryManage',
					'buttons':buttons
				};
				comService.getButtonPermission(urlData,$scope);
			}

			/**
			 * 根据分页显示仓库
			 * pageNum 当前页数
			 */
			function getData(pageNum, repositoryName) {
				//销毁表格
				$scope.showDataTable = 0;

				$scope.selecteds = []; //初始化被勾选的集合

				LocalCache.setObject('formRefer', $scope.formRefer);

				//返回的仓库信息列表
				$scope.repositoryList = [];

				// 查询条件
				var urlData = {};
				if ($scope.type === '1'){
					if (LocalCache.getObject('formRefer')) {
						$scope.formRefer = LocalCache.getObject('formRefer'); // 初始化查询数据
					} else {
						$scope.formRefer = {
							teamLeader: '',//项目经理/Team Leader
							name: '',//人员姓名
							productType: '',//产品类型
							repositoryname: '',//仓库名
							productline: '',//产品线
							repositoryType: '',//仓库类型
							repositoryStatus: '',//仓库状态
							projectname: '',//项目名称
							startTime: '',//开始时间
							department: '',//所属部门
							endTime: '',//结束时间
							remarks: ''//备注
						}; // 初始化查询数据
					}
					//存查询条件
					urlData = {
						'productLine': $scope.formRefer.productline,//产品线
						'repositoryType': $scope.formRefer.repositoryType,//仓库种类
						'repositoryName': $scope.formRefer.repositoryname,//仓库名
						'teamLeader': $scope.formRefer.teamLeader,//项目经理/Team Leader
						'startTime': $scope.formRefer.startTime,//开始时间
						'endTime': $scope.formRefer.endTime,//结束时间
						'team': $scope.formRefer.projectname,//项目名
						'employeeId': $scope.formRefer.name,//人员姓名
						'department': $scope.formRefer.department,//所属部门
						'repositoryStatus': $scope.formRefer.repositoryStatus,//仓库状态
						'remarks': $scope.formRefer.remarks,//备注
						'productType': $scope.formRefer.productType,//产品类型
						'currentPage': pageNum,//当前页数
						'pageSize': $scope.pages.size
					};
				}
				else if ($scope.type === '2'){
					if (LocalCache.getObject('formRefer')) {
						$scope.formRefer = LocalCache.getObject('formRefer'); // 初始化查询数据
					} else {
						$scope.formRefer = {
							dealStatus: '',//处理状态
							quitName: '',//离职人员姓名
							repositoryname: '',//仓库名
							productline: '',//产品线
							repositoryType: '',//仓库类型
							projectname: '',//项目名称
							startTime: '',//开始时间
							department: '',//所属部门
							endTime: '',//结束时间
						}; // 初始化查询数据
					}
					//存查询条件
					urlData = {
						'repositoryName': $scope.formRefer.repositoryname,//仓库名
						'repositoryType': $scope.formRefer.repositoryType,//仓库种类
						'productLine': $scope.formRefer.productline,//产品线
						'team': $scope.formRefer.projectname,//项目名
						'startTime': $scope.formRefer.startTime,//开始时间
						'endTime': $scope.formRefer.endTime,//结束时间
						'department': $scope.formRefer.department,//所属部门
						'dealStatus': $scope.formRefer.dealStatus,//处理状态
						'quitName': $scope.formRefer.quitName,//离职人员姓名
						'currentPage': pageNum,//当前页数
						'pageSize': $scope.pages.size
					};
				}
				else if ($scope.type === '3'){
					if (LocalCache.getObject('formRefer')) {
						$scope.formRefer = LocalCache.getObject('formRefer'); // 初始化查询数据
					} else {
						$scope.formRefer = {
							teamLeader: '',//项目经理/Team Leader
							excludedOperator: '',//排除操作人
							repositoryname: '',//仓库名
							productLine: '',//产品线
							repositoryType: '',//仓库类型
							startTime: '',//开始时间
							endTime: '',//结束时间
						}; // 初始化查询数据
					}
					if (repositoryName){
						$scope.formRefer.repositoryname = repositoryName;
					}
					//存查询条件
					urlData = {
						'teamLeader': $scope.formRefer.teamLeader,//项目经理/Team Leader
						'excludedOperator': $scope.formRefer.excludedOperator,//排除操作人
						'repositoryName': $scope.formRefer.repositoryname,//仓库名
						'repositoryType': $scope.formRefer.repositoryType,//仓库种类
						'productLine': $scope.formRefer.productline,//产品线
						'startTime': $scope.formRefer.startTime,//开始时间
						'endTime': $scope.formRefer.endTime,//结束时间
						'currentPage': pageNum,//当前页数
						'pageSize': $scope.pages.size
					};
				}

				var dateFlag = 1;
				if (!$scope.formRefer.endTime){
					dateFlag = 0;
				} else {
					if ($scope.formRefer.endTime){
						dateFlag = $scope.formRefer.startTime >= $scope.formRefer.endTime ? 1 : 0;
					}
				}

				/**
				 * 获取数据
				 * @param data
				 */
				function getPageData(data){
					//重新生成Table
					$scope.showDataTable = 1;
					if (data.code === AgreeConstant.code) {
						var jsonData = data.data;
						$scope.repositoryList = angular.fromJson(data.data.list);
						if ($scope.repositoryList.length === 0) {
							inform.common(Trans("tip.noData"));
							$scope.pages = inform.initPages();
						} else {
							//分页信息设置
							$scope.pages.total = jsonData.total;
							$scope.pages.star = jsonData.startRow;
							$scope.pages.end = jsonData.endRow;
							$scope.pages.pageNum = jsonData.pageNum;
						}
					} else {
						inform.common(data.message);
					}
					setTimeout(showDataTable, 500);
				}

				// 获取数据
				if (dateFlag === 0) {
					if ($scope.type === '1'){
						svnRepositoryManageService.findRepositoryListByPage(urlData).then(function (data) {
							return getPageData(data);
						}, function (error) {
							inform.common(Trans("tip.requestError"));
						});
					} else if ($scope.type === '2'){
						svnRepositoryManageService.findStaffListByPage(urlData).then(function (data) {
							return getPageData(data);
						}, function (error) {
							inform.common(Trans("tip.requestError"));
						});
					} else if ($scope.type === '3'){
						svnRepositoryManageService.getHisData(urlData).then(function (data) {
							return getPageData(data);
						}, function (error) {
							inform.common(Trans("tip.requestError"));
						});
					}
				} else {
					inform.common(Trans("创建时间应小于截止时间"));
				}
			}

			/**
			 *  离职
			 */
			function leave() {
				$("#leaveDialog").modal('show');
			}

			/**
			 *  调出
			 */
			function callOut() {
				if ($scope.selecteds.length === 0) {
					$("#callOutDialog").modal('hide');
					inform.common('请选择仓库进行操作！');
					return;
				} else {
					$("#callOutDialog").modal('show');
				}

			}

			/**
			 *调用DataTable组件冻结表头和左侧及右侧的列
			 */
			function showDataTable() {
				$('#fixedLeftAndTop').DataTable({
					//可被重新初始化
					retrieve: true,
					//自适应高度
					scrollY: 'calc(100vh - 370px)',
					scrollX: true,
					scrollCollapse: false,
					//控制每页显示
					paging: false,
					//冻结列（默认冻结左1）
					// fixedColumns: {
					// 	leftColumns: 4,
					// 	rightColumns: 1
					// },
					//search框显示
					searching: false,
					//排序箭头
					ordering: false,
					//底部统计数据
					info: false,
				});
			}

			/**
			 * 开始时间
			 */
			$scope.openDateStart = function ($event) {
				$event.preventDefault();
				$event.stopPropagation();
				$scope.openedStart = true;    //开始时间
				$scope.openedEnd = false;
			};

			/**
			 *  结束时间
			 */
			$scope.openDateEnd = function ($event) {
				$event.preventDefault();
				$event.stopPropagation();
				$scope.openedStart = false;
				$scope.openedEnd = true;    //结束时间
			};
			/**
			 * 下载Excel
			 */
			$scope.excelData = function (repositoryType) {
				var urlData = {};
				//拼装查询条件
				if ($scope.type === '1'){
					urlData = {
						'productLine': $scope.formRefer.productline,//产品线
						'repositoryType': $scope.formRefer.repositoryType,//仓库种类
						'repositoryName': $scope.formRefer.repositoryname,//仓库名
						'teamLeader': $scope.formRefer.teamLeader,//项目经理/Team Leader
						'startTime': $scope.formRefer.startTime,//开始时间
						'endTime': $scope.formRefer.endTime,//结束时间
						'team': $scope.formRefer.projectname,//项目名
						'employeeId': $scope.formRefer.name,//人员姓名
						'department': $scope.formRefer.department,//所属部门
						'repositoryStatus': $scope.formRefer.repositoryStatus,//仓库状态
						'remarks': $scope.formRefer.remarks,//备注
						'productType': $scope.formRefer.productType,//产品类型
					};
				} else if ($scope.type === '2'){
					urlData = {
						'repositoryname': $scope.formRefer.repositoryname,//仓库名
						'repositoryType': $scope.formRefer.repositoryType,//仓库种类
						'productline': $scope.formRefer.productline,//产品线
						'projectname': $scope.formRefer.projectname,//项目名
						'startTime': $scope.formRefer.startTime,//开始时间
						'endTime': $scope.formRefer.endTime,//结束时间
						'department': $scope.formRefer.department,//所属部门
						'dealStatus': $scope.formRefer.dealStatus,//处理状态
						'quitName': $scope.formRefer.quitName//离职人员姓名
					};
				} else if ($scope.type === '3'){
					urlData = {
						'teamLeader': $scope.formRefer.teamLeader,//项目经理/Team Leader
						'excludedOperator': $scope.formRefer.excludedOperator,//排除操作人
						'repositoryname': $scope.formRefer.repositoryname,//仓库名
						'repositoryType': $scope.formRefer.repositoryType,//仓库种类
						'productline': $scope.formRefer.productline,//产品线
						'startTime': $scope.formRefer.startTime,//开始时间
						'endTime': $scope.formRefer.endTime//结束时间
					};
				}

				var modalInstance = $modal.open({
					templateUrl: 'myModalContent',
					controller: 'ModalInstanceCtrl',
					size: "sm",
					resolve: {
						items: function () {
							return "是否确定下载？";
						}
					}
				});
				modalInstance.result.then(function () {
					if ($scope.type === '1'){
						if(repositoryType === 'svn'){
							inform.downLoadFile('storageManage/loadSvnExcel', urlData, 'SVN仓库信息表.xlsx');
						}else{
							inform.downLoadFile('storageManage/loadGitExcel', urlData, 'GIT仓库信息表.xlsx');
						}
					} else if ($scope.type === '2'){
						inform.downLoadFile('storageManage/toTransferStaffExcel', urlData, '调离人员信息表.xlsx');
					} else if ($scope.type === '3'){
						inform.downLoadFile('storageManagement/loadHisExcel', urlData, '历史记录汇总信息表.xlsx');
					}
				});
			};

			/**
			 * 全选
			 */
			function selectAll() {
				$scope.select_all = !$scope.select_all;
				if ($scope.select_all) {
					$scope.selecteds = [];
					angular.forEach($scope.repositoryList, function (i) {
						i.checked = true;
						$scope.selecteds.push(i);
					});
					console.log($scope.selecteds.length);
				} else {
					angular.forEach($scope.repositoryList, function (i) {
						i.checked = false;
					});
					$scope.selecteds = [];
				}
			}

			/**
			 * 单选
			 */
			function selectOne(i) {
				i.checked = !i.checked;
				$scope.select_all = false;
				var index = $scope.selecteds.indexOf(i);
				if (index === -1 && i.checked) {
					$scope.selecteds.push(i);
				} else if (index !== -1 && !i.checked) {
					$scope.selecteds.splice(index, 1);
				}
			}

			/**
			 * 保存离职
			 */
			function leaveEmployee() {
				var urlData = {
					'employeesList': $scope.employeeForRepository.employeeNos
				}
				$("#leaveDialog").modal('hide');
				var modalInstance = $modal.open({
					templateUrl: 'myModalContent',
					controller: 'ModalInstanceCtrl',
					size: "sm",
					resolve: {
						items: function () {
							return "是否确定保存修改？";
						}
					}
				});
				modalInstance.result.then(function () {
					svnRepositoryManageService.deleteModuleEmployee(urlData).then(function (data) {
						if (data.code === AgreeConstant.code) {
							inform.common("离职人员成功");
						} else {
							inform.common(data.message);
						}
					}, function () {
						inform.common(Trans("tip.requestError"));
					});
				});

			}

			/**
			 * 人员调出
			 */
			function callOutEmployee() {
				var repositoryIdList = [];
				for (var i = 0; i < $scope.selecteds.length; i++) {
					repositoryIdList.push($scope.selecteds[i].id)
				}
				var urlData = {
					'repositoryIdList': repositoryIdList,
					'employeesList': $scope.employeeForRepositoryCallOut.employeeNos
				}
				$("#callOutDialog").modal('hide');
				var modalInstance = $modal.open({
					templateUrl: 'myModalContent',
					controller: 'ModalInstanceCtrl',
					size: "sm",
					resolve: {
						items: function () {
							return "是否确定保存修改？";
						}
					}
				});
				modalInstance.result.then(function () {
					svnRepositoryManageService.callOutModuleEmployee(urlData).then(function (data) {
						if (data.code === AgreeConstant.code) {
							inform.common("调出人员成功");
						} else {
							inform.common(data.message);
						}
					}, function () {
						inform.common(Trans("tip.requestError"));
					});
				});

			}

			/**
			 * 跳转至仓库修改页面
			 */
			$scope.toRepositoryUp = function (m) {
				if (m === undefined){
					$state.go("app.office.svnRepositoryUpdate");
				}else {
					LocalCache.setObject("repository_updateObject",m);
					$state.go("app.office.svnRepositoryUpdate");
				}
			};

			/**
			 * 跳转至维护模块页面
			 */
			$scope.toModelUp = function (id, repositoryName,repositoryType) {
				$state.go("app.office.svnModelUpdate", { repositoryid: id, repositoryName: repositoryName ,repositoryType:repositoryType});
			};

			// 查看历史详情
			$scope.toHistoryDetail = toHistoryDetail;
			function toHistoryDetail(item) {
				$state.go("app.office.svnRepositoryManage", { type: '3', repositoryName: item.repositoryName });
			}

			//模块详情
			$scope.toModelDetail = toModelDetail;
			function toModelDetail(item) {
				$state.go("app.office.svnModelDetail", { staffId: item.staffId, storageName: item.repositoryName });
			}

			//显示历史详情
			$scope.showHistoryDetail = showHistoryDetail;
			function showHistoryDetail(item) {
				$scope.historyDetail = item;
				$("#historyDetailDialog").modal('show');
			}

			// 调离人员管理-处理状态
			$scope.confirmDeal = confirmDeal;
			function confirmDeal(item) {
				var modalInstance = $modal.open({
					templateUrl: 'myModalContent',
					controller: 'ModalInstanceCtrl',
					size: "sm",
					resolve: {
						items: function () {
							return "是否确认要处理？";
						}
					}
				});
				var urlData = {
					staffStatus: item.staffStatus,
					repositoryName: item.repositoryName,
					moduleId: item.moduleId,
					staffId: item.staffId
				}
				modalInstance.result.then(function () {
					svnRepositoryManageService.dealStaff(urlData).then(function (data) {
						if (data.code === AgreeConstant.code) {
							inform.common("调出人员成功");
							getData($scope.pages.pageNum);
						} else {
							inform.common(data.message);
						}
					}, function () {
						inform.common(Trans("tip.requestError"));
					});
				});
			}

			$scope.$watch('$viewContentLoaded', function () {
				if ($stateParams.type) {
					$scope.type = $stateParams.type;
				} else {
					$scope.type = '1';
				}
				if ($stateParams.repositoryName){
					$scope.formRefer.repositoryname = $stateParams.repositoryName;
				}
				getData($scope.pages.pageNum);
			});
		}]);
})();

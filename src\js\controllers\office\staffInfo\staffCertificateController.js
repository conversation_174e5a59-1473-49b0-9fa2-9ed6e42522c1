(function () {
    'use strict';
    app.controller("staffCertificateController", ['comService', '$rootScope', '$stateParams', '$scope', 'staffCertificateService', '$modal', 'inform', 'Trans', 'AgreeConstant', '$state', 'LocalCache', '$http',
        function (comService, $rootScope, $stateParams, $scope, staffCertificateService, $modal, inform, Trans, AgreeConstant, $state, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            $scope.datepicker = {
                currentDate: new Date()
            };

            //权限标志
            $scope.flag = false;
            //设置员工地区的访问权限
            $scope.areaCodeFlag = true;

            //设置列表的高度
            setDivHeight();
            //新增数据对象
            $scope.addParam = {};
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            // 列表数据
            $scope.getData = getData;
            //分页
            $scope.pages = {
                pageNum: "1",
                size: "100"
            };
            $scope.searchObject = {};
            //获取缓存
            $scope.searchObject = LocalCache.getObject('staffCertificate_searchObject');
            //对原缓存进行覆盖
            LocalCache.setObject("staffCertificate_searchObject", {});
            initPrimaryDeptList();//初始化一级部门列表
            $scope.primaryDeptCode = "";//初始化一级部门
            initPage();
            var paramObj = {
                primaryDeptId:'#primaryDeptName',
                primaryDeptScopeModel:'primaryDeptCode',
                primaryDeptList:'primaryDeptList',
                subDeptList:'deptList',
                subDeptScopeModel:'departmentCode'
            };
            //权限控制
            comService.checkAuthentication($scope,paramObj,departmentCallBack,LocalCache.getSession('loginName'));
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */


            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 65);
            }


            //重置查询条件
            $scope.clearParams = function () {
                //区域权限
                if (!$scope.areaCodeFlag) {
                    $scope.searchObject.area = "";
                }
                $scope.primaryDeptCode = "";
                $scope.departmentCode = "";
                $scope.searchObject.employeeName = "";
                $scope.searchObject.certificate = "";
                $scope.searchObject.getCertificateTime = "";
                $scope.searchObject.certificateType = "";

            };

            //获取证书类别
            function getCertificateTypeList() {
                $scope.certificateTypeList = [];
                comService.getCertificateTypeList().then(function (data) {
                    $scope.certificateTypeList = data.data;
                });
            }


            /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function (data) {
                    if (data.data) {
                        $scope.primaryDeptList = data.data;
                    }
                });
            }

            //修改一级部门，二级部门进行联动
            $scope.changeDept = function () {
                //获取二级部门
                $scope.deptList = [];
                comService.getOrgChildren($scope.primaryDeptCode).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.deptList = data.data;
                    }
                });
            };


            //获取地区
            function getAreaList() {
                $scope.areaList = [];
                comService.getAreaList().then(function (data) {
                    $scope.areaList = data.data;
                });

            }

             /**
             *部门控件的回调处理
             **/
            function departmentCallBack(result){
                 if(result.code === '00'){
                     $state.go('app.office.unAuthority');
                     return;
                 }
                 if(result.code === '01'){
                    //01全部权限
                    $scope.flag = true;
                 }
                 if(result.code === '02'){
                    //部门权限控制
                     $scope.flag = false;
                 }
                 if (result.code === '03') {
                    //地区权限控制
                     $scope.flag = true;
                     $scope.areaCodeFlag = true;
                     $('#area').attr('disabled', true);
                     $scope.searchObject.area = result.areaCode;
                 }
                $scope.getData(1);
            }


            //定义排序对象
            $scope.orderObj = {
                title: '$index',
                desc: true,
                order: function (str) {
                    $scope.orderObj.title = str;
                    $scope.orderObj.desc = !$scope.orderObj.desc;
                }
            };

            //获得证书时间
            $scope.openCertificateTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openCertificateTime1 = true;
            };

            //分页查询
            function getData(page) {
                if ($scope.searchObject.getCertificateTime !== "" || $scope.searchObject.getCertificateTime !== null) {
                    $scope.searchObject.getCertificateTime = inform.format($scope.searchObject.getCertificateTime, 'yyyy-MM-dd');
                }
                //拼装查询条件
                var params = {
                    area: $scope.searchObject.area,
                    primaryDept: $scope.primaryDeptCode,
                    department: $scope.departmentCode,
                    employeeName: $scope.searchObject.employeeName,
                    certificate: $scope.searchObject.certificate,
                    getCertificateTime: $scope.searchObject.getCertificateTime,
                    certificateType: $scope.searchObject.certificateType,
                    page: page,
                    size: $scope.pages.size
                };
                //获取数据(0002无权限访问；0000允许访问)
                staffCertificateService.selectByParam(JSON.stringify(params)).then(function (result) {

                    if (result.code === '0000') {
                        $scope.dataList = result.data.list;
                        if (null == result.data.list || result.data.list.length === 0) {
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages();
                        } else {
                            $scope.pages.total = result.data.total;		// 页面总数
                            $scope.pages.star = result.data.startRow;  	//页面起始数
                            $scope.pages.end = result.data.endRow;  		//页面大小数
                            $scope.pages.pageNum = result.data.pageNum;  	//页面页数
                        }
                    } else {
                        inform.common(result.message);
                    }
                }, function (reason) {
                    console.log("error");
                });
            }

            //初始化页面
            function initPage() {
                getAreaList();//地区
                getCertificateTypeList();
            }

            /**
             * 报表界面切换(详情)
             */
            $scope.openAddModal = function () {
                LocalCache.setObject('staffCertificate_searchObject', $scope.searchObject);
                $state.go('app.office.staffCertificateAddController');
            };

            //打开修改窗口
            $scope.openUpdateModal = function (item) {
                LocalCache.setObject('staffCertificate_searchObject', $scope.searchObject);
                $state.go('app.office.staffCertificateUpdateController', {
                    id: item.id
                });
            };

            // 删除确认
            $scope.deleteConfirm = function (item) {
                inform.modalInstance("确定要删除吗?").result.then(function () {
                    $scope.deleteByParam(item);
                });
            };


            //下载图片
            $scope.downloadPicture = function (id) {
                inform.modalInstance("确定要下载吗?").result.then(function () {
                    staffCertificateService.selectById(id).then(function (res) {
                        if (res.code === '0000') {
                            inform.downLoadFile('picture/downloadPicture', id, '职业证书.zip');
                        } else {
                            inform.common('该条记录不存在图片，请先上传。');
                        }
                    });
                });
            };

            //根据选中的id 删除数据
            $scope.deleteByParam = function (id) {
                staffCertificateService.deleteByIds(id)
                    .then(function (data) {
                        if (data.code === "0000") {
                            inform.common('删除成功');
                            getData(1);
                        } else {
                            inform.common(data.message);
                        }
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            };


            //导出Excel表格
            $scope.toExcel = function () {
                inform.modalInstance("确定要下载吗?").result.then(function () {
                    var params = $scope.searchObject;
                    inform.downLoadFile('staffCertificate/toExcel', params, '员工证书信息.xlsx');
                });
            };

            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */


        }
    ]);
})();
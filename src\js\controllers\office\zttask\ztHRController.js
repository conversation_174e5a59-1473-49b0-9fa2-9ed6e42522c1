/*
 * @Author: fubaole
 * @Date:   2017-09-18 14:53:05
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-01-15 17:20:32
 */
(function() {
	app.controller("hrManagement", ['comService','$rootScope', '$scope','$state','$stateParams','$modal','inform','Trans','AgreeConstant','LocalCache','$http',
	                                       function (comService,$rootScope, $scope,$state,$stateParams, $modal,inform,Trans,AgreeConstant,LocalCache,$http) {
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		//设置列表的宽度
		setDivWidth();
		//窗体大小变化时重新计算宽度
		$(window).resize(setDivWidth);
	    //设置列表的宽度
 		function setDivWidth(){
 			//网页可见区域宽度
 			var clientWidth = document.body.clientWidth - 200;
 			$("#projectsee").width(clientWidth);
 		}
 		//查询条件
		$scope.formRefer = {
	    	
	    	startTime:'',//开始时间	
	    };
		$scope.time = inform.format(new Date(),'yyyy-MM');
 		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
 		
 		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */	
		
		//重置
		$scope.rest = function() {
			$scope.formRefer.startTime = '';
		};
        
		//生成Excel表格
		$scope.toExcel = function() {

			//报表查看月份要小于当前月份
			if ($scope.formRefer.startTime < $scope.time && $scope.formRefer.startTime !== ''){
			var modalInstance = $modal.open({
			  templateUrl: 'myModalContent.html',
              controller: 'ModalInstanceCtrl',
              size: "sm",
              resolve: {
                items: function() {
                return "确定要下载吗！";
                }
             }
			});
	       modalInstance.result.then(function() {
				//开启遮罩层
				inform.showLayer("下载中。。。。。。");
				$http.post(
						$rootScope.getWaySystemApi+'ZTHR/toExcel',
						{
							'startTime':$scope.formRefer.startTime
						},
	            		 {headers: {
									'Content-Type': 'application/json',
									'Authorization':'Bearer ' + LocalCache.getSession("token")||''
								},
						  responseType: 'arraybuffer'//防止中文乱码
						 }
	            		 ).success(function(data){
	            			//如果是IE浏览器
	            			if (window.navigator && window.navigator.msSaveOrOpenBlob) {
	            				var csvData = new Blob([data], {type:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
	            				window.navigator.msSaveOrOpenBlob(csvData,'16 项目人力投入数据报表.xlsx');
	            			}
	            			//google或者火狐浏览器
	            			else{
	            				var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
	    						var objectUrl = URL.createObjectURL(blob);
	    						var aForExcel = $("<a download='16 项目人力投入数据报表.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href",objectUrl);
	    						$("body").append(aForExcel);
	    						$(".forExcel").click();
	    						aForExcel.remove();
	            			}
	            			// 关闭遮罩层
	 						inform.closeLayer();
	 						inform.common("下载成功!");
	            		 });
					
	    	   
	       });
			}else {
    			inform.common(Trans("请输入报表查看月份且查看月份要小于当前月份"));
    		}
	         };
	         /**
		 		 * *************************************************************
		 		 *              方法声明部分                                 结束
		 		 * *************************************************************
		 		 */
	} ]);
})();
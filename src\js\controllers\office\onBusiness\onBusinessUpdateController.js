
(function() {
	app.controller("onBusinessUpdate", ['onBusinessService','comService', '$rootScope', '$scope', 'codeConfigService', '$stateParams', 'inform', 'Trans', 'AgreeConstant','$state',
		function(onBusinessService,comService, $rootScope, $scope, codeConfigService,$stateParams, inform, Trans, AgreeConstant, $state) {
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		// 正则校验配置
		$scope.limitList = AgreeConstant.limitList;
		//初始化页面信息
	    initPages();
	    getData();
        //设置列表的高度
		setDivHeight();

		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */	

		/**
    	 * 设置列表的高度
    	 */
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 185);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 50);
 		}
		/**
		 * 页面初始化
		 */
		function initPages() {
			//获取员工信息
			$scope.employeeMap={};
			$scope.employeeList = [];
			comService.getEmployeesByOrgId('').then(function(data) {
				$scope.employeeList =  data.data;
				 angular.forEach($scope.employeeList, function (res, index) {
                     $scope.employeeMap[res.employeeNo] = res.realName;
                 });
			});
			//获取项目信息
			$scope.nameNumMap={};//根据名字取编号
        	$scope.numIdMap={};//根据编号取ID
			$scope.projectIdList = [];
			codeConfigService.getProjectList().then(function (data) {
				$scope.projectIdList =data.data;
				 angular.forEach($scope.projectIdList, function (project, index) {
                     $scope.numIdMap[project.projectNumber] = project.id;
                     $scope.nameNumMap[project.cname] = project.projectNumber;
                 });
			});
			//获取出差目的
			$scope.purposeList = [];
			$scope.purposeMap = [];
			comService.getParamList('ONBUSINESS', 'ONBUSINESS').then(function (data) {
				$scope.purposeList = data.data;
				angular.forEach($scope.purposeList, function (res, index) {
					$scope.purposeMap[res.param_code] = res.param_value;
				});
			});

			//获取出差方式
           $scope.onBusinessModeList = [];
           comService.getParamList('ONBUSINESS_MODE', 'ONBUSINESS_MODE').then(function (data) {
                if (data.data) {
                    $scope.onBusinessModeList = data.data;
                }
            });
			//获取产品线
			$scope.lineList = [];
			comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
				$scope.lineList = angular.fromJson(data.data);
			});

		}
		/**
		 * 根据id获取出差信息
		 */
        function getData(){
        	onBusinessService.getOnBusinessInfo($stateParams).then(function(data){
            	//页面赋值
            	$scope.changeParam = data.data.list[0];
            	$scope.changeParam.onBusinessProjectNum = $scope.changeParam.projectNum;
        	});
     	}

        /**
         * 计算出差时间
         */
        function stringToDate(){
			var returnTime;
			if(typeof ($scope.changeParam.returnTime)==='string'){
				returnTime = inform.stringToDate($scope.changeParam.returnTime);
			}else{
				returnTime = $scope.changeParam.returnTime;
		    }
			var setOffTime;
			if(typeof ($scope.changeParam.setOffTime)==='string'){
				setOffTime = inform.stringToDate($scope.changeParam.setOffTime);
			}else{
				setOffTime = $scope.changeParam.setOffTime;
			}
			return (returnTime - setOffTime)/(1000 * 60 * 60 * 24) + 1;
		}
       /**
        * 获取项目编号:“出差项目”时联动查出“项目编号”
        */
        $scope.getNum = function(){
        	$scope.changeParam.onBusinessProjectNum = $scope.nameNumMap[$scope.changeParam.onBusinessProject];
        }
		/**
		 * 修改信息
		 */
		$scope.updateOnBusinessInfo = function() {
			if($scope.changeParam.purpose==='0001'||$scope.changeParam.purpose==='0002'||$scope.changeParam.purpose==='0003'){
                if($scope.changeParam.onBusinessProject==null||$scope.changeParam.onBusinessProject===''){
                    inform.common("出差类别为部署实施、驻场开发、解决问题时“出差项目”必输");
                    return;
                }
				if($scope.changeParam.onBusinessProjectNum==null||$scope.changeParam.onBusinessProjectNum===''){
	   			 	inform.common("出差类别为部署实施、驻场开发、解决问题时“项目编号”必输");
	   			 	return;
		   		}
		   	}
		   	if($scope.changeParam.onBusinessProjectNum!=null&&$scope.changeParam.onBusinessProjectNum!==''){
		   		var proId = $scope.numIdMap[$scope.changeParam.onBusinessProjectNum];
		   		if (proId==null){
		   			 inform.common("请输入正确的项目编号");
		      			 return;
		   		}
		   		$scope.proId = proId;
		   	}
		   	//添加出差信息至出差表
		   	updateInfo();
	    };
		/**
		 * 添加出差信息至出差表
		 */
		function updateInfo(){
			var onBusinessDays =stringToDate();
			var businessInfo = {
				'id':$scope.changeParam.id,
				'instanceNumber':$scope.changeParam.instanceNumber,
				'department':$scope.changeParam.department,
				'employeeId':$scope.changeParam.employeeId,
				'setOffTime':inform.format($scope.changeParam.setOffTime,'yyyy-MM-dd'),
				'returnTime':inform.format($scope.changeParam.returnTime,'yyyy-MM-dd'),
				'onBusinessDays':onBusinessDays,
				'site':$scope.changeParam.site,
				'onBusinessProject':$scope.changeParam.onBusinessProject==null?" ":$scope.changeParam.onBusinessProject,
				'purpose':$scope.changeParam.purpose,
				'onBusinessMode':$scope.changeParam.onBusinessMode,
				'transportationFee':$scope.adjustNumber($scope.changeParam.transportationFee),
				'accommodationFee':$scope.adjustNumber($scope.changeParam.accommodationFee),
				'localTransportationFee':$scope.adjustNumber($scope.changeParam.localTransportationFee),
				'businessEntertainmentFee':$scope.adjustNumber($scope.changeParam.businessEntertainmentFee),
				'serviceFee' : $scope.adjustNumber($scope.changeParam.serviceFee),
				'otherFee':$scope.adjustNumber($scope.changeParam.otherFee),
				'foodSubsidies':$scope.adjustNumber($scope.changeParam.foodSubsidies),
				'communicationSubsidy':$scope.adjustNumber($scope.changeParam.communicationSubsidy),
				'personalReimburseFee': $scope.adjustNumber($scope.changeParam.personalReimburseFee),
                'fenBeiTongFee': $scope.adjustNumber($scope.changeParam.fenBeiTongFee),
				'total':$scope.changeParam.total,
				'onBusinessDetailProject':$scope.changeParam.onBusinessDetailProject,
				'productLine':$scope.changeParam.productLine,
                'projectNum':$scope.changeParam.onBusinessProjectNum
			};
			var feeInfo={
				'onbusinessId':$scope.changeParam.id,
			    //项目ID
			    'projectId': $scope.proId,
			    //费用类型
			    'expenseTypeId':'COSTFEE_TYPE_1',
			    //费用金额
			    'amount': $scope.changeParam.total,
			    //费用主体
			    'feeObject': $scope.employeeMap[$scope.changeParam.employeeId],
			    //费用描述
			    'feeDescription': ($scope.changeParam.onBusinessDetailProject==null||$scope.changeParam.onBusinessDetailProject==='')?'差旅费':$scope.changeParam.onBusinessDetailProject,
			    //费用的发生时间
			    'feeTime': inform.format($scope.changeParam.returnTime, 'yyyy-MM-dd')
			};
			var urlData={
				businessInfo: businessInfo,
				feeInfo: feeInfo
			};
			onBusinessService.updateOnBusinessInfo(urlData).then(function(data){
				if(data.code===AgreeConstant.code) {
					inform.common(data.message);
					//调用返回项目信息页面方法
					$scope.goback();
				} else{
					inform.common(data.message);
				}
			}, function(error) {
				inform.common(Trans("tip.requestError"));
			});
		}
		/**
		 * 返回项目信息页面
		 */
		$scope.goback = function() {
			$state.go('app.office.onBusinessManagement');
		};

		/**
		 * 判断数字是否为undefined、null、空,
         * 如果是则返回数字0，
         * 否则将返回值设为数字
		 */
		$scope.adjustNumber = function(str) {
			if(typeof(str)==='undefined' || str==null || str === '' ){
				return parseFloat('0');
			}
			return parseFloat(str);
		};

		/**
		 * 计算合计金额
		 */
		$scope.getTotal = function() {
			var total = $scope.adjustNumber($scope.changeParam.transportationFee)
				+ $scope.adjustNumber($scope.changeParam.accommodationFee)
				+ $scope.adjustNumber($scope.changeParam.localTransportationFee)
				+ $scope.adjustNumber($scope.changeParam.businessEntertainmentFee)
				+ $scope.adjustNumber($scope.changeParam.serviceFee)
				+ $scope.adjustNumber($scope.changeParam.otherFee)
				+ $scope.adjustNumber($scope.changeParam.foodSubsidies)
				+ $scope.adjustNumber($scope.changeParam.communicationSubsidy)
				+ $scope.adjustNumber($scope.changeParam.personalReimburseFee)
				+ $scope.adjustNumber($scope.changeParam.fenBeiTongFee);
			$scope.changeParam.total = total.toFixed(2);
		};

		/**
		 *  修改的出发时间按钮
		 */
		$scope.setOffTime_input = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.returnTime_input1 = false;
			$scope.setOffTime_input1 = true;
		};
		/**
		 *  修改的返回时间按钮
		 */
		$scope.returnTime_input = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.setOffTime_input1 = false;
			$scope.returnTime_input1 = true;
		};

		//计算出差天数
		$scope.getOnBusinessDays= function(){
			if(typeof ($scope.changeParam.setOffTime)==='undefined'
				|| typeof ($scope.changeParam.returnTime)==='undefined'
			    || $scope.changeParam.setOffTime==null
			    || $scope.changeParam.returnTime==null){
				$scope.changeParam.onBusinessDays = 0;
			}else{
				$scope.changeParam.onBusinessDays = stringToDate();
			}
		};

		//根据出差类别，设置产品线信息
		$scope.getProductLine =function () {
			var purpose = $scope.changeParam.purpose;
			var productLine = $scope.changeParam.productLine;
			var productLineFlag = false;
			var purposeFlag = false;

			//判断产品线当前值是否为异地管理、培训、其他
			if(productLine===$scope.purposeMap['0006'] || productLine===$scope.purposeMap['0007'] || productLine===$scope.purposeMap['0008'] ){
				productLineFlag = true;
			}
			//判断出差类别当前值是否为异地管理、培训、其他
			if(purpose==='0006' || purpose==='0007' || purpose==='0008'){
				purposeFlag = true;
			}

			//出差类别为异地管理、培训、其他，将产品线设置为异地管理、培训、其他
			if(purposeFlag){
				$scope.changeParam.productLine = $scope.purposeMap[purpose];
			}
			//出差类别不为异地管理、培训、其他，产品线为异地管理、培训、其他，则置空产品线
			if(!purposeFlag && productLineFlag){
				$scope.changeParam.productLine = '';
			}
		};

			/**
	     * *************************************************************
	     *              方法声明部分                                结束
	     * *************************************************************
	     */	
		} ]);
})();
/*
 * @Author: fubaole
 * @Date:   2018-01-10 17:56:34
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-04-02 17:04:33
 */

(function() {
  'use strict';
  app.controller("work_configurine", ['$rootScope', '$scope', '$state','inform', '$modal', 'Trans', 'SystemService','AgreeConstant',
    function($rootScope, $scope, $state, inform, $modal, Trans, SystemService,AgreeConstant) {
      $scope.defaultImg = AgreeConstant.defaultImg;
      $scope.setDefaultPage = setDefaultPage; // 设置默认页
      $scope.open = open; // 删除配置页
      getData();

      // 获取配置列表
      function getData() {
        SystemService.getPageListByRoleId()
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
             // $scope.resultData = data.result.pageList;
              angular.forEach(data.result.pageList, function(data,index,array){
                if(data.pageImage){
                  data.pageImage = $rootScope.imageDownload+data.pageImage;
                }
              });
              $scope.resultData = data.result.pageList;
              $scope.defaultPageId = data.result.defaultPageId;
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 设置默认页
      function setDefaultPage(id) {
        SystemService.setDefaultPageByRoleId(id)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              inform.common(data.message);
              $state.go('app.system.work_configurine',{},{reload:true});
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 删除配置
      function removePerPage(id) {
        SystemService.removePageByRoleId(id)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              inform.common(data.message);
              $state.go('app.system.work_configurine',{},{reload:true});
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 删除配置页
      function open(item) {
        var modalInstance = $modal.open({
          templateUrl: 'myModalContent.html',
          controller: 'ModalInstanceCtrl',
          size: "sm",
          resolve: {
            items: function() {
              return Trans("common.deleteTip");
            }
          }
        });

        modalInstance.result.then(function() {
          if (item) {
            removePerPage(item);
          }
        });
      }

    }
  ]);
})();
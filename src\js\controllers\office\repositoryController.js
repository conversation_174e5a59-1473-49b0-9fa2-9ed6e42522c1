
(function () {
    app.controller("repositoryManagement", ['comService','$http','$rootScope', '$state','$scope','$modal', 'repositoryService','inform','Trans','AgreeConstant','LocalCache',
        function (comService,$http,$rootScope,$state, $scope, $modal,repositoryService,inform,Trans,AgreeConstant,LocalCache) {
    	
    	$scope.formRefer = LocalCache.getObject('formRefer') || {}; //仓库查询条件
 
    	/**
    	 * 结果
    	 */
    	//保存所有仓库信息
    	$scope.repositoryList = [];
    	

    	$scope.pages = inform.initPages(); // 初始化分页数据
    	$scope.getData = getData; // 分页相关函数
        
        /**
         * 方法
         */
    	//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		
		//设置列表的高度
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 220);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 50);
 		}
    	//刷新页面时就执行该方法
    	getData($scope.pages.pageNum);
    	//初始化页面信息
    	initPages();
    	/**
    	 * 页面初始化
    	 */
    	function initPages() {
    		//获取地区
    		$scope.areaList = [];
    		comService.getParamList('AREA_TYPE','AREA_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.areaList =  data.data;
        		}
            });
    		//获取产品线
    		$scope.productLineList = [];
    		comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.productLineList =  data.data;
        		}
            });
    	}
    	
    	/**
    	 * 根据分页显示仓库
    	 * pageNum 当前页数
    	 */
    	 function getData(pageNum) {
    		 
    		 LocalCache.setObject('formRefer',$scope.formRefer);
    		 
    		 if(LocalCache.getObject('formRefer')){
    		        $scope.formRefer = LocalCache.getObject('formRefer'); // 初始化查询数据
    		      }else{
    		        $scope.formRefer = {
    		    			repositoryname:'',//仓库名
    		    			productline:'',//产品线
    		    			projectname:'',//项目名称
    		    			territory:'',//地域
    		    			startTime:'',//开始时间
    		    			endTime:''//结束时间	
    		    				}; // 初始化查询数据
    		      }
    		 
    		     //返回的仓库信息列表
    			 $scope.repositoryList = [];
    	    		//存查询条件
    	          	var urlData={'repositoryname':$scope.formRefer.repositoryname,//仓库名
    	          			'productline':$scope.formRefer.productline,//产品线
    	          			'projectname':$scope.formRefer.projectname,//项目名
    	          			'territory':$scope.formRefer.territory,//地域
    	          			'startTime':$scope.formRefer.startTime,//开始时间
    	          			'endTime':$scope.formRefer.endTime,//结束时间
    	          			'currentPage':pageNum,//当前页数
    	          			'pageSize':$scope.pages.size};//每页显示条数
    	     
    	          	var dateFlag = $scope.formRefer.startTime >= $scope.formRefer.endTime ? 1 : 0;
    	          	
    	          	if(dateFlag===0){
	    	          	repositoryService.findRepositoryListByPage(urlData).then(function(data) {
	    	         		if (data.code === AgreeConstant.code) {
	    	         			var jsonData = data.data;
	    	         			$scope.repositoryList = jsonData.list;
	    	                     if ($scope.repositoryList.length === 0) {
	    	                    	 inform.common(Trans("tip.noData"));
	        	                     $scope.pages = inform.initPages();
	    	                       } else {
	    	                 	  	//分页信息设置
	    	 	                    $scope.pages.total = jsonData.total;
	    	 	                    $scope.pages.star = jsonData.startRow;
	    	 	                    $scope.pages.end = jsonData.endRow;
	    	 	                    $scope.pages.pageNum = jsonData.pageNum;
	    	                       }
	    	 	              } else {
	    	 	                inform.common(data.message);
	    	 	              }
	    	             }, function(error) {
	    	               inform.common(Trans("tip.requestError"));
	    	             });
    	          	} else {
    	          		inform.common(Trans("创建时间应小于截止时间"));
    	          	}
    		 
         }
    	 /**
			 * 开始时间
			 */ 
			$scope.openDateStart = function($event) {
				$event.preventDefault();
				$event.stopPropagation();
				$scope.openedStart = true;    //开始时间
				$scope.openedEnd = false;
			};
			
			/**
			 *  结束时间
			 */
			$scope.openDateEnd = function($event) {
				$event.preventDefault();
				$event.stopPropagation();
				$scope.openedStart = false;    
				$scope.openedEnd = true;    //结束时间
			};
    	 /**
     	 * 下载Excel
     	 */
    	 $scope.excelData=function () {

             //拼装查询条件
             var urlData={'repositoryname':$scope.formRefer.repositoryname,//查询的仓库名
           			'productline':$scope.formRefer.productline,//查询的产品线
           			'projectname':$scope.formRefer.projectname,//查询的项目名称
           			'territory':$scope.formRefer.territory//查询的地域
           			};
             var modalInstance = $modal.open({
	        		templateUrl: 'myModalContent',
	        		controller: 'ModalInstanceCtrl',
	        		size: "sm",
	        		resolve: {
	        			items: function() {
	        				return "是否确定下载？";
	        			}
	        		}
	        	});
             modalInstance.result.then(function() {
            	 inform.downLoadFile ('repository/loadRepositoryToExcel',urlData,'仓库信息表.xlsx');
            });
         };
         
        /**
      	 * 跳转至仓库修改页面
      	 */
     	 $scope.toRepositoryUp=function (id) {
     		$state.go("app.office.repositoryUpdate",{repositoryid:id});
          };
     	
      }]);
})();
(function () {
    app.controller("performanceAssessSectionController", ['comService','kpiRelationService','$rootScope','$window', '$state','$scope','$stateParams', '$modal','inform','LocalCache','Trans','AgreeConstant','$http',
        function (comService,kpiRelationService,$rootScope,$window,$state, $scope,$stateParams,$modal,inform,LocalCache,Trans,AgreeConstant,$http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.formRefer={};
            //页面分页信息
            $scope.pages = {
                pageNum : '',   //分页页数
                size : '',      //分页每页大小
                total : ''      //数据总数
            };
            //控制页签展示
            $scope.developer= false;
            $scope.test= false;
            $scope.manager= false;
            $scope.leader= false;
            //控制折叠按钮
            $scope.down = true;
            $scope.Dshow = true;
            $scope.typeSelect = null;
            // 初始化分页数据
            $scope.pages = inform.initPages();
            $scope.pages.size = '50';
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            initInfo();
            $scope.getData = getData;
            //季度下拉框数据源
            $scope.quarterSelect = [
                {
                    value: '上半年',
                    label: '上半年'
                },{
                    value: '全年',
                    label: '全年'
                }];
            $scope.LevelList = [
                {
                    value: 'A',
                    label: 'A'
                },{
                    value: 'B',
                    label: 'B'
                },{
                    value: 'C',
                    label: 'C'
                },{
                    value: 'D',
                    label: 'D'
                },{
                    value: 'E',
                    label: 'E'
                }];
            $scope.scoreLevelMap = [{
                score: 5,
                level: 'A'
            },{
                score: 4,
                level: 'B'
            },{
                score: 3,
                level: 'C'
            },{
                score: 2,
                level: 'D'
            },{
                score: 1,
                level: 'E'
            },{
                score: 0,
                level: ''
            }
            ];
            $scope.evaluateStatus = [{
                value: '1', label: '已评价'
            }, {
                value: '0', label: '未评价'
            }];
            var paramObj = {
                subDeptList:'departmentList',
                subDeptScopeModel:'departmentCode'
            };
            //权限控制
            comService.checkAuthentication($scope,paramObj,departmentCallBack,LocalCache.getSession('loginName'));
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 250);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 80);
            }
            /**
             * 初始化
             */
            function initInfo() {
                initTime();
                //读取缓存
                if(JSON.stringify(LocalCache.getObject('down')) !== "{}"){
                    $scope.down = LocalCache.getObject('down');
                    LocalCache.setObject('down', {});
                }
                //获取区域信息
                $scope.areaList = [];
                comService.getAreaList().then(function(data) {
                    $scope.areaList = data.data;
                });
                //获取员工岗位信息
                $scope.staffTitleList = [];
                comService.getParamList('STAFF_INFO_TITLE', 'NEW').then(function (data) {
                    $scope.staffTitleList = data.data;
                });
                //获取员工职称信息
                $scope.companyTitleList = [];
                comService.getParamList('STAFF_TITLE', 'NEW').then(function (data) {
                    $scope.companyTitleList = data.data;
                });
                //获取山东新北洋集团的下级部门信息
                $scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentList = data.data;
                });
                $scope.backFlag = JSON.stringify(LocalCache.getObject('performanceAssessResult')) !== "{}"
            }
            /**
             * 初始化检索条件年度与季度
             */
            function initTime(){
                //当前时间（Date类型）
                var date = new Date();
                //得到上一个季度的第一天
                var lastQuarterFirstDay = new Date(date.getFullYear() , date.getMonth() - 3 , 1);
                var day = inform.format(lastQuarterFirstDay, 'yyyy-MM-dd').split("-");
                $scope.formRefer.year = $scope.formRefer.year != null ? $scope.formRefer.year : day[0];
                //设置季度
                var month = lastQuarterFirstDay.getMonth() + 1;
                $scope.formRefer.quarter = month>6?'全年':'上半年';
            }
            /**
             *部门控件的回调处理
             **/
            function departmentCallBack(result) {
                if (result.code === '00') {
                    $state.go('app.office.unAuthority');
                    return;
                }
                //一级部门负责人
                if ($scope.departmentCode === '') {
                    $scope.test = true;
                    $scope.developer = true;
                    $scope.manager = true;
                    $scope.leader = true;
                    $scope.typeSelect = 'developer'
                } else {
                    //二级部门负责人
                    //隐藏请选择，防止查询到全体员工的考核结果
                    $('#departmentTemp').hide();
                    angular.forEach($scope.departmentList, function (index) {
                        if($scope.formRefer.department == null){
                            $scope.formRefer.department = index.orgName;
                        }
                        if (index.orgName === '测试研究室') {
                            //测试二级部门负责人  允许查询测试工程师页签
                            $scope.test = true;
                        } else {
                            //其他二级部门负责人  允许查看 开发工程师，项目经理、leader页签
                            $scope.developer = true;
                            $scope.manager = true;
                            $scope.leader = true;
                        }
                    });
                    if($scope.test === true){
                        $scope.typeSelect = 'ROLE_LIBRARY_4'
                    }
                    if($scope.developer === true){
                        $scope.typeSelect = 'developer'
                    }
                    if($scope.test === true && $scope.developer === true){
                        $scope.formRefer.department = $scope.departmentList[1].orgName;
                    }

                }
                //查询条件读取缓存
                if(JSON.stringify(LocalCache.getObject('performanceAssess')) !== "{}"){
                    $scope.formRefer = LocalCache.getObject('performanceAssess');
                    LocalCache.setObject('performanceAssess', {});
                }
                //读取来自考核结果界面传递的数据
                $scope.selectParam = JSON.parse($stateParams.urlData);
                if($scope.selectParam != null){
                    if($scope.selectParam.typeSelect === 'ROLE_LIBRARY_4'||$scope.selectParam.typeSelect ==='ROLE_LIBRARY_12'||$scope.selectParam.typeSelect === 'ROLE_LIBRARY_8'){
                        $scope.typeSelect = $scope.selectParam.typeSelect;
                    }else{
                        $scope.typeSelect = 'developer';
                    }
                    $scope.formRefer.employeeName = $scope.selectParam.employeeName;
                    $scope.formRefer.year = $scope.selectParam.year;
                    $scope.formRefer.quarter = $scope.selectParam.quarter;
                }
                //如果页签选择的缓存不为空，则使用缓存数据
                if(JSON.stringify(LocalCache.getObject('typeSelect')) !== "{}"){
                    $scope.typeSelect = LocalCache.getObject('typeSelect');
                    LocalCache.setObject('typeSelect', {});
                }
                $scope.getData(1);
            }


            /**
             * 查询考核结果数据
             * @param pageNum
             */
            function getData(pageNum) {
                //删除已加载冻结头部和部分列的HTML模板
                $scope.dataTableShow = 0;
                if($scope.typeSelect === 'developer'||$scope.typeSelect === 'ROLE_LIBRARY_4'){
                    //如果为开发或者测试页签，则查询区域考核情况和职称考核情况
                    getAreaData();
                    getTitleData();
                }else{
                    //如果为经理或者leader页签， 则查询总体考核情况
                    getAreaData();
                }
                var urlData = {
                    'employeeName':$scope.formRefer.employeeName,
                    'companyTitle':$scope.formRefer.companyTitle,
                    'department':$scope.formRefer.department,
                    'job':$scope.formRefer.job,
                    'area':$scope.formRefer.area,
                    'finalResult': $scope.formRefer.finalResult,
                    'year':$scope.formRefer.year,
                    'quarter':$scope.formRefer.quarter,
                    'finalAssessEvaluate':$scope.formRefer.finalAssessEvaluate,
                    'deviation':$scope.formRefer.deviation,
                    'currentPage':pageNum,
                    'pageSize':$scope.pages.size,
                    'typeSelect':$scope.typeSelect
                };

                kpiRelationService.getPerformanceAssessData(urlData).then(function (data) {
                        //重新加载冻结头部和部分列的HTML模板
                        $scope.dataTableShow = 1;
                        if (data.code===AgreeConstant.code) {
                            $scope.dataList = data.data.list;
                            if ($scope.dataList.length===0) {
                                $scope.pages = inform.initPages(); 			//初始化分页数据
                                inform.common(Trans("tip.noData"));
                            } else {
                                // 分页信息设置
                                $scope.pages.total = data.data.total;           // 页面数据总数
                                $scope.pages.star = data.data.startRow;         // 页面起始数
                                $scope.pages.end = data.data.endRow;            // 页面结束数
                                $scope.pages.pageNum = data.data.pageNum;       //页号
                            }
                            // 获取到跟踪单信息之后滚动条跳至缓存中保存的scrollTop
                            setTimeout(function () {
                                $window.scrollTo(0, $scope.formRefer.pageOffset || 0);
                                if ($scope.formRefer.subDivTBDisScrollTop) {
                                    $('#fixedLeftAndTop').parent().animate({scrollTop: $scope.formRefer.subDivTBDisScrollTop},10);
                                }

                            },500);
                            //调用DataTable组件冻结表头和左侧及右侧的列
                            setTimeout(showDataTable,300);

                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

        /**
        *调用DataTable组件冻结表头和左侧及右侧的列
        */
        function showDataTable(){
            $('#fixedLeftAndTop').DataTable( {
                //可被重新初始化
                retrieve:       true,
                //自适应高度
                scrollY:        'calc(100vh - 350px)',
                scrollX:        true,
                scrollCollapse: true,
                //控制每页显示
                paging:         false,
                //冻结列（默认冻结左1）
                fixedColumns:   {
                    leftColumns: 3,
                    rightColumns: 2
                },
                //search框显示
                searching:      false,
                //排序箭头
                ordering:       false,
                //底部统计数据
                info:           false
            } );

        }
            //获取header数据
            function getAssessAnalysisHeader(pageNum) {
                var urlData = {
                    'employeeName':$scope.formRefer.employeeName,
                    'companyTitle':$scope.formRefer.companyTitle,
                    'department':$scope.formRefer.department,
                    'job':$scope.formRefer.job,
                    'area':$scope.formRefer.area,
                    'finalResult': $scope.formRefer.finalResult,
                    'year':$scope.formRefer.year,
                    'quarter':$scope.formRefer.quarter,
                    'finalAssessEvaluate':$scope.formRefer.finalAssessEvaluate,
                    'deviation':$scope.formRefer.deviation,
                    'typeSelect':$scope.typeSelect
                };
                kpiRelationService.getAssessAnalysisHeader(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                            $scope.headerProportion = data.data.proportion;
                            $scope.dList = data.data.demployeeList;
                            $scope.dCount = data.data.dcount;
                            $scope.eList = data.data.eemployeeList;
                            $scope.eCount = data.data.ecount;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            //获取考核结果区域分析
            function getAreaData(pageNum) {
                getAssessAnalysisHeader();
                var urlData = {
                    'employeeName':$scope.formRefer.employeeName,
                    'companyTitle':$scope.formRefer.companyTitle,
                    'department':$scope.formRefer.department,
                    'job':$scope.formRefer.job,
                    'area':$scope.formRefer.area,
                    'finalResult': $scope.formRefer.finalResult,
                    'year':$scope.formRefer.year,
                    'quarter':$scope.formRefer.quarter,
                    'finalAssessEvaluate':$scope.formRefer.finalAssessEvaluate,
                    'deviation':$scope.formRefer.deviation,
                    'typeSelect':$scope.typeSelect
                };
                kpiRelationService.getAnalysisDataByArea(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                            $scope.areaData = data.data;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            //获取考核结果职称分析
            function getTitleData(pageNum) {
                var urlData = {
                    'employeeName':$scope.formRefer.employeeName,
                    'companyTitle':$scope.formRefer.companyTitle,
                    'department':$scope.formRefer.department,
                    'job':$scope.formRefer.job,
                    'area':$scope.formRefer.area,
                    'finalResult': $scope.formRefer.finalResult,
                    'year':$scope.formRefer.year,
                    'quarter':$scope.formRefer.quarter,
                    'finalAssessEvaluate':$scope.formRefer.finalAssessEvaluate,
                    'deviation':$scope.formRefer.deviation,
                    'typeSelect':$scope.typeSelect
                };
                kpiRelationService.getAnalysisDataByTitle(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                            $scope.titleData = data.data;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
           function getScoreByResult(result){
                switch (result) {
                    case "A":
                        return 5;
                    case "B":
                        return 4;
                    case "C":
                        return 3;
                    case "D":
                        return 2;
                    case "E":
                        return 1;
                    default:
                        return 0;
                }
            }
            //根据分数获取相应等级
            function getLevelByScore(score){
                for(var i = 0;i < $scope.scoreLevelMap.length;i++){
                    if($scope.scoreLevelMap[i].score === score){
                        return $scope.scoreLevelMap[i].level;
                    }
                }
                return '';
            }
            /**
             * 计算理论权重得分，查询条件写入缓存，跳转到评价界面
             */
            $scope.evaluate = function (m) {
                var score = (
                    ((getScoreByResult(m.personResult1) * m.percent1) || 0) +
                    ((getScoreByResult(m.personResult2) * m.percent2) || 0) +
                    ((getScoreByResult(m.personResult3) * m.percent3) || 0)
                    ) / 100;
                m.hrGuideline = getLevelByScore(Math.floor(score));
                $scope.changeParam = angular.copy(m);
                $scope.formRefer.subDivTBDisScrollTop = $('#fixedLeftAndTop').parent().scrollTop();
                $scope.formRefer.pageOffset = $window.pageYOffset;
                LocalCache.setObject('performanceAssess', $scope.formRefer);
                LocalCache.setObject('typeSelect', $scope.typeSelect);
                LocalCache.setObject('down', $scope.down);
                console.log($scope.formRefer);
                var url = 'app.office.performanceAssessEvaluate';
                $state.go(url, {
                    jsonResult: JSON.stringify($scope.changeParam)
                });
            };
            //切换显示状态
            $scope.change = function (){
                $scope.down = !$scope.down;
            }
            //点击“姓名”跳转到个人看板页面
            $scope.toEvaluate = function (m){
                $scope.formRefer.subDivTBDisScrollTop = $('#fixedLeftAndTop').parent().scrollTop();
                $scope.formRefer.pageOffset = $window.pageYOffset;
                //保存查询条件
                LocalCache.setObject('performanceAssess', $scope.formRefer);
                LocalCache.setObject('personDataBoardEmployee', {'name':m.employeeName});
                LocalCache.setObject('typeSelect', $scope.typeSelect);
                LocalCache.setObject('down', $scope.down);
                //跳转到个人看板
                $state.go('app.index_bench',{
                    empId: m.employeeId,
                    years:m.year,
                    quarter:m.quarter === '全年'?5:6});
            }
            //切换header折叠
            $scope.getDetail =  function () {
                $scope.Dshow = $scope.Dshow !== true;
            }
            /**
             * 重置
             */
            $scope.clearParams = function() {
                var department = $scope.formRefer.department;
                $scope.formRefer = {};
                $scope.formRefer.department = department;
                initTime();
            };
            //切换页签
            $scope.toOther = function (typeSelect) {
               $scope.formRefer.subDivTBDisScrollTop = '';
               $scope.formRefer.pageOffset = '';
               $scope.typeSelect = typeSelect;
               $scope.Dshow=true;
               getData();
            }

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
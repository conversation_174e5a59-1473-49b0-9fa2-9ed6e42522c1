(function() {
  	'use strict';
	app.factory('HttpService', HttpService);

	HttpService.$inject=["$q","$http","$rootScope","LocalCache","inform"];

	function HttpService($q,$http,$rootScope,LocalCache,inform){
		var service={
			postLogin:postLogin,
			post:post,
      get :get,
			put :put
		};

		return service;

    //  GET请求
		function get(url,params){
			var deferred = $q.defer();
      inform.showLayer();
			$http({
					method : 'GET',
					url: url,
					params :params,
					headers: {
						'Content-Type': 'application/json',
						'Authorization':'Bearer ' + LocalCache.get("token")||''
					}
			})
			.success(function(data) {
        inform.closeLayer();
				deferred.resolve(data);
			})
			.error(function(data) {
        inform.closeLayer();
        //inform.common("失败");
				deferred.reject(data);
      });
			return deferred.promise;
		}

        //  PUT请求
    function put(url,params){
      var deferred = $q.defer();
      inform.showLayer();
      $http({
          method : 'PUT',
          url: url,
          data :params,
          headers: {
            'Content-Type': 'application/json',
            'Authorization':'Bearer ' + LocalCache.get("token")||''
          }
      })
      .success(function(data) {
        inform.closeLayer();
        deferred.resolve(data);
      })
      .error(function(data) {
        inform.closeLayer();
        deferred.reject(data);
      });
      return deferred.promise;
    }

    //  POST请求
		function post(url,postData){
			var deferred = $q.defer();
      inform.showLayer();
			$http({
				method: 'POST',
				url: url,
				data: postData,
        dataType : 'json',
				headers: {
					'Content-Type': 'application/json',
					'Authorization':'Bearer ' + LocalCache.get("token")||''
				},
			})
			.success(function(data) {
        inform.closeLayer();
				deferred.resolve(data);
			})
			.error(function(data,status,hedaers,config) {
        inform.closeLayer();
        //inform.common("失败");
				deferred.reject(data);
			});
		  return deferred.promise;
		}

    // 登录post提交
    function postLogin(url,postData){
      var deferred = $q.defer();
      inform.showLayer();
      $http({
          method: 'POST',
          url: url,
          data: postData,
          headers: {
              'Content-Type': 'application/json',
              'Authorization':'Basic d2ViX2FwcDo='
          },
      })
      .success(function(data) {
          inform.closeLayer();
          deferred.resolve(data);
      })
      .error(function(data) {
          inform.closeLayer();
          //inform.common("失败");
          deferred.reject(data);
      });
      return deferred.promise;
    }

	}
})();
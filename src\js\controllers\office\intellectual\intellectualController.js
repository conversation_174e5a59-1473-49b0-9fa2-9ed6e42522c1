(function () {
    app.controller("intellectualController", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','intellectualService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,intellectualService,inform,Trans,AgreeConstant,LocalCache,$http) {
       	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		
    	$scope.res = [];   //保存所有信息的集合
		$scope.department = '';
		
		//分页
    	$scope.pages = inform.initPages(); // 初始化分页数据
		initDepartment();
		initTime();
		$scope.getData = getData;
		getData($scope.pages.pageNum);
		$scope.datepicker = {};
        $scope.toggleMin = toggleMin;
        toggleMin();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */


		 //获取当前选定时间
         function toggleMin() {
             $scope.datepicker.currentDate = $scope.datepicker.currentDate ? null : new Date();
         }
		 /**
         * 查询开始时间
         */
        $scope.openDateStart = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = true;
            $scope.openedEnd = false;
        };

        /**
         * 查询结束时间
         */
        $scope.openDateEnd = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = false;
            $scope.openedEnd = true;
        };

 		
    	/**
		 * 重置
		 */ 
		$scope.reset = function() {
			$scope.department='';
			initTime();
		};
		/**
		 * 初始化检索条件开始时间 及 结束时间
		 */
        function initTime(endDate){
			if (endDate==null || endDate==="" ){
				$scope.endDate = inform.format(new Date(),'yyyy-MM-dd');//获取当前系统时间
			} 
			var time = $scope.endDate.split("-");
			var start = time[0]+"/01"+"/01";
			$scope.startDate = inform.format(start,'yyyy-MM-dd');
			//对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
        }
		//获取所有数据
		function getData(pageNum) {
			var postData = {
					'department':$scope.department,
					'startDate':inform.format($scope.startDate,'yyyy-MM-dd'),
					'endDate':inform.format($scope.endDate,'yyyy-MM-dd'),
					'currentPage':pageNum,//当前页数
		          	'pageSize':$scope.pages.size//每页显示条数
				};
			intellectualService.getIntelReportByMap(postData).then(function(data){
				if(data.code===AgreeConstant.code){
					$scope.res = data.data;
					if ($scope.res.length===0) {
	                    inform.common(Trans("tip.noData"));
    	                $scope.pages = inform.initPages();
	                } else {
	                    //分页信息设置
	                    $scope.pages.total = $scope.res.total;
	                    $scope.pages.star = $scope.res.startRow;
	                    $scope.pages.end = $scope.res.endRow;
	                    $scope.pages.pageNum = $scope.res.pageNum;
	                }
	                 $scope.res.sort(function(a,b){
                        // order是规则  objs是需要排序的数组
                        var order = ["平台开发研究室", "项目管理办公室","产品开发一室", "产品开发二室", "产品开发三室", "测试研究室",
                            "项目管理办公室","汇总"];
                        return order.indexOf(a.department) - order.indexOf(b.department);
                     });
				}else{
					inform.common(data.message);
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});			
		}
    	function initDepartment() {
    		//获取山东新北洋集团的下级部门信息
    		$scope.departmentList = [];
    		comService.getOrgChildren('D010053').then(function(data) {
    			$scope.departmentList = comService.getDepartment(data.data);
             });
    	}
		//生成Excel表格
		$scope.toExcel = function() {
			var modalInstance = $modal.open({
			  templateUrl: 'myModalContent.html',
              controller: 'ModalInstanceCtrl',
              size: "sm",
              resolve: {
                items: function() {
                return "确定要下载吗！";
                }
             }
			});
	       modalInstance.result.then(function() {
				//开启遮罩层
				inform.showLayer("下载中。。。。。。");
				var param = {	
					        'department':$scope.department,
							'startDate':inform.format($scope.startDate,'yyyy-MM-dd'),
							'endDate':inform.format($scope.endDate,'yyyy-MM-dd')
						}
				$http.post(
						$rootScope.getWaySystemApi+'intellectual/toExcel',param,
	            		{headers: {
									'Content-Type': 'application/json',
									'Authorization':'Bearer ' + LocalCache.getSession("token")||''
								},
						  responseType: 'arraybuffer'//防止中文乱码
						}
	            		).success(function(data){
	            			//如果是IE浏览器
	            			if (window.navigator && window.navigator.msSaveOrOpenBlob) {
	            				var csvData = new Blob([data], {type:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
	            				window.navigator.msSaveOrOpenBlob(csvData,'14 知识产权报表.xlsx');		   
	            			}
	            			//google或者火狐浏览器
	            			else{
	            				var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
	    						var objectUrl = URL.createObjectURL(blob);
	    						var aForExcel = $("<a download='14 知识产权报表.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href",objectUrl);
	    						$("body").append(aForExcel);
	    						$(".forExcel").click();
	    						aForExcel.remove();
	            			}
	            			// 关闭遮罩层
	 						inform.closeLayer();
	 						inform.common("下载成功!");
	            		 });
					
	    	   
	       });
		
	         };
	 		/**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */	
		
	}]);
})();

  /*
* @Author: fubaole
* @Date:   2017-09-05 10:05:00
* @Last Modified by:   fubaole
* @Last Modified time: 2018-02-24 13:45:13
*/
(function() {
    'use strict';
  app.factory('stafftransferService', stafftransferService);
  stafftransferService.$inject=["HttpService",'$rootScope'];

  function stafftransferService(HttpService,$rootScope){
    var service={
      getAllInfos:getAllInfos,
      staffTransfer:staffTransfer
    };
    return service;
    
    /**
     * 获取所有的信息
     * urlData = {
     *'employeeName' : $scope.formInsert.name, // 员工姓名
     *'currentPage' : $scope.pages.pageNum, // 分页页数
     *'pageSize' : $scope.pages.size    //分页每页显示数量
     *};
     */
  function getAllInfos(urlData) {        
    return HttpService.post($rootScope.getWaySystemApi+'staffManage/selectByName',urlData);
  }
  
  /**
   * 根据关系ID调出人员  设置为调出状态
    *  urlData = {
	*	'employeeNo':$scope.formInsert.code,   			//员工编号
	*	'employeeName':$scope.formInsert.name,  		//员工姓名
	*	'id':'',                  						//关系id 置为空  
	*	'updateEmployeename':$scope.updateEmployeename  //更新者
	*	};
   */
   function staffTransfer(urlData) {
    
    return HttpService.post($rootScope.getWaySystemApi+'staffManage/staffTransfer',urlData);
  }
   
  }
})();

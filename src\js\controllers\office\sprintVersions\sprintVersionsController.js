//# sourceURL=js/controllers/office/sprintVersions/sprintVersionsController.js
(function () {
    app.controller("sprintVersionsController", ['$rootScope', '$scope','$state','$stateParams', 'sprintVersionsService','inform', 'Trans', 'AgreeConstant', 'comService', 'LocalCache',
        function ( $rootScope, $scope,$state,$stateParams,sprintVersionsService, inform, Trans, AgreeConstant,comService,LocalCache) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //获取缓存
            $scope.formRefer = LocalCache.getObject('sprintVersions_formRefer');
            //获取项目信息
            $scope.loaclParam = LocalCache.getObject('project_detail');
            if($scope.loaclParam.projectInfoParam){
                $scope.projectInfoParam = JSON.parse($scope.loaclParam.projectInfoParam);
            }
            $scope.type = $stateParams.type;
            // 获取登录用户的员工id
            $scope.employeeId = LocalCache.getSession('employeeId');
            //对原缓存进行覆盖
            LocalCache.setObject("sprintVersions_formRefer",{});
            $scope.limitList = AgreeConstant.limitList;
            // 页面分页信息
            $scope.pages = {
                pageNum: '',	// 分页页数
                size: '',		// 分页每页大小
                total: ''		// 数据总数
            };
            //产品线配置按钮的权限
            $scope.config=false;
            //保存按钮权限
            $scope.save=false;
            init();
            $scope.time={};
            // 分页
        	$scope.pages = inform.initPages(); // 初始化分页数据
        	$scope.number = $scope.pages.pageNum;
    		$scope.getData = getData;
    		$scope.setDivHeight = setDivHeight;
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);


            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            
          //用于保存dom元素
            var relatedTarget;
            /*
            * 模态框初始化时，把触发此模态框的input保存下来，并给模态框中的textarea赋值
            * */
            $('#edit_Module').on('show.bs.modal', function (e) {
               relatedTarget = e.relatedTarget;
               $("#moduleTextarea").val(relatedTarget.value);
            })
            /*
            * 模态框渲染完成时，自动获取焦点
            * */
            $('#edit_Module').on('shown.bs.modal', function (e) {
               console.log($("#moduleTextarea"));
               $("#moduleTextarea").focus();
            })
            /*
            * 模态框消失时，回填input的值
            * */
            $('#edit_Module').on('hide.bs.modal', function (e) {
               relatedTarget.value = $("#moduleTextarea").val();
               $(relatedTarget).change();
            })
            
            $scope.changeFailUsed = function (proProjectId) {
            	var s='#failReason'+proProjectId;
            	if($("#result option:selected").val()==="0"){
            		$(s).attr("disabled",true);
            	}else{
            		$(s).attr("disabled",false);
            	}
            }

            /**
             * 获取保存按钮权限
             */
            function getSaveButtonPermission() {
                $scope.itemList.filter(function (item) {
                    return item.proManagerId === $scope.employeeId;
                }).forEach(function (item) {
                    item.save = true;
                    $scope.save = true;
                });
            }

            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 65);
            }

            //重置
            $scope.rest = function () {
                $scope.formRefer = {
                    'teamName': '',// 冲刺版本
                    'proManager': '',// 项目经理
                    'proLineCode': '',// 产品线
                    'result': '',// 冲刺结果
                };
            };

          function initTime() {
                    //设置默认时间
                    if ($scope.formRefer.endTime==null){
                        var now = new Date();
                        var endDate = inform.format(now, 'yyyy-MM-dd');
                        var startDate = inform.format(new Date(now.getFullYear(),0,1),"yyyy-MM-dd");
                        //默认开始时间
                        $scope.formRefer.endTime = endDate;
                        $scope.formRefer.startTime = startDate;
                    }
                }
            /**
    		 * 初始化
    		 */
        	function init() {
        		//结果-用于条件选择
        		$scope.resultMap = [{
                    value: '0',
                    label: '成功'
                }, {
                    value: '1',
                    label: '失败'
                },
                {
                    value: '2',
                    label: '待定'
                }];
        		//结果-用于查询结果
        		$scope.isLinkProjectMap = [{
                    value: '0',
                    label: '成功'
                }, {
                    value: '1',
                    label: '失败'
                }];
        		//获取失败原因
        		$scope.failReasonList = [];
        		comService.queryEffectiveParam('sprintVersionsFail','sprintVersionsFail').then(function(data) {
            		if (data.data) {
            			$scope.failReasonList =  data.data;
            		}
                });
        		//获取产品线
                $scope.productLines=[];
                comService.queryEffectiveParam('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
                    $scope.productLines = angular.fromJson(data.data);
                });
                initTime();
                getData();
            }
        	
        	//配置Product Owner
        	$scope.updateConfig = function () {
        		//获取员工信息
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function(data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                    }
                });
                var urlData = {
                        'currentUserName':""
                    };
                //获取参数表中的产品线及Product Owner的对应数据
                sprintVersionsService.getLineManager(urlData).then(function(data) {
                	if (data.code===AgreeConstant.code) {
						// 项目详情
						$scope.lineManagerList = data.data;
					} else {
						inform.common(data.message);
					}
				},
				function(error) {
					inform.common(Trans("tip.requestError"));
				});
        	}
        	

            //获取所有数据以分页的形式
            function getData(pageNum) {
            	$scope.save=false;
            	var urlData ={
        				'teamName':$scope.formRefer.teamName,// 冲刺版本
        				'proManager':$scope.formRefer.proManager,// 项目经理
        				'proLineCode':$scope.formRefer.proLineCode,// 产品线
        				'result':$scope.formRefer.sprintResult,// 冲刺结果
        				'startTime':$scope.formRefer.startTime,// 开始时间
        				'endTime':$scope.formRefer.endTime,// 结束时间
        	            'currentPage':pageNum,// 当前页数
        		        'pageSize':$scope.pages.size,// 每页显示条数
        		        'projectId':$scope.projectId
        			};
            	if($scope.projectInfoParam && $scope.type!=='menu'){
                    urlData.teamName = $scope.projectInfoParam.cname;
            	}
            	sprintVersionsService.getSprintVersionInfo(urlData).then(function(data) {
        					if (data.code===AgreeConstant.code) {
        						// 项目详情
        						$scope.itemList = data.data.list;
        						if ($scope.itemList.length===0) {
        		                    inform.common(Trans("tip.noData"));
        	    	                $scope.pages = inform.initPages();
        		                } else {
        		                    if($scope.projectInfoParam && $scope.type!=='menu'){
        		                        $scope.successNum = 0;
        		                        $scope.failureNum = 0;
        		                        angular.forEach($scope.itemList, function (res, index) {
                                            if(res.resultNote==='成功'){
                                                $scope.successNum = $scope.successNum+1;
                                            }
                                            if(res.resultNote==='失败'){
                                                $scope.failureNum = $scope.failureNum+1;
                                            }
        		                        });
                                        //冲刺成功率
                                        if($scope.itemList.length===0){
                                            $scope.successRate=0;
                                        }else if($scope.successNum === 0 && $scope.failureNum === 0){
                                            $scope.successRate = 0;
                                        }else{
                                            $scope.successRate = ($scope.successNum/($scope.failureNum+$scope.successNum)*100).toFixed(2);
                                        }
        		                    }
        		                    // 分页信息设置
        		                    $scope.pages.total = data.data.total;
        		                    $scope.pages.star = data.data.startRow;
        		                    $scope.pages.end = data.data.endRow;
        		                    $scope.pages.pageNum = data.data.pageNum;
                                    //判断保存按钮是否具有权限
                                    getSaveButtonPermission();
        		                }
        					} else {
        						inform.common(data.message);
        					}
        				},
        				function(error) {
        					inform.common(Trans("tip.requestError"));
        				});
            }

            //批量更新冲刺结果
            $scope.adviceSynchronize = function () {
            	//判断结果为失败的，冲刺失败原因是否为空
            	for(var i=0;i<$scope.itemList.length;i++){
            		//冲刺结果为失败，但是冲刺原因为空
            		if($scope.itemList[i].result==='1'){//冲刺结果为失败
            			if($scope.itemList[i].failReason===null){//没有失败原因
            				layer.confirm("ID为"+$scope.itemList[i].proProjectId+"的项目冲刺失败原因为空，请选择后再保存！",{
                                btn:['确定']
                            },function(result){
                                layer.close(result);
                            });
                			return;
            			}
            		}
            	}
            	
            	//保存
            	sprintVersionsService.updateSprintVersionInfo($scope.itemList).then(function (data) {
                    layer.confirm(data.message,{
                        title:false,
                        btn:['确定']
                    },function(result){
                        layer.close(result);
                        getData(1);
                    });
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            };
            
          //批量更新Product Owner配置信息
            $scope.saveManagerConfig = function () {
            	sprintVersionsService.saveManagerConfig($scope.lineManagerList).then(function (data) {
                    layer.confirm(data.message,{
                        title:false,
                        btn:['确定']
                    },function(result){
                        layer.close(result);
                        $("#config_modal").modal("hide");
                        
                    });
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            };
            
            /**
             * 获取其中一条记录的总工时的详细信息
             */
            $scope.viewBugTimeDetails = function(m,flag) {
            	LocalCache.setObject('sprintVersions_formRefer', $scope.formRefer);
            	LocalCache.setObject('projectHours_formRefer', {});
            	$state.go('app.office.projectHoursDetails', {
                	id: m,		// 禅道项目id
                	flag:flag, //是否是禅道项目
                	isSprintVersion:0 //是否是冲刺版本中查看0是，1否
                });
            }
            
            /**
             * 获取其中一条记录的任务的详细信息
             */
            $scope.viewTaskRateDetails = function(m) {
            	LocalCache.setObject('sprintVersions_formRefer', $scope.formRefer);
            	$state.go('app.office.projectTaskDetails', {
                	id: m,		// 禅道项目id
                	isSprintVersion:0
                });
            }
            
             /**
             * 获取其中一条记录的bug的详细信息
             */
            $scope.viewBugNumDetails = function(m) {
            	LocalCache.setObject('sprintVersions_formRefer', $scope.formRefer);
            	$state.go('app.office.projectBugDetails', {
                	id: m,		// 禅道项目id
                	isSprintVersion:0
                });
            }
            /**
             * 开始时间
             */
            $scope.openDateStart = function () {
                $scope.time.openedStart = true; //开始时间
            };
            /**
             * 结束时间
             */
            $scope.openDateEnd = function () {
                $scope.time.openedEnd = true; //结束时间
            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
/*
 * @Author: dongyinggang
 * @Date:   2019-06-13 17:50:05
 * @Last Modified by:   dongyinggang
 * @Last Modified time:  2019-06-13 17:50:05
 */
(function () {
    'use strict';
    app.factory('blackEventService', blackEventService);
    blackEventService.$inject = ["HttpService", '$rootScope'];

    function blackEventService(HttpService, $rootScope) {

        var service = {
            getBlackEventList: getBlackEventList
        };
        return service;

        /**
         * 分页查询合理化建议
         */
        function getBlackEventList(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'blackEvent/getBlackEventList', urlData);
        }
    }
})();
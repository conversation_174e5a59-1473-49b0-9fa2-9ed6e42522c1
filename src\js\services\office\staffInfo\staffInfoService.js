(function() {
    'use strict';
  app.factory('staffInfoService', staffInfoService);
  staffInfoService.$inject=["HttpService",'$rootScope'];

  function staffInfoService(HttpService,$rootScope){
    
    var service={
        seleteByParam:seleteByParam,
        addByParam:addByParam,
        updateByParam:updateByParam,
        deleteByIds:deleteByIds,
        validUserName:validUserName,
        getPicture:getPicture,
        updatePicByParam:updatePicByParam,
        delPicByParam:delPicByParam,
        getPersonInfo:getPersonInfo,
        selectGroupById:selectGroupById

    };
    return service;
    /**
     * 员工基本信息
     */
    function seleteByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffInfo/seleteByParam', urlData);
    }

    function addByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffInfo/insertByParam', urlData);
    }
  

    function updateByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffInfo/updateByParam', urlData);
    }

    function deleteByIds(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffInfo/deleteByParam', urlData);
    }

    function validUserName(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffInfo/validUserName', urlData);
    }

    //根据id获取员工个人信息
    function getPersonInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'staffInfo/selectPersonInfo', urlData);
    }

    //根据员工编号查询员工照片
    function getPicture(employeeId) {
        return HttpService.get($rootScope.getWaySystemApi+'picture/selectPicture',
    				{'employeeId':employeeId});
    }

    //根据id更新员工照片
    function updatePicByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'picture/updatePicture', urlData);
    }

    //根据id删除员工照片
    function delPicByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'picture/delPicture', urlData);
    }
    //查询小组信息列表
    function selectGroupById() {
        return HttpService.get($rootScope.getWaySystemApi + 'team/selectGroupList');
    }



  }
})();
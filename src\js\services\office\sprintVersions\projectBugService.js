/*
 * @Author: lijing
 * @Date:   2020-10-16 16:37:05
 * @Last Modified by:   lijng
 * @Last Modified time: 2020-10-16 15:20:05
 */
(function() {
    'use strict';
  app.factory('projectBugService', specPassService);
  specPassService.$inject=["HttpService",'$rootScope'];

  function specPassService(HttpService,$rootScope){
    
	var service={
			getBugInfoByProId:getBugInfoByProId
	};
    return service;
    
    /**
     * 获取指定项目的任务信息
     */
    function getBugInfoByProId(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'sprintVersions/getBugInfoByProId', urlData);
    }

  }
})();


(function() {
    'use strict';
  app.factory('companyProjectManagementService', companyProjectManagementService);
  companyProjectManagementService.$inject=["HttpService",'$rootScope'];

  function companyProjectManagementService(HttpService,$rootScope){
    
    var service={
        getCompanyProjectInfoList:getCompanyProjectInfoList,
        addCompanyProjectInfo:addCompanyProjectInfo,
        updateCompanyProjectInfo:updateCompanyProjectInfo,
        getProProjectInfoList:getProProjectInfoList,
        getProProjectRelation:getProProjectRelation,
        getFinishRatePro:getFinishRatePro
    };
    return service;

    /**
     * 分页查询公司立项项目信息
     */
    function getCompanyProjectInfoList(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'companyProjectmanagement/getCompanyProjectInfoList', urlData);
    }
    /**
     * 新增项目信息
     */
    function addCompanyProjectInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'companyProjectmanagement/addCompanyProjectInfo', urlData);
    }
    /**
     * 修改项目信息
     */
    function updateCompanyProjectInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'companyProjectmanagement/updateCompanyProjectInfo', urlData);
    }
    /**
     * 根据所选禅道项目查看是否有关联的公司立项项目
     */
    function getProProjectRelation(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'companyProjectmanagement/getProProjectRelation', urlData);
    }
    /**
     * 查询已关联的禅道项目清单
     */
    function getProProjectInfoList(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'companyProjectmanagement/getProProjectInfoList', urlData);
    }

    /**
     * 查询活动完成率相关项目清单
     */
    function getFinishRatePro(){
        return HttpService.post($rootScope.getWaySystemApi + 'companyProjectmanagement/getFinishRatePro');
    }
  }
})();

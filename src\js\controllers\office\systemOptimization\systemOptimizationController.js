(function () {
    app.controller("systemOptimizationController", ['comService', '$rootScope', '$scope', '$state', '$stateParams', '$modal', 'systemOptimizationService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, $state, $stateParams, $modal, systemOptimizationService, inform, Trans, AgreeConstant, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */

            //是否从我的星星跳转标志
            $scope.formRefer = {};
            $scope.formRefer.flag = '0';
            //获取缓存
            $scope.formRefer = LocalCache.getObject('systemOptimizationController_formRefer');
            if($scope.formRefer.flag === '1'){
                $scope.formRefer.startTime = $scope.formRefer.startDate;
                $scope.formRefer.endTime = $scope.formRefer.endDate;
                $scope.formRefer.responsiblePerson = $scope.formRefer.employeeName;
                console.log($scope.formRefer);
            }
            //清除缓存
            LocalCache.setObject('systemOptimizationController_formRefer', {});
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            $scope.processDomainFlag = false;
            //页面分页信息
            $scope.pages = inform.initPages();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            $scope.quarterShow = {
                "1":"第一季度",
                "2":"第二季度",
                "3":"第三季度",
                "4":"第四季度"
            };
            //初始化页面信息
            initPages();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $("#divTBDisDetail").height(divHeight);
                $("#subDivTBDisDetail").height(divHeight - 35);
            }

            //重置查询条件
            $scope.reset = function () {
                $scope.formRefer = {};
                $scope.processDomainFlag = false;
            };

            function initPages() {
                var flag = 0;
                //初始化完成时间
                var year = new Date().getFullYear();  //当前年份
                $scope.formRefer.startTime = year + "-01-01";
                //获取类别
                $scope.categoriesList = [];
                $scope.categoriesMap = {};
                comService.getParamList('SYSOptimization', 'categories').then(function (data) {
                    if (data.data) {
                        $scope.categoriesList = data.data;
                        angular.forEach($scope.categoriesList, function (res, index) {
                            $scope.categoriesMap[res.param_code] = res.param_value;
                        });
                        flag++;
                        iniGetData(flag);
                    }
                });

                //获取文件类型
                $scope.fileTypeList = [];
                $scope.fileTypeMap = {};
                comService.getParamList('SYSOptimization', 'fileType').then(function (data) {
                    if (data.data) {
                        $scope.fileTypeList = data.data;
                        angular.forEach($scope.fileTypeList, function (res) {
                            $scope.fileTypeMap[res.param_code] = res.param_value;
                        });
                        flag++;
                        iniGetData(flag);
                    }
                });

                //获取过程域
                $scope.processDomainList = [];
                $scope.processDomainMap = {};
                comService.getParamList('SYSOptimization', 'processDomain').then(function (data) {
                    if (data.data) {
                        $scope.processDomainList = data.data;
                        angular.forEach($scope.processDomainList, function (res) {
                            $scope.processDomainMap[res.param_code] = res.param_value;
                        });
                        flag++;
                        iniGetData(flag);
                    }
                });
            }

            /**
             * 判定初始化结束后，调用查询方法
             * @param flag
             */
            function iniGetData(flag){
                if(flag < 3){
                    return;
                }
                $scope.getData();
            }

            /**
             * 获取适用范围获取系研下的二级部门
             */
            $scope.getXYDepartment = function(){
                if($scope.formRefer.adaptedRange==='二级部门'){
                    //获取二级部门
                    $scope.departmentList = [];
                    comService.getOrgChildren('D010053').then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.departmentList = data.data;
                        }
                    });
                }else{
                    $scope.departmentList = [];
                }
            }

            //获取组织培训管理数据的分页信息
            $scope.getData = function (indexNum) {
                var urlData = {
                    'fileName': $scope.formRefer.fileName,
                    'fileType': $scope.formRefer.fileType,
                    'responsiblePerson': $scope.formRefer.responsiblePerson,
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                    'processDomain': $scope.formRefer.processDomainCode,
                    'categories': $scope.formRefer.categories,
                    'adaptedRange': $scope.formRefer.adaptedRange,
                    'department': $scope.formRefer.department,
                    'currentPage': indexNum,
                    'pageSize': $scope.pages.size,
                };
                systemOptimizationService.getInfoWithPage(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.projectData = data.data.list;
                            if ($scope.projectData.length === 0) {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages(); //初始化分页数据
                            }else {
                                // 分页信息设置
                                $scope.pages.total =  data.data.total; 			// 页面数据总数
                                $scope.pages.star =  data.data.startRow; 		// 页面起始数
                                $scope.pages.end =  data.data.endRow;	 		// 页面结束数
                                $scope.pages.pageNum =  data.data.pageNum;       //页号
                            }
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            };

            //选择过程域
            $scope.selectProcessDomain = function (str,code){
                //文本框显示过程域名称
                $scope.formRefer.processDomain = str;
                //向后端传过程域code
                $scope.formRefer.processDomainCode = code;
                document.getElementById("add_info_close_button").click();
            }

            //页面跳转,str存在则为修改,否则为新增
            $scope.popModal = function (id) {
                LocalCache.setObject("systemOptimizationController_formRefer",$scope.formRefer);
                if (id) {
                    $state.go("app.office.systemOptimizationAddOrUpdateController", {item:id});
                }else{
                    $state.go("app.office.systemOptimizationAddOrUpdateController");
                }
            };

            /**
             * 类别值改变时，显示/隐藏过程域
             */
            $scope.categoriesChange = function(){
                //判断类别的值，决定过程域是否显示
                if($scope.formRefer.categories === '01'){
                    $scope.processDomainFlag = true;

                }else{
                    $scope.processDomainFlag = false;
                }
            };

            // 删除弹框
            $scope.open = function (item) {
                inform.modalInstance(Trans("common.deleteTip")).result.then(function () {
                    var urlData = {
                        'id': item.id
                    };
                    systemOptimizationService.deleteInfo(urlData) .then(function (data) {
                            if (data.code === "0000") {
                                inform.common(Trans("tip.delSuccess"));
                                $scope.getData(AgreeConstant.pageNum);
                            } else {
                                inform.common(data.message);
                            }
                        }, function (error) {
                            inform.common(Trans("tip.requestError"));
                        });
                });
            };

            //开始时间
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;    //开始时间
                $scope.openedEnd = false;
            };

            //结束时间
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;    //结束时间
            };
            /**
    		 * excel下载
    		 */
    		$scope.toExcel = function() {
    			var modalInstance = $modal.open({
    				templateUrl: 'myModalContent.html',
    				controller: 'ModalInstanceCtrl',
    				size: "sm",
    				resolve: {
    					items: function() {
    						return "确定要下载吗！";
    					}
    				}
    			});
    			modalInstance.result.then(function() {
    				//拼装下载内容
    				var urlData = {
                        'fileName': $scope.formRefer.fileName,
                        'fileType': $scope.formRefer.fileType,
                        'responsiblePerson': $scope.formRefer.responsiblePerson,
                        'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                        'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                        'processDomain': $scope.formRefer.processDomainCode,
                        'categories': $scope.formRefer.categories,
                        'adaptedRange': $scope.formRefer.adaptedRange,
                        'department': $scope.formRefer.department,
                    };
    				inform.downLoadFile ('systemOptimization/downloadExcel',urlData,'体系优化记录报表.xlsx');
    			});
    		};
            //页面跳转到我的星星--我的星星明细部门贡献详情页
            $scope.getBack = function(){
                $state.go('app.personal_star_department_detail');
            }
            /**
             * *************************************************************
             *              方法调用部分                                 结束
             * *************************************************************
             */
        }]);
})();
(function() {
	'use strict';
	app
	.factory('LoginService', loginService);
	loginService.$inject=['HttpService','$rootScope'];
	function loginService(HttpService,$rootScope){
		var service={
			getUserTokenByUserInfo:getUserTokenByUserInfo,
			
		};
		return service;

		function getUserTokenByUserInfo(username,pwd,rememberMe){
			//原版校验
			// return HttpService.postLogin($rootScope.gateInfoApi+'login/verify?username='+username+'&password='+pwd+'&grant_type=password',{});
		
		   var urlData ={
		   	'username':username,
		   	'password':pwd
		   };
           return HttpService.post($rootScope.getWaySystemApi+'loginAction/loginCheck',urlData);
		}

		
       
	}
})();
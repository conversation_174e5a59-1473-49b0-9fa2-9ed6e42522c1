(function () {
    app.controller('lowQualityManagementController', [
        'lowQualityService',
        'trackingSheetService',
        '$state',
        'comService',
        '$rootScope',
        '$scope',
        'inform',
        'Trans',
        'AgreeConstant',
        'LocalCache',
        '$stateParams',
        '$modal',
        '$http',
        '$state',
        function (
            lowQualityService,
            trackingSheetService,
            state,
            comService,
            $rootScope,
            $scope,
            inform,
            Trans,
            AgreeConstant,
            LocalCache,
            $stateParams,
            $modal,
            $http,
            $state
        ) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //项目状态下拉框数据源
            $scope.statusMap = {
                0: '待审核',
                1: '审核通过',
                2: '审核拒绝',
            };
            //状态查询条件
            $scope.status = [
                {
                    value: '0',
                    label: '待审核',
                },
                {
                    value: '1',
                    label: '审核通过',
                },
                {
                    value: '2',
                    label: '审核拒绝',
                },
            ];
            //获取缓存
            $scope.formRefer = LocalCache.getObject('lowQualityController_formRefer');
            //设置默认时间
            if ($scope.formRefer.startTime == null) {
                var day_360 = new Date().setMonth(new Date().getMonth() - 12);
                $scope.formRefer.startTime = inform.format(new Date(day_360), 'yyyy-MM-dd');
            }
            //清除缓存
            LocalCache.setObject('lowQualityController_formRefer', {});
            $scope.flag = $stateParams.flag;
            $scope.createUser = LocalCache.getSession('currentUserName');

            //修改一级部门，二级部门进行联动
            $scope.changeDept = function () {
                //获取二级部门
                $scope.deptList = [];
                comService.getOrgChildren($scope.formRefer.primaryDept).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.deptList = data.data;
                    }
                });
            };
            //一级部门存在时 重新加载二级部门
            if ($scope.formRefer.primaryDept !== undefined) {
                $scope.changeDept();
            }
            $scope.oprateFlag = false;
            //初始化
            initPages();
            //初始化当前时间
            $scope.datepicker = {
                currentDate: new Date(),
            };
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //页面分页信息
            $scope.pages = {
                pageNum: '', //分页页数
                size: '100', //分页每页大小
                total: '', //数据总数
            };
            //绑定文件控件改变事件
            $('#filesImg1').change(submitForm);
            $('#filesImg1').change(fileChangeEvent);
            $scope.add = false;
            $scope.up = false;
            //判断按钮是否具有权限
            getButtonPermission();
            //获取数据
            $scope.getData = getData;
            //初始化页面信息

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $('#divTBDis').height(divHeight);
                $('#subDivTBDis').height(divHeight - 65);
                if ($scope.flag === 'verify') {
                    $('#subDivTBDis').height(divHeight + 8);
                }
            }
            //重置按钮
            $scope.reset = function () {
                $scope.formRefer = {};
                var day_360 = new Date().setMonth(new Date().getMonth() - 12);
                $scope.formRefer.startTime = inform.format(new Date(day_360), 'yyyy-MM-dd');
                $('#form')[0].reset();
                $('#fileNameDis').text('');
                $scope.deptList = [];
            };

            /**
             * 获取按钮权限
             */
            function getButtonPermission() {
                var buttons = {
                    'Button-LowQualityManagement-add': 'add',
                    'Button-LowQualityManagement-up': 'up',
                };
                var urlData = {
                    userId: LocalCache.getSession('userId'),
                    parentPermission: 'ButtonLowQualityManagement',
                    buttons: buttons,
                };
                comService.getButtonPermission(urlData, $scope);
            }

            /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function (data) {
                    if (data.data) {
                        $scope.primaryDeptList = data.data;
                    }
                });
            }

            /**
             * 页面初始化
             */
            function initPages() {
                initPrimaryDeptList();
                //获取山东新北洋集团的下级部门信息
                $scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function (data) {
                    $scope.departmentList = comService.getDepartment(data.data);
                });
                //进行权限校验
                getRole();
                getDepartmentCode();
                //获取员工信息
                $scope.employeesList = [];
                comService.getEmployeesByOrgId('').then(function (data) {
                    $scope.employeeList = data.data;
                });
                //低级错误类型
                $scope.lowQualityTypeList = [];
                comService.getParamList('LOW_QUALITY_TYPE', 'LOW_QUALITY_TYPE').then(function (data) {
                    $scope.lowQualityTypeList = data.data;
                });
                //低级错误等级
                $scope.lowQualityGradeList = [];
                comService.getParamList('LOW_QUALITY_GRADE', 'LOW_QUALITY_GRADE').then(function (data) {
                    $scope.lowQualityGradeList = data.data;
                });
            }

            //获取登录人所在部门列表
            function getDepartmentCode() {
                comService.validDeptAuthority(LocalCache.getSession('loginName')).then(function (result) {
                    if (result.code === '0000') {
                        if (result.data.code === '01' || result.data.code === '02') {
                            getData(1);
                            return;
                        }
                        //设置有权限的部门列表
                        $scope.haveAuthorityDepartmentList = result.data.deptList;
                        if ($scope.flag === 'verify') {
                            $scope.formRefer.department = $scope.haveAuthorityDepartmentList[0].orgCode;
                        }
                        getData(1);
                    }
                });
            }

            //查询当前用户的角色
            function getRole() {
                comService.getRolePermission('0013').then(function (result) {
                    if (result.code === '0000') {
                        if (result.data.code === '01') {
                            $scope.oprateFlag = true;
                        }
                    }
                });
            }
            /**
             * 根据创建人权限修改修改操作颜色
             */
            $scope.judeUp = function (item) {
                if (typeof $scope.createUser === 'undefined' || $scope.createUser === item) {
                    return 'green';
                }
                return 'gray';
            };
            /**
             * 根据创建人权限修改删除操作颜色
             */
            $scope.judeDel = function (item) {
                if (typeof $scope.createUser === 'undefined' || $scope.createUser === item) {
                    return 'red';
                }
                return 'gray';
            };

            $scope.judeVerify = function (item) {
                var color = 'gray';
                if (typeof $scope.haveAuthorityDepartmentList === 'undefined') {
                    color = 'green';
                }
                angular.forEach($scope.haveAuthorityDepartmentList, function (dept, index) {
                    if (dept.orgCode === item) {
                        color = 'green';
                    }
                });
                return color;
            };
            /**
             * 根据创建人权限判断操作是否可用
             */
            $scope.createUserFlag = function (item) {
                if (typeof $scope.createUser === 'undefined' || $scope.createUser === item) {
                    return false;
                }
                return true;
            };
            /**
             * 获取低级质量问题
             *
             */
            function getData(pages) {
                var urlData = {
                    primaryDept: $scope.formRefer.primaryDept,
                    department: $scope.formRefer.department,
                    personLiable: $scope.formRefer.personLiable, //责任人员名称
                    typeCode: $scope.formRefer.type, //低级质量问题类别
                    gradeCode: $scope.formRefer.grade, //低级质量问题等级
                    createUser: $scope.formRefer.createUser, //创建人
                    status: $scope.flag === 'verify' ? '0' : $scope.formRefer.status, //审核状态
                    startTime: inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    endTime: inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),
                    page: pages,
                    size: $scope.pages.size,
                };
                lowQualityService.getLowQualityInfo(urlData).then(
                    function (data) {
                        if (data.code === '0000') {
                            if (null == data.data) {
                                $scope.lowQualityData = {};
                                inform.common(Trans('tip.noData'));
                                $scope.pages = inform.initPages(); //初始化分页数据
                            } else {
                                //低级质量问题
                                $scope.lowQualityData = data.data.list;
                                // 分页信息设置
                                $scope.pages.total = data.data.total;
                                $scope.pages.star = data.data.startRow;
                                $scope.pages.end = data.data.endRow;
                                $scope.pages.pageNum = data.data.pageNum;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            /**新增数据
             **/
            $scope.addItem = function () {
                LocalCache.setObject('lowQualityController_formRefer', $scope.formRefer);
                $state.go('app.office.lowQualityAdd', null);
            };
            /**
             * 跳转修改
             */
            $scope.go = function (m) {
                const roleInfo = JSON.parse(LocalCache.getSession('roleList'));
                // 是否有超级管理员权限
                const hasSuperAdminPermission = Object.keys(roleInfo).some((key) => roleInfo[key].roleCode === 'SUPER');
                if (
                    typeof $scope.createUser !== 'undefined' &&
                    $scope.createUser !== m.createUser &&
                    !hasSuperAdminPermission
                ) {
                    inform.common('只能修改本人创建的数据');
                    return;
                }
                LocalCache.setObject('lowQualityController_formRefer', $scope.formRefer);
                $state.go('app.office.lowQualityUpdate', {
                    item: m.id,
                });
            };
            /**
             * 删除弹框
             *
             */
            $scope.open = function (m) {
                if (typeof $scope.createUser !== 'undefined' && $scope.createUser !== m.createUser) {
                    inform.common('只能删除本人创建的数据');
                    return;
                }
                inform.modalInstance('确定要删除吗?').result.then(function () {
                    $scope.deleteLowQualityInfo(m);
                });
            };
            /**
             * 跳转审核
             */
            $scope.verify = function (m) {
                var flag = false;
                if (typeof $scope.haveAuthorityDepartmentList === 'undefined') {
                    flag = true;
                    LocalCache.setObject('lowQualityController_formRefer', $scope.formRefer);
                    $state.go('app.office.lowQualityUpdate', {
                        item: m.id,
                        flag: 'verify',
                    });
                }
                angular.forEach($scope.haveAuthorityDepartmentList, function (dept, index) {
                    if (dept.orgCode === m.department) {
                        flag = true;
                        LocalCache.setObject('lowQualityController_formRefer', $scope.formRefer);
                        $state.go('app.office.lowQualityUpdate', {
                            item: m.id,
                            flag: 'verify',
                        });
                    }
                });
                if (!flag) {
                    inform.common('只能审核本部门的数据');
                }
            };
            /**
             * 删除数据
             *
             */
            $scope.deleteLowQualityInfo = function (item) {
                lowQualityService.deleteLowQualityInfo(item).then(
                    function (data) {
                        if (data.code === '0000') {
                            inform.common(Trans('tip.delSuccess'));
                            $scope.getData(AgreeConstant.pageNum);
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            /**
             * excel下载
             */
            $scope.toExcel = function () {
                inform.modalInstance('确定要下载吗?').result.then(function () {
                    //拼装下载内容
                    var params = $scope.formRefer;
                    inform.downLoadFile('LowQualityProblem/toExcel', params, '低级质量问题.xlsx');
                });
            };

            //下载员工基本信息模板
            $scope.toTemplateExcel = function () {
                inform.modalInstance('\u3000\u3000确定下载低级质量模板吗？').result.then(function () {
                    var params = {};
                    inform.downLoadFile('LowQualityProblem/toTemplateExcel', params, '低级质量问题模板.xlsx');
                });
            };
            //文件选择事件
            $scope.selectFile = function () {
                document.getElementById('filesImg1').click();
            };
            /**
             * 选择上传文件后事件
             */
            function fileChangeEvent(e) {
                var fileName = '文件名称：' + e.currentTarget.files[0].name;
                $('#fileNameDis').text(fileName);
            }

            //上传文件
            function submitForm() {
                var formData = new FormData(document.getElementById('form')); //表单id  初始化表单值
                var file = document.querySelector('input[type=file]').files[0]; //获取文档中有类型为file的第一个input元素
                if (!file) {
                    inform.common('请先选择文件!');
                    $('#form')[0].reset();
                    $('#fileNameDis').text('');
                    return false;
                }
                formData.append('fileName', file);
                if (file.size > AgreeConstant.fileSize) {
                    inform.common('上传文件大小不能超过2M');
                    $('#form')[0].reset();
                    $('#fileNameDis').text('');
                    return false;
                }
                var a = file.type;
                if (a !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
                    inform.common('请选择.xlsx类型的文档进行上传!');
                    $('#form')[0].reset();
                    $('#fileNameDis').text('');
                    return false;
                } else {
                    inform.modalInstance('确定要上传文件吗？').result.then(function () {
                        inform.uploadFile('LowQualityProblem/uploadExcel', formData, function func(result) {
                            if (result.code === AgreeConstant.code) {
                                // 关闭遮罩层
                                inform.closeLayer();
                                $modal.open({
                                    templateUrl: 'errorModel.html',
                                    controller: 'ModalInstanceCtrl',
                                    size: 'lg',
                                    resolve: {
                                        items: function () {
                                            return result.message;
                                        },
                                    },
                                });
                            } else {
                                // 关闭遮罩层
                                inform.closeLayer();
                                inform.common('上传失败！');
                            }
                            getData(1);
                            $('#form')[0].reset();
                            $('#fileNameDis').text('');
                        });
                    });
                }
            }

            $scope.getSheetReopenList = function (m) {
                //获取关联的测试单数据
                var param = {
                    batchNum: m.trackingSheetApprovalNum,
                };
                trackingSheetService.getRelatedData(param).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            //项目详情
                            $scope.sheetData = data.data.list;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        },
    ]);
})();

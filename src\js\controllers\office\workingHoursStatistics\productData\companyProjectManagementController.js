(function () {
  app.controller('companyProjectManagement', [
    'comService',
    '$rootScope',
    '$scope',
    'companyProjectManagementService',
    'projectManagementService',
    'inform',
    'Trans',
    'AgreeConstant',
    '$modal',
    '$state',
    '$stateParams',
    'LocalCache',
    '$timeout',
    function (
      comService,
      $rootScope,
      $scope,
      companyProjectManagementService,
      projectManagementService,
      inform,
      Trans,
      AgreeConstant,
      $modal,
      $state,
      $stateParams,
      LocalCache,
      $timeout
    ) {
      /**
       * *************************************************************
       *             初始化部分                                 开始
       * *************************************************************
       */
      //获取缓存
      $scope.formRefer = {};
      $scope.formRefer = LocalCache.getObject('companyProjectManagement_formRefer');
      //对原缓存进行覆盖
      LocalCache.setObject('companyProjectManagement_formRefer', {});
      // 正则校验配置
      $scope.limitList = AgreeConstant.limitList;
      //保存查询出的产品线信息
      $scope.projectData = [];
      //被选中项目的集合
      $scope.proSelected = [];
      //删除按钮的权限
      $scope.deleted = false;
      //页面分页信息
      $scope.pages = {
        pageNum: '', //分页页数
        size: '', //分页每页大小
        total: '', //数据总数
      };
      // 初始化分页数据
      $scope.pages = inform.initPages();
      $scope.hrFlagList = [
        { value: '0', label: '无人力投入' },
        { value: '1', label: '有人力投入' },
      ];
      $scope.formRefer.hrFlag = '1';
      //项目状态下拉框数
      $scope.projectStatusSelect = [
        {
          value: '0',
          label: '待启动',
        },
        {
          value: '1',
          label: '进行中',
        },
        {
          value: '2',
          label: '结项',
        },
        {
          value: '3',
          label: '暂停',
        },
        {
          value: '4',
          label: '终止',
        },
      ];
      $scope.isLinkMarketDecision = [
        {
          value: '1',
          label: '已关联',
        },
        {
          value: '0',
          label: '未关联',
        },
      ];
      //级别
      $scope.xmjbList = ['I级', 'II级', 'III级'];
      //行业
      $scope.xqsshySelect = ['金融', '物流', '新零售', '新兴', '通用'];
      //项目归属
      $scope.xmgsList = ['新北洋', '荣鑫', '数码', '正棋'];
      //产品用途
      $scope.projectPurposeSelect = ['技术规划', '市场规划', '销售支撑', '运营支撑'];
      //软件价值分类
      $scope.softwareValueClassificationSelect = ['软硬一体', '以软促硬', '硬件配套', '技术研发', '其他'];
      //立项来源
      $scope.projectApprovalSourceSelect = [
        { value: 0, label: '公司立项' },
        { value: 1, label: '内部立项' },
      ];
      //项目状态展示Map
      $scope.projectStatueMap = {
        0: '待启动',
        1: '进行中',
        2: '结项',
        3: '暂停',
        4: '终止',
      };
      //设置列表的高度
      setDivHeight();
      //窗体大小变化时重新计算高度
      $(window).resize(setDivHeight);
      //获取数据
      $scope.getData = getData;
      //初始化页面信息
      initPages();
      getData();
      // 监听侧边栏点击状态，改变之后，保证表格列对齐
      $scope.$watch('app.settings.asideFolded', function () {
        const property = Object.keys($('#fixedLeftAndTop'));
        if (property.length > 0) {
          $('#fixedLeftAndTop').DataTable().destroy();
          // 销毁之后丢到下一次脏检查里，drow方法等都没法实现保证列对齐
          $timeout(function () {
            showDataTable();
          });
        }
      });
      /**
       * *************************************************************
       *              初始化部分                                 结束
       * *************************************************************
       */

      /**
       * *************************************************************
       *              方法声明部分                                开始
       * *************************************************************
       */

      /**
       * 页面初始化
       */
      function initPages() {
        //获取产品线
        $scope.projectLine = [];
        comService.queryEffectiveParam('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
          if (data.data) {
            $scope.projectLine = data.data;
          }
        });
        //获取产品线-产品类别-产品名称集合
        $scope.productLineAndType = [];
        $scope.productLineAndTypeMap = {};
        comService.getParamList('PRODUCT_TYPE', '').then(function (data) {
          if (data.data) {
            $scope.productLineAndType = data.data;
            angular.forEach($scope.productLineAndType, function (item) {
              $scope.productLineAndTypeMap[item['param_code']] = item['param_value'];
            });
          }
        });
        //获取系研项目
        $scope.projectList = [];
        $scope.projectMap = {};
        comService.getProjectsName().then(function (data) {
          $scope.projectList = angular.fromJson(data.data);
          angular.forEach($scope.projectList, function (item) {
            $scope.projectMap[item['cname']] = item['cname'];
          });
        });

        $scope.formRefer.hrFlag = $scope.hrFlagList[1].value;
      }
      /**
       * 获取项目
       */
      function getData(pageNum) {
        //删除已加载冻结头部和部分列的HTML模板
        $scope.dataTableShow = 0;
        var urlData = {
          projectCode: $scope.formRefer.projectCode, //项目编号
          projectName: $scope.formRefer.projectName, //项目名称
          productLine: $scope.formRefer.productLine, //产品线
          hardwareModeNo: $scope.formRefer.hardwareModeNo, //型号
          customerName: $scope.formRefer.customerName, //客户名称
          xqsshy: $scope.formRefer.xqsshy, //行业
          projectPurpose: $scope.formRefer.projectPurpose, //产品用途
          softwareValueClassification: $scope.formRefer.softwareValueClassification, //软件价值分类
          startDate: inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'), //开始时间
          endDate: inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间
          marketDecisionName: $scope.formRefer.marketDecisionName, //立项决议函名称
          marketDecisionId: $scope.formRefer.marketDecisionId, //是否关联市场决议函，market_decision_id = 0 表示没有关联市场决议函，否则关联；
          projectStatus: $scope.formRefer.projectStatus, //状态
          isSyncProject: $scope.formRefer.isSyncProject, //立项来源
          page: pageNum,
          pageSize: $scope.pages.size,
          hrFlag: $scope.formRefer.hrFlag,
        };
        companyProjectManagementService.getCompanyProjectInfoList(urlData).then(
          function (data) {
            //重新加载冻结头部和部分列的HTML模板
            $scope.dataTableShow = 1;
            if (data.code === AgreeConstant.code) {
              //项目
              $scope.companyProjectData = data.data.list;
              if ($scope.companyProjectData.length === 0) {
                $scope.pages = inform.initPages(); //初始化分页数据
                inform.common(Trans('tip.noData'));
              } else {
                // 分页信息设置
                $scope.pages.total = data.data.total; // 页面数据总数
                $scope.pages.star = data.data.startRow; // 页面起始数
                $scope.pages.end = data.data.endRow; // 页面结束数
                $scope.pages.pageNum = data.data.pageNum; //页号
              }
              //调用DataTable组件冻结表头和左侧及右侧的列
              setTimeout(showDataTable, 300);
            } else {
              inform.common(data.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      /**
       *调用DataTable组件冻结表头和左侧及右侧的列
       */
      function showDataTable() {
        $('#fixedLeftAndTop').DataTable({
          //可被重新初始化
          retrieve: true,
          //自适应高度
          scrollY: 'calc(100vh - 350px)',
          scrollX: true,
          scrollCollapse: true,
          //控制每页显示
          paging: false,
          //冻结列（默认冻结左1）
          fixedColumns: {
            leftColumns: 3,
            rightColumns: 1,
          },
          //search框显示
          searching: false,
          //排序箭头
          ordering: false,
          //底部统计数据
          info: false,
        });

        // 获取到跟踪单信息之后滚动条跳至缓存中保存的scrollTop
        setTimeout(function () {
          if ($scope.formRefer.subDivTBDisScrollTop) {
            $('#fixedLeftAndTop').parent().animate({ scrollTop: $scope.formRefer.subDivTBDisScrollTop }, 10);
          }
        }, 500);
      }

      /**
       * 设置列表的高度
       */
      function setDivHeight() {
        //网页可见区域高度
        var clientHeight = document.body.clientHeight;
        var divHeight = clientHeight - (150 + 205);
        $('#divTBDis').height(divHeight);
        $('#subDivTBDis').height(divHeight - 85);
      }

      /**
       * 修改信息弹框，str存在，就是新增
       */
      $scope.popModal = function (item, str) {
        if (str === '0') {
          //新增
          $scope.projectId = null;
        } else {
          //修改
          $scope.projectId = item.projectId;
        }
        // 设置滚动条高度
        $scope.formRefer.subDivTBDisScrollTop = $('#fixedLeftAndTop').parent().scrollTop();
        LocalCache.setObject('companyProjectManagement_formRefer', $scope.formRefer);
        $state.go('app.office.companyProjectManagementUpdate', {
          projectId: $scope.projectId,
          isAdd: str,
        });
      };

      //导出公司立项项目信息
      $scope.toExcel = function () {
        inform.modalInstance('确定要下载公司立项项目信息表吗？').result.then(function () {
          var params = {
            projectCode: $scope.formRefer.projectCode, //项目编号
            projectName: $scope.formRefer.projectName, //项目名称
            productLine: $scope.formRefer.productLine, //产品线
            hardwareModeNo: $scope.formRefer.hardwareModeNo, //型号
            customerName: $scope.formRefer.customerName, //客户名称
            xqsshy: $scope.formRefer.xqsshy, //行业
            projectPurpose: $scope.formRefer.projectPurpose, //产品用途
            softwareValueClassification: $scope.formRefer.softwareValueClassification, //软件价值分类
            startDate: inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'), //开始时间
            endDate: inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间
            marketDecisionName: $scope.formRefer.marketDecisionName, //立项决议函名称
            marketDecisionId: $scope.formRefer.marketDecisionId, //是否关联市场决议函，market_decision_id = 0 表示没有关联市场决议函，否则关联；
            projectStatus: $scope.formRefer.projectStatus, //状态
          };
          inform.downLoadFile(
            'companyProjectmanagement/toExcel',
            params,
            '公司立项项目基本信息表' + inform.format(new Date(), 'yyyy-MM-dd') + '.xlsx'
          );
        });
      };

      /**
       * *************************************************************
       *              方法声明部分                                结束
       * *************************************************************
       */
    },
  ]);
})();

(function () {
    app.controller("firstLevelDepartmentController", ['$ocLazyLoad', '$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http', 'firstLevelDepartmentService', 'workingHoursBoardFactory',
        function ($ocLazyLoad, $rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http, firstLevelDepartmentService, workingHoursBoardFactory) {
            // 初始化
            workingHoursBoardFactory.init($scope, '人年', '3');

            // 重置部分
            $scope.resetParam = resetParam;
            function resetParam(){
                workingHoursBoardFactory.initTime($scope, '本年度');
            }

            // top5部分
            $scope.reworkTopFiveData = [];
            $scope.technicalSupportTopFiveData = [];
            $scope.establishesProjectsTopFiveData = [];
            $scope.customerTopFiveData = [];
            $scope.PLMTopFiveData = [];

            // top5部分
            $scope.changeType = function changeType(type) {
                $scope.type = type
                if (type === '1') {
                    // 获取返工top5数据
                    $scope.getReworkTopFiveData();
                } else if (type === '2') {
                    // 获取技术支持top5数据
                    $scope.getTechnicalSupportTopFiveData();
                } else if (type === '3') {
                    // 获取公司立项项目统计top5数据
                    $scope.getEstablishesProjectsTopFiveData();
                } else if (type === '4') {
                    // 获取客户统计top5数据
                    $scope.getCustomerTopFiveData();
                } else if (type === '5') {
                    // 获取PLMtop5数据
                    $scope.getPLMTopFiveData();
                }
            }
            $scope.toMore = function (tabIndex, sortType, orgCode) {
                sortType = sortType || '';
                orgCode = orgCode || '';
                workingHoursBoardFactory.toMore($scope, tabIndex, sortType, orgCode);
            }
            $scope.toEstablishProject = function (){
                $state.go('app.office.companyProWorkTime');
            }
            $scope.toCustomer = function () {
                $state.go('app.office.customerWorkTime');
            }
            $scope.toPLM = function () {
                $state.go('app.office.customerDemandManagement');
            }
            // 获取返工top5数据
            $scope.getReworkTopFiveData = function () {
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime
                }
                firstLevelDepartmentService.getReworkHoursInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.reworkTopFiveData = [];
                        $scope.reworkTopFiveData.project = result.data.projectHoursList.slice(0, 5);
                        $scope.reworkTopFiveData.person = result.data.personHoursList.slice(0, 5);
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            // 获取技术支持top5数据
            $scope.getTechnicalSupportTopFiveData = function () {
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime
                }
                firstLevelDepartmentService.getTopTecSupportHoursInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.technicalSupportTopFiveData = [];
                        $scope.technicalSupportTopFiveData.project = result.data.projectHoursList.slice(0, 5);
                        $scope.technicalSupportTopFiveData.person = result.data.personHoursList.slice(0, 5);
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            // 获取公司立项项目统计top5数据
            $scope.getEstablishesProjectsTopFiveData = function () {
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime,
                    "page":1,
                    "pageSize":5
                }
                firstLevelDepartmentService.getCompanyProWorkTimeList(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.establishesProjectsTopFiveData = result.data.list;
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            // 获取客户统计top5数据
            $scope.getCustomerTopFiveData = function () {
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime,
                    "page":1,
                    "pageSize":5
                }
                firstLevelDepartmentService.getCustomerWorkTimeList(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.customerTopFiveData = result.data.list;
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            // 获取PLMtop5数据
            $scope.getPLMTopFiveData = function () {
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime
                }
                firstLevelDepartmentService.getTopPlmHoursInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        var data = result.data;
                        for(let i = 0; i < data.length; i++){
                            data[i].inputPerDay = (parseFloat(data[i].consumed) / AgreeConstant.workingHoursBoard.perDay).toFixed(2);
                        }
                        $scope.PLMTopFiveData = data;
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * 当窗体大小变化时，修改图例大小
             */
            $scope.currentWorkingHoursTypesChart = null;
            $scope.currentDepartmentWorkingHoursInputChart = null;
            $scope.currentProductLineInputChart = null;
            $scope.currentProductUseChart = null;
            $scope.currentSoftwareValueChart = null;
            $scope.currentDepartmentWorkingHoursChart = null;
            window.addEventListener("resize", chartResize);
            $scope.$on("$destroy", function() {
                window.removeEventListener('resize', chartResize);
            })
            function chartResize() {
                if ($scope.currentWorkingHoursTypesChart) { $scope.currentWorkingHoursTypesChart.resize(); }
                if ($scope.currentDepartmentWorkingHoursInputChart) { $scope.currentDepartmentWorkingHoursInputChart.resize(); }
                if ($scope.currentProductLineInputChart) { $scope.currentProductLineInputChart.resize(); }
                if ($scope.currentProductUseChart) { $scope.currentProductUseChart.resize(); }
                if ($scope.currentSoftwareValueChart) { $scope.currentSoftwareValueChart.resize(); }
                if ($scope.currentDepartmentWorkingHoursChart) { $scope.currentDepartmentWorkingHoursChart.resize(); }
            }

            // 获取工时类型分布情况图表数据
            $scope.toDetail = function (title) {
                $scope.modalTitle = title;
            }
            function getWorkingHoursTypesChartData () {
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime
                }
                workingHoursBoardFactory.chartHideClear($scope.currentWorkingHoursTypesChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentWorkingHoursTypesChart);
                firstLevelDepartmentService.getHoursInfoByType(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.workingHoursTypesInfo = result.data;
                        workingHoursBoardFactory.chartHideLoading($scope.currentWorkingHoursTypesChart);
                        workingHoursBoardFactory.showBar($scope.currentWorkingHoursTypesChart,$scope.workingHoursTypesInfo.slice(0, 10),{
                            title: '工时类型分布情况',
                            xType: 'statisDimensionName',
                            yType: 'hours',
                            fontSize: 12,
                            left: 'center',
                            grid: {
                                gridLeft: '2%',
                                gridRight: '2%',
                            },
                            needCustomSeries: true
                        });
                    } else {
                        inform.common(result.message);
                        workingHoursBoardFactory.chartHideLoading($scope.currentWorkingHoursTypesChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    workingHoursBoardFactory.chartHideLoading($scope.currentWorkingHoursTypesChart);
                });
            }
            // 获取部门工作投入情况图表数据
            function getDepartmentWorkingHoursInputChartData () {
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime
                }
                workingHoursBoardFactory.chartHideClear($scope.currentDepartmentWorkingHoursInputChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentDepartmentWorkingHoursInputChart);
                firstLevelDepartmentService.getHoursInfoInDept(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.departmentWorkingHoursInputInfo = result.data;
                        workingHoursBoardFactory.chartHideLoading($scope.currentDepartmentWorkingHoursInputChart);
                        workingHoursBoardFactory.showBar($scope.currentDepartmentWorkingHoursInputChart,$scope.departmentWorkingHoursInputInfo, {
                            title: '部门工作投入情况',
                            xType: 'statisDimensionName',
                            yType: 'hours',
                            fontSize: 12,
                            left: 'center',
                            grid: {
                                gridLeft: '2%',
                                gridRight: '2%',
                            },
                            needCustomSeries: true
                        });
                    } else {
                        inform.common(result.message);
                        workingHoursBoardFactory.chartHideLoading($scope.currentDepartmentWorkingHoursInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    workingHoursBoardFactory.chartHideLoading($scope.currentDepartmentWorkingHoursInputChart);
                });
            }
            // 跳转到部门工时投入详情
            $scope.toDetailDepartmentHoursData = function () {
                $state.go('app.office.workingHoursBoard', {
                    'typeSelect': '2',
                    'orgCode': null,
                    'sortType': null
                });
            }
            // 获取产品线投入情况图表数据
            function getProductLineInputChartData () {
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime
                }
                workingHoursBoardFactory.chartHideClear($scope.currentProductLineInputChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentProductLineInputChart);
                firstLevelDepartmentService.getHoursInfoInLine(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.productLineInputInfo = result.data;
                        workingHoursBoardFactory.chartHideLoading($scope.currentProductLineInputChart);
                        workingHoursBoardFactory.showPie($scope.currentProductLineInputChart,$scope.productLineInputInfo, {
                            title: '产品线投入情况',
                            type: 'statisDimensionName',
                            value: 'hours'
                        })
                    } else {
                        inform.common(result.message);
                        workingHoursBoardFactory.chartHideLoading($scope.currentProductLineInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    workingHoursBoardFactory.chartHideLoading($scope.currentProductLineInputChart);
                });
            }
            // 获取产品用途分类图表数据
            function getProductUseChartData () {
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime
                }
                workingHoursBoardFactory.chartHideClear($scope.currentProductUseChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentProductUseChart);
                firstLevelDepartmentService.getHoursInfoInProjectPurpose(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.productUseInfo = result.data;
                        workingHoursBoardFactory.chartHideLoading($scope.currentProductUseChart);
                        workingHoursBoardFactory.showPie($scope.currentProductUseChart,$scope.productUseInfo, {
                            title: '产品用途分类',
                            type: 'statisDimensionName',
                            value: 'hours'
                        });
                    } else {
                        inform.common(result.message);
                        workingHoursBoardFactory.chartHideLoading($scope.currentProductUseChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    workingHoursBoardFactory.chartHideLoading($scope.currentProductUseChart);
                });
            }
            // 获取软件价值分类图表数据
            function getSoftwareValueChartData () {
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime
                }
                workingHoursBoardFactory.chartHideClear($scope.currentSoftwareValueChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentSoftwareValueChart);
                firstLevelDepartmentService.getHoursInfoInSoftwareValue(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.softwareValueInfo = result.data;
                        workingHoursBoardFactory.chartHideLoading($scope.currentSoftwareValueChart);
                        workingHoursBoardFactory.showPie($scope.currentSoftwareValueChart,$scope.softwareValueInfo, {
                            title: '软件价值分类',
                            type: 'statisDimensionName',
                            value: 'hours'
                        });
                    } else {
                        inform.common(result.message);
                        workingHoursBoardFactory.chartHideLoading($scope.currentSoftwareValueChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    workingHoursBoardFactory.chartHideLoading($scope.currentSoftwareValueChart);
                });
            }
            // 获取部门工时图表数据
            function getDepartmentWorkingHoursChartData () {
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime
                }
                workingHoursBoardFactory.chartHideClear($scope.currentDepartmentWorkingHoursChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentDepartmentWorkingHoursChart);
                firstLevelDepartmentService.getDepartmentHoursInfo(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.departmentWorkingHoursChartInfo = result.data;
                        workingHoursBoardFactory.chartHideLoading($scope.currentDepartmentWorkingHoursChart);
                        eChartShowForWorkingHoursStatisticsBarAndLine($scope.currentDepartmentWorkingHoursChart,$scope.departmentWorkingHoursChartInfo, '部门工时', AgreeConstant.workingHoursBoard.xyWorkingHoursLegendData);
                    } else {
                        inform.common(result.message);
                        workingHoursBoardFactory.chartHideLoading($scope.currentDepartmentWorkingHoursChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    workingHoursBoardFactory.chartHideLoading($scope.currentDepartmentWorkingHoursChart);
                });
            }

            //部门工时图表显示
            function eChartShowForWorkingHoursStatisticsBarAndLine(currentChart,data,title, legendData){
                var xData = [];
                var reworkHoursData = [];
                var workingHoursInputData = [];
                var managementHoursData = [];
                var technologyHoursData = [];
                var departmentWorkingHoursData = [];
                if (data.length) {
                    angular.forEach(data, function (eachData) {
                        eachData.developTestRate === null ? xData.push(eachData.department) : xData.push(eachData.department + '(' + eachData.developTestRate + ')');
                        reworkHoursData.push({
                            value: eachData.reworkHoursRate,
                            developTestRate: eachData.developTestRate
                        });
                        workingHoursInputData.push({
                            value: eachData.allHours,
                            developTestRate: eachData.developTestRate
                        });
                        managementHoursData.push({
                            value: eachData.administrationHoursRate,
                            developTestRate: eachData.developTestRate
                        });
                        technologyHoursData.push({
                            value: eachData.supportHoursRate,
                            developTestRate: eachData.developTestRate
                        });
                        departmentWorkingHoursData.push({
                            value: eachData.depHoursRate,
                            developTestRate: eachData.developTestRate
                        });
                    });
                    var option = {
                        title:{
                            text:title,
                            textStyle:{
                                fontSize: 18,
                                color: '#333'
                            }
                        },
                        grid:{
                            left:'2%',
                            right:'2%',
                            bottom:'0',
                            containLabel:true
                        },
                        legend: {
                            data: legendData
                        },
                        xAxis: [
                            {
                                type: 'category',
                                data: xData,
                                axisPointer: {
                                    type: 'shadow'
                                }
                            }
                        ],
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'cross',
                                crossStyle: {
                                    color: '#999'
                                }
                            },
                            // formatter: (params, ticket, callback) => {return workingHoursBoardFactory.xyAndDeptFormatterCall(params, ticket, callback, '总工时')}
                            formatter: function (params, ticket, callback) {return workingHoursBoardFactory.xyAndDeptFormatterCall(params, ticket, callback, '总工时')}
                        },
                        yAxis: [
                            {
                                type: 'value',
                                axisLabel: {
                                    formatter: '{value}人年'
                                }
                            },
                            {
                                type: 'value',
                                axisLabel: {
                                    formatter: '{value}%'
                                }
                            }
                        ],
                        series: [
                            {
                                name: legendData[4],
                                type: 'line',
                                yAxisIndex: 1,
                                data: reworkHoursData,
                                show: false,
                                itemStyle:{
                                    color:"#FF4500"
                                }
                            },
                            {
                                name: legendData[0],
                                type: 'bar',
                                yAxisIndex: 0,
                                data: workingHoursInputData,
                                barWidth: '20%',
                                itemStyle:{
                                    color:"#77b606"
                                }
                            },
                            {
                                name: legendData[2],
                                type: 'line',
                                yAxisIndex: 1,
                                data: managementHoursData,
                                itemStyle:{
                                    color:"#fab70c"
                                }
                            },
                            {
                                name: legendData[3],
                                type: 'line',
                                yAxisIndex: 1,
                                data: technologyHoursData,
                                itemStyle:{
                                    color:"#0000CD"
                                }
                            },
                            {
                                name: legendData[1],
                                type: 'line',
                                yAxisIndex: 1,
                                data: departmentWorkingHoursData,
                                itemStyle:{
                                    color:"#74c2f5"
                                }
                            }
                        ]
                    };
                } else {
                    option = {
                        title: [{
                            text: title,
                            textStyle:{
                                fontSize: 12,
                                color: '#333'
                            },
                        },{
                            text: "暂无数据",
                            left: 'center',
                            top: 'center',
                            color: '#333',
                            textStyle: {
                                fontSize: 20
                            }
                        }]
                    }
                }

                currentChart.setOption(option, true);
            }

            // 页面加载后触发
            $scope.getData = getData;
            function getData () {
                //获取系研汇总数据：期初、期末、平均等
                getTotalData();
                // 获取top5的数据
                $scope.changeType($scope.type);
                // 获取工时类型分布情况图表数据
                getWorkingHoursTypesChartData();
                // 获取工部门工作投入情况图表数据
                getDepartmentWorkingHoursInputChartData();
                // 获取产品线投入情况图表数据
                getProductLineInputChartData();
                // 获取产品用途分类图表数据
                getProductUseChartData();
                // 获取产品用途分类图表数据
                getSoftwareValueChartData();
                // 获取部门工时图表数据
                getDepartmentWorkingHoursChartData();
            }
            $scope.loadSuccess = function (){
                $ocLazyLoad.load(
                    [
                        'library/component/echarts.min.js'
                    ]).then(function (){
                    $scope.currentWorkingHoursTypesChart = echarts.init(document.getElementById('workingHoursTypesChart'));
                    $scope.currentDepartmentWorkingHoursInputChart = echarts.init(document.getElementById('departmentWorkingHoursInputChart'));
                    $scope.currentProductLineInputChart = echarts.init(document.getElementById('productLineInputChart'));
                    $scope.currentProductUseChart = echarts.init(document.getElementById('productUseChart'));
                    $scope.currentSoftwareValueChart = echarts.init(document.getElementById('softwareValueChart'));
                    $scope.currentDepartmentWorkingHoursChart = echarts.init(document.getElementById('departmentWorkingHoursChart'));
                    getData();
                });
            }

            // 获取汇总数据
            function getTotalData(){
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime
                }
                $scope.showGatherHoursInfo = false;
                firstLevelDepartmentService.getGatherHoursInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.totalData = result.data;
                        $scope.showGatherHoursInfo = true;
                    } else {
                        inform.common(result.message);
                        $scope.showGatherHoursInfo = true;
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    $scope.showGatherHoursInfo = true;
                });
            }
        }]);
})();

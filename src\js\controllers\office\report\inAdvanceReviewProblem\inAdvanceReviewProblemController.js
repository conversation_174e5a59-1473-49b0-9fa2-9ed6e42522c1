(function () {
    app.controller("inAdvanceReviewProblemController", ['reviewProblemService','$rootScope', '$scope', '$stateParams','$modal', 'inform', 'LocalCache', 'Trans', 'AgreeConstant', '$http','$state',
        function (reviewProblemService, $rootScope, $scope,$stateParams, $modal, inform, LocalCache, Trans, AgreeConstant, $http,$state) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //获取从评审会议页面传入的评审会议id值
            $scope.reviewMeetingId = $stateParams.id;
            $scope.reviewContent = $stateParams.reviewContent;
            $scope.reviewTheme = $stateParams.reviewTheme;
            //绑定文件控件改变事件
            $("#filesImg").change(submitForm);

            //是否接收展示Map
            $scope.acceptMap = {
                "0": '是',
                "1": '否'
            };

            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.pages = {
                pageNum: '', 		// 分页页数
                size: '', 			// 分页每页大小
                total: '' 			// 数据总数
            };
            $scope.pages = inform.initPages(); 	// 初始化分页数据

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            $scope.selectFile = function() {
                document.getElementById("filesImg").click();
            };

            /**
             * 设置列表的高度
             */
            function setDivHeight(){
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - 190;
                //设置整表table所在div高度,包括表头、表内容、表尾,内边距为padding为10,所以实际高度比计算值高20
                $("#divTBDis").height(divHeight);
                //设置表tbody高度,表头35px,表尾55px,少-20让footer超出部分divTBDis,刚好铺满页面
                $("#subDivTBDis").height(divHeight - 65);
            }

            /**
             * 选择上传文件后事件
             */
            function fileChangeReset(){
                //通过表单元素的reset方法实现选择文件的重置
                $("#uploadForm")[0].reset();
            }

            //以分页的形式获取所有数据
            $scope.getData = function (pageNum) {
                var urlData = {
                    'reviewId':$scope.reviewMeetingId,//评审会议id
                    'page': pageNum,            // 分页页数
                    'size': $scope.pages.size   // 分页每页大小
                };
                reviewProblemService.getReviewProblem(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            var jsonData = data.data;
                            $scope.problemList = jsonData.list;
                            if ($scope.problemList.length === 0) {
                                inform.common("无符合条件的评审问题信息");
                                $scope.pages = inform.initPages(); 			        //初始化分页数据
                            } else {
                                // 分页信息设置
                                $scope.pages.total = jsonData.total;		// 页面总数
                                $scope.pages.star = jsonData.startRow;  	//页面起始数
                                $scope.pages.end = jsonData.endRow;  		//页面大小数
                                $scope.pages.pageNum = jsonData.pageNum;  	//页面页数
                            }
                        }else{
                              inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });

            };

            /**
             * 上传文件
             */
             function submitForm() {
                //表单id  初始化表单值
                var formData = new FormData();
                //获取文档中有类型为file的第一个input元素
                var file = document.querySelector('input[type=file]').files[0];
                if (!file) {
                    inform.common("请先选择文件!");
                    return false;
                }
                if(file.size >AgreeConstant.fileSize){
                    inform.common("上传文件大小不能超过2M");
                    fileChangeReset();
                    return false;
                }
                formData.append('file', file);
                formData.append('reviewId',$scope.reviewMeetingId);
                var a = file.type;
                if (a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
                    inform.common("请选择.xlsx类型的文档进行上传!");
                    return false;
                } else {
                    var modalInstance = $modal.open({
                        templateUrl: 'myModal',
                        controller: 'ModalInstanceCtrl',
                        size: "sm",
                        resolve: {
                            items: function () {
                                return "确定要上传文件吗！";
                            }
                        }
                    });
                    var uploadUrl = "";
                    //0-邮件评审,1-会议评审
                    if($stateParams.type === '0'){
                        uploadUrl = $rootScope.getWaySystemApi + 'reviewProblem/uploadMailExcel';
                    }else{
                        uploadUrl = $rootScope.getWaySystemApi + 'reviewProblem/uploadMeetingExcel';
                    }
                    modalInstance.result.then(function () {
                        //开启遮罩层
                        inform.showLayer("上传中。。。。。。");
                        $.ajax({
                            url: uploadUrl,
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            beforeSend: function (request) {
                                request.setRequestHeader("Authorization", 'Bearer ' + LocalCache.getSession("token") || '');
                            },
                            success: function (result) {
                                if (result.code === AgreeConstant.code) {
                                    // 关闭遮罩层
                                    inform.closeLayer();
                                    $modal.open({
                                        templateUrl: 'errorModel',
                                        controller: 'ModalInstanceCtrl',
                                        size: "lg",
                                        resolve: {
                                            items: function () {
                                                return result.message;
                                            }
                                        }
                                    });
                                    $scope.getData(1);
                                    // 关闭遮罩层
                                    inform.closeLayer();
                                } else {
                                    inform.closeLayer();
                                    inform.common(result.message);
                                }
                                //移除文件名称
                                fileChangeReset();
                            },
                            error: function (error) {
                                inform.common(Trans("tip.requestError"));
                            }
                        });
                    });
                }

            }

            /**
             * 新增/修改预评审问题的跳转
             * @param m 修改时包含问题信息，新增时为空
             */
            $scope.popModal = function(m){
                if(m){
                    $state.go('app.office.inAdvanceReviewProblemUp',{
                        problemJson:JSON.stringify(m),
                        reviewId:$scope.reviewMeetingId,
                        reviewType:$stateParams.type,
                        reviewContent:$scope.reviewContent,
                        reviewTheme:$scope.reviewTheme
                    });
                }else{
                    $state.go('app.office.inAdvanceReviewProblemAdd',{
                        reviewId:$scope.reviewMeetingId,
                        reviewType:$stateParams.type,
                        reviewContent:$scope.reviewContent,
                        reviewTheme:$scope.reviewTheme
                    });
                }

            };

            /**
             * 根据id删除评审问题
             * @param m 要删除的评审问题对象
             */
            $scope.deleteProblemById = function(m){
                var deleteModal = $modal.open({
                    templateUrl: 'myModal',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function () {
                            return "确定要删除该问题吗？";
                        }
                    }
                });
                var urlData = {id:m.id};
                deleteModal.result.then(function () {
                    reviewProblemService.deleteProblemById(urlData).then(function (data) {
                            if (data.code === AgreeConstant.code) {
                                layer.confirm(data.message,{
                                    title:false,
                                    btn:['确定']
                                },function(result){
                                    layer.close(result);
                                    $scope.getData(1);
                                });
                            }else {
                                inform.common(data.message);
                            }
                        },
                        function (error) {
                            inform.common(Trans("tip.requestError"));
                        });
                })
            };

            /**
             * 返回同行评审页面
             */
            $scope.back = function(){
              $state.go('app.office.report_0004',{flag:'notMenu'});
            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法初始化调用部分                           开始
             * *************************************************************
             */

            //获取查询结果列表
            $scope.getData(1);
            /**
             * *************************************************************
             *              方法初始化调用部分                           结束
             * *************************************************************
             */
        }]);
})();
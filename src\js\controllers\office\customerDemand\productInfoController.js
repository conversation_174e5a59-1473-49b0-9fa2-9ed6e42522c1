(function () {
    app.controller("productInfoController", ['$state','comService','$rootScope', '$scope','$modal','productInfoService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($state,comService,$rootScope, $scope, $modal,productInfoService,inform,Trans,AgreeConstant,LocalCache,$http) {

     	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		$scope.limitList = AgreeConstant.limitList; // 正则校验配置

    	initPages();//初始化页面信息
    	$scope.updateEmployeename = LocalCache.getSession('currentUserName');   //获取当前的登录用户名

		$scope.loginId = LocalCache.getSession('userId');  //获取当前登录者的id


    	$scope.pages = {
				pageNum : '', 		// 分页页数
				size : '', 			// 分页每页大小
				total : '' 			// 数据总数
			};
    	$scope.pages = inform.initPages(); 	// 初始化分页数据
		$scope.getData = getData; 			// 分页相关函数
		$scope.taskList = [];				// 保存所有信息的集合

		getData($scope.pages.pageNum);		//在刷新页面时调用该方法

	  	$scope.addInfo = addInfo;           // 新增一条信息
	  	$scope.updateInfo = updateInfo;     // 修改一条信息

		//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);

		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */


		 //设置列表的高度
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 +140);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight);
 		}

 		function initPages(){
 		   getData();

 		   $scope.employeeList = [];
           comService.getEmployeesName()
               .then(function (data) {
                   if (data.code === AgreeConstant.code) {
                       $scope.employeeList = data.data;
                   }
           });
          $scope.proList = [];
          comService.getProductManageName()
              .then(function (data) {
                  if (data.code === AgreeConstant.code) {
                      $scope.proList = data.data;
                  }
          });

 		}

 		 $scope.goBack = function (){
              $state.go('app.office.customerDataManagement');
         };



		$scope.update = function (m) {
            $scope.changeParam = angular.copy(m);
        }

        //修改
		function updateInfo(changeParam){
			if(!$scope.changeParam.paramValue){
				inform.common(Trans("产品经理名称不能为空！"));
				return false;
			} else {
			var urlData = {
				     'id':$scope.changeParam.id,
	    			 'paramValue':$scope.changeParam.paramValue,
	    			 'paramDesc':$scope.changeParam.paramDesc
			};

	        productInfoService.updateMessage(urlData).then(function(data){

	        		$("#edit_modal").modal('hide');
	        		if(data.code===AgreeConstant.code){
	        		   $scope.changeParam= {};
	        		   inform.common("修改成功！");
	        		   getData(1);
	        		} else {

	        		   inform.common("修改失败！");
	        		}
	        		getData(1);
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
	        // });

		}
		}

       // 删除弹框
	    $scope.open =function(m) {
	        var modalInstance = $modal.open({
	          templateUrl: 'myModalContent.html',
	          controller: 'ModalInstanceCtrl',
	          size: "sm",
	          resolve: {
	            items: function() {
	              return Trans("common.deleteTip");
	            }
	          }
	        });
	        modalInstance.result.then(function() {
	            if (m) {
	              $scope.delete(m);
	            }
	        });
	    };
         //删除信息
            $scope.delete = function (m){

                var urlData = {
                    'id':m.id
                };
                productInfoService.delMessage(urlData).then(function(data){
                    if(data.code===AgreeConstant.code) {
                        inform.common("删除成功！");
                        $scope.getData(1);
                    } else{
                        inform.common("删除失败");
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }



		//新增
		function addInfo(){
			$scope.addList = [];
			if(!$scope.formInsert.paramValue){
				inform.common(Trans("产品经理名称不能为空！"));
				return false;
			} else {
			var urlData = {
	    			 'paramValue':$scope.formInsert.paramValue,
                     'paramDesc':$scope.formInsert.paramDesc
			};

	        productInfoService.addMessage(urlData).then(function(data){

	        		$("#add_modal").modal('hide');
	        		if(data.code===AgreeConstant.code){
	        		   inform.common("新增产品经理成功");
	        		   getData(1);
	        		} else {

	        		   inform.common("新增产品经理失败");
	        		}

	        		getData(1);
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
	        // });

		}
		}

		//获取所有数据以分页的形式
		function getData(pageNum){
		   	$scope.itemList = [];
        	var urlData ={
//					'productLineName':$scope.formInput.productLineName,  			//产品线名称
//					'documentId':$scope.formInput.documentId,
//					'finishState':$scope.formInput.finishState,
//					'currentPage' : pageNum, 								// 分页页数
//					'pageSize' : $scope.pages.size    						// 分页每页大小
        	};
        	productInfoService.getAllMessage(urlData).then(function(data){
				if(data.code===AgreeConstant.code){
					var jsonData = data.data;
					$scope.itemList = jsonData.list;
					if ($scope.itemList.length===0) {
							$scope.pages = inform.initPages(); 			//初始化分页数据
					} else {
						// 分页信息设置
						$scope.pages.total = jsonData.total;		// 页面总数
						$scope.pages.star = jsonData.startRow;  	//页面起始数
						$scope.pages.end = jsonData.endRow;  		//页面大小数
						$scope.pages.pageNum = jsonData.pageNum;  	//页面页数
					}

				}
        	},
        	function(error) {
				inform.common(Trans("tip.requestError"));
			});
		}



	}]);
})();
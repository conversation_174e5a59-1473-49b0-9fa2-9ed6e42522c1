(function () {
  app.controller('PLMIndexController', [
    'LocalCache',
    'inform',
    'Trans',
    '$rootScope',
    'comService',
    'AgreeConstant',
    '$scope',
    '$stateParams',
    '$state',
    'deptBoardFactory',
    'PLMService',
    function (
      LocalCache,
      inform,
      Trans,
      $rootScope,
      comService,
      AgreeConstant,
      $scope,
      $stateParams,
      $state,
      deptBoardFactory,
      PLMService
    ) {
      // 初始化
      deptBoardFactory.init($scope, '1');
      // 重置部分
      $scope.resetParam = resetParam;
      function resetParam() {
        deptBoardFactory.initTime($scope, '本年度');
      }
      $scope.getData = getData;
      function getData() {
        getStatisticsData();
        getTop5Number();
        // 获取top5的数据
        $scope.changeType($scope.type);
      }
      // 统计区域
      function getStatisticsData() {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
        };
        $scope.showStatisticsInfo = false;
        PLMService.getPLMBoardTotal(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.PLMStatisticsData = result.data;
              $scope.showStatisticsInfo = true;
            } else {
              inform.common(result.message);
              $scope.showStatisticsInfo = true;
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
            $scope.showStatisticsInfo = true;
          }
        );
      }
      // top5标签上面数字
      function getTop5Number() {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
        };
        PLMService.getDeptPLMCount(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.top5 = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // top5部分
      $scope.changeType = function changeType(type) {
        $scope.type = type;
        if (type === '1') {
          // 获取延期未完成数据
          getDelayNoFinishData('延期未完成');
        } else if (type === '2') {
          // 获取临期数据
          getTemporaryData('临期');
        } else if (type === '3') {
          // 获取未到期数据
          getUnexpiredData('未到期');
        } else if (type === '4') {
          // 获取尚未制定计划数据
          getNoPlanYetData('尚未制定计划');
        } else if (type === '5') {
          // 获取延期完成数据
          getDelayFinishData('延期完成');
        } else if (type === '6') {
          // 获取已按时完成数据
          getPlanFinishData('已按时完成');
        } else if (type === '7') {
          // 获取退回或不涉及系研数据
          getBackNoXyData('退回或不涉及系研');
        }
      };
      function getDelayNoFinishData(finishState) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          finishState: finishState,
        };
        PLMService.getDeptPLMInfo(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.delayNoFinishData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      function getTemporaryData(finishState) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          finishState: finishState,
        };
        PLMService.getDeptPLMInfo(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.temporaryData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      function getUnexpiredData(finishState) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          finishState: finishState,
        };
        PLMService.getDeptPLMInfo(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.unexpiredData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      function getNoPlanYetData(finishState) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          finishState: finishState,
        };
        PLMService.getDeptPLMInfo(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.noPlanYetData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      function getDelayFinishData(finishState) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          finishState: finishState,
        };
        PLMService.getDeptPLMInfo(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.delayFinishData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      function getPlanFinishData(finishState) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          finishState: finishState,
        };
        PLMService.getDeptPLMInfo(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.planFinishData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      function getBackNoXyData(finishState) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          finishState: finishState,
        };
        PLMService.getDeptPLMInfo(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.backNoXyData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // 页面加载后触发
      $scope.$watch('$viewContentLoaded', function () {
        var localFormRefer = LocalCache.getObject('departmentList_formRefer');
        if (Object.keys(localFormRefer).length > 0) {
          $scope.formRefer = localFormRefer;
          $scope.butFlag = localFormRefer.searchTimeString;
        }
        if ($stateParams.orgCode) {
          $scope.formRefer.orgCode = $stateParams.orgCode;
        }
        getData();
      });

      $scope.title = '';
      $scope.desc = true;
      // 排序
      $scope.order = (str) => {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      };
    },
  ]);
})();

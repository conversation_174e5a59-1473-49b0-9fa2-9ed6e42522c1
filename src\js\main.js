(function () {
    'use strict';
    angular
        .module('app')
        .run([
            '$rootScope',
            '$state',
            function ($rootScope, $state) {
                // 存储上一个路由
                $rootScope.previousState = null;

                $rootScope.$on('$stateChangeStart', function (event, toState, toParams, fromState, fromParams) {
                    $rootScope.previousState = fromState;
                });
            },
        ])
        .controller('AppCtrl', [
            'LoginService',
            '$scope',
            '$localStorage',
            '$window',
            'LocalCache',
            '$state',
            '$rootScope',
            'AgreeConstant',
            'comService',
            function (
                loginService,
                $scope,
                $localStorage,
                $window,
                LocalCache,
                $state,
                $rootScope,
                AgreeConstant,
                comService
            ) {
                // config
                $scope.app = {
                    color: {
                        primary: '#7266ba',
                        info: '#23b7e5',
                        success: '#27c24c',
                        warning: '#fad733',
                        danger: '#f05050',
                        light: '#e8eff0',
                        dark: '#3a3f51',
                        black: '#1c2b36',
                    },
                    settings: {
                        themeID: 7,
                        navbarHeaderColor: 'bg-black',
                        navbarCollapseColor: 'bg-black',
                        asideColor: 'bg-dark',
                        headerFixed: true,
                        asideFixed: true,
                        asideFolded: false,
                        asideDock: false,
                        container: false,
                    },
                };
                // logo等信息
                $scope.heardInfo = AgreeConstant.login;

                // save settings to local storage
                if (angular.isDefined($localStorage.settings)) {
                    $scope.app.settings = $localStorage.settings;
                } else {
                    $localStorage.settings = $scope.app.settings;
                }
                $scope.$watch(
                    'app.settings',
                    function () {
                        if ($scope.app.settings.asideDock && $scope.app.settings.asideFixed) {
                            // aside dock and fixed must set the header fixed.
                            $scope.app.settings.headerFixed = true;
                        }
                        // save to local storage
                        $localStorage.settings = $scope.app.settings;
                    },
                    true
                );

                // 获取菜单信息

                // 监听 路由
                $rootScope.$on('$stateChangeStart', function (event, toState, toParams, fromState, fromParams) {
                    if (toState.name === 'login.index') {
                        LocalCache.clearDate();
                        return;
                    }
                    // 如果用户不存在
                    if (!LocalCache.getSession('currentUserName') || !LocalCache.getSession('token')) {
                        event.preventDefault(); // 取消默认跳转行为
                        $state.go('login.index'); //跳转到登录界面
                    }
                    // getMenu($state.current.name);
                });
                $rootScope.$on('$stateChangeSuccess', function (event, toState, toParams, fromState, fromParams) {
                    console.log($state.current.name);
                    setTimeout(function () {
                        getMenu($state.current.name);
                    }, 200);
                });
                // 监听错误路由
                $rootScope.$on('$stateNotFound', function (event, unfoundState, fromState, fromParams) {
                    $state.go('page404');
                });
                function getMenu(routePath) {
                    comService.getMenu(routePath).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            // $scope.menuValue = data.data;
                            console.log(data);
                            if (data.data != '') {
                                $('ul.breadcrumb').html("<i class='fa fa-home'></i>" + data.data);
                                LocalCache.set('menu', data.data);
                            } else {
                                $('ul.breadcrumb').html("<i class='fa fa-home'></i>" + LocalCache.get('menu'));
                            }
                        }
                    });
                }
                Interval();
                //定时刷新token
                function Interval() {
                    console.info('计时开始');
                    var timer = setInterval(function () {
                        loginService.refreshToken().then(function (data) {
                            LocalCache.setSession('token', data.result);
                        });
                    }, 1200000);
                }
            },
        ])
        .controller('asideCtrl', [
            '$scope',
            'inform',
            'SystemService',
            'Trans',
            'AgreeConstant',
            function ($scope, inform, SystemService, Trans, AgreeConstant) {
                // 权限数据获取
                // $scope.permissionList = angular.fromJson(LocalCache.getSession("permissionJson"));
                getAsideMenuData();
                function getAsideMenuData() {
                    SystemService.getLeftMenuData('MENU').then(
                        function (data) {
                            if (data.code === AgreeConstant.resultCode) {
                                $scope.permissionList = data.result;
                            } else {
                                inform.common(data.message);
                            }
                        },
                        function () {
                            inform.common(Trans('tip.requestError'));
                        }
                    );
                }
                $scope.getIconClass = function (str) {
                    if (str.icon) {
                        return str.icon;
                    } else {
                        return 'fa fa-square';
                    }
                };
            },
        ])
        .controller('headerCtrl', [
            '$ocLazyLoad',
            '$state',
            '$scope',
            '$rootScope',
            'LocalCache',
            '$http',
            '$interval',
            '$translate',
            'SystemService',
            'AgreeConstant',
            'inform',
            'Trans',
            'comService',
            function (
                $ocLazyLoad,
                $state,
                $scope,
                $rootScope,
                LocalCache,
                $http,
                $interval,
                $translate,
                SystemService,
                AgreeConstant,
                inform,
                Trans,
                comService
            ) {
                //加载公共JS文件
                $ocLazyLoad.load(['library/component/echarts.min.js', 'library/component/WdatePicker.js']);
                //浏览量按钮权限
                $scope.visitsFlag = false;
                //判断按钮是否具有权限
                getButtonPermission();
                // angular translate
                $scope.lang = { isopen: false };
                $scope.langs = { en: 'English', cn: '中文' };
                $scope.oftenLink = [
                    [
                        {
                            img: '../../img/oftenLink/zentao.ico',
                            name: '禅道',
                            val: 'app.office.zentaoIndexController',
                        },
                        {
                            img: '../../img/oftenLink/sonar_old.png',
                            name: 'Sonar7.5',
                            val: 'app.office.sonarIndexController',
                        },
                        {
                            img: '../../img/oftenLink/sonar_new.png',
                            name: 'Sonar9.9',
                            val: 'app.office.sonarnewIndexController',
                        },
                        {
                            img: '../../img/oftenLink/oa.png',
                            name: 'OA',
                            val: 'app.office.oaIndexController',
                        },
                        {
                            img: '../../img/oftenLink/wcp.ico',
                            name: '知识库',
                            val: 'app.office.websearchPubHome',
                        },
                        {
                            img: '../../img/oftenLink/gitLab.png',
                            name: 'Gitlab',
                            val: 'app.office.gitIndexController',
                        },
                        {
                            img: '../../img/oftenLink/jenkins.ico',
                            name: 'Jenkins',
                            val: 'app.office.jenkinsIndexController',
                        },
                    ],
                    [
                        {
                            img: '../../img/oftenLink/maven.png',
                            name: 'maven',
                            val: 'app.office.mavenIndexController',
                        },
                        {
                            img: '../../img/basic/favicon.ico',
                            name: 'CMDB',
                            val: 'app.office.CMDBIndexController',
                        },
                        {
                            img: '../../img/basic/favicon.ico',
                            name: 'apitest内网',
                            val: 'app.office.apitest1IndexController',
                        },
                        {
                            img: '../../img/oftenLink/jumpserver-facio.ico',
                            name: 'JumpServer',
                            val: 'app.office.JumpServerIndexController',
                        },
                        {
                            img: '../../img/oftenLink/dtrack.png',
                            name: '安全漏洞',
                            val: 'app.office.dtrackIndexController',
                        },
                        {
                            img: '../../img/oftenLink/CiHome-favicon.ico',
                            name: 'CiHome',
                            val: 'app.office.CiHomeIndexController',
                        },
                        {
                            img: '../../img/basic/favicon.ico',
                            name: 'Sentinel',
                            val: 'app.office.SentinelIndexController',
                        },
                    ],
                    [
                        {
                            img: '../../img/oftenLink/yapi-favicon.png',
                            name: 'yapi外网',
                            val: 'app.office.yapiIndexController',
                        },
                        {
                            img: '../../img/basic/favicon.ico',
                            name: 'apitest外网',
                            val: 'app.office.apitest2IndexController',
                        },
                        {
                            img: '../../img/oftenLink/logCenter-favicon-32x32.png',
                            name: '日志系统外网',
                            val: 'app.office.logCenterIndexController',
                        },
                        {
                            img: '../../img/basic/favicon.ico',
                            name: 'Zabbix外网',
                            val: 'app.office.apiTestIndexController',
                        },
                        {
                            img: '../../img/oftenLink/Grafana-fav32.png',
                            name: 'Grafana外网',
                            val: 'app.office.grafanaIndexController',
                        },
                        {
                            img: '../../img/oftenLink/Rancher-favicon.png',
                            name: 'Rancher外网',
                            val: 'app.office.rancherIndexController',
                        },
                        {
                            img: '../../img/oftenLink/harbor-favicon.ico',
                            name: 'Harbor外网',
                            val: 'app.office.harborIndexController',
                        },
                    ],
                    [
                        {
                            img: '../../img/oftenLink/sdm-favicon.png',
                            name: '软件发放系统',
                            val: 'app.office.SDMIndexController',
                        },
                        {
                            img: '../../img/oftenLink/superset-favicon.png',
                            name: 'Superset看板',
                            val: 'app.office.SupersetIndexController',
                        },
                    ],
                ];
                /*,{
                        'img':'../../img/oftenLink/file.jpeg',
                        'name':'共享目录',
                        'val':'app.office.shareIndexController'
                    }*/
                setTimeout(linkSee, 500);
                function linkSee() {
                    $('#oftenLink').click();
                }

                /**
                 * 获取按钮权限
                 */
                function getButtonPermission() {
                    var buttons = {
                        'Button-visits': 'visitsFlag',
                    };
                    var urlData = {
                        userId: LocalCache.getSession('userId'),
                        parentPermission: 'visits',
                        buttons: buttons,
                    };
                    comService.getButtonPermission(urlData, $scope);
                }
                $scope.goPersonalBoard = function () {
                    $state.go('app.index_bench', { empId: null, years: null, quarter: null }, { reload: true });
                };
                $scope.selectLang = $scope.langs[$translate.proposedLanguage()] || '中文';
                $scope.setLang = function (langKey, $event) {
                    // set the current lang
                    $scope.selectLang = $scope.langs[langKey];
                    // You can change the language during runtime
                    $translate.use(langKey);
                    $scope.lang.isopen = !$scope.lang.isopen;
                };

                // 退出登录
                $scope.loginOut = function () {
                    LocalCache.clearDate();
                };
                $scope.user = { userName: LocalCache.getSession('currentUserName') };
                // 权限数据获取
                // $scope.permissionMap = angular.fromJson(LocalCache.getSession("permissionMap"));
                getHeaderMenuData();
                function getHeaderMenuData() {
                    SystemService.getOrderMenuData().then(
                        function (data) {
                            if (data.code === AgreeConstant.resultCode) {
                                $scope.permissionMap = data.result;
                            } else {
                                inform.common(data.message);
                            }
                        },
                        function () {
                            inform.common(Trans('tip.requestError'));
                        }
                    );
                }
                // 实时获取接收的消息
                $scope.upd_news = function () {
                    $http({
                        url: $rootScope.getWaySystemApi + $rootScope.messageName + 'noticeMessage/getNoReadMessage',
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            Authorization: 'Bearer ' + LocalCache.getSession('token') || '',
                        },
                    })
                        .success(function (data, header, config, status) {
                            //响应成功
                            if (data.code === AgreeConstant.resultCode) {
                                $scope.count = data.result;
                                console.log(data.result);
                            }
                        })
                        .error(function (data, header, config, status) {
                            //处理响应失败
                        });
                };

                //        $scope.upd_news();
                //        var timeout_upd = $interval(function(){
                //          $scope.upd_news();
                //        } ,16000);
                //        // 清除定时器
                //        $scope.$on('$destroy',function(){
                //          $interval.cancel(timeout_upd);
                //        });
            },
        ]);
})();

/*
 * @Author: sunqixian
 * @Date:   2019-06-12 10:50:05
 * @Last Modified by:   sunqixian
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
    'use strict';
  app.factory('versionReleaseService', versionReleaseService);
  versionReleaseService.$inject=["HttpService",'$rootScope'];

  function versionReleaseService(HttpService,$rootScope){
    
	var service = {
        getVersionReleaseLogList:getVersionReleaseLogList,
        getVersionReleaseLog:getVersionReleaseLog,
        getLine:getLine,
        downloadExcel:downloadExcel,
        getRateOfProductLine:getRateOfProductLine,
        getRateOfDepartment:getRateOfDepartment,
        deleteInfo:deleteInfo,
        syncVersionReleaseLog:syncVersionReleaseLog
	};
    return service;

    /**
     * 删除信息
     */
    function deleteInfo(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'versionReleaseLog/deleteInfo', urlData);
    }
    
    /**
     * 获取所有上线操作信息
     */
    function getVersionReleaseLogList(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'versionReleaseLog/getVersionReleaseLogList', urlData);
    }

    /**
     * 回填某条上线失败的信息
     */
    function getVersionReleaseLog(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'versionReleaseLog/getVersionReleaseLog', urlData);
    }

    /**
     * 根据所选择项目获取产品线
     */
    function getLine(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'versionReleaseLog/getLine', urlData);
    }
    /**
     * 下载线上信息统计报表
     */
    function downloadExcel(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'versionReleaseLog/downloadExcel', urlData);
    }
    /**
     * 获取产品线失败率信息
     */
    function getRateOfProductLine(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'versionReleaseLog/getRateOfProductLine', urlData);
    }
    /**
     * 获取部门线失败率信息
     */
    function getRateOfDepartment(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'versionReleaseLog/getRateOfDepartment', urlData);
    }

    /**
     * 同步钉钉上线流程
     */
    function syncVersionReleaseLog(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'versionReleaseLog/syncVersionReleaseLogList', urlData);
    }

  }
})();

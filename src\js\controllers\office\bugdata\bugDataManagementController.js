/*
 * @Author: fubaole
 * @Date:   2017-09-18 14:53:05
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-01-15 17:20:32
 */
(function () {
    app.controller("bugDataManagementController", ['comService', '$rootScope', '$scope', 'bugDataManagementService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, bugDataManagementService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */

            //获取缓存
            $scope.formInput = LocalCache.getObject('bugData_formRefer');
            //对原缓存进行覆盖
            LocalCache.setObject("bugData_formRefer",{});
            //初始化时间
            initTime();
            //分页数据
            $scope.pages = {
                goNum:null, // 初始化跳转页码
                star:0, //开始条数
                end:0, //结束条数
                total:0, // 总条数
                size:"50", //每页条数
                pageNum:AgreeConstant.pageNum //默认页
            };
            //reopen原因是否缺失
            $scope.isReopenMap = [{
                value: '0',
                label: '否'
            }, {
                value: '1',
                label: '是'
            }];
            //reopen次数
            $scope.reopenCount = [{
                value: '0',
                label: '0次'
            }, {
                value: '1',
                label: '>=1次'
            }, {
                value: '2',
                label: '>=2次'
            }, {
                value: '3',
                label: '>=3次'
            }];
            //reopen次数默认值
            if($scope.formInput.reopenCount == null) {
                $scope.formInput.reopenCount = $scope.reopenCount[1].value;
            }
            $scope.resolvedFlagMap = ["否","是"];
            $scope.bugStatusList = ['激活','已解决','已关闭'];

            $scope.select_all = false;
            //初始化页面信息
            initPages();
            //设置列表的高度
            setDivHeight();
            getData(1);
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            $scope.getData = getData;
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            function initTime() {
                //设置默认时间
                if ($scope.formInput.endTime==null){
                    var endDate = inform.format(new Date(), 'yyyy-MM-dd');
                    var startDate = inform.format(new Date().getTime()-90*24*60*60*1000,"yyyy-MM-dd");
                    //默认开始时间
                    $scope.formInput.endTime = endDate;
                    $scope.formInput.startTime = startDate;
                }
            }
            /**
             *调用DataTable组件冻结表头和左侧及右侧的列
             */
            function showDataTable(){
                $('#fixedLeftAndTop').DataTable( {
                    //可被重新初始化
                    retrieve:       true,
                    //自适应高度
                    scrollY:        'calc(100vh - 350px)',
                    scrollX:        true,
                    scrollCollapse: true,
                    //控制每页显示
                    paging:         false,
                    //冻结列（默认冻结左1）
                    fixedColumns:   {
                        leftColumns: 2,
                        rightColumns: 1
                    },
                    //search框显示
                    searching:      false,
                    //排序箭头
                    ordering:       false,
                    //底部统计数据
                    info:           false
                } );

                // 获取到跟踪单信息之后滚动条跳至缓存中保存的scrollTop
                setTimeout(function () {
                    if ($scope.formInput.subDivTBDisScrollTop) {
                        $('#fixedLeftAndTop').parent().animate({scrollTop: $scope.formInput.subDivTBDisScrollTop},10);
                    }
                },300)
            }
            /**
             * 获取所有跟踪单信息
             */
            function getData(pageNum) {
                //删除已加载冻结头部和部分列的HTML模板
                $scope.dataTableShow = 0;
                var urlData = $scope.formInput;

                urlData.currentPage=pageNum;//当前页数
                urlData.pageSize=$scope.pages.size;//每页显示条数
                bugDataManagementService.getBugData(urlData).then(function (data) {
                    //重新加载冻结头部和部分列的HTML模板
                    $scope.dataTableShow = 1;
                    if (data.code === AgreeConstant.code) {
                        if(null==data.data){
                            $scope.sheetData = {};
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages();
                            $scope.pages.size = "50";
                        } else {

                            $scope.sheetData = data.data.list;
                            //分页信息设置
                            $scope.pages.total = data.data.total;
                            $scope.pages.star = data.data.startRow;
                            $scope.pages.end = data.data.endRow;
                            $scope.pages.pageNum = data.data.pageNum;
                        }
                        //调用DataTable组件冻结表头和左侧及右侧的列
                        setTimeout(showDataTable,300);
                    } else {
                        inform.common(data.message);
                    }
                },function () {
                    inform.common(Trans("tip.requestError"));
                });
            }
            function initPages() {

                //获取员工信息
                $scope.employeeList = [];
                comService.getProUser("").then(function(data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                    }
                });
                //获取产品线
                $scope.productLines = [];
                comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
                    if (data.data) {
                        $scope.productLines =  data.data;
                    }
                });
            }
            //跳转页面
            $scope.operateReopen = function(bugId) {
                LocalCache.setObject('bugData_formRefer', $scope.formInput);
                $state.go('app.office.bugDataReopenDetail',{bugId:bugId});
            }
            //重置
            $scope.reset = function() {
                $scope.formInput = {};
                initTime();
            }
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 210);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 75);
                setTimeout(showDataTable,500);
            }
            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };
            /**
             *下载跟踪单信息
             */
            $scope.toExcel = function() {
                inform.modalInstance("确定要下载bug数据表吗？").result.then(function() {
                    inform.downLoadFile('bugDataManagement/toExcel',$scope.formInput,"bug数据表"+inform.format(new Date(),'yyyy-MM-dd')+".xlsx");
                });
            };
            /**
             *下载bug进度数据信息
             */
            $scope.toExcelBugProgressData = function() {
                inform.modalInstance("确定要下载bug进度数据表吗？").result.then(function() {
                    inform.downLoadFile('bugDataManagement/toExcelBugProgressData',$scope.formInput,"bug进度数据表"+inform.format(new Date(),'yyyy-MM-dd')+".xlsx");
                });
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

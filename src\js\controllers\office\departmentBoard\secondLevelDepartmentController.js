(function () {
    app.controller('secondLevelDepartmentController', [
        '$ocLazyLoad',
        '$rootScope',
        'comService',
        '$scope',
        '$state',
        '$timeout',
        '$stateParams',
        '$modal',
        'inform',
        'Trans',
        'AgreeConstant',
        'LocalCache',
        '$http',
        'secondLevelDepartmentService',
        'workingHoursBoardFactory',
        function (
            $ocLazyLoad,
            $rootScope,
            comService,
            $scope,
            $state,
            $timeout,
            $stateParams,
            $modal,
            inform,
            Trans,
            AgreeConstant,
            LocalCache,
            $http,
            secondLevelDepartmentService,
            workingHoursBoardFactory
        ) {
            // 初始化
            workingHoursBoardFactory.init($scope, '人年');

            // 重置部分
            $scope.resetParam = resetParam;
            function resetParam() {
                $scope.selectOrgCode =
                    LocalCache.getSession('department') || $scope.departmentShowList[0].orgCode || $stateParams.orgCode;
                workingHoursBoardFactory.initTime($scope, '本年度');
            }

            // 部门选择的deptCode
            $scope.selectOrgCode = '';
            //初始化部门
            $scope.departmentShowList = [];
            $scope.getOrgListData = function () {
                // 获取部门数据
                comService.getOrgChildren('D010053').then(function (data) {
                    var department = comService.getDepartment(data.data);
                    $scope.selectOrgCode = LocalCache.getSession('department') || department[0].orgCode;
                    getData();
                    angular.forEach(department, function (i) {
                        if (i.orgCode !== 'D010053') {
                            $scope.departmentShowList.push(i);
                        }
                    });
                });
            };
            $scope.selectItem = function (item) {
                $scope.selectOrgCode = item.orgCode;
                getData();
            };

            // 渐变区域
            function getGradientAreaData() {
                var currentUrlData = {
                    endDate: $scope.formRefer.endTime,
                    orgCode: $scope.selectOrgCode,
                    startDate: $scope.formRefer.startTime,
                };
                secondLevelDepartmentService.getNeedToBeDoneInfo(currentUrlData).then(
                    function (result) {
                        if (result.code === AgreeConstant.code) {
                            $scope.gradientAreaData = [
                                {
                                    name: '待配置用户需求',
                                    number: result.data.customerStoryNum,
                                },
                                {
                                    name: '待拆分工时的迭代',
                                    number: result.data.workingHoursSplitNum,
                                },
                                {
                                    name: '无工时客户需求数量',
                                    number: result.data.noHoursPlmNum,
                                },
                            ];
                        } else {
                            inform.common(result.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }

            // 工时数据百分比
            function getTotalData() {
                var currentUrlData = {
                    endDate: $scope.formRefer.endTime,
                    orgCode: $scope.selectOrgCode,
                    startDate: $scope.formRefer.startTime,
                };
                $scope.showGatherHoursInfo = false;
                secondLevelDepartmentService.getGatherHoursInfo(currentUrlData).then(
                    function (result) {
                        if (result.code === AgreeConstant.code) {
                            $scope.totalData = result.data;
                            $scope.showGatherHoursInfo = true;
                        } else {
                            inform.common(result.message);
                            $scope.showGatherHoursInfo = true;
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                        $scope.showGatherHoursInfo = true;
                    }
                );
            }

            // 工时统计
            $scope.currentDeptProductLineInputChart = null;
            $scope.currentDeptWorkingHoursTypesChart = null;
            $scope.currentDeptWorkingHoursInputChart = null;
            $scope.currentDeptProjectInputChart = null;
            $scope.currentProjectWorkingHoursChart = null;
            window.addEventListener('resize', chartResize);
            $scope.$on('$destroy', function () {
                window.removeEventListener('resize', chartResize);
            });
            function chartResize() {
                if ($scope.currentDeptProductLineInputChart) {
                    $scope.currentDeptProductLineInputChart.resize();
                }
                if ($scope.currentDeptWorkingHoursTypesChart) {
                    $scope.currentDeptWorkingHoursTypesChart.resize();
                }
                if ($scope.currentDeptWorkingHoursInputChart) {
                    $scope.currentDeptWorkingHoursInputChart.resize();
                }
                if ($scope.currentDeptProjectInputChart) {
                    $scope.currentDeptProjectInputChart.resize();
                }
                if ($scope.currentProjectWorkingHoursChart) {
                    $scope.currentProjectWorkingHoursChart.resize();
                }
            }
            // 获取产品线投入情况图表数据
            function getDeptProductLineInputChartData() {
                var currentUrlData = {
                    endDate: $scope.formRefer.endTime,
                    orgCode: $scope.selectOrgCode,
                    startDate: $scope.formRefer.startTime,
                };
                workingHoursBoardFactory.chartHideClear($scope.currentDeptProductLineInputChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentDeptProductLineInputChart);
                secondLevelDepartmentService.getProductLineHoursInfo(currentUrlData).then(
                    function (result) {
                        if (result.code === AgreeConstant.code) {
                            $scope.deptProductLineInputInfo = [];
                            angular.forEach(result.data, function (eachData) {
                                $scope.deptProductLineInputInfo.push({
                                    name: eachData.statisDimensionName,
                                    value: eachData.hours,
                                });
                            });
                            workingHoursBoardFactory.chartHideLoading($scope.currentDeptProductLineInputChart);
                            workingHoursBoardFactory.showPie(
                                $scope.currentDeptProductLineInputChart,
                                $scope.deptProductLineInputInfo,
                                {
                                    title: '产品线投入情况',
                                }
                            );
                        } else {
                            inform.common(result.message);
                            workingHoursBoardFactory.chartHideLoading($scope.currentDeptProductLineInputChart);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                        workingHoursBoardFactory.chartHideLoading($scope.currentDeptProductLineInputChart);
                    }
                );
            }
            // 获取部门工作类型情况图表数据
            $scope.toDetail = function (title) {
                $scope.modalTitle = title;
            };
            function getDeptWorkingHoursTypesChartData() {
                var currentUrlData = {
                    endDate: $scope.formRefer.endTime,
                    orgCode: $scope.selectOrgCode,
                    startDate: $scope.formRefer.startTime,
                };
                workingHoursBoardFactory.chartHideClear($scope.currentDeptWorkingHoursTypesChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentDeptWorkingHoursTypesChart);
                secondLevelDepartmentService.getHoursInfoByType(currentUrlData).then(
                    function (result) {
                        if (result.code === AgreeConstant.code) {
                            $scope.deptWorkingHoursTypesInfo = result.data;
                            workingHoursBoardFactory.chartHideLoading($scope.currentDeptWorkingHoursTypesChart);
                            workingHoursBoardFactory.showBar(
                                $scope.currentDeptWorkingHoursTypesChart,
                                $scope.deptWorkingHoursTypesInfo.slice(0, 10),
                                {
                                    title: '工时类型分布情况',
                                    xType: 'statisDimensionName',
                                    yType: 'hours',
                                    fontSize: 12,
                                    left: 'center',
                                    grid: {
                                        gridLeft: '2%',
                                        gridRight: '2%',
                                    },
                                    needCustomSeries: true,
                                }
                            );
                        } else {
                            inform.common(result.message);
                            workingHoursBoardFactory.chartHideLoading($scope.currentDeptWorkingHoursTypesChart);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                        workingHoursBoardFactory.chartHideLoading($scope.currentDeptWorkingHoursTypesChart);
                    }
                );
            }
            // 获取部门工作投入情况图表数据
            function getDeptWorkingHoursInputChartData() {
                var currentUrlData = {
                    endDate: $scope.formRefer.endTime,
                    orgCode: $scope.selectOrgCode,
                    startDate: $scope.formRefer.startTime,
                };
                workingHoursBoardFactory.chartHideClear($scope.currentDeptWorkingHoursInputChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentDeptWorkingHoursInputChart);
                secondLevelDepartmentService.getHoursInfoInDept(currentUrlData).then(
                    function (result) {
                        if (result.code === AgreeConstant.code) {
                            $scope.deptWorkingHoursInputInfo = [];
                            angular.forEach(result.data, function (eachData) {
                                $scope.deptWorkingHoursInputInfo.push({
                                    name: eachData.statisDimensionName,
                                    value: eachData.hours,
                                });
                            });
                            workingHoursBoardFactory.chartHideLoading($scope.currentDeptWorkingHoursInputChart);
                            workingHoursBoardFactory.showBar(
                                $scope.currentDeptWorkingHoursInputChart,
                                $scope.deptWorkingHoursInputInfo,
                                {
                                    title: '部门工作投入情况统计',
                                    xType: 'name',
                                    yType: 'value',
                                    fontSize: 12,
                                    left: 'center',
                                    grid: {
                                        gridLeft: '2%',
                                        gridRight: '2%',
                                    },
                                }
                            );
                        } else {
                            inform.common(result.message);
                            workingHoursBoardFactory.chartHideLoading($scope.currentDeptWorkingHoursInputChart);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                        workingHoursBoardFactory.chartHideLoading($scope.currentDeptWorkingHoursInputChart);
                    }
                );
            }
            // 获取部门项目投入图表数据
            function getDeptProjectInputChartData() {
                var currentUrlData = {
                    endDate: $scope.formRefer.endTime,
                    orgCode: $scope.selectOrgCode,
                    startDate: $scope.formRefer.startTime,
                };
                workingHoursBoardFactory.chartHideClear($scope.currentDeptProjectInputChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentDeptProjectInputChart);
                secondLevelDepartmentService.getDeptProjectInputChartData(currentUrlData).then(
                    function (result) {
                        if (result.code === AgreeConstant.code) {
                            $scope.deptProjectInputChartInfo = result.data;
                            workingHoursBoardFactory.chartHideLoading($scope.currentDeptProjectInputChart);
                            workingHoursBoardFactory.showBar(
                                $scope.currentDeptProjectInputChart,
                                $scope.deptProjectInputChartInfo.slice(0, 10),
                                {
                                    title: '部门项目投入',
                                    xType: 'projectName',
                                    yType: 'allHours',
                                    fontSize: 18,
                                    left: 'auto',
                                    grid: {
                                        gridLeft: '1%',
                                        gridRight: '1%',
                                    },
                                    needCustomSeries: true,
                                }
                            );
                        } else {
                            inform.common(result.message);
                            workingHoursBoardFactory.chartHideLoading($scope.currentDeptProjectInputChart);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                        workingHoursBoardFactory.chartHideLoading($scope.currentDeptProjectInputChart);
                    }
                );
            }
            // 获取项目工时图表数据
            $scope.toProjectWorkingHours = function () {
                $state.go('app.office.workingHoursBoard', {
                    typeSelect: '3',
                    orgCode: $scope.selectOrgCode,
                    sortType: null,
                });
            };
            function getProjectWorkingHoursChartData() {
                var currentUrlData = {
                    endDate: $scope.formRefer.endTime,
                    orgCode: $scope.selectOrgCode,
                    startDate: $scope.formRefer.startTime,
                };
                workingHoursBoardFactory.chartHideClear($scope.currentProjectWorkingHoursChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentProjectWorkingHoursChart);
                secondLevelDepartmentService.getProjectHoursInfo(currentUrlData).then(
                    function (result) {
                        if (result.code === AgreeConstant.code) {
                            console.log(result.data);
                            $scope.projectWorkingHoursChartInfo = result.data.list.slice(0, 10);
                            workingHoursBoardFactory.chartHideLoading($scope.currentProjectWorkingHoursChart);
                            eChartShowForWorkingHoursStatisticsBarAndLine(
                                $scope.currentProjectWorkingHoursChart,
                                $scope.projectWorkingHoursChartInfo,
                                '项目工时数据',
                                AgreeConstant.workingHoursBoard.deptWorkingHoursLegendData,
                                18
                            );
                        } else {
                            inform.common(result.message);
                            workingHoursBoardFactory.chartHideLoading($scope.currentProjectWorkingHoursChart);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                        workingHoursBoardFactory.chartHideLoading($scope.currentProjectWorkingHoursChart);
                    }
                );
            }

            //项目工时图表显示
            function eChartShowForWorkingHoursStatisticsBarAndLine(
                currentChart,
                data,
                title,
                legendData,
                fontSize,
                xDataType,
                inputWorkDataType,
                managementHoursRadioDataType,
                technologyHoursRadioDataType,
                rewordHoursRadioDataType,
                developTestRate
            ) {
                fontSize = fontSize || 12;
                xDataType = xDataType || 'projectName';
                inputWorkDataType = inputWorkDataType || 'allHours';
                managementHoursRadioDataType = managementHoursRadioDataType || 'administrationHoursRate';
                technologyHoursRadioDataType = technologyHoursRadioDataType || 'supportHoursRate';
                rewordHoursRadioDataType = rewordHoursRadioDataType || 'reworkHoursRate';
                developTestRate = developTestRate || 'developTestRate';
                var xData = [];
                var inputWorkData = [];
                var managementHoursRadioData = [];
                var technologyHoursRadioData = [];
                var rewordHoursRadioData = [];
                var option = {};
                if (data.length) {
                    angular.forEach(data, function (eachData) {
                        xData.push(eachData[xDataType] + '(' + eachData[developTestRate] + ')');
                        inputWorkData.push({
                            value: eachData[inputWorkDataType],
                            developTestRate: eachData.developTestRate,
                        });
                        managementHoursRadioData.push({
                            value: eachData[managementHoursRadioDataType],
                            developTestRate: eachData.developTestRate,
                        });
                        technologyHoursRadioData.push({
                            value: eachData[technologyHoursRadioDataType],
                            developTestRate: eachData.developTestRate,
                        });
                        rewordHoursRadioData.push({
                            value: eachData[rewordHoursRadioDataType],
                            developTestRate: eachData.developTestRate,
                        });
                    });
                    option = {
                        title: {
                            text: title,
                            textStyle: {
                                fontSize: fontSize,
                                color: '#333',
                            },
                        },
                        legend: {
                            data: legendData,
                        },
                        xAxis: [
                            {
                                type: 'category',
                                data: xData,
                                axisPointer: {
                                    type: 'shadow',
                                },
                                axisLabel: {
                                    rotate: 25,
                                },
                            },
                        ],
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'cross',
                                crossStyle: {
                                    color: '#999',
                                },
                            },
                            formatter: function (params, ticket, callback) {
                                return workingHoursBoardFactory.xyAndDeptFormatterCall(
                                    params,
                                    ticket,
                                    callback,
                                    '投入工作量'
                                );
                            },
                        },
                        yAxis: [
                            {
                                type: 'value',
                                axisLabel: {
                                    formatter: '{value}人年',
                                },
                            },
                            {
                                type: 'value',
                                axisLabel: {
                                    formatter: '{value}%',
                                },
                            },
                        ],
                        series: [
                            {
                                name: legendData[0],
                                type: 'bar',
                                yAxisIndex: 0,
                                data: inputWorkData,
                                barWidth: '20%',
                            },
                            {
                                name: legendData[1],
                                type: 'line',
                                yAxisIndex: 1,
                                data: managementHoursRadioData,
                            },
                            {
                                name: legendData[2],
                                type: 'line',
                                yAxisIndex: 1,
                                data: technologyHoursRadioData,
                            },
                            {
                                name: legendData[3],
                                type: 'line',
                                yAxisIndex: 1,
                                data: rewordHoursRadioData,
                            },
                        ],
                    };
                    if (data.length > 13) {
                        option.dataZoom = [
                            {
                                type: 'slider',
                                showDetail: false,
                                height: 12,
                                bottom: 8,
                                start: 0,
                                end: 60,
                                minSpan: 30,
                            },
                        ];
                    }
                } else {
                    option = {
                        title: [
                            {
                                text: title,
                                textStyle: {
                                    fontSize: fontSize,
                                    color: '#333',
                                },
                            },
                            {
                                text: '暂无数据',
                                left: 'center',
                                top: 'center',
                                color: '#333',
                                textStyle: {
                                    fontSize: 20,
                                },
                            },
                        ],
                    };
                }
                currentChart.setOption(option, true);
            }

            // top5部分
            $scope.changeType = function changeType(type) {
                $scope.type = type;
                if (type === '1') {
                    // 获取返工top5数据
                    $scope.getDeptReworkTopFiveData();
                } else if (type === '2') {
                    // 获取技术支持top5数据
                    $scope.getDeptTechnicalSupportTopFiveData();
                }
            };
            $scope.toMore = function (tabIndex, sortType, orgCode) {
                sortType = sortType || '';
                orgCode = orgCode || $scope.selectOrgCode;
                workingHoursBoardFactory.toMore($scope, tabIndex, sortType, orgCode);
            };
            // 获取返工top5数据
            $scope.getDeptReworkTopFiveData = function () {
                var currentUrlData = {
                    endDate: $scope.formRefer.endTime,
                    orgCode: $scope.selectOrgCode,
                    startDate: $scope.formRefer.startTime,
                };
                secondLevelDepartmentService.getReworkHoursInfo(currentUrlData).then(
                    function (result) {
                        if (result.code === AgreeConstant.code) {
                            $scope.reworkTopFiveData = [];
                            $scope.reworkTopFiveData.project = result.data.projectHoursList.slice(0, 5);
                            $scope.reworkTopFiveData.person = result.data.personHoursList.slice(0, 5);
                        } else {
                            inform.common(result.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            // 获取技术支持top5数据
            $scope.getDeptTechnicalSupportTopFiveData = function () {
                var currentUrlData = {
                    endDate: $scope.formRefer.endTime,
                    orgCode: $scope.selectOrgCode,
                    startDate: $scope.formRefer.startTime,
                };
                secondLevelDepartmentService.getTopTecSupportHoursInfo(currentUrlData).then(
                    function (result) {
                        if (result.code === AgreeConstant.code) {
                            $scope.technicalSupportTopFiveData = [];
                            $scope.technicalSupportTopFiveData.project = result.data.projectHoursList.slice(0, 5);
                            $scope.technicalSupportTopFiveData.person = result.data.personHoursList.slice(0, 5);
                        } else {
                            inform.common(result.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };

            // 页面加载后触发
            $scope.getData = getData;
            function getData() {
                // 获取渐变区域数据
                getGradientAreaData();
                // 获取工时数据百分比
                getTotalData();
                // 获取top5的数据
                $scope.changeType('1');
                // 部门产品线投入情况数据
                getDeptProductLineInputChartData();
                // 获取部门工作投入类型情况图表数据
                getDeptWorkingHoursTypesChartData();
                // 获取部门工作投入情况图表数据
                getDeptWorkingHoursInputChartData();
                // 获取部门项目投入图表数据
                getDeptProjectInputChartData();
                // 获取部门工时数据
                getProjectWorkingHoursChartData();
            }
            $scope.loadSuccess = function () {
                $ocLazyLoad.load(['library/component/echarts.min.js']).then(function () {
                    $scope.currentDeptProductLineInputChart = echarts.init(
                        document.getElementById('deptProductLineInputChart')
                    );
                    $scope.currentDeptWorkingHoursTypesChart = echarts.init(
                        document.getElementById('deptWorkingHoursTypesChart')
                    );
                    $scope.currentDeptWorkingHoursInputChart = echarts.init(
                        document.getElementById('deptWorkingHoursInputChart')
                    );
                    $scope.currentDeptProjectInputChart = echarts.init(
                        document.getElementById('deptProjectInputChart')
                    );
                    $scope.currentProjectWorkingHoursChart = echarts.init(
                        document.getElementById('projectWorkingHoursChart')
                    );
                    if ($state.current.url === '/workingHoursBoard') {
                        $scope.getOrgListData();
                    } else {
                        var localFormRefer = LocalCache.getObject('departmentList_formRefer');
                        if (Object.keys(localFormRefer).length > 0) {
                            $scope.formRefer = localFormRefer;
                            $scope.butFlag = localFormRefer.searchTimeString;
                        }
                        if ($stateParams.orgCode) {
                            $scope.selectOrgCode = $stateParams.orgCode;
                        }
                        getData();
                    }
                });
            };
        },
    ]);
})();

(function() {
    'use strict';
    app.factory('staffDailyActionService', staffDailyActionService);
    staffDailyActionService.$inject = ["HttpService", '$rootScope'];

    function staffDailyActionService(HttpService, $rootScope) {
        var service = {
            getStaffDailyActionInfo: getStaffDailyActionInfo,
            deleteStaffDailyActionInfo: deleteStaffDailyActionInfo,
            addStaffDailyActionInfo: addStaffDailyActionInfo,
            updateStaffDailyActionInfo: updateStaffDailyActionInfo
        };
        return service;
        /**
         * 分页查询红黑事件
         */
        function getStaffDailyActionInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'StaffDailyAction/getStaffDailyActionInfo', urlData);
        }
        /**
         * 删除红黑事件
         */
        function deleteStaffDailyActionInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'StaffDailyAction/deleteStaffDailyActionInfo', urlData);
        }
        /**
         * 新增红黑事件
         */
        function addStaffDailyActionInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'StaffDailyAction/addStaffDailyActionInfo', urlData);
        }
        /**
         * 修改红黑事件
         */
        function updateStaffDailyActionInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'StaffDailyAction/updateStaffDailyActionInfo', urlData);
        }
    }
})();
(function () {
    app.controller("personalQaDataController", ['$ocLazyLoad', '$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'personalQaDataService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http','dataReportFactory',
        function ($ocLazyLoad, $rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, personalQaDataService, inform, Trans, AgreeConstant, LocalCache, $http,dataReportFactory) {           
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //页面数据
			$scope.formRefer = {};
            $scope.formInput = {};
            //用例类型
            $scope.caseTypeMap = {
                "feature": "功能测试",
                "performance": "性能测试",
                "config": "配置相关",
                "install": "安装部署",
                "security": "安全相关",
                "interface": "接口测试",
                "other": "其他"
            };
            //用例等级
            $scope.casePriorityMap = {
                "1": "1级",
                "2": "2级",
                "3": "3级",
                "4": "4级"
            };
            $scope.timeSelect=['上月', '上年', '本月', '本年'];
            $scope.initTime = function (m){
                initTime(m)
            };
            initTime('本年');
            $scope.getQaData = getQaData;
            $scope.getData = getData;
            $scope.reset = reset;
            //初始化信息
            initData();


            $scope.currentBugDataOfSeverityInputChart = null;
            $scope.currentBugDataOfTypeInputChart = null;
            $scope.currentCaseDataOfTypeInputChart = null;
            $scope.currentCaseDataOfPriorityInputChart = null;
            $scope.currentTestTaskOfStatusInputChart = null;
            $scope.currentLaunchOfProjectInputChart = null;
            $scope.currentReviewDataOfTypeInputChart = null;
            
            window.addEventListener("resize", chartResize);
            $scope.$on("$destroy", function() {
                window.removeEventListener('resize', chartResize);
            })
            function chartResize() {
                if ($scope.currentBugDataOfSeverityInputChart) { $scope.currentBugDataOfSeverityInputChart.resize(); }
                if ($scope.currentBugDataOfTypeInputChart) { $scope.currentBugDataOfTypeInputChart.resize(); }
                if ($scope.currentCaseDataOfTypeInputChart) { $scope.currentCaseDataOfTypeInputChart.resize(); }
                if ($scope.currentCaseDataOfPriorityInputChart) { $scope.currentCaseDataOfPriorityInputChart.resize(); }
                if ($scope.currentTestTaskOfStatusInputChart) { $scope.currentTestTaskOfStatusInputChart.resize(); }
                if ($scope.currentLaunchOfProjectInputChart) { $scope.currentLaunchOfProjectInputChart.resize(); }
                if ($scope.currentReviewDataOfTypeInputChart) { $scope.currentReviewDataOfTypeInputChart.resize(); }
            }

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
           

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 初始化
             */
            function initData() {
                //获取当前登录者的empId
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ? $scope.sessionEmpId : $stateParams.empId;
                //获取当前登录者的中文名
                $scope.formRefer.employeeName = LocalCache.getSession('employeeName');
                $scope.formRefer.loginName = LocalCache.getSession('loginName');
                $scope.formRefer.employeeId = LocalCache.getSession('employeeId');
                var person = LocalCache.getObject('personDataBoardEmployee');
                if(person.name){
                    $scope.formRefer.employeeName = person.name;
                    $scope.formRefer.loginName = person.loginName;
                    $scope.formRefer.employeeId = person.employeeCode;
                }
                reset();
                getData(); 

            }

            // 时间段选择
            function initTime(flag) {
                // 快捷键选项
                $scope.butFlag = flag;
                let date = new Date();
                let y = date.getFullYear(); //当前年份
                let lastMonth = date.getMonth() - 1; //上月
                let lastYear = y -1 ; //上年
                if ('上月' === $scope.butFlag) {
                    // 获取上月天数
                    let daysInMonth = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
                    $scope.formRefer.startTime = inform.format(new Date(y, lastMonth, 1), 'yyyy-MM-01');
                    $scope.formRefer.endTime = inform.format(new Date(y, lastMonth, daysInMonth), 'yyyy-MM-dd');
                }
                if ('上年' === $scope.butFlag) {
                    $scope.formRefer.startTime = inform.format(new Date(lastYear, 0, 1), 'yyyy-01-01');
                    $scope.formRefer.endTime = inform.format(new Date(lastYear, 11, 31), 'yyyy-12-31');
                }
                if ('本年' === $scope.butFlag) {
                    $scope.formRefer.startTime = inform.format(date, 'yyyy-01-01');
                    $scope.formRefer.endTime = inform.format(date, 'yyyy-MM-dd');
                }
                if ('本月' === $scope.butFlag) {
                    $scope.formRefer.startTime = inform.format(date, 'yyyy-MM-01');
                    $scope.formRefer.endTime = inform.format(date, 'yyyy-MM-dd');
                }
            }
            /**
             * 获取运维数据统计信息
             */
            function getData() {
                getBugStatisticData();
                getCaseStatisticData();
                getTestTaskStatisticData();
            }

            function getBugStatisticData(){
                var urlData = {
                    'operatorName':$scope.formRefer.employeeName,
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd')//结束时间
                }
                $scope.tableData = [];
                personalQaDataService.getBugDataStatistics(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.bugStatisticData = data.data;
                       
                            } 
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            function getCaseStatisticData(){
                var urlData = {
                    'operatorName':$scope.formRefer.employeeName,
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                    'employeeId': $scope.formRefer.employeeId
                }
                $scope.tableData = [];
                personalQaDataService.getCaseDataStatistics(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.caseStatisticData = data.data;
                       
                            } 
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            function getTestTaskStatisticData(){
                var urlData = {
                    'operatorName':$scope.formRefer.employeeName,
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd')//结束时间
                }
                $scope.tableData = [];
                personalQaDataService.getTestTaskDataStatistics(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.testTaskStatisticData = data.data;
                       
                            } 
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            function getBugDataOfSeverityChartData () {
                var currentUrlData = {
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                    "operatorName":$scope.formRefer.employeeName
                }
                dataReportFactory.chartHideClear($scope.currentBugDataOfSeverityInputChart);
                dataReportFactory.chartShowLoading($scope.currentBugDataOfSeverityInputChart);
                personalQaDataService.getBugDataOfSeverity(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.bugSeverityDataInputInfo = result.data;
                        dataReportFactory.chartHideLoading($scope.currentBugDataOfSeverityInputChart);
                        dataReportFactory.showPie($scope.currentBugDataOfSeverityInputChart,$scope.bugSeverityDataInputInfo, {
                            title: 'bug严重程度分布',
                            type: 'bugSeverity',
                            value:'bugNum',
                            fontSize:'12',
                            israte: true
                        });
                    } else {
                        inform.common(result.message);
                        dataReportFactory.chartHideLoading($scope.currentBugDataOfSeverityInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    dataReportFactory.chartHideLoading($scope.currentBugDataOfSeverityInputChart);
                });
            }

            function getBugDataOfTypeChartData () {
                var currentUrlData = {
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                    "operatorName":$scope.formRefer.employeeName
                }
                dataReportFactory.chartHideClear($scope.currentBugDataOfTypeInputChart);
                dataReportFactory.chartShowLoading($scope.currentBugDataOfTypeInputChart);
                personalQaDataService.getBugDataOfType(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.bugTypeDataInputInfo = result.data;
                        dataReportFactory.chartHideLoading($scope.currentBugDataOfTypeInputChart);
                        dataReportFactory.showPie($scope.currentBugDataOfTypeInputChart,$scope.bugTypeDataInputInfo, {
                            title: 'bug类型分布',
                            type: 'bugType',
                            value:'bugNum',
                            fontSize:'12',
                            israte: true
                        });
                    } else {
                        inform.common(result.message);
                        dataReportFactory.chartHideLoading($scope.currentBugDataOfTypeInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    dataReportFactory.chartHideLoading($scope.currentBugDataOfTypeInputChart);
                });
            }
            
            function getCaseDataOfTypeChartData () {
                var currentUrlData = {
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                    "operatorName":$scope.formRefer.employeeName
                }
                dataReportFactory.chartHideClear($scope.currentCaseDataOfTypeInputChart);
                dataReportFactory.chartShowLoading($scope.currentCaseDataOfTypeInputChart);
                personalQaDataService.getCaseDataOfType(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.caseTypeDataInputInfo = result.data;
                        dataReportFactory.chartHideLoading($scope.currentCaseDataOfTypeInputChart);
                        dataReportFactory.showPie($scope.currentCaseDataOfTypeInputChart,$scope.caseTypeDataInputInfo, {
                            title: '用例类型分布',
                            type: 'caseType',
                            value:'caseNum',
                            fontSize:'12',
                            israte: true
                        });
                    } else {
                        inform.common(result.message);
                        dataReportFactory.chartHideLoading($scope.currentCaseDataOfTypeInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    dataReportFactory.chartHideLoading($scope.currentCaseDataOfTypeInputChart);
                });
            }
            

            function getCaseDataOfPriorityChartData () {
                var currentUrlData = {
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                    "operatorName":$scope.formRefer.employeeName
                }
                dataReportFactory.chartHideClear($scope.currentCaseDataOfPriorityInputChart);
                dataReportFactory.chartShowLoading($scope.currentCaseDataOfPriorityInputChart);
                personalQaDataService.getCaseDataOfPriority(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.casePriorityDataInputInfo = result.data;
                        dataReportFactory.chartHideLoading($scope.currentCaseDataOfPriorityInputChart);
                        dataReportFactory.showPie($scope.currentCaseDataOfPriorityInputChart,$scope.casePriorityDataInputInfo, {
                            title: '用例等级分布',
                            type: 'casePriority',
                            value:'caseNum',
                            fontSize:'12',
                            israte: true
                        });
                    } else {
                        inform.common(result.message);
                        dataReportFactory.chartHideLoading($scope.currentCaseDataOfPriorityInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    dataReportFactory.chartHideLoading($scope.currentCaseDataOfPriorityInputChart);
                });
            }
            
            function getTestTaskDataOfStatusChartData () {
                var currentUrlData = {
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                    "operatorName":$scope.formRefer.employeeName
                }
                dataReportFactory.chartHideClear($scope.currentTestTaskOfStatusInputChart);
                dataReportFactory.chartShowLoading($scope.currentTestTaskOfStatusInputChart);
                personalQaDataService.getTestTaskDataOfStatus(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.testTaskStatusDataInputInfo = result.data;
                        dataReportFactory.chartHideLoading($scope.currentTestTaskOfStatusInputChart);
                        dataReportFactory.showPie($scope.currentTestTaskOfStatusInputChart,$scope.testTaskStatusDataInputInfo, {
                            title: '提测单状态分布',
                            type: 'testTaskStatus',
                            value:'testTaskNum',
                            fontSize:'12',
                            israte: true
                        });
                    } else {
                        inform.common(result.message);
                        dataReportFactory.chartHideLoading($scope.currentTestTaskOfStatusInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    dataReportFactory.chartHideLoading($scope.currentTestTaskOfStatusInputChart);
                });
            }

            
            function getLaunchOfProjectChartData () {
                var currentUrlData = {
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                    "operatorName":$scope.formRefer.employeeName
                }
                dataReportFactory.chartHideClear($scope.currentLaunchOfProjectInputChart);
                dataReportFactory.chartShowLoading($scope.currentLaunchOfProjectInputChart);
                personalQaDataService.getLaunchOfProject(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.launchProjectDataInputInfo = result.data;
                        dataReportFactory.chartHideLoading($scope.currentLaunchOfProjectInputChart);
                        dataReportFactory.showPie($scope.currentLaunchOfProjectInputChart,$scope.launchProjectDataInputInfo, {
                            title: '发布项目',
                            type: 'project',
                            value:'launchNum',
                            fontSize:'12',
                            israte: true
                        });
                    } else {
                        inform.common(result.message);
                        dataReportFactory.chartHideLoading($scope.currentLaunchOfProjectInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    dataReportFactory.chartHideLoading($scope.currentLaunchOfProjectInputChart);
                });
            }

            function getReviewOfTypeChartData () {
                var currentUrlData = {
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                    "operatorName":$scope.formRefer.employeeName
                }
                dataReportFactory.chartHideClear($scope.currentReviewDataOfTypeInputChart);
                dataReportFactory.chartShowLoading($scope.currentReviewDataOfTypeInputChart);
                personalQaDataService.getReviewOfType(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.reviewTypeDataInputInfo = result.data;
                        dataReportFactory.chartHideLoading($scope.currentReviewDataOfTypeInputChart);
                        dataReportFactory.showBarAndLine($scope.currentReviewDataOfTypeInputChart,$scope.reviewTypeDataInputInfo, {
                            title: '评审数据',
                            xType: 'reviewType',
                            yType:'reviewNum',
                            yTypeLine:'reviewPassRate',
                            left:'left',
                            fontSize:'12',
                            israte: true
                        },[
                            '评审次数',
                            '评审通过率'
                        ]);
                    } else {
                        inform.common(result.message);
                        dataReportFactory.chartHideLoading($scope.currentReviewDataOfTypeInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    dataReportFactory.chartHideLoading($scope.currentReviewDataOfTypeInputChart);
                });
            }

			function reset() {
                initTime('本年');
                // $scope.formRefer.endTime = '';
            }

            $scope.goback = function () {
                $state.go('app.office.personKpi');
            };

            //评审时间 -开始
            $scope.openDateStart = function($event) {
                $scope.butFlag = '';
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = true;
                $scope.formRefer.openedEnd = false;
            };
            //评审时间 -结束
            $scope.openDateEnd = function($event) {
                $scope.butFlag = '';
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = false;
                $scope.formRefer.openedEnd = true;
            };
            
            // 页面加载后触发
            $scope.getData = getQaChartData;

            function getQaChartData(){
                getBugDataOfSeverityChartData();
                getBugDataOfTypeChartData();
                getCaseDataOfTypeChartData();
                getCaseDataOfPriorityChartData();
                getTestTaskDataOfStatusChartData();
                getLaunchOfProjectChartData();
                getReviewOfTypeChartData();
            }

            $scope.loadSuccess = function (){
                $ocLazyLoad.load(
                    [
                        'library/component/echarts.min.js'
                    ]).then(function (){
                    $scope.currentBugDataOfSeverityInputChart = echarts.init(document.getElementById("bugDataOfSeverityInputChart"));
                    $scope.currentBugDataOfTypeInputChart = echarts.init(document.getElementById("bugDataOfTypeInputChart"));
                    $scope.currentCaseDataOfTypeInputChart = echarts.init(document.getElementById("caseDataOfTypeInputChart"));
                    $scope.currentCaseDataOfPriorityInputChart = echarts.init(document.getElementById("caseDataOfPriorityInputChart"));
                    $scope.currentTestTaskOfStatusInputChart = echarts.init(document.getElementById("testTaskOfStatusInputChart"));
                    $scope.currentLaunchOfProjectInputChart = echarts.init(document.getElementById("launchOfProjectInputChart")); 
                    $scope.currentReviewDataOfTypeInputChart = echarts.init(document.getElementById("reviewDataOfTypeInputChart"));                                      
                    getQaChartData();
                    
                });
            }

            
            $scope.showCaseList = function () {
                $state.go('app.office.caseDevote');
                $scope.formRefer.startTime = inform.format($scope.formRefer.startTime, 'yyyy-MM-dd');
                $scope.formRefer.endTime = inform.format($scope.formRefer.endTime, 'yyyy-MM-dd');
                $scope.formRefer.employee  = $scope.formRefer.loginName;
                LocalCache.setObject('testReportProduct_formRefer', $scope.formRefer);
                  
            };

            $scope.showTestTaskList = function () {
                $state.go('app.office.testTask');
                $scope.formInput.owner = $scope.formRefer.employeeName;
                $scope.formInput.startTime = inform.format($scope.formRefer.startTime, 'yyyy-MM-dd');
                $scope.formInput.endTime = inform.format($scope.formRefer.endTime, 'yyyy-MM-dd');//结束时间
                LocalCache.setObject('testTask_formRefer', $scope.formInput);
                  
            };

            $scope.showBugList = function () {
                $state.go('app.office.bugFound');
                $scope.formRefer.startTime = inform.format($scope.formRefer.startTime, 'yyyy-MM-dd');
                $scope.formRefer.endTime = inform.format($scope.formRefer.endTime, 'yyyy-MM-dd');
                $scope.formRefer.employee  = $scope.formRefer.loginName;
                LocalCache.setObject('testReportProduct_formRefer', $scope.formRefer);
                  
            };

            function getQaData(){
                //获取统计信息
                getData();
                //获取图表信息
                getQaChartData();
            }
            
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

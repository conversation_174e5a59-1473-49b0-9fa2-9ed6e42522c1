/*
 * @Author: fubaole
 * @Date:   2017-09-18 14:53:05
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-01-15 17:20:32
 */
(function () {
    app.controller("workingHoursQueryController", ['comService', '$rootScope', '$scope', 'workingHoursManagementService','nonWorkDaysService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, workingHoursManagementService,nonWorkDaysService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
    		$scope.limitList = AgreeConstant.limitList; // 正则校验配置
    		$scope.formRefer={};
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //初始化
            initPages();

            $scope.getData = getData;
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
        	 * 页面初始化
        	 */
        	function initPages() {

                //每月工作天数
                $scope.workDayMonthly = 22;
        		//获取产品线
        		$scope.lineMap = {};
        		comService.getParamList('PRO_PRODUCT_TYPE','PRO_PRODUCT_TYPE').then(function(data) {
        			angular.forEach(data.data,function (line) {
                        $scope.lineMap[line.param_value] = line.param_code;
                    });
        		});
        		 //获取部门
                $scope.departmentMap = {};
                comService.getOrgChildren('D010053').then(function(data) {
                	 angular.forEach(data.data,function (dep) {
                         $scope.departmentMap[dep.orgName] = dep.orgCode;
                     });
                });
                //设置统计月份
                var date = new Date();

                var startDate = new Date(date.getFullYear(), date.getMonth()-1, 1);
                $scope.formRefer.startTime = inform.format(startDate,"yyyy-MM");

                var endDate = new Date(date.getFullYear(), date.getMonth(), 0);
                $scope.formRefer.endTime = inform.format(endDate,"yyyy-MM");

                var urlData = {
                    'startTime': inform.format(startDate,"yyyy-MM-dd"),
                    'endTime': inform.format(endDate,"yyyy-MM-dd")
                };

                nonWorkDaysService.getWorkDaysInTimeQuantum(urlData)
                    .then(
                        function (data) {
                            if (data.code === AgreeConstant.code) {
                                $scope.formRefer.workDay = data.data;
                            } else {
                                //查询不到间隔时间长度时 使用 月数*默认每月工作天数
                                $scope.formRefer.workDay = inform.monthBetween($scope.formRefer.startTime, $scope.formRefer.endTime) * $scope.workDayMonthly;
                            }
                            getData();
                        },
                        function (error) {
                            inform.common(Trans("tip.requestError"));
                        }
                    );



        	}

        	$scope.updateWorkDay = function() {

                $scope.formRefer.startTime.split("-");
                var startMonth = $scope.formRefer.startTime.split("-")[1];
                var startDate = new Date(parseInt($scope.formRefer.startTime.split("-")[0]), parseInt(startMonth) - 1, 1);

                var endMonth = $scope.formRefer.endTime.split("-")[1];
                var endDate = new Date(parseInt($scope.formRefer.endTime.split("-")[0]), parseInt(endMonth), 0);

                var urlData = {
                    'startTime': inform.format(startDate,"yyyy-MM-dd"),
                    'endTime': inform.format(endDate,"yyyy-MM-dd")
                };

                nonWorkDaysService.getWorkDaysInTimeQuantum(urlData)
                    .then(
                        function (data) {
                            if (data.code === AgreeConstant.code) {
                                $scope.formRefer.workDay = data.data;
                            } else {
                                //查询不到间隔时间长度时 使用 月数*默认每月工作天数
                                $scope.formRefer.workDay = inform.monthBetween($scope.formRefer.startTime, $scope.formRefer.endTime) * $scope.workDayMonthly;
                            }
                        },
                        function (error) {
                            inform.common(Trans("tip.requestError"));
                        }
                    );

            }

        	$scope.reset = function(){
        		var date = new Date();
                date.setDate(1);
                date.setMonth(date.getMonth()-1);
                $scope.formRefer.startTime = inform.format(date,"yyyy-MM");
                $scope.formRefer.endTime = inform.format(date,"yyyy-MM");
                //每月工作天数
                $scope.workDayMonthly = 22;
                $scope.updateWorkDay();
        	}
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
                $("#divTBDis1").height(divHeight);
                $("#subDivTBDis1").height(divHeight - 50);
            }
            /**
             * 获取部门与产品线信息
             */
            function getData() {
            	if (Number.isNaN($scope.formRefer.workDay)||$scope.formRefer.workDay==null||$scope.formRefer.workDay===''||$scope.formRefer.workDay<=0){
            		inform.common("请输入正确工作日");
            		return;
            	}
                var urlData = {
                    'startTime': $scope.formRefer.startTime,
                    'endTime': $scope.formRefer.endTime,
                    'workDay': $scope.formRefer.workDay
                };
                workingHoursManagementService.getData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                        	//产品线情况报告
        					$scope.lineData = data.data.lineData;
                            //部门报告
                            $scope.depData = data.data.depData;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            /**
             * 下载汇总信息
             */
            $scope.toExcel = function () {
            	if ($scope.formRefer.workDay==null||$scope.formRefer.workDay===''){
            		inform.common("请输入正确工作日");
            		return;
            	}
                var urlData = {
                    'startTime': $scope.formRefer.startTime,
                    'endTime': $scope.formRefer.endTime,
                    'workDay': $scope.formRefer.workDay
                };
            	inform.modalInstance("确定要下载吗?").result.then(function () {
                	inform.downLoadFile('workingHoursManagement/toExcel',urlData,'部门与产品线工时统计.xlsx');
            	});
            }
            /**
             * 获取查询时间段
             */
            $scope.toDetails= function(flag,unit){
            	var startMonth = $scope.formRefer.startTime.split('-');
            	var firstDay = inform.format(new Date(startMonth[0],startMonth[1]*1-1,1),"yyyy-MM-dd");
            	var endMonth = $scope.formRefer.endTime.split('-');
            	var lastDay = inform.format(new Date(endMonth[0],endMonth[1],0),"yyyy-MM-dd");
            	if (flag==='person'){
            		unit = $scope.departmentMap[unit];
            	}
            	if (flag==='project'){
            		unit = $scope.lineMap[unit];
            	}
            	var urlData={
            		'startTime':firstDay,
            		'endTime':lastDay,
            		'unit':unit
            	}
            	LocalCache.setObject('ManagementHours_formRefer', urlData);
            	if (flag==='person'){
            		$state.go('app.office.personHours');
            	}
            	if (flag==='project'){
            		$state.go('app.office.projectHours');
            	}
            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();

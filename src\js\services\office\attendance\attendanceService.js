/*
 * @Author: dongyinggang
 * @Date:   2019-08-28 10:11:00
 * @Last Modified by:   dongyinggang
 * @Last Modified time:  2019-08-28 10:11:00
 */
(function() {
    'use strict';
    app.factory('attendanceService', attendanceService);
    attendanceService.$inject=["HttpService",'$rootScope'];

    function attendanceService(HttpService,$rootScope){

        var service={

            getAttendanceByMap:getAttendanceByMap,
            selectAttendanceInfo:selectAttendanceInfo


        };
        return service;

        /**
         * 分页查询员工考勤统计
         */
        function getAttendanceByMap(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'attendance/getAttendanceByMap', urlData);
        }

        /**
         * 查询平均延迟打卡工时，平均平日延迟打卡工时，平均周末延迟打卡工时
         */
        function selectAttendanceInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'attendance/selectAttendanceInfo', urlData);
        }



    }
})();
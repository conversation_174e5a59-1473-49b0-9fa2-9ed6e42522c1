(function() {
    'use strict';
    app.factory('projectWorkingBoardService', projectWorkingBoardService);
    projectWorkingBoardService.$inject=["HttpService",'$rootScope'];

    function projectWorkingBoardService(HttpService,$rootScope){
        function getProjectHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptWorkingHours/getProjectHoursInfo', urlData);
        }

        return {
            getProjectHoursInfo: getProjectHoursInfo,
        };
    }
})();
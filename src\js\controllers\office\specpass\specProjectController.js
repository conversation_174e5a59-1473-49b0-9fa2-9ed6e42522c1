
(function() {
	app.controller("specprojectManagement", ['comService', '$rootScope', '$stateParams','$scope','projectManagementService', 'specPassService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
		function(comService, $rootScope,$stateParams, $scope, projectManagementService,specPassService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {
		
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		$scope.projectData = [];//项目情况报告

		$scope.formRefer = {};//页面查询条件
		$scope.formRefer.cname = '';
		$scope.formRefer.productLine = '';
		$scope.formRefer.type = '';
		
		//页面分页信息
		$scope.pages = {
			pageNum : '',	//分页页数
			size : '',		//分页每页大小
			total : ''		//数据总数
		};
		$scope.pages = inform.initPages(); // 初始化分页数据

		//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		//开发模型下拉框数据源
        $scope.typeSelect = [{
            value: '瀑布',
            label: '瀑布'
        },{
            value: '敏捷',
            label: '敏捷'
        }];
		//初始化页面信息
    	initPages();
    	$scope.getData = getData; 
    	getData($scope.pages.pageNum);
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */	
    	
    	/**
    	 * 设置列表的高度
    	 */
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 185);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 50);
 		}
    	/**
    	 * 页面初始化
    	 */
    	function initPages() {
    		//获取产品线
    		$scope.productLineList = [];
    		comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.projectLine =  data.data;
        		}
            });
    	}
    	/**
    	 * 获取项目
    	 */
		function getData(pageNum) {
			if ($scope.formRefer.cname==='' ) {
				$scope.formRefer.cname = $stateParams.cname;
			}
			if ($scope.formRefer.productLine==='' ) {
				$scope.formRefer.productLine = $stateParams.productLine;
			}
			if ($scope.formRefer.type==='' ) {
				$scope.formRefer.type = $stateParams.type;
			}
			var urlData ={
	                'cname':$scope.formRefer.cname,
	                'productLine':$scope.formRefer.productLine,
	                'type':$scope.formRefer.type,
	                'page':pageNum,
	                'pageSize':$scope.pages.size
				};
				projectManagementService.getProjectInfo(urlData).then(function(data) {
					if (data.code===AgreeConstant.code) {
	                    //项目报告
	                    $scope.projectData = data.data.list;
	                    // 分页信息设置
	                    $scope.pages.total = data.data.total;           // 页面数据总数
	                    $scope.pages.star = data.data.startRow;         // 页面起始数
	                    $scope.pages.end = data.data.endRow;            // 页面结束数
	                    $scope.pages.pageNum = data.data.pageNum;       //页号
					} else {
						inform.common(data.message);
					}
				},
				function(error) {
					inform.common(Trans("tip.requestError"));
				});
		}
    	
		/**
      	 * 报表界面切换(详情)
      	 */
     	$scope.details=function (item) {
                $state.go('app.office.specDetailManagement',{
                	project    :item.id,
                	cname      :$scope.formRefer.cname,
                	productLine:$scope.formRefer.productLine,
                	type       :$scope.formRefer.type
                });
        };
 
	    /**
	     * *************************************************************
	     *              方法声明部分                                结束
	     * *************************************************************
	     */	
	      
		} ]);
})();
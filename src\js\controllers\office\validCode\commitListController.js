//# sourceURL=js/controllers/office/validCode/commitListController.js
(function () {
    app.controller("commitList", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','commitListService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,commitListService,inform,Trans,AgreeConstant,LocalCache,$http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //分页
            $scope.pages = inform.initPages(); // 初始化分页数据
            $scope.type = $stateParams.type;
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);


            
            //获取缓存
            $scope.formRefer = LocalCache.getObject('commitList_formRefer');
            //对原缓存进行覆盖
            LocalCache.setObject('commitList_formRefer', {});

                    
            $scope.getData = getData; 			// 分页相关函数
            getData();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */


          
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 175);
                $("#divTBDisTab").height(divHeight);
                $("#subDivTBDisTab").height(divHeight - 50);
            }


            $scope.resetParam = function(){
                var date = new Date();
                date.setMonth(date.getMonth() - 1);
                $scope.formRefer.startTime = inform.format(date, 'yyyy-MM-dd');
                $scope.formRefer.endTime = '';
                $scope.formRefer.proProjectName = '';
                $scope.formRefer.productLine = '';
                $scope.formRefer.confirmSheetStatus = '';
                $scope.formRefer.creator = '';
            }
            /**
             * 获取提交明细
             */
            function getData(pageNum) {
                var urlData ={
                    'loginName': $stateParams.pushUser,//提交人
                    'startTime': inform.format($stateParams.startTime, 'yyyy-MM-dd'),//开始时间
                    'endTime': inform.format($stateParams.endTime, 'yyyy-MM-dd'), //结束时间
                    'page': pageNum,//当前页数
                    'pageSize': $scope.pages.size//每页显示条数
                };
                commitListService.getPersonalCommitList(urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        //测试报告集合
                        $scope.commitData = data.data.list;
                        if ($scope.commitData.length===0) {
                            $scope.pages = inform.initPages(); 			//初始化分页数据
                            inform.common(Trans("tip.noData"));
                        } else {
                        // 分页信息设置
                            $scope.pages.total = data.data.total;           // 页面数据总数
                            $scope.pages.star = data.data.startRow;         // 页面起始数
                            $scope.pages.end = data.data.endRow;            // 页面结束数
                            $scope.pages.pageNum = data.data.pageNum;       //页号
                        }
                    } else {
                        inform.common(data.message);
                    }
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }    
        
            
           
            /**
             * 重置
             */
            $scope.reset = function() {
                $scope.formRefer={};
                initTime();
            }

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
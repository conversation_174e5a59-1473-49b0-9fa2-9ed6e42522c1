(function() {
    'use strict';
  app.factory('proProjectModule', proProjectModule);
  proProjectModule.$inject=["comService","AgreeConstant","inform","Trans"];

  function proProjectModule(comService,AgreeConstant,inform,Trans){
    var service={
		 initModule:initModule
    };
    return service;

    //回调方法
    var proProjectModuleBack = null;
    //scope
    var proProjectModuleScope = null;
    /**
     * 初始化产品线与禅道项目弹框
     *  参数结构
     * dataInfo= {productLine:'产品线id', keyWord:'平台项目名称' }
     */
    function initModule(dataInfo,scope,callback) {
        if(dataInfo.keyWord){
            document.getElementById("proProjectName").value=dataInfo.keyWord;
        }

    	//查禅道产品线
		scope.proProjectLine = [];
		//获取项目
		scope.getProProject = getProProject;
		//选择项目
        scope.selectProProject = selectProProject;
        //选中产品线
        scope.selectProducLine =selectProducLine;
        //回调方法
        proProjectModuleBack = callback;
        proProjectModuleScope = scope;
        comService.getParamList('PRO_PRODUCT_TYPE','PRO_PRODUCT_TYPE').then(function (data) {
			if (data.data) {
			    scope.proProjectLine = data.data;
			    getProProject(dataInfo);
			    $("#proProject").click();
			}
		});
    }

    /**
     * 选中产品线与项目关键字触发的事件
     */
     function selectProducLine(line){
           var paramInfo={
                'productLine':line,
                'keyWord':document.getElementById("proProjectName").value
            }
         getProProject(paramInfo);
     }
    /**
     * 根据产品线与项目关键字查询符合的禅道项目
     */
     function getProProject(dataInfo){

        if(!dataInfo.keyWord || dataInfo.keyWord.length ==0) {
            dataInfo.keyWord =document.getElementById("proProjectName").value;
        }
        //修改被选中的字体颜色
        proProjectModuleScope.proProjectLine.forEach(function (ele) {ele.active=ele.param_code===dataInfo.productLine;})
        proProjectModuleScope.proProjectModuleData = [];
        //获取关键字与产品线下的禅道项目
        comService.getZentaoProject(dataInfo).then(function(data) {
            if (data.code===AgreeConstant.code) {
                angular.forEach(data.data, function(res, index) {
                    var json = {
                        id: res.id,//执行ID
                        name: res.NAME,//执行名称
                        parentId:res.parentId//项目id
                    };
                    proProjectModuleScope.proProjectModuleData.push(json);
                });
            } else {
                inform.common(data.message);
            }
        }, function() {
            inform.common(Trans("tip.requestError"));
        });
     }
     /**
      * 选择项目
      */
      function selectProProject(m){
        $("#proProjectModule").modal("hide");
        proProjectModuleBack(m);
      }
  }
})();
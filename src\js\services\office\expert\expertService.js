(function() {
    'use strict';
  app.factory('expertService', expertService);
  expertService.$inject=["HttpService",'$rootScope'];

  function expertService(HttpService,$rootScope){
    
	var service={
			selectData:selectData,
			delData:delData,
			addInfo:addInfo,
			upInfo:upInfo,
			selectOne:selectOne,
			getTerritoryList:getTerritoryList,
            selectPersonInfoForCompanyTitle:selectPersonInfoForCompanyTitle
	};
    return service;

    /**
     * 获取所有的信息
     */
    function selectData(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'expertDatabase/selectData', urlData);
    }
    /**
     * 删除信息
     */
    function delData(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'expertDatabase/delData', urlData);
    }
    /**
     * 添加信息
     */
    function addInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'expertDatabase/addInfo', urlData);
    }
    /**
     * 更新信息
     */
    function upInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'expertDatabase/upInfo', urlData);
    }
    /**
     * 回填信息
     */
    function selectOne(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'expertDatabase/selectOne', urlData);
    }
    /**
     * 获取行业领域
     */
    function getTerritoryList(){
        return HttpService.post($rootScope.getWaySystemApi + 'expertDatabase/getTerritoryList');
    }

    /**
    * 通过行业领域类获取技术人才信息
    */
    function selectPersonInfoForCompanyTitle(urlData){
      return HttpService.post($rootScope.getWaySystemApi + 'expertDatabase/selectPersonInfoForCompanyTitle', urlData);
    }


  }
})();
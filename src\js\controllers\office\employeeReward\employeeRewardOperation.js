(function() {
    app.controller("employeeRewardOperation", ['employeeRewardService', 'comService', '$rootScope', '$scope', '$state', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function(employeeRewardService, comService, $rootScope, $scope, $state, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置

            $scope.formInsert = [];//新增参数
            employeeSelect();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
            }
            /**
             * 获取获奖人员信息列表
             */
            function employeeSelect() {
                //获取获奖人员信息列表,获取产品线
                $scope.employeeList = [];
                comService.getEmployeesName().then(function(data) {
                    $scope.employeeList = angular.fromJson(data.data);
                    rewardSelect();
                });
            }
            /**
             *  奖项列表
             */
            function rewardSelect() {
                $scope.rewardList = [];
                comService.getParamList('EMPLOYEE_REWARD', 'EMPLOYEE_REWARD').then(function(data) {
                    $scope.rewardList = angular.fromJson(data.data);
                    //新增列表
                    $scope.rewardAddList = [];
                    $scope.addNewBind();
                });
            }
            /**
             *  新增界面
             */
            $scope.addReward = function() {
                var urlData = {
                    "rewardAddList": JSON.stringify($scope.rewardAddList)
                };
                if ($scope.rewardAddList.length === 0) {
                    inform.common("请输入明细");
                    return;
                }
                employeeRewardService.addReward(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        layer.confirm(data.message, {
                            title: false,
                            btn: ['确定']
                        }, function(result) {
                            layer.close(result);
                            $scope.goback();
                        });
                    } else if (data.code === "0004") {
                        $("#add_modal").modal('hide');
                        var modalInstance = $modal.open({
                            templateUrl: 'myModalContent.html',
                            controller: 'ModalInstanceCtrl',
                            size: "sm",
                            resolve: {
                                items: function() {
                                    return "实例号已存在，请重新填写！";
                                }
                            }
                        });
                        modalInstance.result.then(function() {})
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 列表新增一行
             */
            $scope.addNewBind = function() {
                var rewardVO = {
                    "rewardTypeCode": "", //奖项
                    "rewardName": "", //奖项名称
                    "date": "", //获奖时间  
                    "bounty": "", //奖金
                    "nameList": "", //所选员工id
                    "cause": "", //获得奖项原因
                    "mark": "" //备注
                };
                $scope.rewardAddList.push(rewardVO);
            }
            /**
             * 列表取消一行
             */
            $scope.deleteNewBind = function(index) {
                $scope.rewardAddList.splice(index, 1);
            }
            $scope.goback = function() {
                $state.go("app.office.employeeReward");
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }
    ]);
})();
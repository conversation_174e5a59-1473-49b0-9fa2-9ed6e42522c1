(function () {
    app.controller('repositoryPermissionMain', [
        'comService',
        '$http',
        '$rootScope',
        '$state',
        '$scope',
        '$modal',
        'repositoryPermissionMainService',
        'inform',
        'Trans',
        'AgreeConstant',
        'LocalCache',
        '$stateParams',
        function (
            comService,
            $http,
            $rootScope,
            $state,
            $scope,
            $modal,
            repositoryPermissionMainService,
            inform,
            Trans,
            AgreeConstant,
            LocalCache,
            $stateParams
        ) {
            $scope.formRefer = LocalCache.getObject('formRefer') || {}; //仓库查询条件

            $scope.type = '1';
            $scope.changeType = function changeType(type, item) {
                $scope.type = type;
                if (type === '1') {
                    $scope.formRefer = {};
                    LocalCache.setObject('formRefer', {});
                    $state.go('app.office.repositoryPermissionMain', { type: '1' });
                } else if (type === '2') {
                    $scope.formRefer = {};
                    LocalCache.setObject('formRefer', {});
                    $state.go('app.office.repositoryPermissionMain', { type: '2' });
                }
            };

            $scope.repositoryTypeList = [
                {
                    value: 'svn',
                    label: 'svn',
                },
                {
                    value: 'git',
                    label: 'git',
                },
            ];

            $scope.repositoryStatusList = [
                {
                    value: '进行中',
                    label: '进行中',
                },
                {
                    value: '权限回收',
                    label: '权限回收',
                },
                {
                    value: '权限维护',
                    label: '权限维护',
                },
            ];

            $scope.dealStatusList = [
                {
                    value: '已处理',
                    label: '已处理',
                },
                {
                    value: '未处理',
                    label: '未处理',
                },
            ];
            //初始化按钮权限
            $scope.upModule = false; //维护模块
            /**
             * 结果
             */
            //被勾选的集合
            $scope.selecteds = [];
            //全选状态
            $scope.select_all = false;
            // 初始化分页数据
            $scope.pages = inform.initPages();
            //查询页面
            $scope.getData = getData;
            //全选方法
            $scope.selectAll = selectAll;
            //单选方法
            $scope.selectOne = selectOne;

            //刷新页面时就执行getData($scope.pages.pageNum)方法
            getData($scope.pages.pageNum);
            setDivHeight(); //设置列表的高度

            $(window).resize(setDivHeight); //窗体大小变化时重新计算高度
            $(window).resize(showDataTable);
            //判断按钮是否具有权限
            getButtonPermission();
            //初始化页面信息
            initPages();

            /**
             * 页面初始化
             */
            function initPages() {
                $scope.selecteds = [];
                //初始化选中状态为非选中
                angular.forEach($scope.repositoryList, function (r) {
                    r.checked = false;
                });
                //获取员工信息
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function (data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                    }
                });
                //获取产品线
                $scope.productLineList = [];
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.productLineList = data.data;
                    }
                });
                //获取部门
                $scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function (data) {
                    $scope.departmentList = comService.getDepartment(data.data);
                });
                // 不活跃时间列表
                $scope.inactiveTimeList = [
                    {
                        value: '30',
                        label: '1个月',
                    },
                    {
                        value: '60',
                        label: '2个月',
                    },
                    {
                        value: '90',
                        label: '3个月',
                    },
                    {
                        value: '120',
                        label: '4个月',
                    },
                    {
                        value: '150',
                        label: '5个月',
                    },
                    {
                        value: '180',
                        label: '6个月',
                    },
                    {
                        value: '181',
                        label: '6个月以上',
                    },
                ];
                // 如果产品线项初始状态有值，需要自动获取产品线下的产品类别
                if ($scope.formRefer.productline) {
                    getProductTypeList();
                }
            }

            $scope.getProductTypeList = getProductTypeList;
            function getProductTypeList() {
                $scope.productTypeList = [];
                comService.getParamList('PRODUCT_TYPE', $scope.formRefer.productline).then(function (data) {
                    if (data.data) {
                        $scope.productTypeList = data.data;
                    }
                });
            }

            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $('#divTBDis').height(divHeight);
                $('#subDivTBDis').height(divHeight - 85);
            }

            /**
             * 权限管理
             */
            function getButtonPermission() {
                var buttons = {
                    'Button-SvnRepositoryManage-upModule': 'upModule', //维护模块
                };
                var urlData = {
                    userId: LocalCache.getSession('userId'),
                    parentPermission: 'Button-SvnRepositoryManage',
                    buttons: buttons,
                };
                comService.getButtonPermission(urlData, $scope);
            }

            /**
             * 根据分页显示仓库
             * pageNum 当前页数
             */
            function getData(pageNum) {
                //销毁表格
                $scope.showDataTable = 0;

                $scope.selecteds = []; //初始化被勾选的集合

                LocalCache.setObject('formRefer', $scope.formRefer);

                //返回的仓库信息列表
                $scope.repositoryList = [];
                // 仓库活跃度查询
                $scope.repositoryActiveList = [];

                // 查询条件
                var urlData = {};
                if ($scope.type === '1') {
                    if (LocalCache.getObject('formRefer')) {
                        $scope.formRefer = LocalCache.getObject('formRefer'); // 初始化查询数据
                    } else {
                        $scope.formRefer = {
                            teamLeader: '', //项目经理/Team Leader
                            productType: '', //产品类型
                            repositoryname: '', //仓库名
                            productline: '', //产品线
                            repositoryType: '', //仓库类型
                            repositoryStatus: '', //仓库状态
                            projectname: '', //项目名称
                            startTime: '', //开始时间
                            department: '', //所属部门
                            endTime: '', //结束时间
                            remarks: '', //备注
                        }; // 初始化查询数据
                    }
                    //存查询条件
                    urlData = {
                        productLine: $scope.formRefer.productline, //产品线
                        repositoryType: $scope.formRefer.repositoryType, //仓库种类
                        repositoryName: $scope.formRefer.repositoryname, //仓库名
                        teamLeader: $scope.formRefer.teamLeader, //项目经理/Team Leader
                        startTime: $scope.formRefer.startTime, //开始时间
                        endTime: $scope.formRefer.endTime, //结束时间
                        team: $scope.formRefer.projectname, //项目名
                        department: $scope.formRefer.department, //所属部门
                        repositoryStatus: $scope.formRefer.repositoryStatus, //仓库状态
                        remarks: $scope.formRefer.remarks, //备注
                        productType: $scope.formRefer.productType, //产品类型
                        currentPage: pageNum, //当前页数
                        pageSize: $scope.pages.size,
                    };
                } else if ($scope.type === '2') {
                    if (LocalCache.getObject('formRefer')) {
                        $scope.formRefer = LocalCache.getObject('formRefer'); // 初始化查询数据
                    } else {
                        $scope.formRefer = {
                            teamLeader: '', //项目经理/Team Leader
                            repositoryname: '', //仓库名
                            productline: '', //产品线
                            repositoryStatus: '', //仓库状态
                            projectname: '', //项目名称
                            inactiveTime: '', //不活跃时间
                            department: '', //所属部门
                        }; // 初始化查询数据
                    }
                    //存查询条件
                    urlData = {
                        productLine: $scope.formRefer.productline, //产品线
                        repositoryName: $scope.formRefer.repositoryname, //仓库名
                        repositoryType: $scope.formRefer.repositoryType, //仓库种类
                        teamLeader: $scope.formRefer.teamLeader, //项目经理/Team Leader
                        department: $scope.formRefer.department, //所属部门
                        repositoryStatus: $scope.formRefer.repositoryStatus, //仓库状态
                        team: $scope.formRefer.projectname, //项目名
                        notActiveTime: $scope.formRefer.inactiveTime, //不活跃时间
                        currentPage: pageNum, //当前页数
                        pageSize: $scope.pages.size,
                    };
                }

                var dateFlag = 1;
                if (!$scope.formRefer.endTime) {
                    dateFlag = 0;
                } else {
                    if ($scope.formRefer.endTime) {
                        dateFlag = $scope.formRefer.startTime >= $scope.formRefer.endTime ? 1 : 0;
                    }
                }

                /**
                 * 获取数据
                 * @param data
                 */
                function getPageData(data, type) {
                    //重新生成Table
                    $scope.showDataTable = 1;
                    if (data.code === AgreeConstant.code) {
                        var jsonData = data.data;
                        if (type === 'active') {
                            $scope.repositoryActiveList = angular.fromJson(data.data.list);
                        } else {
                            $scope.repositoryList = angular.fromJson(data.data.list);
                        }
                        if (!type && $scope.repositoryList.length === 0) {
                            inform.common(Trans('tip.noData'));
                            $scope.pages = inform.initPages();
                        } else if (type === 'active' && $scope.repositoryActiveList.length === 0) {
                            inform.common(Trans('tip.noData'));
                            $scope.pages = inform.initPages();
                        } else {
                            //分页信息设置
                            $scope.pages.total = jsonData.total;
                            $scope.pages.star = jsonData.startRow;
                            $scope.pages.end = jsonData.endRow;
                            $scope.pages.pageNum = jsonData.pageNum;
                        }
                    } else {
                        inform.common(data.message);
                    }

                    setTimeout(showDataTable, 500);
                }

                // 获取数据
                if (dateFlag === 0) {
                    if ($scope.type === '1') {
                        repositoryPermissionMainService.findRepositoryListByPage(urlData).then(
                            function (data) {
                                return getPageData(data);
                            },
                            function (error) {
                                inform.common(Trans('tip.requestError'));
                            }
                        );
                    } else if ($scope.type === '2') {
                        repositoryPermissionMainService.findRepositoryActiveListByPage(urlData).then(
                            function (data) {
                                // 仓库活跃度查询
                                return getPageData(data, 'active');
                            },
                            function (error) {
                                inform.common(Trans('tip.requestError'));
                            }
                        );
                    }
                } else {
                    inform.common(Trans('创建时间应小于截止时间'));
                }
            }

            /**
             *调用DataTable组件冻结表头和左侧及右侧的列
             */
            function showDataTable() {
                $('#fixedLeftAndTop').DataTable({
                    //可被重新初始化
                    retrieve: true,
                    //自适应高度
                    scrollY: 'calc(100vh - 370px)',
                    scrollX: true,
                    scrollCollapse: false,
                    //控制每页显示
                    paging: false,
                    //冻结列（默认冻结左1）
                    // fixedColumns: {
                    // 	leftColumns: 4,
                    // 	rightColumns: 1
                    // },
                    //search框显示
                    searching: false,
                    //排序箭头
                    ordering: false,
                    //底部统计数据
                    info: false,
                });
            }

            /**
             * 开始时间
             */
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true; //开始时间
                $scope.openedEnd = false;
            };

            /**
             *  结束时间
             */
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true; //结束时间
            };
            /**
             * 下载Excel
             */
            $scope.excelData = function (repositoryType) {
                var urlData = {};
                if ($scope.type === '1') {
                    urlData = {
                        productLine: $scope.formRefer.productline, //产品线
                        repositoryType: $scope.formRefer.repositoryType, //仓库种类
                        repositoryName: $scope.formRefer.repositoryname, //仓库名
                        teamLeader: $scope.formRefer.teamLeader, //项目经理/Team Leader
                        startTime: $scope.formRefer.startTime, //开始时间
                        endTime: $scope.formRefer.endTime, //结束时间
                        team: $scope.formRefer.projectname, //项目名
                        department: $scope.formRefer.department, //所属部门
                        repositoryStatus: $scope.formRefer.repositoryStatus, //仓库状态
                        remarks: $scope.formRefer.remarks, //备注
                        productType: $scope.formRefer.productType, //产品类型
                    };
                } else if ($scope.type === '2') {
                    urlData = {
                        productLine: $scope.formRefer.productline, //产品线
                        repositoryName: $scope.formRefer.repositoryname, //仓库名
                        repositoryType: $scope.formRefer.repositoryType, //仓库种类
                        teamLeader: $scope.formRefer.teamLeader, //项目经理/Team Leader
                        department: $scope.formRefer.department, //所属部门
                        repositoryStatus: $scope.formRefer.repositoryStatus, //仓库状态
                        team: $scope.formRefer.projectname, //项目名
                        notActiveTime: $scope.formRefer.inactiveTime, //不活跃时间
                    };
                }
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent',
                    controller: 'ModalInstanceCtrl',
                    size: 'sm',
                    resolve: {
                        items: function () {
                            return '是否确定下载？';
                        },
                    },
                });
                modalInstance.result.then(function () {
                    if ($scope.type === '1') {
                        inform.downLoadFile('storageManage/toStoragePowerExcel', urlData, '仓库权限信息表.xlsx');
                    } else if ($scope.type === '2') {
                        inform.downLoadFile('storageManage/activeDataExcel', urlData, '仓库活跃度信息表.xlsx');
                    }
                });
            };

            /**
             * 全选
             */
            function selectAll() {
                $scope.select_all = !$scope.select_all;
                if ($scope.select_all) {
                    $scope.selecteds = [];
                    angular.forEach($scope.repositoryList, function (i) {
                        i.checked = true;
                        $scope.selecteds.push(i);
                    });
                } else {
                    angular.forEach($scope.repositoryList, function (i) {
                        i.checked = false;
                    });
                    $scope.selecteds = [];
                }
            }

            /**
             * 单选
             */
            function selectOne(i) {
                i.checked = !i.checked;
                $scope.select_all = false;
                var index = $scope.selecteds.indexOf(i);
                if (index === -1 && i.checked) {
                    $scope.selecteds.push(i);
                } else if (index !== -1 && !i.checked) {
                    $scope.selecteds.splice(index, 1);
                }
            }

            // 跳转仓库权限详情
            $scope.toRepositoryDetail = function (id, type) {
                $state.go('app.office.repositoryPermissionDetail', {
                    repositoryid: id,
                    lastPage: 'repositoryPermission',
                    repositoryType: type,
                });
            };
            // 跳转仓库活跃度详情
            $scope.toActivityDetail = function (id, type) {
                $state.go('app.office.repositoryActivityDetail', { repositoryid: id, repositoryType: type });
            };

            $scope.$watch('$viewContentLoaded', function () {
                if ($stateParams.type) {
                    $scope.type = $stateParams.type;
                } else {
                    $scope.type = '1';
                }
                getData($scope.pages.pageNum);
            });
        },
    ]);
})();

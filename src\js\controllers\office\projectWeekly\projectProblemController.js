//# sourceURL=js/controllers/office/projectWeekly/projectProblemAddController.js
(function() {
    app.controller("projectProblemController", ['projectProblemService', '$state', 'comService', '$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$stateParams', '$modal', '$http', '$state',
        function(projectProblemService, state, comService, $rootScope, $scope, inform, Trans, AgreeConstant, LocalCache, $stateParams, $modal, $http, $state) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //页面分页信息
            $scope.pages = {
                pageNum: '', //分页页数
                size: '100', //分页每页大小
                total: '' //数据总数
            };
            $scope.problemContentDownloadFlagMap = {
                "0":'下载',
                "1":'不下载'
            };
            //获取数据
            getData(1);
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 130);
                $("#divTBDis2").height(divHeight);
                $("#subDivTBDis2").height(divHeight - 50);
            }
            /**
             * 获取项目
             */
            function getData(pages) {
                var urlData = {
                    'projectId': $stateParams.projectId,
                    'page': pages,
                    'pageSize': $scope.pages.size
                };
                projectProblemService.getProjectProblemInfo(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        //项目详情
                        $scope.jsonData = data.data.list;
                        // 分页信息设置
                        $scope.pages.total = data.data.total;
                        $scope.pages.star = data.data.startRow;
                        $scope.pages.end = data.data.endRow;
                        $scope.pages.pageNum = data.data.pageNum;
                        //设置列表的高度
                        setDivHeight();
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 新增
             */
            $scope.add = function() {
    			$state.go("app.office.projectProblemAddController", {projectId: $stateParams.projectId,type:null});
            };
            /**
             * 修改
             */
            $scope.up = function(m) {
            	LocalCache.setObject('projectProblem_item',m.id);
    			$state.go("app.office.projectProblemAddController", {projectId: $stateParams.projectId,type:"up"});
            };
            /**
             * 删除数据
             */
            $scope.remove = function(item) {
                //删除弹出框
                inform.modalInstance(Trans("common.deleteTip")).result.then(function() {
                    projectProblemService.deleteProjectProblemInfo(item).then(function(data) {
                        if (data.code === "0000") {
                            inform.common(Trans("tip.delSuccess"));
                            getData(AgreeConstant.pageNum);
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
                });
            };
            /**
             * 下拉时间框
             */
            $scope.requestTimeUp = {};
            $scope.identifyTimeUp = {};
            //计划按钮
            $scope.identifyTimeUp = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.identifyTimeUp.identifyTime = true;
                $scope.requestTimeUp.requestTime = false;
            };
            // 实际完成时间按钮
            $scope.requestTimeUp = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.identifyTimeUp.identifyTime = false;
                $scope.requestTimeUp.requestTime = true;
            };
        /**
		 * 跳转反馈问题界面
		 */
		$scope.goToFeedBackProblem = function() {

		    var param = {"projectName":LocalCache.getObject('projectWeekly_projectName')};
		    //保存projectid 作为返回的参数
		     LocalCache.setSession('projectWeekly_projectId',$stateParams.projectId);
		    //保存调整的查询条件
		    LocalCache.setObject('feedbackProblem_formRefer',param);
			$state.go("app.office.feedbackProblem");

		};
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        }
    ]);
})();
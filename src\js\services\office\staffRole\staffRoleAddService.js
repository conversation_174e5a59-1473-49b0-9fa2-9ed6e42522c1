
(function() {
    'use strict';
  app.factory('staffRoleAddService', staffRoleAddService);
  staffRoleAddService.$inject=["HttpService",'$rootScope'];

  function staffRoleAddService(HttpService,$rootScope){
    var service={
      addStaffRole:addStaffRole,
      selectStaffRemarks:selectStaffRemarks
    };
    return service;	
    /**
	 * 新增员工角色
	 */
	function addStaffRole(urlData) {
		return HttpService.post($rootScope.getWaySystemApi+'staffRole/addStaffRole',urlData);
	}

  /**
	    * 查询备注
	  */
   function selectStaffRemarks(urlData) {
    return HttpService.post($rootScope.getWaySystemApi+'staffRole/selectStaffRemarks',urlData);
  }
	
	
      
    
	
  }
})();

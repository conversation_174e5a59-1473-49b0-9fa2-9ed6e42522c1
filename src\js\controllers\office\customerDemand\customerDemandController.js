(function () {
    app.controller("customerDemandManagement", ['comService', 'customerDataService', '$rootScope', '$scope', '$modal', 'customerDemandService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function (comService, customerDataService, $rootScope, $scope, $modal, customerDemandService, inform, Trans, AgreeConstant, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */


            //下载工时时间
            $scope.startTime = '';
            $scope.endTime = '';
            //获取当前的登录用户名
            $scope.updateEmployeename = LocalCache.getSession('currentUserName');
            //获取当前登录者的id
            $scope.loginId = LocalCache.getSession('userId');
    	    //系研工作选择的权限
            $scope.changeIsHaveHours=false;
            $scope.formInsert = {
                id: '',
                department:'',         //部门
                documentId: '',//文档编号
                upgradeId: '',//请求升级编号
                documentTitle:'',       //plm主题
                publishStatus:'',//发布状态
                managerReceiveTime: '',//项目经理接收时间
                productLine: '',//产品线id
                finishState: '',//完成情况
                createTime: '',//创建时间
                regenerTime: '',//修改时间
                isHaveHours:'0'//系研是否投入人力
            };
            //完成状态选择
            $scope.finishStateSelect = ['按时完成','延期完成','延期未完成','未到期','临期','退回市场','退回产品经理','不涉及系研','尚未制定计划'];
            //获得当前时间（yyyy-MM）
            $scope.currentDate = $scope.currentDate ? null : inform.format(new Date(), "yyyy-MM");
            //初始化上传时间
            $scope.currentTime = getPreMonth(inform.format(new Date(), 'yyyy-MM-dd'))
            $scope.pages = {
                pageNum: '', 		// 分页页数
                size: '', 			// 分页每页大小
                total: '' 			// 数据总数
            };
            $scope.pages = inform.initPages(); 	// 初始化分页数据
            $scope.getData = getData; 			// 分页相关函数
            $scope.taskList = [];				// 保存所有信息的集合

            $scope.datepicker = {
                currentDate: new Date()
            };
            $scope.updateInfo = updateInfo;     // 修改一条信息
            initTime();
            //初始化页面信息
            initPages();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //在刷新页面时调用该方法
            getData($scope.pages.pageNum);
            //判断是否具有权限
            getButtonPermission();

            //工时信息
            $scope.consumedVlaue = "";
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
             /**
              * 获取系研工作选择的权限
              */
              function getButtonPermission(){
                   var buttons = {
                     'Button-customerDemand-changeIsHaveHours':'changeIsHaveHours'
                   };
                   var urlData = {
                      'userId':LocalCache.getSession("userId"),
                      'parentPermission':'customerDemand',
                      'buttons':buttons
                   };
                   comService.getButtonPermission(urlData,$scope);
              }
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 190);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 55);
            }

            /**
             * 初始化检索条件开始时间 及 结束时间
             */
            function initTime(endDate){
                if (endDate==null || endDate==="" ){
                    $scope.endTime = inform.format(new Date(),'yyyy-MM-dd');//获取当前系统时间
                }
                var time = $scope.endTime.split("-");
                var start = time[0]+"/01"+"/01";
                $scope.startTime = inform.format(start,'yyyy-MM-dd');
                //对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
            }
            //开始时间
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;    //开始时间
                $scope.openedEnd = false;
            };

            //结束时间
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;    //结束时间
            };

            //重置
            $scope.rest = function () {
                $scope.formInsert.department = '';
                $scope.formInsert.productLine = '';
                $scope.formInsert.documentTitle = '';
                $scope.formInsert.publishStatus = '';
                $scope.formInsert.documentId = '';
                $scope.formInsert.finishState = '';
                $scope.formInsert.upgradeId = '';
                $scope.formInsert.isHaveHours = '0';
                initTime();
                $scope.uploadTime = getPreMonth(inform.format(new Date(), 'yyyy-MM-dd'));
            }

            /**
             * 获取产品线code
             */
            function initPages() {
                //获取产品线
                $scope.productLineList = [];
                $scope.productLineListForUpdate = [];
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.productLineListForUpdate = JSON.parse(JSON.stringify(data.data));
                        $scope.productLineList = data.data;
                        var emptyProductLine = {"param_code":"无","param_value":"无","param_desc":"无"};
                        $scope.productLineList.push(emptyProductLine);
                    }
                });

                $scope.employeeList = [];
                comService.getEmployeesName()
                    .then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.employeeList = data.data;
                        }
                    });

				//获取部门
				$scope.departmentList = [];
				$scope.departmentListForUpdate = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentListForUpdate = JSON.parse(JSON.stringify(data.data));
                    $scope.departmentList = comService.getDepartment(data.data);
                    var emptyDepartmentLine = {"orgCode":"无","orgName":"无","orgId":"无"};
                    $scope.departmentList.push(emptyDepartmentLine);
                });
            }
            /**
             * 页面选中的修改信息复制
             */
            $scope.update = function (m) {

                $scope.changeParam = angular.copy(m);
                //设置系研工作反显
                if($scope.changeParam.isHaveHours === '0'){
                    var haveHoursBox = document.getElementById("haveHours");
                    haveHoursBox.checked = true;
                }else {
                    var noHaveHoursBox = document.getElementById("noHaveHours");
                    noHaveHoursBox.checked = true;
                }
            };

            //获取发布关联详情
            $scope.getPublishDetails = function (upgradeId) {
                $scope.publishDetailData = [];
                customerDemandService.getPublishDetails(upgradeId).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        //子产品需求
                        $scope.publishDetailData = data.data;
                    } else {
                        inform.common(data.message);
                    }
                }, function () {
                    inform.common(Trans("tip.requestError"));
                });
            };

            //修改
            function updateInfo(changeParam) {
                $scope.addList = [];
                 if (!changeParam.productLine) {
                    inform.common(Trans("产品线不能为空！"));
                    return false;
                }else if (!changeParam.department) {
                    inform.common(Trans("部门不能为空！"));
                    return false;
                } else {
                    if($scope.changeIsHaveHours){
                    //获取系研工作的选择值
                        var haveHoursBox = document.getElementById("haveHours");
                        if(haveHoursBox.checked){
                            changeParam.isHaveHours = '0';
                        }else {
                            changeParam.isHaveHours = '1';
                            if(!changeParam.remarks){
                                inform.common(Trans("无系研工作时，备注不能为空！"));
                                return false;
                            }
                        }
                    }

                    var urlData = {
                        'documentId': changeParam.documentId,//文档编号
                        'upgradeId': changeParam.upgradeId,//请求升级编号
                        'department': changeParam.department,//部门id
                        'productLine': changeParam.productLine,//产品线id
                        'isHaveHours':changeParam.isHaveHours,//工时信息
                        'remarks':changeParam.remarks,//备注信息
                        'demandSolutionTime': inform.format(changeParam.demandSolutionTime, 'yyyy-MM-dd'),//需求处理完成时间
                        'managerReceiveTime': changeParam.managerReceiveTime,//项目经理接收时间
                        'createDate': changeParam.createDate//创建时间
                    };
                    customerDataService.updateMessage(urlData).then(function (data) {
                            if (data.code === AgreeConstant.code) {
                                $("#edit_modal").modal('hide');
                                inform.common(Trans("信息修改成功！"));
                            } else {
                                $("#edit_modal").modal('hide');
                                inform.common(Trans("信息修改失败！"));
                            }
                            getData(1);
                            $scope.changeParam = {};
                        },
                        function (error) {
                            inform.common(Trans("tip.requestError"));
                        });
                }
            }
            /**
             * 获取所有的数据以分页的形式
             * pageNum 分页页数
             */
            function getData(pageNum) {

                if($scope.startTime == null) {
                    inform.common(Trans("请输入开始时间！"));
                    return false;
                }
                if($scope.endTime == null) {
                    inform.common(Trans("请输入结束时间！"));
                    return false;
                }

                $scope.taskList = [];//保存信息的集合
                $scope.totalList = [];//保存汇总信息的集合

                var start = inform.format($scope.startTime, 'yyyy-MM-dd');
                var end = inform.format($scope.endTime, 'yyyy-MM-dd');

                if (start > end) {
                    inform.common(Trans("上线的结束时间必须大于开始时间！"));
                    return false;
                }
                var stats = '';
                angular.forEach($scope.formInsert.finishState, function (finishState, i) {
                    stats = stats +finishState +',';
                });
                var urlData = {
                    'department':$scope.formInsert.department,//部门
                    'productLine': $scope.formInsert.productLine,  			//产品线名称
                    'documentTitle':$scope.formInsert.documentTitle, //plm主题
                    'documentId': $scope.formInsert.documentId,
                    'finishState': stats,
                    'publishStatus':$scope.formInsert.publishStatus,
                    'personLiable':$scope.formInsert.personLiable,//责任人
                    'upgradeId':$scope.formInsert.upgradeId,
                    'createTime': start,
                    'regenerTime': end,
                    'isHaveHours':$scope.formInsert.isHaveHours,//系研是否投入人力
                    'currentPage': pageNum, 								// 分页页数
                    'pageSize': $scope.pages.size    						// 分页每页大小

                };
                customerDemandService.getCustomerDemand(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            var jsonData = data.data;
                            $scope.taskList = jsonData.list;
                            if ($scope.taskList.length === 0) {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages(); 			//初始化分页数据
                            } else {
                                // 分页信息设置
                                $scope.pages.total = jsonData.total;		// 页面总数
                                $scope.pages.star = jsonData.startRow;  	//页面起始数
                                $scope.pages.end = jsonData.endRow;  		//页面大小数
                                $scope.pages.pageNum = jsonData.pageNum;  	//页面页数
                            }
                            angular.forEach(data.data.list, function (res, i) {
                                if (!res.flowModuleType) {
                                    res.flowModuleType = 0;
                                }
                                if (!res.managerReceiveTime) {
                                    res.managerReceiveTime = 0;
                                }
                                if (!res.develReceiveTime) {
                                    res.develReceiveTime = 0;
                                }
                                if (!res.customerName) {
                                    res.customerName = 0;
                                }
                                if (!res.productLineName) {
                                    res.productLineName = 0;
                                }
                                if (!res.projectManager) {
                                    res.projectManager = 0;
                                }

                            });

                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
                    //获取汇总信息
                    getFinishStateTotalInfo(urlData);
            }

            function getFinishStateTotalInfo(urlData){
                customerDemandService.getFinishStateTotalInfo(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.totalInfo = data.data;
                            if($scope.totalInfo == null){
                                //汇总信息
                                $scope.totalInfo = {
                                    'plmAllTotal':'0',
                                    'finishOnTime':'0',
                                    'timeoutTotal':'0',
                                    'backTotal':'0',
                                    'noXiYan':'0',
                                    'rate':'0'
                                }
                            }else {
                                $scope.totalInfo.timeoutTotal = $scope.totalInfo.delayedCompletion*1 + $scope.totalInfo.delayNotCompleted*1;
                                $scope.totalInfo.backTotal = $scope.totalInfo.returnToMarket*1 + $scope.totalInfo.returnToProductManager*1;
                                var num1 = $scope.totalInfo.finishOnTime*1 + $scope.totalInfo.notDue*1 + $scope.totalInfo.expiringSoon*1;
                                var num2 = $scope.totalInfo.plmAllTotal*1 - $scope.totalInfo.backTotal*1
                                - $scope.totalInfo.noXiYan*1 - $scope.totalInfo.noPlan*1;
                                $scope.totalInfo.rate = (num1/num2*100).toFixed(2);
                            }
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            /**
             * 获取上一个月
             *
             * @date 格式为yyyy-mm-dd的日期，
             */
            function getPreMonth(date) {
                var arr = date.split('-');
                var year = arr[0]; //获取当前日期的年份
                var month = arr[1]; //获取当前日期的月份
                var year2 = year;
                var month2 = parseInt(month) - 1;
                if (month2 === 0) {//如果是1月份，则取上一年的12月份
                    year2 = parseInt(year2) - 1;
                    month2 = 12;
                }

                if (month2 < 10) {
                    month2 = '0' + month2;//月份填补成2位。
                }
                var t2 = year2 + '-' + month2;
                return t2;
            }

            //导出Excel表格
            $scope.toExcel = function () {
                var start = inform.format($scope.startTime, 'yyyy-MM-dd');
                var end = inform.format($scope.endTime, 'yyyy-MM-dd');
                var stats = '';
                angular.forEach($scope.formInsert.finishState, function (finishState, i) {
                    stats = stats +finishState +',';
                });
                var urlData = {
                    'department':$scope.formInsert.department,//部门
                    'productLine': $scope.formInsert.productLine,  			//产品线名称
                    'documentTitle':$scope.formInsert.documentTitle, //plm主题
                    'documentId': $scope.formInsert.documentId,
                    'finishState': stats,
                    'publishStatus':$scope.formInsert.publishStatus,
                    'personLiable':$scope.formInsert.personLiable,//责任人
                    'upgradeId':$scope.formInsert.upgradeId,
                    'createTime': start,
                    'regenerTime': end,
                    'isHaveHours':$scope.formInsert.isHaveHours//系研是否投入人力
                //    'uploadTime': inform.format($scope.uploadTime, 'yyyy-MM')
                };
                inform.modalInstance("确定要下载吗?").result.then(function () {
                    inform.downLoadFile('customerDemand/toExcel', urlData, '客户需求完成情况_'+ inform.format(new Date(), 'yyyy-MM-dd')+'.xlsx');
                });
            };


            //导出Excel表格
            $scope.toPlmExcel = function () {
                var start = inform.format($scope.startTime, 'yyyy-MM-dd');
                var end = inform.format($scope.endTime, 'yyyy-MM-dd');
                var urlData = {
                    'createTime': start,
                    'regenerTime': end
                //    'uploadTime': inform.format($scope.uploadTime, 'yyyy-MM')
                };
                inform.modalInstance("确定要下载吗?").result.then(function () {
                    inform.downLoadFile('plmCustomerDemand/toPlmHoursExcel', urlData, 'PLM客户需求工时信息.xlsx');
                });
            };
            
            $scope.viewConsumed = function (item) {
                customerDataService.viewConsumed(item.upgradeId).then(function (data) {
                    if (data.code === '0000') {
                        item.consumed = data.data;
                    }
                });
            }

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

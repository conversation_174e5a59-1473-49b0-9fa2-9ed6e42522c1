/*
 * @Author: sunqixian
 * @Date:   2019-06-12 10:50:05
 * @Last Modified by:   sunqixian
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
    'use strict';
  app.factory('emergencyLaunchService', emergencyLaunchService);
  emergencyLaunchService.$inject=["HttpService",'$rootScope'];

  function emergencyLaunchService(HttpService,$rootScope){
    
	var service = {
        getEmergencyLaunchList:getEmergencyLaunchList,
        syncEmergencyLaunchFlow:syncEmergencyLaunchFlow
        // getVersionReleaseLog:getVersionReleaseLog,
        // getLine:getLine,
        // downloadExcel:downloadExcel,
        // getRateOfProductLine:getRateOfProductLine,
        // getRateOfDepartment:getRateOfDepartment,
        // deleteInfo:deleteInfo
	};
    return service;

    
    /**
     * 获取紧急上线流程信息
     */
    function getEmergencyLaunchList(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'emergencyLaunch/getEmergencyLaunchList', urlData);
    }

    /**
     * 同步紧急上线流程信息
     */
    function syncEmergencyLaunchFlow(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'emergencyLaunch/syncEmergencyLaunchFlow', urlData);
  }

  }
})();

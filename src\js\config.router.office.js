/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date:   2018-03-19 10:13:58
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-04-04 09:21:17
 */

// 新增路由配置样例
// 项目新增页面的路由配置
// 如公司管理company_Management.html页面的路由配置
(function () {
    'use strict';
    /**
     * 二次开发路由配置
     */
    angular.module('app').config([
        '$stateProvider',
        function ($stateProvider) {
            //下面开始office配置
            $stateProvider

                .state('app.office', {
                    abstract: true,
                    url: '/office',
                    template: '<div ui-view></div>',
                })
                //code
                .state('app.office.codeManagement', {
                    url: '/codeManagement',
                    templateUrl: 'tpl/office/code/codeReport.html',
                    controller: 'codeManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/code/codeController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/code/codeService.js');
                            },
                        ],
                    },
                })
                //codeConfig
                .state('app.office.codeManageConfig', {
                    url: '/codeConfigController',
                    templateUrl: 'tpl/office/code/codeConfig.html',
                    controller: 'codeConfigController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/code/codeConfigController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(['js/services/office/code/codeService.js']);
                            },
                        ],
                    },
                })
                //历史代码质量
                .state('app.office.codeHistoryManagement', {
                    url: '/codeHistoryManagement',
                    templateUrl: 'tpl/office/code/codeReportHistory.html',
                    controller: 'codeHistoryManagement',
                    params: {
                        name: null,
                        cName: null,
                        cManager: null,
                        cType: null,
                        cDepartment: null,
                        riskNote: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/code/codeHistoryController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/code/codeService.js');
                            },
                        ],
                    },
                })
                //tree
                .state('app.office.treeController', {
                    url: '/treeController',
                    templateUrl: 'tpl/office/svnTree/tree.html',
                    controller: 'treeController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/tree/treeController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/tree/treeService.js');
                            },
                        ],
                    },
                })
                //config
                .state('app.office.configController', {
                    url: '/svnConfig',
                    templateUrl: 'tpl/office/config/svnConfig.html',
                    controller: 'configController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/config/configController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/config/svnConfigService.js');
                            },
                        ],
                    },
                })
                //peer
                .state('app.office.peerReviewAction', {
                    url: '/peerReview',
                    templateUrl: 'tpl/office/report/peerReview/peerReview.html',
                    controller: 'peerReviewController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/report/peerReview/peerReviewController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/report/peerReview/peerReviewService.js');
                            },
                        ],
                    },
                })
                //其他部门贡献
                .state('app.office.departmentContribution', {
                    url: '/departmentContribution',
                    templateUrl: 'tpl/office/departmentContribution/departmentContribution.html',
                    controller: 'depContribution',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/departmentContribution/depContribution.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/departmentContribution/depContributionService.js'
                                );
                            },
                        ],
                    },
                })
                //其他部门贡献--添加/修改
                .state('app.office.depContributionOperate', {
                    url: '/depContributionOperate',
                    templateUrl: 'tpl/office/departmentContribution/depContributionOperate.html',
                    controller: 'depContributionOperate',
                    params: {
                        contributionInfoParam: null,
                        isAdd: null, //0:新增；1：修改
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/departmentContribution/depContributionOperate.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/departmentContribution/depContributionService.js'
                                );
                            },
                        ],
                    },
                })
                //培训输出情况汇报
                .state('app.office.trainPlan', {
                    url: '/trainPlan',
                    templateUrl: 'tpl/office/trainPlan/trainPlan.html',
                    controller: 'trainPlanController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/trainPlan/trainPlanController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/trainPlan/trainPlanService.js');
                            },
                        ],
                    },
                })
                //培训输出情况管理页面
                .state('app.office.trainPlanManagement', {
                    url: '/trainPlanManagement',
                    templateUrl: 'tpl/office/trainPlan/trainPlanManagement.html',
                    controller: 'trainPlanManagement',
                    params: {
                        type: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/trainPlan/trainPlanManagement.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/trainPlan/trainPlanService.js');
                            },
                        ],
                    },
                })
                //培训评价详情
                .state('app.office.trainEvaluateView', {
                    url: '/trainEvaluateView',
                    templateUrl: 'tpl/office/trainPlan/trainEvaluateView.html',
                    controller: 'trainEvaluateView',
                    params: {
                        id: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/trainPlan/trainEvaluateView.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/trainPlan/trainPlanService.js');
                            },
                        ],
                    },
                })
                //新增培训详情
                .state('app.office.trainPlanManagementAdd', {
                    url: '/trainPlanManagementAdd',
                    templateUrl: 'tpl/office/trainPlan/trainPlanAdd.html',
                    controller: 'trainPlanManagementDetails',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/trainPlan/trainPlanManagementDetails.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/trainPlan/trainPlanService.js');
                            },
                        ],
                    },
                })
                //更新培训详情
                .state('app.office.trainPlanManagementUp', {
                    url: '/trainPlanManagementUp',
                    templateUrl: 'tpl/office/trainPlan/trainPlanUp.html',
                    controller: 'trainPlanManagementDetails',
                    params: {
                        item: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/trainPlan/trainPlanManagementDetails.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/trainPlan/trainPlanService.js');
                            },
                        ],
                    },
                })
                //更新培训学员详情
                .state('app.office.trainPlanPersonUp', {
                    url: '/trainPlanPersonUp',
                    templateUrl: 'tpl/office/trainPlan/trainPlanPersonUp.html',
                    controller: 'trainPlanPersonUp',
                    params: {
                        trainId: null,
                        personId: null,
                        person: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/trainPlan/trainPlanPersonUp.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/trainPlan/trainPlanService.js');
                            },
                        ],
                    },
                })
                //邮件页面
                .state('app.office.mailManagement', {
                    url: '/mailManagement',
                    templateUrl: 'tpl/office/mail/mail.html',
                    controller: 'mailManagement',
                    params: {
                        dataInfo: null,
                        root: null,
                        serialNumber: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/controllers/office/mail/mailManagement.js',
                                    'library/wangEditor/wangEditor.min.js',
                                ]);
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/mail/mailService.js');
                            },
                        ],
                    },
                })
                //仓库管理
                .state('app.office.repositoryManagement', {
                    url: '/repositoryManagement',
                    templateUrl: 'tpl/office/repository/repository.html',
                    controller: 'repositoryManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/repositoryController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/repositoryService.js');
                            },
                        ],
                    },
                })
                //新增仓库
                .state('app.office.repositoryAdd', {
                    url: '/repositoryAdd:repositoryid',
                    templateUrl: 'tpl/office/repository/repository_Add.html',
                    controller: 'repositoryAddController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/repositoryAddController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/repositoryService.js');
                            },
                        ],
                    },
                })
                //更新仓库
                .state('app.office.repositoryUpdate', {
                    url: '/repositoryUpdate:repositoryid',
                    templateUrl: 'tpl/office/repository/repository_Up.html',
                    controller: 'repositoryAddController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/repositoryAddController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/repositoryService.js');
                            },
                        ],
                    },
                })
                //模块查看
                .state('app.office.moduleSeeController', {
                    url: '/moduleSeeController:moduleid',
                    templateUrl: 'tpl/office/repository/module_See.html',
                    controller: 'moduleSeeController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/moduleSeeController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/repositoryService.js');
                            },
                        ],
                    },
                })
                //人员调出
                .state('app.office.stafftransferManagement', {
                    url: '/stafftransferManagement',
                    templateUrl: 'tpl/office/staff/stafftransfer.html',
                    controller: 'stafftransferManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/stafftransferController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/stafftransferService.js');
                            },
                        ],
                    },
                })
                //人员变动历史查询
                .state('app.office.staffchangeManagement', {
                    url: '/staffchangeManagement',
                    templateUrl: 'tpl/office/staff/staffchange.html',
                    controller: 'staffchangeManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/staffchangeController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/staffchangeService.js');
                            },
                        ],
                    },
                })

                /**
                 * 报表部分 ------------------------------------------------- start
                 */
                // 部门工作统计---同行评审统计表
                .state('app.office.report_0004', {
                    url: '/report_0004',
                    templateUrl: 'tpl/office/report/report_0004.html',
                    controller: 'report_0004Controller',
                    params: {
                        flag: 'menu',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/report/report_0004Controller.js');
                            },
                        ],
                    },
                })
                // 部门工作统计---同行评审统计表新增
                .state('app.office.report_0004_add', {
                    url: '/report_0004/add',
                    templateUrl: 'tpl/office/report/report_0004_add.html',
                    controller: 'report_0004AddController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/report/report_0004AddController.js');
                            },
                        ],
                    },
                })
                // 部门工作统计---同行评审—预评审统计表新增
                .state('app.office.report_0004_inAdvance_add', {
                    url: '/report_0004/inAdvanceAdd',
                    templateUrl: 'tpl/office/report/report_0004_inAdvance_add.html',
                    controller: 'report_0004InAdvanceAddController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/report/report_0004InAdvanceAddController.js'
                                );
                            },
                        ],
                    },
                })
                // 部门工作统计---同行评审统计表修改
                .state('app.office.report_0004_update', {
                    url: '/report_0004/update',
                    templateUrl: 'tpl/office/report/report_0004_update.html',
                    controller: 'report_0004UpdateController',
                    params: {
                        jsonResult: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/report/report_0004UpdateController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/expert/expertService.js');
                            },
                        ],
                    },
                })
                // 部门工作统计---同行评审—预评审统计表修改
                .state('app.office.report_0004_inAdvance_update', {
                    url: '/report_0004/inAdvanceUpdate',
                    templateUrl: 'tpl/office/report/report_0004_inAdvance_update.html',
                    controller: 'report_0004InAdvanceUpdateController',
                    params: {
                        jsonResult: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/report/report_0004InAdvanceUpdateController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/report/reviewProblem/reviewProblemService.js',
                                    'js/services/office/report/reportService.js',
                                ]);
                            },
                        ],
                    },
                })
                // 部门工作统计---同行评审文件上传
                .state('app.office.reportUpload', {
                    url: '/report_0004/upload',
                    templateUrl: 'tpl/office/report/reportUpload.html',
                    controller: 'reportUploadController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/report/reportUploadController.js');
                            },
                        ],
                    },
                })
                /**
                 * 报表部分 ------------------------------------------------- end
                 */

                // 新增客户端类型
                .state('app.lms.clientType_Add11', {
                    url: '/clientType_Add11/:id',
                    templateUrl: 'tpl/lms/clientType_Add11.html',
                    controller: 'clientType_Add11 as vm',
                })
                // 更新客户端类型
                .state('app.lms.clientType_Update11', {
                    url: '/clientType_Update11/:id',
                    templateUrl: 'tpl/lms/clientType_Update11.html',
                    controller: 'clientType_Update11 as vm',
                })
                //测试
                .state('app.office.webTest', {
                    url: '/web_test_office',
                    templateUrl: 'tpl/lms/web_test_offce.html',
                    controller: 'web_test as vm',
                })
                //部门/产品线工时统计
                .state('app.office.workingHoursManagement', {
                    url: '/workingHoursManagement',
                    templateUrl: 'tpl/office/workingHoursManagement/workingHoursManagement.html',
                    controller: 'workingHoursQueryController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursManagement/workingHoursManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/nonWorkDays/nonWorkDaysService.js',
                                    'js/services/office/workingHoursManagement/workingHoursManagementService.js',
                                ]);
                            },
                        ],
                    },
                })
                //项目工时统计
                .state('app.office.projectHours', {
                    url: '/projectHours',
                    templateUrl: 'tpl/office/workingHoursManagement/projectHours.html',
                    controller: 'projectHoursController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursManagement/projectHoursController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/workingHoursManagement/projectHoursService.js'
                                );
                            },
                        ],
                    },
                })
                //项目工时查看详情
                .state('app.office.projectHoursDetails', {
                    url: '/projectHoursDetails',
                    templateUrl: 'tpl/office/workingHoursManagement/projectHoursDetails.html',
                    controller: 'projectHoursDetailsController',
                    params: {
                        flag: null,
                        id: null,
                        isSprintVersion: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursManagement/projectHoursDetailsController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/workingHoursManagement/projectHoursService.js'
                                );
                            },
                        ],
                    },
                })
                //个人工时统计
                .state('app.office.personHours', {
                    url: '/personHours',
                    templateUrl: 'tpl/office/workingHoursManagement/personHours.html',
                    controller: 'personHoursController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursManagement/personHoursController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/workingHoursManagement/personHoursService.js'
                                );
                            },
                        ],
                    },
                })
                //任务视图界面
                .state('app.office.taskView', {
                    url: '/taskView',
                    templateUrl: 'tpl/office/personalDataBoard/taskView/taskView.html',
                    controller: 'taskViewController',
                    params: {
                        account: null,
                        startTime: null,
                        endTime: null,
                        name: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/personalDataBoard/taskViewController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/workingHoursManagement/taskView.js');
                            },
                        ],
                    },
                })
                //个人项目投入
                .state('app.office.personHoursProject', {
                    url: '/personHoursProject',
                    templateUrl: 'tpl/office/workingHoursManagement/personHoursProject.html',
                    controller: 'personHoursProjectController',
                    params: {
                        account: null,
                        startTime: null,
                        endTime: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursManagement/personHoursProjectController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/workingHoursManagement/personHoursProjectService.js'
                                );
                            },
                        ],
                    },
                })
                //禅道人力统计
                .state('app.office.hrAction', {
                    url: '/hrManagement',
                    templateUrl: 'tpl/office/zttask/hr.html',
                    controller: 'hrManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/zttask/ztHRController.js');
                            },
                        ],
                    },
                })

                //特殊放行情况报告
                .state('app.office.specPassManagement', {
                    url: '/specPassManagement',
                    templateUrl: 'tpl/office/specpass/specpass.html',
                    controller: 'specPassManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/specpass/specPassController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/specpass/specPassService.js');
                            },
                        ],
                    },
                })
                //返工情况应用报告
                .state('app.office.reworkManagement', {
                    url: '/reworkManagement',
                    templateUrl: 'tpl/office/zttask/rework.html',
                    controller: 'reworkManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/zttask/reworkController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/zttask/reworkService.js');
                            },
                        ],
                    },
                })
                //禅道应用报告
                .state('app.office.appReportManagement', {
                    url: '/appReportManagement',
                    templateUrl: 'tpl/office/zttask/appReport.html',
                    controller: 'appReportManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/zttask/appReportController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/zttask/appReportService.js');
                            },
                        ],
                    },
                })
                //知识库信息配置
                .state('app.office.knowledgeMessage', {
                    url: '/knowledgeMessage',
                    templateUrl: 'tpl/office/knowledge/knowledgeMessage.html',
                    controller: 'knowledgeMessage',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/knowledge/knowledgeMessageController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/knowledge/knowledgeService.js');
                            },
                        ],
                    },
                })
                //缺陷逃逸情况汇报
                .state('app.office.leakManagement', {
                    url: '/leakManagement',
                    templateUrl: 'tpl/office/zttask/leak.html',
                    controller: 'leakManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/zttask/leakController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/zttask/leakService.js');
                            },
                        ],
                    },
                })
                //人均bug数情况汇报
                .state('app.office.bugManagement', {
                    url: '/bugManagement',
                    templateUrl: 'tpl/office/zttask/bug.html',
                    controller: 'leakManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/zttask/leakController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/zttask/leakService.js');
                            },
                        ],
                    },
                })
                //过程符合度情况汇报表
                .state('app.office.processConformityManagement', {
                    url: '/processConformityManagement',
                    templateUrl: 'tpl/office/processConformity/processConformity.html',
                    controller: 'processConformityManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/processconformity/processConformityController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/processConformity/processConformityService.js'
                                );
                            },
                        ],
                    },
                })

                //过程符合度情况汇报录入
                .state('app.office.processDataManagement', {
                    url: '/processDataManagement',
                    templateUrl: 'tpl/office/processConformity/processData.html',
                    controller: 'processDataManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/processconformity/processDataController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/processConformity/processDataService.js');
                            },
                        ],
                    },
                })
                //PLM客户需求
                .state('app.office.customerDemandManagement', {
                    url: '/customerDemandManagement',
                    templateUrl: 'tpl/office/customerDemand/customerDemand.html',
                    controller: 'customerDemandManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/customerDemand/customerDemandController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/customerDemand/customerDemandService.js');
                            },
                        ],
                    },
                })
                //PLM客户需求录入
                .state('app.office.customerDataManagement', {
                    url: '/customerDataManagement',
                    templateUrl: 'tpl/office/customerDemand/customerData.html',
                    controller: 'customerDataManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/customerDemand/customerDataController.js'
                                );
                            },
                        ],
                    },
                })
                //内部需求管理
                .state('app.office.internalDemandManagement', {
                    url: '/internalDemandManagement',
                    templateUrl: 'tpl/office/customerDemand/internalDemandManagement.html',
                    controller: 'internalDemandManagementController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/customerDemand/internalDemandManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/customerDemand/internalDemandManagementService.js'
                                );
                            },
                        ],
                    },
                })
                //内部需求修改管理
                .state('app.office.internalDemandManagement_update', {
                    url: '/internalDemandManagement/update',
                    templateUrl: 'tpl/office/customerDemand/internalDemandManagement_update.html',
                    controller: 'internalDemandManagementUpdateController',
                    params: {
                        id: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/customerDemand/internalDemandManagementUpdateController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/customerDemand/internalDemandManagementService.js'
                                );
                            },
                        ],
                    },
                })
                //上线失败率情况
                .state('app.office.versionReleaseManagement', {
                    url: '/versionReleaseManagement',
                    templateUrl: 'tpl/office/versionReleaseLog/versionReleaseManagement.html',
                    controller: 'versionReleaseManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/versionRelease/versionReleaseManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/versionRelease/versionReleaseService.js');
                            },
                        ],
                    },
                })
                //上线失败率情况信息添加
                .state('app.office.versionReleaseDetail', {
                    url: '/versionReleaseDetail',
                    templateUrl: 'tpl/office/versionReleaseLog/versionReleaseDetail.html',
                    controller: 'versionReleaseDetailController',
                    params: {
                        id: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/versionRelease/versionReleaseDetailController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/versionRelease/versionReleaseService.js');
                            },
                        ],
                    },
                })

                //上线失败率情况信息数据统计下载
                .state('app.office.versionReleaseReport', {
                    url: '/versionReleaseReport',
                    templateUrl: 'tpl/office/versionReleaseLog/versionReleaseReport.html',
                    controller: 'versionReleaseReport',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/versionRelease/versionReleaseReportController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/versionRelease/versionReleaseService.js');
                            },
                        ],
                    },
                })
                //项目计划偏移报表
                .state('app.office.projectScheduleDeviation', {
                    url: '/projectScheduleDeviation',
                    templateUrl: 'tpl/office/projectScheduleDeviation/projectScheduleDeviation.html',
                    controller: 'scheduleDeviationController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectScheduleDeviation/scheduleDeviationController.js'
                                );
                            },
                        ],
                    },
                })
                //项目计划偏移报表详情管理表
                .state('app.office.scheduleDeviationDetailManagement', {
                    url: '/scheduleDeviationDetailManagement',
                    templateUrl: 'tpl/office/projectScheduleDeviation/scheduleDeviationDetailManagement.html',
                    controller: 'scheduleDeviationDetailManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectScheduleDeviation/scheduleDeviationDetailManagement.js'
                                );
                            },
                        ],
                    },
                })
                //项目计划偏移报表管理页面
                .state('app.office.scheduleDeviationManagement', {
                    url: '/scheduleDeviationManagement',
                    templateUrl: 'tpl/office/projectScheduleDeviation/scheduleDeviationManagement.html',
                    controller: 'scheduleDeviationManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectScheduleDeviation/scheduleDeviationManagement.js'
                                );
                            },
                        ],
                    },
                })
                //项目信息管理
                .state('app.office.projectManagement', {
                    url: '/projectManagement',
                    templateUrl: 'tpl/office/projectScheduleDeviation/projectManagement.html',
                    controller: 'projectManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectScheduleDeviation/projectManagementController.js'
                                );
                            },
                        ],
                    },
                })
                //项目信息管理--添加/修改
                .state('app.office.projectManagementUpdate', {
                    url: '/projectManagementUpdate',
                    templateUrl: 'tpl/office/projectScheduleDeviation/projectManagementUpdate.html',
                    controller: 'projectManagementUpdate',
                    params: {
                        projectInfoParam: null,
                        isAdd: null, //0:新增；1：修改
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectScheduleDeviation/projectManagementUpdateController.js'
                                );
                            },
                        ],
                    },
                })
                //项目信息管理--详情
                .state('app.office.projectManagementDetail', {
                    url: '/projectManagementDetail',
                    templateUrl: 'tpl/office/projectScheduleDeviation/projectManagementDetail.html',
                    controller: 'projectManagementDetail',
                    params: {
                        projectInfoParam: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectScheduleDeviation/projectManagementDetailController.js'
                                );
                            },
                        ],
                    },
                })
                //团队信息管理
                .state('app.office.groupManagement', {
                    url: '/groupManagement',
                    templateUrl: 'tpl/office/projectScheduleDeviation/groupManagement.html',
                    controller: 'groupManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectScheduleDeviation/groupManagementController.js'
                                );
                            },
                        ],
                    },
                })
                //团队信息管理--添加/修改
                .state('app.office.groupManagementUpdate', {
                    url: '/groupManagementUpdate',
                    templateUrl: 'tpl/office/projectScheduleDeviation/groupManagementUpdate.html',
                    controller: 'groupManagementUpdate',
                    params: {
                        groupInfoParam: null,
                        isAdd: null, //0:新增；1：修改
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectScheduleDeviation/groupManagementUpdateController.js'
                                );
                            },
                        ],
                    },
                })
                //团队信息管理--详情
                .state('app.office.groupManagementDetail', {
                    url: '/groupManagementDetail',
                    templateUrl: 'tpl/office/projectScheduleDeviation/groupManagementDetail.html',
                    controller: 'groupManagementDetail',
                    params: {
                        groupInfoParam: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectScheduleDeviation/groupManagementDetailController.js'
                                );
                            },
                        ],
                    },
                })
                //kpi展板
                .state('app.office.kpiController', {
                    url: '/kpiController',
                    templateUrl: 'tpl/office/kpi/kpiReport.html',
                    controller: 'kpiController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/kpi/kpiController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/kpi/kpiService.js');
                            },
                        ],
                    },
                })
                //合理化建议管理
                .state('app.office.adviceController', {
                    url: '/adviceController',
                    templateUrl: 'tpl/office/advice/adviceManagement.html',
                    controller: 'adviceController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/advice/adviceController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/advice/adviceService.js');
                            },
                        ],
                    },
                })
                //合理化建议报告
                .state('app.office.adviceReport', {
                    url: '/adviceReport',
                    templateUrl: 'tpl/office/advice/adviceReport.html',
                    controller: 'adviceReportController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/advice/adviceReportController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/advice/adviceService.js');
                            },
                        ],
                    },
                })
                //项目版本档案管理
                .state('app.office.versionsManagement', {
                    url: '/versionsManagement',
                    templateUrl: 'tpl/office/versions/versionsManagement.html',
                    controller: 'versionsManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/versions/versionsManagement.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/versions/versionsService.js');
                            },
                        ],
                    },
                })
                //项目版本档案管理新增页面
                .state('app.office.versionsAddManagement', {
                    url: '/versionsAddManagement',
                    templateUrl: 'tpl/office/versions/versionsAdd.html',
                    controller: 'versionsAddManagement',
                    params: {
                        versionId: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/versions/versionsAdd.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/versions/versionsService.js');
                            },
                        ],
                    },
                })
                //项目发布管理
                .state('app.office.projectRelease', {
                    url: '/projectRelease',
                    templateUrl: 'tpl/office/projectRelease/projectRelease.html',
                    controller: 'projectRelease',
                    params: {
                        type: 'menu',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectRelease/projectRelease.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectRelease/projectReleaseService.js');
                            },
                        ],
                    },
                })
                //项目发布管理修改页面
                .state('app.office.projectReleaseUp', {
                    url: '/projectReleaseUp',
                    templateUrl: 'tpl/office/projectRelease/projectReleaseUp.html',
                    controller: 'projectReleaseUp',
                    params: {
                        incident: null,
                        projectId: 0,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectRelease/projectReleaseUp.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectRelease/projectReleaseService.js');
                            },
                        ],
                    },
                })
                //知识产权情况汇报
                .state('app.office.intellectualController', {
                    url: '/intellectualController',
                    templateUrl: 'tpl/office/intellectual/IntellectualReport.html',
                    controller: 'intellectualController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/intellectual/intellectualController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/intellectual/intellectualService.js');
                            },
                        ],
                    },
                })
                //知识产权情况-申请专利明细管理
                .state('app.office.patentApplyController', {
                    url: '/patentApplyController',
                    templateUrl: 'tpl/office/intellectual/patentApplyManagement.html',
                    controller: 'patentApplyController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/intellectual/patentApplyController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/intellectual/intellectualService.js');
                            },
                        ],
                    },
                })
                //知识产权情况-软著明细管理
                .state('app.office.softRightController', {
                    url: '/softRightController',
                    templateUrl: 'tpl/office/intellectual/softRightManagement.html',
                    controller: 'softRightController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/intellectual/softRightController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/intellectual/intellectualService.js');
                            },
                        ],
                    },
                })
                //项目不符合项项目展示列表
                .state('app.office.incongruentManagement', {
                    url: '/incongruentManagement',
                    templateUrl: 'tpl/office/incongruent/incongruent.html',
                    controller: 'incongruentManagement',
                    params: {
                        cname: null,
                        productLine: null,
                        type: null,
                        qualityEngineer: null,
                        department: null,
                        twoDepartment: null,
                        projectStatus: null,
                        enter: 'menu',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/incongruent/incongruentController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/incongruent/incongruentService.js');
                            },
                        ],
                    },
                })
                //项目不符合项问题列表详情管理表
                .state('app.office.incongruentDetailManagement', {
                    url: '/incongruentDetailManagement',
                    templateUrl: 'tpl/office/incongruent/incongruentDetail.html',
                    controller: 'incongruentDetailManagement',
                    params: {
                        project: null,
                        cname: null,
                        productLine: null,
                        type: null,
                        qualityEngineer: null,
                        department: null,
                        twoDepartment: null,
                        projectStatus: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/incongruent/incongruentDetailController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/incongruent/incongruentService.js');
                            },
                        ],
                    },
                })
                //项目不符合项问题展示页面（含下载）
                .state('app.office.incongruentQuestionManagement', {
                    url: '/incongruentQuestionManagement',
                    templateUrl: 'tpl/office/incongruent/incongruentQuestion.html',
                    controller: 'incongruentQuestionManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/incongruent/incongruentQuestionController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/incongruent/incongruentService.js');
                            },
                        ],
                    },
                })

                //参数管理
                .state('app.office.paramManagement', {
                    url: '/paramManagement',
                    templateUrl: 'tpl/office/paramManagement/paramManagement.html',
                    controller: 'paramManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/paramManagement/paramManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/paramManagement/paramManagementService.js');
                            },
                        ],
                    },
                })
                //代码走查记录管理
                .state('app.office.codeReviewManagement', {
                    url: '/codeReviewManagement',
                    templateUrl: 'tpl/office/codeReview/codeReviewManagement.html',
                    controller: 'codeReviewController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/codeReview/codeReviewController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/codeReview/codeReviewService.js');
                            },
                        ],
                    },
                })
                //非工作日期管理
                .state('app.office.nonWorkDays', {
                    url: '/nonWorkDays',
                    templateUrl: 'tpl/office/nonWorkDays/NonWorkDays.html',
                    controller: 'nonWorkDaysController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/nonWorkDays/nonWorkDaysController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/nonWorkDays/nonWorkDaysService.js');
                            },
                        ],
                    },
                })
                //质量事故管理
                .state('app.office.qualityAccidentManagement', {
                    url: '/qualityAccidentManagement',
                    templateUrl: 'tpl/office/qualityAccident/quality_accident_management.html',
                    controller: 'qualityAccidentManagementController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/qualityAccident/quality_accident_management_controller.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/qualityAccident/quality_accident_management_service.js'
                                );
                            },
                        ],
                    },
                })
                //质量事故报表
                .state('app.office.qualityAccidentReport', {
                    url: '/qualityAccidentReport',
                    templateUrl: 'tpl/office/qualityAccident/quality_accident_report.html',
                    controller: 'qualityAccidentReportController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/qualityAccident/quality_accident_report_controller.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/qualityAccident/quality_accident_management_service.js'
                                );
                            },
                        ],
                    },
                })
                //项目的特殊放行列表详情管理表
                .state('app.office.specDetailManagement', {
                    url: '/specDetailManagement',
                    templateUrl: 'tpl/office/specpass/specDetail.html',
                    controller: 'specDetailManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/specpass/specDetailController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/specpass/specPassService.js');
                            },
                        ],
                    },
                })
                //部门信息管理
                .state('app.office.departmentManagement', {
                    url: '/departmentManagement',
                    templateUrl: 'tpl/office/department/departmentManagement.html',
                    controller: 'departmentManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/department/departmentManagement.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/department/departmentService.js');
                            },
                        ],
                    },
                })
                //员工信息管理
                .state('app.office.staffInfoManagementController', {
                    url: '/staffInfoManagementController',
                    templateUrl: 'tpl/office/staffInfo/staffInfoManagement.html',
                    controller: 'staffInfoManagementController',
                    params: {
                        searchObjectArea: null,
                        searchObjectDepartment: null,
                        searchObjectEmployeeName: null,
                        searchObjectCompanyTitle: null,
                        searchObjectOnboardTime: null,
                        searchObjectOnboardStopTime: null,
                        searchObjectEducation: null,
                        searchObjectState: null,
                        entrance: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/staffInfo/staffInfoManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/staffInfo/staffInfoService.js');
                            },
                        ],
                    },
                })
                //员工信息添加
                .state('app.office.staffInfoAddController', {
                    url: '/staffInfoAddController',
                    templateUrl: 'tpl/office/staffInfo/staffInfoAdd.html',
                    controller: 'staffInfoAddController',
                    params: {
                        searchObjectArea: null,
                        searchObjectDepartment: null,
                        searchObjectEmployeeName: null,
                        searchObjectCompanyTitle: null,
                        searchObjectOnboardTime: null,
                        searchObjectOnboardStopTime: null,
                        searchObjectEducation: null,
                        searchObjectState: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/staffInfo/staffInfoAddController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/staffInfo/staffInfoService.js');
                            },
                        ],
                    },
                })
                //员工信息修改
                .state('app.office.staffInfoUpdateController', {
                    url: '/staffInfoUpdateController',
                    templateUrl: 'tpl/office/staffInfo/staffInfoUpdate.html',
                    controller: 'staffInfoUpdateController',
                    params: {
                        id: null,
                        employeeId: null,
                        searchObjectArea: null,
                        searchObjectDepartment: null,
                        searchObjectEmployeeName: null,
                        searchObjectCompanyTitle: null,
                        searchObjectOnboardTime: null,
                        searchObjectOnboardStopTime: null,
                        searchObjectEducation: null,
                        searchObjectState: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/staffInfo/staffInfoUpdateController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/staffInfo/staffInfoService.js');
                            },
                        ],
                    },
                })
                .state('app.office.staffPerformManagementController', {
                    url: '/staffPerformManagementController',
                    templateUrl: 'tpl/office/staffInfo/staffPerformManagement.html',
                    controller: 'staffPerformManagementController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/staffInfo/staffPerformController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/staffInfo/staffPerformanceService.js');
                            },
                        ],
                    },
                })

                .state('app.office.staffTitleController', {
                    url: '/staffTitleController',
                    templateUrl: 'tpl/office/staffInfo/staffTitleManagement.html',
                    controller: 'staffTitleController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/staffInfo/staffTitleController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/staffInfo/staffTitleService.js');
                            },
                        ],
                    },
                })
                .state('app.office.attendanceController', {
                    url: '/attendanceController',
                    templateUrl: 'tpl/office/attendance/attendanceManagement.html',
                    controller: 'attendanceController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/attendance/attendanceController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/attendance/attendanceService.js');
                            },
                        ],
                    },
                })
                .state('app.office.attendanceDetail', {
                    url: '/attendanceDetail',
                    templateUrl: 'tpl/office/attendance/attendanceDetail.html',
                    controller: 'attendanceDetailController',
                    params: {
                        employeeNo: null,
                        startDate: null,
                        endDate: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/attendance/attendanceDetailController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/attendance/attendanceDetailService.js');
                            },
                        ],
                    },
                })

                .state('app.office.staffDimissionManagementController', {
                    url: '/staffDimissionManagementController',
                    templateUrl: 'tpl/office/staffInfo/staffDimissionManagement.html',
                    controller: 'staffDimissionManagementController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/staffInfo/staffDimissionController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/staffInfo/staffDimissionService.js');
                            },
                        ],
                    },
                })

                //周报
                .state('app.office.projectWeeklyReportManagement', {
                    url: '/projectWeeklyReportManagement',
                    templateUrl: 'tpl/office/projectWeeklyReport/projectWeeklyReport.html',
                    controller: 'projectWeeklyReportManagement',
                    params: {
                        projectId: null,
                    },
                })
                //周报管理
                .state('app.office.projectWeeklyReportAddManagement', {
                    url: '/projectWeeklyReportAddManagement',
                    templateUrl: 'tpl/office/projectWeeklyReport/projectWeeklyReportManagement.html',
                    controller: 'projectWeeklyReportAddManagement',
                    params: {
                        projectId: null,
                        type: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectWeeklyReport/projectWeeklyReportAddManagement.js'
                                );
                            },
                        ],
                    },
                })
                //项目周报
                .state('app.office.projectWeekly', {
                    url: '/projectWeekly',
                    templateUrl: 'tpl/office/projectWeekly/projectWeekly.html',
                    controller: 'projectWeeklyManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectWeekly/projectWeeklyController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectWeekly/projectWeeklyService.js');
                            },
                        ],
                    },
                })
                //项目周报项目信息管理
                .state('app.office.projectWeeklyAddManagement', {
                    url: '/projectWeeklyAddManagement',
                    templateUrl: 'tpl/office/projectWeekly/projectWeeklyManagement.html',
                    controller: 'projectWeeklyAddManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectWeekly/projectWeeklyAddManagement.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectWeekly/projectWeeklyService.js');
                            },
                        ],
                    },
                })
                //项目周报详情
                .state('app.office.projectWeeklyDetail', {
                    url: '/projectWeeklyDetail',
                    templateUrl: 'tpl/office/projectWeekly/projectWeeklyDetail.html',
                    controller: 'projectWeeklyDetailManagement',
                    params: {
                        projectId: null,
                        type: null,
                        projectTypeFlag: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectWeekly/projectWeeklyDetailController.js'
                                );
                            },
                        ],
                    },
                })
                .state('app.office.productInfoController', {
                    url: '/productInfoController',
                    templateUrl: 'tpl/office/customerDemand/productInfo.html',
                    controller: 'productInfoController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/customerDemand/productInfoController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/customerDemand/productInfoService.js');
                            },
                        ],
                    },
                })
                //试用期考核
                .state('app.office.staffProbationPeriodController', {
                    url: '/staffProbationPeriodController',
                    templateUrl: 'tpl/office/staffInfo/staffProbationPeriod.html',
                    controller: 'staffProbationPeriodController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/staffInfo/staffProbationPeriodController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/staffInfo/staffPerformanceService.js');
                            },
                        ],
                    },
                })
                //员工信息权限管理
                .state('app.office.dataPermissionGroup', {
                    url: '/dataPermissionGroup',
                    templateUrl: 'tpl/office/dataPermissionGroup/dataPermissionGroup.html',
                    controller: 'dataPermissionGroup',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/dataPermissionGroup/dataPermissionGroup.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/dataPermissionGroup/dataPermissionGroupService.js'
                                );
                            },
                        ],
                    },
                })
                //人才库管理
                .state('app.office.expertController', {
                    url: '/expertController',
                    templateUrl: 'tpl/office/expert/expertManagement.html',
                    controller: 'expertController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/expert/expertController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/expert/expertService.js');
                            },
                        ],
                    },
                })
                //人才库添加
                .state('app.office.expertControllerAdd', {
                    url: '/expertControllerAdd',
                    templateUrl: 'tpl/office/expert/expertManagementAdd.html',
                    controller: 'expertControllerAdd',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/expert/expertControllerAdd.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/expert/expertService.js');
                            },
                        ],
                    },
                })
                //人才库更新
                .state('app.office.expertControllerUp', {
                    url: '/expertControllerUp',
                    templateUrl: 'tpl/office/expert/expertManagementAdd.html',
                    controller: 'expertControllerAdd',
                    params: {
                        item: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/expert/expertControllerAdd.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/expert/expertService.js');
                            },
                        ],
                    },
                })
                //新增数据访问权限
                .state('app.office.dataPermissionGroupAdd', {
                    url: '/dataPermissionGroupAdd',
                    templateUrl: 'tpl/office/dataPermissionGroup/dataPermissionGroupAdd.html',
                    controller: 'dataPermissionGroupDetails',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/dataPermissionGroup/dataPermissionGroupDetails.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/dataPermissionGroup/dataPermissionGroupService.js'
                                );
                            },
                        ],
                    },
                })
                //更新数据访问权限
                .state('app.office.dataPermissionGroupUp', {
                    url: '/dataPermissionGroupUp',
                    templateUrl: 'tpl/office/dataPermissionGroup/dataPermissionGroupUp.html',
                    controller: 'dataPermissionGroupDetails',
                    params: {
                        item: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/dataPermissionGroup/dataPermissionGroupDetails.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/dataPermissionGroup/dataPermissionGroupService.js'
                                );
                            },
                        ],
                    },
                })
                //更新数据访问权限
                .state('app.office.unAuthority', {
                    url: '/unAuthority',
                    templateUrl: 'tpl/office/demo/unAuthority.html',
                    controller: '',
                })
                //代码走读问题
                .state('app.office.codeReviewProblemController', {
                    url: '/codeReviewProblemController',
                    templateUrl: 'tpl/office/codeReview/codeReviewProblem.html',
                    controller: 'codeReviewProblemController',
                    params: {
                        id: null,
                        add: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/codeReview/codeReviewProblemController.js'
                                );
                            },
                        ],
                    },
                })
                .state('app.office.reviewProblem', {
                    url: '/reviewProblem',
                    templateUrl: 'tpl/office/report/reviewProblem/reviewProblem.html',
                    controller: 'reviewProblemController',
                    params: {
                        id: null,
                        type: null,
                        reviewContent: null,
                        projectName: null,
                        reviewTheme: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/report/reviewProblem/reviewProblemController.js'
                                );
                            },
                        ],
                    },
                })
                .state('app.office.reviewProblemAdd', {
                    url: '/reviewProblemAdd',
                    templateUrl: 'tpl/office/report/reviewProblem/reviewProblemAdd.html',
                    controller: 'reviewProblemAddController',
                    params: {
                        reviewId: null,
                        reviewType: null,
                        reviewContent: null,
                        reviewTheme: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/report/reviewProblem/reviewProblemAddController.js'
                                );
                            },
                        ],
                    },
                })
                .state('app.office.reviewProblemUp', {
                    url: '/reviewProblemUp',
                    templateUrl: 'tpl/office/report/reviewProblem/reviewProblemUp.html',
                    controller: 'reviewProblemUpController',
                    params: {
                        problemJson: null,
                        reviewId: null,
                        reviewType: null,
                        reviewContent: null,
                        reviewTheme: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/report/reviewProblem/reviewProblemUpController.js'
                                );
                            },
                        ],
                    },
                })
                //预评审问题相关路由
                .state('app.office.inAdvanceReviewProblem', {
                    url: '/inAdvanceReviewProblem',
                    templateUrl: 'tpl/office/report/inAdvanceReviewProblem/inAdvanceReviewProblem.html',
                    controller: 'inAdvanceReviewProblemController',
                    params: {
                        id: null,
                        type: null,
                        reviewContent: null,
                        reviewTheme: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/report/inAdvanceReviewProblem/inAdvanceReviewProblemController.js'
                                );
                            },
                        ],
                    },
                })
                .state('app.office.inAdvanceReviewProblemAdd', {
                    url: '/inAdvanceReviewProblemAdd',
                    templateUrl: 'tpl/office/report/inAdvanceReviewProblem/inAdvanceReviewProblemAdd.html',
                    controller: 'inAdvanceReviewProblemAddController',
                    params: {
                        reviewId: null,
                        reviewType: null,
                        reviewContent: null,
                        reviewTheme: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/report/inAdvanceReviewProblem/inAdvanceReviewProblemAddController.js'
                                );
                            },
                        ],
                    },
                })
                .state('app.office.inAdvanceReviewProblemUp', {
                    url: '/inAdvanceReviewProblemUp',
                    templateUrl: 'tpl/office/report/inAdvanceReviewProblem/inAdvanceReviewProblemUp.html',
                    controller: 'inAdvanceReviewProblemUpController',
                    params: {
                        problemJson: null,
                        reviewId: null,
                        reviewType: null,
                        reviewContent: null,
                        reviewTheme: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/report/inAdvanceReviewProblem/inAdvanceReviewProblemUpController.js'
                                );
                            },
                        ],
                    },
                })
                //员工证书管理
                .state('app.office.staffCertificateController', {
                    url: '/staffCertificateController',
                    templateUrl: 'tpl/office/staffInfo/staffCertificateManagement.html',
                    controller: 'staffCertificateController',
                    params: {
                        id: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/staffInfo/staffCertificateController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/staffInfo/staffCertificateService.js');
                            },
                        ],
                    },
                })
                //员工证书管理 新增
                .state('app.office.staffCertificateAddController', {
                    url: '/staffCertificateAddController',
                    templateUrl: 'tpl/office/staffInfo/staffCertificateAdd.html',
                    controller: 'staffCertificateAddController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/staffInfo/staffCertificateAddController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/staffInfo/staffCertificateService.js');
                            },
                        ],
                    },
                })
                //员工证书管理 修改
                .state('app.office.staffCertificateUpdateController', {
                    url: '/staffCertificateUpdateController',
                    templateUrl: 'tpl/office/staffInfo/staffCertificateUpdate.html',
                    controller: 'staffCertificateUpdateController',
                    params: {
                        id: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/staffInfo/staffCertificateUpdateController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/staffInfo/staffCertificateService.js');
                            },
                        ],
                    },
                })
                //员工获奖统计
                .state('app.office.employeeReward', {
                    url: '/employeeReward',
                    templateUrl: 'tpl/office/employeeReward/employeeReward.html',
                    controller: 'employeeReward',
                    params: {
                        id: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/employeeReward/employeeReward.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/employeeReward/employeeRewardService.js');
                            },
                        ],
                    },
                })
                //员工获奖统计新增
                .state('app.office.employeeRewardAdd', {
                    url: '/employeeRewardAdd',
                    templateUrl: 'tpl/office/employeeReward/employeeRewardAdd.html',
                    controller: 'employeeRewardOperation',
                    params: {
                        searchObject: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/employeeReward/employeeRewardOperation.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/employeeReward/employeeRewardService.js');
                            },
                        ],
                    },
                })
                //出差信息管理
                .state('app.office.onBusinessManagement', {
                    url: '/onBusinessManagement',
                    templateUrl: 'tpl/office/onBusiness/onBusinessManagement.html',
                    controller: 'onBusinessController',
                    params: {
                        formRefer: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/onBusiness/onBusinessController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/onBusiness/onBusinessService.js');
                            },
                        ],
                    },
                })
                //出差信息管理--添加
                .state('app.office.onBusinessAdd', {
                    url: '/onBusinessAdd',
                    templateUrl: 'tpl/office/onBusiness/onBusinessAdd.html',
                    controller: 'onBusinessAdd',
                    params: {
                        formRefer: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/onBusiness/onBusinessAddController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/onBusiness/onBusinessService.js');
                            },
                        ],
                    },
                })
                //出差信息管理--修改
                .state('app.office.onBusinessUpdate', {
                    url: '/onBusinessUpdate',
                    templateUrl: 'tpl/office/onBusiness/onBusinessUpdate.html',
                    controller: 'onBusinessUpdate',
                    params: {
                        id: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/onBusiness/onBusinessUpdateController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/onBusiness/onBusinessService.js');
                            },
                        ],
                    },
                })
                //小组管理
                .state('app.office.teamManagement', {
                    url: '/teamManagement',
                    templateUrl: 'tpl/office/team/teamManagement.html',
                    controller: 'teamManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/team/teamManagement.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/team/teamService.js');
                            },
                        ],
                    },
                })
                //定时任务
                .state('app.office.quartzScheduledController', {
                    url: '/quartzScheduledController',
                    templateUrl: 'tpl/office/quartzScheduled/quartzScheduled.html',
                    controller: 'quartzScheduledController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/quartzScheduled/quartzScheduledController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/quartzScheduled/quartzScheduledService.js');
                            },
                        ],
                    },
                })
                //定时任务新增
                .state('app.office.quartzScheduledAddController', {
                    url: '/quartzScheduledAddController',
                    templateUrl: 'tpl/office/quartzScheduled/quartzScheduledAdd.html',
                    controller: 'quartzScheduledAddController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/quartzScheduled/quartzScheduledAddController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/quartzScheduled/quartzScheduledService.js');
                            },
                        ],
                        loadCronGen: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/quartzScheduled/cronGen.min.js');
                            },
                        ],
                    },
                })
                //定时任务修改
                .state('app.office.quartzScheduledUpController', {
                    url: '/quartzScheduledUpController',
                    templateUrl: 'tpl/office/quartzScheduled/quartzScheduledUp.html',
                    controller: 'quartzScheduledUpController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/quartzScheduled/quartzScheduledUpController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/quartzScheduled/quartzScheduledService.js');
                            },
                        ],
                        loadCronGen: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/quartzScheduled/cronGen.min.js');
                            },
                        ],
                    },
                })
                //定时任务历史记录
                .state('app.office.quartzScheduledHistory', {
                    url: '/quartzScheduledHistory',
                    templateUrl: 'tpl/office/quartzScheduled/quartzScheduledHistory.html',
                    controller: 'quartzScheduledHistoryController',
                    params: {
                        id: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/quartzScheduled/quartzScheduledHistoryController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/quartzScheduled/quartzScheduledService.js');
                            },
                        ],
                    },
                })
                //预警信息
                .state('app.office.earlyWarningController', {
                    url: '/earlyWarningController',
                    templateUrl: 'tpl/office/earlyWarning/earlyWarning.html',
                    controller: 'earlyWarningController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/earlyWarning/earlyWarningController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/quartzScheduled/quartzScheduledService.js');
                            },
                        ],
                    },
                })
                //预警信息新增
                .state('app.office.earlyWarningAddController', {
                    url: '/earlyWarningAddController',
                    templateUrl: 'tpl/office/earlyWarning/earlyWarningAdd.html',
                    controller: 'earlyWarningAddController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/earlyWarning/earlyWarningAddController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/earlyWarning/earlyWarningService.js');
                            },
                        ],
                    },
                })
                //预警信息修改
                .state('app.office.earlyWarningUpController', {
                    url: '/earlyWarningUpController',
                    templateUrl: 'tpl/office/earlyWarning/earlyWarningUp.html',
                    controller: 'earlyWarningUpController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/earlyWarning/earlyWarningUpController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/earlyWarning/earlyWarningService.js');
                            },
                        ],
                    },
                })
                //预警信息历史记录
                .state('app.office.earlyWarningHistory', {
                    url: '/earlyWarningHistory',
                    templateUrl: 'tpl/office/earlyWarning/earlyWarningHistory.html',
                    controller: 'earlyWarningHistoryController',
                    params: {
                        id: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/earlyWarning/earlyWarningHistoryController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/quartzScheduled/quartzScheduledService.js');
                            },
                        ],
                    },
                })
                //低级质量问题
                .state('app.office.lowQualityManagement', {
                    url: '/lowQualityManagement',
                    templateUrl: 'tpl/office/lowQualityProblem/lowQualityManagement.html',
                    controller: 'lowQualityManagementController',
                    params: {
                        flag: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/lowQualityProblem/lowQualityManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/lowQualityProblem/lowQualityService.js',
                                    'js/services/office/trackingSheet/trackingSheetService.js',
                                ]);
                            },
                        ],
                    },
                })
                //关键技术问题
                .state('app.office.keyTechIssueManagement', {
                    url: '/keyTechIssueManagement',
                    templateUrl: 'tpl/office/keyTechIssue/keyTechIssueManagement.html',
                    controller: 'keyTechIssueManagementController',
                    params: {
                        flag: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/keyTechIssue/keyTechIssueManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/keyTechIssue/keyTechIssueService.js',
                                    'js/services/office/trackingSheet/trackingSheetService.js',
                                ]);
                            },
                        ],
                    },
                })
                //关键技术问题
                .state('app.office.keyTechIssueVerify', {
                    url: '/keyTechIssueVerify',
                    templateUrl: 'tpl/office/keyTechIssue/keyTechIssueManagement.html',
                    controller: 'keyTechIssueManagementController',
                    params: {
                        flag: 'verify',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/keyTechIssue/keyTechIssueManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/keyTechIssue/keyTechIssueService.js');
                            },
                        ],
                    },
                })
                //关键技术问题新增
                .state('app.office.keyTechIssueAdd', {
                    url: '/keyTechIssueAdd',
                    templateUrl: 'tpl/office/keyTechIssue/keyTechIssueAdd.html',
                    controller: 'keyTechIssueAddController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/keyTechIssue/keyTechIssueAddController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/keyTechIssue/keyTechIssueService.js');
                            },
                        ],
                    },
                })
                //关键技术问题更新
                .state('app.office.keyTechIssueUpdate', {
                    url: '/keyTechIssueUpdate',
                    templateUrl: 'tpl/office/keyTechIssue/keyTechIssueAdd.html',
                    controller: 'keyTechIssueAddController',
                    params: {
                        item: null,
                        flag: 'update',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/keyTechIssue/keyTechIssueAddController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/keyTechIssue/keyTechIssueService.js');
                            },
                        ],
                    },
                })
                //关键技术问题详情
                .state('app.office.keyTechIssueDetail', {
                    url: '/keyTechIssueDetail',
                    templateUrl: 'tpl/office/keyTechIssue/keyTechIssueAdd.html',
                    controller: 'keyTechIssueAddController',
                    params: {
                        item: null,
                        flag: 'detail',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/keyTechIssue/keyTechIssueAddController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/keyTechIssue/keyTechIssueService.js');
                            },
                        ],
                    },
                })
                //低级质量问题添加
                .state('app.office.lowQualityAdd', {
                    url: '/lowQualityAdd',
                    templateUrl: 'tpl/office/lowQualityProblem/lowQualityAdd.html',
                    controller: 'lowQualityAddController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/lowQualityProblem/lowQualityAddController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/lowQualityProblem/lowQualityService.js');
                            },
                        ],
                    },
                })
                //低级质量问题更新
                .state('app.office.lowQualityUpdate', {
                    url: '/lowQualityUpdate',
                    templateUrl: 'tpl/office/lowQualityProblem/lowQualityAdd.html',
                    controller: 'lowQualityAddController',
                    params: {
                        item: null,
                        flag: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/lowQualityProblem/lowQualityAddController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/lowQualityProblem/lowQualityService.js');
                            },
                        ],
                    },
                })
                //低级质量问题审核
                .state('app.office.lowQualityVerify', {
                    url: '/lowQualityVerify',
                    templateUrl: 'tpl/office/lowQualityProblem/lowQualityManagement.html',
                    controller: 'lowQualityManagementController',
                    params: {
                        flag: 'verify',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/lowQualityProblem/lowQualityManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/lowQualityProblem/lowQualityService.js',
                                    'js/services/office/trackingSheet/trackingSheetService.js',
                                ]);
                            },
                        ],
                    },
                })
                //需支援事项
                .state('app.office.projectSupportController', {
                    url: '/projectSupport',
                    templateUrl: 'tpl/office/projectSupport/projectSupportManagement.html',
                    controller: 'projectSupportController',
                    params: {
                        projectId: null,
                    },
                })
                //需支援事项更新
                .state('app.office.projectSupportAddController', {
                    url: '/projectSupportAdd',
                    templateUrl: 'tpl/office/projectSupport/projectSupportAdd.html',
                    controller: 'projectSupportAddController',
                    params: {
                        item: null,
                        projectId: null,
                        type: null,
                    },
                })
                //风险管理
                .state('app.office.projectRiskController', {
                    url: '/projectRiskController',
                    templateUrl: 'tpl/office/projectSupport/projectRiskManagement.html',
                    controller: 'projectRiskController',
                    params: {
                        projectId: null,
                    },
                })
                //周报问题
                .state('app.office.projectProblemController', {
                    url: '/projectProblemController',
                    templateUrl: 'tpl/office/projectWeekly/projectProblemManagement.html',
                    controller: 'projectProblemController',
                    params: {
                        projectId: null,
                    },
                })
                //问题管理
                .state('app.office.projectProblemAddController', {
                    url: '/projectProblemAddController',
                    templateUrl: 'tpl/office/projectWeekly/projectProblemAddManagement.html',
                    controller: 'projectProblemAddController',
                    params: {
                        projectId: null,
                        type: null,
                    },
                })
                //周报—里程碑
                .state('app.office.projectMilestoneController', {
                    url: '/projectMilestoneController',
                    templateUrl: 'tpl/office/projectSupport/projectMilestone.html',
                    controller: 'projectMilestoneController',
                    params: {
                        projectId: null,
                    },
                })
                //评委评审问题统计报表
                .state('app.office.judgesReviewQuestionsController', {
                    url: '/judgesReviewQuestionsController',
                    templateUrl: 'tpl/office/report/judgesReviewQuestions/judgesReviewQuestions.html',
                    controller: 'judgesReviewQuestionsController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/report/judgesReviewQuestions/judgesReviewQuestionsController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/report/judgesReviewQuestions/judgesReviewQuestionsService.js'
                                );
                            },
                        ],
                    },
                })
                //会议信息管理页面
                .state('app.office.meetingController', {
                    url: '/meetingController',
                    templateUrl: 'tpl/office/meeting/meetingManagement.html',
                    controller: 'meetingController',
                    params: {
                        dataInfo: null,
                        formRefer: null,
                        type: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/meeting/meetingController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/mail/mailService.js',
                                    'js/services/office/meeting/meetingService.js',
                                ]);
                            },
                        ],
                    },
                })
                //会议信息新增页面
                .state('app.office.meetingAddController', {
                    url: '/meetingAddController',
                    templateUrl: 'tpl/office/meeting/meetingAddManagement.html',
                    controller: 'meetingAddController',
                    params: {
                        jsonResult: null,
                        formRefer: null,
                        serialNumber: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/meeting/meetingAddController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/mail/mailService.js',
                                    'js/services/office/expert/expertService.js',
                                    'js/services/office/meeting/meetingService.js',
                                ]);
                            },
                        ],
                    },
                })
                //会议信息修改页面
                .state('app.office.meetingUpdateController', {
                    url: '/meetingUpdateController',
                    templateUrl: 'tpl/office/meeting/meetingUpdateManagement.html',
                    controller: 'meetingUpdateController',
                    params: {
                        jsonResult: null,
                        formRefer: null,
                        serialNumber: null,
                        type: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/meeting/meetingUpdateController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/mail/mailService.js',
                                    'js/services/office/expert/expertService.js',
                                    'js/services/office/meeting/meetingService.js',
                                ]);
                            },
                        ],
                    },
                })
                //会议信息反馈(同行评审界面)
                .state('app.office.peerReviewFeedback', {
                    url: '/peerReviewFeedback',
                    templateUrl: 'tpl/office/meeting/peerReviewFeedback.html',
                    controller: 'peerReviewFeedback',
                    params: {
                        jsonResult: null,
                        formRefer: null,
                        serialNumber: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/meeting/peerReviewFeedback.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/mail/mailService.js',
                                    'js/services/office/meeting/meetingService.js',
                                ]);
                            },
                        ],
                    },
                })
                //体系建设优化信息管理页面
                .state('app.office.systemOptimizationController', {
                    url: '/systemOptimizationController',
                    templateUrl: 'tpl/office/systemOptimization/systemOptimization.html',
                    controller: 'systemOptimizationController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/systemOptimization/systemOptimizationController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/systemOptimization/systemOptimizationService.js'
                                );
                            },
                        ],
                    },
                })
                //体系建设优化信息管理新增和修改页面
                .state('app.office.systemOptimizationAddOrUpdateController', {
                    url: '/systemOptimizationAddOrUpdateController',
                    templateUrl: 'tpl/office/systemOptimization/systemOptimizationAddOrUpdate.html',
                    controller: 'systemOptimizationAddOrUpdateController',
                    params: {
                        item: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/systemOptimization/systemOptimizationAddOrUpdateController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/systemOptimization/systemOptimizationService.js'
                                );
                            },
                        ],
                    },
                })
                //线上问题管理页面
                .state('app.office.bugOnlineController', {
                    url: '/bugOnlineController',
                    templateUrl: 'tpl/office/bug/bugOnline.html',
                    controller: 'bugOnlineController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/bug/bugOnlineController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/bug/bugOnlineService.js');
                            },
                        ],
                    },
                })
                //线上问题详情管理页面
                .state('app.office.bugOnlineDetailController', {
                    url: '/bugOnlineDetailController',
                    templateUrl: 'tpl/office/bug/bugOnlineDetail.html',
                    controller: 'bugOnlineDetailController',
                    params: {
                        id: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/bug/bugOnlineDetailController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/bug/bugOnlineService.js');
                            },
                        ],
                    },
                })
                //数据字典管理页面
                .state('app.office.paramController', {
                    url: '/paramController',
                    templateUrl: 'tpl/office/param/paramManagement.html',
                    controller: 'paramController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/param/paramController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/param/paramService.js');
                            },
                        ],
                    },
                })
                //新增数据字典管理页面
                .state('app.office.paramAddController', {
                    url: '/paramAddController',
                    templateUrl: 'tpl/office/param/paramAddManagement.html',
                    controller: 'paramAddController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/param/paramAddController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/param/paramService.js');
                            },
                        ],
                    },
                })
                //修改数据字典管理页面
                .state('app.office.paramUpdateController', {
                    url: '/paramUpdateController',
                    templateUrl: 'tpl/office/param/paramUpdateManagement.html',
                    controller: 'paramUpdateController',
                    params: {
                        paramTypeCode: null,
                        paramName: null,
                        paramCode: null,
                        paramDesc: null,
                        paramValue: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/param/paramUpdateController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/param/paramService.js');
                            },
                        ],
                    },
                })
                //禅道项目字典管理页面
                .state('app.office.zentaoParamController', {
                    url: '/zentaoParamController',
                    templateUrl: 'tpl/office/zttask/zentaoParamManagement.html',
                    controller: 'zentaoParamController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/zttask/zentaoParamController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/param/paramService.js');
                            },
                        ],
                    },
                })
                //wcp登录
                .state('app.office.wcpLoginController', {
                    url: '/wcpLoginController',
                    templateUrl: 'tpl/wcp/wcpLoginPage.html',
                    controller: 'wcpLoginController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/wcp/wcpLoginController.js');
                            },
                        ],
                    },
                })
                //wcp首页
                .state('app.office.websearchPubHome', {
                    url: '/websearchPubHome',
                    controller: 'wcpController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/wcp/wcpController.js');
                            },
                        ],
                    },
                })
                //禅道登录
                .state('app.office.zentaoLoginController', {
                    url: '/zentaoLoginController',
                    templateUrl: 'tpl/zentao/zentaoLoginPage.html',
                    controller: 'zentaoLoginController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/zentao/zentaoLoginController.js');
                            },
                        ],
                    },
                })
                //禅道首页
                .state('app.office.zentaoIndexController', {
                    url: '/zentaoIndexController',
                    controller: 'zentaoController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/zentao/zentaoController.js');
                            },
                        ],
                    },
                })
                //OA首页
                .state('app.office.oaIndexController', {
                    url: '/oaIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'http://oa.byintra.com/iPortal/Login.aspx',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //Git首页
                .state('app.office.gitIndexController', {
                    url: '/gitIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'http://gitlab.xtjc.net',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //maven首页
                .state('app.office.mavenIndexController', {
                    url: '/mavenIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'http://maven.xtjc.net',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //Sonar7.5
                .state('app.office.sonarIndexController', {
                    url: '/sonarIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'http://sonar.xtjc.net',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //Sonar9.9
                .state('app.office.sonarnewIndexController', {
                    url: '/sonarnewIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'http://sonar99.xtjc.net:9000/',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //apitest（内网）--自动化测试平台
                .state('app.office.apitest1IndexController', {
                    url: '/apitest1IndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'http://apitest.xtjc.net',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //apitest（外网）--自动化测试平台
                .state('app.office.apitest2IndexController', {
                    url: '/apitest2IndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'http://103.46.168.45:25780',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //CMDB
                .state('app.office.CMDBIndexController', {
                    url: '/CMDBIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'http://cmdb.xtjc.net/',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //JumpServer
                .state('app.office.JumpServerIndexController', {
                    url: '/JumpServerIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'http://jumpserver.xtjc.net',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //安全漏洞管理
                .state('app.office.dtrackIndexController', {
                    url: '/dtrackIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'http://dtrack.xtjc.net:30182/',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //CiHome
                .state('app.office.CiHomeIndexController', {
                    url: '/CiHomeIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'http://cihome.xtjc.net/',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //Sentinel
                .state('app.office.SentinelIndexController', {
                    url: '/SentinelIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'http://sentinel.xtjc.net/',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //yapi(外网)
                .state('app.office.yapiIndexController', {
                    url: '/yapiIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'https://xy-yapi.xinbeiyang.info/ ',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //日志系统（外网）
                .state('app.office.logCenterIndexController', {
                    url: '/logCenterIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'https://xy-logcenter.xinbeiyang.info/',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //Zabbix（外网）
                .state('app.office.apiTestIndexController', {
                    url: '/apiTestIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'https://xy-apitest.xinbeiyang.info/',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //Grafana运维看板（外网）
                .state('app.office.grafanaIndexController', {
                    url: '/grafanaIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'https://xy-grafana.xinbeiyang.info/',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //Rancher外网
                .state('app.office.rancherIndexController', {
                    url: '/rancherIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'https://rancher.renrenqu.com:8443/',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //Harbor（外网）
                .state('app.office.harborIndexController', {
                    url: '/harborIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'https://harbor.renrenqu.com:8443/',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //Jenkins首页
                .state('app.office.jenkinsIndexController', {
                    url: '/jenkinsIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'http://jenkins.xtjc.net',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //软件发放系统(SDM)首页
                .state('app.office.SDMIndexController', {
                    url: '/SDMIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'http://sdm.newbeiyang.com.cn',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //superset看板首页
                .state('app.office.SupersetIndexController', {
                    url: '/SupersetIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'http://kanban.xtjc.net',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //共享目录
                .state('app.office.shareIndexController', {
                    url: '/shareIndexController',
                    controller: 'oftenLinkController',
                    params: {
                        api: 'file://192.168.28.2/资料共享/',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/oftenLink/oftenLinkController.js');
                            },
                        ],
                    },
                })
                //提测单
                .state('app.office.testTask', {
                    url: '/testTask',
                    templateUrl: 'tpl/office/testTask/testTask.html',
                    controller: 'testTaskController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/testTask/testTaskController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/testTask/testTaskService.js');
                            },
                        ],
                    },
                })
                //提测单不规范命名
                .state('app.office.nonstandardName', {
                    url: '/nonstandardName',
                    templateUrl: 'tpl/office/testTask/nonstandardName.html',
                    controller: 'nonstandardNameController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/testTask/nonstandardNameController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/testTask/testTaskService.js');
                            },
                        ],
                    },
                })
                //指标数据管理
                .state('app.office.roleKpiController', {
                    url: '/roleKpiController',
                    templateUrl: 'tpl/office/roleKpi/roleKpi.html',
                    controller: 'roleKpiController',
                    params: {
                        type: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/roleKpi/roleKpiController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/roleKpi/roleKpiService.js');
                            },
                        ],
                    },
                })
                //指标数据更新
                .state('app.office.roleKpiUpController', {
                    url: '/roleKpiUpController',
                    templateUrl: 'tpl/office/roleKpi/roleKpiUp.html',
                    controller: 'roleKpiUpController',
                    params: {
                        item: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/roleKpi/roleKpiUpController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/roleKpi/roleKpiService.js');
                            },
                        ],
                    },
                })
                //指标数据员工关联更新
                .state('app.office.roleKpiPersonUpController', {
                    url: '/roleKpiPersonUpController',
                    templateUrl: 'tpl/office/roleKpi/roleKpiPersonUp.html',
                    controller: 'roleKpiPersonUpController',
                    params: {
                        roleName: '',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/roleKpi/roleKpiPersonUpController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/roleKpi/roleKpiService.js');
                            },
                        ],
                    },
                })
                //度量元采集数据
                .state('app.office.measureHis', {
                    url: '/measureHis',
                    templateUrl: 'tpl/office/measure/measureHis.html',
                    controller: 'measureHisController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/measure/measureHisController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/measure/measureHisService.js');
                            },
                        ],
                    },
                })
                //个人指标
                .state('app.office.personKpi', {
                    url: '/personKpi',
                    templateUrl: 'tpl/office/personKpi/personKpi.html',
                    controller: 'personKpiController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/personKpi/personKpiController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/personKpi/personKpiService.js');
                            },
                        ],
                    },
                })
                //冲刺版本管理
                .state('app.office.sprintVersions', {
                    url: '/sprintVersions',
                    templateUrl: 'tpl/office/sprintVersions/sprintVersions.html',
                    controller: 'sprintVersionsController',
                    params: {
                        isBack: 1,
                        type: 'menu',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/sprintVersions/sprintVersionsController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/sprintVersions/sprintVersionsService.js');
                            },
                        ],
                    },
                })
                //冲刺版本管理-任务明细
                .state('app.office.projectTaskDetails', {
                    url: '/projectTaskDetails',
                    templateUrl: 'tpl/office/sprintVersions/projectTaskDetails.html',
                    controller: 'projectTaskDetailsController',
                    params: {
                        id: null,
                        isSprintVersion: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/sprintVersions/projectTaskDetailsController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/sprintVersions/projectTaskService.js');
                            },
                        ],
                    },
                })
                //冲刺版本管理-bug明细
                .state('app.office.projectBugDetails', {
                    url: '/projectBugDetails',
                    templateUrl: 'tpl/office/sprintVersions/projectBugDetails.html',
                    controller: 'projectBugDetailsController',
                    params: {
                        id: null,
                        isSprintVersion: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/sprintVersions/projectBugDetailsController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/sprintVersions/projectBugService.js');
                            },
                        ],
                    },
                })
                //测试报表明细
                .state('app.office.testReportProductDetails', {
                    url: '/testReportProductDetails',
                    templateUrl: 'tpl/office/testReportProduct/testReportProductDetailsManagement.html',
                    controller: 'testReportDetailController',
                    params: {
                        functionName: null,
                        product: null,
                        index: null,
                        param: null,
                        account: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/testReportProduct/testReportProductDetailController.js'
                                );
                            },
                        ],
                    },
                })
                //测试用例覆盖率_需求通过率
                .state('app.office.storyPassRate', {
                    url: '/storyPassRateManagement',
                    templateUrl: 'tpl/office/testReportProduct/testReportProductManagement.html',
                    controller: 'testReportProductController',
                    params: {
                        functionName: 'storyPassRate',
                    },
                })
                // 看板管理
                .state('app.office.tvDisplayManagement', {
                    url: '/tvDisplayManagement',
                    templateUrl: 'tpl/office/tv/tvDisplayManagement.html',
                    controller: 'tvDisplayManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/tv/tvDisplayManagement.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/tv/tvService.js');
                            },
                        ],
                    },
                })
                // 消息管理
                .state('app.office.tvInfoManagement', {
                    url: '/tvInfoManagement',
                    templateUrl: 'tpl/office/tv/tvInfoManagement.html',
                    controller: 'tvInfoManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/tv/tvInfoManagement.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/tv/tvService.js');
                            },
                        ],
                    },
                })
                //大屏看板
                .state('tvFrame', {
                    url: '/tvFrame',
                    templateUrl: 'tpl/tv/tvFrame.html',
                    controller: 'tvFrameController',
                    resolve: {
                        loadebWSocket: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('library/angular/angular-websocket.js');
                            },
                        ],
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/tvFrame/tvFrameController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/tv/tvService.js');
                            },
                        ],
                    },
                })
                //知识分享看板
                .state('share', {
                    url: '/share',
                    templateUrl: 'tpl/tv/shareKnowledge.html',
                    controller: 'shareKnowledgeController',
                    resolve: {
                        loadPDF: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('library/component/pdf.js');
                            },
                        ],
                        loadPDFWorker: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('library/component/pdf.worker.js');
                            },
                        ],
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/tvFrame/shareKnowledgeController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/tv/tvService.js');
                            },
                        ],
                    },
                })
                //即时奖励看板
                .state('immediateReward', {
                    url: '/immediateReward',
                    templateUrl: 'tpl/tv/immediateReward.html',
                    controller: 'immediateRewardController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/tvFrame/immediateRewardController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/tv/tvService.js');
                            },
                        ],
                    },
                })
                // 2022年第一季度培训光荣榜
                .state('trainGlorious', {
                    url: '/trainGlorious',
                    templateUrl: 'tpl/tv/trainGlorious.html',
                    controller: 'trainGloriousController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/tvFrame/trainGloriousController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/tv/tvService.js');
                            },
                        ],
                        loadMyFactory: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/tvFrame/util/commonListUtil.js');
                            },
                        ],
                    },
                })
                // 2022年第一季度评审贡献榜
                .state('reviewContribution', {
                    url: '/reviewContribution',
                    templateUrl: 'tpl/tv/reviewContribution.html',
                    controller: 'reviewContributionController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/tvFrame/reviewContributionController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/tv/tvService.js');
                            },
                        ],
                        loadMyFactory: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/tvFrame/util/commonListUtil.js');
                            },
                        ],
                    },
                })
                // 2022年第一季度评审效率榜
                .state('reviewEfficiency', {
                    url: '/reviewEfficiency',
                    templateUrl: 'tpl/tv/reviewEfficiency.html',
                    controller: 'reviewEfficiencyController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/tvFrame/reviewEfficiencyController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/tv/tvService.js');
                            },
                        ],
                        loadMyFactory: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/tvFrame/util/commonListUtil.js');
                            },
                        ],
                    },
                })
                // 看板管理
                .state('app.office.tvDetailsManagement', {
                    url: '/tvDetailsManagement',
                    templateUrl: 'tpl/office/tv/tvDetailsManagement.html',
                    controller: 'tvDetailsManagement',
                    params: {
                        id: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/tv/tvDetailsManagement.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/tv/tvService.js');
                            },
                        ],
                    },
                })
                //Bug严重程度等级统计
                .state('app.office.bugSeverity', {
                    url: '/bugSeverityManagement',
                    templateUrl: 'tpl/office/testReportProduct/testReportProductManagement.html',
                    controller: 'testReportProductController',
                    params: {
                        functionName: 'bugSeverity',
                    },
                })
                //bug解决方案统计
                .state('app.office.bugResolution', {
                    url: '/bugResolutionManagement',
                    templateUrl: 'tpl/office/testReportProduct/testReportProductManagement.html',
                    controller: 'testReportProductController',
                    params: {
                        functionName: 'bugResolution',
                    },
                })
                //缺陷逃逸
                .state('app.office.leakRate', {
                    url: '/leakRateManagement',
                    templateUrl: 'tpl/office/testReportProduct/testReportProductManagement.html',
                    controller: 'testReportProductController',
                    params: {
                        functionName: 'leakRate',
                        flag: true,
                    },
                })
                //二次故障率/缺陷修改率
                .state('app.office.reopenRate', {
                    url: '/reopenRateManagement',
                    templateUrl: 'tpl/office/testReportProduct/testReportProductManagement.html',
                    controller: 'testReportProductController',
                    params: {
                        functionName: 'reopenRate',
                    },
                })
                //缺陷生存周期
                .state('app.office.bugCycle', {
                    url: '/bugCycleManagement',
                    templateUrl: 'tpl/office/testReportProduct/testReportProductManagement.html',
                    controller: 'testReportProductController',
                    params: {
                        functionName: 'bugCycle',
                    },
                })
                //用例执行覆盖率
                .state('app.office.caseExecuteRate', {
                    url: '/caseExecuteRateManagement',
                    templateUrl: 'tpl/office/testReportProduct/testReportProductManagement.html',
                    controller: 'testReportProductController',
                    params: {
                        functionName: 'caseExecuteRate',
                    },
                })
                //bug类型统计
                .state('app.office.bugType', {
                    url: '/bugTypeManagement',
                    templateUrl: 'tpl/office/testReportProduct/testReportProductManagement.html',
                    controller: 'testReportProductController',
                    params: {
                        functionName: 'bugType',
                    },
                })
                //测试用例类型统计
                .state('app.office.caseType', {
                    url: '/caseTypeManagement',
                    templateUrl: 'tpl/office/testReportProduct/testReportProductManagement.html',
                    controller: 'testReportProductController',
                    params: {
                        functionName: 'caseType',
                    },
                })
                //测试用例能手
                .state('app.office.caseDevote', {
                    url: '/caseDevoteManagement',
                    templateUrl: 'tpl/office/testReportProduct/testReportProductManagement.html',
                    controller: 'testReportProductController',
                    params: {
                        functionName: 'caseDevote',
                        flag: true,
                        department: '20',
                    },
                })
                //bug发现能手
                .state('app.office.bugFound', {
                    url: '/bugFoundManagement',
                    templateUrl: 'tpl/office/testReportProduct/testReportProductManagement.html',
                    controller: 'testReportProductController',
                    params: {
                        functionName: 'bugFound',
                        flag: true,
                        department: '20',
                    },
                })
                //bug贡献能手
                .state('app.office.bugDevote', {
                    url: '/bugDevoteManagement',
                    templateUrl: 'tpl/office/testReportProduct/testReportProductManagement.html',
                    controller: 'testReportProductController',
                    params: {
                        functionName: 'bugDevote',
                        flag: true,
                    },
                })
                //提测质量
                .state('app.office.testTaskQuality', {
                    url: '/testTaskQualityManagement',
                    templateUrl: 'tpl/office/testReportProduct/testReportProductManagement.html',
                    controller: 'testReportProductController',
                    params: {
                        functionName: 'testTaskQuality',
                        flag: true,
                    },
                })
                //成本监控
                .state('app.office.costMonitoring', {
                    url: '/costMonitoring',
                    templateUrl: 'tpl/office/cost/costMonitoring/costMonitoringManagement.html',
                    controller: 'costMonitoringController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/cost/costMonitoring/costMonitoringController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/cost/costMonitoring/costMonitoringService.js'
                                );
                            },
                        ],
                    },
                })
                //成本监控--项目成本、维护成本投入
                .state('app.office.costInfo', {
                    url: '/costInfo',
                    templateUrl: 'tpl/office/cost/costMonitoring/costInfoManagement.html',
                    controller: 'costInfoController',
                    params: {
                        functionType: null,
                        projectId: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/cost/costMonitoring/costInfoController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/cost/costMonitoring/costMonitoringService.js'
                                );
                            },
                        ],
                    },
                })
                //成本监控--变更记录
                .state('app.office.changeInfo', {
                    url: '/changeInfo',
                    templateUrl: 'tpl/office/cost/costMonitoring/changeInfoManagement.html',
                    controller: 'changeInfoController',
                    params: {
                        functionType: null,
                        projectId: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/cost/costMonitoring/changeInfoController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/cost/costMonitoring/costMonitoringService.js'
                                );
                            },
                        ],
                    },
                })
                //成本监控--变更明细
                .state('app.office.changeDetails', {
                    url: '/changeDetails',
                    templateUrl: 'tpl/office/cost/costMonitoring/changeDetailsManagement.html',
                    controller: 'changeInfoController',
                    params: {
                        functionType: null,
                        projectId: null,
                        nowVersion: null,
                        comperVersion: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/cost/costMonitoring/changeInfoController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/cost/costMonitoring/costMonitoringService.js'
                                );
                            },
                        ],
                    },
                })
                //成本监控--费用明细
                .state('app.office.freeDetails', {
                    url: '/freeDetails',
                    templateUrl: 'tpl/office/cost/costMonitoring/freeDetailsManagement.html',
                    controller: 'changeInfoController',
                    params: {
                        functionType: null,
                        projectId: null,
                        status: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/cost/costMonitoring/changeInfoController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/cost/costMonitoring/costMonitoringService.js'
                                );
                            },
                        ],
                    },
                })
                //费用记录
                .state('app.office.feeRecord', {
                    url: '/feeRecord',
                    templateUrl: 'tpl/office/cost/feeRecord/feeRecordManagement.html',
                    controller: 'feeRecordController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/cost/feeRecord/feeRecordController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/cost/feeRecord/feeRecordService.js');
                            },
                        ],
                    },
                })
                //项目成本管理-预算管理-预算变更导入
                .state('app.office.changePersonBudget', {
                    url: '/changePersonBudget',
                    templateUrl: 'tpl/office/cost/changePersonBudget.html',
                    controller: 'changePersonBudgetController',
                    params: {
                        projectId: null,
                        projectName: null,
                        version: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/cost/changePersonBudgetController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/cost/costInputService.js');
                            },
                        ],
                    },
                })
                //项目成本管理-预算变更申请
                .state('app.office.budgetChangeApplyController', {
                    url: '/budgetChangeApplyController',
                    templateUrl: 'tpl/office/cost/budgetChangeApplyManagement.html',
                    controller: 'budgetChangeApplyController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/cost/budgetChangeApplyController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/cost/budgetService.js',
                                    'js/services/office/cost/budgetChangeApplyService.js',
                                ]);
                            },
                        ],
                    },
                })
                //项目成本管理-预算变更申请-预算变更申请生成
                .state('app.office.budgetChangeApplyDetails', {
                    url: '/budgetChangeApplyDetails',
                    templateUrl: 'tpl/office/cost/budgetChangeApplyDetails.html',
                    controller: 'budgetChangeApplyController',
                    params: {
                        projectId: null,
                        projectName: null,
                        projectManager: null,
                        version: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/cost/budgetChangeApplyController.js');
                            },
                        ],
                    },
                })

                //考核关系
                .state('app.office.kpiRelation', {
                    url: '/kpiRelation',
                    templateUrl: 'tpl/office/kpi/kpiRelationManagement.html',
                    controller: 'kpiRelationController',
                    params: {
                        entry: 'menu',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/kpi/kpiRelationController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(['js/services/office/kpi/kpiRelationService.js']);
                            },
                        ],
                    },
                })
                //考核流程
                .state('app.office.performanceAssess', {
                    url: '/performanceAssess',
                    templateUrl: 'tpl/office/performanceAssess/performanceAssessManagement.html',
                    controller: 'performanceAssessController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/performanceAssess/performanceAssessController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/kpi/kpiRelationService.js',
                                    'js/services/office/humanResStatistics/attendanceStatisticsService.js',
                                ]);
                            },
                        ],
                    },
                })
                //绩效考核
                .state('app.office.performanceAssessEvaluate', {
                    url: '/performanceAssessEvaluate',
                    templateUrl: 'tpl/office/performanceAssess/performanceAssessEvaluate.html',
                    controller: 'performanceAssessEvaluateController',
                    params: {
                        jsonResult: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/performanceAssess/performanceAssessEvaluateController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/kpi/kpiRelationService.js',
                                    'js/services/office/roleKpi/roleKpiService.js',
                                ]);
                            },
                        ],
                    },
                })
                //绩效考核节选
                .state('app.office.performanceAssessSection', {
                    url: '/performanceAssessSectionController',
                    templateUrl: 'tpl/office/performanceAssess/performanceAssessSection.html',
                    controller: 'performanceAssessSectionController',
                    params: {
                        urlData: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/performanceAssess/performanceAssessSectionController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/kpi/kpiRelationService.js');
                            },
                        ],
                    },
                })
                // 绩效考核--绩效辅导记录管理（列表展示页）
                .state('app.office.performanceLogManagement', {
                    url: '/performanceLogManagement',
                    templateUrl: 'tpl/office/performanceLogManagement/performanceLogManagement.html',
                    controller: 'performanceLogManagement',
                    params: {
                        // 类型：edit/view
                        type: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/performanceLogManagement/performanceLogManagement.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/performanceLogManagement/performanceLogManagementService.js'
                                );
                            },
                        ],
                    },
                })
                // 绩效考核--绩效辅导记录（新增/编辑/查看辅导记录页面）
                .state('app.office.coachingLogUpdate', {
                    url: '/coachingLogUpdate',
                    templateUrl: 'tpl/office/performanceLogManagement/coachingLogUpdate.html',
                    controller: 'coachingLogUpdate',
                    params: {
                        // 类型：新增/编辑/查看
                        type: null,
                        // 任务id
                        id: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/performanceLogManagement/coachingLogUpdate.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/performanceLogManagement/performanceLogManagementService.js'
                                );
                            },
                        ],
                    },
                })
                // 绩效考核--绩效沟通记录（新增/编辑/查看沟通记录页面）
                .state('app.office.communicationLogUpdate', {
                    url: '/communicationLogUpdate',
                    templateUrl: 'tpl/office/performanceLogManagement/communicationLogUpdate.html',
                    controller: 'communicationLogUpdate',
                    params: {
                        // 类型：新增/编辑/查看
                        type: null,
                        // 任务id
                        id: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/performanceLogManagement/communicationLogUpdate.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/performanceLogManagement/performanceLogManagementService.js'
                                );
                            },
                        ],
                    },
                })
                //工时负载部门界面
                .state('app.office.workHoursLoadDepartment', {
                    url: '/workHoursLoadDepartment',
                    templateUrl: 'tpl/office/workHoursLoad/workHoursLoadManager.html',
                    controller: 'workHoursLoadController',
                    params: {
                        menu: 'department',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workHoursLoad/workHoursLoadController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/workHoursLoad/workHoursLoadService.js');
                            },
                        ],
                    },
                })
                //工时负载项目经理页面
                .state('app.office.workHoursLoadManager', {
                    url: '/workHoursLoadManager',
                    templateUrl: 'tpl/office/workHoursLoad/workHoursLoadManager.html',
                    controller: 'workHoursLoadController',
                    params: {
                        menu: 'manager',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workHoursLoad/workHoursLoadController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/workHoursLoad/workHoursLoadService.js');
                            },
                        ],
                    },
                })
                //项目成本管理-预算管理
                .state('app.office.budgetController', {
                    url: '/budgetController',
                    templateUrl: 'tpl/office/cost/budgetManagement.html',
                    controller: 'budgetController',
                    params: {
                        isProjectBudget: null, //1：项目预算；2：PLM预算
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/cost/budgetController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/cost/costInputService.js',
                                    'js/services/office/cost/budgetService.js',
                                ]);
                            },
                        ],
                    },
                })
                //项目成本管理-预算管理-确认预算
                .state('app.office.confrimBudgetController', {
                    url: '/confrimBudgetController',
                    templateUrl: 'tpl/office/cost/confrimBudget.html',
                    controller: 'confrimBudgetController',
                    params: {
                        isProjectBudget: null, //1：项目预算；2：PLM预算
                        projectId: null,
                        projectName: null,
                        plmUpgradeId: null,
                        version: null,
                        isAddBudget: null,
                        isDetail: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/cost/confrimBudgetController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(['js/services/office/cost/budgetService.js']);
                            },
                        ],
                    },
                })
                //项目成本管理-预算管理-预算详情(不可修改)
                .state('app.office.detailBudgetController', {
                    url: '/detailBudgetController',
                    templateUrl: 'tpl/office/cost/detailBudget.html',
                    controller: 'confrimBudgetController',
                    params: {
                        isProjectBudget: null, //1：项目预算；2：PLM预算
                        projectId: null,
                        projectName: null,
                        plmUpgradeId: null,
                        version: null,
                        isDetail: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/cost/confrimBudgetController.js');
                            },
                        ],
                    },
                })
                //项目成本管理-预算管理-预算变更
                .state('app.office.changePersonBudgetController', {
                    url: '/changePersonBudgetController',
                    templateUrl: 'tpl/office/cost/changePersonBudget.html',
                    controller: 'changePersonBudgetController',
                    params: {
                        projectId: null,
                        projectName: null,
                        plmUpgradeId: null,
                        version: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/cost/changePersonBudgetController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/cost/budgetService.js',
                                    'js/services/office/cost/budgetChangeApplyService.js',
                                ]);
                            },
                        ],
                    },
                })
                //项目成本管理-结算管理
                .state('app.office.settleController', {
                    url: '/settleController',
                    templateUrl: 'tpl/office/cost/settleManagement.html',
                    controller: 'settleController',
                    params: {
                        type: null, //1：项目结算；2：PLM结算
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/cost/settleController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/cost/costMonitoring/costMonitoringService.js',
                                    'js/services/office/cost/settleService.js',
                                ]);
                            },
                        ],
                    },
                })
                //项目成本管理-结算明细预览 settleInfo
                .state('app.office.settleInfo', {
                    url: '/settleInfo',
                    templateUrl: 'tpl/office/cost/settleInfo.html',
                    controller: 'settleInfoController',
                    params: {
                        projectId: null,
                        plm: null,
                        status: null,
                        projectName: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/cost/settleInfoController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/cost/costMonitoring/costMonitoringService.js',
                                    'js/services/office/cost/settleService.js',
                                ]);
                            },
                        ],
                    },
                })
                //成本投入管理
                .state('app.office.costInputManager', {
                    url: '/castInputManager',
                    templateUrl: 'tpl/office/cost/costInputManager.html',
                    controller: 'costInputManagerController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/cost/costInputManagerController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/cost/costInputService.js');
                            },
                        ],
                    },
                })

                //费用投入确认
                .state('app.office.feeConfirm', {
                    url: '/feeConfirm',
                    templateUrl: 'tpl/office/cost/feeConfirm.html',
                    controller: 'feeConfirmController',
                    params: {
                        id: null,
                        projectId: null,
                        companyProjectId: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/cost/feeConfirmController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/cost/costInputService.js');
                            },
                        ],
                    },
                })

                //人力投入明细
                .state('app.office.costHrInputDetail', {
                    url: '/costHrInputDetail',
                    templateUrl: 'tpl/office/cost/costHrInputDetail.html',
                    controller: 'costHrInputDetailController',
                    params: {
                        detailItem: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/cost/costHrInputDetailController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/cost/costInputService.js');
                            },
                        ],
                    },
                })
                //项目团队
                .state('app.office.projectTeam', {
                    url: '/projectTeam',
                    templateUrl: 'tpl/office/cost/projectTeam.html',
                    controller: 'projectTeamController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/cost/projectTeamController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/cost/projectTeamService.js');
                            },
                        ],
                    },
                })
                //项目团队详细
                .state('app.office.projectTeamDetail', {
                    url: '/projectTeamDetail',
                    templateUrl: 'tpl/office/cost/projectTeamDetail.html',
                    controller: 'projectTeamDetailController',
                    params: {
                        projectId: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/cost/projectTeamDetailController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/cost/projectTeamService.js');
                            },
                        ],
                    },
                })
                // 冲刺成功率报表
                .state('app.office.sprintSuccessRate', {
                    url: '/sprintSuccessRate',
                    templateUrl: 'tpl/office/sprintVersions/sprintSuccessRate.html',
                    controller: 'sprintSuccessRateController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/sprintVersions/sprintSuccessRate.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/sprintVersions/sprintVersionsService.js');
                            },
                        ],
                    },
                })
                //项目Kpi查询
                .state('app.office.projectKpiInfo', {
                    url: '/projectKpiInfo',
                    templateUrl: 'tpl/office/projectKpi/kpiInfo/kpiInfo.html',
                    controller: 'kpiInfoController',
                    params: {
                        type: '0',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiInfoController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiInfoService.js');
                            },
                        ],
                    },
                })
                //团队Kpi查询
                .state('app.office.teamKpiInfo', {
                    url: '/teamKpiInfo',
                    templateUrl: 'tpl/office/projectKpi/kpiInfo/kpiInfo.html',
                    controller: 'kpiInfoController',
                    params: {
                        type: '1',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiInfoController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiInfoService.js');
                            },
                        ],
                    },
                })
                //项目Kpi详情
                .state('app.office.projectKpiDetail', {
                    url: '/projectKpiDetail',
                    templateUrl: 'tpl/office/projectKpi/kpiInfo/projectKpiDetail.html',
                    controller: 'kpiDetailsController',
                    params: {
                        kpiId: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiDetailsController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiManagemetService.js');
                            },
                        ],
                        loadMyService1: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiInfoService.js');
                            },
                        ],
                    },
                })
                //团队Kpi详情
                .state('app.office.teamKpiDetail', {
                    url: '/teamKpiDetail',
                    templateUrl: 'tpl/office/projectKpi/kpiInfo/teamKpiDetail.html',
                    controller: 'kpiDetailsController',
                    params: {
                        kpiId: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiDetailsController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiManagemetService.js');
                            },
                        ],
                        loadMyService1: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiInfoService.js');
                            },
                        ],
                    },
                })
                //项目交付能力详情
                .state('app.office.KPI_0_01', {
                    url: '/proKpi01',
                    templateUrl: 'tpl/office/projectKpi/kpiInfo/projectKpiDetails/KPI_0_01.html',
                    controller: 'kpiDetailRulesController',
                    params: {
                        kpiId: null,
                        firstKpiCode: null,
                        returnFunction: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiDetailRulesController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiInfoService.js');
                            },
                        ],
                    },
                })
                //项目交付质量详情
                .state('app.office.KPI_0_02', {
                    url: '/proKpi02',
                    templateUrl: 'tpl/office/projectKpi/kpiInfo/projectKpiDetails/KPI_0_02.html',
                    controller: 'kpiDetailRulesController',
                    params: {
                        kpiId: null,
                        firstKpiCode: null,
                        returnFunction: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiDetailRulesController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiInfoService.js');
                            },
                        ],
                    },
                })
                //项目交付过程质量详情
                .state('app.office.KPI_0_03', {
                    url: '/proKpi03',
                    templateUrl: 'tpl/office/projectKpi/kpiInfo/projectKpiDetails/KPI_0_03.html',
                    controller: 'kpiDetailRulesController',
                    params: {
                        kpiId: null,
                        firstKpiCode: null,
                        returnFunction: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiDetailRulesController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiInfoService.js');
                            },
                        ],
                    },
                })
                //项目成本控制详情
                .state('app.office.KPI_0_04', {
                    url: '/proKpi04',
                    templateUrl: 'tpl/office/projectKpi/kpiInfo/projectKpiDetails/KPI_0_04.html',
                    controller: 'kpiDetailRulesController',
                    params: {
                        kpiId: null,
                        firstKpiCode: null,
                        returnFunction: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiDetailRulesController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiInfoService.js');
                            },
                        ],
                    },
                })
                //团队交付能力详情
                .state('app.office.KPI_1_01', {
                    url: '/teamKpi01',
                    templateUrl: 'tpl/office/projectKpi/kpiInfo/teamKpiDetails/KPI_1_01.html',
                    controller: 'kpiDetailRulesController',
                    params: {
                        kpiId: null,
                        firstKpiCode: null,
                        returnFunction: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiDetailRulesController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiInfoService.js');
                            },
                        ],
                    },
                })
                //团队交付质量详情
                .state('app.office.KPI_1_02', {
                    url: '/teamKpi02',
                    templateUrl: 'tpl/office/projectKpi/kpiInfo/teamKpiDetails/KPI_1_02.html',
                    controller: 'kpiDetailRulesController',
                    params: {
                        kpiId: null,
                        firstKpiCode: null,
                        returnFunction: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiDetailRulesController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiInfoService.js');
                            },
                        ],
                    },
                })
                //团队交付过程质量详情
                .state('app.office.KPI_1_03', {
                    url: '/teamKpi03',
                    templateUrl: 'tpl/office/projectKpi/kpiInfo/teamKpiDetails/KPI_1_03.html',
                    controller: 'kpiDetailRulesController',
                    params: {
                        kpiId: null,
                        firstKpiCode: null,
                        returnFunction: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiDetailRulesController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiInfoService.js');
                            },
                        ],
                    },
                })
                //项目考核历史数据查询
                .state('app.office.projectKpiHistory', {
                    url: '/projectKpiHistory',
                    templateUrl: 'tpl/office/projectKpi/kpiInfo/kpiHistory.html',
                    controller: 'kpiHistoryController',
                    params: {
                        type: '0',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiHistoryController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiInfoService.js');
                            },
                        ],
                    },
                })
                //团队考核历史数据查询
                .state('app.office.teamKpiHistory', {
                    url: '/teamKpiHistory',
                    templateUrl: 'tpl/office/projectKpi/kpiInfo/kpiHistory.html',
                    controller: 'kpiHistoryController',
                    params: {
                        type: '1',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiHistoryController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiInfoService.js');
                            },
                        ],
                    },
                })
                //项目考核管理
                .state('app.office.proKpiManagement', {
                    url: '/proKpiManagement',
                    templateUrl: 'tpl/office/projectKpi/kpiManagement/proKpiManagement.html',
                    controller: 'kpiManagemetController',
                    params: {
                        flag: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiManagemetController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiManagemetService.js');
                            },
                        ],
                    },
                })
                //团队考核管理
                .state('app.office.teamKpiManagement', {
                    url: '/teamKpiManagement',
                    templateUrl: 'tpl/office/projectKpi/kpiManagement/teamKpiManagement.html',
                    controller: 'kpiManagemetController',
                    params: {
                        flag: 'managemet',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiManagemetController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiManagemetService.js');
                            },
                        ],
                    },
                })
                //团队考核监控
                .state('app.office.teamKpiMonitor', {
                    url: '/teamKpiMonitor',
                    templateUrl: 'tpl/office/projectKpi/kpiManagement/teamKpiManagement.html',
                    controller: 'kpiManagemetController',
                    params: {
                        flag: 'monitor',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiManagemetController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiManagemetService.js');
                            },
                        ],
                    },
                })
                //项目Kpi详情权重管理
                .state('app.office.projectKpiManagementDetail', {
                    url: '/projectKpiManagementDetail',
                    templateUrl: 'tpl/office/projectKpi/kpiManagement/projectKpiManagementDetail.html',
                    controller: 'kpiDetailsController',
                    params: {
                        kpiId: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiDetailsController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiManagemetService.js');
                            },
                        ],
                        loadMyService1: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiInfoService.js');
                            },
                        ],
                    },
                })
                //团队Kpi详情权重管理
                .state('app.office.teamKpiManagementDetail', {
                    url: '/teamKpiManagementDetail',
                    templateUrl: 'tpl/office/projectKpi/kpiManagement/teamKpiManagementDetail.html',
                    controller: 'kpiDetailsController',
                    params: {
                        kpiId: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectKpi/kpiDetailsController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiManagemetService.js');
                            },
                        ],
                        loadMyService1: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/kpiInfoService.js');
                            },
                        ],
                    },
                })
                // 项目经理&团队负责人评价管理
                .state('app.office.evaluateManagement', {
                    url: '/evaluateManagement',
                    templateUrl: 'tpl/office/projectKpi/evaluate/evaluateManagement.html',
                    controller: 'evaluateManagementController',
                    params: {
                        flag: 'management',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectKpi/evaluateManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/evaluateService.js');
                            },
                        ],
                    },
                })
                // 项目经理&团队负责人评价历史查看
                .state('app.office.evaluateHistory', {
                    url: '/evaluateHistory',
                    templateUrl: 'tpl/office/projectKpi/evaluate/evaluateManagement.html',
                    controller: 'evaluateManagementController',
                    params: {
                        flag: 'history',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectKpi/evaluateManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/evaluateService.js');
                            },
                        ],
                    },
                })
                // 项目经理&团队负责人评价修改
                .state('app.office.evaluateModify', {
                    url: '/evaluateModify',
                    templateUrl: 'tpl/office/projectKpi/evaluate/evaluateModifyOrDetail.html',
                    controller: 'evaluateModifyOrDetailController',
                    params: {
                        flag: 'modify',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectKpi/evaluateModifyOrDetailController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/evaluateService.js');
                            },
                        ],
                    },
                })
                // 项目经理&团队负责人评价查看
                .state('app.office.evaluateDetail', {
                    url: '/evaluateDetail',
                    templateUrl: 'tpl/office/projectKpi/evaluate/evaluateModifyOrDetail.html',
                    controller: 'evaluateModifyOrDetailController',
                    params: {
                        flag: 'detail',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectKpi/evaluateModifyOrDetailController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/projectKpi/evaluateService.js');
                            },
                        ],
                    },
                })
                // 项目入口
                .state('app.office.projectEntrance', {
                    url: '/projectEntrance',
                    templateUrl: 'tpl/office/projectEntrance/projectEntrance.html',
                    controller: 'projectManagement',
                    params: {
                        flag: 'entrance',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectScheduleDeviation/projectManagementController.js'
                                );
                            },
                        ],
                    },
                })
                // 团队入口
                .state('app.office.teamEntrance', {
                    url: '/teamEntrance',
                    templateUrl: 'tpl/office/projectEntrance/teamEntrance.html',
                    controller: 'groupManagement',
                    params: {
                        flag: 'entrance',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectScheduleDeviation/groupManagementController.js'
                                );
                            },
                        ],
                    },
                })
                // 项目详情
                .state('app.office.projectEntranceDetail', {
                    url: '/projectEntranceDetail',
                    templateUrl: 'tpl/office/projectEntrance/projectEntranceDetail.html',
                    controller: 'projectEntranceDetailsController',
                    params: {
                        projectInfoParam: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectEntrance/projectEntranceDetailsController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/cost/costMonitoring/costMonitoringService.js'
                                );
                            },
                        ],
                    },
                })
                // 团队详情
                .state('app.office.teamEntranceDetail', {
                    url: '/teamEntranceDetail',
                    templateUrl: 'tpl/office/projectEntrance/teamEntranceDetail.html',
                    controller: 'teamEntranceDetailsController',
                    params: {
                        type: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectEntrance/teamEntranceDetailsController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/projectEntrance/teamEntranceDetailsService.js'
                                );
                            },
                        ],
                    },
                })
                // 团队-reopen-bug详情
                .state('app.office.teamEntranceBugReopen', {
                    url: '/teamEntranceBugReopen',
                    templateUrl: 'tpl/office/projectEntrance/teamEntranceDetail/teamEntranceBugReopenInfo.html',
                    controller: 'teamEntranceBugReopenController',
                })
                // 团队-jenkins构建数据
                .state('app.office.jenkinsJobDataReport', {
                    url: '/jenkinsJobDataReport',
                    templateUrl: 'tpl/office/projectEntrance/teamEntranceDetail/jenkinsJobDataReport.html',
                    controller: 'jenkinsJobDataReportController',
                })

                // 团队-jenkins构建明细
                .state('app.office.jobBuild', {
                    url: '/jobBuildList',
                    templateUrl: 'tpl/office/projectEntrance/teamEntranceDetail/jobBuildList.html',
                    controller: 'jobBuild',
                    params: {
                        jobName: null,
                        startTime: null,
                        endTime: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/projectEntrance/jobBuildController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/projectEntrance/teamEntranceDetailsService.js'
                                );
                            },
                        ],
                    },
                })

                //各仓库代码提交明细
                .state('app.office.repoCommitList', {
                    url: '/repoCommitList',
                    templateUrl: 'tpl/office/projectEntrance/teamEntranceDetail/repoCommitList.html',
                    controller: 'repoCommit',
                    params: {
                        repoName: null,
                        projectId: null,
                        startTime: null,
                        endTime: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/projectEntrance/repoCommitController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/projectEntrance/teamEntranceDetailsService.js'
                                );
                            },
                        ],
                    },
                })
                // 个人看板入口
                /*              .state('app.office.personalDataBoardEntrance', {
                  url: '/personalDataBoardEntrance',
                  templateUrl: 'tpl/office/personalDataBoard/personalDataBoard.html',
                  controller: 'personalDataBoardController',
                  params: {
                      "flag": 'entrance'
                  },
                  resolve: {
                    loadMyCtrl: ['$ocLazyLoad', function($ocLazyLoad){
                    return $ocLazyLoad.load('js/controllers/office/personalDataBoard/personalDataBoardController.js')
                    }],
                    loadMyService: ['$ocLazyLoad', function($ocLazyLoad){
                    return $ocLazyLoad.load('js/services/office/personalDataBoard/personalDataBoardService.js')
                    }]
                }
              })*/
                //反馈问题录入
                .state('app.office.feedbackProblem', {
                    url: '/feedbackProblem',
                    templateUrl: 'tpl/office/feedbackProblem/feedbackProblem.html',
                    controller: 'feedbackProblemController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/feedbackProblem/feedbackProblemController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/feedbackProblem/feedbackProblemService.js');
                            },
                        ],
                    },
                })
                //反馈问题新增、修改、详情
                .state('app.office.feedbackProblemManagement', {
                    url: '/feedbackProblemManagement',
                    templateUrl: 'tpl/office/feedbackProblem/feedbackProblemManagement.html',
                    controller: 'feedbackProblemManagementController',
                    params: {
                        item: null,
                        flag: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/feedbackProblem/feedbackProblemManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/feedbackProblem/feedbackProblemService.js');
                            },
                        ],
                    },
                })
                //生成钉钉跟踪单流程
                .state('app.office.createTrackingSheet', {
                    url: '/createTrackingSheet',
                    templateUrl: 'tpl/office/feedbackProblem/toTrackingSheet.html',
                    controller: 'toTrackingSheetController',
                    params: {
                        item: null,
                        projectName: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/feedbackProblem/toTrackingSheetController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/feedbackProblem/feedbackProblemService.js');
                            },
                        ],
                    },
                })
                //生成停机事件
                .state('app.office.createOnlineServerEvent', {
                    url: '/createOnlineServerEvent',
                    templateUrl: 'tpl/office/feedbackProblem/toOnlineServerEvent.html',
                    controller: 'toOnlineServerEventController',
                    params: {
                        item: null,
                        projectName: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/feedbackProblem/toOnlineServerEventController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/feedbackProblem/feedbackProblemService.js');
                            },
                        ],
                    },
                })
                //跟踪单
                .state('app.office.trackingSheet', {
                    url: '/trackingSheet',
                    templateUrl: 'tpl/office/trackingSheet/trackingSheet.html',
                    controller: 'trackingSheetController',
                    params: {
                        type: 'menu',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/trackingSheet/trackingSheetController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/trackingSheet/trackingSheetService.js',
                                    'js/services/office/lowQualityProblem/lowQualityService.js',
                                ]);
                            },
                        ],
                    },
                })
                //跟踪单修改、详情
                .state('app.office.trackingSheetManagement', {
                    url: '/trackingSheetManagement',
                    templateUrl: 'tpl/office/trackingSheet/trackingSheetManagement.html',
                    controller: 'trackingSheetManagementController',
                    params: {
                        item: null,
                        flag: null,
                        batchNum: null,
                        activatedCount: null,
                        approvalNum: null,
                        startTime: null,
                        sponsorName: null,
                        productLine: null,
                        department: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/trackingSheet/trackingSheetManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/trackingSheet/trackingSheetService.js');
                            },
                        ],
                    },
                })
                //非共性问题跟踪单
                .state('app.office.notCommonProblemsSheet', {
                    url: '/notCommonProblemsSheet',
                    templateUrl: 'tpl/office/trackingSheet/notCommonProblemsSheet.html',
                    controller: 'trackingSheetController',
                    params: {
                        notCommonProblem: '非共性问题',
                        type: 'menu',
                    },
                })
                //git管理
                .state('app.office.git', {
                    url: '/git',
                    templateUrl: 'tpl/office/git/gitManagement.html',
                    controller: 'gitController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/git/gitController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/git/gitService.js');
                            },
                        ],
                    },
                })
                //访问量
                .state('app.visits', {
                    url: '/visits',
                    templateUrl: 'tpl/office/visits/visits.html',
                    controller: 'visitsController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/visits/visitsController.js');
                            },
                        ],
                    },
                })
                //用户需求查询
                .state('app.office.customerStory', {
                    url: '/customerStory',
                    templateUrl: 'tpl/office/workingHoursStatistics/customerStory.html',
                    controller: 'customerStoryController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursStatistics/customerStoryController.js'
                                );
                            },
                        ],
                    },
                })
                //用户需求管理
                .state('app.office.customerStoryManagement', {
                    url: '/customerStoryManagement',
                    templateUrl: 'tpl/office/workingHoursStatistics/customerStoryManagement.html',
                    controller: 'customerStoryManagementController',
                    params: {
                        item: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursStatistics/customerStoryManagementController.js'
                                );
                            },
                        ],
                    },
                })
                //用户需求详情
                .state('app.office.customerStoryDetail', {
                    url: '/customerStoryDetail',
                    templateUrl: 'tpl/office/workingHoursStatistics/customerStoryDetail.html',
                    controller: 'customerStoryDetailController',
                    params: {
                        item: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursStatistics/customerStoryDetailController.js'
                                );
                            },
                        ],
                    },
                })
                //客户信息管理
                .state('app.office.customerManagement', {
                    url: '/customerManagement',
                    templateUrl: 'tpl/office/workingHoursStatistics/productData/customerManagement.html',
                    controller: 'customerManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursStatistics/productData/customerManagementController.js'
                                );
                            },
                        ],
                    },
                })
                //客户信息管理--添加/修改
                .state('app.office.customerManagementUpdate', {
                    url: '/customerManagementUpdate',
                    templateUrl: 'tpl/office/workingHoursStatistics/productData/customerManagementUpdate.html',
                    controller: 'customerManagementUpdate',
                    params: {
                        customerInfoParam: null,
                        isAdd: null, //0:新增；1：修改
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursStatistics/productData/customerManagementUpdateController.js'
                                );
                            },
                        ],
                    },
                })
                //硬件型号管理
                .state('app.office.hardwareModeManagement', {
                    url: '/hardwareModeManagement',
                    templateUrl: 'tpl/office/workingHoursStatistics/productData/hardwareModeManagement.html',
                    controller: 'hardwareModeManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursStatistics/productData/hardwareModeManagementController.js'
                                );
                            },
                        ],
                    },
                })
                //硬件型号管理--添加/修改
                .state('app.office.hardwareModeManagementUpdate', {
                    url: '/hardwareModeManagementUpdate',
                    templateUrl: 'tpl/office/workingHoursStatistics/productData/hardwareModeManagementUpdate.html',
                    controller: 'hardwareModeManagementUpdate',
                    params: {
                        hardwareModeInfoParam: null,
                        isAdd: null, //0:新增；1：修改
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursStatistics/productData/hardwareModeManagementUpdateController.js'
                                );
                            },
                        ],
                    },
                })
                //公司立项项目管理
                .state('app.office.companyProjectManagement', {
                    url: '/companyProjectManagement',
                    templateUrl: 'tpl/office/workingHoursStatistics/productData/companyProjectManagement.html',
                    controller: 'companyProjectManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursStatistics/productData/companyProjectManagementController.js'
                                );
                            },
                        ],
                    },
                })
                //公司立项项目管理--添加/修改
                .state('app.office.companyProjectManagementUpdate', {
                    url: '/companyProjectManagementUpdate',
                    templateUrl: 'tpl/office/workingHoursStatistics/productData/companyProjectManagementUpdate.html',
                    controller: 'companyProjectManagementUpdate',
                    params: {
                        projectId: null,
                        isAdd: null, //0:新增；1：修改
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursStatistics/productData/companyProjectManagementUpdateController.js'
                                );
                            },
                        ],
                    },
                })
                // 工时拆分查询信息
                .state('app.office.workingHoursManagementSplit', {
                    url: '/workingHoursManagementSplit',
                    templateUrl: 'tpl/office/workingHoursStatistics/workingHoursManagement.html',
                    controller: 'workingHoursManagementController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursStatistics/workingHoursManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/workingHoursStatistics/workingHoursService.js'
                                );
                            },
                        ],
                    },
                })
                // 工时拆分
                .state('app.office.workingHoursSplit', {
                    url: '/workingHoursSplit',
                    templateUrl: 'tpl/office/workingHoursStatistics/workingHoursSplit.html',
                    controller: 'workingHoursSplitController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workingHoursStatistics/workingHoursSplitController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/workingHoursStatistics/workingHoursService.js'
                                );
                            },
                        ],
                    },
                })
                //红黑事件管理
                .state('app.office.staffDailyActionManagement', {
                    url: '/staffDailyActionManagement',
                    templateUrl: 'tpl/office/staffDailyAction/staffDailyActionManagement.html',
                    controller: 'staffDailyActionManagementController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/staffDailyAction/staffDailyActionManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/staffDailyAction/staffDailyActionService.js'
                                );
                            },
                        ],
                    },
                })
                //红黑事件添加
                .state('app.office.staffDailyActionAdd', {
                    url: '/staffDailyActionAdd',
                    templateUrl: 'tpl/office/staffDailyAction/staffDailyActionAdd.html',
                    controller: 'staffDailyActionAddController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/staffDailyAction/staffDailyActionAddController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/staffDailyAction/staffDailyActionService.js'
                                );
                            },
                        ],
                    },
                })
                //红黑事件更新
                .state('app.office.staffDailyActionUpdate', {
                    url: '/staffDailyActionUpdate',
                    templateUrl: 'tpl/office/staffDailyAction/staffDailyActionAdd.html',
                    controller: 'staffDailyActionAddController',
                    params: {
                        item: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/staffDailyAction/staffDailyActionAddController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/staffDailyAction/staffDailyActionService.js'
                                );
                            },
                        ],
                    },
                })

                //bug数据管理
                .state('app.office.bugDataManagement', {
                    url: '/bugDataManagement',
                    templateUrl: 'tpl/office/bugdata/bugDataManagement.html',
                    controller: 'bugDataManagementController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/bugdata/bugDataManagementController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/bugdata/bugDataManagementService.js');
                            },
                        ],
                    },
                })
                //bug数据reopen详细
                .state('app.office.bugDataReopenDetail', {
                    url: '/bugDataReopenDetail',
                    templateUrl: 'tpl/office/bugdata/bugDataReopenDetail.html',
                    controller: 'bugDataReopenDetailController',
                    params: {
                        bugId: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/bugdata/bugDataReopenDetailController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/bugdata/bugDataManagementService.js');
                            },
                        ],
                    },
                })

                //一点改善
                .state('app.office.correct', {
                    url: '/correct',
                    templateUrl: 'tpl/office/correct/correct.html',
                    controller: 'correctController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/correct/correctController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/correct/correctService.js');
                            },
                        ],
                    },
                })
                //工时统计-公司立项项目工时统计
                .state('app.office.companyProWorkTime', {
                    url: '/companyProWorkTimeManagement',
                    templateUrl: 'tpl/office/workTime/companyProWorkTimeManagement.html',
                    controller: 'companyProWorkTimeManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workTime/companyProWorkTimeController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/workTime/companyProWorkTimeService.js');
                            },
                        ],
                    },
                })
                //工时统计-公司立项项目工时统计-详情
                .state('app.office.companyProWorkTimeDetail', {
                    url: '/companyProWorkTimeManagementDetail',
                    templateUrl: 'tpl/office/workTime/companyProWorkTimeManagementDetail.html',
                    controller: 'companyProWorkTimeManagementDetail',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workTime/companyProWorkTimeDetailController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/workTime/companyProWorkTimeService.js');
                            },
                        ],
                    },
                })
                //工时统计-客户工时统计
                .state('app.office.customerWorkTime', {
                    url: '/customerWorkTimeManagement',
                    templateUrl: 'tpl/office/workTime/customerWorkTimeManagement.html',
                    controller: 'customerWorkTimeManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/workTime/customerWorkTimeController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/workTime/customerWorkTimeService.js');
                            },
                        ],
                    },
                })
                //工时统计-客户工时统计-详情
                .state('app.office.customerWorkTimeDetail', {
                    url: '/customerWorkTimeManagementDetail',
                    templateUrl: 'tpl/office/workTime/customerWorkTimeManagementDetail.html',
                    controller: 'customerWorkTimeManagementDetail',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/workTime/customerWorkTimeDetailController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/workTime/customerWorkTimeService.js');
                            },
                        ],
                    },
                })
                //发布确认单-管理
                .state('app.office.confirmationSheet', {
                    url: '/confirmationSheetManagement',
                    templateUrl: 'tpl/office/confirmationSheet/confirmationSheetManagement.html',
                    controller: 'confirmationSheetManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/confirmationSheet/confirmationSheetController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/confirmationSheet/confirmationSheetService.js'
                                );
                            },
                        ],
                    },
                })
                //发布确认单-修改
                .state('app.office.confirmationSheetUpdate', {
                    url: '/confirmationSheetUpdateManagement',
                    templateUrl: 'tpl/office/confirmationSheet/confirmationSheetUpdateManagement.html',
                    controller: 'confirmationSheetUpdateManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/confirmationSheet/confirmationSheetUpdateController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/confirmationSheet/confirmationSheetService.js'
                                );
                            },
                        ],
                    },
                })
                //发布确认单-确认
                .state('app.office.confirmationSheetConfirm', {
                    url: '/confirmationSheetConfirmManagement',
                    templateUrl: 'tpl/office/confirmationSheet/confirmationSheetConfirmManagement.html',
                    controller: 'confirmationSheetConfirmManagement',
                    params: {
                        id: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/confirmationSheet/confirmationSheetConfirmController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/confirmationSheet/confirmationSheetService.js'
                                );
                            },
                        ],
                    },
                })

                //基线管理
                .state('app.office.baseLineManagement', {
                    url: '/baseLineManagement',
                    templateUrl: 'tpl/office/baseLine/baseLineManagement.html',
                    controller: 'baseLineManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/baseLine/baseLineManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/baseLine/baseLineService.js');
                            },
                        ],
                    },
                })
                //基线文件管理
                .state('app.office.baseLineFileManagement', {
                    url: '/baseLineFileManagement',
                    templateUrl: 'tpl/office/baseLine/baseLineFileManagement.html',
                    controller: 'baseLineFileManagement',
                    params: {
                        baseLine: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/baseLine/baseLineFileManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/baseLine/baseLineService.js');
                            },
                        ],
                    },
                })

                //评审榜管理
                .state('app.office.reviewTop', {
                    url: '/reviewTop',
                    templateUrl: 'tpl/office/reviewTop/reviewTop.html',
                    controller: 'reviewTop',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/reviewTop/reviewTopController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/reviewTop/reviewTopService.js');
                            },
                        ],
                    },
                })

                //SVN|GIT管理
                .state('app.office.svnRepositoryManage', {
                    url: '/svnRepositoryManage',
                    templateUrl: 'tpl/office/svnRepositoryManage/svnRepositoryManage.html',
                    controller: 'svnRepositoryManage',
                    params: {
                        type: null,
                        repositoryName: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/svnRepositoryManage/svnRepositoryManageController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/svnRepositoryManage/svnRepositoryManageService.js'
                                );
                            },
                        ],
                    },
                })
                //修改仓库
                .state('app.office.svnRepositoryUpdate', {
                    url: '/svnRepositoryUpdate:repositoryDetails',
                    templateUrl: 'tpl/office/svnRepositoryManage/svnRepositoryUpdate.html',
                    controller: 'svnRepositoryUpdateController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/svnRepositoryManage/svnRepositoryUpdateController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/svnRepositoryManage/svnRepositoryUpdateService.js'
                                );
                            },
                        ],
                    },
                })
                //维护模块
                .state('app.office.svnModelUpdate', {
                    url: '/svnModelUpdate',
                    params: {
                        repositoryid: null,
                        repositoryName: null,
                        repositoryType: null,
                    },
                    templateUrl: 'tpl/office/svnRepositoryManage/svnModelUpdate.html',
                    controller: 'svnModelUpdateController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/svnRepositoryManage/svnModelUpdateController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/svnRepositoryManage/svnModelUpdateService.js'
                                );
                            },
                        ],
                    },
                })
                //模块详情
                .state('app.office.svnModelDetail', {
                    url: '/svnModelDetail',
                    templateUrl: 'tpl/office/svnRepositoryManage/svnModelDetail.html',
                    controller: 'svnModelDetailController',
                    params: {
                        staffId: null,
                        storageName: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/svnRepositoryManage/svnModelDetailController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/svnRepositoryManage/svnModelDetailService.js'
                                );
                            },
                        ],
                    },
                })

                //员工角色管理
                .state('app.office.staffRole', {
                    url: '/staffRole',
                    templateUrl: 'tpl/office/staffRole/staffRoleList.html',
                    controller: 'staffRoleListController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/staffRole/staffRoleListController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/staffRole/staffRoleListService.js');
                            },
                        ],
                    },
                })

                //修改员工角色
                .state('app.office.staffRoleUpdate', {
                    url: '/staffRoleUpdate',
                    params: { dataDetails: null },
                    templateUrl: 'tpl/office/staffRole/staffRoleUpdate.html',
                    controller: 'staffRoleUpdateController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/staffRole/staffRoleUpdateController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/staffRole/staffRoleUpdateService.js');
                            },
                        ],
                    },
                })

                //新增员工角色
                .state('app.office.staffRoleAdd', {
                    url: '/staffRoleAdd',
                    templateUrl: 'tpl/office/staffRole/staffRoleAdd.html',
                    controller: 'staffRoleAddController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/staffRole/staffRoleAddController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/staffRole/staffRoleAddService.js');
                            },
                        ],
                    },
                })

                //个人看板
                .state('app.office.personalBoard', {
                    url: '/personalBoard',
                    templateUrl: 'tpl/office/personalDataBoard/personalBoard.html',
                    controller: 'personalBoardController',
                    params: {
                        baseLine: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/personalDataBoard/personalBoardController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/personalDataBoard/personalBoardService.js');
                            },
                        ],
                    },
                })
                //人力资源统计
                .state('app.office.humanResStatistics', {
                    url: '/humanResStatisticsManagement',
                    templateUrl: 'tpl/office/humanResStatistics/humanResStatisticsManagement.html',
                    controller: 'humanResStatisticsManagement',
                    params: {
                        type: 1,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/controllers/office/humanResStatistics/humanResStatisticsManagementController.js',
                                    'js/controllers/office/humanResStatistics/attendanceStatisticsController.js',
                                    'js/controllers/office/humanResStatistics/groupStatisticsController.js',
                                    'js/controllers/office/humanResStatistics/attendanceDetailController.js',
                                ]);
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/humanResStatistics/humanResStatisticsService.js',
                                    'js/services/office/humanResStatistics/attendanceStatisticsService.js',
                                ]);
                            },
                        ],
                    },
                })
                //人力变动同比统计
                .state('app.office.humanResChangeStatistics', {
                    url: '/humanResChangeStatisticsManagement',
                    templateUrl: 'tpl/office/humanResStatistics/humanResChangeStatistics.html',
                    controller: 'humanResChangeStatisticsController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/humanResStatistics/humanResChangeStatisticsController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/humanResStatistics/humanResStatisticsService.js'
                                );
                            },
                        ],
                    },
                })
                //人力资源统计-考勤-更多界面
                .state('app.office.attendanceMore', {
                    url: '/attendanceMoreManagement',
                    templateUrl: 'tpl/office/humanResStatistics/attendanceMore.html',
                    controller: 'attendanceMoreController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/humanResStatistics/attendanceMoreController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/humanResStatistics/attendanceStatisticsService.js'
                                );
                            },
                        ],
                    },
                })
                //系研看板
                .state('app.office.xyKpi', {
                    url: '/xyKpiManagement',
                    templateUrl: 'tpl/office/personalDataBoard/xyKpi.html',
                    controller: 'xyKpiController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/personalDataBoard/xyKpiController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/personalDataBoard/xyKpiService.js');
                            },
                        ],
                    },
                })
                //平台人力投入分析看板
                .state('app.office.manpowerAnalysis', {
                    url: '/manpowerAnalysis',
                    templateUrl: 'tpl/office/personalDataBoard/manpowerInvestAnalysis.html',
                    controller: 'manpowerInvestAnalysisController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/personalDataBoard/manpowerInvestAnalysisController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/personalDataBoard/manpowerInvestAnalysisService.js'
                                );
                            },
                        ],
                        loadMyFactory: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/controllers/office/departmentDetailBoard/commonUtil/util.js',
                                    'js/controllers/office/departmentBoard/commonUtil/util.js',
                                ]);
                            },
                        ],
                    },
                })
                // 一级部门工时看板
                .state('app.office.workingHoursBoard', {
                    url: '/workingHoursBoard',
                    templateUrl: 'tpl/office/departmentBoard/workingHoursBoard.html',
                    controller: 'workingHoursBoard',
                    params: {
                        typeSelect: '1',
                        orgCode: null,
                        sortType: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/controllers/office/departmentBoard/workingHoursBoard.js',
                                    'js/controllers/office/departmentBoard/firstLevelDepartmentController.js',
                                    'js/controllers/office/departmentBoard/secondLevelDepartmentController.js',
                                    'js/controllers/office/departmentBoard/projectWorkingHoursBoardController.js',
                                    'js/controllers/office/departmentBoard/personWorkingHoursBoardController.js',
                                ]);
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/departmentBoard/workingHoursBoard.js',
                                    'js/services/office/departmentBoard/firstLevelDepartmentService.js',
                                    'js/services/office/departmentBoard/secondLevelDepartmentService.js',
                                    'js/services/office/departmentBoard/personWorkingBoardService.js',
                                    'js/services/office/departmentBoard/projectWorkingBoardService.js',
                                ]);
                            },
                        ],
                        loadMyFactory: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/departmentBoard/commonUtil/util.js');
                            },
                        ],
                    },
                })
                // 团队详情
                .state('app.office.projectWorkingHoursDetail', {
                    url: '/projectWorkingHoursDetail',
                    templateUrl: 'tpl/office/departmentBoard/projectWorkingHoursDetail.html',
                    controller: 'projectWorkingHoursDetailController',
                    params: {
                        startTime: null,
                        endTime: null,
                        searchTimeString: null,
                        teamCode: null,
                        projectName: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/controllers/office/departmentBoard/projectWorkingHoursDetailController.js',
                                ]);
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/departmentBoard/projectWorkingDetailService.js',
                                ]);
                            },
                        ],
                        loadMyFactory: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/departmentBoard/commonUtil/util.js');
                            },
                        ],
                    },
                })
                // 个人详情
                .state('app.office.personWorkingHoursDetail', {
                    url: '/personWorkingHoursDetail',
                    templateUrl: 'tpl/office/departmentBoard/personWorkingHoursDetail.html',
                    controller: 'personWorkingHoursDetailController',
                    params: {
                        startTime: null,
                        endTime: null,
                        searchTimeString: null,
                        employeeId: null,
                        employeeName: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/controllers/office/departmentBoard/personWorkingHoursDetailController.js',
                                ]);
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/departmentBoard/personWorkingDetailService.js',
                                ]);
                            },
                        ],
                        loadMyFactory: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/departmentBoard/commonUtil/util.js');
                            },
                        ],
                    },
                })
                // 部门看板列表
                .state('app.office.departmentList', {
                    url: '/departmentList',
                    templateUrl: 'tpl/office/departmentDetailBoard/departmentList.html',
                    controller: 'departmentListController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/departmentDetailBoard/departmentListController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/departmentDetailBoard/departmentListService.js',
                                ]);
                            },
                        ],
                        loadMyFactory: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/departmentDetailBoard/commonUtil/util.js'
                                );
                            },
                        ],
                    },
                })
                // 部门看板详情
                .state('app.office.departmentDetail', {
                    url: '/departmentDetail',
                    templateUrl: 'tpl/office/departmentDetailBoard/departmentDetail.html',
                    controller: 'departmentDetailController',
                    params: {
                        typeSelect: '1',
                        orgCode: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/controllers/office/departmentDetailBoard/departmentDetailController.js',
                                    'js/controllers/office/departmentDetailBoard/departmentBoardIndexController.js',
                                    'js/controllers/office/departmentDetailBoard/departmentSprintIndexController.js',
                                    'js/controllers/office/departmentDetailBoard/PLMIndexController.js',
                                    'js/controllers/office/departmentDetailBoard/PLMActiveController.js',
                                    'js/controllers/office/departmentDetailBoard/onlineProblemIndexController.js',
                                    'js/controllers/office/departmentDetailBoard/processDataIndexController.js',
                                    'js/controllers/office/departmentBoard/secondLevelDepartmentController.js',
                                    'js/controllers/office/departmentDetailBoard/deptContributionIndexController.js',
                                    'js/controllers/office/departmentDetailBoard/deptProcessAndCostIndexController.js',
                                    'js/controllers/office/departmentDetailBoard/BugProgressController.js',
                                    'js/controllers/office/departmentDetailBoard/internalDemandBoardController.js',
                                ]);
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/departmentDetailBoard/departmentBoardIndexService.js',
                                    'js/services/office/departmentDetailBoard/departmentSprintService.js',
                                    'js/services/office/departmentDetailBoard/onlineProblemService.js',
                                    'js/services/office/departmentDetailBoard/processDataForBoardService.js',
                                    'js/services/office/departmentBoard/secondLevelDepartmentService.js',
                                    'js/services/office/departmentDetailBoard/deptContributionService.js',
                                    'js/services/office/departmentDetailBoard/PLMService.js',
                                    'js/services/office/departmentDetailBoard/PLMActiveService.js',
                                    'js/services/office/departmentDetailBoard/deptProcessAndCostService.js',
                                    'js/services/office/cost/costMonitoring/costMonitoringService.js',
                                    'js/services/office/departmentDetailBoard/BugProgressService.js',
                                    'js/services/office/departmentDetailBoard/internalDemandBoardService.js',
                                ]);
                            },
                        ],
                        loadMyFactory: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/controllers/office/departmentDetailBoard/commonUtil/util.js',
                                    'js/controllers/office/departmentBoard/commonUtil/util.js',
                                ]);
                            },
                        ],
                    },
                })
                // 事件管理
                .state('app.office.eventManagement', {
                    url: '/eventManagement',
                    templateUrl: 'tpl/office/blackEvent/eventManagement.html',
                    controller: 'eventManagementController',
                    params: {
                        eventType: 1,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/controllers/office/blackEvent/eventManagementController.js',
                                    'js/controllers/office/blackEvent/blackEventManagementController.js',
                                    'js/controllers/office/blackEvent/redEventManagementController.js',
                                ]);
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/blackEvent/eventManagementService.js',
                                    'js/services/office/blackEvent/blackEventService.js',
                                    'js/services/office/blackEvent/redEventService.js',
                                ]);
                            },
                        ],
                    },
                })
                // plm系统计划数据转任务
                .state('app.office.plmPlantData', {
                    url: '/plmPlantData',
                    templateUrl: 'tpl/office/zttask/plmPlantData.html',
                    controller: 'plmPlantDataController',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/zttask/plmPlantDataController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(['js/services/office/plm/plmService.js']);
                            },
                        ],
                    },
                })
                // 仓库权限查询
                .state('app.office.repositoryPermissionMain', {
                    url: '/repositoryPermissionMain',
                    templateUrl: 'tpl/office/repositoryPermission/repositoryPermissionMain.html',
                    controller: 'repositoryPermissionMain',
                    params: {
                        type: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/repositoryPermission/repositoryPermissionMain.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/repositoryPermission/repositoryPermissionMainService.js',
                                ]);
                            },
                        ],
                    },
                })
                //仓库权限查询详情
                .state('app.office.repositoryPermissionDetail', {
                    url: '/repositoryPermissionDetail',
                    templateUrl: 'tpl/office/repositoryPermission/repositoryPermissionDetail.html',
                    controller: 'repositoryPermissionDetailController',
                    params: {
                        repositoryid: null,
                        employeeNo: null,
                        lastPage: null,
                        repositoryType: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/repositoryPermission/repositoryPermissionDetailController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/repositoryPermission/repositoryPermissionDetailService.js'
                                );
                            },
                        ],
                    },
                })
                //仓库历史人员权限查询详情
                .state('app.office.repositoryEmployeePerHis', {
                    url: '/repositoryEmployeePerHis',
                    templateUrl: 'tpl/office/repositoryPermission/repositoryEmployeePerHis.html',
                    controller: 'repositoryEmployeePerHisController',
                    params: {
                        repositoryid: null,
                        employeeNo: null,
                        repositoryType: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/repositoryPermission/repositoryEmployeePerHisController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/repositoryPermission/historyRepositoryInfoService.js'
                                );
                            },
                        ],
                    },
                })
                //仓库历史权限查询详情
                .state('app.office.historyRepositoryInfo', {
                    url: '/historyRepositoryInfo',
                    templateUrl: 'tpl/office/repositoryPermission/historyRepositoryInfo.html',
                    controller: 'historyRepositoryInfoController',
                    params: {
                        repositoryid: null,
                        repositoryType: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/repositoryPermission/historyRepositoryInfoController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/repositoryPermission/historyRepositoryInfoService.js'
                                );
                            },
                        ],
                    },
                })
                //仓库活跃度查询详情
                .state('app.office.repositoryActivityDetail', {
                    url: '/repositoryActivityDetail',
                    templateUrl: 'tpl/office/repositoryPermission/repositoryActivityDetail.html',
                    controller: 'repositoryActivityDetailController',
                    params: {
                        repositoryid: null,
                        repositoryType: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/repositoryPermission/repositoryActivityDetailController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/services/office/repositoryPermission/repositoryActivityDetailService.js'
                                );
                            },
                        ],
                    },
                })
                // 人员权限查询
                .state('app.office.employeePermissionMain', {
                    url: '/employeePermissionMain',
                    templateUrl: 'tpl/office/employeePermission/employeePermissionMain.html',
                    controller: 'employeePermissionMain',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/employeePermission/employeePermissionMain.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/employeePermission/employeePermissionMainService.js',
                                ]);
                            },
                        ],
                    },
                })
                // 人员仓库权限详情
                .state('app.office.personRepositoryDetail', {
                    url: '/personRepositoryDetail',
                    templateUrl: 'tpl/office/employeePermission/personRepositoryDetail.html',
                    controller: 'personRepositoryDetailController',
                    params: {
                        repositoryid: null,
                        employeeNo: null,
                        isHistory: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/employeePermission/personRepositoryDetailController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/employeePermission/personRepositoryDetailService.js',
                                ]);
                            },
                        ],
                    },
                })
                //有效代码管理
                .state('app.office.validCodeManagement', {
                    url: '/validCodeManagement',
                    templateUrl: 'tpl/office/validCode/validCode.html',
                    controller: 'validCodeManagement',
                    params: {
                        type: 'menu',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/validCode/validCodeManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/validCode/validCodeManagementService.js');
                            },
                        ],
                    },
                })
                // 提交明细
                .state('app.office.commitList', {
                    url: '/commitList',
                    templateUrl: 'tpl/office/validCode/commitList.html',
                    controller: 'commitList',
                    params: {
                        pushUser: null,
                        startTime: null,
                        endTime: null,
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/validCode/commitListController.js');
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/validCode/commitListService.js');
                            },
                        ],
                    },
                })

                //代码数据报告
                .state('app.office.codeDataReport', {
                    url: '/codeDataReport',
                    templateUrl: 'tpl/office/validCode/codeDataReport.html',
                    controller: 'codeDataReport',
                    params: {
                        typeSelect: '1',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/controllers/office/validCode/codeDataReportController.js',
                                    'js/controllers/office/validCode/aggregateStatisticController.js',
                                    'js/controllers/office/validCode/orgStatisticController.js',
                                    'js/controllers/office/validCode/bugStatisticController.js',
                                ]);
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load([
                                    'js/services/office/validCode/codeDataReportService.js',
                                    'js/services/office/validCode/aggregateStatisticService.js',
                                    'js/services/office/validCode/orgStatisticService.js',
                                    'js/services/office/validCode/bugStatisticService.js',
                                ]);
                            },
                        ],
                        loadMyFactory: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/controllers/office/validCode/commonUtil/util.js');
                            },
                        ],
                    },
                })
                //运维支持流程列表
                .state('app.office.opsSupportProcess', {
                    url: '/opsSupportProcess',
                    templateUrl: 'tpl/office/validCode/opsSupportProcess.html',
                    controller: 'opsSupportProcess',
                    params: {
                        type: 'menu',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/validCode/opsSupportProcessController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/validCode/opsSupportProcessService.js');
                            },
                        ],
                    },
                })
                //持续集成流程
                .state('app.office.opsIntegrationProcess', {
                    url: '/opsIntegrationProcess',
                    templateUrl: 'tpl/office/validCode/opsIntegrationProcess.html',
                    controller: 'opsIntegrationProcess',
                    params: {
                        type: 'menu',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/validCode/opsIntegrationProcessController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/validCode/opsIntegrationProcessService.js');
                            },
                        ],
                    },
                })

                //紧急上线流程管理
                .state('app.office.emergencyLaunchManagement', {
                    url: '/emergencyLaunchManagement',
                    templateUrl: 'tpl/office/emergencyLaunch/emergencyLaunchManagement.html',
                    controller: 'emergencyLaunchManagement',
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/emergencyLaunch/emergencyLaunchManagementController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/emergencyLaunch/emergencyLaunchService.js');
                            },
                        ],
                    },
                })
                //线上监控告警流程列表
                .state('app.office.onlineAlarmProcess', {
                    url: '/onlineAlarmProcess',
                    templateUrl: 'tpl/office/validCode/onlineAlarmProcess.html',
                    controller: 'onlineAlarmProcess',
                    params: {
                        type: 'menu',
                    },
                    resolve: {
                        loadMyCtrl: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load(
                                    'js/controllers/office/validCode/onlineAlarmProcessController.js'
                                );
                            },
                        ],
                        loadMyService: [
                            '$ocLazyLoad',
                            function ($ocLazyLoad) {
                                return $ocLazyLoad.load('js/services/office/validCode/onlineAlarmProcessService.js');
                            },
                        ],
                    },
                });
        },
    ]);
})();

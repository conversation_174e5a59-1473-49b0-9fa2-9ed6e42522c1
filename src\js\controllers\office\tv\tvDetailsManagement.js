(function () {
    app.controller("tvDetailsManagement", ['OfficeFileTool','$rootScope', 'comService', 'SystemService', 'tvService', '$scope', '$state', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function (OfficeFileTool,$rootScope, comService, SystemService, tvService, $scope, $state, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {

        /**
         * *************************************************************
         *             初始化部分                                 开始
         * *************************************************************
         */
        var clientHeight = document.body.clientHeight;
        var clientWidth = document.body.clientWidth;
        $("#buttonStyle").css(inform.getButtonStyle(clientHeight, clientWidth));
        // 初始化信息
        $scope.spec = {};
        $scope.spec.tvParamList = [];
        // 默认看板类型为自定义看板
        $scope.spec.tvType = 1;
        // 判断修改和新增
        $scope.displayId = $stateParams.id;
        // 获取下拉列表数据
        getDataInfo();
        //获取初始信息函数
        $scope.getData = getData;
        //图片文件上传初始化
        $scope.param = {};
        $scope.param.attachmentAddress = [];
        $scope.param.currentAddress = [];
        $scope.param.attachmentSize = [];
        $scope.param.attachmentAddressID=[];
        //创建文件上传组件
        var paramObj ={listId:'thelist',
            removeCall:function (id) {
                var index = $scope.param.attachmentAddressID.indexOf(id);
                $scope.$apply();
                $scope.param.currentAddress.splice(index,1);
                $scope.param.attachmentAddress.splice(index,1);
                $scope.param.attachmentSize.splice(index,1);
                $scope.param.attachmentAddressID.splice(index,1);
            },
            getFilePathCall:function (fileId) {
                var index = $scope.param.attachmentAddressID.indexOf(fileId);
                var filePath = $scope.param.attachmentAddress[index];
                return filePath;
            },
            getSizeOfFiles:function () {
                var size = 0;
                for (var i = 0; i <  $scope.param.attachmentSize.length; i++) {
                    size = size + parseInt($scope.param.attachmentSize[i]);
                }
            },
            uploadSuccess:function (file,response) {
                $scope.param.attachmentAddress.push(response.data);
                $scope.param.currentAddress.push(response.data);
                $scope.param.attachmentAddressID.push(file.id);
                $scope.param.attachmentSize.push(file.size);
            }
        };
        var uploader = OfficeFileTool.createUploader(paramObj,'abs/usr/local/nginx/html/office/doc/pdf');
        if ($scope.displayId) {
            getData();
        }else{
            initTime();
        }
        /**
         * *************************************************************
         *              初始化部分                                 结束
         * *************************************************************
         */

        /**
         * *************************************************************
         *              方法声明部分                                 开始
         * *************************************************************
         */
        /**
         * 当新增时，初始化时间
         */
        function initTime(){
            var date = new Date();
            $scope.spec.startTime = inform.format(date,"yyyy-MM-dd");
            //截止时间为当前时间后延一个月
            date.setMonth(date.getMonth()+1);
            $scope.spec.endTime = inform.format(date,"yyyy-MM-dd");
        }
        /**
         * 选择自定义看板时
         */
        $scope.customPanel = function (){
            //配置信息都不显示
            $scope.showFile = false;
            $scope.showText = false;
            //内容URL可编辑
            $scope.URLedit = false;
            $scope.spec.contentUrl = "";
        };

        /**
         * 选择文字公告看板时
         */
        $scope.textPanel = function () {
            $scope.spec.tvType = 2;
            //显示文字公告看板的配置信息
            $scope.showText = true;
            $scope.showFile = false;
            //内容URL不可编辑，默认为/index.dev.html#/immediateReward
            $scope.URLedit = true;
            $scope.spec.contentUrl = "/index.dev.html#/immediateReward";
            //初始化文字看板公告配置信息
            $scope.spec.tvParamList = [];
            $scope.spec.tvParamList[0]={"paramName":"imgUrl","paramValue":""};
            $scope.spec.tvParamList[1]={"paramName":"showMessage","paramValue":"false"};
            $scope.spec.tvParamList[2]={"paramName":"message1","paramValue":""};
            $scope.spec.tvParamList[3]={"paramName":"nameList1","paramValue":""};
        };

        /**
         * 选择文档展示看板时
         */
        $scope.filePanel = function (){
            $scope.spec.tvType = 3;
            //显示文档展示看板的配置信息
            $scope.showText = false;
            $scope.showFile = true;
            //内容URL不可编辑，默认为/index.dev.html#/share
            $scope.URLedit = true;
            $scope.spec.contentUrl = "/index.dev.html#/share";
            //初始化文档看板公告配置信息
            $scope.spec.tvParamLis = [];
            $scope.spec.tvParamList[0]={"paramName":"pdfDoc","paramValue":""};
            $scope.spec.tvParamList[1]={"paramName":"time","paramValue":""};
            $scope.spec.tvParamList[2]={"paramName":"showMessage","paramValue":"false"};
        };

        /**
         * 根据tvType的值显示相应配置信息
         */
        function showConfigInfo() {
            //tvType为3时显示文档展示看板的配置信息
            if ($scope.spec.tvType === 3) {
                $scope.showFile = true;
                $scope.URLedit = true;
                //不可修改看板类型
                $scope.editRadio = true;
            //tvType为2时显示文字展示看板的配置信息
            } else if ($scope.spec.tvType === 2) {
                $scope.showText = true;
                $scope.URLedit = true;
                $scope.editRadio = true;
            //否则为自定义看板，不显示配置信息
            } else {
                $scope.showFile = false;
                $scope.showText = false;
                $scope.editRadio = true;
            }
        }

        /**
         * 获取下拉列表数据
         */
        function getDataInfo() {
            SystemService.getAllRole().then(function (result) {
                if (result.code === AgreeConstant.resultCode) {
                    $scope.roles = angular.fromJson(result.result);
                } else {
                    inform.common(result.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        }

        /**
         * 获取信息
         */
        function getData() {
            var urlData = {
                'id': $scope.displayId
            };
            tvService.getTvFrameDetail(urlData).then(function (result) {
                if (result.code === AgreeConstant.code) {
                    $scope.spec = angular.fromJson(result.data);
                    $scope.spec.role = $scope.spec.role.split(',').map(Number);
                    showConfigInfo();
                    //返现数据
                    $scope.param.attachmentAddressID = [];
                    $scope.param.currentAddress = [];
                    $scope.param.attachmentAddress = [];
                    $scope.param.attachmentSize = [];
                    if(null != result.data.imagFilePath && '' !== result.data.imagFilePath) {
                        $scope.param.flag = '0';
                        angular.forEach(result.data.imagFilePath.split(','), function(res, index) {
                            $scope.param.attachmentAddress.push(res);
                            $scope.param.currentAddress.push(res);
                        });
                        //创建回显的文件列表 返回文件id集合
                        var fileIdList = uploader.initShowFileList($scope.param.attachmentAddress);
                        if (fileIdList.length > 0) {
                            $scope.param.attachmentAddressID = fileIdList;
                        }
                    }
                } else {
                    inform.common(result.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        }

        /**
         * 添加参数
         */
        $scope.addNewBind = function () {
            $scope.spec.tvParamList.push({
                tvFrameId: $scope.displayId
            });
        };
        /**
         * 删除参数
         * @param bindId 当前行的信息
         */
        $scope.deleteNewBind = function (bindId) {
            $scope.spec.tvParamList.splice(bindId, 1);
        };
        /**
         * 保存信息
         */
        $scope.saveInfo = function () {
            var urlData = {
                'id': $scope.displayId,
                'title': $scope.spec.title,
                'contentUrl': $scope.spec.contentUrl,
                'state': $scope.spec.state,
                'playDuration': $scope.spec.playDuration,
                'role': $scope.spec.role.toString(),
                'tvType': $scope.spec.tvType,
                'imagFilePath':$scope.param.currentAddress.join(','),
                'tvParamList': $scope.spec.tvParamList,
                'startTime' : $scope.spec.startTime,
                'endTime' : $scope.spec.endTime
            };
            if ($scope.displayId) {
                tvService.updateTvFrameDetail(urlData).then(function (result) {
                    fun(result)
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            } else {
                tvService.addTvFrameDetail(urlData).then(function (result) {
                    fun(result)
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
        };

        /*
        * 通用函数
        * */
        function fun(result) {
            if (result.code === AgreeConstant.code) {
                inform.common(result.message);
                $state.go('app.office.tvDisplayManagement');
            } else {
                inform.common(result.message);
            }
        }

        /**
         * *************************************************************
         *              方法声明部分                                 结束
         * *************************************************************
         */
    }]);
})();
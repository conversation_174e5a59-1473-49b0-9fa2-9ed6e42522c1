(function() {
    app.controller("onBusinessAdd", ['onBusinessService', 'comService', '$rootScope', '$scope', 'codeConfigService', '$stateParams', 'inform', 'Trans', 'AgreeConstant', '$state',
        function(onBusinessService, comService, $rootScope, $scope, codeConfigService, $stateParams, inform, Trans, AgreeConstant, $state) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //初始化费用金额
            $scope.spec = {
                'total': 0,
                'transportationFee': 0,
                'accommodationFee': 0,
                'localTransportationFee': 0,
                'businessEntertainmentFee': 0,
                'serviceFee': 0,
                'otherFee': 0,
                'foodSubsidies': 0,
                'communicationSubsidy': 0,
                'personalReimburseFee':0,
                'fenBeiTongFee':0
            };
            //设置列表的高度
            setDivHeight();
            //初始化页面信息
            initPages();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
            }
            /**
             * 页面初始化
             */
            function initPages() {
                //获取项目信息
            	$scope.nameNumMap={};//根据名字取编号
            	$scope.numIdMap={};//根据编号取ID
                $scope.projectIdList = [];
                codeConfigService.getProjectList().then(function(data) {
                    $scope.projectIdList = data.data;
                    angular.forEach($scope.projectIdList, function (project, index) {
                        $scope.numIdMap[project.projectNumber] = project.id;
                        $scope.nameNumMap[project.cname] = project.projectNumber;
                    });
                });
                //获取出差类别
                $scope.purposeList = [];
                $scope.purposeMap = [];
                comService.getParamList('ONBUSINESS', 'ONBUSINESS').then(function(data) {
                    if (data.data) {
                        $scope.purposeList = data.data;
                        angular.forEach($scope.purposeList, function (res, index) {
                            $scope.purposeMap[res.param_code] = res.param_value;
                        });
                    }
                });
                //获取出差方式
                $scope.onBusinessModeList = [];
                comService.getParamList('ONBUSINESS_MODE', 'ONBUSINESS_MODE').then(function(data) {
                    if (data.data) {
                        $scope.onBusinessModeList = data.data;
                    }
                });
                //获取员工信息
                $scope.employeeMap={};
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function(data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                        angular.forEach($scope.employeeList, function (res, index) {
                            $scope.employeeMap[res.employeeNo] = res.realName;
                        });
                    }
                });
                //获取产品线
                $scope.lineList = [];
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    $scope.lineList = angular.fromJson(data.data);
                });
            }
            /**
             * 获取项目编号:“出差项目”时联动查出“项目编号”
             */
            $scope.getNum = function(){
            	 $scope.spec.onBusinessProjectNum = $scope.nameNumMap[$scope.spec.onBusinessProject];
            }
            /**
             * 添加信息
             */
            $scope.addOnBusinessInfo = function() {
            	if($scope.spec.purpose==='0001'||$scope.spec.purpose==='0002'||$scope.spec.purpose==='0003'){
                    if($scope.spec.onBusinessProject==null||$scope.spec.onBusinessProject===''){
                        inform.common("出差类别为部署实施、驻场开发、解决问题时“出差项目”必输");
                        return;
                    }
            		if($scope.spec.onBusinessProjectNum==null||$scope.spec.onBusinessProjectNum===''){
            			 inform.common("出差类别为部署实施、驻场开发、解决问题时“项目编号”必输");
            			 return;
            		}
            	}
            	if($scope.spec.onBusinessProjectNum!=null&&$scope.spec.onBusinessProjectNum!==''){
            		var proId = $scope.numIdMap[$scope.spec.onBusinessProjectNum];
            		if (proId==null){
            			 inform.common("请输入正确的项目编号");
    	       			 return;
            		}
            		$scope.proId = proId;
            	}
            	//添加出差信息至出差表
                addInfo();
            };
            /**
             * 添加出差信息至出差表
             */
            function addInfo(){
            	var onBusinessDays = ($scope.spec.returnTime - $scope.spec.setOffTime) / (1000 * 60 * 60 * 24) + 1;
                var businessInfo = {
                    'instanceNumber': $scope.spec.instanceNumber,
                    'employeeId': $scope.spec.employeeId,
                    'setOffTime': inform.format($scope.spec.setOffTime, 'yyyy-MM-dd'),
                    'returnTime': inform.format($scope.spec.returnTime, 'yyyy-MM-dd'),
                    'onBusinessDays': onBusinessDays,
                    'site': $scope.spec.site,
                    'onBusinessProject': $scope.spec.onBusinessProject==null?" ":$scope.spec.onBusinessProject,
                    'purpose': $scope.spec.purpose,
                    'onBusinessMode': $scope.spec.onBusinessMode,
                    'transportationFee': $scope.adjustNumber($scope.spec.transportationFee),
                    'accommodationFee': $scope.adjustNumber($scope.spec.accommodationFee),
                    'localTransportationFee': $scope.adjustNumber($scope.spec.localTransportationFee),
                    'businessEntertainmentFee': $scope.adjustNumber($scope.spec.businessEntertainmentFee),
                    'serviceFee': $scope.adjustNumber($scope.spec.serviceFee),
                    'otherFee': $scope.adjustNumber($scope.spec.otherFee),
                    'foodSubsidies': $scope.adjustNumber($scope.spec.foodSubsidies),
                    'communicationSubsidy': $scope.adjustNumber($scope.spec.communicationSubsidy),
                    'personalReimburseFee': $scope.adjustNumber($scope.spec.personalReimburseFee),
                    'fenBeiTongFee': $scope.adjustNumber($scope.spec.fenBeiTongFee),
                    'total': $scope.spec.total,
                    'onBusinessDetailProject':$scope.spec.onBusinessDetailProject,
                    'productLine':$scope.spec.productLine,
                    'projectNum':$scope.spec.onBusinessProjectNum,
                };
                var feeInfo = {
                    //项目ID
                    'projectId': $scope.proId,
                    //费用类型
                    'expenseTypeId':'COSTFEE_TYPE_1',
                    //费用金额
                    'amount': $scope.spec.total,
                    //费用主体
                    'feeObject': $scope.employeeMap[$scope.spec.employeeId],
                    //费用描述
                    'feeDescription': ($scope.spec.onBusinessDetailProject==null||$scope.spec.onBusinessDetailProject==='')?'差旅费':$scope.spec.onBusinessDetailProject,
                    //费用的发生时间
                    'feeTime': inform.format($scope.spec.returnTime, 'yyyy-MM-dd')
                };
                var urlData={
                    businessInfo: businessInfo,
                    feeInfo: feeInfo
                };
                onBusinessService.addOnBusinessInfo(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        inform.common(data.message);
                        $scope.goback();
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 返回项目信息
             */
            $scope.goback = function() {
                $state.go('app.office.onBusinessManagement');
            };
            /**
             * 判断数字是否为undefined、null、空,
             * 如果是则返回数字0，
             * 否则将返回值设为数字
             */
            $scope.adjustNumber = function(str) {
                if (typeof(str) === 'undefined' || str==null || str === '') {
                    return parseFloat('0');
                }
                return parseFloat(str);
            };
            /**
             * 计算合计金额
             */
            $scope.getTotal = function() {
                var total = $scope.adjustNumber($scope.spec.transportationFee) + $scope.adjustNumber($scope.spec.accommodationFee)
                + $scope.adjustNumber($scope.spec.localTransportationFee) + $scope.adjustNumber($scope.spec.businessEntertainmentFee)
                + $scope.adjustNumber($scope.spec.serviceFee) + $scope.adjustNumber($scope.spec.otherFee) + $scope.adjustNumber($scope.spec.foodSubsidies)
                + $scope.adjustNumber($scope.spec.communicationSubsidy) + $scope.adjustNumber($scope.spec.personalReimburseFee) +$scope.adjustNumber($scope.spec.fenBeiTongFee);
                $scope.spec.total = total.toFixed(2);
            };
            /**
             *  新增的出发时间按钮
             */
            $scope.setOffTime_input = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.returnTime_input1 = false;
                $scope.setOffTime_input1 = true;
            };
            /**
             *  新增的返回时间按钮
             */
            $scope.returnTime_input = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.setOffTime_input1 = false;
                $scope.returnTime_input1 = true;
            };

            //计算出差天数
            $scope.getOnBusinessDays= function(){
                if(typeof ($scope.spec.setOffTime)==='undefined'
                    || typeof ($scope.spec.returnTime)==='undefined'
                    || $scope.spec.setOffTime==null
                    || $scope.spec.returnTime==null){
                    $scope.spec.onBusinessDays = 0;
                }else{
                    $scope.spec.onBusinessDays = ($scope.spec.returnTime - $scope.spec.setOffTime) / (1000 * 60 * 60 * 24) + 1;
                }
            };

            //根据出差类别，设置产品线信息
            $scope.getProductLine =function () {
                var purpose = $scope.spec.purpose;
                var productLine = $scope.spec.productLine;
                var productLineFlag = false;
                var purposeFlag = false;

                //判断产品线当前值是否为异地管理、培训、其他
                if(productLine === $scope.purposeMap['0006'] || productLine === $scope.purposeMap['0007'] || productLine === $scope.purposeMap['0008'] ){
                    productLineFlag = true;
                }
                //判断出差类别当前值是否为异地管理、培训、其他
                if(purpose === '0006' || purpose === '0007' || purpose === '0008'){
                    purposeFlag = true;
                }

                //出差类别为异地管理、培训、其他，将产品线设置为异地管理、培训、其他
                if(purposeFlag){
                    $scope.spec.productLine = $scope.purposeMap[purpose];
                }
                //出差类别不为异地管理、培训、其他，产品线为异地管理、培训、其他，则置空产品线
                if(!purposeFlag && productLineFlag){
                    $scope.spec.productLine = '';
                }
            };
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        }
    ]);
})();
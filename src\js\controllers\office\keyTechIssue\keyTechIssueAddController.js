(function () {
    app.controller("keyTechIssueAddController", ['keyTechIssueService', '$rootScope', 'comService', '$scope', '$state', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function (keyTechIssueService, $rootScope, comService, $scope, $state, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.flag = $stateParams.flag;
            $scope.status = 'null';
            $scope.refuseFlag = false;
            $scope.scoreFlag = false;
            $scope.changeParam = {};
            initPages();
            //新增时明细列表
            $scope.detailedList = [];
            //合议明细
            $scope.discussList = [];
            //初始化得分
            $scope.scoreList = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25];
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            function initPages() {
                setValue();
            }

            /**
             * 若为更新 则赋值
             */
            function setValue() {
                if ($scope.flag === 'detail') {
                    $scope.signName = "关键技术问题详情";
                }
                keyTechIssueService.getDataById($stateParams.item).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.changeParam.id = data.data.id;
                        $scope.changeParam.creatorName = data.data.creator;
                        $scope.changeParam.questionLevel = data.data.questionLevel;
                        $scope.changeParam.technologeyLine = data.data.technologeyLine;
                        $scope.changeParam.dates = data.data.dates;
                        $scope.changeParam.questionResource = data.data.questionResource;
                        $scope.changeParam.purposeAngle = data.data.purposeAngle;
                        $scope.changeParam.questionScore = data.data.questionScore;
                        $scope.changeParam.reviewResult = data.data.reviewResult;
                        $scope.changeParam.questionDesc = data.data.questionDesc;
                        $scope.changeParam.questionSolution = data.data.questionSolution;

                        //获取解决人、参与程度
                        angular.forEach(data.data.solvePersonList, function (m, index) {
                            $scope.detailedList.push({
                                'solveName': m.solveName,
                                'type': m.type,
                                'personScore': m.personScore
                            });
                        });

                        //获取合议人
                        angular.forEach(data.data.collegialPanelPersonList, function (m, index) {
                            $scope.discussList.push({
                                'suggestStaffName': m.suggestStaffName,
                                'suggestQuestionLevel': m.suggestQuestionLevel,
                                'remark': m.remark
                            });
                        });
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * 返回
             */
            $scope.goback = function () {
                $state.go("app.office.keyTechIssueManagement", null);
            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }
    ]);
})();
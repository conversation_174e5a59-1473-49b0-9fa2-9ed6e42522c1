(function () {
    app.controller("repositoryPermissionDetailController", ['comService','$rootScope', '$scope','$state','$stateParams','$modal','repositoryPermissionDetailService','inform','$window','Trans','AgreeConstant',
        function (comService,$rootScope, $scope,$state,$stateParams, $modal,repositoryPermissionDetailService,inform,$window,Trans,AgreeConstant) {
            //查询输入框
            $scope.formRefer={};
            $scope.formRefer.repositoryid = '';
            // 初始化分页数据
            $scope.pages = inform.initPages();

            function getData() {
                var urlData = {
                    repositoryName: $scope.formRefer.repositoryid,
                    employeeId: $scope.formRefer.employeeNo,
                    repositoryType: $scope.formRefer.repositoryType
                }
                repositoryPermissionDetailService.getAuthorizationDetail(urlData).then(function (data) {
                    $scope.showTable = 1;
                    if (data.code === AgreeConstant.code) {
                        var jsonData = data.data;
                        $scope.tableDataList1 = jsonData.docAuthorizationList;
                        $scope.tableDataList2 = jsonData.codeAuthorizationList;
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            $scope.toHistory = function(){
                $state.go("app.office.historyRepositoryInfo", { repositoryid: $scope.formRefer.repositoryid,repositoryType:$scope.formRefer.repositoryType });
            }

            $scope.backPage = function () {
                if ($scope.lastPage) {
                    if ($scope.lastPage === 'repositoryPermission') {
                        $state.go("app.office.repositoryPermissionMain");
                    } else if ($scope.lastPage === 'personRepository') {
                        $state.go("app.office.personRepositoryDetail", {
                            'repositoryid': null,
                            'employeeNo': $scope.formRefer.employeeNo,
                            'repositoryType':$scope.formRefer.repositoryType
                        });
                    }else if($scope.lastPage === 'personRepositoryHistory'){
                        $state.go("app.office.personRepositoryDetail", {
                            'repositoryid': null,
                            'employeeNo': $scope.formRefer.employeeNo,
                            'isHistory': '1',
                            'repositoryType':$scope.formRefer.repositoryType
                        });
                    }
                } else {
                    $state.go("app.office.repositoryPermissionMain");
                }
            }

            $scope.$watch('$viewContentLoaded', function () {
                if ($stateParams.repositoryid) {
                    $scope.formRefer.repositoryid = $stateParams.repositoryid;
                }
                if ($stateParams.employeeNo) {
                    $scope.formRefer.employeeNo = $stateParams.employeeNo;
                }
                if ($stateParams.lastPage) {
                    $scope.lastPage = $stateParams.lastPage;
                }
                if ($stateParams.repositoryType) {
                    $scope.formRefer.repositoryType = $stateParams.repositoryType;
                }
                getData();
            });
        }]);
})();

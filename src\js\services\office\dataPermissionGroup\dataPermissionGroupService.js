(function() {
    'use strict';
    app.factory('dataPermissionGroupService', dataPermissionGroupService);
    dataPermissionGroupService.$inject = ["HttpService", '$rootScope'];

    function dataPermissionGroupService(HttpService, $rootScope) {
        var service = {
            getInfo: getInfo,
            verifyPermission: verifyPermission,
            getWhiteList: getWhiteList,
            getResourceList: getResourceList,
            addGroup: addGroup,
            updateGroup: updateGroup,
            deleteById: deleteById
        };
        return service;
        /**
         * 分页查询权限组信息
         * @param urlData 
         */
        function getInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'dataPermissionGroup/getDataPermissionGroupByMap', urlData);
        }
        /**
         * 添加数据
         * @param urlData 
         */
        function addGroup(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'dataPermissionGroup/addGroup', urlData);
        }
        /*
         * 数据校验
         * @param urlData 
         */
        function verifyPermission(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'dataPermissionGroup/verifyPermission', urlData);
        }
        /**
         * 获取数据访问权限白名单
         * @param urlData 
         */
        function getWhiteList() {
            return HttpService.post($rootScope.getWaySystemApi + 'dataPermissionGroup/getWhiteList');
        }
        /**
         *  受限数据资源列表
         * @param urlData 
         */
        function getResourceList() {
            return HttpService.post($rootScope.getWaySystemApi + 'dataPermissionGroup/getResourceList');
        }
        /**
         * 修改数据
         * @param urlData 
         */
        function updateGroup(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'dataPermissionGroup/updateGroup', urlData);
        }
        /**
         * 删除数据
         * @param urlData 
         */
        function deleteById(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'dataPermissionGroup/deleteById', urlData);
        }
    }
})();
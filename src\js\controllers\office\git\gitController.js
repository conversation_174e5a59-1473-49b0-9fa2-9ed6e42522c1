(function() {
    'use strict';
    app.controller("gitController", ['comService','$rootScope', '$stateParams', '$scope', 'gitService','$modal','inform', 'Trans', 'AgreeConstant','$state', 'LocalCache', '$http',
        function(comService, $rootScope,$stateParams,$scope, gitService,$modal,inform, Trans, AgreeConstant,$state, LocalCache, $http ) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //创建标签的存储信息
            $scope.changeParam = {};
            //查询条件
            $scope.formRefer = {};
            //分页
            $scope.pages = inform.initPages();
            $scope.pages.size = '20';
            //被选中项目的集合
            $scope.proSelected=[];
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //初始化时间
            initTime();
            initInfo();
            $scope.getData = getData;
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 250);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 80);
            }
            /**
             * 初始化
             */
            function initInfo() {
                getData();
            }
            /**
             * 获取git仓库
             */
            function getData(pageNum) {
                $scope.proSelected = [];
                var urlData = {
                    'name':$scope.formRefer.name,
                    'tagName':$scope.formRefer.tagName,
                    'projectName':$scope.formRefer.projectName,
                    'startDate':inform.format($scope.formRefer.startDate, 'yyyyMMdd'),
                    'endDate':inform.format($scope.formRefer.endDate, 'yyyyMMdd'),
                    //当前页数
                    'page':pageNum,
                    //每页显示条数
                    'pageSize':$scope.pages.size
                }
                gitService.getData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if(null==data.data){
                                $scope.tableData = {};
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages();
                            } else {
                                //项目详情
                                $scope.tableData = data.data.list;
                                //分页信息设置
                                $scope.pages.total = data.data.total;
                                $scope.pages.star = data.data.startRow;
                                $scope.pages.end = data.data.endRow;
                                $scope.pages.pageNum = data.data.pageNum;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            /**
             * 重置
             */
            $scope.clearParams = function() {
                $scope.formRefer = {};
                initTime();
            };
            /**
             * 开始时间
             */
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };

            /**
             * 结束时间
             */
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };

            /**
             * 初始化检索条件开始时间
             */
            function initTime() {
                var date = new Date();
                //开始日期向前推3个月
                date.setMonth(date.getMonth() - 3);
                //对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
                $scope.formRefer.startDate = inform.format(date, 'yyyy-MM-dd');
            }
            //传递所选tag数据
            $scope.popModal = function() {
                if(!$scope.proSelected || $scope.proSelected.length <=0){
                    inform.common('请选择项目！');
                    return;
                }

                $("#tag_modal").click();
            };
            /**
             * 创建标签
             */
            $scope.doTag = function() {
                if($scope.changeParam.name==='' || $scope.changeParam.name==null){
                    inform.common('请输入版本号！');
                    return;
                }
                if($scope.changeParam.message==='' || $scope.changeParam.message==null){
                    inform.common('请输入提交记录！');
                    return;
                }
                var urlData = {
                    "version": $scope.changeParam.name,
                    "ext": $scope.changeParam.ext,
                    "message": $scope.changeParam.message,
                    "tagList":$scope.proSelected
                };
                gitService.doTag(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        inform.common(data.message);
                        $("#create_tag_modal").modal("hide");
                        getData();
                    } else {
                        inform.common(data.message);
                    }
                    //创建标签完成 清空数据
                    $scope.changeParam = {};
                    $scope.proSelected = [];
                }, function() {
                    inform.common(Trans("tip.requestError"));
                });
            };
            /**
             *
             * 同步项目下的tag数据
             * @param item 所选项目
             */
            $scope.synTag = function (item) {
                var urlData = {
                    "projectId": item.projectId,
                    "repositoryName": item.repositoryName,
                    "projectName": item.projectName,
                    "type": item.type
                };
                gitService.synTag(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        inform.common(data.message);
                        getData();
                    } else {
                        inform.common(data.message);
                    }
                }, function() {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /*** 全选函数*/
            $scope.selectAll=function () {
                if ($scope.select_all) {
                    $scope.proSelected=[];
                    angular.forEach($scope.tableData, function (i) {
                        i.checked=true;
                        $scope.proSelected.push(i);
                    });
                } else {
                    angular.forEach($scope.tableData, function (i) {
                        i.checked=false;
                    });
                    $scope.proSelected=[];
                }
            }
            /*** 单选项目*/
            $scope.selectOne=function (i) {
                $scope.select_all=false;
                var index=$scope.proSelected.indexOf(i);
                if (index===-1 && i.checked) {
                    $scope.proSelected.push(i);
                } else if (index !==-1 && !i.checked) {
                    $scope.proSelected.splice(index, 1);
                }
            };
            $scope.select=function (i) {
                $scope.select_all=false;
                $scope.selectAll();
                var tagNameArr = [];
                var index=$scope.proSelected.indexOf(i);
                if (index===-1) {
                    $scope.proSelected.push(i);
                    //tag名按照-分组
                    tagNameArr = i.tagName.split("-");

                    if (tagNameArr[0].match("^SV")) {
                        tagNameArr[0] = tagNameArr[0].replace("SV","V");
                    }

                    $scope.changeParam.name = "";
                    $scope.changeParam.ext = "";
                    for (var j = 0; j < tagNameArr.length; j++) {
                        //找到日期位置
                        if (tagNameArr[j].match("^[0-9]{8}$")) {
                            //从开始位置到日期位置前一位 作为版本号
                            for (var k = 0; k < j - 1; k++) {
                                if (k !== 0 && k !== j - 1) {
                                    $scope.changeParam.name = $scope.changeParam.name + "-";
                                }
                                $scope.changeParam.name = $scope.changeParam.name + tagNameArr[k];
                            }
                            //从日期位置后一位到最后 作为附加信息
                            for (var m = j + 1; m < tagNameArr.length; m++) {
                                $scope.changeParam.ext = $scope.changeParam.ext + tagNameArr[m];
                                if (m !== tagNameArr.length - 1) {
                                    $scope.changeParam.ext = $scope.changeParam.ext + "-";
                                }
                            }
                            break;
                        }
                    }

                }
            };
            //取消所选项目
            $scope.cancel=function () {
                $scope.proSelected = [];
            };
            
            //拷贝标签地址 add by zhangyegong 20211025
            $scope.copyTagUrl = function () {
                //判断是否选择待拷贝数据
                if($scope.proSelected.length===0) {
                    inform.common("请选择需要拷贝的数据");
                    return;
                }
                //获取标签url并组装数据，多个标签以回车换行符分隔
                var content = "";
                for (var i=0, j=$scope.proSelected.length; i<j; i++) {
                    if (i === (j-1)) {
                        content = content + $scope.proSelected[i].url;
                    } else {
                        content = content + $scope.proSelected[i].url + "\r\n";
                    }
                }
                //创建隐藏域，执行copy命令
                var copyElement = document.createElement("textarea");
                copyElement.style.position = 'fixed';
                copyElement.style.opacity = '0';
                copyElement.textContent = content;  //为隐藏域赋值
                var body = document.getElementsByTagName('body')[0];
                body.appendChild(copyElement);
                copyElement.select();
                try{
                    document.execCommand('copy');
                    inform.common("拷贝成功");
                }catch (e) {
                    console.log("拷贝失败,"+e)
                    inform.common("拷贝失败");
                }
                //删除隐藏域
                body.removeChild(copyElement);
            }
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */

        }
    ]);
})();

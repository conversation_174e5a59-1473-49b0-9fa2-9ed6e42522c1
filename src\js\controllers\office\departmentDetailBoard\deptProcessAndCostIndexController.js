(function () {
    app.controller("deptProcessAndCostIndexController", ['LocalCache', '$rootScope','Trans', 'comService', '$scope', '$stateParams', '$state', 'AgreeConstant', 'inform', 'deptBoardFactory', 'deptProcessAndCostService', 'costMonitoringService',
        function (LocalCache, $rootScope,Trans, comService,$scope, $stateParams, $state, AgreeConstant, inform, deptBoardFactory, deptProcessAndCostService, costMonitoringService) {
            // 初始化
            deptBoardFactory.init($scope, '1');
            // 重置部分
            $scope.resetParam = resetParam;
            function resetParam(){
                deptBoardFactory.initTime($scope, '本年度');
            }

            // 统计区域
            function getStatisticsData() {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                $scope.showStatisticsInfo = false;
                deptProcessAndCostService.getRateInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.statisticsList = result.data;
                        $scope.showStatisticsInfo = true;
                    } else {
                        inform.common(result.message);
                        $scope.showStatisticsInfo = true;
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    $scope.showStatisticsInfo = true;
                });
            }

            var colorList = AgreeConstant.departmentBoard.mileStonesLineColorList;

            $scope.dealLine = function (item) {
                var styleObj = {};
                var isFinish = parseInt(item.isFinished);
                if (isFinish) {
                    styleObj['background-color'] = colorList[3];
                } else {
                    var isCurrent = parseInt(item.isCurrent);
                    if (isCurrent) {
                        var currentDeviation = parseInt(item.currentDeviation);
                        if (currentDeviation > 10) {
                            styleObj['background-color'] = colorList[2];
                        } else if (currentDeviation > 5 && currentDeviation <= 10) {
                            styleObj['background-color'] = colorList[1];
                        } else if (currentDeviation <= 5) {
                            styleObj['background-color'] = colorList[0];
                        }
                    } else {
                        styleObj['background-color'] = colorList[4];
                    }
                }
                return styleObj;
            }
            
            $scope.dealFlag = function (item) {
                var styleObj = {};
                var isFinish = parseInt(item.isFinished);
                if (!isFinish) {
                    styleObj['color'] = colorList[3];
                } else {
                    var deviation = parseInt(item.deviation);
                    if (deviation > 10) {
                        styleObj['color'] = colorList[2];
                    } else if (deviation > 5 && deviation <= 10) {
                        styleObj['color'] = colorList[1];
                    } else if (deviation <= 5) {
                        styleObj['color'] = colorList[0];
                    }
                }
                return styleObj;
            }

            $scope.checkMileStoneName = function (item) {
                var styleObj = {};
                var isCurrent = parseInt(item.isCurrent);
                if (isCurrent) {
                    styleObj['color'] = colorList[2];
                } else {
                    styleObj['color'] = '#58666e';
                }
                return styleObj;
            }

            var statusList = AgreeConstant.departmentBoard.mileStonesStatusList;
            $scope.checkStatus = function (item) {
                var status = parseInt(item.status);
                return statusList[status];
            }

            function getProjectList() {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                deptProcessAndCostService.getProjectProgressList(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.projectList = result.data;
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            // 进度弹窗框部分
            var processColorList = AgreeConstant.departmentBoard.processColorList;
            $scope.toProcess = function (item) {
                $scope.modalTitle = '进度';
                getProgressTopInfo(item.id);
                getProcessChart(item.id);
            }
            function getProgressTopInfo(id) {
                var currentUrlData = {
                    "id": id
                }
                deptProcessAndCostService.getProgressInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.topInfo = result.data;
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            function getProcessChart(id) {
                var currentUrlData = {
                    "id": id
                }
                deptProcessAndCostService.getProgressChartInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.processDetailList = result.data;
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            $scope.checkProcessInnerBgc = function (value) {
                var styleObj = {};
                var status = parseInt(value);
                if (status === 2) {
                    styleObj['background-color'] = processColorList[1];
                } else if (status === 1 || status === 0){
                    styleObj['background-color'] = processColorList[0];
                }
                return styleObj;
            }


            // 成本弹窗框部分
            $scope.getRelatedLevel = getRelatedLevel;
            $scope.toCost = function (item) {
                $scope.modalTitle = '成本';
                getTopInfo(item.id, item.projectName);
                getRelatedLevel('0', item.id)
                toCharts(item.id);
            }

            function getTopInfo(id, projectName) {
                var currentUrlData = {
                    "id": id,
                    "projectName": projectName
                }
                deptProcessAndCostService.getCostInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.costTopInfo = result.data;
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            function toCharts(id){
                //获取当前项目的开始时间、结束时间、并以周作为间隔，查询人力预算，平均到每周
                var urlData={
                    'projectId':id
                }
                costMonitoringService.getProjectCostChartData(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.projectCostChartData = data.data
                        setChartData();
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            window.addEventListener("resize", chartResize);
            $scope.$on("$destroy", function() {
                window.removeEventListener('resize', chartResize);
            })
            function chartResize() {
                if ($scope.myCodeCharts) { $scope.myCodeCharts.resize(); }
            }
            function setChartData(){
                var xLength = 0;
                if($scope.projectCostChartData.allWeeksActual>$scope.projectCostChartData.allWeeksPre){
                    xLength = $scope.projectCostChartData.allWeeksActual;
                }else {
                    xLength = $scope.projectCostChartData.allWeeksPre;
                }
                $scope.xData = [];
                for(var i=0;i<xLength*1+1;i++){
                    $scope.xData.push('第'+i+'周');
                }
                $scope.legendData = [];
                var titleText='';
                if($scope.projectCostChartData.budgetAddWeekHr){
                    $scope.legendData.push('预算人力');
                    titleText = '预算投入总人力：'+$scope.projectCostChartData.budgetAddWeekHr[$scope.projectCostChartData.budgetAddWeekHr.length-1]+'人天';
                }
                if($scope.projectCostChartData.actualAddWeekHr){
                    $scope.legendData.push('实际人力');
                    if(titleText){
                        titleText = titleText + '，实际投入总人力：'+$scope.projectCostChartData.actualAddWeekHr[$scope.projectCostChartData.actualAddWeekHr.length-1]+'人天';
                    }else {
                        titleText = '实际投入总人力：'+$scope.projectCostChartData.actualAddWeekHr[$scope.projectCostChartData.actualAddWeekHr.length-1]+'人天';
                    }
                }
                $scope.myCodeCharts = $scope.myCodeCharts ? $scope.myCodeCharts : echarts.init(document.getElementById('costChart'));
                var option = {
                    title: {
                        text: titleText,
                        left: '32%',
                        top:'5%',
                        //  textAlign: 'center',
                        textStyle:{
                            fontWeight: '',
                            fontSize: 15
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: formatterCall//用formatter回调函数显示多项数据内容
                    },
                    legend: {
                        data: $scope.legendData//['预算人力', '实际人力']
                    },
                    grid: {
                        left: '3%',
                        right: '5%',
                        bottom: '3%',
                        containLabel: true
                    },
                    toolbox: {

                    },
                    xAxis: {
                        type: 'category',
                        name:'日期',
                        boundaryGap: false,
                        data: $scope.xData,
                        axisLabel:{
                            rotate:35
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name:'人天',
                    },
                    series: [
                        {
                            name: '预算人力',
                            type: 'line',
                            smooth:false,//关键点，为true是不支持虚线，实线就用true
                            //线条样式
                            itemStyle:{
                                normal:{
                                    lineStyle:{
                                        type:'dashed'  //'dashed'网格线类型 'dotted'虚线 'solid'实线
                                    }
                                }
                            },
                            color:['#66ffcc'],
                            data: $scope.projectCostChartData.budgetAddWeekHr
                        },
                        {
                            name: '实际人力',
                            type: 'line',
                            color:['blue'],
                            data:$scope.projectCostChartData.actualAddWeekHr
                        }
                    ]
                };
                $scope.myCodeCharts.setOption(option, true);
                setTimeout(function (){
                    chartResize();
                }, 300)
            }
            /**
             * 用formatter回调函数自定义显示多项数据内容
             **/
            function formatterCall (params, ticket, callback) {
                var htmlStr = '';
                for(var i=0;i<params.length;i++){
                    var param = params[i];
                    var xName = param.name;//x轴的名称
                    var seriesName = param.seriesName;//图例名称
                    var value = param.value;//y轴值
                    var color = param.color;//图例颜色
                    if (!value) {
                        continue;
                    }

                    htmlStr +='<div>';
                    //为了保证和原来的效果一样，这里自己实现了一个点的效果
                    htmlStr += '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:'+color+';"></span>';
                    if(i===0 && seriesName ==='预算人力'){
                        htmlStr += "当前参考投入总人力：" +value+ '人天<br/>';//x轴的名称
                        $scope.currentReferenceHrPre = value;
                    }
                    if(i===1 || seriesName ==='实际人力'){
                        htmlStr += "已投入总人力：" +value+ '人天<br/>';//x轴的名称
                        var dto = $scope.projectCostChartData.actualAvgWeekHrMap[xName];
                        htmlStr += "本周投入人力："+dto.actualWeekHr+"人天<br/>";
                        htmlStr += "本周结束日期："+dto.actualWeekEndTime+"<br/>";
                        if(params.length === 2){
                            htmlStr += "参考偏差：" +(($scope.currentReferenceHrPre*1-value*1)/$scope.currentReferenceHrPre*1*100).toFixed(2)+ '%<br/>';//x轴的名称
                        }
                    }
                    htmlStr += '</div>';
                }
                return htmlStr;
            }

            var status=['1','2'];
            var seclist=['预算','开发','超出预算'];
            $scope.functionType = 'costInfo';
            /**
             * 获取人员级别
             */
            function getRelatedLevel(plmLog, id){
                var urlData = {
                    'plm': plmLog,
                    'projectId': id,
                    'status': status
                };
                costMonitoringService.getRelatedLevel(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        if (plmLog!=='plm'){
                            //项目表头
                            $scope.levelList = data.data;
                            $scope.secLevelList=[];
                            angular.forEach($scope.levelList, function (detail, i) {
                                for (var j=0;j<seclist.length;j++){
                                    $scope.secLevelList.push(seclist[j]);
                                }
                            })
                            //合计
                            for (var d=0;d<seclist.length-1;d++){
                                $scope.secLevelList.push(seclist[d]);
                            }
                            if ($scope.funtype!==true){
                                $scope.secLevelList.push('偏差比例');
                            }
                        } else {
                            //PLM表头
                            $scope.plmLevelList = data.data;
                            $scope.plmSecLevelList=[];
                            angular.forEach($scope.plmLevelList, function (detail, i) {
                                for (var j=0;j<seclist.length;j++){
                                    $scope.plmSecLevelList.push(seclist[j]);
                                }
                            });
                            //合计
                            for (var b=0;b<seclist.length-1;b++){
                                $scope.plmSecLevelList.push(seclist[b]);
                            }
                            if ($scope.funtype!==true){
                                $scope.plmSecLevelList.push('偏差比例');
                            }
                        }
                        //获取人员岗位
                        getRelatedTitle(plmLog, id);
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 获取人员岗位
             */
            function getRelatedTitle(plmLog, id){
                var urlData = {
                    'plm': plmLog,
                    'projectId': id,
                    'status': status
                };
                costMonitoringService.getRelatedTitle(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        if (plmLog!=='plm'){
                            //项目行
                            $scope.titleList = data.data;
                        } else {
                            //PLM行
                            $scope.plmTitleList = data.data;
                        }
                        //获取单元格（再单元格后添加调用费用）
                        getHrDetail(plmLog, id);
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 获取单元格
             */
            function getHrDetail(plmLog, id){
                var urlData = {
                    'plm': plmLog,
                    'projectId': id,
                    'status': status
                };
                costMonitoringService.getHrDetail(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        if (plmLog!=='plm'){
                            //项目行
                            $scope.infoList=[];
                            packageInfo(data.data,$scope.titleList,$scope.levelList,$scope.infoList,seclist);
                            //人力费用
                            getAmountDetail(plmLog,$scope.infoList, id);
                            //项目费用
                            getProjectFee(id)
                        } else {
                            //PLM行
                            $scope.plmInfoList = [];
                            packageInfo(data.data,$scope.plmTitleList,$scope.plmLevelList,$scope.plmInfoList,seclist);

                        }
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 获取人力费用
             */
            function getAmountDetail(plmLog,finList ,id){
                costMonitoringService.getAmountDetail(id,plmLog).then(function (data) {
                    getAmountReturn(data,plmLog,finList);
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 获取人力费用回调
             */
            var getAmountReturn = function (data,plmLog,finList){
                if (data.code === AgreeConstant.code) {
                    var infoList = data.data
                    if (infoList.length===0){
                        return;
                    }
                    getAmounDetailSum(plmLog,infoList,finList);
                } else {
                    inform.common(data.message);
                }
            }
            /**
             * 费用求合计
             */
            function getAmounDetailSum(plmLog,infoList,finList){
                var levels = [];
                if (plmLog!=='plm'){
                    //项目表头
                    levels = $scope.levelList;
                } else {
                    //PLM表头
                    levels = $scope.plmLevelList;
                }
                var info = {
                    title: '成本合计',
                    level: []
                };
                //计算合计
                var sumAll=[0,0,0];
                //级别
                angular.forEach(levels, function (detail, i) {
                    var falg=false;
                    for (var s=0;s<infoList.length;s++){
                        var one = infoList[s];
                        if (one.level===detail){
                            falg = true;
                            //如果是开发
                            if ($scope.funtype!==true){
                                sumAll[0]=(sumAll[0]*1+one.budget*1).toFixed(2);
                                info.level.push(inform.formatMoney(one.budget));
                                sumAll[1]=(sumAll[1]*1+one.develop*1).toFixed(2);
                                info.level.push(inform.formatMoney(one.develop));
                                sumAll[2]=(sumAll[2]*1+one.exceedBudget*1).toFixed(2);
                                info.level.push(inform.formatMoney(one.exceedBudget));
                                continue;
                            }
                            //如果是维护
                            sumAll[0]=(sumAll[0]*1+one.develop*1).toFixed(2);
                            info.level.push(inform.formatMoney(one.develop));
                            sumAll[1]=(sumAll[1]*1+one.maintain*1).toFixed(2);
                            info.level.push(inform.formatMoney(one.maintain));
                            sumAll[2]=(sumAll[2]*1+one.allPutInTo*1).toFixed(2);
                            info.level.push(inform.formatMoney(one.allPutInTo));
                        }
                    }
                    if (falg===false){
                        info.level.push(0);
                        info.level.push(0);
                        info.level.push(0);
                    }
                })
                var sumAllMoney = [];
                for (var x=0;x<sumAll.length;x++){
                    sumAllMoney[x] = inform.formatMoney(sumAll[x]);
                }
                //如果是开发
                if ($scope.funtype!==true){
                    //计算行合计
                    getLineSum(sumAll,info.level,sumAllMoney);
                } else {
                    info.level.push(sumAllMoney[0]);
                    info.level.push(sumAllMoney[1]);
                }
                finList.push(info);
            }
            /**
             * 组装数据
             */
            function packageInfo (list,titles,levels,finList,secs){
                //岗位
                angular.forEach(titles, function (title, i) {
                    //一个岗位一条记录
                    var info = {
                        title: title,
                        level: []
                    };
                    //记录行合计
                    var sumOne=[0,0,0];
                    //级别
                    angular.forEach(levels, function (level, i) {
                        //状态
                        angular.forEach(secs, function (sec, i) {
                            //默认无匹配
                            var flag=false;
                            for(var j=0;j<list.length;j++){
                                var one = list[j];
                                if(one.title === title){
                                    if (one.level === level){
                                        if(one.type===sec){
                                            info.level.push(inform.removeZero(one.workload));
                                            sumOne[i] = inform.removeZero(sumOne[i]*1+one.workload*1);
                                            //如果有匹配的
                                            flag=true;
                                            continue;
                                        }
                                    }
                                }
                            }
                            if (flag===false){
                                info.level.push("0");
                            }
                        });
                    });
                    if ($scope.funtype!==true){
                        getLineSum(sumOne,info.level)
                    } else {
                        info.level.push(sumOne[0]);
                        info.level.push(sumOne[1]);
                    }
                    finList.push(info);
                });
                //记录列合计
                var sumAll = {
                    title: '人力合计',
                    level: []
                };
                if (finList!=null&&finList.length!==0){
                    getCellSum(sumAll,finList)
                }
            }
            /**
             * 获取行合计
             */
            function getLineSum (sumOne,info,sumAllMoney){
                //计算行合计
                for (var i = 0;i<sumOne.length;i++){
                    if (i===2){
                        //计算偏差比例
                        var num = sumOne[0]*1;
                        if (num===0){
                            num=1;
                        }
                        //如果预计为0则应该(x-0)/1
                        var test = (((sumOne[0]*1-sumOne[1]*1)/num)*100).toFixed(0);
                        info.push(test+'%');
                    }else{
                        if (sumAllMoney!=null){
                            info.push(sumAllMoney[i]);
                        }else{
                            info.push(sumOne[i]);
                        }
                    }
                }
            }
            /**
             * 获取列合计
             */
            function getCellSum(sumAll,finList){
                //每个岗位的
                for (var i=0;i<finList.length;i++){
                    var one = finList[i];
                    //第几位数据   为维护时不存在百分比
                    var size = one.level.length
                    if ($scope.funtype!==true){
                        size = one.level.length-1;
                    }
                    for (var j=0;j<size;j++){
                        if (sumAll.level[j]==null){
                            sumAll.level[j] = inform.removeZero(one.level[j]*1);
                        }else {
                            sumAll.level[j] = inform.removeZero(sumAll.level[j]*1 + one.level[j]*1);
                        }
                    }
                }
                if ($scope.funtype!==true){
                    //重新计算超出预算且不为维护时（维护需要计算总投入可以直接相加）
                    for (var x=0;x<sumAll.level.length;x++){
                        var num = (x+1)%3;
                        if (num==0){
                            if (sumAll.level[x-2]>sumAll.level[x-1]){
                                sumAll.level[x] = 0;
                            }else {
                                sumAll.level[x] = inform.removeZero(sumAll.level[x-1]-sumAll.level[x-2]);
                            }

                        }
                    }
                    //不为维护时才会计算比例
                    var allSum = [];
                    allSum.push(sumAll.level[sumAll.level.length-2]);
                    allSum.push(sumAll.level[sumAll.level.length-1]);
                    allSum.push(0)
                    var sum = [];
                    getLineSum(allSum,sum);
                    sumAll.level.push(sum[2]);
                }
                finList.push(sumAll);
            }
            /**
             * 获取项目费用
             */
            function getProjectFee(id){
                var urlData = {
                    'plm': "0",
                    'projectId': id,
                    'status': status
                };
                costMonitoringService.getProjectFee(urlData).then(returnFunction, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * 费用的回调方法
             */
            var returnFunction = function (data) {
                if (data.code === AgreeConstant.code) {
                    $scope.feeList = data.data;
                    var fee ={
                        'feeType':'合计',
                        'feeCost':0,
                        'feePre':0,
                        'feeDiff':0
                    };
                    for (var i=0;i<$scope.feeList.length;i++){
                        var one=$scope.feeList[i];
                        fee.feeCost = (fee.feeCost*1+one.feeCost*1).toFixed(2);
                        fee.feePre = (fee.feePre*1+one.feePre*1).toFixed(2);
                        fee.feeDiff = (fee.feeDiff*1+one.feeDiff*1).toFixed(2);
                        one.feeCost = inform.formatMoney(one.feeCost);
                        one.feePre = inform.formatMoney(one.feePre);
                        one.feeDiff = inform.formatMoney(one.feeDiff);
                    }
                    //不为维护则需计算比例
                    if ($scope.funtype!==true){
                        var test = 0;
                        //计算超出比率
                        if (fee.feeCost*1>fee.feePre*1){
                            var num = fee.feePre*1;
                            if (num===0){
                                num=1;
                            }
                            //如果预计为0则应该(x-0)/1
                            test = (((fee.feeCost*1-fee.feePre*1)/num)*100).toFixed(0);
                        }
                        fee.feeDiff =test+'%';
                    } else {
                        fee.feeDiff = inform.formatMoney(fee.feeDiff);
                    }
                    fee.feeCost = inform.formatMoney(fee.feeCost);
                    fee.feePre = inform.formatMoney(fee.feePre);
                    $scope.feeList.push(fee);
                    //获取PLM表头
                    getRelatedLevel("plm");
                } else {
                    inform.common(data.message);
                }
            }

            $scope.getData = getData;
            function getData() {
                getStatisticsData();
                getProjectList();
            }
            // 页面加载后触发
            $scope.$watch('$viewContentLoaded', function () {
                var localFormRefer = LocalCache.getObject('departmentList_formRefer');
                if (Object.keys(localFormRefer).length > 0) {
                    $scope.formRefer = localFormRefer;
                    $scope.butFlag = localFormRefer.searchTimeString;
                }
                if ($stateParams.orgCode) {
                    $scope.formRefer.orgCode = $stateParams.orgCode;
                }
                getData();
            });
        }]);
})();
(function () {
    app.controller("toTrackingSheetController", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','feedbackProblemService','projectManagementService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,feedbackProblemService,projectManagementService,inform,Trans,AgreeConstant,LocalCache,$http) {

		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		$scope.limitList = AgreeConstant.limitList;
		$scope.projectName = $stateParams.projectName;
        initInfo();

        //问题来源
        $scope.problemSourceSelect = ['客户','市场','测试组','项目组','产品组','售前','售后','运维','荣鑫保障','下游项目组','其他'];
        //严重程度
        $scope.problemSeveritySelect = ['事件','轻微线上问题','一般线上问题','严重线上问题'];


		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */

		/**
		 * 初始化
		 */
    	function initInfo() {
            //获取山东新北洋集团的下级部门信息
            $scope.departmentList = [];
            $scope.departmentMap = {};
            comService.getOrgChildren('D010053').then(function(data) {
            	$scope.departmentList = comService.getDepartment(data.data);
            	 for(var j = 0; j < data.data.length; j++) {
                     $scope.departmentMap[data.data[j].orgName] = data.data[j].orgCode;
                 }
             });
            //获取员工信息
            $scope.employeeList = [];
            comService.getEmployeesByOrgId('').then(function(data) {
                if (data.data) {
                    $scope.employeeList = data.data;
                }
            });

            setTimeout(getProblemInfo,500);
    	}

    	/**
         * 获取某个反馈问题的详情
         */
        function getProblemInfo() {

        	 var urlData = {
                'problemId':$stateParams.item
        	 };
        	 feedbackProblemService.getData(urlData).then(function (data) {
             	if (data.code === AgreeConstant.code) {
             	    var itemData = data.data.list[0];
             	    //问题诊断人转换为数组
             	    var problemDiagnostician = $.trim(itemData.problemDiagnostician);
             	    //"、“替换为”，“
             	    problemDiagnostician=problemDiagnostician.replace(/、/g,",");
             	    itemData.problemDiagnostician = problemDiagnostician.split(",");
             	    //
             	    itemData.problemSeverity =itemData.problemSeverity;
                    //反馈问题详情
                    $scope.item = itemData;
                    $scope.item.productPM = "";
                    getProductPMName($scope.item.officeProjectId)
                } else {
                    inform.common(data.message);
                }
            }, function () {
                inform.common(Trans("tip.requestError"));
            });
        }


        /**
         * 获取某个反馈问题的对应的Product Owner名称
         */
        function getProductPMName(projectId) {

            if(projectId ==='') {
                return;
            }
             var urlData = {
                'id':projectId
             };
             projectManagementService.getProjectInfoById(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    $scope.item.productPM = data.data.productManagerName;
                } else {
                    inform.common(data.message);
                }
            }, function () {
                inform.common(Trans("tip.requestError"));
            });

        }
        /**
         * 提交详情信息,存在就更新，不存在就新增
         */
        $scope.submitEventFun = function (){

            var urlData = $scope.item;
            urlData.projectName = $scope.projectName;
            //问题诊断人员
            urlData.problemDiagnostician=$scope.item.problemDiagnostician.toString();

            feedbackProblemService.createTrackingSheet(urlData).then(function (result) {
                if (result.code === AgreeConstant.code) {
                        inform.common('创建钉钉跟踪单成功！');
                        window.history.go(-1);
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
        }
	 		/**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */

	}]);
})();
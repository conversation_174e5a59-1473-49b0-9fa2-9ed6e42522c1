/*
* @Author: fubaole
* @Date:   2017-09-05 10:05:00
* @Last Modified by:   fubaole
* @Last Modified time: 2018-02-24 13:45:13
*/
(function() {
    'use strict';
  app.factory('comService', comService);
  comService.$inject=["HttpService",'$rootScope'];

  function comService(HttpService,$rootScope){
    var service={
    		getParamList:getParamList,
    		getOrganizationListByParentId:getOrganizationListByParentId,
    		getOrgChildren:getOrgChildren,//获取指定部门的所有下级部门信息
    		getEmployeesByOrgId:getEmployeesByOrgId,//获取指定部门的所有员工信息
            getEmployeesByOrgCode:getEmployeesByOrgCode,//获取指定部门的所有员工信息
    		getLines:getLines,//获取所有产品线名称
    		getProjectsByLine:getProjectsByLine,//获取产品线下所有项目名称
    		getProjectsByLineOffice:getProjectsByLineOffice,//office项目中 获取所有产品线下的项目
    		getDepartment:getDepartment,//获取所有需要的部门
			getProjectsName:getProjectsName,//获取所有的项目名称
			getProjectsNameByParams:getProjectsNameByParams,//获取所有的项目名称(可通过参数筛选)
		    getEmployeesName:getEmployeesName,//获取所有的员工名称
		    getProductManageName:getProductManageName,//获取所有的Product Owner列表
		    getTitleList:getTitleList,//获取职称
		    getProfessionalModuleList:getProfessionalModuleList,//专业模块列表
		    getEthnicGroupList:getEthnicGroupList,//获取系研族群列表
		    getAreaList:getAreaList,//地区列表
		    getStateList:getStateList,//状态
		    getLeavingReasonList:getLeavingReasonList,//获取员工离职原因列表
            validAuthentication:validAuthentication,//获取权限码
            checkAuthentication:checkAuthentication,//检查权限并控制部门控件
            getCertificateTypeList:getCertificateTypeList,//获取证书类别列表
            getRolePermission:getRolePermission,//获取角色权限
            getStaffList:getStaffList,//获取员工id和姓名列表
            getPrimaryDeptList:getPrimaryDeptList, //获取一级部门列表
            selectSecDeptList:selectSecDeptList,//获取二级部门列表
            isCenterOffice:isCenterOffice,//判断是否为中心办成员
            getButtonPermission:getButtonPermission,//根据用户获取权限按钮
            getButtonPermissionName:getButtonPermissionName,//无回掉函数
            queryEffectiveParam:queryEffectiveParam,//参数字典控制：获取参数value与code
            upParamValue:upParamValue,//更新参数表param_value
            getProDepartmant:getProDepartmant,//获取禅道中的部门
            getProUser:getProUser,//获取禅道中的用户
            getZentaoProject:getZentaoProject,//根据产品线与关键字查询禅道项目信息
            getProLangList:getProLangList,//获取禅道lang表信息
            getMenu:getMenu,//获取页面菜单
            getOrgChildrenAndSelf:getOrgChildrenAndSelf,//获取子部门和自身
            getRolePermissionNew:getRolePermissionNew,
            getGroupInfoList:getGroupInfoList,
            validDeptAuthority:validDeptAuthority
    };
    return service;
    function getOrgChildrenAndSelf(orgCode){
        return HttpService.get($rootScope.getWaySystemApi+'common/getOrgChildrenAndSelf',
            {'orgCode':orgCode});
    }
    function getMenu(routePath) {
        return HttpService.get($rootScope.getWaySystemApi+'common/getMenu',
            {'routePath':routePath});
    }
    /**
     * 获取平台对应的禅道项目
     */
    function getZentaoProject(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'common/getZentaoProject', urlData);
    }
    /**
     * 更新参数表value
     */
    function upParamValue(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'common/upParamValue', urlData);
    }
    /**
     * 参数字典控制：获取参数value与code
     */
    function queryEffectiveParam(paramName,paramTypeCode){
        var urlData={
            'paramName':paramName,
            'paramTypeCode':paramTypeCode
        };
        return HttpService.post($rootScope.getWaySystemApi+'param/queryEffectiveParam',urlData);
    }
    /**
     * 获取所有的Product Owner列表
     */
    function getProductManageName(){
    	return HttpService.get($rootScope.getWaySystemApi+'common/getProductManageList');
    }
    /**
     * 根据用户获取权限按钮
	 * @param urlData 用户ID,按钮父级权限菜单名称
     */
	function getButtonPermission(urlData,scope) {
        var buttons=urlData.buttons;
		HttpService.get($rootScope.getWaySystemApi+'org/getUser/getButtonPermission',
				{'userId':urlData.userId,'parentPermission':urlData.parentPermission}).then(function(data) {
			angular.forEach(data.data, function(button, i) {//遍历有权限的按钮
				var name = buttons[button];//查看该角色具有权限的按钮是否是页面所需要判断的
				if (null != name){
					scope[name] = true;//动态获取$scope下的变量
				}
			});
		});
	}
	/**
	* 检查员工权限控制一级和二级部门控件是否可以编辑
	* @param param {"primaryDeptId":一级部门控件Id,
	                primaryDeptList:一级部门控件的数据集合对象,
	                primaryDeptScopeModel:一级部门控件的ScopeModel,
	                subDeptList:初始化二级部门控件的数据集合对象,
	                subDeptScopeModel:二级部门控件的ScopeModel}
	* @param callBack 回调处理
	**/
	function checkAuthentication(scope,param,callBack,loginName) {
        //默认值
        scope[param.primaryDeptScopeModel] = '-999';
        scope[param.subDeptScopeModel] = '-999';
       //判断是否为中心办
       isCenterOffice().then(function (res) {

        if(res.code === "0000" && res.data.code === '01'){
            scope[param.primaryDeptScopeModel] = '';
            scope[param.subDeptScopeModel] = '';
             //01全部权限
             callBack(res.data);
        } else {
            //检查部门权限
             validAuthentication("0001","2").then(function (result) {
                if(result.code === '0000'){
                    if(result.data.code === '02'){
                        //判断是一级部门负责人还是二级部门负责人
                        validDeptAuthority(loginName).then(function (deptResult) {
                           if(deptResult.code === '0000'){
                                //一级部门负责人
                               if(deptResult.data.code === '02'){
                                     scope[param.primaryDeptScopeModel] = res.data.primaryDeptCode;
                                     //初始化一级部门控件
                                     scope[param.primaryDeptList] = deptResult.data.deptList;
                                     scope[param.subDeptScopeModel] = '';
                                     //初始化二级部门
                                     getOrgChildren(res.data.primaryDeptCode).then(function (data) {
                                         if (data.code === '0000') {
                                             scope[param.subDeptList] = data.data;
                                         }
                                     });
                               } else if(deptResult.data.code === '03') {
                                     scope[param.primaryDeptScopeModel] = res.data.primaryDeptCode;
                                    //二级部门负责人
                                    //一级部门控件不可编辑
                                    $(param.primaryDeptId).attr("disabled","disabled");
                                    scope[param.subDeptScopeModel] = res.data.departmentCode;
                                    scope[param.subDeptList] = deptResult.data.deptList;
                               }
                               callBack({'code':'02'});
                           } else {
                                //00无权限
                                callBack({'code':'00'});
                           }
                        });

                    } else {
                        scope[param.primaryDeptScopeModel] = '';
                        scope[param.subDeptScopeModel] = '';
                        callBack(result.data);
                    }
                } else {

                     //00无权限
                     callBack({'code':'00'});
                }

              });
        }

	  });
	}
	/**
     * 根据用户获取权限按钮
	 * @param urlData 用户ID,按钮父级权限菜单名称
     */
	function getButtonPermissionName(urlData) {
		return HttpService.get($rootScope.getWaySystemApi+'org/getUser/getButtonPermission',
				{'userId':urlData.userId,'parentPermission':urlData.parentPermission});
	}
    /**
     * 获取指定部门的所有下级部门信息
	 * @param orgCode 部门编号
     */
	function getOrgChildren(orgCode) {
		return HttpService.get($rootScope.getWaySystemApi+'common/getOrgChildren',
				{'orgCode':orgCode});
	}

    /**
     * 获取指定部门的所有员工信息
	 * @param orgId 部门ID
	 * @param isAll 1代表查询全部，0代表查询在职员工
     */
    function getEmployeesByOrgId(orgId, isAll) {
        if (!isAll) {
            isAll = 0;
        }
        return HttpService.get($rootScope.getWaySystemApi + 'common/getEmployeesByOrgId',
            {
                'orgId': orgId,
                'isAll': isAll
            });
    }
 /**
     * 获取指定部门的所有员工信息信息
     * @param orgCode 部门编号
     */
    function getEmployeesByOrgCode(orgCode) {
        return HttpService.get($rootScope.getWaySystemApi+'common/getEmployeesByOrgCode',
                {'orgCode':orgCode});
    }
	  /**
	   * 获取所有员工Name
	   */
	  function getEmployeesName() {
		  return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'common/getEmployeesName');

	  }

    /**
     * 获取指定参数的信息
	 * @param paramName 参数名称
	 * @param paramType 参数分类
     */
	function getParamList(paramName,paramType) {
		return HttpService.get($rootScope.getWaySystemApi+'common/getParamList',
				{'paramName':paramName,'paramType':paramType});
	}

	/**
	 * 通过ID获取下级组织机构信息
	 */
    function getOrganizationListByParentId(id) {
        return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getOrgListByParentId?parentOrgId='+id);
      }
    /**
     * 获取所有产品线名称
     */
    function getLines() {
    	return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'common/getLines');
    }
    /**
     * 获取产品线下所有项目名称
     */
    function getProjectsByLine(id) {
    	return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'common/getProjectsByLine?lineId='+id);
    }
    /**
     * 获取Office下需要的部门
     */
    function getDepartment(data) {
    	var array = [];
    	angular.forEach(data, function(org, i) {//遍历一级部门列表
			array.push(org);
    	});
    	array.push({orgCode:'D010053',orgName:'软件开发部'});
    	return array;
    }
    /**
     * 获取Office产品线下所有项目名称
     */
    function getProjectsByLineOffice(id) {
    	return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'common/getProjectsByLineOffice?lineId='+id);
    }
    /**
	 * 获取所有项目名称
	 */
    function getProjectsName() {
    	return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'common/getProjectsName');
    }
    /**
	 * 获取所有项目名称(可通过参数筛选)
	 */
    function getProjectsNameByParams(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'common/getProjectsNameByParams',urlData);
    }

    /**
     * 获取所有职称
     */
    function getTitleList() {
        return HttpService.post($rootScope.getWaySystemApi+'common/getTitleList');
    }
    /**
     * 获取专业模块列表
     */
    function getProfessionalModuleList() {
        return HttpService.post($rootScope.getWaySystemApi+'common/getStaffProfessionalModule');
    }
    /**
     * 获取系研族群列表
     */
    function getEthnicGroupList() {
        return HttpService.post($rootScope.getWaySystemApi+'common/getStaffEthnicGroup');
    }

    /**
     * 获取地区列表
     */
    function getAreaList() {
        return HttpService.post($rootScope.getWaySystemApi+'common/getAreaList');
    }

     /**
     * 获取状态列表
     */
    function getStateList() {
        return HttpService.post($rootScope.getWaySystemApi+'common/getStateList');
    }

    /**
     * 获取状态列表
     */
    function getLeavingReasonList() {
        return HttpService.post($rootScope.getWaySystemApi+'common/getLeavingReasonList');
    }
    /**
    *获取权限码
    */
    function validAuthentication(resourceId,positionType) {
        if (!positionType) {
            positionType = 0;
        }
        return HttpService.get($rootScope.getWaySystemApi+'dataPermissionGroup/validAuthentication',{
            'resourceId': resourceId,
            'positionType': positionType
        });
    }
    /**
    *获取证书类别
    */
    function getCertificateTypeList() {
        return HttpService.post($rootScope.getWaySystemApi+'common/getCertificateTypeList');
    }

    /**
     *获取角色权限
     */
    function getRolePermission(resourceId) {
        return HttpService.post($rootScope.getWaySystemApi+'rolePermission/userPowerChcek',resourceId);
    }

      /**
       *获取角色权限 新
       */
    function getRolePermissionNew(resourceId) {
        return HttpService.post($rootScope.getWaySystemApi+'rolePermission/userPowerCheckNew',resourceId);
    }
    /**
    *获取员工工号和姓名列表
    */
    function getStaffList() {
        return HttpService.post($rootScope.getWaySystemApi+'common/getStaffList');
    }
    /**
     * 获取一级部门列表
     */
    function getPrimaryDeptList(){
        return HttpService.post($rootScope.getWaySystemApi + 'common/selectDeptListByLoginName');
    }

    /**
     * 获取二级部门列表
     */
    function selectSecDeptList(primaryDeptCode){
        return HttpService.post($rootScope.getWaySystemApi + 'common/selectSecDeptList',primaryDeptCode);
    }
    /**
    ** 判断是否为中心办成员
    **/
    function isCenterOffice(){
        return HttpService.post($rootScope.getWaySystemApi + 'dataPermissionGroup/isCenterOffice');
    }

    /**
     ** 获取禅道中的部门
     **/
     function getProDepartmant(){
         return HttpService.get($rootScope.getWaySystemApi + 'common/getProDepartmant');
     }
     /**
      * 获取禅道中的用户
      */
     function getProUser(departId){
    	 return HttpService.get($rootScope.getWaySystemApi + 'common/getProUser',{'departId':departId});
     }
     /**
      * 获取禅道Lang表指定参数的信息
      * @param paramName 参数名称
      * @param paramType 参数分类
       */
     function getProLangList(module, section, lang, system) {
         return HttpService.get($rootScope.getWaySystemApi + 'common/getProLangList',
             {
                 'module': module,
                 'section': section,
                 'lang': lang,
                 'system': system
             });
     }
     //获取小组集合
     function getGroupInfoList(){
        return HttpService.post($rootScope.getWaySystemApi + 'common/getGroupInfoList');
     }
     //验证部门权限(flag是判断正副：0正，1副）
     function validDeptAuthority(param,flag){
         if(!flag){
             flag = "";
         }
        return HttpService.get($rootScope.getWaySystemApi+'dataPermissionGroup/validDeptAuthority',{
            'param':param,
            'flag':flag
        });
     }
   }
})();

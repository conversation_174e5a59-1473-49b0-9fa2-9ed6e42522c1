(function () {
    app.controller("costInputManagerController", ['$rootScope', 'comService', 'costInputService', '$scope', '$state', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http', function ($rootScope, comService, costInputService, $scope, $state, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {

        /**
         * *************************************************************
         *             初始化部分                                 开始
         * *************************************************************
         */

        // 查询参数（多次用到，定义为全局）
        var urlData = {};
        // 查询数据
        $scope.getData = getData;
        // 人力成本投入数据
        $scope.costInputData = [];
        // 费用成本投入数据
        $scope.costInputFeeData = [];
        $scope.formRefer = {};
        $scope.formRefer.costStatus = '0';
        //获取缓存
        if (Object.keys(LocalCache.getObject('costInputManager_searchObject')).length > 0) {
            $scope.formRefer = LocalCache.getObject('costInputManager_searchObject');
        }
        //对原缓存进行覆盖
        LocalCache.setObject("costInputManager_searchObject", {});
        // 是否为项目经理
        $scope.isProjectManager = false;
        // 判断是否为项目经理
        checkProjectManager();
        // 获取初始化数据
        getInitData();
        // 成本状态下拉框数据
        $scope.costStatusMapQueryList = [{
            value: '0',
            label: '待确认'
        }, {
            value: '1',
            label: '已确认'
        }, {
            value: '2',
            label: '已结算'
        }];
        //项目投入程度
        $scope.costStatusMapList = [{
            value: '0',
            label: '待确认'
        }, {
            value: '1',
            label: '已确认'
        }, {
            value: '2',
            label: '已结算'
        }, {
            value: '3',
            label: '已确认'
        }];
        /**
         * *************************************************************
         *              初始化部分                                 结束
         * *************************************************************
         */

        /**
         * *************************************************************
         *              方法声明部分                                 开始
         * *************************************************************
         */

        /*
        * 判断登录人是否是项目经理
        * */
        function checkProjectManager() {
            if (JSON.parse(LocalCache.getSession('roleList')).find(function (item) {
                return item.roleName === '项目经理';
            })) {
                $scope.isProjectManager = true;
            }
        }

        /*
        * 获取页面信息
        * */
        function getInitData() {
            //获取产品线
            comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                if (data.data) {
                    $scope.projectLine = data.data;
                }
            });
            //获取系统集成研发的下级部门信息
            $scope.departmentList = [];
            comService.getOrgChildren('D010053').then(function (data) {
                $scope.departmentList = comService.getDepartment(data.data);
            });
            //获取所有岗位信息
            $scope.costTitleMap = new Map();
            costInputService.getCostWeekTitle().then(function (result) {
                if (result.code === AgreeConstant.code) {
                    var jsonMap = angular.fromJson(result.data);
                    // 把map转为数组
                    for (var key in jsonMap) {
                        $scope.costTitleMap.set(key, jsonMap[key]);
                    }
                    // 对数组排序
                    $scope.costTitleMap = Array.from($scope.costTitleMap).sort(function (a, b) {
                        return parseInt(a[0]) - parseInt(b[0]);
                    });
                    // 获取每周成本信息
                    getData();
                } else {
                    inform.common(result.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
            initTime();
        }

        /**
         * 初始化时间
         */
        function initTime() {
            if($scope.formRefer.startTime == null){
                var date = new Date();
                date.setMonth(date.getMonth() - 2);
                $scope.formRefer.startTime = inform.format(date, 'yyyy-MM-dd');
            }
        }
        /**
         * 获取每周的项目成本信息
         */
        function getData() {
            urlData = {
                'orgCode': $scope.formRefer.orgCode,
                'productLine': $scope.formRefer.productLine,
                'statusList': $scope.formRefer.costStatus === '1' ? ['1', '3'] : [$scope.formRefer.costStatus],
                'cname': $scope.formRefer.projectName,
                'projectManager': $scope.formRefer.projectManager,
                'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd')//结束时间
            };
            if (!urlData.statusList[0]) {
                urlData.statusList = [];
            }
            // 获取人力投入信息
            getHrData();
            // 获取费用投入信息
            getFeeData();
        }
        /*
        * 获取人力投入信息
        * */
        function getHrData() {
            costInputService.getProjectcostWeek(urlData).then(function (result) {
                if (result.code === AgreeConstant.code) {
                    $scope.costInputData = angular.fromJson(result.data);
                    // 若某岗位没有数据则赋值为0
                    $scope.costInputData.forEach(function (item) {
                        $scope.costTitleMap.forEach(function (titleItem) {
                            if (!item.projectcostWeekDetailList.find(function (findItem) {
                                return findItem.titleCode === titleItem[0].toString();
                            })) {
                                var element = {
                                    titleCode: titleItem[0].toString(),
                                    workload: "0"
                                };
                                item.projectcostWeekDetailList.push(element);
                            }
                        });
                        // 对赋值完的数组进行排序
                        item.projectcostWeekDetailList.sort(function (a, b) {
                            return parseInt(a.titleCode) - parseInt(b.titleCode);
                        });
                    });

                    $scope.costInputDataNotPlm = [];
                    // 拆分数组（是否为plm需求）

                    $scope.costInputDataNotPlm = $scope.costInputData;

                    $scope.costInputDataNotPlm.forEach(function (row) {
                        var sum = 0;
                        row.projectcostWeekDetailList.forEach(function (cell) {
                            sum = sum + Number(cell.workload);
                        });
                        row.sum = Number(sum).toFixed(2);
                    })

                } else {
                    inform.common(result.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        }
        /*
        * 获取费用投入信息
        * */
        function getFeeData() {
            costInputService.getProjectcostFee(urlData).then(function (result) {
                if (result.code === AgreeConstant.code) {
                    $scope.costInputFeeData = angular.fromJson(result.data);
                    $scope.costInputFeeData.forEach(function (item) {
                        item.amount = inform.formatMoney(item.amount);
                    });
                } else {
                    inform.common(result.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        }
        /**
         * 确认弹框
         */
        $scope.confirm = function (str, func) {
            var modalInstance = $modal.open({
                templateUrl: 'myModalContent.html',
                controller: 'ModalInstanceCtrl',
                size: "sm",
                resolve: {
                    items: function items() {
                        return Trans(str);
                    }
                }
            });
            modalInstance.result.then(function () {
                func();
            });
        };
        /*
        * 跳转至人力详情页面
        * */
        $scope.goToHrDetail = function (item) {
            item = {
                'projectId': item.projectId,
                'plmUpgradeId':item.plmUpgradeId,
                'documentId':item.documentId,
                'startDate': item.startDate,
                'endDate': item.endDate,
                'statusList': [item.status],
                'id': item.id,
                'status': item.status,
                'week': item.week
            }
            LocalCache.setObject('costInputManager_searchObject', $scope.formRefer);
            $state.go('app.office.costHrInputDetail', {
                detailItem: JSON.stringify(item)
            });
        };
        /*
        * 重置查询信息
        * */
        $scope.reset = function () {
            $scope.formRefer = {};
            $scope.formRefer.costStatus = '0';
            initTime();
        };
        /**
         * 开始时间
         */
        $scope.openDateStart = function () {
            $scope.openedStart = true; //开始时间
        };
        /**
         * 结束时间
         */
        $scope.openDateEnd = function () {
            $scope.openedEnd = true; //结束时间
        };
        /**
         * 跳转修改
         */
        $scope.go = function(item) {
            if (item == null){
                inform.common("异常.");
                return;
            }
            // 设置滚动条高度
            $scope.formRefer.subDivTBDisScrollTop = $('#subDivTBDis').scrollTop();
            //保存当前页面查询条件
            LocalCache.setObject('costInputManager_searchObject', $scope.formRefer);
            //跳转页面
            $state.go("app.office.feeConfirm",
                {
                    id: item.id,
                    projectId: item.projectId,
                    companyProjectId: item.companyProjectId
                });
        };
        /**
         * *************************************************************
         *              方法声明部分                                 结束
         * *************************************************************
         */
    }]);
})();

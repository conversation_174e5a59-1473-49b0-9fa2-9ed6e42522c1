#wrapper-login {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  color: #fff;
  box-sizing:content-box;
  min-width: 990px;
  background:url("../../img/login/login-bg.png") no-repeat;
  -webkit-background-size: cover;
  background-size: cover;
}
#wrapper-login .header {
  height: 60px;
  line-height: 60px;
  width: 100%;
  padding: 0 20px;
  border-bottom: 1px solid #5c6063;
}
#wrapper-login .header > img {
  vertical-align: sub;
}
#wrapper-login .header > span {
  padding-left: 10px;
  font-size: 18px;
}
#wrapper-login .keyword ,
#wrapper-login .login-content {
  float: left;
  width: 50%;
  margin-top:10%;
}
#wrapper-login .keyword {
  padding-left: 15%;
  padding-top: 60px;
}

#wrapper-login .keyword > p {
    font-size: 14px;
}
#wrapper-login .login-content {
    padding-left: 12%;
    color: #373d41;
}
#wrapper-login .login-content .form-login {
    background-color: #fff;
    padding: 25px;
    width: 380px;
    height: 380px;
}

#wrapper-login .form-content .user-name {
    margin-top: 40px;
}
#wrapper-login .form-content .user-psd,
#wrapper-login .form-content .user-name {
    height: 40px;
    line-height: 40px;
    margin-bottom: 20px;
}
#wrapper-login .form-content .user-psd:hover,
#wrapper-login .form-content .user-name:hover,
.check-code .inputc-code:hover {
    border:1px solid #00c1de;
}

.check-code .input-code {
    height:30px;
    line-height: 30px;
}

.check-code .identifyingCode{
    color: #00c1de;
    border:1px solid #cfdadd;
    text-align: center;
    font-weight: bold;
    height: 40px;
    line-height: 40px;
    width: 100%;
}
.Remember {
    font-size:14px;
    line-height: 20px;
    vertical-align: middle;
    padding: 8px 0 ;
}

.Remember .seletLogin {
    display: inline-block;
    width: 15px;
    height: 15px;
    margin-right: 8px;
    vertical-align: middle;
    background: url("../../img/login/select.png") no-repeat left top;
    background-size: cover;
    cursor: pointer;
}

.Remember .seletedLogin {
    display: inline-block;
    width: 15px;
    height: 15px;
    margin-right: 8px;
    vertical-align: middle;
    background: url("../../img/login/selected.png") no-repeat left top;
    background-size: cover;
    cursor: pointer;
}
.submitBtn {
    margin-top: 30px;
    text-align: center;
    width: 100%;
    height: 40px;
    line-height: 40px;
}
.submitBtn button {
    width: 100%;
    height: 40px;
    font-size: 14px;
    color:#fff;
    letter-spacing:6px;
    border: 0;
}
.submitBtn button.btn-default {
   margin-top: 30px;
}

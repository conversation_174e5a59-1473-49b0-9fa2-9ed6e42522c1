
(function () {
    app.controller("teamEntranceDetailsController", [ '$scope','$rootScope','$stateParams','$state','LocalCache','inform','teamEntranceDetailsService','AgreeConstant','$modal',
        function ($scope,$rootScope, $stateParams,$state,LocalCache,inform,teamEntranceDetailsService,AgreeConstant,$modal) {
            //获取项目信息
            $scope.loaclParam = LocalCache.getObject('project_detail');
            if($scope.loaclParam.projectInfoParam){
                $scope.projectInfoParam = JSON.parse($scope.loaclParam.projectInfoParam);
            }
            if(!$stateParams.type){
                $scope.type=1;
            }else{
                $scope.type = $stateParams.type;
            }
            if($scope.type != 11){
                LocalCache.setObject('jenkinsJobData_formRefer', {});
            }
    		$scope.flag=0;
    		$scope.formRefer = {};
            initTime();
            $scope.getKpiInfo = getKpiInfo;
            $scope.echartsForCurrentVersionBug = echartsForCurrentVersionBug;
            $scope.eChartForOnLineBug = eChartForOnLineBug;
            $scope.echartsForAllVersionBugs = echartsForAllVersionBugs;
            $scope.eChartForKpi = eChartForKpi;
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            getData();

            //设置列表的高度
            function setDivHeight(){
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - 250;
                var clientWidth = document.body.clientWidth;
                var divWidth = clientWidth - 450;
                $("#divTBDis").height(divHeight);
                $("#divTBDis").width(divWidth);
                $("#subDivTBDis").height(divHeight);

                $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
                $("#buttonStyle").css({"width": 100+"px"});
            }

            function timeout(){
                //4表示图表信息全部获取完成
                if(4 === $scope.flag){
                    setTimeout(echartsForCurrentVersionBug,500);
                    setTimeout(eChartForOnLineBug,500);
                    setTimeout(echartsForAllVersionBugs,500);
                    setTimeout(eChartForKpi,500);
                }
            }

            function initTime(){
                //获取当前年
                $scope.formRefer.currentYear = inform.format(new Date(),"yyyy");
                $scope.formRefer.period = '6';
                //当前月
                var currentMonth = inform.format(new Date(),"MM");
                if(currentMonth>6){
                    $scope.formRefer.period = '7';
                }
                $scope.yearList = [$scope.formRefer.currentYear,parseInt($scope.formRefer.currentYear) - 1,parseInt($scope.formRefer.currentYear) - 2];
            }

            //查询当前团队的首页信息
            function getData(){
                var urlData = {
                    'projectName':$scope.projectInfoParam.cname
                }
                //获取当前项目的迭代信息
                teamEntranceDetailsService.selectIterationList(urlData).then(function(data) {
                	if (data.code===AgreeConstant.code) {
                        //迭代版本列表
                        $scope.proProjectList = data.data;
                        if ($scope.proProjectList.length!==0) {
                            //获取当前迭代bug情况
                            $scope.currentVersionBug = $scope.proProjectList[0];
                            $scope.getCurrentVersionBug();
                            //历史迭代bug情况
                            $scope.getHistoryVersionBug();
                            //当前迭代概况
                            $scope.getCurrentInfo();
                        }
                	}
                });
                //获取每月线上bug情况
                getOnlineBug();
                //一年内团队概况
                getOneYearInfo();
                //kpi信息
                getKpiInfo();
                //获取历史迭代bug情况页签内容
                getHistoryBugDetail();
            }
            //获取当前迭代bug情况
            $scope.getCurrentVersionBug = function(){
                var urlData = {
                    'ztProjectId':$scope.currentVersionBug.id,
                    'begin':$scope.currentVersionBug.begin,
                    'end':$scope.currentVersionBug.end,
                    'limit':3
                }
                $scope.currentVersionBugList=[];
                //获取当前迭代版本的每日bug情况
                teamEntranceDetailsService.selectCurrentIterationBug(urlData).then(function(data) {
                	if (data.code===AgreeConstant.code) {
                        if (data.data.length!==0) {
                            $scope.iterationBugInfo = data.data;
                            $scope.currentVersionBugList = data.data.iterationBugPOList;
                        }
                	}
                	$scope.flag++;
                    timeout();
                });
            }
            //历史迭代bug情况
            $scope.getHistoryVersionBug = function(){
                var ids = [];
                var length = $scope.proProjectList.length>8? 8:$scope.proProjectList.length;
                for(var i=0;i<length;i++){
                    ids.push($scope.proProjectList[i].id);
                }
                var urlData = {
                    'ztProjectIds':ids
                }
                $scope.historyVersionBugList=[];
                //获取历史迭代bug情况
                teamEntranceDetailsService.selectHistoryIterationBug(urlData).then(function(data) {
                	if (data.code===AgreeConstant.code) {
                        if (data.data.length!==0) {
                            $scope.historyVersionBugList = data.data;
                        }
                	}
                	$scope.flag++;
                    timeout();
                });
            }
            //当前迭代概况
            $scope.getCurrentInfo = function(){
                var urlData = {
                    'ztProjectId':$scope.currentVersionBug.id
                }
                //当前迭代概况
                teamEntranceDetailsService.selectCurrentIterationInfo(urlData).then(function(data) {
                	if (data.code===AgreeConstant.code) {
                        $scope.currentInfo = data.data;
                	}
                });
            }
            //获取每月线上bug情况
            function getOnlineBug(){
                var urlData = {
                    'projectName':$scope.projectInfoParam.cname,
                    'projectId':$scope.projectInfoParam.id
                }
                $scope.onlineBugList=[];
                teamEntranceDetailsService.getOnlineBug(urlData).then(function(data) {
                	if (data.code===AgreeConstant.code) {
                        if (data.data.length!==0) {
                            $scope.onlineBugList = data.data;
                        }
                	}
                	$scope.flag++;
                    timeout();
                });
            }
            //一年内团队概况
            function getOneYearInfo(){
                var urlData = {
                    'projectName':$scope.projectInfoParam.cname,
                    'projectId':$scope.projectInfoParam.id
                }
                teamEntranceDetailsService.getOneYearInfo(urlData).then(function(data) {
                	if (data.code===AgreeConstant.code) {
                        $scope.oneYearInfo = data.data;
                        var sprintInfoVO = $scope.oneYearInfo.sprintInfoVO;
                        $scope.text="冲刺总数："+sprintInfoVO.totalCount+"\n冲刺完成次数："+sprintInfoVO.completeCount+"\n冲刺进行中次数："+sprintInfoVO.runningCount+"\n冲刺成功次数："+sprintInfoVO.successCount;
                        var releaseInfoVO = $scope.oneYearInfo.releaseInfoVO;
                        $scope.text2="特殊放行数："+releaseInfoVO.specialReleaseCount+"\n特殊放行率："+releaseInfoVO.specialReleaseRate;
                        var peerReviewInfoVO = $scope.oneYearInfo.peerReviewInfoVO;
                        $scope.text3="评审总数："+peerReviewInfoVO.totalCount+"\n通过次数："+peerReviewInfoVO.passCount+"\n有条件通过次数："+peerReviewInfoVO.conditionPassCount;
                        var processConformInfoVO = $scope.oneYearInfo.processConformInfoVO;
                        $scope.text4="总检查项："+processConformInfoVO.totalCheckCount+"\n通过项："+processConformInfoVO.passCheckCount;
                        var onLineBugInfoVO = $scope.oneYearInfo.onLineBugInfoVO;
                        $scope.text5="线上问题总数："+onLineBugInfoVO.totalCount;
                        $scope.text7="reopen数量："+onLineBugInfoVO.reopenCount+"\nreopen比率："+onLineBugInfoVO.reopenRate;
                        var bugInfoVO = $scope.oneYearInfo.bugInfoVO;
                        $scope.text6="reopen数："+bugInfoVO.reopenCount+"\nreopen比率："+bugInfoVO.reopenRate;
                        $scope.warningInfoFlag = Object.keys($scope.oneYearInfo.warningInfo).length;
                	}
                });
            }
            //kpi信息
            function getKpiInfo(flag){
                var urlData = {
                    'projectId':$scope.projectInfoParam.id,
                    'year':$scope.formRefer.currentYear,
                    'quarter':$scope.formRefer.period
                }
                $scope.kpiInfoList=[];
                teamEntranceDetailsService.getKpiInfo(urlData).then(function(data) {
                	if (data.code===AgreeConstant.code) {
                        $scope.kpiInfoList = data.data;
                	}
                	if(flag){
                        eChartForKpi();
                	}else{
                 	   $scope.flag++;
                       timeout();
                	}
                });
            }

            //显示当前迭代版本的bug创建及解决等情况
    		function echartsForCurrentVersionBug(m) {
    		    var titleText = $scope.currentVersionBug.name;
    		    if(m){
    		        titleText = m.projectName+'Bug情况';
    		        if($scope.currentVersionBugList.length>=3){
                        titleText = titleText+" (初期："+$scope.iterationBugInfo.createBugStage1+"|"+$scope.iterationBugInfo.resolvedBugStage1
                        +"；中期："+$scope.iterationBugInfo.createBugStage2+"|"+$scope.iterationBugInfo.resolvedBugStage2
                        +"；末期："+$scope.iterationBugInfo.createBugStage3+"|"+$scope.iterationBugInfo.resolvedBugStage3
                        var createBugs = $scope.iterationBugInfo.createBugStage1+$scope.iterationBugInfo.createBugStage2+$scope.iterationBugInfo.createBugStage3;
                        if(createBugs !== m.bugTotal){
                            titleText = titleText+"；迭代外Bug："+(m.bugTotal-createBugs)
                        }
                        titleText = titleText+")";
                    }
    		        $scope.currentVersionBugDetailCharts = echarts.init(document.getElementById('bugDetailInfoChart'));
    		    }else {
    		        $scope.currentVersionBugDetailCharts = echarts.init(document.getElementById('currentVersionBugDetailChart'));
    		    }

                var xData=[];
                var newBugsSum=[];
                var resolvedBugSum=[];
                var haveBugNum=[];
                var num=0;
                angular.forEach($scope.currentVersionBugList,function (item) {
                    xData.push(item.day);
                    newBugsSum.push(item.newBugsSum);
                    resolvedBugSum.push(-item.resolvedBugSum);
                    num = num+item.newBugsSum*1-item.resolvedBugSum*1;
                    if(num<0){
                        num=0;
                    }
                    haveBugNum.push(num);
                });
                var intervalNum = 0;
                if($scope.currentVersionBugList.length>30){
                    intervalNum = ($scope.currentVersionBugList.length/30).toFixed(0)*1;
                }

                var option = {
                      title:{
                        text:titleText,
                        textStyle:{
                            fontSize: 13,
                            color: '#333'
                        }
                      },
                        legend: {
                            data: ['新增bug数量', '解决bug数量', '存量bug数量'],
                            top:'5%',
                            left: '30%'
                        },
                        tooltip: {},
                        xAxis: {
                            data: xData,
                            name: '日期',
                            axisLine: {onZero: true},
                            splitLine: {show: true},
                            splitArea: {show: false},
                            axisLabel:{
                                interval:intervalNum,
                                rotate:45
                            },
                        },
                        yAxis: {
                             type: 'value',
                             name: '个',
                             max:function(num) {
                                   if(num.max>3){
                                     return num.max;
                                   }
                                    return 3;
                                 },
                             min:function(value) {
                                   if(value.min<-3){
                                     return value.min;
                                   }
                                     return -3;
                                  }
                        },
                        grid: {
                            bottom: 100
                        },
                        series: [
                            {
                                name: '新增bug数量',
                                type: 'bar',
                                stack: 'one',
                                data: newBugsSum,
                                color:"#A52A2A",
                                itemStyle : {
                                        normal: {
                                            label : {
                                                show: true,
                                                formatter: formatterCall
                                            }
                                        }
                                }
                            },
                            {
                                name: '解决bug数量',
                                type: 'bar',
                                stack: 'one',
                                data: resolvedBugSum,
                                itemStyle : {
                                        normal: {
                                            label : {
                                                show: true,
                                                formatter: formatterCall
                                            }
                                        }
                                }
                            },
                            {
                                name: '存量bug数量',
                                type: 'line',
                                stack: 'three',
                                data: haveBugNum,
                                itemStyle : {
                                        normal: {
                                            label : {
                                                show: true,
                                                position: 'left',
                                                formatter: formatterCall
                                            }
                                        }
                                }
                            }
                        ]
                };
                $scope.currentVersionBugDetailCharts.setOption(option, true);
    		}

    		//线上bug情况
            function eChartForOnLineBug(){
                var xData=[];
                var onlineBugNum=[];
                angular.forEach($scope.onlineBugList,function (item) {
				    xData.push(item.happenMonth);
				    onlineBugNum.push(item.bugCount);
				});

                $scope.onlineBugCharts = echarts.init(document.getElementById('onlineBugChart'));
                var option = {
                      title:{
                        text:"每月线上Bug情况",
                        textStyle:{
                            fontSize: 13,
                            color: '#333'
                        }
                      },
/*                        legend: {
                            data: ['线上Bug数量'],
                            left: '40%'
                        },*/
                        tooltip: {},
                        xAxis: {
                            data: xData,
                            name: '日期',
                            axisLine: {onZero: true},
                            splitLine: {show: true},
                            splitArea: {show: false},
                        //    boundaryGap: false,
                            axisLabel:{
                                interval:0,
                                rotate:20
                            },
                        },
                        yAxis: {
                            type: 'value',
                            name: '个',
                            max:function(num) {
                                  if(num.max>5){
                                    return num.max;
                                  }
                                   return 5;
                                },
                        },
                        grid: {
                            bottom: 100
                        },
                        series: [
                            {
                             //   name: '线上Bug数量',
                                type: 'line',
                             //   stack: 'one',
                                data: onlineBugNum,
                                color:"#A52A2A",
                                itemStyle : {
                                        normal: {
                                            label : {
                                                show: true,
                                                formatter: formatterCall
                                            }
                                        }
                                }
                            }
                        ]
                };
                $scope.onlineBugCharts.setOption(option, true);
            }

            //历史迭代版本bug情况
            function echartsForAllVersionBugs(){
                var xData=[];
                var bugTotal=[];
                var reopenTotal=[];
                var bugAvgLifeCycle=[];
                angular.forEach($scope.historyVersionBugList,function (item) {
				    xData.push(item.begin+"-"+item.end);
				    bugTotal.push(item.bugTotal);
				    reopenTotal.push(item.reopenTotal);
				    bugAvgLifeCycle.push(item.bugAvgLifeCycle);
				});

                $scope.allVersionBugsCharts = echarts.init(document.getElementById('allVersionBugsChart'));
                var option = {
                      title:{
                        text:"历史迭代Bug情况",
                        textStyle:{
                            fontSize: 13,
                            color: '#333'
                        }
                      },
                        legend: {
                            data: ['bug总数', 'reopen情况', 'bug平均解决周期'],
                            left: '30%'
                        },
                        tooltip: {},
                        xAxis: {
                            data: xData,
                            name: '日期',
                            axisLine: {onZero: true},
                            splitLine: {show: true},
                            splitArea: {show: false},
                            axisLabel:{
                                interval:0,
                                rotate:30
                            },
                        //    boundaryGap: false,
                        },
                        yAxis: [
                            {
                                type: 'value',
                                name: '个',
                                max:function(value) {
                                      if(value.max>5){
                                        return value.max;
                                      }
                                       return 5;
                                    },
                            },
                            {
                                type: 'value',
                                name: '天',
                                max:function(value) {
                                      if(value.max>3){
                                        return value.max;
                                      }
                                       return 3;
                                    },
                            }
                        ],
                        grid: {
                            bottom: 100
                        },
                        series: [
                            {
                                name: 'bug总数',
                                type: 'line',
                                stack: 'one',
                                data: bugTotal,
                                color:"#A52A2A",
                                itemStyle : {
                                        normal: {
                                            label : {
                                                show: true,
                                                formatter: formatterCall
                                            }
                                        }
                                }
                            },
                            {
                                name: 'reopen情况',
                                type: 'line',
                                stack: 'two',
                                data: reopenTotal,
                                itemStyle : {
                                        normal: {
                                            label : {
                                                show: true,
                                                position: 'bottom',
                                                formatter: formatterCall
                                            }
                                        }
                                }
                            },
                            {
                                name: 'bug平均解决周期',
                                type: 'line',
                                stack: 'three',
                                yAxisIndex: 1,
                                data: bugAvgLifeCycle,
                                itemStyle : {
                                        normal: {
                                            label : {
                                                show: true,
                                                position: 'left',
                                                formatter: formatterCall
                                            }
                                        }
                                }
                            }
                        ]
                };
                $scope.allVersionBugsCharts.setOption(option, true);
            }

            /**
             * 设置饼状图数据-KPI信息
             */
            function eChartForKpi() {
                var dataValue = [0,0,0,0];
                var KPI_1_01 = {subKpiStr:''};
                var KPI_1_02 = {subKpiStr:''};
                var KPI_1_03 = {subKpiStr:''};
                var KPI_1_04 = {subKpiStr:''};
                if(JSON.stringify($scope.kpiInfoList.kpiMap)!==JSON.stringify({})){
                    KPI_1_01 = $scope.kpiInfoList.kpiMap['KPI_1_01'];
                    KPI_1_02 = $scope.kpiInfoList.kpiMap['KPI_1_02'];
                    KPI_1_03 = $scope.kpiInfoList.kpiMap['KPI_1_03'];
                    KPI_1_04 = $scope.kpiInfoList.kpiMap['KPI_1_04'];

                    dataValue=[KPI_1_01.score,KPI_1_02.score,KPI_1_03.score,KPI_1_04.score];
                }

                $scope.kpiCharts = echarts.init(document.getElementById('kpiChart'));

                const source = {
                    data: dataValue,
                    indicator: [
                       { name: '交付能力', max: 100 ,subValue:KPI_1_01.subKpiStr,weight:KPI_1_01.weight},
                      { name: '交付质量', max: 100 ,subValue:KPI_1_02.subKpiStr,weight:KPI_1_02.weight},
                      { name: '交付过程质量', max: 100 ,subValue:KPI_1_03.subKpiStr,weight:KPI_1_03.weight},
                      { name: '部门贡献', max: 100 ,subValue:KPI_1_04.subKpiStr,weight:KPI_1_04.weight}
                    ]
                }
                const buildSeries = function(data){
                    const helper = data.map((item, index) => {
                        const arr = ['','','',''];
                        arr.splice(index, 1, item);
                        return arr;
                    })

                    return [data, ...helper].map((item, index) => {
                        return {
                            type: 'radar',
                            symbol: index === 0 ? 'circle' : 'none',
                            symbolSize: 6,
                        color:'blue',
                                              areaStyle: { // 单项区域填充样式
                                                  normal: {
                                                      color: 'rgba(100,149,237,0.6)' // 填充的颜色。[ default: "#000" ]
                                                  }
                                              },
                            label: { // 单个拐点文本的样式设置
                                normal: {
                                    show: true,// 单个拐点文本的样式设置。[ default: false ]
                                    position: 'right',// 标签的位置。[ default: top ]
                                    distance: 5,// 距离图形元素的距离。当 position 为字符描述值（如 'top'、'insideRight'）时候有效。[ default: 5 ]
                                    color: 'blue',// 文字的颜色。如果设置为 'auto'，则为视觉映射得到的颜色，如系列色。[ default: "#fff" ]
                                    fontSize: 14,// 文字的字体大小
                                    formatter: function(params) {
                                        return params.value;
                                    }
                                }
                            },
                            tooltip: {
                                show: index === 0 ? false : true,
                                formatter: function() {
                                    return source.indicator[index - 1].name + '(权重=' + source.indicator[index - 1].weight+'%;分数='+source.data[index - 1]+")<br>"
                                        +"<font size= 1>"+source.indicator[index - 1].subValue+"</font>";
                                }
                            },
                            z: index === 0 ? 1 : 2,
                            data: [item]
                        }
                    })
                }

                $scope.payOption = {
                    tooltip: {},
                    radar: {
                      center:["50%","43%"],
                      radius:"65%",
                      indicator: source.indicator,
                      name: {
                          textStyle: {
                              padding: [-10, -12]  // 控制文字padding
                          }
                      }
                    },
                    series: buildSeries(source.data)
                };

               $scope.kpiCharts.setOption($scope.payOption, true);
           }

             /**
             * 用formatter回调函数自定义显示多项数据内容
                 **/
            function formatterCall (params, ticket, callback) {
                if (params.value !== 0) {
                    return params.value;
                } else {
                    return '';
                }
            }

           /**
            * 当窗体大小变化时，修改图例大小
            */
           window.addEventListener("resize", function () {
               if ($scope.currentVersionBugDetailCharts) { $scope.currentVersionBugDetailCharts.resize(); }
               if ($scope.onlineBugCharts) { $scope.onlineBugCharts.resize(); }
               if ($scope.allVersionBugsCharts) { $scope.allVersionBugsCharts.resize(); }
               if ($scope.kpiCharts) { $scope.kpiCharts.resize(); }
           });

           $scope.getBugCreateDetail = function(m){
               var urlData = {
                   'ztProjectId':m.project,
                   'begin':m.begin,
                   'end':m.end,
                   'limit':3
               }
               $scope.currentVersionBugList=[];
               //获取当前迭代版本的每日bug情况
               teamEntranceDetailsService.selectCurrentIterationBug(urlData).then(function(data) {
                   if (data.code===AgreeConstant.code) {
                       if (data.data.length!==0) {
                           $scope.iterationBugInfo = data.data;
                           $scope.currentVersionBugList = data.data.iterationBugPOList;
                       }
                   }
                   echartsForCurrentVersionBug(m);
               });
           }

            //返回团队看板数据
            $scope.gobackTeamDataBoard = function(){
                $state.go("app.office.teamEntrance");
            };

           function getHistoryBugDetail(){
                var urlData = {
                    'projectName':$scope.projectInfoParam.cname
                }
                $scope.historyBugDetail;
                $scope.historyBugDetailList = [];
                teamEntranceDetailsService.getHistoryBugDetail(urlData).then(function(data) {
                	if (data.code===AgreeConstant.code) {
                	    $scope.historyBugDetail = data.data;
                        $scope.historyBugDetailList = data.data.versions.reverse();
                	}
                });
           }

           $scope.warningOver= function(type){
               const tipContentDiv = $('#tipContent');
               type ? tipContentDiv.css('display','block') : tipContentDiv.css('display','none');
           }

      }]);
})();
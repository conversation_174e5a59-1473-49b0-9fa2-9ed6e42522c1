
/**
 * 测试质量数据统计配置
 */
var reportListConfig = {
		//测试用例覆盖率_需求通过率
    	'storyPassRate':{
    		//调用哪个类
    		'className':'com.snbc.office.dao.pro.testReportProduct.TestCaseMapper',
    		//下载文件名
    		'name':'测试用例覆盖率_需求通过率',
    		//sheet页名称
    		'detailName':['测试用例覆盖率_需求通过率','测试用例覆盖率_需求通过率项目明细','测试用例覆盖率_需求通过率模块明细'],
    		//对应哪个前端查询子页面
    		'htmlFile':'tpl/office/testReportProduct/testCase/storyPassRateManagement.html',
    		//调用哪些方法：一个sheet一个二维数组；一个方法一个统计表格；一个sheet中可多个方法
    		'functionName':[["{'functionName':'storyPassRateProduct'}"]
    						,["{'functionName':'storyPassRateProject'}"]
    						,["{'functionName':'storyPassRateModule'}"]],
    		//占用几个单元格，与表头相对应
    		'mergedRegion':[[1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1]],
    		//汇总页面每个表格的表头，与getData方法相对应
    		'headMap':[['产品线','产品名称','软件需求总数','已覆盖用例软件需求数','零用例软件需求数','测试用例总数','已测试通过软件需求数','测试用例覆盖率','软件需求通过率']],
    		//查询方法
    		'getData':["{'functionName':'storyPassRateProduct'}"],
    		//明细页面每个表格的表头，与subGetData方法相对应
    		'subHeadMap':[
    		           ['产品线','所属产品','执行名称','执行当前状态','软件需求总数','已覆盖用例软件需求数','零用例软件需求数','测试用例总数','已测试通过软件需求数','测试用例覆盖率','软件需求通过率'],
    		           ['产品线','所属产品','模块名称','模块路径','软件需求总数','已覆盖用例软件需求数','零用例软件需求数','测试用例总数','已测试通过软件需求数','测试用例覆盖率','软件需求通过率']
    		           ],
    		 //查询方法 明细，一个方法表示一个表格
    		 'subGetData':["{'functionName':'storyPassRateProject'}","{'functionName':'storyPassRateModule'}"]
    	},
    	//缺陷逃逸
    	'leakRate':{
    		//调用哪个类
    		'className':'com.snbc.office.dao.office.testReportProduct.LeakMapper',
    		//下载文件名
    		'name':'缺陷逃逸',
    		//sheet页名称
    		'detailName':['缺陷逃逸','缺陷逃逸项目明细'],//,'缺陷逃逸模块明细'
    		//对应哪个前端查询子页面
    		'htmlFile':'tpl/office/testReportProduct/leak/leakRateManagement.html',
    		//调用哪些方法：一个sheet一个二维数组；一个方法一个统计表格；一个sheet中可多个方法
    		'functionName':[["{'functionName':'leakRateProduct'}"]
    						,["{'functionName':'leakRateProject'}"]],//['leakRateModule']
    		//占用几个单元格，与表头相对应
    		'mergedRegion':[[1,1,1,1,1,1],[1,1,1,1,1,1,1,1]],//,[1,1,1,1,1,1,1,1]
    		//汇总页面每个表格的表头，与getData方法相对应
    		'headMap':[['产品线','产品名称','测试用例总数','缺陷数','线上问题发现数量','缺陷逃逸']],
    		//查询方法 汇总页面查询方法
    		'getData':["{'functionName':'leakRateProduct'}"],
    		//明细页面每个表格的表头，与subGetData方法相对应
    		'subHeadMap':[
    		           ['产品线','所属产品','项目名称','项目当前状态','测试用例总数','缺陷数','线上问题发现数量','缺陷逃逸']
    		           //,['产品线','所属产品','模块名称','模块路径','测试用例总数','缺陷数','线上问题发现数量','缺陷逃逸']
    		           ],
    		 //查询方法 明细，一个方法表示一个表格
    		 'subGetData':["{'functionName':'leakRateProject'}"]//,'leakRateModule'
    	},
    	//二次故障率_缺陷修复率
    	'reopenRate':{
    		//调用哪个类
    		'className':'com.snbc.office.dao.pro.testReportProduct.BugOrReopenMapper',
    		//下载文件名
    		'name':'二次故障率_缺陷修复率',
    		//sheet页名称
    		'detailName':['二次故障率_缺陷修复率','二次故障率_缺陷修复率项目明细','二次故障率_缺陷修复率模块明细'],
    		//对应哪个前端查询子页面
    		'htmlFile':'tpl/office/testReportProduct/bugOrReopen/reopenRateManagement.html',
    		//调用哪些方法：一个sheet一个二维数组；一个方法一个统计表格；一个sheet中可多个方法
    		'functionName':[["{'functionName':'reopenRateProduct'}"]
    						,["{'functionName':'reopenRateProject'}"]
    						,["{'functionName':'reopenRateModule'}"]],
    		//占用几个单元格，与表头相对应
    		'mergedRegion':[[1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1]],
    		//汇总页面每个表格的表头，与getData方法相对应
    		'headMap':[['产品线','产品名称','软件需求总数','完成软件需求数','测试用例数','Bug数','Reopen缺陷数','严重Bug数','关闭Bug数','二次故障率','严重故障率','缺陷修复率']],
    		//查询方法 汇总页面查询方法
    		'getData':["{'functionName':'reopenRateProduct'}"],
    		//明细页面每个表格的表头，与subGetData方法相对应
    		'subHeadMap':[
    		           ['产品线','所属产品','执行名称','执行当前状态','软件需求总数','完成软件需求数','测试用例数','Bug数','Reopen缺陷数','严重Bug数','关闭Bug数','二次故障率','严重故障率','缺陷修复率'],
    		           ['产品线','所属产品','模块名称','模块路径','软件需求总数','完成软件需求数','测试用例数','Bug数','Reopen缺陷数','严重Bug数','关闭Bug数','二次故障率','严重故障率','缺陷修复率']
    		           ],
    		 //查询方法 明细，一个方法表示一个表格
    		 'subGetData':["{'functionName':'reopenRateProject'}","{'functionName':'reopenRateModule'}"]
    	},
    	//缺陷生存周期
    	'bugCycle':{
    		//调用哪个类
    		'className':'com.snbc.office.dao.pro.testReportProduct.BugOrReopenMapper',
    		//下载文件名
    		'name':'缺陷生存周期',
    		//sheet页名称
    		'detailName':['缺陷生存周期','缺陷生存周期项目明细','缺陷生存周期模块明细'],
    		//对应哪个前端查询子页面
    		'htmlFile':'tpl/office/testReportProduct/bugOrReopen/bugCycleManagement.html',
    		//调用哪些方法：一个sheet一个二维数组；一个方法一个统计表格；一个sheet中可多个方法
    		'functionName':[["{'functionName':'bugCycleProduct'}"]
    						,["{'functionName':'bugCycleProject'}"]
    						,["{'functionName':'bugCycleModule'}"]],
    		//占用几个单元格，与表头相对应
    		'mergedRegion':[[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]],
    		//汇总页面每个表格的表头，与getData方法相对应
    		'headMap':[['产品线','产品名称','软件需求总数','完成软件需求数','测试用例数','Bug总数','激活bug数量','已解决bug数量','已关闭bug数量','≤2天','3~7天','1~2周','2周~1月','1月以上','异常数据','已关闭缺陷修复平均时长','已关闭缺陷关闭平均时长','已关闭缺陷生存周期']],
    		//查询方法 汇总页面查询方法
    		'getData':["{'functionName':'bugCycleProduct'}"],
    		//明细页面每个表格的表头，与subGetData方法相对应
    		'subHeadMap':[
    		           ['产品线','所属产品','执行名称','执行当前状态','软件需求总数','完成软件需求数','测试用例数','Bug总数','激活bug数量','已解决bug数量','已关闭bug数量','≤2天','3~7天','1~2周','2周~1月','1月以上','异常数据','已关闭缺陷修复平均时长','已关闭缺陷关闭平均时长','已关闭缺陷生存周期'],
    		           ['产品线','所属产品','模块名称','模块路径','软件需求总数','完成软件需求数','测试用例数','Bug总数','激活bug数量','已解决bug数量','已关闭bug数量','≤2天','3~7天','1~2周','2周~1月','1月以上','异常数据','已关闭缺陷修复平均时长','已关闭缺陷关闭平均时长','已关闭缺陷生存周期']
    		           ],
    		 //查询方法 明细，一个方法表示一个表格
    		 'subGetData':["{'functionName':'bugCycleProject'}","{'functionName':'bugCycleModule'}"]
    	},
    	//Bug严重程度等级统计
    	'bugSeverity':{
    		//调用哪个类
    		'className':'com.snbc.office.dao.pro.testReportProduct.BugOrReopenMapper',
    		//下载文件名
    		'name':'Bug严重程度等级统计',
    		//sheet页名称
    		'detailName':['Bug严重程度等级统计','Bug严重程度等级统计项目明细','Bug严重程度等级统计模块明细'],
    		//对应哪个前端查询子页面
    		'htmlFile':'tpl/office/testReportProduct/bugOrReopen/bugSeverityManagement.html',
    		//调用哪些方法：一个sheet一个二维数组；一个方法一个统计表格；一个sheet中可多个方法
    		'functionName':[["{'functionName':'bugSeverityProduct'}"]
    						,["{'functionName':'bugSeverityProject'}"]
    						,["{'functionName':'bugSeverityModule'}"]],
    		//占用几个单元格，与表头相对应
    		'mergedRegion':[[1,1,1,1,1,1],[1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1]],
    		//汇总页面每个表格的表头，与getData方法相对应
    		'headMap':[['产品线','产品名称','致命的','严重的','一般的','建议的']],
    		//查询方法 汇总页面查询方法
    		'getData':["{'functionName':'bugSeverityProduct'}"],
    		//明细页面每个表格的表头，与subGetData方法相对应
    		'subHeadMap':[
    		           ['产品线','所属产品','执行名称','执行当前状态','致命的','严重的','一般的','建议的'],
    		           ['产品线','所属产品','模块名称','模块路径','致命的','严重的','一般的','建议的']
    		           ],
    		 //查询方法 明细，一个方法表示一个表格
    		 'subGetData':["{'functionName':'bugSeverityProject'}","{'functionName':'bugSeverityModule'}"]
    	},
    	//bug解决方案统计
    	'bugResolution':{
    		//调用哪个类
    		'className':'com.snbc.office.dao.pro.testReportProduct.BugOrReopenMapper',
    		//下载文件名
    		'name':'Bug解决方案统计',
    		//sheet页名称
    		'detailName':['Bug解决方案统计','Bug解决方案统计项目明细','Bug解决方案统计模块明细'],
    		//对应哪个前端查询子页面
    		'htmlFile':'tpl/office/testReportProduct/bugOrReopen/bugResolutionManagement.html',
    		//调用哪些方法：一个sheet一个二维数组；一个方法一个统计表格；一个sheet中可多个方法
    		'functionName':[["{'functionName':'bugResolutionProduct'}"]
    						,["{'functionName':'bugResolutionProject'}"]
    						,["{'functionName':'bugResolutionModule'}"]],
    		//占用几个单元格，与表头相对应
    		'mergedRegion':[[1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1]],
    		//汇总页面每个表格的表头，与getData方法相对应
    		'headMap':[['产品线','产品名称','空','设计如此','重复Bug','外部原因','已解决','无法重现','延期处理','不与解决','转为需求','Bug描述不清']],
    		//查询方法 汇总页面查询方法
    		'getData':["{'functionName':'bugResolutionProduct'}"],
    		//明细页面每个表格的表头，与subGetData方法相对应
    		'subHeadMap':[
    		           ['产品线','所属产品','执行名称','执行当前状态','空','设计如此','重复Bug','外部原因','已解决','无法重现','延期处理','不与解决','转为需求','Bug描述不清'],
    		           ['产品线','所属产品','模块名称','模块路径','空','设计如此','重复Bug','外部原因','已解决','无法重现','延期处理','不与解决','转为需求','Bug描述不清']
    		           ],
    		 //查询方法 明细，一个方法表示一个表格
    		 'subGetData':["{'functionName':'bugResolutionProject'}","{'functionName':'bugResolutionModule'}"]
    	},
    	//用例执行覆盖率
    	'caseExecuteRate':{
    		//调用哪个类
    		'className':'com.snbc.office.dao.pro.testReportProduct.TestCaseMapper',
    		//下载文件名
    		'name':'用例执行覆盖率',
    		//sheet页名称
    		'detailName':['用例执行覆盖率','用例执行覆盖率项目明细','用例执行覆盖率模块明细'],
    		//对应哪个前端查询子页面
    		'htmlFile':'tpl/office/testReportProduct/testCase/caseExecuteRateManagement.html',
    		//调用哪些方法：一个sheet一个二维数组；一个方法一个统计表格；一个sheet中可多个方法
    		'functionName':[["{'functionName':'caseExecuteRateProduct'}"]
    						,["{'functionName':'caseExecuteRateProject'}"]
    						,["{'functionName':'caseExecuteRateModule'}"]],
    		//占用几个单元格，与表头相对应
    		'mergedRegion':[[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]],
    		//汇总页面每个表格的表头，与getData方法相对应
    		'headMap':[['产品线','产品名称','软件需求总数','测试用例总数','已执行的测试用例总数','未执行的测试用例总数','执行通过的测试用例总数','测试用例总执行数','通过的执行数','失败的执行数','阻塞数','缺陷数','测试用例通过率','测试用例执行率','测试用例命中率']],
    		//查询方法 汇总页面查询方法
    		'getData':["{'functionName':'caseExecuteRateProduct'}"],
    		//明细页面每个表格的表头，与subGetData方法相对应
    		'subHeadMap':[
    		           ['产品线','所属产品','执行名称','项目当前状态','软件需求总数','测试用例总数','已执行的测试用例总数','未执行的测试用例总数','执行通过的测试用例总数','测试用例总执行数','通过的执行数','失败的执行数','阻塞数','缺陷数','测试用例通过率','测试用例执行率','测试用例命中率'],
    		           ['产品线','所属产品','模块名称','模块路径','软件需求总数','测试用例总数','已执行的测试用例总数','未执行的测试用例总数','执行通过的测试用例总数','测试用例总执行数','通过的执行数','失败的执行数','阻塞数','缺陷数','测试用例通过率','测试用例执行率','测试用例命中率']
    		           ],
    		 //查询方法 明细，一个方法表示一个表格
    		 'subGetData':["{'functionName':'caseExecuteRateProject'}","{'functionName':'caseExecuteRateModule'}"]
    	},
    	//bug类型统计
    	'bugType':{
    		//调用哪个类
    		'className':'com.snbc.office.dao.pro.testReportProduct.BugOrReopenMapper',
    		//下载文件名
    		'name':'Bug类型统计',
    		//sheet页名称
    		'detailName':['Bug类型统计','Bug类型统计项目明细','Bug类型统计模块明细'],
    		//对应哪个前端查询子页面
    		'htmlFile':'tpl/office/testReportProduct/bugOrReopen/bugTypeManagement.html',
    		//调用哪些方法：一个sheet一个二维数组；一个方法一个统计表格；一个sheet中可多个方法
    		'functionName':[["{'functionName':'bugTypeProduct'}"]
    						,["{'functionName':'bugTypeProject'}"]
    						,["{'functionName':'bugTypeModule'}"]],
    		//占用几个单元格，与表头相对应
    		'mergedRegion':[[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]],
    		//汇总页面每个表格的表头，与getData方法相对应
    		'headMap':[['产品线','产品名称','空','需求不明确','需求有争议','需求缺失','需求变更','需求错误','设计未进行','设计缺陷','界面问题','数据问题','功能问题','改动波及','安装部署问题','性能问题','文档问题','兼容性问题','日志问题','升级问题','安全问题','配置问题','接口问题','用例缺陷','操作问题']],
    		//查询方法 汇总页面查询方法
    		'getData':["{'functionName':'bugTypeProduct'}"],
    		//明细页面每个表格的表头，与subGetData方法相对应
    		'subHeadMap':[
    		           ['产品线','所属产品','执行名称','执行当前状态','空','需求不明确','需求有争议','需求缺失','需求变更','需求错误','设计未进行','设计缺陷','界面问题','数据问题','功能问题','改动波及','安装部署问题','性能问题','文档问题','兼容性问题','日志问题','升级问题','安全问题','配置问题','接口问题','用例缺陷','操作问题'],
    		           ['产品线','所属产品','模块名称','模块路径','空','需求不明确','需求有争议','需求缺失','需求变更','需求错误','设计未进行','设计缺陷','界面问题','数据问题','功能问题','改动波及','安装部署问题','性能问题','文档问题','兼容性问题','日志问题','升级问题','安全问题','配置问题','接口问题','用例缺陷','操作问题']
    		           ],
    		 //查询方法 明细，一个方法表示一个表格
    		 'subGetData':["{'functionName':'bugTypeProject'}","{'functionName':'bugTypeModule'}"]
    	},
    	//测试用例类型统计
    	'caseType':{
    		//调用哪个类
    		'className':'com.snbc.office.dao.pro.testReportProduct.TestCaseMapper',
    		//下载文件名
    		'name':'测试用例类型统计',
    		//sheet页名称
    		'detailName':['测试用例类型统计','测试用例类型统计项目明细','测试用例类型统计模块明细'],
    		//对应哪个前端查询子页面
    		'htmlFile':'tpl/office/testReportProduct/testCase/caseTypeManagement.html',
    		//调用哪些方法：一个sheet一个二维数组；一个方法一个统计表格；一个sheet中可多个方法
    		'functionName':[["{'functionName':'caseTypeProduct'}"]
    						,["{'functionName':'caseTypeProject'}"]
    						,["{'functionName':'caseTypeModule'}"]],
    		//占用几个单元格，与表头相对应
    		'mergedRegion':[[1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1]],
    		//汇总页面每个表格的表头，与getData方法相对应
    		'headMap':[['产品线','产品名称','功能测试','性能测试','配置相关','安装部署','安全相关','接口测试','其他']],
    		//查询方法 汇总页面查询方法
    		'getData':["{'functionName':'caseTypeProduct'}"],
    		//明细页面每个表格的表头，与subGetData方法相对应
    		'subHeadMap':[
    		           ['产品线','所属产品','执行名称','项目当前状态','功能测试','性能测试','配置相关','安装部署','安全相关','接口测试','其他'],
    		           ['产品线','所属产品','模块名称','模块路径','功能测试','性能测试','配置相关','安装部署','安全相关','接口测试','其他']
    		           ],
    		 //查询方法 明细，一个方法表示一个表格
    		 'subGetData':["{'functionName':'caseTypeProject'}","{'functionName':'caseTypeModule'}"]
    	},
    	//测试用例能手
    	'caseDevote':{
    		//调用哪个类
    		'className':'com.snbc.office.dao.pro.testReportProduct.TestCaseMapper',
    		//下载文件名
    		'name':'测试用例能手',
    		//sheet页名称
    		'detailName':['测试用例能手','测试用例能手个人明细'],
    		//对应哪个前端查询子页面
    		'htmlFile':'tpl/office/testReportProduct/testCase/caseDevoteManagement.html',
    		//调用哪些方法：一个sheet一个二维数组；一个方法一个统计表格；一个sheet中可多个方法
    		'functionName':[["{'functionName':'caseDevote','sortName':'总计','sortRule':'gradeDown'}"]
    						,["{'functionName':'caseDevotePersonal'}"]],
    		//占用几个单元格，与表头相对应
    		'mergedRegion':[[1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1]],
    		//汇总页面每个表格的表头，与getData方法相对应
    		'headMap':[['姓名','所属部门','功能测试用例数量','性能测试用例数量','配置相关用例数量','安装部署用例数量','安全相关用例数量','接口测试用例数量','其他用例数量','总计']],
    		//查询方法 汇总页面查询方法
    		'getData':["{'functionName':'caseDevote','sortName':'总计','sortRule':'gradeDown'}"],
    		//明细页面每个表格的表头，与subGetData方法相对应
    		'subHeadMap':[
    		           ['产品线','姓名','参与产品名称','参与执行名称','执行当前状态','功能测试用例数量','性能测试用例数量','配置相关用例数量','安装部署用例数量','安全相关用例数量','接口测试用例数量','其他用例数量','总计']
    		           ],
    		 //查询方法 明细，一个方法表示一个表格
    		 'subGetData':["{'functionName':'caseDevotePersonal','sortName':'总计','sortRule':'gradeDown'}"],
    		 'calculateSum':[['总计','功能测试用例数量','性能测试用例数量','配置相关用例数量','安装部署用例数量','安全相关用例数量','接口测试用例数量','其他用例数量'],[]]
    	},
    	//bug发现能手
    	'bugFound':{
    		//调用哪个类
    		'className':'com.snbc.office.dao.pro.testReportProduct.BugOrReopenMapper',
    		//下载文件名
    		'name':'bug发现能手',
    		//sheet页名称
    		'detailName':['bug发现能手','bug发现能手个人明细'],
    		//对应哪个前端查询子页面
    		'htmlFile':'tpl/office/testReportProduct/bugOrReopen/bugFoundManagement.html',
    		//调用哪些方法：一个sheet一个二维数组；一个方法一个统计表格；一个sheet中可多个方法
    		'functionName':[["{'functionName':'bugFound','sortName':'总计','sortRule':'gradeDown'}"]
    						,["{'functionName':'bugFoundPersonal'}"]],
    		//占用几个单元格，与表头相对应
    		'mergedRegion':[[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]],
    		//汇总页面每个表格的表头，与getData方法相对应
    		'headMap':[['姓名','所属部门','未解决','空','设计如此','重复Bug','外部原因','已解决','无法重现','延期处理','不予解决','转为需求','Bug描述不清','有效率','总计']],
    		//查询方法 汇总页面查询方法
    		'getData':["{'functionName':'bugFound','sortName':'总计','sortRule':'gradeDown'}"],
    		//明细页面每个表格的表头，与subGetData方法相对应
    		'subHeadMap':[
    		           ['产品线','姓名','参与产品名称','参与执行名称','执行当前状态','未解决','空','设计如此','重复Bug','外部原因','已解决','无法重现','延期处理','不予解决','转为需求','Bug描述不清','有效率','总计']
    		           ],
    		 //查询方法 明细，一个方法表示一个表格
    		 'subGetData':["{'functionName':'bugFoundPersonal','sortName':'总计','sortRule':'gradeDown'}"],
    		 //计算总计相关，二维数组[0]字母列需要显示的总计，第一个字符串表示表头名；
    		 //[1]为数字行需要显示的总计，第一个字符串表示字段名，第二个为需要将"总计"写入的列名；
    		 'calculateSum':[['总计','未解决','空','设计如此','重复Bug','外部原因','已解决','无法重现','延期处理','不予解决','转为需求','Bug描述不清'],[]],
 			 //计算比率相关，需要计算几个比率就在二维数组中添加几个数组；
    		 //第一个字段为列名，第二个字段为分子(多个以|分隔，相加得出分子),第三个字段为分母(多个以|分隔，相加得出分母)
    		 'calculateRate':[['有效率','未解决|空|重复Bug|外部原因|已解决|无法重现|延期处理|不予解决|转为需求|Bug描述不清','总计']]
    	},
    	//Bug贡献能手
    	'bugDevote':{
    		//调用哪个类
    		'className':'com.snbc.office.dao.pro.testReportProduct.BugOrReopenMapper',
    		//下载文件名
    		'name':'Bug贡献能手',
    		//sheet页名称
    		'detailName':['Bug贡献能手','Bug贡献能手个人明细'],
    		//对应哪个前端查询子页面
    		'htmlFile':'tpl/office/testReportProduct/bugOrReopen/bugDevoteManagement.html',
    		//调用哪些方法：一个sheet一个二维数组；一个方法一个统计表格；一个sheet中可多个方法
    		'functionName':[["{'functionName':'bugDevote','sortName':'总计','sortRule':'gradeDown'}"]
    						,["{'functionName':'bugDevotePersonal'}"]],
    		//占用几个单元格，与表头相对应
    		'mergedRegion':[[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]],
    		//汇总页面每个表格的表头，与getData方法相对应
    		'headMap':[['姓名','所属部门','空','设计如此','重复Bug','外部原因','已解决','无法重现','延期处理','不予解决','转为需求','Bug描述不清','解决率','总计']],
    		//查询方法 汇总页面查询方法
    		'getData':["{'functionName':'bugDevote','sortName':'总计','sortRule':'gradeDown'}"],
    		//明细页面每个表格的表头，与subGetData方法相对应
    		'subHeadMap':[
    		           ['产品线','姓名','参与产品名称','参与执行名称','执行当前状态','空','设计如此','重复Bug','外部原因','已解决','无法重现','延期处理','不予解决','转为需求','Bug描述不清','解决率','总计']
    		           ],
    		 //查询方法 明细，一个方法表示一个表格
    		 'subGetData':["{'functionName':'bugDevotePersonal','sortName':'总计','sortRule':'gradeDown'}"],
    		 'calculateSum':[['总计','空','设计如此','重复Bug','外部原因','已解决','无法重现','延期处理','不予解决','转为需求','Bug描述不清'],[]],
 			 'calculateRate':[['解决率','外部原因|已解决|重复Bug','空|重复Bug|外部原因|已解决|无法重现|延期处理|不予解决']]
    	},
    	//提测质量
    	'testTaskQuality':{
    		//调用哪个类
    		'className':'com.snbc.office.dao.pro.testReportProduct.TestTaskMapper',
    		//下载文件名
    		'name':'提测质量',
    		//sheet页名称
    		'detailName':['提测质量','提测质量项目明细'],//,'提测质量模块明细'
    		//对应哪个前端查询子页面
    		'htmlFile':'tpl/office/testReportProduct/testTaskQuality/testTaskQualityManagement.html',
    		//调用哪些方法：一个sheet一个二维数组；一个方法一个统计表格；一个sheet中可多个方法
    		'functionName':[["{'functionName':'testTaskQualityProduct'}"]
    						,["{'functionName':'testTaskQualityProject'}"]],//,['testTaskQualityModule']
    		//占用几个单元格，与表头相对应
    		'mergedRegion':[[1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1]],//,[1,1,1,1,1,1,1,1,1,1]
    		//汇总页面每个表格的表头，与getData方法相对应
    		'headMap':[['产品线','产品名称','待测测试单','测试中测试单','被阻塞测试单','已完成测试单','阻塞质量占比','总计']],
    		//查询方法 汇总页面查询方法
    		'getData':["{'functionName':'testTaskQualityProduct'}"],
    		//明细页面每个表格的表头，与subGetData方法相对应
    		'subHeadMap':[
    		           ['产品线','所属产品','项目名称','项目当前状态','待测测试单','测试中测试单','被阻塞测试单','已完成测试单','阻塞质量占比','总计']
    		           //,['产品线','所属产品','模块名称','模块路径','待测测试单','测试中测试单','被阻塞测试单','已完成测试单','阻塞质量占比','总计']
    		           ],
    		 //查询方法 明细，一个方法表示一个表格
    		 'subGetData':["{'functionName':'testTaskQualityProject'}"],//,'testTaskQualityModule'
    		 'calculateSum':[['总计','待测测试单','测试中测试单','被阻塞测试单','已完成测试单'],[]]
    	}
    };

(function () {
	app.controller("customerManagementUpdate", ['comService', '$rootScope', '$scope', 'customerManagementService','customerModule', '$stateParams', 'inform', 'Trans', '$modal','AgreeConstant','LocalCache', '$state',
	function (comService, $rootScope, $scope, customerManagementService,customerModule, $stateParams, inform, Trans, $modal,AgreeConstant, LocalCache,$state) {
		/**
   * *************************************************************
   *             初始化部分                                 开始
   * *************************************************************
   */
		// 正则校验配置
		$scope.limitList = AgreeConstant.limitList;
		//增加还是修改0：增加，1：修改
		$scope.isAdd = $stateParams.isAdd;
        //状态下拉框数
		$scope.customerStatusSelect = [{
			value: '0',
			label: '启用'
		}, {
			value: '1',
			label: '归档'
		}];
        $scope.param = JSON.parse($stateParams.customerInfoParam);
        if($scope.isAdd === '0'){
            $scope.param.customerStatus = '0';
        }
        setCheckBosValue();
        //行业
        $scope.professionList = ['金融','物流','新零售','新兴'];
		//设置列表的高度
		setDivHeight();
		$(window).resize(setDivHeight); //窗体大小变化时重新计算高度
		/**
   * *************************************************************
   *              初始化部分                                 结束
   * *************************************************************
   */

		/**
   * *************************************************************
   *              方法声明部分                                开始
   * *************************************************************
   */

		/**
    	 * 设置列表的高度
    	 */
		function setDivHeight() {
			//网页可见区域高度
			var clientHeight = document.body.clientHeight;
			var divHeight = clientHeight - (150 + 185);
			$("#divTBDis").height(divHeight);
			$("#subDivTBDis").height(divHeight - 50);
			var clientWidth = document.body.clientWidth;
			$("#buttonStyle").css(inform.getButtonStyle(clientHeight, clientWidth));
		}

        //初始化checkbox的值
         function setCheckBosValue() {
            setTimeout(setData,2000);
        }
        function setData() {
                if($scope.isAdd==="0"){
                     return;
                }
                var boxes = document.getElementsByName("profession");
                var dataList = $scope.param.profession.split(",");
            	//获取页面所有产品名称
            	for(var i=0;i<boxes.length;i++){
            	    boxes[i].checked = false;
            		//现有的产品名称
            		for(var j=0;j<dataList.length;j++){
            			if (boxes[i].value === dataList[j]){
            				boxes[i].checked = true;
            			}
            		}
            	}
        }
        //获取选中的行业
        function getProfessionsData() {
                var boxes = document.getElementsByName("profession");
                $scope.professions="";
            	//获取页面所有产品名称
            	for(var i=0;i<boxes.length;i++){
            	    if(boxes[i].checked){
                        $scope.professions = $scope.professions+boxes[i].value+",";
            	    }
            	}
            	$scope.professions = $scope.professions.substring(0,$scope.professions.length-1);
        }
		/**
       * 修改信息
       */
		$scope.updateInfo  = function () {
                getProfessionsData();
				var urlData = {
				    'customerId': $scope.param.customerId,//客户编号
				    'customerName': $scope.param.customerName,//客户名称
				    'customerNameOld': $scope.param.customerNameOld,//客户名称
					'profession': $scope.professions,//行业
					'customerStatus':$scope.param.customerStatus,//状态
                    'note': $scope.param.note //备注

				};
                if($scope.isAdd==="0"){
                    customerManagementService.addCustomerInfo(urlData).then(function (data) {
                        judgeData(data);
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
                }else {
                    customerManagementService.updateCustomerInfo(urlData).then(function (data) {
                        judgeData(data);
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
                }
		};
		function judgeData(data){
            if (data.code === AgreeConstant.code) {
                inform.common(data.message);
                $scope.goback();
            } else {
                //弹框提示
                inform.common(data.message);
            }
		}
		/**
   * 返回客户信息
   */
		$scope.goback = function () {
			$state.go('app.office.customerManagement');
		};

        $scope.initModule = function (){
            var data={
                'keyWord':$scope.param.customerName
            };
            customerModule.initModule(data,$scope,setCustomerInfo);
        }
        /**
         * 根据所选中的客户回填信息
         */
        function setCustomerInfo(data){
            $scope.param.customerName = data.name;
            $scope.param.customerId = data.id;
        }
		/**
     * *************************************************************
     *              方法声明部分                                结束
     * *************************************************************
     */
	}]);
})();

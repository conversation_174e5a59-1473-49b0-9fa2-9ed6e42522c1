(function () {
    'use strict';
    app.factory('costInputService', costInputService);
    costInputService.$inject = ["HttpService", '$rootScope'];

    function costInputService(HttpService, $rootScope) {
        var service = {
            getCostWeekTitle: getCostWeekTitle,
            getProjectcostWeek: getProjectcostWeek,
            getProjectcostFee: getProjectcostFee,
            getHoursTypeByTimePeriod: getHoursTypeByTimePeriod,
            getProjectcostHrDetail: getProjectcostHrDetail,
            updateFeeCost: updateFeeCost,
            updateHrCost: updateHrCost,
            syncProjectcostHrInfo: syncProjectcostHrInfo
        };
        return service;
        /**
         * 查询所有岗位信息
         */
        function getCostWeekTitle() {
            return HttpService.get($rootScope.getWaySystemApi + 'projectCostInputManager/getCostWeekTitle');
        }
        /**
         * 每周项目成本信息
         * @param  参数
         */
        function getProjectcostWeek(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'projectCostInputManager/getProjectcostWeek', urlData);
        }
        /**
         * 查看项目费用成本
         * @param  参数
         */
        function getProjectcostFee(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'projectCostInputManager/getProjectcostFee', urlData);
        }
        /**
         * 获取时间段内项目中存在的工时类型
         * @param  参数
         */
        function getHoursTypeByTimePeriod(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'projectCostInputManager/getHoursTypeByTimePeriod', urlData);
        }
        /**
         * 获取人力详情信息
         * @param  参数
         */
        function getProjectcostHrDetail(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'projectCostInputManager/getProjectcostHrDetail', urlData);
        }
        /**
         * 修改项目费用成本状态
         * @param  参数
         */
        function updateFeeCost(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'projectCostInputManager/updateFeeCost', urlData);
        }
        /**
         * 修改项目人力成本状态
         * @param  参数
         */
        function updateHrCost(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'projectCostInputManager/updateHrCost', urlData);
        }
        /**
         * 同步人力数据
         * @param  参数
         */
        function syncProjectcostHrInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'projectCostInputManager/syncProjectcostHrInfo', urlData);
        }
    }
})();

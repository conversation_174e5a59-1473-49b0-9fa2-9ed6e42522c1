/*
 * @Author: fubaole
 * @Date:   2017-09-25 11:10:39
 * @Last Modified by:   haohong<PERSON>
 * @Last Modified time: 2018-03-02 14:00:25
 */

(function() {
    'use strict';
    app.controller("send_news", ['$rootScope', '$scope', '$timeout', '$stateParams', '$modal', '$log', 'Trans', 'MessageService', 'inform', 'LocalCache', 'AgreeConstant',
        function($rootScope, $scope, $timeout, $stateParams, $modal, $log, Trans, MessageService, inform, LocalCache, AgreeConstant) {
            var interfaceMap = {};

            $scope.map = {}; //条件
            $scope.orderStr = "create_time desc"; //条件
            $scope.getData = getData; //初始化函数
            $scope.pages = inform.initPages(); // 初始化分页数据
            $scope.getData($scope.pages.pageNum); // 初始化请求数据
            $scope.title = 'id';
            $scope.order = order; // 排序
            $scope.advQuery = advQuery; //高级查询功能按钮
            $scope.searchData = searchData; // 查询
            $scope.resendMessage = resendMessage; // 重新发送消息
            $scope.open = open; // 删除消息
            $scope.checked = []; // 存放选中的ID
            $scope.selectAll = selectAll; // 全选
            $scope.selectOne = selectOne; // 单选

            // 获取消息类型值
            getMessageType("alarm_notice_method");

            // 获取消息发送状态
            getMessageType('messageSendStatus');

            // 排序函数
            function order(str) {
                $scope.title = str;
                $scope.desc = !$scope.desc;
            }

            // 查询
            function searchData(num) {
                interfaceMap = angular.copy($scope.map);
                getData(AgreeConstant.pageNum);
            }

            // 获取下拉框数据
            function getMessageType(str) {
                MessageService.getDictValueListByDictTypeCode(str)
                    .then(function(data) {
                        if (data.code === AgreeConstant.resultCode) {
                            if (str === 'alarm_notice_method') {
                                $scope.messageTypeCode = data.result;
                            }
                            if (str === 'messageSendStatus') {
                                $scope.messageSendType = data.result;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 获取表格数据
            function getData(num) {
                $scope.checked = [];
                $scope.select_all = false;
                if (!num) { inform.common(Trans('tip.pageNumTip')); return; }
                MessageService.getNoticeMessageByMap(JSON.stringify(interfaceMap), num, $scope.pages.size, $scope.orderStr)
                    .then(function(data) {
                        if (data.code === AgreeConstant.resultCode) {
                            $scope.pages.goNum = null;
                            var jsonData = data.result;
                            $scope.resultData = jsonData.list;
                            if ($scope.resultData.length === 0) {
                                inform.common(Trans('tip.noData'));
                                $scope.pages = inform.initPages();
                            } else {
                                $scope.pages.total = jsonData.total;
                                $scope.pages.star = (jsonData.pageNum - 1) * jsonData.pageSize + 1;
                                $scope.pages.end = jsonData.pageNum * jsonData.pageSize;
                                $scope.pages.pageNum = jsonData.pageNum;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans('tip.requestError'));
                    });
            }

            // 重新发送消息
            function resendMessage(ids) {
                if (ids.length > 0) {
                    MessageService.reSendMessage(ids.join())
                        .then(function(data) {
                            if (data.code === AgreeConstant.resultCode) {
                                inform.common(Trans("tip.sendMessage"));
                                console.log(data);
                            } else {
                                console.log(data);
                                inform.common(data.message);
                            }
                        }, function() {
                            inform.common(Trans("tip.requestError"));
                        });
                } else {
                    inform.common(Trans('common.chooseOneOpt'));
                }
            }

            // 全选函数
            function selectAll() {
                if ($scope.select_all) {
                    $scope.checked = [];
                    angular.forEach($scope.resultData, function(i) {
                        i.checked = true;
                        $scope.checked.push(i.messageId);
                    });
                } else {
                    angular.forEach($scope.resultData, function(i) {
                        i.checked = false;
                    });
                    $scope.checked = [];
                }
            }

            // 选择一个
            function selectOne() {
                angular.forEach($scope.resultData, function(i) {
                    var index = $scope.checked.indexOf(i.messageId);
                    if (index === -1 && i.checked) {
                        $scope.checked.push(i.messageId);
                    } else if (index !== -1 && !i.checked) {
                        $scope.checked.splice(index, 1);
                    }
                });
                if ($scope.resultData.length === $scope.checked.length) {
                    $scope.select_all = true;
                } else {
                    $scope.select_all = false;
                }
            }

            // 删除消息
            function open(item) {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function() {
                            return Trans('common.deleteTip');
                        }
                    }
                });
                modalInstance.result.then(function() {
                    if (item) {
                        MessageService.removeNoticeMessageByIds(item.messageId)
                            .then(function(data) {
                                if (data.code === AgreeConstant.resultCode) {
                                    interfaceMap = {};
                                    $scope.map = {};
                                    getData(1);
                                    inform.common(Trans('tip.delSuccess'));
                                } else {
                                    inform.common(data.message);
                                }
                            }, function() {
                                inform.common(Trans('tip.requestError'));
                            });
                    }
                });
            }

            //高级查询功能按钮
            function advQuery() {
                $scope.isOpen = !$scope.isOpen;
                $scope.map.sendType = "";
            }

        }
    ]);
})();
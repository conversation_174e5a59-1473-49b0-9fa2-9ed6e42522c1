(function () {
    app.controller("projectWorkingHoursDetailController", ['inform', 'Trans', '$ocLazyLoad', '$rootScope','comService', '$scope', '$stateParams', '$state', 'workingHoursBoardFactory', 'projectWorkingDetailService', 'AgreeConstant',
        function (inform, Trans, $ocLazyLoad, $rootScope,comService,$scope, $stateParams, $state, workingHoursBoardFactory, projectWorkingDetailService, AgreeConstant) {
            $scope.getData = getData;
            function getData() {
                getTotalData();
                getEngineerLevelChartData();
                getDepartmentChartData();
                getWorkingTypesChartData();
                getEngineerInputChartData();
            }

            $scope.currentEngineerLevelChart = null;
            $scope.currentDepartmentChart = null;
            $scope.currentWorkingTypesChart = null;
            $scope.currentEngineerInputChart = null;
            window.addEventListener("resize", chartResize);
            $scope.$on("$destroy", function() {
                window.removeEventListener('resize', chartResize);
            })
            function chartResize() {
                if ($scope.currentEngineerLevelChart) { $scope.currentEngineerLevelChart.resize(); }
                if ($scope.currentDepartmentChart) { $scope.currentDepartmentChart.resize(); }
                if ($scope.currentWorkingTypesChart) { $scope.currentWorkingTypesChart.resize(); }
                if ($scope.currentEngineerInputChart) { $scope.currentEngineerInputChart.resize(); }
            }
            function getEngineerLevelChartData() {
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime,
                    "projectId": $scope.formRefer.projectId
                }
                workingHoursBoardFactory.chartHideClear($scope.currentEngineerLevelChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentEngineerLevelChart);
                projectWorkingDetailService.getTitleHoursInfo(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.engineerLevelInfo = result.data;
                        workingHoursBoardFactory.chartHideLoading($scope.currentEngineerLevelChart);
                        workingHoursBoardFactory.showPie($scope.currentEngineerLevelChart,$scope.engineerLevelInfo, {
                            title: '按工程师级别统计',
                            type: 'statisDimensionName',
                            value: 'hours',
                            unit: '人月'
                        })
                    } else {
                        inform.common(result.message);
                        workingHoursBoardFactory.chartHideLoading($scope.currentEngineerLevelChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    workingHoursBoardFactory.chartHideLoading($scope.currentEngineerLevelChart);
                });
            }
            function getDepartmentChartData () {
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime,
                    "projectId": $scope.formRefer.projectId
                }
                workingHoursBoardFactory.chartHideClear($scope.currentDepartmentChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentDepartmentChart);
                projectWorkingDetailService.getDeptHoursInfo(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.departmentInfo = result.data;
                        workingHoursBoardFactory.chartHideLoading($scope.currentDepartmentChart);
                        workingHoursBoardFactory.showPie($scope.currentDepartmentChart,$scope.departmentInfo, {
                            title: '按部门统计',
                            type: 'statisDimensionName',
                            value: 'hours',
                            unit: '人月'
                        });
                    } else {
                        inform.common(result.message);
                        workingHoursBoardFactory.chartHideLoading($scope.currentDepartmentChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    workingHoursBoardFactory.chartHideLoading($scope.currentDepartmentChart);
                });
            }
            $scope.toDetail = function (title) {
                $scope.modalTitle = title;
            }
            function getWorkingTypesChartData () {
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime,
                    "projectId": $scope.formRefer.projectId
                }
                workingHoursBoardFactory.chartHideClear($scope.currentWorkingTypesChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentWorkingTypesChart);
                projectWorkingDetailService.getHourTypeHoursInfo(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.workingTypesInfo = result.data;
                        workingHoursBoardFactory.chartHideLoading($scope.currentWorkingTypesChart);
                        workingHoursBoardFactory.showBar($scope.currentWorkingTypesChart,$scope.workingTypesInfo.slice(0, 10),{
                            title: '按工时类型统计',
                            xType: 'statisDimensionName',
                            yType: 'hours',
                            fontSize: 12,
                            left: 'center',
                            grid: {
                                gridLeft: '2%',
                                gridRight: '2%',
                            },
                            needCustomSeries: true,
                            unit: '人月'
                        });
                    } else {
                        inform.common(result.message);
                        workingHoursBoardFactory.chartHideLoading($scope.currentWorkingTypesChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    workingHoursBoardFactory.chartHideLoading($scope.currentWorkingTypesChart);
                });
            }
            $scope.toPerson = function () {
                $state.go('app.office.workingHoursBoard', {
                    'orgCode': $stateParams.orgCode,
                    'typeSelect': '4',
                    "sortType": null
                });
            }
            function getEngineerInputChartData () {
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime,
                    "projectId": $scope.formRefer.projectId
                }
                workingHoursBoardFactory.chartHideClear($scope.currentEngineerInputChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentEngineerInputChart);
                projectWorkingDetailService.getPersonHoursInfoList(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.engineerInputChartInfo = result.data.list;
                        workingHoursBoardFactory.chartHideLoading($scope.currentEngineerInputChart);
                        eChartShowForWorkingHoursStatisticsBarAndLine($scope.currentEngineerInputChart,$scope.engineerInputChartInfo, '工程师投入情况', AgreeConstant.workingHoursBoard.engineerInputLegendData);
                    } else {
                        inform.common(result.message);
                        workingHoursBoardFactory.chartHideLoading($scope.currentEngineerInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    workingHoursBoardFactory.chartHideLoading($scope.currentEngineerInputChart);
                });
            }
            function eChartShowForWorkingHoursStatisticsBarAndLine(currentChart,data,title, legendData){
                var xData = [];
                var reworkHoursData = [];
                var workingHoursInputData = [];
                var managementHoursData = [];
                var technologyHoursData = [];
                if (data.length) {
                    angular.forEach(data, function (eachData) {
                        eachData.currentProHoursRate === null ? xData.push(eachData.employeeName) : xData.push(eachData.employeeName + '  ' + eachData.currentProHoursRate + '%');
                        reworkHoursData.push({
                            value: eachData.reworkHoursRate
                        });
                        workingHoursInputData.push({
                            value: eachData.currentProHoursDay
                        });
                        managementHoursData.push({
                            value: eachData.administrationHoursRate
                        });
                        technologyHoursData.push({
                            value: eachData.supportHoursRate
                        });
                    });
                    var option = {
                        title:{
                            text:title,
                            textStyle:{
                                fontSize: 18,
                                color: '#333'
                            }
                        },
                        grid:{
                            left:'2%',
                            right:'2%',
                            bottom:'0',
                            containLabel:true
                        },
                        legend: {
                            data: legendData
                        },
                        xAxis: [
                            {
                                type: 'category',
                                data: xData,
                                axisPointer: {
                                    type: 'shadow'
                                },
                                axisLabel:{
                                    rotate:30
                                }
                            }
                        ],
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'cross',
                                crossStyle: {
                                    color: '#999'
                                }
                            },
                            formatter: function (params, ticket, callback) {return workingHoursBoardFactory.xyAndDeptFormatterCall(params, ticket, callback, '工时投入', '人天')}
                        },
                        yAxis: [
                            {
                                type: 'value',
                                axisLabel: {
                                    formatter: '{value}人天'
                                }
                            },
                            {
                                type: 'value',
                                axisLabel: {
                                    formatter: '{value}%'
                                }
                            }
                        ],
                        series: [
                            {
                                name: legendData[3],
                                type: 'line',
                                yAxisIndex: 1,
                                data: reworkHoursData,
                                show: false,
                                itemStyle:{
                                    color:"#FF4500"
                                }
                            },
                            {
                                name: legendData[0],
                                type: 'bar',
                                yAxisIndex: 0,
                                data: workingHoursInputData,
                                barWidth: '20%',
                                itemStyle:{
                                    color:"#77b606"
                                }
                            },
                            {
                                name: legendData[1],
                                type: 'line',
                                yAxisIndex: 1,
                                data: managementHoursData,
                                itemStyle:{
                                    color:"#fab70c"
                                }
                            },
                            {
                                name: legendData[2],
                                type: 'line',
                                yAxisIndex: 1,
                                data: technologyHoursData,
                                itemStyle:{
                                    color:"#0000CD"
                                }
                            }
                        ]
                    };
                } else {
                    option = {
                        title: [{
                            text: title,
                            textStyle:{
                                fontSize: 12,
                                color: '#333'
                            },
                        },{
                            text: "暂无数据",
                            left: 'center',
                            top: 'center',
                            color: '#333',
                            textStyle: {
                                fontSize: 20
                            }
                        }]
                    }
                }

                currentChart.setOption(option, true);
            }

            function getTotalData(){
                var currentUrlData = {
                    "endDate": $scope.formRefer.endTime,
                    "startDate": $scope.formRefer.startTime,
                    "projectId": $scope.formRefer.projectId
                }
                $scope.showGatherHoursInfo = false;
                projectWorkingDetailService.getGatherHoursInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.totalData = result.data;
                        $scope.showGatherHoursInfo = true;
                    } else {
                        inform.common(result.message);
                        $scope.showGatherHoursInfo = true;
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    $scope.showGatherHoursInfo = true;
                });
            }

            $scope.loadSuccess = function (){
                $ocLazyLoad.load(
                    [
                        'library/component/echarts.min.js'
                    ]).then(function (){
                    workingHoursBoardFactory.init($scope, '人月');
                    workingHoursBoardFactory.initTime($scope, $stateParams.searchTimeString);
                    $scope.formRefer.startTime = $stateParams.startTime;
                    $scope.formRefer.endTime = $stateParams.endTime;
                    $scope.formRefer.projectId = $stateParams.teamCode;
                    $scope.projectName = $stateParams.projectName;
                    $scope.currentEngineerLevelChart = echarts.init(document.getElementById('engineerLevelChart'));
                    $scope.currentDepartmentChart = echarts.init(document.getElementById('departmentChart'));
                    $scope.currentWorkingTypesChart = echarts.init(document.getElementById('workingTypesChart'));
                    $scope.currentEngineerInputChart = echarts.init(document.getElementById('engineerInputChart'));
                    $scope.getData();
                });
            }
        }]);
})();

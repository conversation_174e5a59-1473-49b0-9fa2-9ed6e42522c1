/*
 * @Author: dongyinggang
 * @Date:   2019-05-24 16:37:05
 * @Last Modified by:   dongyinggang
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function () {
    'use strict';
    app.factory('appReportService', appReportService);
    appReportService.$inject = ["HttpService", '$rootScope'];

    function appReportService(HttpService, $rootScope) {

        var service = {
            getProductLineResult:getProductLineResult,
            getProjectResultByParam: getProjectResultByParam,
            getTaskNum:getTaskNum
        };
        return service;

        /**
         * 获取产品线的禅道应用展示内容
         */
        function getProductLineResult(ztSearchParamDto) {
            return HttpService.post($rootScope.getWaySystemApi + 'ztApply/getProductLineResult', ztSearchParamDto);
        }

        /**
         * 获取项目的禅道应用展示内容
         */
        function getProjectResultByParam(ztSearchParamDto) {
            return HttpService.post($rootScope.getWaySystemApi + 'ztApply/getProjectResult', ztSearchParamDto);
        }

        /**
         * 获取项目的禅道应用展示内容
         */
        function getTaskNum(ztSearchParamDto) {
            return HttpService.post($rootScope.getWaySystemApi + 'ztApply/getTaskNum', ztSearchParamDto);
        }

    }
})();

(function () {
    'use strict';

    angular.module('app').factory('dayjsService', dayjsService);

    function dayjsService() {
        // 确保dayjs在全局范围可用
        if (typeof dayjs === 'undefined') {
            console.error('Day.js库未被加载！');
            return {};
        }

        // 返回服务对象
        return {
            dayjs: dayjs,

            // 获取当前时间
            now: function () {
                return dayjs();
            },

            // 格式化日期
            format: function (date, formatStr = 'YYYY-MM-DD') {
                return dayjs(date).format(formatStr);
            },

            // 解析日期
            parse: function (dateStr, formatStr) {
                return formatStr ? dayjs(dateStr, formatStr) : dayjs(dateStr);
            },

            // 增加时间
            add: function (date, value, unit) {
                return dayjs(date).add(value, unit);
            },

            // 减少时间
            subtract: function (date, value, unit) {
                return dayjs(date).subtract(value, unit);
            },

            // 获取相对时间
            fromNow: function (date) {
                return dayjs(date).fromNow();
            },

            // 比较两个日期
            isBefore: function (date1, date2) {
                return dayjs(date1).isBefore(dayjs(date2));
            },

            isAfter: function (date1, date2) {
                return dayjs(date1).isAfter(dayjs(date2));
            },

            isSame: function (date1, date2, unit) {
                return dayjs(date1).isSame(dayjs(date2), unit);
            },

            // 判断日期是否相同或之前
            isSameOrBefore: function (date1, date2, unit) {
                return dayjs(date1).isBefore(dayjs(date2), unit) || dayjs(date1).isSame(dayjs(date2), unit);
            },

            // 判断日期是否相同或之后
            isSameOrAfter: function (date1, date2, unit) {
                return dayjs(date1).isAfter(dayjs(date2), unit) || dayjs(date1).isSame(dayjs(date2), unit);
            },

            // 获取时间差
            diff: function (date1, date2, unit, float) {
                return dayjs(date1).diff(dayjs(date2), unit, float);
            },
        };
    }
})();

(function () {
    app.controller("depContributionOperate", ['comService', '$rootScope', '$scope', 'depContributionService', '$state', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, depContributionService, $state, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // isAdd记录页面是修改还是新增 0：新增 1：修改
            var isAdd = $stateParams.isAdd;
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //新增对象
            $scope.specDetail = {};
            //分数
            $scope.scoreList = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16];
            //分数下拉框显示标志 0:不显示 1:显示
            $scope.flag = 0;
            //修改页面回显
            $scope.specDetail = JSON.parse($stateParams.contributionInfoParam);
            //初始化页面信息
            initPages();
            $scope.setDraggableTime = setDraggableTime;
            // 设置二级部门
            $scope.getTwoDepartment = getTwoDepartment;

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 初始化页面信息
             */
            function initPages() {
                initDepart();
                editPage();
                // 贡献人下拉列表
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function (data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                        $scope.specDetail.employee = $scope.specDetail.employee == null ? [] : $scope.specDetail.employee.split(',');
                        setDraggableTime();
                    }
                });
            }

            /**
             * 贡献人变化时延迟1秒修改li属性允许拖拽
             */
            function setDraggableTime() {

                setTimeout(setDraggableOfli, 1000 * 1);
            }

            /**
             * 修改li属性允许拖拽
             */
            function setDraggableOfli() {

                var subNodes = document.querySelectorAll("ul.chosen-choices li.search-choice");

                for (var i = 0; i < subNodes.length; i++) {
                    $(subNodes[i]).attr("draggable", true);
                }
            }

            /**
             * 初始化部门列表
             */
            function initDepart() {
                //获取一级部门
                $scope.oneDepartmentList = [];
                comService.getOrgChildren('0002').then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.oneDepartmentList = data.data;
                    }
                });
            }

            //获取二级部门
            function getTwoDepartment() {
                //获取二级部门
                $scope.twoDepartmentList = [];
                comService.getOrgChildren($scope.specDetail.oneDepartmentCode).then(setTwoDepartment);
            }
            
            //获取系研下的二级部门
            function getXYDepartment(){
                //获取二级部门
                $scope.twoDepartmentList = [];
                comService.getOrgChildren('D010053').then(setTwoDepartment);
            }
            /**
            *设置二级部门下拉框值
            **/
            function setTwoDepartment (data) {
                if (data.code === AgreeConstant.code) {
                    $scope.twoDepartmentList = data.data;
                }
            }
            /**
             * 根据贡献类型设置贡献分数
             */
            $scope.setScore = function (){
                //判断是否是新增页面
                    if($scope.specDetail.type==='申报材料编写'){
                        $scope.specDetail.score = 20;
                        $scope.scoreEdit = true;
                        $scope.flag = false;
                    }
                    else if($scope.specDetail.type==='管理实践分享' || $scope.specDetail.type==='沙龙分享' ){
                        $scope.specDetail.score = 10;
                        $scope.scoreEdit = true;
                        $scope.flag = false;
                    }
                    else if($scope.specDetail.type==='案例分析' || $scope.specDetail.type==='宣贯学习' ){
                        $scope.specDetail.score = 0;
                        $scope.scoreEdit = false;
                        $scope.flag = false;
                    }
                    else if($scope.specDetail.type==='开源模块' || $scope.specDetail.type==='质量汇报'){
                        $scope.specDetail.score = 1;
                        $scope.flag = true;
                    }
                    else{
                        $scope.specDetail.score = null;
                        $scope.scoreEdit = false;
                        $scope.flag = false;
                    }
            }

            function editPage(){
                //如果是修改且一级部门为系研
                if (isAdd === '1' && $scope.specDetail.oneDepartmentCode === 'D010053'){
                    //获取系研下的所有二级部门
                    getXYDepartment();
                }
                if (isAdd === '1') {
                    if($scope.specDetail.type==='案例分享' || $scope.specDetail.type==='其他'){
                        $scope.scoreEdit = false;
                        $scope.flag = false;
                    }
                    else if($scope.specDetail.type==='开源模块'){
                        $scope.flag = true;
                    }
                    else {
                        $scope.scoreEdit = true;
                        $scope.flag = false;
                    }
                }
            }

            /**
             * 添加或修改部门贡献页面
             */
            $scope.addDetail = function () {
                var urlData = {
                    //id存在就更新，不存在就插入
                    'id' : $scope.specDetail.id,
                    'type': $scope.specDetail.type,
                    'oneDepartment': $scope.specDetail.oneDepartmentCode,
                    'twoDepartment': $scope.specDetail.twoDepartmentCode,
                    'employees': $scope.specDetail.employee,
                    'theme': $scope.specDetail.theme,
                    'createDay': inform.format($scope.specDetail.createDay, 'yyyy-MM-dd'),
                    'description': $scope.specDetail.description,
                    'score': $scope.specDetail.score
                };
                depContributionService.upData(urlData).then(function (data) {
                    callBackFunction(data);
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });

            };

            function callBackFunction(data) {
                if (data.code === AgreeConstant.code) {
                    layer.confirm(data.message, {
                        title: false,
                        btn: ['确定']
                    }, function (result) {
                        layer.close(result);
                        $state.go("app.office.departmentContribution");
                    });
                } else {
                    inform.common(data.message);
                }

            }

            //更新时间
            $scope.openDateup = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.up = true; //更新时间
            };

            //新增时间
            $scope.openDateadd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.add = true; //新增时间

            };

            //返回页面跳转
            $scope.goback = function () {
                $state.go("app.office.departmentContribution");
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

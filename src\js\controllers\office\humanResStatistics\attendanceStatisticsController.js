
(function () {
    app.controller("attendanceStatisticsController", [ '$scope','$rootScope','$stateParams','$state','LocalCache','inform','Trans','comService','attendanceStatisticsService','AgreeConstant','$modal',
        function ($scope,$rootScope, $stateParams,$state,LocalCache,inform,Trans,comService,attendanceStatisticsService,AgreeConstant,$modal) {

    		$scope.formRefer = {};
            //标记数据获取进度
            $scope.flag=0;
            //标记是否是在初始化时设置快捷按钮样式
            $scope.initFlag=0;
            //是否点击了查询按钮
            $scope.selectFlag;
    		$scope.resetParam = resetParam;
            $scope.timeSelect=['前一月','前一季度','上半年','下半年','本年度','上年度'];
            //初始化数据
            initData();
            $scope.getData = getData;

           /**
            * 当窗体大小变化时，修改图例大小
            */
           window.addEventListener("resize", function () {
               if ($scope.currentDepartmentAttendanceChart) { $scope.currentDepartmentAttendanceChart.resize(); }
               if ($scope.currentDepartmentWorkStrengthChart) { $scope.currentDepartmentWorkStrengthChart.resize(); }
               if ($scope.currentAreaWorkStrengthChart) { $scope.currentAreaWorkStrengthChart.resize(); }
               if ($scope.currentAreaWorkStrengthWeiHaiChart) { $scope.currentAreaWorkStrengthWeiHaiChart.resize(); }
               if ($scope.currentAreaWorkStrengthBeiJingChart) { $scope.currentAreaWorkStrengthBeiJingChart.resize(); }
               if ($scope.currentAreaWorkStrengthXiAnChart) { $scope.currentAreaWorkStrengthXiAnChart.resize(); }
               if ($scope.currentAreaWorkStrengthShenZhenChart) { $scope.currentAreaWorkStrengthShenZhenChart.resize(); }
               if ($scope.currentManagerAttendanceChart) { $scope.currentManagerAttendanceChart.resize(); }
           });

            function timeout(){
                //表示图表信息全部获取完成
                if(8 === $scope.flag){
                    setTimeout(eChartForDepartmentAttendance,500);
                    setTimeout(eChartForWorkStrength,500);
                }
            }
            function initData(){
                //初始化部门
				$scope.departmentShowList = [];
				$scope.departmentMap = {};
                comService.getOrgChildren('D010053').then(function(data) {
                    var department = comService.getDepartment(data.data);
                    angular.forEach(department, function (i) {
                        $scope.departmentMap[i.orgCode]=i.orgName;
                        if(i.orgCode !=='D010053'){
                            $scope.departmentShowList.push(i);
                        }
                    });
                    setTimeout(resetParam,500);
                    //再次调用，初始化时resetParam中调用不生效
                    $scope.initTime('前一季度');
                });
                //初始化地区
                $scope.areaList = [];
                $scope.areaMap = {};
                $scope.areaMap1 = {};
                comService.getParamList('AREA_TYPE', 'AREA_TYPE').then(function (data) {
                    $scope.areaList = data.data;
                    angular.forEach($scope.areaList, function (area) {
                        $scope.areaMap[area.param_value]=area.param_code;
                        $scope.areaMap1[area.param_code]=area.param_value;
                    });
                });
                //初始化产品线
                $scope.productLineList = [];
                $scope.productLineMap = {};
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    $scope.productLineList = data.data;
                    angular.forEach($scope.productLineList, function (productLine) {
                        $scope.productLineMap[productLine.param_code]=productLine.param_value;
                    });
                });
            }
            function resetParam(){
                $scope.initTime('前一月');
                //重置部门选择
                var departmentBoxes = document.getElementsByName("department");
                //获取页面所有产权归属
                for(var m=0;m<departmentBoxes.length;m++){
                    departmentBoxes[m].checked = true;
                }
                getData();
            }
            $scope.initTime = function(flag){
                $scope.butFlag = flag;
                var date = new Date();
                var y = date.getFullYear();  //当前年份
                //设置为1号，防止31号时获取到当月
                date.setDate(1);
                if('前一月'===$scope.butFlag){
                    date.setMonth(date.getMonth()-1);
                    $scope.formRefer.startTime = inform.format(date,"yyyy-MM");
                    $scope.formRefer.endTime = inform.format(date,"yyyy-MM");
                }
                if('前一季度'===$scope.butFlag){
                    //当前月份
                    var m = new Date().getMonth();
                    //当前季度
                    var q = parseInt(m / 3);
                    //上一季度的开始日期
                    $scope.formRefer.startTime = inform.format(new Date(y, (q - 1) * 3, 1),"yyyy-MM");
                    //上一季度的结束日期
                    $scope.formRefer.endTime = inform.format(new Date(y, q * 3, 0),"yyyy-MM");
                }
                if('上半年'===$scope.butFlag){
                    $scope.formRefer.startTime = y+'-01';
                    $scope.formRefer.endTime = y+'-06';
                }
                if('下半年'===$scope.butFlag){
                    $scope.formRefer.startTime = y+'-07';
                    $scope.formRefer.endTime = y+'-12';
                }
                if('本年度'===$scope.butFlag){
                    $scope.formRefer.startTime = y+'-01';
                    $scope.formRefer.endTime = y+'-12';
                }
                if('上年度'===$scope.butFlag){
                    $scope.formRefer.startTime = parseInt(y) - 1+'-01';
                    $scope.formRefer.endTime = parseInt(y) - 1+'-12';
                }
                if($scope.initFlag===0){
                    setTimeout(setButton,500);
                }else {
                    setButton();
                }
            }
            //设置按钮颜色
            function setButton(){
                $scope.initFlag=1;
                //获取所有快捷按钮
                var buttonBoxes = document.getElementsByName("buttons");
                angular.forEach(buttonBoxes, function (but) {
                    if($scope.butFlag === but.id){
                        $("#"+but.id).css("background-color", "#16a8f8");
                    }else{
                        $("#"+but.id).css("background-color", "#CDCDC1");
                    }
                });
            }
            //查询当前团队的首页信息
            function getData(flag){
                $scope.selectFlag=flag;
                //获取选中的部门集合
                var selectDepList=[];
                var departmentBoxes = document.getElementsByName("department");
                //获取页面所有产权归属
                for(var m=0;m<departmentBoxes.length;m++){
                    if(departmentBoxes[m].checked){
                        selectDepList.push(departmentBoxes[m].value);
                    }
                }
                $scope.urlData={
                    'deptCodeList':selectDepList,
                    'statisDateStart':$scope.formRefer.startTime,
                    'statisDateEnd':$scope.formRefer.endTime
                }
                //获取系研数据
                getAttendanceTotalInfo();
                //获取部门出勤率/工时利用率统计
                getDepartmentAttendanceInfo();
                //部门工作强度统计
                getDepartmentWorkStrengthInfo();
                //区域工作强度统计--汇总
                getAreaWorkStrengthInfo();
                //区域工作强度统计--威海
                $scope.weihaiAreaWorkStrengthInfo=[];
                getAreaWorkStrengthByAreaInfo('威海',weihaiAreaWorkStrengthData);
                //区域工作强度统计--北京
                $scope.beijingAreaWorkStrengthInfo=[];
                getAreaWorkStrengthByAreaInfo('北京',beijingAreaWorkStrengthData);
                //区域工作强度统计--西安
                $scope.xianAreaWorkStrengthInfo=[];
                getAreaWorkStrengthByAreaInfo('西安',xianAreaWorkStrengthData);
                //区域工作强度统计--深圳
                $scope.shenzhenAreaWorkStrengthInfo=[];
                getAreaWorkStrengthByAreaInfo('深圳',shenzhenAreaWorkStrengthData);
                //部门管理职能人员考勤统计
                getManagerAttendanceInfo();
            }
            //获取系研数据
            function getAttendanceTotalInfo(){
                $scope.totalData=[];
                attendanceStatisticsService.getAttendanceTotalInfo($scope.urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        $scope.totalData = data.data;
                    }
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            //获取部门出勤率/工时利用率统计
            function getDepartmentAttendanceInfo(){
                $scope.departmentAttendanceInfo=[];
                attendanceStatisticsService.getDepartmentAttendanceInfo($scope.urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        $scope.departmentAttendanceInfo = data.data;
                        if($scope.selectFlag){
                            eChartForDepartmentAttendance();
                        }
                    } else {
                        inform.common(data.message);
                    }
                    $scope.flag++;
                    timeout();
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            //部门工作强度统计
            function getDepartmentWorkStrengthInfo(){
                $scope.departmentWorkStrengthInfo=[];
                attendanceStatisticsService.getDepartmentWorkStrengthInfo($scope.urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        $scope.departmentWorkStrengthInfo = data.data;
                        if($scope.selectFlag){
                            eChartShowForWorkStrength($scope.currentDepartmentWorkStrengthChart,$scope.departmentWorkStrengthInfo,'系研工作强度统计');
                        }
                    } else {
                        inform.common(data.message);
                    }
                    $scope.flag++;
                    timeout();
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            //区域工作强度统计--汇总
            function getAreaWorkStrengthInfo(){
                $scope.areaWorkStrengthInfo=[];
                attendanceStatisticsService.getAreaWorkStrengthInfo($scope.urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        $scope.areaWorkStrengthInfo = data.data;
                        if($scope.selectFlag){
                            eChartShowForWorkStrength($scope.currentAreaWorkStrengthChart,$scope.areaWorkStrengthInfo,'区域工作强度统计--汇总');
                        }
                    } else {
                        inform.common(data.message);
                    }
                    $scope.flag++;
                    timeout();
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            //区域工作强度统计--分区域
            function getAreaWorkStrengthByAreaInfo(type,backFunction){
                var areaList=[];
                areaList.push($scope.areaMap[type]);
                var currentUrlData = JSON.parse(JSON.stringify($scope.urlData));
                currentUrlData.regionCodeList = areaList;
                attendanceStatisticsService.getAreaWorkStrengthByAreaInfo(currentUrlData).then(backFunction,
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            //处理区域工作强度统计--威海
            function weihaiAreaWorkStrengthData(weihaiData){
                if (weihaiData.code===AgreeConstant.code) {
                     $scope.weihaiAreaWorkStrengthInfo = weihaiData.data;
                     if($scope.selectFlag){
                         eChartShowForWorkStrength($scope.currentAreaWorkStrengthWeiHaiChart,$scope.weihaiAreaWorkStrengthInfo,'区域工作强度统计--威海');
                     }
                } else {
                    inform.common(data.message);
                }
                $scope.flag++;
                timeout();
            }
            //处理区域工作强度统计--北京
            function beijingAreaWorkStrengthData(data){
                if (data.code===AgreeConstant.code) {
                       $scope.beijingAreaWorkStrengthInfo = data.data;
                       if($scope.selectFlag){
                           eChartShowForWorkStrength($scope.currentAreaWorkStrengthBeiJingChart,$scope.beijingAreaWorkStrengthInfo,'区域工作强度统计--北京');
                       }
                } else {
                    inform.common(data.message);
                }
                $scope.flag++;
                timeout();
            }
            //处理区域工作强度统计--西安
            function xianAreaWorkStrengthData(data){
                if (data.code===AgreeConstant.code) {
                        $scope.xianAreaWorkStrengthInfo = data.data;
                        if($scope.selectFlag){
                            eChartShowForWorkStrength($scope.currentAreaWorkStrengthXiAnChart,$scope.xianAreaWorkStrengthInfo,'区域工作强度统计--西安');
                        }
                } else {
                    inform.common(data.message);
                }
                $scope.flag++;
                timeout();
            }
            //处理区域工作强度统计--深圳
            function shenzhenAreaWorkStrengthData(data){
                if (data.code===AgreeConstant.code) {
                       $scope.shenzhenAreaWorkStrengthInfo = data.data;
                       if($scope.selectFlag){
                           eChartShowForWorkStrength($scope.currentAreaWorkStrengthShenZhenChart,$scope.shenzhenAreaWorkStrengthInfo,'区域工作强度统计--深圳');
                       }
                } else {
                    inform.common(data.message);
                }
                $scope.flag++;
                timeout();
            }

            //部门管理职能人员考勤统计
            function getManagerAttendanceInfo(){
                $scope.managerAttendanceInfo=[];
                attendanceStatisticsService.getManagerAttendanceInfo($scope.urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        $scope.managerAttendanceInfo = data.data;
                        if($scope.selectFlag){
                            eChartShowForWorkStrength($scope.currentManagerAttendanceChart,$scope.managerAttendanceInfo,'系研管理职能人员考勤统计');
                        }
                    } else {
                        inform.common(data.message);
                    }
                    $scope.flag++;
                    timeout();
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            //下载工作强度数据
            $scope.downloadAttendanceStatistics = function(){
                 inform.modalInstance("确定要下载工作强度统计表吗？").result.then(function() {
                     inform.downLoadFile('hr_download/for_workintensity',$scope.urlData,"工作强度统计表"+inform.format(new Date(),'yyyy-MM-dd')+".xlsx");
                });
            }

            //部门出勤率统计显示
            function eChartForDepartmentAttendance() {
                $scope.currentDepartmentAttendanceChart = echarts.init(document.getElementById('departmentAttendanceChart'));
                var xData=[];
                var attendRate=[];

                angular.forEach($scope.departmentAttendanceInfo, function (attendanceInfo) {
                    xData.push($scope.departmentMap[attendanceInfo.statisTargetName]==null? attendanceInfo.statisTargetName:$scope.departmentMap[attendanceInfo.statisTargetName]);
                    attendRate.push(attendanceInfo.attendRate);
                });

                var option = {
                  title:{
                      text:"系研出勤率",
                      textStyle:{
                          fontSize: 12,
                          color: '#333'
                      }
                  },
                  tooltip: {
                    trigger: 'axis',
                    formatter:formatterCall,
                    axisPointer: {
                      type: 'cross',
                      crossStyle: {
                        color: '#999'
                      }
                    }
                  },
                  legend: {
                    data: ['出勤率']
                  },
                  xAxis: [
                    {
                      type: 'category',
                      data:xData,
                      top:16,
                      axisPointer: {
                        type: 'shadow'
                      },
                      axisLabel:{
                          rotate:25
                      },
                    }
                  ],
                  yAxis: [
                    {
                      type: 'value',
                      name: '出勤率',
                      min:90,
                      axisLabel: {
                        formatter: '{value}%'
                      }
                    }
                  ],
                  series: [
                    {
                      name: '出勤率',
                      type: 'line',
                      label: {
                        show: true,
                        formatter: '{c}%'
                      },
                      data: attendRate
                    }

                  ]
                };
                $scope.currentDepartmentAttendanceChart.setOption(option, true);
            }
            //工作强度图表创建
            function eChartForWorkStrength(){
                $scope.currentDepartmentWorkStrengthChart = echarts.init(document.getElementById('departmentWorkStrengthChart'));
                $scope.currentAreaWorkStrengthChart = echarts.init(document.getElementById('areaWorkStrengthChart'));
                $scope.currentAreaWorkStrengthWeiHaiChart = echarts.init(document.getElementById('areaWorkStrengthWeiHaiChart'));
                $scope.currentAreaWorkStrengthBeiJingChart = echarts.init(document.getElementById('areaWorkStrengthBeiJingChart'));
                $scope.currentAreaWorkStrengthXiAnChart = echarts.init(document.getElementById('areaWorkStrengthXiAnChart'));
                $scope.currentAreaWorkStrengthShenZhenChart = echarts.init(document.getElementById('areaWorkStrengthShenZhenChart'));
                $scope.currentManagerAttendanceChart = echarts.init(document.getElementById('managerAttendanceChart'));

                eChartShowForWorkStrength($scope.currentDepartmentWorkStrengthChart,$scope.departmentWorkStrengthInfo,'系研工作强度统计');
                eChartShowForWorkStrength($scope.currentAreaWorkStrengthChart,$scope.areaWorkStrengthInfo,'区域工作强度统计--汇总');
                eChartShowForWorkStrength($scope.currentAreaWorkStrengthWeiHaiChart,$scope.weihaiAreaWorkStrengthInfo,'区域工作强度统计--威海');
                eChartShowForWorkStrength($scope.currentAreaWorkStrengthBeiJingChart,$scope.beijingAreaWorkStrengthInfo,'区域工作强度统计--北京');
                eChartShowForWorkStrength($scope.currentAreaWorkStrengthXiAnChart,$scope.xianAreaWorkStrengthInfo,'区域工作强度统计--西安');
                eChartShowForWorkStrength($scope.currentAreaWorkStrengthShenZhenChart,$scope.shenzhenAreaWorkStrengthInfo,'区域工作强度统计--深圳');
                eChartShowForWorkStrength($scope.currentManagerAttendanceChart,$scope.managerAttendanceInfo,'系研管理职能人员考勤统计');
            }

            //工作强度图表显示
            function eChartShowForWorkStrength(currentChart,data,title){
                var xData=[];
                var workHourDelayNormalAverage=[];
                var workHourDelayTotalAverage=[];
                var workIntensityNormal=[];
                var workIntensityTotal=[];
                var mapData = $scope.departmentMap;
                if(title==='区域工作强度统计--汇总'){
                    mapData = $scope.areaMap1;
                }
                angular.forEach(data, function (workHourDelay) {
                    xData.push(mapData[workHourDelay.statisTargetName]==null? workHourDelay.statisTargetName:mapData[workHourDelay.statisTargetName]);
                    workHourDelayNormalAverage.push(workHourDelay.workHourDelayNormalAverage);
                    workHourDelayTotalAverage.push(workHourDelay.workHourDelayTotalAverage);
                    workIntensityNormal.push(workHourDelay.workIntensityNormal);
                    workIntensityTotal.push(workHourDelay.workIntensityTotal);
                });
                var option = {
                  title:{
                      text:title,
                      textStyle:{
                          fontSize: 12,
                          color: '#333'
                      }
                  },
                  tooltip: {
                    trigger: 'axis',
                    formatter:formatterCall,
                    axisPointer: {
                      type: 'cross',
                      crossStyle: {
                        color: '#999'
                      }
                    }
                  },
                  legend: {
                    data: ['平日延时打卡工时', '总延时打卡工时', '平日工作强度','总工作强度'],
                    textStyle:{
                        fontSize: 10,
                    },
                    top:16
                  },
                  xAxis: [
                    {
                      type: 'category',
                      data:xData,
                      axisPointer: {
                        type: 'shadow'
                      },
                      axisLabel:{
                        rotate:25
                      }
                    }
                  ],
                  yAxis: [
                    {
                      type: 'value',
                      name: '小时数',
                      nameTextStyle:{
                        padding:[0,0,0,-58]
                      },
                      axisLabel: {
                        formatter: '{value}'
                      }
                    },
                    {
                      type: 'value',
                      name: '工作强度',
                      min:100,
                      nameTextStyle:{
                        padding:[0,0,0,55]
                      },
                      axisLabel: {
                        formatter: '{value}%'
                      }
                    }
                  ],
                  series: [
                    {
                      name: '平日延时打卡工时',
                      type: 'bar',
                      label: {
                        show: true,
                      },
                      data: workHourDelayNormalAverage
                    },
                    {
                      name: '总延时打卡工时',
                      type: 'bar',
                      label: {
                        show: true,
                      },
                      data: workHourDelayTotalAverage

                    },
                    {
                      name: '平日工作强度',
                      type: 'line',
                      yAxisIndex: 1,
                      label:{
                          show:true,
                          formatter: '{c}%'
                      },
                      data: workIntensityNormal
                    },
                    {
                      name: '总工作强度',
                      type: 'line',
                      yAxisIndex: 1,
                      label:{
                          show:true,
                          formatter: '{c}%'
                      },
                      data: workIntensityTotal
                    }

                  ]
                };

                currentChart.setOption(option, true);
            }
        //自定义鼠标悬浮样式
		 function formatterCall (params, ticket, callback) {
            var htmlStr = '';
            for(var i=0;i<params.length;i++){
                var param = params[i];
                var xName = param.name;//x轴的名称
                var seriesName = param.seriesName;//图例名称
                var value = param.value;//y轴值
                var color = param.color;//图例颜色
                if(i===0){
                    htmlStr += xName + '<br/>';//x轴的名称
                }
                htmlStr +='<div>';
                //为了保证和原来的效果一样，这里自己实现了一个点的效果
                htmlStr += '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:'+color+';"></span>';
                //圆点后面显示的文本
                htmlStr += seriesName;
                htmlStr += '<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">';
                if('平日工作强度'===seriesName || '总工作强度'===seriesName || '出勤率'===seriesName){
                    htmlStr += +value+ '%';
                }else{
                    htmlStr += +value;
                }

                htmlStr += '</span>';
                htmlStr += '</div>';
            }
            return htmlStr;
         }

         $scope.gotoAttendanceMore = function(){
            $state.go('app.office.attendanceMore');
         }

        //开始时间
        $scope.openSearchOnboardTime = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openSearchOnboardTime1 = true;
            $scope.openStopOnboardTime1 = false;
        };
        //截止时间
         $scope.openStopOnboardTime = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openSearchOnboardTime1 = false;
            $scope.openStopOnboardTime1 = true;
        };

      }]);
})();
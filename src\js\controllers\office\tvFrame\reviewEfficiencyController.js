
(function () {
    app.controller("reviewEfficiencyController", ['$rootScope', 'comService', 'SystemService', 'tvService', '$scope', '$state', '$location', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http', 'listUtil',
        function ($rootScope, comService, SystemService, tvService, $scope, $state,$location, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http, listUtil) {
            listUtil.init();
            // 从后端获取相关数据
            listUtil.getData({
                'type': 1
            }).then(function (result) {
                if (result.code === AgreeConstant.code) {
                    $scope.itemList = angular.fromJson(result.data)
                } else {
                    inform.common(result.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
            //窗体大小变化时重新计算高度
            $(window).resize(listUtil.setCss);
            // 参数
            var params = $location.search();
            // 处理标题当前年份和时间
            $scope.currentYear = params.year;
            $scope.currentQuarter = params.quarter;
        }])
})();

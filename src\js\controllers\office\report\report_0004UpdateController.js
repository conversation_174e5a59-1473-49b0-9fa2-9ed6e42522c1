(function () {
    app.controller("report_0004UpdateController", ['reviewProblemService', 'comService', '$http', 'LocalCache', '$rootScope', '$state', '$stateParams', '$scope', '$modal', 'reportService', 'inform', 'Trans', 'AgreeConstant',
        function (reviewProblemService, comService, $http, LocalCache, $rootScope, $state, $stateParams, $scope, $modal, reportService, inform, Trans, AgreeConstant ) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.employeeMap = {};
            //控制评审时间字段是否必填
            $scope.planCompletionDatFlag =false;
            //校验变量
            $scope.limitList = AgreeConstant.limitList;
            //页面分页信息
            $scope.pages = {
                pageNum: '',	//分页页数
                size: '',		//分页每页大小
                total: ''		//数据总数
            };
            //季度下拉框数据源
            $scope.quarterSelect = [{
                value: '0',
                label: '第1季度'
            }, {
                value: '1',
                label: '第2季度'
            }, {
                value: '2',
                label: '第3季度'
            }, {
                value: '3',
                label: '第4季度'
            }];
            //评审主题Map
            $scope.themeMap = reviewThemeListConfig;
            //评审级别下拉框数据源
            $scope.levelSelect = [{
                value: '0',
                label: '一级'
            }, {
                value: '1',
                label: '二级'
            }, {
                value: '2',
                label: '三级'
            }];

            //产品线下拉框数据源
            $scope.lineSelect = [];

            //产品线展示Map
            $scope.lineMap = {};

            //评审结果下拉框数据源
            $scope.resSelect = [{
                value: '0',
                label: '有条件通过'
            }, {
                value: '1',
                label: '通过'
            }, {
                value: '2',
                label: '不通过'
            }, {
                value: '3',
                label: '未评审'
            }];

            //是否跟踪下拉框数据源
            $scope.trackSelect = [{
                value: '0',
                label: '是'
            }, {
                value: '1',
                label: '否'
            }];

            //是否跟踪下拉框数据源
            $scope.typeMap = [{
                value: '0',
                label: '邮件'
            }, {
                value: '1',
                label: '会议'
            }];

            $scope.projectChange = projectChange;
            //预评审list
            $scope.preRerviewList = [];

            //初始化页面信息
            initPages();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
                var clientWidth = document.body.clientWidth;
                $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
            }

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            //当修改数据时选择日期，自动将季度回填
            $scope.dateToQuarterUpdate = function () {
                if(typeof ($scope.changeParam.PLAN_COMPLETION_DATE) === 'undefined'
                    || '' === $scope.changeParam.PLAN_COMPLETION_DATE || null == $scope.changeParam.PLAN_COMPLETION_DATE){
                    $scope.changeParam.QUARTER = '';
                    return;
                }
                var month = $scope.changeParam.PLAN_COMPLETION_DATE.getMonth() + 1;
                $scope.changeParam.QUARTER = inform.dateToQuarter(month);
            };

            /**
             * 页面初始化
             */
            function initPages() {
                var flag = 0;
                //获取所有项目名称
                $scope.projectList = [];
                //获取最近关闭或者进行中的所有项目名称
                var urlData = {flag: 'doing'};
                comService.getProjectsNameByParams(urlData).then(function (data) {
                    $scope.projectList = angular.fromJson(data.data);
                    flag++;
                    setParams(flag);
                });

                //获取员工信息
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function(data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                        $scope.employeeMap = {};
                        for(var i = 0;i < $scope.employeeList.length; i++) {
                            $scope.employeeMap[$scope.employeeList[i].realName] = $scope.employeeList[i].companyTitleLevel;
                        }
                        flag++;
                        setParams(flag);
                    }
                });

            }

            function setParams(flag){
                if(flag < 2){
                    return;
                }
                //修改时查询页面传来的Json
                $scope.changeParam = JSON.parse($stateParams.jsonResult);
                $scope.changeParam.TEM_NAME = parseInt($scope.changeParam.TEM_NAME);
                //将预评审信息的id字符串转换为数组
                if(null != $scope.changeParam.pre_review_id) {
                    $scope.changeParam.pre_review_id = $scope.changeParam.pre_review_id.split(',');
                }
                //预评审list
                $scope.preRerviewList = [];

                //若为未评审，则默认评审时间不必填
                if($scope.changeParam.REVIEW_RESULT === '3'){
                    $scope.planCompletionDatFlag =true;
                }

                //获取评委列表
                getJudgeByReview($scope.changeParam.ID);
                //查询项目信息
                projectChange($scope.changeParam.TEM_NAME,true);
            }



            /**
             * 根据关键角色名称，获取其等级
             * @param item
             */
            $scope.setJudgeGrade = function(item){
                if(typeof (item) !== 'undefined') {
                    item.judgeGrade = $scope.employeeMap[item.judgeName];
                    $scope.calAvgGrade()
                }
            };

/**
             * 根据关键角色名称，遍历以获取其编号
             * @param item
             */
            $scope.setJudgeNo = function(item){
                for(var i = 0;i <$scope.employeeList.length;i++){
                    if($scope.employeeList[i].realName === item.judgeName){
                        item.employeeId = $scope.employeeList[i].employeeNo;
                    }
                }
            };

            /**
             * 查询评委信息
             * @param reviewId
             */
            function getJudgeByReview(reviewId) {
                var param = {'reviewId': reviewId};
                reportService.getJudgeByReview(param).then(function (data) {
                    $scope.changeParam.judgeList = angular.fromJson(data.data);
                    $scope.calAvgGrade();
                    $scope.updateCalWorkLoad();

                });
            }

            /**
             * 校验评委列表中是否存在重复
             * 规则：
             * 1.重复则提示"评委×××,×××重复,请修改"并返回false
             * 2.无重复则返回true
             * @returns {boolean}
             */
            function verifyJudgeList() {
                var verifyList = [];
                var duplicate = "";
                for (var i = 0; i < $scope.changeParam.judgeList.length; i++) {
                    if (verifyList.indexOf($scope.changeParam.judgeList[i].judgeName) > -1
                        && duplicate.indexOf($scope.changeParam.judgeList[i].judgeName) < 0) {
                        duplicate = duplicate.concat($scope.changeParam.judgeList[i].judgeName).concat(",");
                    }
                    verifyList.push($scope.changeParam.judgeList[i].judgeName);
                }
                //如果为空,说明无重复则返回true;
                if (!duplicate) {
                    return true;
                }
                //不为空,则提示哪些评委重复,并返回false
                inform.common("评委" + duplicate.substring(0, duplicate.length - 1) + "存在重复,请修改");
                return false;
            }

            //修改信息
            $scope.updateInfo = function () {
                //校验jList中是否存在相同的评委
                if (!verifyJudgeList()) {
                    return;
                }
                var urlData = {
                    'ID': $scope.changeParam.ID,
                    'QUARTER': $scope.changeParam.QUARTER,
                    'REVIEW_LEVEL': $scope.changeParam.REVIEW_LEVEL,
                    'PLAN_COMPLETION_DATE': inform.format($scope.changeParam.PLAN_COMPLETION_DATE, 'yyyy-MM-dd'),
                    'PRODUCT_LINE': $scope.changeParam.productLine,
                    'TEM_NAME': $scope.changeParam.TEM_NAME,
                    'REVIEW_CONTENT': $scope.changeParam.REVIEW_CONTENT,
                    'REVIEW_MEETING_TIME': $scope.changeParam.REVIEW_MEETING_TIME,
                    'REVIEW_RESULT': $scope.changeParam.REVIEW_RESULT,
                    'REVIEW_PROBLEM_NUMBER': $scope.changeParam.REVIEW_PROBLEM_NUMBER,
                    'LIABLE_PERSON': null == $scope.changeParam.LIABLE_PERSON?null:$scope.changeParam.LIABLE_PERSON.join(','),
                    'PROJECT_MANAGER': $scope.changeParam.PROJECT_MANAGER,
                    'PARTICIPANTS': $scope.changeParam.PARTICIPANTS,
                    'IS_TRACK_ONZENTAO': $scope.changeParam.IS_TRACK_ONZENTAO,
                    'PROJECT_ADMINISTRATORS': $scope.changeParam.PROJECT_ADMINISTRATORS,
                    'COMMENTS': $scope.changeParam.COMMENTS,
                    'VERSION': $scope.changeParam.VERSION,
                    'REVIEW_TYPE': $scope.changeParam.REVIEW_TYPE,
                    'REVIEW_THEME': $scope.changeParam.REVIEW_THEME,
                    'DOC_PAGES': $scope.changeParam.DOC_PAGES,
                    'WORKLOAD': $scope.changeParam.WORKLOAD,
                    'REVIEW_EFFICIENCY': $scope.changeParam.REVIEW_EFFICIENCY,
                    'PREREVIEW': $scope.changeParam.PREREVIEW,
                    'judgeList': JSON.stringify($scope.changeParam.judgeList),
                    'pre_review_id':null == $scope.changeParam.pre_review_id?null:$scope.changeParam.pre_review_id.join(',')
                };

                reportService.verifyReviewExist(urlData).then(function (data) {
                    //请求失败则弹出后台返回信息
                    if (data.code !== AgreeConstant.code) {
                        inform.common(data.message);
                        return;
                    }
                    //结果大于0,则提示已存在
                    if (data.data > 0) {
                        inform.common("该评审已存在,请勿重复添加");
                        return;
                    }
                    //为0则进行更新
                    updateReviewInfo(urlData);
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            };

            function updateReviewInfo(urlData) {
                reportService.updateReportInfo(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        layer.confirm(data.message, {
                            title: false,
                            btn: ['确定']
                        }, function (result) {
                            layer.close(result);
                            $state.go('app.office.report_0004', {'flag': 'notMenu'});
                        });
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * 选择项目时，回填产品线项目经理、项目助理信息
             * 根据项目id重新查询预评审列表
             * @param projectId
             */
            function projectChange(projectId,flag){
                reportService.getProjectInfoById(projectId).then(function (data) {
                    if (data.code !== AgreeConstant.code) {
                        inform.common(data.message);
                        return;
                    }
                    data.data = angular.fromJson(data.data);
                    if (data.data.length === 0) {
                        inform.common("该项目不存在")
                    }else{
                        $scope.changeParam.productLineName = data.data.productLineName;
                        $scope.changeParam.productLine = data.data.productLine;
                        $scope.changeParam.PROJECT_MANAGER = data.data.projectManager;
                        $scope.changeParam.PROJECT_ADMINISTRATORS = data.data.projectAssistant;
                    }

                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });

                //若flag存在，则表示为初始化，不置空pre_review_id
                if(!flag){
                    $scope.changeParam.pre_review_id = [];
                }
                reportService.getPreRerviewReviewContentAndId(projectId).then(function(data) {
                    if (data.data) {
                        $scope.preRerviewList = data.data;
                    }
                });
            }


            //修改页面新增一行
            $scope.addBind = function () {
                var judgeVO = {
                    "reviewId": $scope.changeParam.ID,
                    'employeeId':'',
                    "judgeName": "",
                    "judgeGrade": "",
                    "judgeWorkLoad": $scope.changeParam.REVIEW_MEETING_TIME || '',
                    'judgeJoin':'0',
                    'flag': true
                };
                $scope.changeParam.judgeList.push(judgeVO);
                //计算评审工作量,评审效率
                $scope.updateCalWorkLoad();
            };

            /**
             * 修改页面取消一行,若有关联问题,则弹出提示框,否则直接删除
             * @param index
             */
            $scope.deleteBind = function (index) {
                var urlData = {
                    'reviewId': $scope.changeParam.ID,
                    'employeeId':$scope.changeParam.judgeList[index].employeeId,
                    'judge': $scope.changeParam.judgeList[index].judgeName
                };
                reviewProblemService.selectByReviewAndJudge(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        if (data.data === 0) {
                            deleteJudge(index);
                        } else {
                            var deleteModal = $modal.open({
                                templateUrl: 'myModal',
                                controller: 'ModalInstanceCtrl',
                                size: "sm",
                                resolve: {
                                    items: function () {
                                        return "该评委关联了" + data.data + "条问题,\n" +
                                            "确认删除么？";
                                    }
                                }
                            });
                            deleteModal.result.then(function () {
                                deleteJudge(index);
                            })
                        }
                    }
                });

            };

            /**
             * 执行页面的删除评委操作
             * @param index
             */
            function deleteJudge(index) {
                if (index >= 0) {
                    $scope.changeParam.judgeList.splice(index, 1);
                }
                //计算评审工作量,评审效率
                $scope.updateCalWorkLoad();
                //计算评委平均等级
                $scope.calAvgGrade();
            }

            /**
             * 邮件评审-当评委投入工作量修改时触发重新计算评审工作量和效率
             */
            $scope.updateCalWorkLoad = function () {
                var total = 0;
                for (var i = 0; i < $scope.changeParam.judgeList.length; i++) {
                    var workLoad = parseFloat($scope.changeParam.judgeList[i].judgeWorkLoad);
                    if (isFinite(workLoad) && $scope.changeParam.judgeList[i].judgeJoin === '0') {
                        //工作量等于评委工作量的加合值
                        total = total + workLoad;
                    }
                }
                //判断加合结果是否合法,不合法归0,合法保留一位小数
                $scope.changeParam.WORKLOAD = transToNum(total, 1);
                //修改效率
                $scope.efficiencyListener();
            };

            /**
             * 计算   评审效率 = 问题数量/评审工作量
             */
            $scope.efficiencyListener = function () {
                var efficiency = ($scope.changeParam.REVIEW_PROBLEM_NUMBER / $scope.changeParam.WORKLOAD);
                $scope.changeParam.REVIEW_EFFICIENCY = transToNum(efficiency, 1);
            };

            /**
             * 修改：持续时间变化时(说明非邮件)->修改评委投入工作量,更新、评审工作量、评审效率
             */
            $scope.updateDuringChange = function () {
                //评委工作量赋值
                for (var i = 0; i < $scope.changeParam.judgeList.length; i++) {
                    //若评委不参加，直接置为0
                    if($scope.changeParam.judgeList[i].judgeJoin === '1'){
                        //评委工作量是会议时长
                        $scope.changeParam.judgeList[i].judgeWorkLoad = '0';
                    }else {
                        //评委工作量是会议时长
                        $scope.changeParam.judgeList[i].judgeWorkLoad = $scope.changeParam.REVIEW_MEETING_TIME;
                    }
                }
                //随之更新评审工作量和效率
                $scope.updateCalWorkLoad();
            };

            /**
             * 修改：页数发生变化时-->密度改变
             */
            $scope.updateDocPageChange = function () {
                //密度改变了
                $scope.changeParam.QUESTION_DENSITY =
                    transToNum($scope.changeParam.REVIEW_PROBLEM_NUMBER / $scope.changeParam.DOC_PAGES, 2);
            };
            /**
             * 当修改时评委等级输入框光标移出：计算评委平均等级
             */
            $scope.calAvgGrade = function () {
                var total = 0;
                var num = 0;
                for (var i = 0; i < $scope.changeParam.judgeList.length; i++) {
                    var judgeGrade = parseInt($scope.changeParam.judgeList[i].judgeGrade);
                    if (isFinite(judgeGrade)  && $scope.changeParam.judgeList[i].judgeJoin === '0') {
                        total = total + judgeGrade;
                    }
                    //评委人数
                    if ($scope.changeParam.judgeList[i].judgeJoin === '0') {
                        num++;
                    }
                }
                $scope.changeParam.jListNum = num;
                $scope.changeParam.avgGrade = transToNum(total / num, 1);
            };
            /**
             * 当修改评审类型变化为邮件时->更新会议时间->更新评委投入工作量、评审工作量、评审效率
             */
            $scope.reviewTypeChange = function () {
                if ($scope.changeParam.REVIEW_TYPE === '0') {
                    //类型为邮件类型,将会议时间重置
                    $scope.changeParam.REVIEW_MEETING_TIME = '';
                }
                //更新评委投入工作量、评审工作量、评审效率
                $scope.updateDuringChange();
            };

            /**
             *  修改评审时间
             */
            $scope.updateDateOne = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.updateTimeOne = true;
            };

            /**
             * 转化NaN和Infinity为0.0
             *
             * 判断入参str是否合法,不合法归0,合法保留num位小数
             * isNaN方法是判断是不是一个非数字值，若是，则返回true,否则返回false
             * isFinite方法是判断是不是无穷数，参数不是NaN,infinity,-infinity时返回true
             */
            function transToNum(str, num) {
                if (!isFinite(str)) {
                    return 0.0;
                } else {
                    return str.toFixed(num);
                }
            }

            /**
             * 当会议结果变化为未评审时
             */
            $scope.reviewResultChange = function (item) {
                //若选项不为“为评审”，直接返回
                if (item !== '3') {
                    $scope.planCompletionDatFlag = false;
                    return;
                }

                $scope.planCompletionDatFlag = true;
                $scope.changeParam.REVIEW_MEETING_TIME = '0';
                $scope.changeParam.REVIEW_PROBLEM_NUMBER = '0';
                $scope.changeParam.DOC_PAGES = '0';
                $scope.changeParam.REVIEW_TYPE = '1';
                $scope.updateDuringChange();
            };

            /**
             * 评委是否参与选项改变后触发的事件
             */
            $scope.judgeJoinChange = function(item){
                //状态为参加
                if(item.judgeJoin === '1'){
                    item.judgeWorkLoad = '0';
                }else{
                    //若为会议类型，则为工作量复制，否则清空
                    if($scope.changeParam.REVIEW_TYPE !== '0'){
                        item.judgeWorkLoad = $scope.changeParam.REVIEW_MEETING_TIME
                    }else{
                        item.judgeWorkLoad = '';
                    }
                }
                //计算平均等级
                $scope.calAvgGrade();
                //计算平均工作量
                $scope.updateCalWorkLoad();
            };


            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();
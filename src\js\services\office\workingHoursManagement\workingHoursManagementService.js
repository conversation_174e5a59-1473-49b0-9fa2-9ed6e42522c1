/*
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date:   2019-01-10 15:20:05
 * @Last Modified by:   l<PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
    'use strict';
  app.factory('workingHoursManagementService', workingHoursManagementService);
  workingHoursManagementService.$inject=["HttpService",'$rootScope'];

  function workingHoursManagementService(HttpService,$rootScope){
    var service={
    	getData: getData
    };
    return service;
    /**
     * 汇总查询
     */
    function getData(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'workingHoursManagement/getData', urlData);
    }
  }
})();

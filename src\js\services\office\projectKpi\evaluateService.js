(function() {
    'use strict';
    app.factory('evaluateService', evaluateService);
    evaluateService.$inject = [ "HttpService", '$rootScope' ];

    function evaluateService(HttpService, $rootScope) {
        var service = {
            getEvaluateData:getEvaluateData,
            getDetailData:getDetailData,
            updateEvaluateData:updateEvaluateData,
            referenceData:referenceData,
            getCodeSonarInfo:getCodeSonarInfo
        };
        return service;

        /**
         * 获取kpi管理信息
         */
        function getEvaluateData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'evaluatePerson/getEvaluateData', urlData);
        }
        /**
         * 评价人维护
         */
        function getDetailData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'evaluatePerson/getDetailData', urlData);
        }
        /**
         * 保存评价详情
         */
        function updateEvaluateData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'evaluatePerson/updateEvaluateData', urlData);
        }
        /**
         * 过程质量评价获取参考信息
         */
        function referenceData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'evaluatePerson/referenceData', urlData);
        }
        /**
         * 代码质量获取sonar分数信息
         */
        function getCodeSonarInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'evaluatePerson/getCodeSonarInfo', urlData);
        }
    }
})();

(function() {
    'use strict';
  app.factory('companyProjectModule', companyProjectModule);
  companyProjectModule.$inject=["companyProjectManagementService","comService","AgreeConstant","inform","Trans"];

  function companyProjectModule(companyProjectManagementService,comService,AgreeConstant,inform,Trans){
    var service={
		 initModule:initModule
    };
    return service;

    //回调方法
    var companyProjectModuleBack = null;
    //scope
    var companyProjectModuleScope = null;
    //项目状态
    var projectStatus = '';
    /**
     * 初始弹框
     *  参数结构
     * dataInfo= {companyProjectName:'立项项目名称' }
     */
    function initModule(dataInfo,scope,callback) {
        if(dataInfo.projectName){
            document.getElementById("companyProjectName").value=dataInfo.projectName;
        }
        if(dataInfo.projectStatus){
            projectStatus=dataInfo.projectStatus;
        }
    	//查产品线
		scope.productLineList = [];
		//获取立项项目
		scope.getCompanyProject = getCompanyProject;
		//选择立项项目
        scope.selectCompanyProject = selectCompanyProject;
        //选中产品线
        scope.selectProductLine =selectProductLine;
        //回调方法
        companyProjectModuleBack = callback;
        companyProjectModuleScope = scope;

        comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function (data) {
			if (data.data) {
			    scope.productLineList = data.data;
			    getCompanyProject(dataInfo);
			    $("#companyProject").click();
			}
		});
    }

    /**
     * 选中产品线与项目触发的事件
     */
     function selectProductLine(productLine){
           var paramInfo={
                'productLine':productLine,
                'projectName':document.getElementById("companyProjectName").value,
                'projectStatus':projectStatus
            }
         getCompanyProject(paramInfo);
     }
    /**
     * 根据产品线查询立项项目
     */
     function getCompanyProject(dataInfo){
     //修改被选中的字体颜色
        companyProjectModuleScope.productLineList.forEach(function (ele) {ele.active=ele.param_code===dataInfo.productLine;})
        companyProjectModuleScope.companyProjectModuleData = [];
        //获取立项项目列表
		companyProjectManagementService.getCompanyProjectInfoList(dataInfo).then(function(data) {
			if (data.code===AgreeConstant.code) {
                //项目
                companyProjectModuleScope.companyProjectModuleData = data.data.list;
     			//设置焦点
                setTimeout(setFocus,500);
			} else {
				inform.common(data.message);
			}
		},
		function(error) {
			inform.common(Trans("tip.requestError"));
		});
     }
     function setFocus(){
        document.getElementById("companyProjectName").focus()
     }
     /**
      * 选择立项项目
      */
      function selectCompanyProject(m){
        $("#companyProjectModule").modal("hide");
        companyProjectModuleBack(m);
      }
  }
})();
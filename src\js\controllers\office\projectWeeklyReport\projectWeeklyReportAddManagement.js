//# sourceURL=js/controllers/office/projectWeeklyReport/projectWeeklyReportAddManagement.js
(function () {
    app.controller("projectWeeklyReportAddManagement", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','projectWeeklyReportService','inform','Trans','AgreeConstant','LocalCache',
        function ($rootScope,comService,$scope,$state,$stateParams, $modal,projectWeeklyReportService,inform,Trans,AgreeConstant,LocalCache) {
       	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	$scope.limitList = AgreeConstant.limitList; // 正则校验配置
    	$scope.paramInfo = {
    		time: inform.format(new Date(), 'yyyy-MM-dd')
    	};
        //初始化当前时间
        $scope.datepicker = {
        	currentDate : new Date()
        };
        
        $scope.type = $stateParams.type;
        $scope.paramInfo.projectId = $stateParams.projectId;

        if ($scope.type === "up"){
        	//获取缓存
        	$scope.paramInfo = LocalCache.getObject('projectWeeklyReport_item');
        	$scope.weekInfo = $scope.paramInfo.year+"年第"+$scope.paramInfo.week+"周周报   "+$scope.paramInfo.oneWeek;
        }else{
            $scope.week = inform.getWeek($scope.paramInfo.time);
            $scope.year = inform.getWeekDay($scope.paramInfo.time,7,"yyyy-MM-dd").substr(0,4);
            $scope.paramInfo.oneWeek = inform.getWeekDay($scope.paramInfo.time,1,"yyyy-MM-dd") +"--"+inform.getWeekDay($scope.paramInfo.time,7,"yyyy-MM-dd");
            $scope.weekInfo = $scope.year+"年第"+$scope.week +"周周报";
            selLastWeekProgress();
        }
        //上周的记录id
        var id = '';

        $scope.sum = sum;
        sum();




		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
		 /**
		  * 记录时间
		  */
		$scope.openDateStart = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.openedStart = true;    
		};
		/**
		 * 修改时间时，确认是否覆盖值
		 */
		$scope.sureLastWeekProgress = function() {

		   if($scope.type === "up"){
                return;
           }
           $scope.week = inform.getWeek($scope.paramInfo.time);
           $scope.year = inform.getWeekDay($scope.paramInfo.time,7,"yyyy-MM-dd").substr(0,4);
           $scope.paramInfo.oneWeek = inform.getWeekDay($scope.paramInfo.time,1,"yyyy-MM-dd") +"--"+inform.getWeekDay($scope.paramInfo.time,7,"yyyy-MM-dd");
           $scope.weekInfo = $scope.year+"年第"+$scope.week +"周周报";
           getPerson();
		};

		//设置一周开始时间 默认周天0
        $scope.dateOptions ={
            startingDay:1
        };

		/**
		 * 获取上周进展
		 */
		function selLastWeekProgress() {
			if($scope.type === "up"){
				return;
			}
			var urlData = {
			   'projectId': $scope.paramInfo.projectId
		    };
			 projectWeeklyReportService.selLastWeekProgress(urlData).then(function (data) {
	               if (data.code === AgreeConstant.code) {
	            	   
	            	   if (data.data==null){
	            		   data = {
	            				data:{}
	            		   };
	            		   return;
	            	   }
	            	   id = (data.data.id!==undefined)?data.data.id:'';
	            	   $scope.week = inform.getWeek(data.data.oneWeek);
                       $scope.year = inform.getWeekDay(data.data.oneWeek,7,"yyyy-MM-dd").substr(0,4);
                       $scope.paramInfo.oneWeek = inform.getWeekDay(data.data.oneWeek,1,"yyyy-MM-dd") +"--"+inform.getWeekDay(data.data.oneWeek,7,"yyyy-MM-dd");
                       $scope.paramInfo.time = data.data.oneWeek;
	            	   $scope.paramInfo.lastWeekProgress = judeNull(data.data.lastWeekProgress, 'lastProgress', '上周未填写计划');
	            	   $scope.paramInfo.recentPlan = judeNull(data.data.recentPlan, 'recentPlan', '上周未填写近期工作计划');
	            	   getPerson();
	               } else {
	                   inform.common("获取上周进展失败");
	               }
	           }, function (error) {
	               inform.common(Trans("tip.requestError"));
	           });
		}
		 /**
         * 判断是否上周存在此内容
         */
        function judeNull(data, id, msg){
        	if (data==null){
     		   $("#"+ id).attr('placeholder',msg);
     		  return "";
     	   } else {
     		   return data
     	   }
        }
        /**
         * 计算总投入人力
         */
        function sum(){
        	$scope.paramInfo.rdInput = $scope.paramInfo.rdInput?$scope.paramInfo.rdInput:'0';
        	$scope.paramInfo.managementInput = $scope.paramInfo.managementInput?$scope.paramInfo.managementInput:'0';
        	$scope.paramInfo.testInput = $scope.paramInfo.testInput?$scope.paramInfo.testInput:'0';
        	$scope.paramInfo.assistantInput = $scope.paramInfo.assistantInput ?$scope.paramInfo.assistantInput :'0';
        	$scope.paramInfo.frameWorkInput = $scope.paramInfo.frameWorkInput?$scope.paramInfo.frameWorkInput:'0';
        	$scope.paramInfo.sumInput = $scope.paramInfo.rdInput*1+$scope.paramInfo.managementInput*1
        								+$scope.paramInfo.testInput*1+$scope.paramInfo.assistantInput*1
        								+$scope.paramInfo.frameWorkInput*1;
        }
        /**
         * 获取默认显示人数
         */
        function getPerson(){
            $scope.paramInfo.rdInput=0;
            $scope.paramInfo.managementInput=0;
            $scope.paramInfo.testInput=0;
            $scope.paramInfo.assistantInput=0;
            $scope.paramInfo.frameWorkInput=0;
            var urlData = {
                'projectId': $scope.paramInfo.projectId,
                'startTime':inform.getWeekDay($scope.paramInfo.time,1,"yyyy-MM-dd"),
                'endTime':inform.getWeekDay($scope.paramInfo.time,7,"yyyy-MM-dd")
            };
            projectWeeklyReportService.getPerson(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                	$scope.personMap = data.data;
                	angular.forEach($scope.personMap, function(one, i) {
              		  if(one.title_code === "2"){
              			$scope.paramInfo.rdInput = one.input;
              		  }
              		  if(one.title_code === "3"){
              			$scope.paramInfo.testInput = one.input;
              		  }
              		  if(one.title_code === "1"){
              			$scope.paramInfo.managementInput = one.input;
              		  }
              		  if(one.title_code === "4"){
               			$scope.paramInfo.assistantInput = one.input;
               		  }
              		  if(one.title_code === "5"){
               			$scope.paramInfo.frameWorkInput = one.input;
               		  }
          	       });
                	sum();
                } else {
                    inform.common("获取项目人员失败");
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        }
       /**
        * 保存信息
        */
       $scope.saveInfo = function(){
    	   if($scope.type==null && $scope.paramInfo.time==null){
    		   inform.common("请选择记录时间");
    	   }
           var urlData = {
               'projectId': $scope.paramInfo.projectId,
               'time': $scope.paramInfo.time,//记录时间
               'rdInput': $scope.paramInfo.rdInput,//开发人力
               'managementInput': $scope.paramInfo.managementInput,//管理人力
               'assistantInput': $scope.paramInfo.assistantInput,//助理人力
               'frameWorkInput': $scope.paramInfo.frameWorkInput,//架构师人力
               'testInput': $scope.paramInfo.testInput,//测试人力
               'recentPlan':$scope.paramInfo.recentPlan,//近期计划
               'lastWeekProgress': $scope.paramInfo.lastWeekProgress,//上周进展
               'thisWeekPlan': $scope.paramInfo.thisWeekPlan,//本周计划
               'lastWeekPlan':id,//上周的记录id号
               'year': $scope.year,
               'week': $scope.week
           };
           //新增
           if ($scope.type==null){
        	   var modalInstance = $modal.open({
                   templateUrl: 'myModalContent.html',
                   controller: 'ModalInstanceCtrl',
                   size: "md",
                   resolve: {
                       items: function () {
                           return Trans("是否确定添加 "+$scope.paramInfo.oneWeek+":\n"+$scope.year+"年 第"+$scope.week+"周周报");
                       }
                   }
               });
               modalInstance.result.then(function () {
            	   addInfo(urlData);
               });
           } else {
           //修改 
          	 upInfo(urlData)
          }
       };
       /**
        * 添加信息
        */
       function addInfo(urlData) {
    	   projectWeeklyReportService.addInfo(urlData).then(function (data) {
              callBackFunction(data);
           }, function (error) {
               inform.common(Trans("tip.requestError"));
           });
        }
        /**
         * 修改信息
         * @param urlData
         */
        function upInfo(urlData) {
        	urlData.id =  $scope.paramInfo.id;
            urlData.week = $scope.paramInfo.week;
            urlData.year = $scope.paramInfo.year;
        	projectWeeklyReportService.updateInfo(urlData).then(function (data) {
                callBackFunction(data);
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
         }

         function callBackFunction(data){
            if (data.code === AgreeConstant.code) {
                layer.confirm(data.message,{
                    title:false,
                    btn:['确定']
                },function(result){
                    layer.close(result);
                    $scope.back();
                });
            } else {
                inform.common(data.message);
            }
         }
         /**
          * 返回
          */
         $scope.back = function(){
        	$state.go("app.office.projectWeeklyDetail", {projectId: $scope.paramInfo.projectId});
         };
        	 
	 	/**
	 	 * *************************************************************
	 	 *              方法声明部分                                 结束
	 	 * *************************************************************
	 	 */	
		
	}]);
})();
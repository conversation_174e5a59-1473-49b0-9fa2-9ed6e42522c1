//# sourceURL=js/controllers/office/projectWeeklyReport/projectWeeklyReportManagement.js
(function () {
    app.controller("projectWeeklyReportManagement", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','projectWeeklyReportService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,projectWeeklyReportService,inform,Trans,AgreeConstant,LocalCache,$http) {
       	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		//初始化分页数据
    	$scope.pages = inform.initPages();
        //窗体大小变化时重新计算高度
        $(window).resize(setDivHeight);
        $scope.projectId = $stateParams.projectId;
        $scope.getData = getData; 			// 分页相关函数    
        getData();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
        /**
         * 设置列表的高度
         */
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (150 + 185);
            $("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight);
        }
    	/**
         * 获取本项目的周报项目信息
         */
        function getData(pageNum) {
            var urlData = {
                'projectId': $scope.projectId,//项目id
                'page': pageNum,//当前页数
                'pageSize': $scope.pages.size//每页显示条数
            };
            projectWeeklyReportService.selectData(urlData).then(function (data) {
                  //项目详情
                  $scope.tableData = data.data.list;
                  if ($scope.tableData.length === 0) {
                      $scope.pages = inform.initPages();
                  } else {
                	  angular.forEach($scope.tableData, function(one, i) {
                		  var sum = 0;
                		  sum = one.rdInput*1 + one.managementInput*1 + one.testInput*1 + one.assistantInput*1 + one.frameWorkInput*1;
                		  one.inputPerson = one.inputPerson + ";\n总投入人力:" + sum+"。";
            	       });
                      //分页信息设置
                      $scope.pages.total = data.data.total;
                      $scope.pages.star = data.data.startRow;
                      $scope.pages.end = data.data.endRow;
                      $scope.pages.pageNum = data.data.pageNum;
                      //设置列表的高度
                      setDivHeight();
                  }
             },
             function () {
                 inform.common(Trans("tip.requestError"));
             });
        }
        /**
         * 修改
         */
        $scope.up = function(m) {
        	LocalCache.setObject('projectWeeklyReport_item',m);
			$state.go("app.office.projectWeeklyReportAddManagement", {projectId: $scope.projectId,type:"up"});
        }
        //设置一周开始时间 默认周天0
        $scope.dateOptions ={
            startingDay:1
        }
        /**
         * 新增
         */
        $scope.add = function() {
			$state.go("app.office.projectWeeklyReportAddManagement", {projectId: $scope.projectId,type:null});
        }
        /**
         * 删除弹框
         */ 
        $scope.del = function (m) {
            var modalInstance = $modal.open({
                templateUrl: 'myModalContent.html',
                controller: 'ModalInstanceCtrl',
                size: "sm",
                resolve: {
                    items: function () {
                        return Trans("common.deleteTip");
                    }
                }
            });
            modalInstance.result.then(function () {
                if (m) {
                    $scope.delete(m);
                }
            });
        };	 
        /**
         * 删除信息
         */
        $scope.delete = function (m) {
            var urlData = {
                'id': m.id
            };
            projectWeeklyReportService.delData(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    layer.confirm(data.message, {
                        title: false,
                        btn: ['确定']
                    }, function (result) {
                        layer.close(result);
                        getData(1);
                    });
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        };
	 	/**
	 	 * *************************************************************
	 	 *              方法声明部分                                 结束
	 	 * *************************************************************
	 	 */	
		
	}]);
})();
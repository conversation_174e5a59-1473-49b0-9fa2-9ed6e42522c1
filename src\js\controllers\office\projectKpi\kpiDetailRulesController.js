(function () {
    'use strict';
    app.controller("kpiDetailRulesController", ['$scope','$state','comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','kpiInfoService','$stateParams','LocalCache', '$modal','$http',
        function ($scope,$state,comService, $rootScope, inform, Trans, AgreeConstant,kpiInfoService,$stateParams,LocalCache, $modal,$http) {

		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		 // 上方项目信息
        $scope.topInfo = LocalCache.getObject('kpiInfo_topInfo');
        // 一级指标与二级指标数据
        $scope.kpiInfo = LocalCache.getObject('kpiDetails_kpiInfo');
        // --------------------------------------所有指标回调方法定义----------------------------
        var returnFunctionList = {
            kpi001Details:kpi001Details,
            kpi002Details:kpi002Details,
            kpi003Details:kpi003Details,
            kpi004Details:kpi004Details,
            kpi101Details:kpi101Details,
            kpi102Details:kpi102Details,
            kpi103Details:kpi103Details
        }
        getThirdKpi();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */
        /**
		 * 获取指标数据支撑（三级指标）
		 */
        function getThirdKpi(){
            var urlData = {
                'kpiId': $stateParams.kpiId,
                'firstKpiCode': $stateParams.firstKpiCode
            };
            kpiInfoService.getThirdKpi(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    //查询三级级指标接口，根据传进来的returnFunction调用List中的对应方法
                    returnFunctionList[$stateParams.returnFunction](data.data);
                } else {
                    inform.common(data.message);
                }
            },
            function () {
                inform.common(Trans("tip.requestError"));
            });
        }
        /**
         * 项目交付能力详情
         */
        function kpi001Details(data){
            $scope.kpiInfo.kpi0011 = data[0].dataDetail.split("=")[1];
            $scope.kpiInfo.kpi0012 = data[1].dataDetail.split(",")[2].split("=")[1];
            $scope.kpiInfo.kpi0013 = data[1].dataDetail.split(",")[1].split("=")[1];
            $scope.kpiInfo.kpi0014 = data[1].dataDetail.split(",")[0].split("=")[1];
        }
        /**
         * 项目交付质量详情
         */
        function kpi002Details(data){
            $scope.kpiInfo.kpi0021 = data[0].dataDetail.split("=")[1];
            $scope.kpiInfo.kpi0022 = data[0].realityVal.split("/")[0];
            $scope.kpiInfo.kpi0023 = data[0].realityVal.split("/")[1];
            $scope.kpiInfo.kpi0024 = data[1].dataDetail.split(",")[0].split("=")[1];
            $scope.kpiInfo.kpi0025 = data[1].dataDetail.split(",")[1].split("=")[1];
            $scope.kpiInfo.kpi0026 = data[1].dataDetail.split(",")[2].split("=")[1];
            $scope.kpiInfo.kpi0027 = data[1].dataDetail.split(",")[3].split("=")[1];
            $scope.kpiInfo.kpi0028 = data[1].dataDetail.split(",")[4].split("=")[1];
        }
        /**
         * 项目交付过程质量详情
         */
        function kpi003Details(data){
            $scope.kpiInfo.kpi0039 = data[0].dataDetail.split(",")[0].split("=")[1];
            $scope.kpiInfo.kpi00310 = data[0].dataDetail.split(",")[1].split("=")[1];
            $scope.kpiInfo.kpi00311 = data[0].dataDetail.split(",")[2].split("=")[1];

            $scope.kpiInfo.kpi0031 = data[1].dataDetail.split(",")[0].split("=")[1];
            $scope.kpiInfo.kpi0032 = data[1].dataDetail.split(",")[1].split("=")[1];
            $scope.kpiInfo.kpi0033 = data[1].dataDetail.split(",")[2].split("=")[1];
            $scope.kpiInfo.kpi0034 = data[1].dataDetail.split(",")[3].split("=")[1];
            $scope.kpiInfo.kpi0035 = data[2].dataDetail.split(",")[0].split("=")[1];
            $scope.kpiInfo.kpi0036 = data[2].dataDetail.split(",")[1].split("=")[1];
            $scope.kpiInfo.kpi0037 = data[2].dataDetail.split(",")[2].split("=")[1];
            $scope.kpiInfo.kpi0038 = data[2].dataDetail.split(",")[7].split("=")[1];

            $scope.kpiInfo.kpi00312 = data[2].dataDetail.split(",")[3].split("=")[1];
            $scope.kpiInfo.kpi00313 = data[2].dataDetail.split(",")[4].split("=")[1];
            $scope.kpiInfo.kpi00314 = data[2].dataDetail.split(",")[5].split("=")[1];
            $scope.kpiInfo.kpi00315 = data[2].dataDetail.split(",")[6].split("=")[1];
        }

        /**
         * 项目成本控制详情
         */
        function kpi004Details(data){
            $scope.kpiInfo.kpi0041 = data[0].dataDetail.split(",")[0].split("=")[1];
            $scope.kpiInfo.kpi0042 = data[0].dataDetail.split(",")[1].split("=")[1];
            $scope.kpiInfo.kpi0043 = data[0].dataDetail.split(",")[2].split("=")[1];
            $scope.kpiInfo.kpi0044 = data[0].dataDetail.split(",")[3].split("=")[1];
        }

        /**
         * 团队交付能力详情
         */
        function kpi101Details(data){
            $scope.kpiInfo.kpi1011 = data[0].dataDetail.split(",")[3].split("=")[1];
            $scope.kpiInfo.kpi1012 = data[0].dataDetail.split(",")[0].split("=")[1];
            $scope.kpiInfo.kpi1019 = data[0].dataDetail.split(",")[1].split("=")[1];
            $scope.kpiInfo.kpi10110 = data[0].dataDetail.split(",")[2].split("=")[1];

            $scope.kpiInfo.kpi1013 = data[1].dataDetail.split(",")[0].split("=")[1];
            $scope.kpiInfo.kpi1014 = data[1].dataDetail.split(",")[1].split("=")[1];
            $scope.kpiInfo.kpi1015 = data[1].dataDetail.split(",")[2].split("=")[1];
            $scope.kpiInfo.kpi1016 = data[1].dataDetail.split(",")[3].split("=")[1];
            $scope.kpiInfo.kpi1017 = data[1].dataDetail.split(",")[4].split("=")[1];
            $scope.kpiInfo.kpi1018 = data[1].dataDetail.split(",")[5].split("=")[1];
        }
        /**
         * 团队交付质量详情
         */
        function kpi102Details(data){
            $scope.kpiInfo.kpi1021 = data[0].dataDetail.split("=")[1];
            $scope.kpiInfo.kpi1022 = data[0].realityVal.split("/")[0];
            $scope.kpiInfo.kpi1023 = data[0].realityVal.split("/")[1];
        }
        /**
         * 团队交付过程质量详情
         */
        function kpi103Details(data){
            $scope.kpiInfo.kpi1031 = data[0].dataDetail.split(",")[0].split("=")[1];
            $scope.kpiInfo.kpi1032 = data[0].dataDetail.split(",")[1].split("=")[1];
            $scope.kpiInfo.kpi1033 = data[0].dataDetail.split(",")[2].split("=")[1];

            $scope.kpiInfo.kpi1034 = data[2].dataDetail.split(",")[0].split("=")[1];
            $scope.kpiInfo.kpi1035 = data[2].dataDetail.split(",")[1].split("=")[1];
            $scope.kpiInfo.kpi1036 = data[2].dataDetail.split(",")[2].split("=")[1];
            $scope.kpiInfo.kpi1037 = data[2].dataDetail.split(",")[3].split("=")[1];

            $scope.kpiInfo.kpi1038 = data[3].dataDetail.split(",")[0].split("=")[1];
            $scope.kpiInfo.kpi1039 = data[3].dataDetail.split(",")[1].split("=")[1];
            $scope.kpiInfo.kpi10310 = data[3].dataDetail.split(",")[2].split("=")[1];
            $scope.kpiInfo.kpi10311 = data[3].dataDetail.split(",")[7].split("=")[1];

            $scope.kpiInfo.kpi10312 = data[3].dataDetail.split(",")[3].split("=")[1];
            $scope.kpiInfo.kpi10313 = data[3].dataDetail.split(",")[4].split("=")[1];
            $scope.kpiInfo.kpi10314 = data[3].dataDetail.split(",")[5].split("=")[1];
            $scope.kpiInfo.kpi10315 = data[3].dataDetail.split(",")[6].split("=")[1];
        }

	     /**
          * *************************************************************
          *              方法声明部分                                 结束
          * *************************************************************
          */
         }
    ]);
})();
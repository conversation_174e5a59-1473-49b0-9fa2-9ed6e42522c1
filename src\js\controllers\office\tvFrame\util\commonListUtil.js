(function () {
    'use strict';
    app.factory('listUtil', listUtil);

    listUtil.$inject=["$location", "tvService"];


    function listUtil($location, tvService) {
        return {
            init: init,
            setCss: setCss,
            getCurrentQuarter: getCurrentQuarter,
            getCurrentYear: getCurrentYear,
            getLastYear: getLastYear,
            getData: getData,
        };

        /**
         * *************************************************************
         *             初始化部分                                 开始
         * *************************************************************
         */
        function init() {
            // 捕捉鼠标事件并反馈给父页面
            document.addEventListener('click',function () {
                $('#iframeDiv',window.parent.document).click();
            },false);
            // 参数
        }
        /**
         * *************************************************************
         *             初始化部分                                 结束
         * *************************************************************
         */

        /*
         * 评审贡献榜和评审效率榜设置css样式
         */
        function setCss() {
            //网页可见区域宽度
            let clientWidth = document.body.clientWidth;
            var img = $('.myavatar');
            img.css({'width': parseInt(clientWidth / 26)});
            img.css({'height': parseInt(clientWidth / 21)});
            $('.rightInfo').css({'padding-left': parseInt(clientWidth / 71)});
            $('.nameAndDept').css({'font-size': parseInt(clientWidth / 91)});
            var message = $('.message');
            message.css({'font-size': parseInt(clientWidth / 106)});
            var star = $('.star');
            star.css({'font-size': parseInt(clientWidth / 109)});
            var questionCount = $('.questionCount');
            questionCount.css({'font-size': parseInt(clientWidth / 62)});
        }
        /*
         * 获取当前季度
         */
        function getCurrentQuarter() {
            var today = new Date();
            var month = today.getMonth() + 1 - 3;
            if (month >= 1 && month <= 3) {
                return '一';
            } else if (month >= 4 && month <= 6) {
                return '二';
            } else if (month >= 7 && month <= 9) {
                return '三';
            } else {
                return '四';
            }
        }
        /*
         * 获取当前年份
         */
        function getCurrentYear() {
            return new Date()
        }

        function getLastYear() {
            var dateTime = new Date().getFullYear();
            return new Date(new Date().setFullYear(dateTime - 1));
        }

        function getData(urlData) {
            return tvService.getReviewContributionData(urlData);
        }
    }
})();

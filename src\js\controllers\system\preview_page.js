/*
 * @Author: fubaole
 * @Date:   2018-01-18 09:48:43
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-29 17:43:22
 */

(function() {
  'use strict';
  angular.module('app')
    .controller("preview_page", ['$rootScope', '$scope', 'inform', '$stateParams', '$state', 'LocalCache', 'Trans', 'SystemService','AgreeConstant',
      function($rootScope, $scope, inform, $stateParams, $state, LocalCache, Trans, SystemService,AgreeConstant) {

        $scope.getClass = getClass; // 设置区块宽度
        $scope.reload = reload; // 重新加载
        getData();

        // 获取配置信息
        function getData() {
          SystemService.getPageByPageId($stateParams.pageId)
            .then(function(data) {
              if (data.code===AgreeConstant.resultCode) {
                // 块
                $scope.blocks = data.result.rowList[0].containerList[0].blockContentList;
              } else {
                inform.common(data.message);
              }
            }, function(error) {
              inform.common(Trans("tip.requestError"));
            });
        }

        // 占屏比例
        function getClass(str) {
          var classname = '';
           switch (str) {
            case AgreeConstant.widthTwoOne:
              classname = 'col-sm-6'; // 1/2
              break;
            case AgreeConstant.widthThreeOne:
              classname = 'col-sm-4'; // 1/3
              break;
            case AgreeConstant.widthTwoThree:
              classname = 'col-sm-8'; // 2/3
              break;
            case AgreeConstant.widthFourOne:
              classname = 'col-sm-3'; // 1/4
              break;
            case AgreeConstant.widthThreeFour:
              classname = 'col-sm-9'; // 3/4
              break;
            default:
              classname = 'col-sm-12'; // 1
          }
          return classname;
        }

        // 重载
      function reload(id){
        $scope.$broadcast('reload', id);
      }

      }
    ]);
})();
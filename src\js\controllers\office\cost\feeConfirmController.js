(function () {
  app.controller('feeConfirmController', [
    'costInputService',
    'companyProjectManagementService',
    'plmModule',
    'companyProjectModule',
    'hardwareModeModule',
    'customerModule',
    'productTypeNameModule',
    '$rootScope',
    'comService',
    '$scope',
    '$state',
    '$stateParams',
    '$modal',
    'customerStoryService',
    'inform',
    'Trans',
    'AgreeConstant',
    'LocalCache',
    '$http',
    function (
      costInputService,
      companyProjectManagementService,
      plmModule,
      companyProjectModule,
      hardwareModeModule,
      customerModule,
      productTypeNameModule,
      $rootScope,
      comService,
      $scope,
      $state,
      $stateParams,
      $modal,
      customerStoryService,
      inform,
      Trans,
      AgreeConstant,
      LocalCache,
      $http
    ) {
      /**
       * *************************************************************
       *             初始化部分                                 开始
       * *************************************************************
       */
      initInfo();
      //默认公司已立项
      $scope.checkedNoProject = false;
      $scope.dataFlag = false;
      /**
       * *************************************************************
       *              初始化部分                                 结束
       * *************************************************************
       */

      /**
       * *************************************************************
       *              方法声明部分                                 开始
       * *************************************************************
       */
      /**
       * 选择条件
       */
      $scope.radioSelect = function (item) {
        if (item === 'companyProject') {
          $scope.checkedNoProject = false;
          $scope.dataFlag = false;
        } else {
          $scope.checkedCompanyProject = false;
          $scope.dataFlag = true;
          $scope.item.companyProjectId = null;
          $scope.item.companyProject = null;
        }
      };
      /**
       * 初始化
       */
      function initInfo() {
        $scope.item = {};
        //获取项目归属
        $scope.projectAttributionList = [];
        $scope.projectAttributionMap = {};
        comService.queryEffectiveParam('WEEKLY_REPORT', 'AFFILIATION').then(function (data) {
          $scope.projectAttributionList = data.data;
          angular.forEach($scope.projectAttributionList, function (item) {
            $scope.projectAttributionMap[item['paramCode']] = item['paramValue'];
          });
        });
        //获取行业
        $scope.professionListCustomer = ['金融', '物流', '新零售', '新兴'];
        //产品线、产品分类与产品名称
        $scope.productLineAndType = [];
        $scope.productLineAndTypeMap = {};
        comService.getParamList('PRODUCT_TYPE', '').then(function (data) {
          if (data.data) {
            $scope.productLineAndType = data.data;
            angular.forEach($scope.productLineAndType, function (item) {
              $scope.productLineAndTypeMap[item['param_code']] = item['param_value'];
            });
          }
        });
        //如果该项目存在对应的立项信息
        if ($stateParams.companyProjectId != null) {
          var urlData = {
            projectId: $stateParams.companyProjectId, //公司项目ID
            page: 1,
            pageSize: 1,
          };
          companyProjectManagementService.getCompanyProjectInfoList(urlData).then(
            function (data) {
              if (data.code === AgreeConstant.code) {
                //项目
                $scope.companyProjectData = data.data.list;
                if ($scope.companyProjectData.length === 0) {
                  inform.common(Trans('tip.noData'));
                } else {
                  //公司项目赋值
                  setCompanyProjectInfo($scope.companyProjectData[0]);
                }
              } else {
                inform.common(data.message);
              }
            },
            function (error) {
              inform.common(Trans('tip.requestError'));
            }
          );
        }
      }
      /**
       * 客户
       */
      $scope.initCustomerModule = function () {
        var data = {
          keyWord: $scope.item.customer,
        };
        customerModule.initModule(data, $scope, setCustomerInfo);
      };
      /**
       * 根据所选中的客户回填信息
       */
      function setCustomerInfo(data) {
        $scope.item.customer = data.name;
        $scope.item.customerId = data.id;
        $scope.item.profession = data.profession.split(',')[0];
      }
      /**
       * 公司立项项目
       */
      $scope.initCompanyProjectModule = function () {
        var data = {
          projectName: '',
        };

        companyProjectModule.initModule(data, $scope, setCompanyProjectInfo);
      };
      /**
       * 根据所选中的公司立项项目回填信息
       */
      function setCompanyProjectInfo(data) {
        $scope.item.companyProject = data.projectName;
        $scope.item.companyProjectId = data.projectId;
        $scope.item.hardwareModeNo = data.hardwareModeNo;
        $scope.item.customer = data.customerName;
        $scope.item.customerId = data.customerId;
        $scope.item.profession = data.xqsshy.split(',')[0];
        $scope.item.projectAttribution = data.xmgs;
        $scope.item.productLine = data.productLine;
        $scope.item.productType = data.productType;
        $scope.item.productSubType = data.productSubType;
        if ($scope.productLineAndTypeMap[$scope.item.productLine]) {
          $scope.item.product = $scope.productLineAndTypeMap[$scope.item.productLine];
        }
        if ($scope.productLineAndTypeMap[$scope.item.productType]) {
          $scope.item.product = $scope.item.product + '、' + $scope.productLineAndTypeMap[$scope.item.productType];
        }

        $scope.item.product =
          $scope.item.product +
          '、' +
          ($scope.item.productSubType ? $scope.productLineAndTypeMap[$scope.item.productSubType] : '');
      }
      /**
       * 型号
       */
      $scope.initHardwareModeModule = function () {
        var data = {
          productLine: $scope.item.productLine,
          productType: $scope.item.productType,
          productName: $scope.item.productSubType,
          hardwareModeNo: $scope.item.hardwareModeNo,
        };
        hardwareModeModule.initModule(data, $scope, setHardwareModeInfo);
      };
      /**
       * 根据所选中的硬件型号回填信息
       */
      function setHardwareModeInfo(data) {
        $scope.item.hardwareModeNo = data.name;
      }
      /**
       * 产品类别-名称
       */
      $scope.initProductTypeNameModule = function () {
        var data = {
          productLine: $scope.item.productLine,
          productType: $scope.item.productType,
          productName: $scope.item.productSubType,
        };
        productTypeNameModule.initModule(data, $scope, setProductTypeNameInfo);
      };
      /**
       * 根据所选中的产品回填信息
       */
      function setProductTypeNameInfo(data) {
        $scope.item.productLine = data.productLine;
        $scope.item.productType = data.productType;
        $scope.item.productSubType = data.productName;
        // 获取产品线、产品类型和产品子类型的值
        const productLine = $scope.productLineAndTypeMap[$scope.item.productLine];
        const productType = $scope.productLineAndTypeMap[$scope.item.productType];
        const productSubType = $scope.productLineAndTypeMap[$scope.item.productSubType];

        // 初始化产品字符串
        let productString = productLine + '、' + productType;

        // 如果产品子类型不是 undefined，则添加到产品字符串中
        if (productSubType) {
          productString += '、' + productSubType;
        }

        // 将最终的产品字符串赋值给 $scope.item.product
        $scope.item.product = productString;
      }
      /**
       * 提交详情信息,更新
       */
      $scope.manageRelation = function () {
        if ($scope.item.customer == null) {
          inform.common('请选择客户信息。');
          return;
        }
        if ($scope.item.profession == null) {
          inform.common('请选择行业信息。');
          return;
        }
        if ($scope.item.product == null) {
          inform.common('请选择产品相关信息。');
          return;
        }
        //公司未立项,但是基础信息均未配置
        if ($scope.dataFlag === true && !($scope.item.customer || $scope.item.profession || $scope.item.product)) {
          inform.common('当前状态为：公司未立项，请配置基础信息。');
          return;
        }
        //公司立项
        if ($scope.dataFlag === false && !$scope.item.companyProjectId) {
          inform.common('当前状态为：公司已立项，请选择公司立项项目。');
          return;
        }
        var urlData = {
          projectId: $stateParams.projectId,
          companyProjectId: $scope.item.companyProjectId,
          id: $stateParams.id,
          hardwareModelNo: $scope.item.hardwareModeNo,
          customerId: $scope.item.customerId,
          profession: $scope.item.profession,
          projectAttribution: $scope.item.projectAttribution,
          productLine: $scope.item.productLine,
          productType: $scope.item.productType,
          productSubType: $scope.item.productSubType,
        };
        costInputService.updateFeeCost(urlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              inform.common('确认费用成本成功！');
              window.history.go(-1);
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      };
      /**
       * *************************************************************
       *              方法声明部分                                 结束
       * *************************************************************
       */
    },
  ]);
})();

/*
 * @Author: fubaole
 * @Date:   2018-01-03 11:42:27
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-30 10:00:28
 */
(function() {
  'use strict';
  app.controller("lineBarCtrl", ['$scope','$rootScope', 'inform', '$timeout', 'Trans','$interval', '$location',
    function($scope,$rootScope, inform, $timeout, Trans, $interval, $location) {
      $scope.getData = getData;
      $scope.initConfig = initConfig;
      var option = {};
      initConfig();
      getData();

      function getData (){
        //tip:这里请求一个接口，后台返回给图表信息+区块的contentId，$scope.contentId接收，用于判断此页面要不要刷新
        option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              }
            }
          },
          toolbox: {
            feature: {
                dataView: {show: true, readOnly: false},
                magicType: {show: true, type: ['line', 'bar']},
                restore: {show: true}
            }
          },
          legend: {
            data: ['蒸发量', '降水量', '平均温度']
          },
          xAxis: [{
            type: 'category',
            data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            axisPointer: {
              type: 'shadow'
            }
          }],
          yAxis: [{
              type: 'value',
              name: '水量',
              min: 0,
              max: 250,
              interval: 50,
              axisLabel: {
                formatter: '{value} ml'
              }
            },
            {
              type: 'value',
              name: '温度',
              min: 0,
              max: 25,
              interval: 5,
              axisLabel: {
                formatter: '{value} °C'
              }
            }
          ],
          series: [{
              name: '蒸发量',
              type: 'bar',
              data: [2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3]
            },
            {
              name: '降水量',
              type: 'bar',
              data: [2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3]
            },
            {
              name: '平均温度',
              type: 'line',
              yAxisIndex: 1,
              data: [2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2]
            }
          ]
        };
        //tip:接口数据成功后，调用此方法，渲染页面
        reloadPageData(option);
        
      }

      //接口获取的数据，执行数据渲染操作
      function reloadPageData(option){
        if($scope.$parent.screenFlag){
          $timeout(function(){
            $scope.fullBar.setOption(option,true);
          },0);
        }else{
          $timeout(function(){
            $rootScope.lineBar.setOption(option,true);
          },0);
        }
      }

      function initConfig (){
        // 全屏图表
        if($scope.$parent.screenFlag){
          $('.fullscreen #lineBar').width(document.body.clientWidth*0.85);
          $('.fullscreen #lineBar').height(document.body.clientHeight*0.6);
          console.log($('.fullscreen #lineBar').height());
          console.log($('.fullscreen #lineBar').width());
          $timeout(function(){
            $scope.fullBar = echarts.init($('.fullscreen #lineBar')[0]);
            $scope.fullBar.setOption(option,true);
          },0);
        }else{
          $timeout(function(){
            $rootScope.lineBar = echarts.init(document.getElementById('lineBar'));
            $rootScope.lineBar.setOption(option,true);
          },0);
        }
      }

      // 刷新该模块
      $scope.$on('reload', function(e, id) {
          console.log("父级传来的数据ID"+id+"根据ID重新加载该模块");
          //tip:根据接口给的contentId，判断if(contentId===id),则执行刷新操作,调用getData();
            getData();
      });

      var timeout_upd = $interval(function(){
        if ($location.path() === '/app/index_bench' || $location.path().indexOf('preview_page')!== -1) {
          getData();
        }
        
      } ,16000);

      // 清除定时器
      $scope.$on('$destroy',function(){
        $interval.cancel(timeout_upd);
      });

      
    }
  ]);
})();
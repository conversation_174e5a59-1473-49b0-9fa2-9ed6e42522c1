(function () {
    app.controller("opsIntegrationProcess", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','opsIntegrationProcessService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,opsIntegrationProcessService,inform,Trans,AgreeConstant,LocalCache,$http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //分页
            $scope.pages = inform.initPages(); // 初始化分页数据
            $scope.type = $stateParams.type;
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
           
            //流程类型
            $scope.approvalType = ['新建Jenkins任务','修改Jenkins任务','删除Jenkins任务','Job构建失败','Sonar问题屏蔽','Sonar项目禁用','调整编译服务器环境'];

             //流程状态
            $scope.approvalStatus = ['新创建','审批中','被终止','完成','取消'];

            //获取缓存
            $scope.formRefer = LocalCache.getObject('opsIntegrationProcess_formRefer');
            //对原缓存进行覆盖
            LocalCache.setObject('opsIntegrationProcess_formRefer', {});
            //初始化时间
            initTime();
                    
            $scope.getData = getData; 			// 分页相关函数
            getData();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            function initTime() {
                //设置默认时间
                if ($scope.formRefer.endTime==null){
                    var now = new Date();
                    var endDate = inform.format(now, 'yyyy-MM-dd');
                    var startDate = inform.format(now,"yyyy-MM-01");
                    //默认开始时间
                    $scope.formRefer.endTime = endDate;
                    $scope.formRefer.startTime = startDate;
                }
            }


            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (165 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 80);
            }

            /**
             * 获取项目
             */
            function getData(pageNum) {
                var urlData ={
                    'approvalStatus': $scope.formRefer.approvalStatus,//状态
                    'type': $scope.formRefer.approvalType,//类型
                    'operatorName': $scope.formRefer.operatorName,
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间    
                    'page': pageNum,//当前页数
                    'pageSize': $scope.pages.size//每页显示条数
                };
                opsIntegrationProcessService.getOpsIntegrationProcess(urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        //测试报告集合
                        $scope.opsIntegrationProcessData = data.data.list;
                        if ($scope.opsIntegrationProcessData.length===0) {
                            $scope.pages = inform.initPages(); 			//初始化分页数据
                            inform.common(Trans("tip.noData"));
                        } else {
                        // 分页信息设置
                            $scope.pages.total = data.data.total;           // 页面数据总数
                            $scope.pages.star = data.data.startRow;         // 页面起始数
                            $scope.pages.end = data.data.endRow;            // 页面结束数
                            $scope.pages.pageNum = data.data.pageNum;       //页号
                        }
                    } else {
                        inform.common(data.message);
                    }
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }    
        
            
           
            /**
             * 重置
             */
            $scope.reset = function() {
                $scope.formRefer={};
                initTime();
            }

            /**
         *下载持续集成流程
         */
        $scope.toExcel = function() {
            var urlData ={
                'approvalStatus': $scope.formRefer.approvalStatus,//状态
                'operatorName':$scope.formRefer.operatorName,
                'type': $scope.formRefer.approvalType,//类型
                'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间  
            };
            inform.modalInstance("确定要下载持续集成流程数据表吗？").result.then(function() {
       
                inform.downLoadFile('opsIntegrationProcess/toExcel',urlData,"持续集成流程数据表"+inform.format(new Date(),'yyyy-MM-dd')+".xlsx");
           });
        };

      
        $scope.formatDeviation = formatDeviation;
        function formatDeviation(deviation){
            return Number(deviation*100).toFixed(0)+'%';
        }

        
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
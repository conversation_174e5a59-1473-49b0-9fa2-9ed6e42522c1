/*
 * @Author: fubaole
 * @Date:   2018-02-27 13:45:17
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-06 09:56:39
 */

(function() {
  'use strict';
  angular.module('app')
    .directive('searchTitle', ['$timeout', function($timeout) {
      return {
        restrict: 'EA',
        replace:true,
        scope:{
          highSearch: '@',
          // advQuery:'&',
          searchData:'&',
          inputData:'='
        },
        templateUrl: 'tpl/blocks/searchTitle.html',
        link: function(scope, ele, attrs) {
          scope.$watch(scope.inputData, function (value) {
            scope.inputData = scope[attrs.inputData];
          });
          // 高级查询
          scope.advQuery = function(){
            scope.isOpen = !scope.isOpen;
            angular.forEach(scope.inputData.unfirstRow,function(res){
              res.model = '';
            });
          };
          // 重置
          scope.reset = function() {
            angular.forEach(scope.inputData.unfirstRow,function(res){
              res.model = '';
            });
             angular.forEach(scope.inputData.row,function(res){
              res.model = '';
            });
          };
        },
        controller:function($scope) {
          $scope.dateOptions = {
            formatYear: 'yy',
            class: 'datepicker',
            showWeeks:false
          };

          $scope.toggleMin = function () {
            $scope.currentDate = $scope.currentDate ? null : new Date();
          };

          $scope.limitMore = function(){

          };

          $scope.toggleMin();
          $scope.opened = function ($event,str,status) {
            $event.preventDefault();
            $event.stopPropagation();
            if(status=='normal'){
              $scope.inputData.row[str].opened = true;
            }
            if(status=='high'){
              $scope.inputData.unfirstRow[str].opened = true;
            }
          };

        }
      };
    }]);
})();
(function() {
  'use strict';
  app.controller("history", ['$rootScope', '$scope', '$interval', '$stateParams', 'inform', 'Microservice', 'Trans',
    function($rootScope, $scope, $interval, $stateParams, inform, Microservice, Trans) {

      $scope.size = 10;
      $scope.state = "";
      $scope.historyTimer = 0;
      $scope.getHistory = getHistory;
      $scope.getHistory();
      $scope.$watch('$scope.historyTimer', function(newValue, oldValue) {
        if (newValue !== oldValue) {
          $interval.cancel($scope.interval);
          if (newValue !== '0') {
            $scope.interval = $interval(function() {
              $scope.getHistory();
            }, $scope.historyTimer);
          }
        }
      });

      $scope.$on('$destroy', function() {
        $interval.cancel($scope.interval);
      });
      // 获取数据
      function getHistory() {
        // Microservice.getHistory()
        //   .then(function (data) {
        //     console.log(data)
        //     $scope.historyData = data;
        //    console.log("getHistory:"+$scope.historyData);

        //   },function(){
        //     inform.common(Trans("tip.requestError"));
        //   });
      }

    }
  ]);
})();
/*
 * @Author: dongyinggang
 * @Date:   2019-06-13 17:50:05
 * @Last Modified by:   dongyinggang
 * @Last Modified time:  2019-06-13 17:50:05
 */
(function () {
    'use strict';
    app.factory('bugOnlineService', bugOnlineService);
    bugOnlineService.$inject = ["HttpService", '$rootScope'];

    function bugOnlineService(HttpService, $rootScope) {

        var service = {

            getBugOnlineInfo: getBugOnlineInfo,
            updateBugOnlineInfo: updateBugOnlineInfo,
            getBugOnlineDetailInfo:getBugOnlineDetailInfo,
            deleteBugOnlineInfo:deleteBugOnlineInfo

        };
        return service;

        /**
         * 分页查询线上bug
         */
        function getBugOnlineInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'bugOnline/getBugOnlineInfo', urlData);
        }
        /**
         * 查询某个线上bug
         */
        function getBugOnlineDetailInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'bugOnline/getBugOnlineDetailInfo', urlData);
        }
        /**
         * 同步线上bug
         */
        function updateBugOnlineInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'bugOnline/updateBugOnlineInfo', urlData);
        }

        /**
         * 删除线上bug
         */
        function deleteBugOnlineInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'bugOnline/deleteBugOnlineInfo', urlData);
        }


    }
})();
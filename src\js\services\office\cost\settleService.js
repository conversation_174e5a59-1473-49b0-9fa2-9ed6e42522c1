/*
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date:   2020-12-08 10:37:05
 */
(function() {
    'use strict';
    app.factory('settleService', settleService);
    settleService.$inject=["HttpService",'$rootScope'];

    function settleService(HttpService,$rootScope){

        var service={
            getProjectSettle:getProjectSettle,
            getPlmBudget:getPlmBudget,
            getFeeBudgetInfo:getFeeBudgetInfo,
            getProjectStageList:getProjectStageList,
            getTitleLevelList:getTitleLevelList,
            applySettle:applySettle,
            cancelSettle:cancelSettle,
            finishSettle:finishSettle,
            toExcel:toExcel,
            createCurrentWeekHrCost:createCurrentWeekHrCost
        };
        return service;

        /**
         * 项目导出结算表
         */
        function toExcel(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costSettle/exportSettleInfo', urlData);
        }
        /**
         * 完成项目结算
         */
        function finishSettle(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costSettle/finishSettleProject', urlData);
        }
        /**
         * 项目申请结算
         */
        function cancelSettle(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costSettle/cancelSettleProject', urlData);
        }
        /**
         * 项目申请结算
         */
        function applySettle(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costSettle/applySettleProject', urlData);
        }

        /**
         * 获取项目结算信息
         */
        function getProjectSettle(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costSettle/getSettleProject', urlData);
        }

        /**
         * 获取plm预算信息
         */
        function getPlmBudget(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'budgetAction/getPlmBudget', urlData);
        }

        /**
         * 获取项目费用预算明细
         */
        function getFeeBudgetInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'budgetAction/getFeeBudgetInfo', urlData);
        }

        /**
         * 获取项目相关阶段
         */
        function getProjectStageList(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costCom/getProjectStageList', urlData);
        }

        /**
         * 查询项目相关岗位和级别
         */
        function getTitleLevelList(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costCom/getTitleLevelList', urlData);
        }

        /**
         * 调用接口生成当前周的人力数据
         */
        function createCurrentWeekHrCost(projectId) {
            return HttpService.post($rootScope.getWaySystemApi + 'costSettle/createCurrentWeekHrCost', projectId);
        }
    }
})();

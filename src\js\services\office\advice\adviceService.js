/*
 * @Author: dongyinggang
 * @Date:   2019-06-13 17:50:05
 * @Last Modified by:   dongyinggang
 * @Last Modified time:  2019-06-13 17:50:05
 */
(function () {
    'use strict';
    app.factory('adviceService', adviceService);
    adviceService.$inject = ["HttpService", '$rootScope'];

    function adviceService(HttpService, $rootScope) {

        var service = {

            getAdviceByMap: getAdviceByMap,
            getAdviceStatisticsByMap: getAdviceStatisticsByMap,
            addAdvice: addAdvice,
            updateAdvice: updateAdvice,
            deleteById: deleteById,
            getDeptList: getDeptList,
            adviceSynchronize: adviceSynchronize

        };
        return service;

        /**
         * 分页查询合理化建议
         */
        function getAdviceByMap(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'advice/getAdviceByMap', urlData);
        }

        /**
         * 分页查询合理化建议报告
         */
        function getAdviceStatisticsByMap(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'advice/getAdviceStatisticsByMap', urlData);
        }

        /**
         * 添加合理化建议
         */
        function addAdvice(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'advice/insertAdvice', urlData);
        }


        /**
         * 修改合理化建议
         */
        function updateAdvice(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'advice/updateAdvice', urlData);
        }

        /**
         * 删除合理化建议
         */
        function deleteById(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'advice/deleteById', urlData);
        }

        /**
         * 获取系研及其下属二级部门
         * @return {[type]} [description]
         */
        function getDeptList() {
            return HttpService.get($rootScope.getWaySystemApi + 'advice/getDeptList',
                {});
        }

        /**
         * 同步合理化建议
         */
        function adviceSynchronize(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'advice/adviceSynchronize', urlData);
        }

    }
})();
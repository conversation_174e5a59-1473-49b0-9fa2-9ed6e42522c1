# Vue3 与 AngularJS 集成指南

本文档介绍如何在现有的 AngularJS 项目中集成 Vue3 组件，实现渐进式迁移。

## 🎯 方案概述

采用**微前端**的方式，将 Vue3 组件打包成 UMD 格式，通过自定义 AngularJS 指令在现有项目中使用 Vue3 组件。

### 优势
- ✅ 保持现有 AngularJS 项目不受影响
- ✅ 可以逐步迁移部分页面到 Vue3
- ✅ 两个框架可以共存并通信
- ✅ 使用 Element Plus 现代化 UI 组件库
- ✅ 支持热更新开发

## 📁 项目结构

```
src/
├── vue-components/              # Vue3 组件子项目
│   ├── package.json            # Vue3 项目配置
│   ├── vite.config.js          # Vite 构建配置
│   ├── src/
│   │   ├── main.js             # Vue3 入口文件
│   │   ├── components/         # 通用 Vue3 组件
│   │   └── pages/              # Vue3 页面组件
│   └── dist/                   # 构建输出目录
├── js/
│   ├── directives/
│   │   └── vue-component.js    # AngularJS-Vue3 桥接指令
│   └── controllers/
│       └── office/attendance/
│           └── attendanceDetailVue.js  # Vue3 页面控制器
├── tpl/
│   └── office/attendance/
│       └── attendanceDetailVue.html    # Vue3 页面模板
└── library/
    └── vue-components/         # Vue3 组件构建输出
        ├── vue-components.umd.js
        └── style.css
```

## 🚀 快速开始

### 1. 安装 Vue3 组件依赖

```bash
# 安装 Vue3 组件的依赖
npm run install:vue

# 或者手动安装
cd src/vue-components
npm install
```

### 2. 构建 Vue3 组件

```bash
# 一键安装并构建
npm run setup:vue

# 或者单独构建
npm run build:vue
```

### 3. 启动开发服务器

```bash
# 启动 AngularJS 开发服务器
npm start

# 在另一个终端启动 Vue3 开发服务器（可选，用于 Vue3 组件热更新）
npm run dev:vue
```

### 4. 访问 Vue3 页面

打开浏览器访问：`http://localhost:8000/#/app/office/attendanceDetailVue`

## 🔧 开发指南

### 创建新的 Vue3 页面组件

1. 在 `src/vue-components/src/pages/` 目录下创建新的 Vue 文件
2. 在 `src/vue-components/src/main.js` 中导入并注册组件
3. 创建对应的 AngularJS 控制器和模板
4. 在路由配置中添加新路由

### 示例：创建一个新的 Vue3 组件

```vue
<!-- src/vue-components/src/pages/MyNewPage.vue -->
<template>
  <div class="my-new-page">
    <el-card>
      <h2>我的新页面</h2>
      <el-button @click="handleClick">点击我</el-button>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const handleClick = () => {
  console.log('按钮被点击了')
}
</script>
```

### AngularJS 中使用 Vue3 组件

```html
<!-- AngularJS 模板 -->
<div vue-component="MyNewPage" 
     vue-props="vm.vueProps"
     on-vue-event="vm.handleVueEvent(eventName, data)">
</div>
```

```javascript
// AngularJS 控制器
angular.module('app').controller('myNewPageController', function($scope) {
    var vm = this;
    
    // 传递给 Vue 组件的数据
    vm.vueProps = {
        title: '标题',
        data: []
    };
    
    // 处理来自 Vue 组件的事件
    vm.handleVueEvent = function(eventName, data) {
        console.log('Vue 事件:', eventName, data);
    };
});
```

## 🔄 数据通信

### AngularJS → Vue3

通过 `vue-props` 属性传递数据：

```javascript
// AngularJS 控制器
vm.vueProps = {
    userList: vm.userList,
    searchParams: vm.searchParams
};

// 监听数据变化，自动更新 Vue 组件
$scope.$watch('vm.userList', function(newVal) {
    vm.vueProps.userList = newVal;
}, true);
```

### Vue3 → AngularJS

通过事件机制通信：

```javascript
// Vue3 组件中触发事件
const emit = defineEmits(['search', 'export'])

const handleSearch = () => {
    emit('search', { keyword: 'test' })
}

// AngularJS 中处理事件
vm.handleVueEvent = function(eventName, data) {
    switch(eventName) {
        case 'search':
            vm.doSearch(data);
            break;
        case 'export':
            vm.doExport(data);
            break;
    }
};
```

## 📦 构建和部署

### 开发环境

```bash
# 启动 AngularJS 开发服务器
npm start

# 启动 Vue3 组件开发服务器（支持热更新）
npm run dev:vue
```

### 生产环境

```bash
# 构建 Vue3 组件
npm run build:vue

# 构建后的文件会自动输出到 src/library/vue-components/
# AngularJS 项目会自动引用构建后的文件
```

## 🎨 样式处理

### Element Plus 样式

Element Plus 的样式已经包含在构建输出中，会自动加载。

### 自定义样式

在 Vue 组件中使用 `<style scoped>` 来避免样式冲突：

```vue
<style scoped>
.my-component {
    /* 组件特定样式 */
}
</style>
```

## 🐛 常见问题

### 1. Vue 组件不显示

- 检查 `library/vue-components/vue-components.umd.js` 文件是否存在
- 检查浏览器控制台是否有 JavaScript 错误
- 确保已正确构建 Vue3 组件

### 2. 样式冲突

- 使用 Vue 的 `scoped` 样式
- 为 Vue 组件添加特定的 CSS 类名前缀
- 检查 Element Plus 样式是否正确加载

### 3. 数据不更新

- 检查 AngularJS 的 `$scope.$apply()` 是否正确调用
- 确保 Vue 组件的 props 响应式更新
- 使用 `$scope.$watch` 监听数据变化

## 📚 参考资料

- [Vue 3 官方文档](https://vuejs.org/)
- [Element Plus 官方文档](https://element-plus.org/)
- [Vite 官方文档](https://vitejs.dev/)
- [AngularJS 官方文档](https://angularjs.org/)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个集成方案。

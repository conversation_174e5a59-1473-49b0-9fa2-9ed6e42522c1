(function () {
    app.controller('communicationLogUpdate', [
        'comService',
        'performanceLogManagementService',
        '$rootScope',
        '$scope',
        '$state',
        '$stateParams',
        '$modal',
        'inform',
        'Trans',
        'AgreeConstant',
        'LocalCache',
        '$http',
        '$timeout',
        function (
            comService,
            performanceLogManagementService,
            $rootScope,
            $scope,
            $state,
            $stateParams,
            $modal,
            inform,
            Trans,
            AgreeConstant,
            LocalCache,
            $http,
            $timeout
        ) {
            /**
             * *************************************************************
             *              初始化部分                                 开始
             * *************************************************************
             */
            // 表单初始化
            $scope.formRefer = {
                // 被考核人域账号
                assessedAccount: '',
                // 记录类型
                recordType: '绩效沟通',
                // 考核周期
                assessmentCycle: '',
                // 辅导日期
                communicateDate: '',
                // 沟通人账号
                communicateAccount: LocalCache.getSession('currentUserName'),
                // 所需资源
                resourceRequirement: '',
                // 成绩和个人成长
                personalGrowth: '',
                // 不足及改进点
                lackAndImprove: '',
                // 反馈、建议、答复情况
                feedbackAdvise: '',
                // 结果认同
                resultAgree: '',
            };

            // 改进任务跟踪表格
            let dataTable;
            // 改进目标及评价列表
            $scope.performanceTargetDtoList = [];
            // 改进任务跟踪（禅道任务列表）
            $scope.zentaoTaskList = [];
            //  禅道任务列表（展开/折叠）
            $scope.isExpanded = true;
            // dataTable插件，用于处理展开树状结构
            let treeGrid;
            // 确认是否为仅查看
            const type = $state.params.type;
            if (type === 'add' || type === 'edit') {
                $scope.isEditable = true;
            } else {
                $scope.isEditable = false;
            }
            const typeList = {
                add: '新增',
                edit: '编辑',
                view: '查看',
            };
            $scope.showType = typeList[type];
            // 员工对应的下拉列表
            $scope.employeeList = [];
            // （绩效沟通）记录类型对应的下拉选项
            $scope.logType = ['日常绩效辅导', '低绩效改进', '述职改进', '试用期考核'];
            // 获取近三年的考核周期列表
            $scope.headList = [];
            //获取二级部门对应的员工姓名
            const getEmployeeByDepartment = async () => {
                try {
                    const data = await comService.getEmployeesByOrgId('');
                    if (data.data) {
                        $scope.employeeList = data.data;
                    }
                } catch (error) {
                    console.error(error, '获取员工数据失败');
                }
            };
            // 导入上期未达成目标的按钮
            $scope.isImportButtonDestroy = false;
            /**
            * *************************************************************
            *              初始化部分                                 结束
            * *************************************************************

            */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            // 选择不同的人对应不同的数据
            $scope.$watch('formRefer.assessedAccount', async () => {
                $scope.performanceTargetDtoList = [];
                $scope.zentaoTaskList = [];
                try {
                    // 获取对应tag
                    const res = await performanceLogManagementService.getPerformanceLogDetailByTag({
                        userAccount: $scope.formRefer.assessedAccount,
                        assessmentCycle: $scope.formRefer.assessmentCycle,
                    });
                    if (res.code === '0000') {
                        if (!res.data) {
                            updateTreeTable();
                            return;
                        }
                        const { performanceTargetDtoList, performanceCommFatherTaskInfo, ...rest } = res.data;
                        $scope.performanceTargetDtoList = performanceTargetDtoList;
                        $scope.zentaoTaskList = handleZentaoTaskList(performanceCommFatherTaskInfo);
                        $scope.formRefer = rest;
                        $scope.$apply();
                        updateTreeTable();
                    } else {
                        inform.common(res.message);
                    }
                } catch (error) {
                    console.error(error, '切换tag获取数据失败');
                }
            });

            /**
             * 设置头部Tab页，自动与时间同步
             * @param {String?} cycle 考核周期
             */
            const setHeadList = (cycle = '') => {
                $scope.headList = [];
                let curYear;
                let curMonth;
                if (cycle) {
                    let match = cycle.match(/^(\d+)(.*)/);
                    let assessmentYear = match[1];
                    let assessmentCycle = match[2];
                    curYear = assessmentYear;
                    curMonth = assessmentCycle === '全年' ? 7 : 1;
                } else {
                    // 给考核周期设置默认值
                    const now = new Date();
                    curYear = now.getFullYear();
                    curMonth = now.getMonth() + 1;
                }

                // 考核周期从哪一年开始
                let startYear = curYear;
                if (curMonth >= 7) {
                    $scope.formRefer.assessmentCycle = `${curYear}全年`;
                } else {
                    $scope.formRefer.assessmentCycle = `${curYear}上半年`;
                }

                for (let year = startYear; year >= startYear - 2; year--) {
                    $scope.headList.push(`${year}全年`);
                    $scope.headList.push(`${year}上半年`);
                }

                if (curMonth < 7) {
                    $scope.headList.shift();
                    $scope.headList.push(`${startYear - 3}全年`);
                }

                $scope.headList = $scope.headList.map((item, index) => {
                    return {
                        title: item,
                        isActive: index === 0,
                    };
                });
            };
            setHeadList();
            /**
             * 处理接口返回的数据
             * @returns {Array} 处理之后的禅道任务树状数组
             */
            const handleZentaoTaskList = (data) => {
                if (data.length === 0) return [];
                const map = {
                    childTaskId: 'taskId',
                    childTaskName: 'taskName',
                    childTaskStatus: 'taskStatus',
                    childConsumedHours: 'consumedHours',
                    childPlanFinishedDate: 'planFinishedDate',
                    childActualFinishedDate: 'actualFinishedDate',
                };
                const res = data.map((i) => {
                    if (!i.children || i.children.length === 0) {
                        return i;
                    }
                    // 替换掉children里的变量名，将其和一级菜单保持一致
                    const child = i.children.map((j) => {
                        for (const key in j) {
                            const keyMap = map[key];
                            if (keyMap) {
                                j[keyMap] = j[key];
                            }
                        }
                        return j;
                    });
                    i.children = child;
                    return i;
                });
                return res;
            };
            /**
             * 获取全部表单数据(根据路由参数)
             */
            const getFormData = async () => {
                try {
                    const params = {
                        communicateId: $state.params.id,
                    };
                    const res = await performanceLogManagementService.getPerformanceLogDetailById(params);
                    if (res.data) {
                        const { performanceTargetDtoList, performanceCommFatherTaskInfo, ...rest } = res.data;
                        $scope.performanceTargetDtoList = performanceTargetDtoList;
                        $scope.zentaoTaskList = handleZentaoTaskList(performanceCommFatherTaskInfo);
                        $scope.formRefer = rest;
                        setHeadList($scope.formRefer.assessmentCycle);
                        $timeout(getDataTable);
                    } else {
                        inform.common(res.message);
                    }
                } catch (error) {
                    console.error(error, '获取表单数据失败');
                }
            };
            // 返回按钮
            $scope.goBack = () => {
                if ($rootScope.previousState.name === 'app.index_bench') {
                    // 从首页来的，就返回个人绩效页签
                    $state.go('app.index_bench', {
                        tagType: 9,
                    });
                    return;
                }
                history.go(-1);
            };
            // 定义一个函数来获取列定义
            const getColumns = () => {
                const baseColumns = [
                    { data: 'taskId', title: '禅道任务ID', width: '10%' },
                    { data: 'taskName', title: '禅道任务名称', width: '30%' },
                    { data: 'taskStatus', title: '任务状态', width: '10%' },
                    { data: 'consumedHours', title: '消耗（H）', width: '7%' },
                    { data: 'planFinishedDate', title: '计划完成时间', width: '10%' },
                    { data: 'actualFinishedDate', title: '实际完成时间', width: '10%' },
                ];

                if ($scope.isEditable) {
                    // 状态为已完成：才能关闭，其余不行
                    // 状态为未开始/进行中：不能激活，其余可以激活
                    baseColumns.push({
                        data: (data) => {
                            const closeButton = `<a class="performance-log-management-link" style="margin-right: 10px">关闭</a>`;
                            const activeButton = `<a class="performance-log-management-link">激活</a>`;
                            let res = '';
                            if (data.taskStatus === '已完成') {
                                res += closeButton;
                            }
                            if (data.taskStatus !== '进行中' && data.taskStatus !== '未开始') {
                                res += activeButton;
                            }
                            return res;
                        },
                        title: '操作',
                        width: '10%',
                    });
                }
                return baseColumns;
            };

            // 获取改进任务跟踪树状表格
            const getDataTable = () => {
                // 如果表格已存在，先销毁
                if (dataTable) {
                    dataTable.destroy();
                    $('#treeTable').empty();
                }

                const columns = getColumns();

                dataTable = $('#treeTable').DataTable({
                    dom: 'tr',
                    ordering: false,
                    processing: true,
                    data: $scope.zentaoTaskList,
                    language: {
                        emptyTable: '暂无数据',
                    },
                    columns,
                });
                // dataTable插件，用于处理展开树状结构
                // 插件位置：src\library\jquery\datatables\plugins\treeGrid.js
                treeGrid = $.fn.dataTable.treeGrid(dataTable, {
                    treeColumn: 1,
                    expandAll: true,
                });

                treeGrid.expandAll();
                const treeTableListener = (dataTable) => {
                    $('#treeTable').on('click', '.performance-log-management-link', function () {
                        const data = dataTable.row($(this).closest('tr')).data();
                        const action = $(this).text();
                        if (action === '关闭') {
                            handleTaskClose(data);
                        } else if (action === '激活') {
                            handleTaskActivate(data);
                        }
                    });
                };
                treeTableListener(dataTable);
            };

            // 在$scope.zentaoTaskList变化时调用
            $scope.$watch('zentaoTaskList', function (newVal, oldVal) {
                getDataTable();
            });
            /**
             * 初始化页面
             */
            async function init() {
                const cache = LocalCache.getObject('communicationLogUpdate', $scope.formRefer);
                try {
                    getEmployeeByDepartment();
                    if ($state.params.id) {
                        getFormData();
                    } else if (cache) {
                        $scope.formRefer = cache;
                    } else {
                        getDataTable();
                    }
                } catch (error) {
                    console.error(error, '初始化失败');
                }
            }
            init();
            // 关闭禅道任务
            const handleTaskClose = async (data) => {
                if (!$scope.isEditable) {
                    return;
                }
                try {
                    const params = {
                        proTaskId: data.taskId,
                        communicateId: $scope.formRefer.id,
                    };
                    const res = await performanceLogManagementService.closeZentaoTask(params);
                    if (res.code !== '0000') {
                        inform.common(res.message);
                        return;
                    }
                    inform.common('关闭成功');
                    getFormData();
                } catch (error) {
                    console.error(error, '关闭失败');
                }
            };
            // 激活禅道任务
            const handleTaskActivate = async (data) => {
                if (!$scope.isEditable) {
                    return;
                }
                try {
                    const params = {
                        proTaskId: data.taskId,
                        assignedTo: $scope.formRefer.assessedAccount,
                        communicateId: $scope.formRefer.id,
                    };
                    const res = await performanceLogManagementService.activeZentaoTask(params);
                    if (res.code !== '0000') {
                        inform.common(res.message);
                        return;
                    }
                    inform.common('激活成功');
                    getFormData();
                } catch (error) {
                    console.error(error, '激活失败');
                }
            };
            // 处理禅道任务展开/折叠
            $scope.handleCollapse = () => {
                $scope.isExpanded = !$scope.isExpanded;
                if ($scope.isExpanded) {
                    treeGrid.expandAll();
                } else {
                    treeGrid.collapseAll();
                }
            };
            // 删除改进目标及评价
            $scope.deleteGoal = async (index) => {
                //自定义弹出框
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: 'sm',
                    resolve: {
                        items: function () {
                            return '确认删除此记录吗？';
                        },
                    },
                });
                await modalInstance.result;
                $scope.performanceTargetDtoList.splice(index, 1);
            };
            // 增加改进目标
            $scope.addToBeImporvedGoal = () => {
                $scope.performanceTargetDtoList.push({
                    // 绩效目标
                    targetVal: '',
                    // 关键结果
                    keyResult: '',
                    // 计划完成时间
                    planEndDate: '',
                    // 实际完成时间,注意这里默认值是null
                    endDate: null,
                    // 状态
                    targetStatus: '未达成',
                    // 评价
                    evaluateVal: '',
                });
            };
            /**
             * 更新改进任务跟踪表格数据
             */
            const updateTreeTable = () => {
                if (dataTable) {
                    // 清空表格数据
                    dataTable.clear();
                    // 添加新的数据
                    dataTable.rows.add($scope.zentaoTaskList);
                    // 重新绘制表格
                    dataTable.draw();
                }
            };
            /**
             * tag切换的点击事件
             * @param {Object} item 当前tag
             */
            $scope.handleHeaderClick = async (item, index) => {
                if (!$scope.formRefer.assessedAccount) {
                    inform.common('请选择被考核人');
                    return;
                }
                $scope.isExpanded = true;
                $scope.formRefer = {
                    // 被考核人域账号（员工姓名）
                    assessedAccount: $scope.formRefer.assessedAccount,
                    // 记录类型
                    recordType: '绩效沟通',
                    // 考核周期
                    assessmentCycle: $scope.formRefer.assessmentCycle,
                    // 辅导日期
                    communicateDate: '',
                    // 沟通人账号
                    communicateAccount: LocalCache.getSession('currentUserName'),
                    // 所需资源
                    resourceRequirement: '',
                    // 成绩和个人成长
                    personalGrowth: '',
                    // 不足及改进点
                    lackAndImprove: '',
                    // 反馈、建议、答复情况
                    feedbackAdvise: '',
                    // 结果认同
                    resultAgree: '',
                };
                $scope.performanceTargetDtoList = [];
                $scope.zentaoTaskList = [];
                // 对应考核周期进行变更
                $scope.formRefer.assessmentCycle = item.title;
                // 只有当前考核周期可编辑
                if (index === 0 && $state.params.type !== 'view') {
                    $scope.isEditable = true;
                } else {
                    $scope.isEditable = false;
                }
                $scope.headList.forEach((i) => {
                    i.isActive = false;
                });
                item.isActive = true;
                try {
                    // 获取对应tag
                    const res = await performanceLogManagementService.getPerformanceLogDetailByTag({
                        userAccount: $scope.formRefer.assessedAccount,
                        assessmentCycle: $scope.formRefer.assessmentCycle,
                    });
                    if (res.code === '0000') {
                        if (!res.data) {
                            updateTreeTable();
                            return;
                        }
                        const { performanceTargetDtoList, performanceCommFatherTaskInfo, ...rest } = res.data;
                        $scope.performanceTargetDtoList = performanceTargetDtoList;
                        $scope.zentaoTaskList = handleZentaoTaskList(performanceCommFatherTaskInfo);
                        $scope.formRefer = rest;
                        $scope.$apply();
                        updateTreeTable();
                    } else {
                        inform.common(res.message);
                    }
                } catch (error) {
                    console.error(error, '切换tag获取数据失败');
                }
            };
            /**
             * 导入上期未达成目标
             */
            $scope.importUnfinishedGoal = async () => {
                if (!$scope.formRefer.assessedAccount) {
                    inform.common('请选择被考核人');
                    return;
                }
                try {
                    const params = {
                        userAccount: $scope.formRefer.assessedAccount,
                        lastAssessmentCycle: $scope.headList[1].title,
                        assessmentCycle: $scope.headList[0].title,
                    };
                    const res = await performanceLogManagementService.getCommunicationUnfinishedTask(params);
                    if (res.code === '0000') {
                        $scope.isImportButtonDestroy = true;
                        $scope.$apply();
                        if (res.data.length > 0) {
                            $scope.performanceTargetDtoList = [...$scope.performanceTargetDtoList, ...res.data];
                            $scope.$apply();
                        } else {
                            inform.common('无上期未达成目标');
                        }
                    } else {
                        inform.common(res.message);
                    }
                } catch (error) {
                    console.error(error, '获取上期未达成目标失败');
                }
            };
            /**
             * 保存
             */
            $scope.save = async (type) => {
                try {
                    const typemap = {
                        save: 0,
                        saveAndCreateTask: 1,
                    };
                    const saveMethod = typemap[type];
                    const params = {
                        ...$scope.formRefer,
                        saveMethod,
                        performanceTargetDtoList: $scope.performanceTargetDtoList,
                    };
                    const res = await performanceLogManagementService.savePerformanceLogDetail(params);
                    if (res.code !== '0000') {
                        inform.common(res.message);
                        return;
                    }
                    inform.common('保存成功');
                    $state.go('app.office.performanceLogManagement');
                } catch (error) {
                    console.error(error, '保存失败');
                }
            };
            /**
             * 跳转至个人看板kpi数据页签
             */
            $scope.toPersonalBoard = () => {
                if (!$scope.formRefer.assessedAccount) {
                    inform.common('请选择被考核人');
                    return;
                }
                //季度下拉框数据源
                const quarterSelectArr = [
                    {
                        value: '6',
                        label: '上半年',
                    },
                    {
                        value: '7',
                        label: '下半年',
                    },
                    {
                        value: '5',
                        label: '全年',
                    },
                ];

                let match = $scope.formRefer.assessmentCycle.match(/^(\d+)(.*)/);
                let assessmentYear = match[1];
                let assessmentCycle = match[2];

                const years = assessmentYear;
                const quarter = quarterSelectArr.find((i) => i.label === assessmentCycle).value;

                const currentUserInfo = $scope.employeeList.find(
                    (i) => i.loginName === $scope.formRefer.assessedAccount
                );

                // 保存查询条件
                LocalCache.setObject('communicationLogUpdate', $scope.formRefer);
                // 向个人看板页面传入域账号与姓名
                LocalCache.setObject('personDataBoardEmployee', {
                    name: currentUserInfo.realName,
                    loginName: currentUserInfo.loginName,
                });

                // 跳转到个人看板
                // 注意：这里跳转之后，个人首页的所有信息都会变成选择的被考核人信息
                $state.go('app.index_bench', {
                    // 被考核人工号
                    empId: currentUserInfo.employeeNo,
                    years,
                    quarter,
                    tagType: '4',
                });
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        },
    ]);
})();

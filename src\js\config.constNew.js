/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date:   2018-03-16 15:11:29
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-22 11:46:12
 */
angular.module('app')
    .run(
        ['$rootScope', 'AgreeConstant',
            function($rootScope, AgreeConstant) {


                //二次开发接口路径配置
                $rootScope.getWaySystemApi = "http://127.0.0.1:7788/"; // 自测
                $rootScope.gateInfoApi="http://127.0.0.1:7788/";

                AgreeConstant.login = {
                    "loginTitle": "新北洋授权管理系统",
                    "loginLogo": "../../img/basic/logo.png",
                    "title": "授权管理",
                    "headLogo": "img/basic/logo.png",
                    "desc": {
                        "subTitle": "新北洋授权管理系统",
                        "txt": [
                            "轻量应用保护系统首发，更低价格更高服务增值。",
                            "快速创建和易于管理的知识产权保护，更安全，更可靠，更贴心。"
                        ]
                    }
                };

                /**
                * 二次开发登录页信息配置
                * 示例：
                * AgreeConstant.login = {
                *     "loginTitle":"分拣物流管理平台",
                *     "loginLogo":"../../img/basic/logo.png",
                *     "title":"分拣管理",
                *     "headLogo":"img/basic/logo.png",
                *     "desc":{
                *         "subTitle":"分拣物流管理平台",
                *         "txt":[
                *           "新北洋基础业务平台用于快速业务集成及技术集成",
                            "平台提供个性化UI设计、通用的业务功能",
                            "平台简单易上手，开发效率高"
                *         ]
                *     };
                */


                /**
                * 二次开发配置区块自适应窗口
                * 示例：
                *  window.addEventListener("resize", function () {
                        if ($rootScope.bar) { $rootScope.bar.resize(); }
                        if ($rootScope.pie) { $rootScope.pie.resize(); }
                        if ($rootScope.autoBar) { $rootScope.autoBar.resize(); }
                        if ($rootScope.lineBar) { $rootScope.lineBar.resize(); }
                    });
                */


                /**
                * 二次开发正则配置
                * 示例：
                *   AgreeConstant.limitList.user = /^(?!-)(?!.*?-$)[a-zA-Z0-9_\u4e00-\u9fa5]{1,10}$/;
                    AgreeConstant.limitList.userTxt ='请输入汉字、字母、数字和下划线，最大长度为10。';
                */


                /**
                 * 二次开发新增常量约定
                 * 示例：AgreeConstant.defaultAdd = '../../../img/newAdd/p0.jpg';
                 */

            }
        ]);
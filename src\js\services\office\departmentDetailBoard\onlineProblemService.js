(function() {
    'use strict';
    app.factory('onlineProblemService', onlineProblemService);
    onlineProblemService.$inject=["HttpService",'$rootScope'];

    function onlineProblemService(HttpService,$rootScope){

        function getOnlineBugChart(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'OnlineBug/getOnlineBugChart',urlData);
        }

        function getRateInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'OnlineBug/getRateInfo',urlData);
        }

        function getOnlineBugList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'OnlineBug/getOnlineBugList',urlData);
        }

        function getOnlineBugCount(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'OnlineBug/getOnlineBugCount',urlData);
        }

        function getDeptPLMInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptPLM/getDeptPLMInfo',urlData);
        }

        return {
            getOnlineBugChart: getOnlineBugChart,
            getRateInfo: getRateInfo,
            getOnlineBugList: getOnlineBugList,
            getOnlineBugCount: getOnlineBugCount,
            getDeptPLMInfo: getDeptPLMInfo,
        };
    }
})();
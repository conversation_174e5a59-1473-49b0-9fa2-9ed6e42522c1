//# sourceURL=js/controllers/office/validCode/validCodeManagementController.js
(function () {
    app.controller("validCodeManagement", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','validCodeManagementService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,validCodeManagementService,inform,Trans,AgreeConstant,LocalCache,$http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //分页
            $scope.pages = inform.initPages(); // 初始化分页数据
            $scope.type = $stateParams.type;
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
			//获取部门
            $scope.departmentList = [];
            comService.getOrgChildren('D010053').then(function(data) {
                $scope.departmentList = comService.getDepartment(data.data);
            });
           
            //获取岗位
            $scope.professionalModuleList = [];
            comService.getParamList('STAFF_PROFESSIONAL_MODULE', 'NEW').then(function (data) {
                $scope.professionalModuleList = data.data;
            });

            
            //获取缓存
            $scope.formRefer = LocalCache.getObject('validCodeManagement_formRefer');
            //对原缓存进行覆盖
            LocalCache.setObject('validCodeManagement_formRefer', {});
            //初始化时间
            initTime();
            //初始化部门
            initOrg();
                    
            $scope.getData = getData; 			// 分页相关函数
            getData();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            function initTime() {
                //设置默认时间
                if ($scope.formRefer.endTime==null){
                    var now = new Date();
                    var endDate = inform.format(now, 'yyyy-MM-dd');
                    var startDate = inform.format(now,"yyyy-MM-01");
                    //默认开始时间
                    $scope.formRefer.endTime = endDate;
                    $scope.formRefer.startTime = startDate;
                }
            }

            function initOrg(){
                //设置默认部门
                if ($scope.formRefer.department==null){
                    var department = LocalCache.getSession('department');
                    $scope.formRefer.department = department;
                }
            }

            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (165 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 80);
            }
            /**
             * 获取项目
             */
            function getData(pageNum) {
                var urlData ={
                    'orgCode': $scope.formRefer.department,//部门
                    'employeeName': $scope.formRefer.employeeName,//姓名
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间
                    'professionalModule': $scope.formRefer.professionalModule, //岗位 
                    'page': pageNum,//当前页数
                    'pageSize': $scope.pages.size//每页显示条数
                };
                validCodeManagementService.getPersonalValidCode(urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        $scope.personalValidCodeData = data.data.list;
                        if ($scope.personalValidCodeData.length===0) {
                            $scope.pages = inform.initPages(); 			//初始化分页数据
                            inform.common(Trans("tip.noData"));
                        } else {
                        // 分页信息设置
                            $scope.pages.total = data.data.total;           // 页面数据总数
                            $scope.pages.star = data.data.startRow;         // 页面起始数
                            $scope.pages.end = data.data.endRow;            // 页面结束数
                            $scope.pages.pageNum = data.data.pageNum;       //页号
                        }
                    } else {
                        inform.common(data.message);
                    }
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }    
        
            
           
            /**
             * 重置
             */
            $scope.reset = function() {
                $scope.formRefer={};
                initTime();
            }

            /**
         *下载有效代码信息
         */
        $scope.toExcel = function() {
            var urlData ={
                'orgCode': $scope.formRefer.department,//部门
                'employeeName': $scope.formRefer.employeeName,//姓名
                'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间
                'professionalModule': $scope.formRefer.professionalModule, //岗位  
            };
            inform.modalInstance("确定要下载有效代码数据表吗？").result.then(function() {
       
                inform.downLoadFile('validCodeData/toExcel',urlData,"有效代码数据表"+inform.format(new Date(),'yyyy-MM-dd')+".xlsx");
           });
        };

        //跳转详情页
        $scope.showCommit = function(m){
            LocalCache.setObject("validCodeManagement_formRefer",$scope.formRefer);
            $state.go("app.office.commitList", {pushUser:m.loginName,
                startTime:$scope.formRefer.startTime, endTime:$scope.formRefer.endTime});
		};

        $scope.formatDeviation = formatDeviation;
        function formatDeviation(deviation){
            return Number(deviation*100).toFixed(0)+'%';
        }

        
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
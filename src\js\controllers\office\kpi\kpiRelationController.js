(function () {
    app.controller('kpiRelationController', [
        'comService',
        'kpiRelationService',
        '$rootScope',
        '$state',
        '$scope',
        '$stateParams',
        '$modal',
        'inform',
        'LocalCache',
        'Trans',
        'AgreeConstant',
        '$http',
        function (
            comService,
            kpiRelationService,
            $rootScope,
            $state,
            $scope,
            $stateParams,
            $modal,
            inform,
            LocalCache,
            Trans,
            AgreeConstant,
            $http
        ) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //查询条件
            $scope.formRefer = {};
            $scope.entryType = $stateParams.entry;
            LocalCache.setObject('personDataBoardFlag', '');
            LocalCache.setObject('personDataBoardEmployee', {});
            if (Object.keys(LocalCache.getObject('kpiRelation')).length > 0) {
                $scope.formRefer = LocalCache.getObject('kpiRelation');
                $scope.formRefer.person = '';
                $scope.defaultDept = $scope.formRefer.department;
            }
            LocalCache.setObject('kpiRelation', '');
            //页面分页信息
            $scope.pages = {
                pageNum: '', //分页页数
                size: '', //分页每页大小
                total: '', //数据总数
            };
            // 初始化分页数据
            $scope.pages = inform.initPages();
            $scope.pages.size = '50';
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            initInfo();
            $scope.getData = getData;

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 250);
                $('#divTBDis').height(divHeight);
                $('#subDivTBDis').height(divHeight - 80);
            }

            /**
             * 初始化
             */
            function initInfo() {
                initTime();
                $scope.areaList = [];
                comService.getAreaList().then(function (data) {
                    $scope.areaList = data.data;
                });

                //获取员工岗位信息
                $scope.staffTitleList = [];
                comService.getParamList('STAFF_INFO_TITLE', 'NEW').then(function (data) {
                    $scope.staffTitleList = data.data;
                });
                //获取山东新北洋集团的下级部门信息
                $scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function (data) {
                    $scope.departmentList = data.data;
                    var select = { orgCode: '', orgName: '请选择' };
                    $scope.departmentList.unshift(select);
                    //不是通过菜单进来，说明是从个人看板调用的controller，所以除了考核人，其他查询条件不应该赋值
                    if ($scope.entryType !== 'menu') {
                        $scope.formRefer = {};
                        //获取当前登录者的姓名
                        $scope.formRefer.person = LocalCache.getSession('employeeName');
                        var person = LocalCache.getObject('personDataBoardEmployee');
                        if (person.name) {
                            $scope.formRefer.person = person.name;
                        }
                        getData();
                    } else {
                        getDepartmentCode();
                    }
                });
            }
            //获取部门权限码
            function getDepartmentCode() {
                comService.validDeptAuthority(LocalCache.getSession('loginName')).then(function (result) {
                    if (result.code === '0000') {
                        if (result.data.code === '00') {
                            $state.go('app.office.unAuthority');
                            return;
                        }
                        if (result.data.code === '01' || result.data.code === '02') {
                            getData(1);
                            return;
                        }
                        //重新设置部门信息，只显示有权限的部门列表
                        $scope.departmentList = result.data.deptList;
                        $scope.formRefer.department = $scope.departmentList[0].orgName;
                        $scope.defaultDept = $scope.formRefer.department;
                        getData(1);
                    }
                });
            }

            /**
             * 初始化检索条件年度与季度
             */
            function initTime() {
                //当前时间（Date类型）
                var date = new Date();
                //得到上一个季度的第一天
                var lastQuarterFirstDay = new Date(date.getFullYear(), date.getMonth() - 3, 1);
                var day = inform.format(lastQuarterFirstDay, 'yyyy-MM-dd').split('-');
                $scope.formRefer.years = $scope.formRefer.years != null ? $scope.formRefer.years : day[0];
                //设置季度
                var month = lastQuarterFirstDay.getMonth() + 1;
                $scope.formRefer.quarter = month > 6 ? '7' : '6';
            }

            function getData(pageNum) {
                var urlData = {
                    department: $scope.formRefer.department,
                    area: $scope.formRefer.area,
                    name: $scope.formRefer.employeeName,
                    titleSelected: $scope.formRefer.titleSelected,
                    person: $scope.formRefer.person,
                    currentPage: pageNum,
                    pageSize: $scope.pages.size,
                };
                kpiRelationService.getKpiRelationByPage(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.dataList = data.data.list;
                            if ($scope.dataList.length === 0) {
                                $scope.pages = inform.initPages(); //初始化分页数据
                                inform.common(Trans('tip.noData'));
                            } else {
                                // 分页信息设置
                                $scope.pages.total = data.data.total; // 页面数据总数
                                $scope.pages.star = data.data.startRow; // 页面起始数
                                $scope.pages.end = data.data.endRow; // 页面结束数
                                $scope.pages.pageNum = data.data.pageNum; //页号
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }

            /**
             * 重置
             */
            $scope.clearParams = function () {
                $scope.formRefer = {};
                $scope.formRefer.department = $scope.defaultDept;
                $scope.formRefer.titleSelected = ['062', '039', '049'];
            };
            /**
             * 点击
             * @param m
             */
            $scope.clickModal = function (m) {
                var quarter = null;
                if ($scope.formRefer.quarter !== '5') {
                    quarter = $scope.formRefer.quarter;
                }
                $state.go('app.index_bench', {
                    empId: m.employeeCode,
                    years: $scope.formRefer.years,
                    quarter: quarter,
                });
                if ($scope.entryType === 'menu') {
                    LocalCache.setObject('kpiRelation', $scope.formRefer);
                }
                LocalCache.setObject('personDataBoardFlag', 'personalStar');
                LocalCache.setObject('personDataBoardEmployee', m);
            };

            /**
             * 反馈问题上传文件
             */
            function submitForm(e) {
                //表单id  初始化表单值
                var formData = new FormData();
                //获取文档中有类型为file的第一个input元素
                var file = document.querySelector('input[type=file]').files[0];
                if (!file) {
                    inform.common('请先选择文件!');
                    return false;
                }
                if (file.size > AgreeConstant.fileSize) {
                    inform.common('上传文件大小不能超过2M');
                    fileChangeReset();
                    return false;
                }
                formData.append('file', file);
                var a = file.type;
                if (
                    !(
                        a === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                        a === 'application/vnd.ms-excel'
                    )
                ) {
                    inform.common('请选择Excel文档进行上传!');
                    return false;
                } else {
                    var modalInstance = $modal.open({
                        templateUrl: 'myModalContent.html',
                        controller: 'ModalInstanceCtrl',
                        size: 'sm',
                        resolve: {
                            items: function items() {
                                return '确定要导入吗！';
                            },
                        },
                    });
                    var uploadUrl = $rootScope.getWaySystemApi + 'kpiRelation/importKpiRelationData';

                    modalInstance.result.then(function () {
                        //开启遮罩层
                        inform.showLayer('上传中。。。。。。');
                        $.ajax({
                            url: uploadUrl,
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            beforeSend: function beforeSend(request) {
                                request.setRequestHeader(
                                    'Authorization',
                                    'Bearer ' + LocalCache.getSession('token') || ''
                                );
                            },
                            success: function success(result) {
                                if (result.code === AgreeConstant.code) {
                                    // 关闭遮罩层
                                    inform.closeLayer();
                                    layer.confirm(
                                        result.message,
                                        {
                                            title: false,
                                            btn: ['确定'],
                                        },
                                        function (confirmMsg) {
                                            layer.close(confirmMsg);
                                        }
                                    );
                                } else {
                                    inform.closeLayer();
                                    $modal.open({
                                        templateUrl: 'tpl/common/errorModel.html',
                                        controller: 'ModalInstanceCtrl',
                                        size: 'lg',
                                        resolve: {
                                            items: function () {
                                                return result.message;
                                            },
                                        },
                                    });
                                }
                                //移除文件名称 通过表单元素的reset方法实现选择文件的重置
                                $('#formUpload')[0].reset();
                            },
                            error: function error(_error) {
                                inform.common(Trans('tip.requestError'));
                            },
                        });
                    });
                }
            }

            /**
             * 导入反馈问题
             */
            $scope.selectFile = function () {
                //删除change事件
                $('#filesUpload').off('change');
                //绑定导入excel文件控件改变事件
                $('#filesUpload').change(submitForm);
                document.getElementById('filesUpload').click();
            };
            /**
             * 选择上传文件后事件
             */
            function fileChangeReset() {
                //通过表单元素的reset方法实现选择文件的重置
                $('#formUpload')[0].reset();
            }

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        },
    ]);
})();

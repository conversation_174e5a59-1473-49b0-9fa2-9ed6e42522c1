(function() {
    'use strict';
    app.controller("param_Change", ['$rootScope', '$scope', '$state', '$stateParams', 'inform', 'SystemService', 'Trans', 'AgreeConstant',
        function($rootScope, $scope, $state, $stateParams, inform, SystemService, Trans, AgreeConstant) {
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.param = {}; // 条件
            $scope.getData = getData; // 初始化函数
            $scope.getTypeCode = getTypeCode; // 获取参数类型值
            $scope.getTypeCode();
            $scope.onSubmit = onSubmit; // 保存修改的参数数据

            // 获取参数类型值
            function getTypeCode() {
                SystemService.getTypeCode()
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.typeCode = data.result;
                            $scope.getData();
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 根据ID 获取参数信息
            function getData() {
                SystemService.getParamById($stateParams.paramId)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.param = data.result;
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 保存修改的参数数据
            function onSubmit() {
                SystemService.saveParam($scope.param)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            inform.common(Trans("tip.saveSuccess"));
                            $state.go("app.system.param_Management");
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }
        }
    ]);
})();
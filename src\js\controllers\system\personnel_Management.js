/*
 * @Author: fubaole
 * @Date:   2017-09-28 13:45:34
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-16 09:53:29
 */
(function() {
    'use strict';
    app.controller("personnel_Management", ['FileUploader', '$rootScope', '$scope', 'inform', '$modal', '$state', 'Trans', 'SystemService','ConfigService', 'LocalCache', 'AgreeConstant',
        function(FileUploader, $rootScope, $scope, inform, $modal, $state, Trans, SystemService,ConfigService, LocalCache, AgreeConstant) {

            var interfaceMap = {};
            $scope.map = {}; //条件
            $scope.treeData = []; // 存放组织机构数据
            $scope.getData = getData; //初始化函数
            $scope.pages = inform.initPages(); // 初始化分页数据
            $scope.getData($scope.pages.pageNum); // 获取人员数据
            $scope.searchData = searchData; // 查询数据
            getTreeData(); // 获取树组织机构
            $scope.getOrgById = getOrgById; // 根据ID获取人员所在组织机构
            $scope.saveOrg = saveOrg; // 保存组织机构修改操作
            $scope.goAdd = goAdd; // 创建人员跳转
            $scope.open = open; // 状态改变弹框
            $scope.getLabelClass = getLabelClass; //修改文字颜色
            $scope.showEditOrg = LocalCache.getSession('currentUserName');
            // 排序
            $scope.title = 'employeeId';
            $scope.desc = true;
            $scope.order = order;

            // 存放选中的ID
            $scope.checked = [];
            $scope.selectAll = selectAll; // 全选函数
            $scope.selectOne = selectOne; // 单选函数

            // 设置侧边的高度,随窗口变动
            inform.autoHeight();
            window.onresize = inform.autoHeight;


            // 左侧树配置
            var Leftsetting = angular.copy(ConfigService.dataAndCb);
            Leftsetting.callback.beforeClick = nodeSelect; // 点击节点前回调
            // 右侧树配置
            var Rightsetting = angular.copy(ConfigService.radioConfig);
            Rightsetting.callback.onCheck = onCheck; // 点击节点回调

            // 排序
            function order(str) {
                $scope.title = str;
                $scope.desc = !$scope.desc;
            }

            // 查询
            function searchData() {
                $scope.select_all = false;
                interfaceMap = angular.copy($scope.map);
                getData(AgreeConstant.pageNum);
            }

            // 获取所有组织机构
            function getTreeData() {
                SystemService.getAllOrg()
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            angular.forEach(data.result, function(res, index) {
                                var jsonTree = {
                                    "id": res.orgId,
                                    "pId": res.parentId,
                                    "name": res.orgName,
                                    "open": res.orgId===AgreeConstant.treeRootNode ? true : false
                                };
                                data.result[index] = angular.extend(jsonTree, res);
                                $scope.treeData.push(data.result[index]);
                            });
                            $scope.treeData = inform.unique($scope.treeData);
                            $.fn.zTree.init($("#treeDemo"), Leftsetting, $scope.treeData);
                        } else {
                            inform.common(data.message);
                        }
                    }, function() {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 获取左侧树节点
            function nodeSelect(treeId, treeNode) {
                if(!treeNode.isOrg){
                    return false;
                }
                $scope.checkArray = treeNode.orgId;
                $scope.searchData($scope.pageNum);
                console.log($scope.checkArray);
            }

            // 获取人员表格数据
            function getData(num) {
                $scope.checked = [];
                $scope.select_all = false;
                if (!num) { inform.common(Trans('tip.pageNumTip')); return; }
                interfaceMap.orgId = $scope.checkArray;
                SystemService.getEmployeeListByMapWithPage(interfaceMap, parseInt(num), $scope.pages.size)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.pages.goNum = null;
                            var jsonData = angular.fromJson(data.result);
                            $scope.resultData = jsonData.list;
                            if ($scope.resultData.length===0) {
                                inform.common(Trans('tip.noData'));
                                $scope.pages = inform.initPages();
                            } else {
                                $scope.pages.total = jsonData.total;
                                $scope.pages.star = (jsonData.pageNum - 1) * jsonData.pageSize + 1;
                                $scope.pages.end = jsonData.pageNum * jsonData.pageSize;
                                $scope.pages.pageNum = jsonData.pageNum;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 根据ID获取人员所在组织机构
            function getOrgById(item) {
                $scope.employeeId = item.employeeId;
                $scope.orgTreeData = angular.copy($scope.treeData);
                SystemService.getOrgListByEmployeeId(item.employeeId)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            console.log(data.result);
                            angular.forEach($scope.orgTreeData, function(i) {
                                if (i.isOrg===false ) { i.nocheck = true; }
                                angular.forEach(data.result, function(j) {
                                    if (i.orgId===j.orgId) {
                                        i.checked = true;
                                    }
                                });
                            });
                            $.fn.zTree.init($("#rightTree"), Rightsetting, $scope.orgTreeData);
                        } else {
                            inform.common(data.message);
                        }
                    }, function() {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 选中
            function onCheck(e, treeId, treeNode) {
                $scope.orgChecked = []; // 存放选中的树节点（修改所在组织机构）
                var treeObj = $.fn.zTree.getZTreeObj("rightTree"),
                    nodes = treeObj.getCheckedNodes(true);
                angular.forEach(nodes, function(obj, i) {
                    $scope.orgChecked.push(nodes[i].id);
                });
                console.log($scope.orgChecked); //获取选中节点的值
            }

            // 保存组织机构修改操作
            function saveOrg() {
                if ($scope.orgChecked && $scope.orgChecked.length) {
                    SystemService.saveEmployeeToOrg($scope.employeeId, $scope.orgChecked)
                        .then(function(data) {
                            if (data.code===AgreeConstant.resultCode) {
                                inform.common(Trans("tip.saveSuccess"));
                                $("#edit_orgin").modal('hide');
                                getData(AgreeConstant.pageNum);
                            } else {
                                inform.common(data.message);
                            }
                        }, function() {
                            inform.common(Trans("tip.requestError"));
                        });
                } else {
                    inform.common(Trans("common.chooseOneOrEditOrg"));
                }
            }

            // 创建人员跳转
            function goAdd() {
                if (!$scope.checkArray) {
                    inform.common(Trans('common.chooseOneOrg'));
                } else {
                    $state.go("app.system.personnel_Add", { "orgId": $scope.checkArray });
                }
            }

            // 全选函数
            function selectAll() {
                if ($scope.select_all) {
                    $scope.checked = [];
                    angular.forEach($scope.resultData, function(i) {
                        i.checked = true;
                        // $scope.checked.push(i.employeeId);
                        $scope.checked.push(i);
                    });
                    console.log($scope.checked.length);
                } else {
                    angular.forEach($scope.resultData, function(i) {
                        i.checked = false;
                    });
                    $scope.checked = [];
                }
            }

            // 单选函数
            function selectOne() {
                angular.forEach($scope.resultData, function(i) {
                    var index = $scope.checked.indexOf(i);
                    if (index === -1 && i.checked) {
                        $scope.checked.push(i);
                    } else if (index !== -1 && !i.checked) {
                        $scope.checked.splice(index, 1);
                    }
                });
                if ($scope.resultData.length === $scope.checked.length) {
                    $scope.select_all = true;
                } else {
                    $scope.select_all = false;
                }
            }

            // 修改人员状态弹框
            function open(str) {
                if ($scope.checked.length) {
                    var employeeIds = [];
                    angular.forEach($scope.checked, function(res) {
                        if (res.status===str && str===AgreeConstant.forbiddenStatus) {
                            inform.common(Trans('personnel.choseDisabled'));
                            return;
                        }
                        if (res.status===str && str != AgreeConstant.forbiddenStatus) {
                            inform.common(Trans('personnel.choseEnabled'));
                            return;
                        }
                        if (res.employeeNo===AgreeConstant.superAdmin) {
                            inform.common(Trans('personnel.notChosenOpt'));
                            return;
                        }
                        if (res.employeeNo===LocalCache.getSession('currentUserName')) {
                            inform.common(Trans('personnel.loginUserNotOpt'));
                            return;
                        }
                        employeeIds.push(res.employeeId);
                    });

                    if (employeeIds.length===$scope.checked.length) {
                        var modalInstance = $modal.open({
                            templateUrl: 'myModalContent.html',
                            controller: 'ModalInstanceCtrl',
                            size: "sm",
                            resolve: {
                                items: function() {
                                    if (str===AgreeConstant.forbiddenStatus) {
                                        return Trans('personnel.sureOn');
                                    } else {
                                        return Trans('personnel.sureOff');
                                    }
                                }
                            }
                        });
                        modalInstance.result.then(function() {
                            savePersonStatus(employeeIds, str);
                        });
                    }
                } else {
                    inform.common(Trans('common.chooseOneOpt'));
                }
            }

            // 保存人员状态的修改
            function savePersonStatus(ids, status) {
                SystemService.changeEmployeeStatus(ids, status)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            getData($scope.pages.pageNum);
                        } else {
                            inform.common(data.message);
                        }
                    }, function() {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 添加文字颜色
            function getLabelClass(str) {
                if (str===AgreeConstant.forbiddenStatus) {
                    return "green";
                } else {
                    return "red";
                }
            }


            // ---------导入功能------------
            var uploader = $scope.uploader = new FileUploader({
                url:'上传的后台的路径',
                method: 'POST',
                // queueLimit: 1,//文件个数
                // autoUpload: true
            });
            uploader.headers.Authorization= 'Bearer ' + LocalCache.getSession("token")||''; //头信息 令牌
            // 上传回调
            uploader.onSuccessItem = function(fileItem, response, status, headers) {
                console.info('onSuccessItem', fileItem, response, status, headers);
            };
            uploader.onErrorItem = function(fileItem, response, status, headers) {
                console.info('onErrorItem', fileItem, response, status, headers);
            };
            // ---------导入功能------------

        }
    ]);
})();
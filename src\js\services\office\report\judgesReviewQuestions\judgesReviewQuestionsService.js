
(function() {
    'use strict';
  app.factory('judgesReviewQuestionsService', judgesReviewQuestionsService);
  judgesReviewQuestionsService.$inject=["HttpService",'$rootScope'];

  function judgesReviewQuestionsService(HttpService,$rootScope){
    var service={
    		selectDateByTitle : selectDateByTitle,
    		selectDataByEmployeeName : selectDataByEmployeeName,
    		selectDateByProductLine : selectDateByProductLine
    };
    return service;

    /**
     * 查询各职称的评审问题数量统计
     * @return
     */
    function selectDateByTitle(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'JudgesReviewQuestions/selectDataByTitle', urlData);
    }
   /**
    * 查询各评委的评审问题数量统计
    * @return
    */
    function selectDataByEmployeeName(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'JudgesReviewQuestions/selectDataByEmployeeName', urlData);
    }

    /**
    * 查询各产品线的评审问题数量统计
    * @return
    */
    function selectDateByProductLine(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'JudgesReviewQuestions/selectDataByProductLine', urlData);
    }


  }
})();
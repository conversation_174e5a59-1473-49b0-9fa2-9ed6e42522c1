(function () {
    app.controller('testTaskController', [
        'testTaskService',
        '$rootScope',
        '$scope',
        '$state',
        'inform',
        'Trans',
        'AgreeConstant',
        'comService',
        'LocalCache',
        function (testTaskService, $rootScope, $scope, $state, inform, Trans, AgreeConstant, comService, LocalCache) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.formInput = {};
            //获取缓存
            $scope.formInput = LocalCache.getObject('testTask_formRefer');
            if (!$scope.formInput.startTime) {
                $scope.formInput.startTime = inform.format(new Date(), 'yyyy') + '-01-01';
            }
            //对原缓存进行覆盖
            LocalCache.setObject('testTask_formRefer', { startTime: inform.format(new Date(), 'yyyy') + '-01-01' });
            //定义特殊项目类型对象(默认选择sp类项目)
            $scope.specailtype = {
                specailtype: 'SPECAIL_TYPE_1',
            };

            $scope.save = false;
            $scope.specailType = false;
            //初始化页面信息
            initPages();
            $scope.pages = {
                goNum: null, // 初始化跳转页码
                star: 0, //开始条数
                end: 0, //结束条数
                total: 0, // 总条数
                size: '20', //每页条数
                pageNum: AgreeConstant.pageNum, //默认页
            };
            //选择框阻塞原因
            $scope.reasonMap = [
                { value: '提测质量问题--冒烟不通过', label: '提测质量问题--冒烟不通过' },
                { value: '提测质量问题--sonar不合格、构建失败', label: '提测质量问题--sonar不合格、构建失败' },
                { value: '测试准入审核--需求、设计、提测点有问题', label: '测试准入审核--需求、设计、提测点有问题' },
                { value: '测试准入审核--提测单有问题', label: '测试准入审核--提测单有问题' },
                { value: '测试准入审核--自测不通过', label: '测试准入审核--自测不通过' },
                { value: '测试准入审核--测试条件不具备', label: '测试准入审核--测试条件不具备' },
                { value: '测试准入审核--计划变更', label: '测试准入审核--计划变更' },
                { value: '其他', label: '其他' },
            ];
            //阻塞原因
            $scope.reasonSearchMap = [
                { value: '1', label: '未填写' },
                { value: '提测质量问题--冒烟不通过', label: '提测质量问题--冒烟不通过' },
                { value: '提测质量问题--sonar不合格、构建失败', label: '提测质量问题--sonar不合格、构建失败' },
                { value: '测试准入审核--需求、设计、提测点有问题', label: '测试准入审核--需求、设计、提测点有问题' },
                { value: '测试准入审核--提测单有问题', label: '测试准入审核--提测单有问题' },
                { value: '测试准入审核--自测不通过', label: '测试准入审核--自测不通过' },
                { value: '测试准入审核--测试条件不具备', label: '测试准入审核--测试条件不具备' },
                { value: '测试准入审核--计划变更', label: '测试准入审核--计划变更' },
                { value: '其他', label: '其他' },
            ];
            $scope.isLinkProjectMap = [
                {
                    value: '0',
                    label: '已关联',
                },
                {
                    value: '1',
                    label: '未关联',
                },
            ];
            $scope.statusList = [
                {
                    value: '0',
                    label: '正常',
                },
                {
                    value: '1',
                    label: '已删除',
                },
            ];
            // 分页相关函数
            $scope.getData = getData;
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //判断按钮是否具有权限
            getButtonPermission();

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 获取按钮权限
             */
            function getButtonPermission() {
                var buttons = {
                    'Button-testTaskController-save': 'save',
                    'Button-testTaskController-specailType': 'specailType',
                };
                var urlData = {
                    userId: LocalCache.getSession('userId'),
                    parentPermission: 'ButtontestTaskController',
                    buttons: buttons,
                };
                comService.getButtonPermission(urlData, $scope);
            }

            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $('#divTBDis').height(divHeight);
                $('#subDivTBDis').height(divHeight - 65);
            }

            //重置
            $scope.rest = function () {
                $scope.formInput = {};
                $scope.formInput.startTime = inform.format(new Date(), 'yyyy') + '-01-01';
            };

            function initPages() {
                //获取提测单状态
                $scope.statusList = [];
                comService.getParamList('TEST_LIST_STATUS', 'TEST_LIST_STATUS').then(function (data) {
                    if (data.data) {
                        $scope.statusList = data.data;
                        $scope.formInput.status = 'blocked';
                    }
                });
                //获取所有项目名称
                $scope.projectList = [];
                comService.getProjectsName().then(function (data) {
                    $scope.projectList = angular.fromJson(data.data);
                    //在刷新页面时调用该方法
                    getData($scope.pages.pageNum);
                });

                //获取测试人员名单('29' 表示测试部门)
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('29').then(function (data) {
                    $scope.employeeList = data.data;
                });
                //获取所有人员名单(包括离职的员工)
                $scope.allEmployeeList = [];
                comService.getEmployeesByOrgId('', '1').then(function (data) {
                    $scope.allEmployeeList = data.data;
                });
                //获取特殊项目类型
                $scope.specailtypeList = [];
                comService.getParamList('SPECAIL_TYPE', 'SPECAIL_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.specailtypeList = data.data;
                    }
                });
            }

            /**
             * 获取下拉框数据。设置已经被其他特殊类型选中的项目，不会出现在下拉框中
             * @constructor
             */
            $scope.projectIdDisabledSelcet = function (specailtype) {
                var urlData = {
                    specailtypeProjectList: specailtype.projectId != null ? specailtype.projectId.join(',') : null, //项目id的list转换的字符串
                    specailtype: specailtype.specailtype, //特殊项目类型
                };
                //获取特殊项目类型
                testTaskService.getSpecailtypeAndAllProjectList(urlData).then(
                    function (data) {
                        if (data.data) {
                            //为特殊类型项目的项目名称字段赋值
                            var specailtypePi = data.data.specailtypeProjectList;
                            $scope.specailtype.projectId = [];
                            angular.forEach(specailtypePi, function (projectId) {
                                $scope.specailtype.projectId.push(parseInt(projectId.project_id));
                            });

                            //全部可选的项目信息
                            $scope.specailtypeProjectList = data.data.projectList;
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };

            //获取所有数据以分页的形式
            function getData(pageNum) {
                $scope.itemList = [];
                var urlData = {
                    projectId: $scope.formInput.projectId, //项目
                    testName: $scope.formInput.testName, //提测单名称
                    id: $scope.formInput.testId, //提测单Id
                    isLinkProject: $scope.formInput.isLinkProject, //是否关联office项目
                    proProjectName: $scope.formInput.proProjectName, //禅道项目名
                    startTime: inform.format($scope.formInput.startTime, 'yyyy-MM-dd'), //开始时间
                    endTime: inform.format($scope.formInput.endTime, 'yyyy-MM-dd'), //结束时间
                    page: pageNum, // 分页页数
                    pageSize: $scope.pages.size, // 分页每页大小
                    reason: $scope.formInput.reason, //阻塞原因
                    status: $scope.formInput.status, //提测单状态
                    owner: $scope.formInput.owner,
                    productLine: $scope.formInput.productLine,
                };
                testTaskService.getTestTaskInfo(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            var jsonData = data.data;
                            $scope.itemList = jsonData.list;
                            if ($scope.itemList.length === 0) {
                                $scope.pages = inform.initPages(); //初始化分页数据
                                $scope.pages.size = '20';
                            } else {
                                // 分页信息设置
                                $scope.pages.total = jsonData.total; // 页面总数
                                $scope.pages.star = jsonData.startRow; //页面起始数
                                $scope.pages.end = jsonData.endRow; //页面大小数
                                $scope.pages.pageNum = jsonData.pageNum; //页面页数
                            }
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }

            //更新提测单
            $scope.adviceSynchronize = function () {
                testTaskService.updateTestTaskInfo($scope.itemList).then(
                    function (data) {
                        layer.confirm(
                            data.message,
                            {
                                title: false,
                                btn: ['确定'],
                            },
                            function (result) {
                                layer.close(result);
                                getData(1);
                            }
                        );
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            //命名不规范查看
            $scope.nonstandardName = function () {
                LocalCache.setObject('testTask_formRefer', $scope.formInput);
                $state.go('app.office.nonstandardName');
            };
            /**
             * 获取下载信息
             */
            $scope.toExcel = function () {
                inform.modalInstance('确定要下载吗?').result.then(function () {
                    inform.downLoadFile('testTask/toExcel', null, '测试质量数据.xlsx');
                });
            };

            $scope.toTestListDetailExcel = function () {
                var urlData = {
                    projectId: $scope.formInput.projectId, //项目
                    testName: $scope.formInput.testName, //提测单名称
                    id: $scope.formInput.testId, //提测单Id
                    isLinkProject: $scope.formInput.isLinkProject, //是否关联office项目
                    proProjectName: $scope.formInput.proProjectName, //禅道项目名
                    startTime: inform.format($scope.formInput.startTime, 'yyyy-MM-dd'), //开始时间
                    endTime: inform.format($scope.formInput.endTime, 'yyyy-MM-dd'), //结束时间   					// 分页每页大小
                    reason: $scope.formInput.reason, //阻塞原因
                    status: $scope.formInput.status, //提测单状态
                    owner: $scope.formInput.owner,
                };
                inform.modalInstance('确定要下载吗?').result.then(function () {
                    inform.downLoadFile('testTask/toTestListDetailExcel', urlData, '提测单详情数据.xlsx');
                });
            };
            /**
             * 打开特殊项目管理
             */
            $scope.openSpecailTypeModal = function () {
                //定义特殊项目类型对象(默认选择sp类项目)
                $scope.specailtype = {
                    specailtype: 'SPECAIL_TYPE_1',
                };
                //获取下拉框数据。设置已经被其他特殊类型选中的项目，不会出现在下拉框中
                $scope.projectIdDisabledSelcet($scope.specailtype);
                $('#special_type').modal('show');
            };

            $scope.onSubmitModal = function () {
                var urlData = {
                    specailtypeProjectList:
                        $scope.specailtype.projectId != null ? $scope.specailtype.projectId.join(',') : null, //项目id的list转换的字符串
                    specailtype: $scope.specailtype.specailtype, //特殊项目类型
                };
                testTaskService.saveSpecialType(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            inform.common(data.message);
                            $('#special_type').modal('hide');
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        },
    ]);
})();

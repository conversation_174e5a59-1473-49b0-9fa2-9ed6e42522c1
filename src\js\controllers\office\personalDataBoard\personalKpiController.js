(function () {
    app.controller("personalKpiController", ['$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'roleKpiService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function ($rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, roleKpiService, inform, Trans, AgreeConstant, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //页面数据
            $scope.formRefer = {};
            //设置列表的高度
            setDivHeight();
            //季度下拉框数据源
            $scope.quarterSelect = [
                {
                    value: '6',
                    label: '上半年'
                },{
                    value: '7',
                    label: '下半年'
                },{
                    value: '5',
                    label: '年度'
                }];
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.getPersonalData = getData;
            $scope.excelData = excelData;
            $scope.projectExcelData = projectExcelData;
            $scope.reset = reset;
            //初始化信息
            initData();

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 初始化
             */
            function initData() {
                //获取当前登录者的empId
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ?
                    $scope.sessionEmpId : $stateParams.empId;
                //初始化查询时间
                initTime();
                //获取角色
                $scope.roleCodeList = [];
                $scope.personRoleCodeList = [];
                comService.queryEffectiveParam('ROLE_LIBRARY', 'ROLE_LIBRARY').then(function (data) {
                    if (data.data) {
                        $scope.roleCodeList = data.data;
                        $scope.personRoleCodeList = data.data;
                    }
                    getData();
                });

                //获取一级指标
                $scope.kpiCodeList = [];
                comService.queryEffectiveParam('KPI_LIBRARY', '2000').then(function (data) {
                    if (data.data) {
                        $scope.kpiCodeList = data.data;
                    }
                });
            }

            /**
             * 获取所有指标关联信息
             */
            function getData() {
                if ($scope.formRefer.years == null || $scope.formRefer.years === "") {
                    inform.common("请选择年度");
                    return;
                }
                if ($scope.formRefer.quarter == null || $scope.formRefer.quarter === "") {
                    inform.common("请选择季度");
                    return;
                }
                roleKpiService.selectPersonalData($scope.formRefer).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.developerData = data.data.kpiDeveloper;
                                $scope.teamLeaderData = data.data.kpiTeamLeader;
                                $scope.projectManagerData = data.data.kpiProjectManager;
                            } else {
                                inform.common(Trans("tip.noData"));
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
                //查询考核结果
                roleKpiService.selectPersonalKpi($scope.formRefer).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        if (null !== data.data) {
                            $scope.personalKpi = data.data;
                        }
                    }
                });
            }

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 210);
                $("#divTBDis").height(divHeight);
                $("#divTBDis2").height(divHeight);
                $("#divTBDis3").height(divHeight);
                $("#subDivTBDis2").height(divHeight - 40);
            }
            /**
             * 初始化检索条件年度与季度
             */
            function initTime(){

                //当前时间（Date类型）
                var date = new Date();
                //得到上一个季度的第一天
                var lastQuarterFirstDay = new Date(date.getFullYear() , date.getMonth() - 3 , 1);
                var day = inform.format(lastQuarterFirstDay, 'yyyy-MM-dd').split("-");
                $scope.formRefer.years = null != $stateParams.years ? $stateParams.years : day[0];
                //设置季度
                var month = lastQuarterFirstDay.getMonth() + 1;
                $scope.formRefer.quarter = null != $stateParams.quarter ? $stateParams.quarter : (inform.dateToQuarter(month)*1+1)+"";

                if($scope.formRefer.quarter === "1" || $scope.formRefer.quarter === "2") {
                    $scope.formRefer.quarter = "6";
                }
                if($scope.formRefer.quarter === "3" || $scope.formRefer.quarter === "4") {
                    $scope.formRefer.quarter = "5";
                }
            }

            function reset() {
                $scope.formRefer.years = "";
                $scope.formRefer.quarter = "";
            }

            $scope.showDetail = function (className) {
                var obj = document.getElementsByClassName(className);
                for (var i = 0; i < obj.length; i++) {
                    if(obj[i].style.display === "block") {
                        obj[i].style.display = "none";
                    } else {
                        obj[i].style.display = "block";
                    }
                }

            };
            $scope.goback = function () {
                $state.go('app.office.personKpi');
            };
            /**
             * 下载Excel
             */
            function excelData () {
                //拼装查询条件
                var urlData={
                    'quarter':$scope.formRefer.quarter,
                    'empId':$scope.formRefer.empId,
                    'years':$scope.formRefer.years
                };

                inform.downLoadFile ('roleKpi/downLoadExcel',urlData,'岗位角色述职（技术专家）.xlsx');

            }
            /**
             * 下载项目经理/团队Leaders的述职Excel
             */
            function projectExcelData () {
                //拼装查询条件
                var urlData={
                    'quarter':$scope.formRefer.quarter,
                    'empId':$scope.formRefer.empId,
                    'years':$scope.formRefer.years
                };

                inform.downLoadFile ('roleKpi/downLoadLeaderExcel',urlData,'项目经理TeamLeader述职.xlsx');

            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

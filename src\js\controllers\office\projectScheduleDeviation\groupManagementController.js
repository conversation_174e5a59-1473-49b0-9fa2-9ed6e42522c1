(function () {
    app.controller('groupManagement', [
        'comService',
        '$rootScope',
        '$scope',
        'projectManagementService',
        'inform',
        'Trans',
        'AgreeConstant',
        '$modal',
        '$state',
        '$stateParams',
        'LocalCache',
        function (
            comService,
            $rootScope,
            $scope,
            projectManagementService,
            inform,
            Trans,
            AgreeConstant,
            $modal,
            $state,
            $stateParams,
            LocalCache
        ) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //部门
            $scope.departmentList = [];
            //产品线
            $scope.projectLine = [];
            //获取缓存
            $scope.formRefer = {};
            $scope.formRefer = LocalCache.getObject('groupManagement_formRefer');
            if (Object.keys($scope.formRefer).length <= 0) {
                // 如果没有缓存调用默认方法
                resetParam();
            }
            //对原缓存进行覆盖
            LocalCache.setObject('groupManagement_formRefer', {});
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //保存查询出的产品线信息
            $scope.projectData = [];
            //被选中团队的集合
            $scope.proSelected = [];
            //设置按钮的权限
            $scope.deleted = false;
            $scope.add = false;
            $scope.update = false;
            //页面分页信息
            $scope.pages = {
                pageNum: '', //分页页数
                size: '', //分页每页大小
                total: '', //数据总数
            };
            // 初始化分页数据
            $scope.pages = inform.initPages();

            //团队状态下拉框数
            $scope.projectStatusSelect = AgreeConstant.teamStatusList;
            //考核状态(表格显示)
            $scope.kpiStatusMap = {
                0: '待考核',
                1: '考核中',
                2: '已存档',
            };
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //获取数据
            $scope.getData = getData;
            $scope.resetParam = resetParam;
            //初始化页面信息
            initPages();
            //判断按钮是否具有权限
            getButtonPermission();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 获取按钮权限
             */
            function getButtonPermission() {
                var buttons = {
                    'Button-projectAndGroupManagement-delete': 'deleted',
                    'Button-projectAndGroupManagement-add': 'add',
                    'Button-projectAndGroupManagement-update': 'update',
                };
                var urlData = {
                    userId: LocalCache.getSession('userId'),
                    parentPermission: 'projectAndGroupManagement',
                    buttons: buttons,
                };
                comService.getButtonPermission(urlData, $scope);
            }
            /**
             * 页面初始化
             */
            function initPages() {
                comService.queryEffectiveParam('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.projectLine = data.data;
                    }
                });
                comService
                    .getOrgChildren('D010053')
                    .then(function (data) {
                        $scope.departmentList = comService.getDepartment(data.data);
                        // 如果部门有默认值的情况，并且不在部门列表里面，就将部门置空
                        if ($scope.formRefer.department) {
                            const isInDepartmentList = $scope.departmentList.findIndex(
                                (i) => i.orgCode === $scope.formRefer.department
                            );
                            if (isInDepartmentList === -1) $scope.formRefer.department = '';
                        }
                        getData();
                    })
                    .catch((data) => inform.common(data.message));
            }
            /**
             * 获取团队
             */
            function getData(pageNum) {
                var urlData = {
                    department: $scope.formRefer.department, //部门
                    cname: $scope.formRefer.cname, //项目名称
                    projectManager: $scope.formRefer.projectManager,
                    productLine: $scope.formRefer.productLine, //产品线名
                    projectStatus: $scope.formRefer.projectStatus, //项目状态
                    projectTypeFlag: 'T', //项目类别
                    page: pageNum,
                    pageSize: $scope.pages.size,
                };
                projectManagementService.getProjectInfoList(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            //项目报告
                            $scope.projectData = data.data.list;
                            if ($scope.projectData.length === 0) {
                                $scope.pages = inform.initPages(); //初始化分页数据
                                inform.common(Trans('tip.noData'));
                            } else {
                                // 分页信息设置
                                $scope.pages.total = data.data.total; // 页面数据总数
                                $scope.pages.star = data.data.startRow; // 页面起始数
                                $scope.pages.end = data.data.endRow; // 页面结束数
                                $scope.pages.pageNum = data.data.pageNum; //页号
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 205);
                $('#divTBDis').height(divHeight);
                $('#subDivTBDis').height(divHeight - 85);
            }

            /**
             * 修改信息弹框，str存在，就是新增
             */
            $scope.popModal = function (item, str) {
                if (str === '0') {
                    //新增
                    $scope.groupInfoParam = {};
                } else {
                    if (null != item.operations) {
                        item.operations = item.operations.split(',');
                    }
                    //修改
                    $scope.groupInfoParam = angular.copy(item);
                }
                LocalCache.setObject('groupManagement_formRefer', $scope.formRefer);
                $state.go('app.office.groupManagementUpdate', {
                    groupInfoParam: JSON.stringify($scope.groupInfoParam),
                    isAdd: str,
                });
            };

            //查询指定项目的详情
            $scope.getGroupDetail = function (item) {
                LocalCache.setObject('groupManagement_formRefer', $scope.formRefer);
                $scope.groupInfoParam = angular.copy(item);
                LocalCache.setObject('project_detail', {
                    projectInfoParam: JSON.stringify($scope.groupInfoParam),
                });
                if ($stateParams.flag === 'entrance') {
                    //将项目名称写入同行评审缓存
                    LocalCache.setObject('formRefer_rp_0004', {
                        name: item.cname,
                        type: 'entrance',
                    });
                    $state.go('app.office.teamEntranceDetail', {
                        type: 1,
                    });
                } else {
                    $state.go('app.office.groupManagementDetail');
                }
            };

            //保存需要删除的团队id
            $scope.getGroupId = function (groupId) {
                $scope.currentGroupId = groupId;
                $scope.formRefer.deletedReason = '';
            };

            //逻辑删除团队信息
            $scope.deleteGroup = function () {
                $('#editDeleteReason_modal').modal('hide');
                inform.modalInstance('确定要删除吗？').result.then(function () {
                    var urlData = {
                        id: $scope.currentGroupId, //团队id;
                        projectTypeFlag: 'T', //项目类别
                        deletedReason: $scope.formRefer.deletedReason, //删除原因
                    };
                    projectManagementService.deleteProjectInfo(urlData).then(
                        function (data) {
                            if (data.code === AgreeConstant.code) {
                                getData(1);
                            }
                            inform.common(data.message);
                        },
                        function (error) {
                            inform.common(Trans('tip.requestError'));
                        }
                    );
                });
            };

            //下载团队基本信息表
            $scope.toExcel = function () {
                var selectIdList = [];
                if (!$scope.select_all && $scope.proSelected.length > 0) {
                    selectIdList = $scope.proSelected;
                }
                inform.modalInstance('确定要下载基本信息表吗？').result.then(function () {
                    var params = {
                        department: $scope.formRefer.department, //部门
                        cname: $scope.formRefer.cname, //项目名称
                        projectManager: $scope.formRefer.projectManager,
                        productLine: $scope.formRefer.productLine, //产品线名
                        projectStatus: $scope.formRefer.projectStatus, //项目状态
                        projectTypeFlag: 'T', //项目类别
                        selectIdList: selectIdList,
                    };
                    inform.downLoadFile(
                        'projectmanagement/toExcel',
                        params,
                        '团队基本信息表' + inform.format(new Date(), 'yyyy-MM-dd') + '.xlsx'
                    );
                });
            };

            /*** 全选函数*/
            $scope.selectAll = function () {
                if ($scope.select_all) {
                    $scope.proSelected = [];
                    angular.forEach($scope.projectData, function (i) {
                        i.checked = true;
                        $scope.proSelected.push(i.id);
                    });
                    console.log($scope.proSelected.length);
                } else {
                    angular.forEach($scope.projectData, function (i) {
                        i.checked = false;
                    });
                    $scope.proSelected = [];
                }
            };
            /*** 单选项目*/
            $scope.selectOne = function (i) {
                $scope.select_all = false;
                var index = $scope.proSelected.indexOf(i.id);
                if (index === -1 && i.checked) {
                    $scope.proSelected.push(i.id);
                } else if (index !== -1 && !i.checked) {
                    $scope.proSelected.splice(index, 1);
                }
            };
            //重置参数
            function resetParam() {
                $scope.formRefer = {};
                if ($stateParams.flag === 'entrance') {
                    $scope.formRefer.department = LocalCache.getSession('department');
                    if ($scope.departmentList.length > 0) {
                        const isInDepartmentList = $scope.departmentList.findIndex(
                            (i) => i.orgCode === $scope.formRefer.department
                        );
                        if (isInDepartmentList === -1) $scope.formRefer.department = '';
                    } else {
                        initPages();
                    }
                    $scope.formRefer.projectStatus = '进行中';
                }
            }
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        },
    ]);
})();

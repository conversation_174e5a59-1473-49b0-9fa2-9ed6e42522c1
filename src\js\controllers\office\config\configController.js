/*
* @Author: fubaole
* @Date:   2017-09-18 14:53:05
* @Last Modified by:   fubaole
* @Last Modified time: 2018-01-15 17:20:32
*/
(function () {
    app.controller("configController", ['$rootScope', '$scope','svnConfigService','inform','Trans','AgreeConstant',
        function ($rootScope, $scope,svnConfigService,inform,Trans,AgreeConstant) {
    	
    	//svn配置结构
    	$scope.formInsert = {username:'',password:'',path:''};
    	
    	$scope.upLogSize = upLogSize;//更新显示日志条数的方法
		
		$scope.logSize = "100";//默认显示日志条数
		
    	getData();
    	
    	getLogSize();
    	
    	//获取svn配置
        function getData() {
        	$scope.res = [];
        	svnConfigService.getConfig().then(
        		function(data) {
        			if (data.code===AgreeConstant.code) {
        				$scope.res = angular.fromJson(data.data);
        				
        				//页面赋值
        				$scope.formInsert.username = data.data.username;
        				$scope.formInsert.path = data.data.path;
        			} else {
        				inform.common(data.message);
        			}
        		}, function(error) {
        			inform.common(Trans("tip.requestError"));
        		}
        	);
        }
        
        //更新svn配置
        $scope.updateData = function (){
        	
        	if (!$scope.formInsert.username || $scope.formInsert.username==="") {
        		inform.common("请输入用户名");
        		return;
        	}
        	
        	if ($scope.formInsert.password==="") {
        		inform.common("请输入密码");
        		return;
        	}
        	
        	if (!$scope.formInsert.path || $scope.formInsert.path==="") {
        		inform.common("请输入仓库根目录");
        		return;
        	}
        	svnConfigService.updateConfig($scope.formInsert.username, $scope.formInsert.password, $scope.formInsert.path).then(function(data) {
        		
        		if (data.code===AgreeConstant.code) {
        			getData();
        			inform.common(Trans("更新完成"));
        		} else {
        			inform.common(data.message);
        		}
        	}, function(error) {
        		inform.common(Trans("tip.requestError"));
        	});
        };
		
		/**
		 * 更新显示日志日期限制
		 */
		function upLogSize() {
			svnConfigService.upLogSize($scope.logSize).then(function(data) {
                 if (data.code===AgreeConstant.code) {
                	 inform.common("更新数据成功");
                 } else {
                     inform.common(data.message);
                 }
             }, function() {
                 inform.common(Trans("tip.requestError"));
             });
         }
        
		//获取日志日期限制
		function getLogSize() {
			svnConfigService.getLogSize().then(function(data) {
                 if (data.code===AgreeConstant.code) {
                	 $scope.logSize = angular.fromJson(data.data);
                 } else {
                     inform.common(data.message);
                 }
             }, function() {
                 inform.common(Trans("tip.requestError"));
             });
         }
        
      }]);
})();
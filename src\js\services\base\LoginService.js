(function() {
	'use strict';
	app.factory('LoginService', loginService);
	loginService.$inject=['HttpService','$rootScope','inform'];
	function loginService(HttpService,$rootScope,inform){
		var service={
			getUserTokenByUserInfoByLdap:getUserTokenByUserInfoByLdap,
			getUserTokenByUserInfo:getUserTokenByUserInfo,
			changePassword:changePassword,
			getValidateCode:getValidateCode,
			checkValidateCode:checkValidateCode,
			permissionList:[],
			getUserByLoginName:getUserByLoginName,
			refreshToken:refreshToken
		};
		return service;

		function getUserTokenByUserInfo(param){
			return HttpService.postLogin($rootScope.getWaySystemApi+$rootScope.authorityName+'auth/getToken',param); // 单体
			// return HttpService.postLogin($rootScope.gateWayApi+'/uaa/oauth/token?username='+param.username+'&password='+param.password+'&grant_type=password',{}); // 微服务
		}

		function getUserTokenByUserInfoByLdap(param){
			//域校验接口
			return HttpService.postLogin($rootScope.getWaySystemApi+'auth/loginAction/loginCheck',param);
		}

		function changePassword(oldPwd,newPwd) {
			var urlData="";
            urlData = inform.formateGetRequest(urlData,"oldPwd",encodeURIComponent(oldPwd));
            urlData = inform.formateGetRequest(urlData,"newPwd",encodeURIComponent(newPwd));
			return HttpService.post($rootScope.getWaySystemApi+'org/changePassword'+urlData);
		}
		// 获取验证码
		function getValidateCode(){
			return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'auth/getValidateCode');
		}

		// 验证 验证码
		function checkValidateCode(res,checkRes){
			return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'auth/checkValidateCode?result='+res+'&checkResult='+checkRes);
		}
		
		// 根据用户名获取登录者部门
		function getUserByLoginName(){
			return HttpService.post($rootScope.getWaySystemApi + 'org/getUser/getUserByLoginName');
		}

		function refreshToken(){
			console.log("调用refresh方法");
			return HttpService.get($rootScope.getWaySystemApi+'auth/refreashToken',null);
		}

	}
})();
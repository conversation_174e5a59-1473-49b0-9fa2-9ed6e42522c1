
(function() {
    'use strict';
  app.factory('hardwareModeManagementService', hardwareModeManagementService);
  hardwareModeManagementService.$inject=["HttpService",'$rootScope'];

  function hardwareModeManagementService(HttpService,$rootScope){
    
    var service={
        getHardwareModeInfoList:getHardwareModeInfoList,
        updateHardwareModeInfo:updateHardwareModeInfo,
        addHardwareModeInfo:addHardwareModeInfo
    };
    return service;

    /**
     * 分页查询硬件型号信息
     */
    function getHardwareModeInfoList(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'hardwareModeManagement/getHardwareModeInfoList', urlData);
    }
    /**
    * 修改硬件型号信息
    */
    function updateHardwareModeInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'hardwareModeManagement/updateHardwareModeInfo', urlData);
    }
    //新增型号
    function addHardwareModeInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'hardwareModeManagement/addHardwareModeInfo', urlData);
    }

    }
})();

(function () {
    app.controller('meetingUpdateController', [
        'comService',
        '$http',
        'LocalCache',
        '$rootScope',
        '$state',
        '$stateParams',
        '$scope',
        '$modal',
        'meetingService',
        'mailService',
        'inform',
        'Trans',
        'AgreeConstant',
        'expertService',
        'OfficeFileTool',
        'dayjsService',
        '$timeout',
        function (
            comService,
            $http,
            LocalCache,
            $rootScope,
            $state,
            $stateParams,
            $scope,
            $modal,
            meetingService,
            mailService,
            inform,
            Trans,
            AgreeConstant,
            expertService,
            OfficeFileTool,
            dayjsService,
            $timeout
        ) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            const now = dayjsService.format(dayjsService.now());
            // 外部所有的编辑项
            $scope.changeParam = {};

            var flag = 0;
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            $scope.seletedTime = now;

            //初始化彈框
            $scope.timeSlot = [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19];
            $scope.minute = [];
            for (var i = 0; i < $scope.timeSlot.length; i++) {
                $scope.minute.push('00');
                $scope.minute.push('30');
            }
            $scope.nowTime = inform.format(new Date(), 'yyyy-MM-dd');
            // 8：00 - 19：30构成的时间数组
            $scope.timePriodList = [];
            for (var j = 0; j < 26; j++) {
                let period = '';
                if (j === 0 || j === 25) {
                    period = '';
                } else {
                    period = `${$scope.timeSlot[Math.floor((j - 1) / 2)]}:${$scope.minute[j - 1]}`;
                }
                $scope.timePriodList.push(period);
            }

            $scope.meetingInfoList = [];
            // 查询条件对应的会议室
            $scope.room = 0;
            // 所有会议室
            $scope.meetingRoomList;

            //会议状态下拉框数据源
            $scope.meetingStateSelect = [
                {
                    value: '00',
                    label: '草稿',
                },
                {
                    value: '01',
                    label: '已确定',
                },
            ];
            //会议形式下拉框数据源
            $scope.meetingShapeSelect = [
                {
                    value: '0',
                    label: '邮件',
                },
                {
                    value: '1',
                    label: '会议',
                },
            ];
            $scope.roleList = [
                {
                    value: '1',
                    label: '关键人员',
                },
                {
                    value: '2',
                    label: '参会人员',
                },
            ];
            var meetingTitleParam = [];
            $scope.keyFigureList = [];
            //绑定文件控件改变事件
            $('#files').change(submitForm);
            //初始化参数
            init();
            $scope.getEmployeeByTerritory = getEmployeeByTerritory;
            getEmployeeByTerritory();
            $scope.type = $stateParams.type;
            //时间选择框下拉框数据源
            $scope.timeSelect = [];
            $scope.getMeeting = getMeeting;
            $scope.meetingList = [];
            $scope.meetingSeeList = [];
            // 记录原始返回的会议信息
            $scope.MeetingBeforeList = [];
            //初始化会议规格对象
            $scope.spec = {
                startTime: null,
                endTime: null,
            };
            //设置时间选择框
            setTimeSelect();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $('#divTBDis').height(divHeight);
                $('#subDivTBDis').height(divHeight - 50);
                $('#buttonStyle').css(inform.getButtonStyle(500, 800));
            }
            //创建文件上传组件
            var paramObj = {
                listId: 'thelist',
                removeCall: function (id) {
                    var index = $scope.changeParam.attachmentAddressID.indexOf(id);
                    $scope.changeParam.attachmentAddress.splice(index, 1);
                    $scope.changeParam.attachmentSize.splice(index, 1);
                    $scope.changeParam.attachmentAddressID.splice(index, 1);
                },
                getFilePathCall: function (fileId) {
                    var index = $scope.changeParam.attachmentAddressID.indexOf(fileId);
                    var filePath = $scope.changeParam.attachmentAddress[index];
                    return filePath;
                },
                getSizeOfFiles: function () {
                    var size = 0;
                    for (var i = 0; i < $scope.changeParam.attachmentSize.length; i++) {
                        size = size + parseInt($scope.changeParam.attachmentSize[i]);
                    }
                    return size;
                },
                uploadSuccess: function (file, response) {
                    $scope.changeParam.attachmentAddress.push(response.data);
                    $scope.changeParam.attachmentAddressID.push(file.id);
                    $scope.changeParam.attachmentSize.push(file.size);
                },
            };
            var uploader = OfficeFileTool.createUploader(paramObj, 'meeting');
            var finalMsg = '';
            var msgFlag = 0;
            var delFlag = 0;
            var addRoom = [];
            var deletRoom = [];
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            $scope.toBefore = function () {
                const now = dayjsService.now().format('YYYY-MM-DD');
                if (now === $scope.seletedTime) {
                    return;
                }
                var time = new Date($scope.seletedTime);
                $scope.seletedTime = inform.format(new Date(time.getTime() - 24 * 60 * 60 * 1000), 'yyyy-MM-dd');
                $scope.getMeetingInfoByTime();
            };
            $scope.toAfter = function () {
                var time = new Date($scope.seletedTime);
                $scope.seletedTime = inform.format(new Date(time.getTime() + 24 * 60 * 60 * 1000), 'yyyy-MM-dd');
                $scope.getMeetingInfoByTime();
            };
            function init() {
                //获取员工信息
                $scope.loginMap = {};
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function (data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                        angular.forEach($scope.employeeList, function (employee) {
                            $scope.loginMap[employee.realName] = employee.loginName;
                        });
                        flag++;
                        getMeeting();
                    }
                });
                //获取会议类别
                $scope.meetingTypeList = [];
                comService.queryEffectiveParam('MeetingType', 'MeetingType').then(function (data) {
                    if (data.data) {
                        $scope.meetingTypeList = data.data;
                        flag++;
                        getMeeting();
                    }
                });
                //获取会议等级
                $scope.meetingGradeList = [];
                comService.queryEffectiveParam('MeetingType', 'MeetingType_2').then(function (data) {
                    if (data.data) {
                        $scope.meetingGradeList = data.data;
                        flag++;
                        getMeeting();
                    }
                });
                //获取行业领域
                $scope.expertTradeList = [];
                comService.queryEffectiveParam('ExpertTrade', 'ExpertTrade').then(function (data) {
                    if (data.data) {
                        $scope.expertTradeList = data.data;
                        flag++;
                        getMeeting();
                    }
                });
                // 获取会议室
                getMeetingRoom();
            }

            /**
             * 获取所有会议室
             */
            function getMeetingRoom(init, meetingPlace, index) {
                meetingService.getMeetingRoom().then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            flag++;
                            $scope.meetingRoomList = data.data;
                            getMeeting();
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }

            /**
             * 设置鼠标移入的title，显示会议信息
             */
            $scope.getTitle = function (id, period) {
                // 00：00 - 8：00和20：00 - 24：00 一定没有会议室占用
                if (!period) return '';

                const orderedMeetingRoom = $scope.meetingInfoList
                    ? $scope.meetingInfoList.filter((i) => i.address.includes(id))
                    : [];

                let currentMeeting = {};
                const curDay = dayjsService.format($scope.seletedTime);

                orderedMeetingRoom.forEach((i) => {
                    const startTimer = `${curDay} ${i.begintime}:00`;
                    const endTimer = `${curDay} ${i.endtime}:00`;

                    const startPeriod = `${curDay} ${period}:00`;
                    // 计算结束时间
                    const endPeriodDateTime = dayjsService.add(startPeriod, 30, 'minute');
                    const endPeriod = `${curDay} ${dayjsService.format(endPeriodDateTime, 'HH:mm')}:00`;

                    // 判断两个时间段是否重叠
                    const isOverlap =
                        dayjsService.isBefore(startPeriod, endTimer) && dayjsService.isAfter(endPeriod, startTimer);

                    if (isOverlap) {
                        currentMeeting = i;
                    }
                });
                if (Object.keys(currentMeeting).length === 0) return '';
                const titleVal = `会议名称：${currentMeeting.NAME}
召集人：${currentMeeting.callercallerLASTNAME || ''}
开始时间：${currentMeeting.begindate} ${currentMeeting.begintime}
结束时间：${currentMeeting.enddate} ${currentMeeting.endtime}`;

                return titleVal;
            };

            /**
             * 获取会议信息
             */
            function getMeeting() {
                // 设置默认的会议日期时间
                $scope.seletedTime = $scope.changeParam.meetingDay;
                // 等待全部下拉列表查询完毕
                if (flag !== 5) {
                    return;
                }
                var urlData = {
                    id: $stateParams.serialNumber,
                };
                meetingService.getMeeting(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.changeParam = data.data;

                            //会议室
                            for (var i = 0; i < $scope.changeParam.meetingRooms.length; i++) {
                                var roomObject = $scope.changeParam.meetingRooms[i];
                                var one = {
                                    // 新建会议成功之后对应的oa流程的id
                                    meetingId: roomObject.id,
                                    meetingPlace: roomObject.place,
                                    meetingRoom: roomObject.roomId.split(',').map((i) => Number(i)),
                                    startTime: roomObject.startTime,
                                    endTime: roomObject.endTime,
                                };

                                $scope.meetingSeeList.push(one);
                                $scope.meetingList.push(one);
                                $scope.MeetingBeforeList.push(one);
                            }
                            if (null != $scope.changeParam.accessory && '' !== $scope.changeParam.accessory) {
                                $scope.changeParam.attachmentAddress = $scope.changeParam.accessory.split(',');
                                $scope.changeParam.attachmentSize = $scope.changeParam.accessorySize.split(',');
                            } else {
                                $scope.changeParam.attachmentAddress = [];
                                $scope.changeParam.attachmentSize = [];
                            }
                            //会议纪要回显
                            if (
                                null != $scope.changeParam.meetingAccessory &&
                                '' !== $scope.changeParam.meetingAccessory
                            ) {
                                var fileArray = $scope.changeParam.meetingAccessory.replace(/\\/g, '/').split('/');
                                var name = fileArray[fileArray.length - 1];
                                createFile(name);
                            }
                            $scope.changeParam.attachmentAddressID = [];

                            $scope.keyFigureList = $scope.changeParam.keyFigures;
                            //创建回显的文件列表 返回文件id集合
                            var fileIdList = uploader.initShowFileList($scope.changeParam.attachmentAddress);
                            if (fileIdList.length > 0) {
                                $scope.changeParam.attachmentAddressID = fileIdList;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }

            /**
             * 设置新增后提示信息
             */
            function setMsg(mail, id) {
                if (msgFlag !== addRoom.length) {
                    return;
                }
                if (finalMsg === '') {
                    if (mail != null) {
                        packageMail(id);
                    } else {
                        //跳回主页面
                        layer.confirm(
                            '修改会议信息成功',
                            {
                                title: false,
                                btn: ['确定'],
                            },
                            function (result) {
                                layer.close(result);
                                $state.go('app.office.meetingController');
                            }
                        );
                    }
                } else {
                    //跳回修改
                    layer.confirm(
                        finalMsg,
                        {
                            title: false,
                            btn: ['确定'],
                        },
                        function (result) {
                            layer.close(result);
                            $state.go('app.office.meetingUpdateController', {
                                serialNumber: id,
                            });
                        }
                    );
                }
            }
            /**
             * 点击新增弹窗内的保存触发的事件
             */
            $scope.toSeeList = function () {
                if ($scope.meetingList[0].meetingRoom.length === 0) {
                    inform.common('请选择会议室');
                    return;
                }
                if (!$scope.meetingList[0].startTime) {
                    inform.common('请选择会议开始时间');
                    return;
                }
                if (!$scope.meetingList[0].endTime) {
                    inform.common('请选择会议结束时间');
                    return;
                }
                var flag = 0;

                // 校验所有会议的开始时间是否小于结束时间
                for (var i = 0; i < $scope.meetingList.length; i++) {
                    const item = $scope.meetingList[i];
                    const startTimeArray = item.startTime.split(':');
                    const endTimeArray = item.endTime.split(':');

                    if (
                        startTimeArray[0] > endTimeArray[0] ||
                        (startTimeArray[0] === endTimeArray[0] && startTimeArray[1] >= endTimeArray[1])
                    ) {
                        inform.common('第' + (i + 1) + '条会议的结束时间必须大于开始时间，请重新选择！');
                        return;
                    }
                }

                // 检查会议室是否可用
                for (var j = 0; j < $scope.meetingList.length; j++) {
                    if ($scope.meetingList[j].meetingRoomStatus === '不可用') {
                        flag++;
                    }
                }

                if (flag) {
                    inform.common('会议室占用,请重新选择.');
                    return;
                }

                $scope.meetingSeeList = $scope.meetingList.map((i) => {
                    return {
                        meetingRoomStatus: i.meetingRoomStatus,
                        meetingPlace: i.meetingPlace,
                        meetingRoom: i.meetingRoom,
                        startTime: i.startTime,
                        endTime: i.endTime,
                    };
                });
                // 与外部编辑页面保持同步
                $scope.changeParam.startTime = $scope.meetingList[0].startTime;
                $scope.changeParam.endTime = $scope.meetingList[0].endTime;
                $scope.changeParam.meetingDay = $scope.seletedTime;
                $('#add_meeting').modal('hide');
            };
            /**
             * 保存和提交会议信息
             */
            $scope.checkRoom = function (mail) {
                msgFlag = 0;
                finalMsg = '';

                if ($scope.meetingList.length === 0) {
                    $scope.UpdateInfo(mail);
                } else {
                    var engTimeMsg = '';
                    for (var j = 0; j < $scope.meetingList.length; j++) {
                        if ($scope.meetingList[j].endTime !== $scope.changeParam.endTime) {
                            engTimeMsg =
                                engTimeMsg + $scope.meetingList[j].meetingRoom + '预定结束时间与会议结束时间不符\n';
                        }
                    }
                    if (engTimeMsg === '') {
                        $scope.UpdateInfo(mail);
                    } else {
                        var modalInstance = $modal.open({
                            templateUrl: 'errorModel.html',
                            controller: 'ModalInstanceCtrl',
                            size: 'lg',
                            resolve: {
                                items: function () {
                                    return engTimeMsg;
                                },
                            },
                        });
                        modalInstance.result.then(function () {
                            $scope.UpdateInfo(mail);
                        });
                    }
                }
            };
            /**
             * 调用接口保存会议信息
             * @param {string} mail 邮件地址
             */
            function extracted(mail) {
                if ($scope.changeParam.meetingType == null) {
                    inform.common('请选择会议类别');
                    return;
                }
                if ($scope.changeParam.meetingType === 'MeetingType_2' && $scope.changeParam.meetingGrade == null) {
                    inform.common('请选择会议等级');
                    return;
                }
                if ($scope.keyFigureList.length === 0) {
                    inform.common('请选择人员');
                    return;
                }
                //校验关键角色列表是否有重复
                if (!verifyFigureList()) {
                    return;
                }
                //以逗号拼接关键角色
                var keyFigure = '';
                for (var i = 0; i < $scope.keyFigureList.length; i++) {
                    keyFigure = keyFigure
                        .concat($scope.keyFigureList[i].keyFigure)
                        .concat(',')
                        .concat($scope.keyFigureList[i].role)
                        .concat(';');
                }
                //获取当前时间是星期几
                var now = new Date($scope.changeParam.meetingDay);
                var meetingWeek = '';
                if (now.getDay() === 0) {
                    meetingWeek = '星期7';
                } else {
                    meetingWeek = '星期' + now.getDay();
                }

                var urlData = {
                    id: $scope.changeParam.id,
                    meetingType: $scope.changeParam.meetingType,
                    meetingDay: inform.format($scope.changeParam.meetingDay, 'yyyy-MM-dd'),
                    meetingWeek: meetingWeek,
                    startTime: $scope.changeParam.startTime,
                    endTime: $scope.changeParam.endTime,
                    meetingShape: $scope.changeParam.meetingShape,
                    meetingTheme: $scope.changeParam.meetingTheme,
                    projectAssistant: $scope.changeParam.projectAssistant,
                    meetingPlace: $scope.changeParam.meetingPlace,
                    meetingRoom: $scope.meetingSeeList
                        .map((i) => {
                            // 会议室名称的集合
                            const room = $scope.meetingRoomList
                                .filter((j) => i.meetingRoom.includes(j.id))
                                .map((j) => j.NAME)
                                .join(',');
                            return room;
                        })
                        .join(','),
                    meetingState: $scope.changeParam.meetingState,
                    meetingGrade: $scope.changeParam.meetingGrade,
                    keyFigure: keyFigure,
                    remark: $scope.changeParam.remark,
                    accessory: $scope.changeParam.attachmentAddress.join(','),
                    accessorySize: $scope.changeParam.attachmentSize.join(','),
                    meetingAccessory: $scope.changeParam.meetingAccessory,
                    meetingNumber: $scope.changeParam.meetingNumber,
                    // 用于判断是否需要新建会议
                    hasCreate:
                        $scope.meetingSeeList.length > 0 && !$scope.meetingSeeList.some((item) => item.meetingId),
                    meetingRooms: $scope.meetingSeeList.map((i) => {
                        i.roomId = i.meetingRoom.join(',');
                        i.place = i.meetingPlace;
                        i.room = $scope.meetingRoomList
                            .filter((j) => i.meetingRoom.includes(j.id))
                            .map((j) => j.NAME)
                            .join(',');
                        return i;
                    }),
                };

                meetingService.updateMeetingInfo(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            inform.common('保存成功');
                            $state.go('app.office.meetingController');
                        } else {
                            let msg = data.message;
                            if (data.message === '草稿') {
                                msg = '会议室被占用，请重新选择会议室';
                            }
                            inform.common(msg);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }

            //修改信息
            $scope.UpdateInfo = function (mail) {
                if (mail !== undefined && $scope.changeParam.meetingShape === '1' && $scope.meetingList.length === 0) {
                    inform.modalInstance('     您尚未预定会议室，确认继续发送邮件提醒吗？').result.then(function () {
                        extracted(mail);
                    });
                } else {
                    extracted(mail);
                }
            };
            /**
             * 获取人员信息
             */
            function getEmployeeByTerritory() {
                //拼接选中的行业领域
                var boxes = document.getElementsByName('experts');
                var territory = '';
                for (var i = 0; i < boxes.length; i++) {
                    if (boxes[i].checked) {
                        territory = territory + boxes[i].value + ',';
                    }
                }
            }

            /**
             * 文件选择事件
             */
            $scope.selectFile = function () {
                document.getElementById('files').click();
            };

            /**
             * 上传文件
             */
            function submitForm(e) {
                var formData = new FormData(document.getElementById('form12'));
                var file = e.currentTarget.files[0];
                if (!file) {
                    inform.common('请先选择文件!');
                    return false;
                } else if (file.size > AgreeConstant.fileSize) {
                    inform.common('上传文件大小不得超过2M，请分割后重新上传!');
                    $('#form12')[0].reset();
                    return false;
                }
                formData.append('file', file);
                inform.uploadFile('uploadFile/returnPath', formData, function func(result) {
                    $scope.changeParam.meetingAccessory = result.data;
                    // 关闭遮罩层
                    inform.closeLayer();
                    createFile(file.name);
                    getMeeting();
                });
            }

            /**
             * 回显
             */
            function createFile(name) {
                $('#meetingList').empty();
                $scope.fileName = name;
                $('#meetingList').append(
                    '<div  id="' +
                        'meetingList' +
                        '" class="item">' +
                        '<span id="fileNameDis">' +
                        name +
                        '</span>' +
                        '<p class="downLoad-this" style="display: inline-block;margin-left: 20px;" title="下载文件">' +
                        '<a class="glyphicon glyphicon-arrow-down green" style="top: 3px;"></a></p>' +
                        '</div>'
                );
            }

            /**
             * 下载文件
             */
            $('#meetingList').on('click', '.downLoad-this', function () {
                var filePath = $scope.changeParam.meetingAccessory;
                //拼装下载内容
                var urlData = {
                    filePath: filePath,
                };
                inform.downLoadFile('mail/downLoadFile', urlData, $scope.fileName);
            });

            /**
             * 设置时间选择框
             */
            function setTimeSelect() {
                for (var i = 6; i < 24; i++) {
                    //获取小时
                    var hour;
                    if (i < 10) {
                        hour = '0' + i;
                    } else {
                        hour = i;
                    }
                    for (var j = 0; j < 12; j++) {
                        //获取分钟（每10分钟，取一次）
                        var minutes;
                        if (j < 2) {
                            minutes = '0' + j * 5;
                        } else {
                            minutes = j * 5;
                        }
                        var value = {
                            value: hour + ':' + minutes,
                        };
                        //存入数组
                        $scope.timeSelect.push(value);
                    }
                }
            }

            /**
             * 校验关键角色列表中是否存在重复
             * 规则：
             * 1.重复则提示"关键角色×××,×××重复,请修改"并返回false
             * 2.无重复则返回true
             * @returns {boolean}
             */
            function verifyFigureList() {
                var verifyList = [];
                var duplicate = '';
                for (var i = 0; i < $scope.keyFigureList.length; i++) {
                    if (
                        verifyList.indexOf($scope.keyFigureList[i].keyFigure) > -1 &&
                        duplicate.indexOf($scope.keyFigureList[i].keyFigure) < 0
                    ) {
                        duplicate = duplicate.concat($scope.keyFigureList[i].keyFigure).concat(',');
                    }
                    verifyList.push($scope.keyFigureList[i].keyFigure);
                }
                //如果为空,说明无重复则返回true;
                if (!duplicate) {
                    return true;
                }
                //不为空,则提示哪些评委重复,并返回false
                inform.common('关键角色' + duplicate.substring(0, duplicate.length - 1) + '存在重复,请修改');
                return false;
            }
            /**
             * 发送邮件
             */
            function packageMail(id) {
                var urlData = {};
                if ($scope.changeParam.meetingType === 'MeetingType_2') {
                    urlData = {
                        beanName: 'com.snbc.office.service.impl.review.ReviewMeetingSeriveImpl',
                        serialNumber: id,
                    };
                } else if ($scope.changeParam.meetingType === 'MeetingType_1') {
                    urlData = {
                        beanName: 'com.snbc.office.service.impl.OrdinaryMeetingSeriveImpl',
                        serialNumber: id,
                    };
                }
                mailService.packageMail(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            var info = data.data;
                            info.addressees = [];
                            info.addresseesHand = [];
                            for (var i = 0; i < $scope.keyFigureList.length; i++) {
                                var name = $scope.keyFigureList[i].keyFigure;
                                if ($scope.loginMap[name] == null || $scope.loginMap[name] === '') {
                                    info.addresseesHand.push(name);
                                } else {
                                    info.addressees.push($scope.loginMap[name] + '@newbeiyang.com');
                                }
                            }
                            if (
                                $scope.changeParam.meetingAccessory != null &&
                                $scope.changeParam.meetingAccessory !== '' &&
                                $scope.type === 'return'
                            ) {
                                info.accessory = $scope.changeParam.meetingAccessory;
                                info.accessorySize = AgreeConstant.fileSize + '';
                            } else {
                                info.accessory = $scope.changeParam.attachmentAddress.join(',');
                                info.accessorySize = $scope.changeParam.attachmentSize.join(',');
                            }
                            info.theme = $scope.changeParam.meetingTheme;
                            LocalCache.setObject('mail_info', info);
                            $state.go('app.office.mailManagement', {
                                root:
                                    $scope.type === 'return'
                                        ? 'app.office.meetingController'
                                        : 'app.office.meetingUpdateController',
                                serialNumber: id,
                                dataInfo: $scope.type === 'return' ? 2 : null,
                            });
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            /**
             * 编辑界面外部的会议时间选择
             * 判断会议时间是否正常
             * 时间不正常，弹出提示信息，并清空最后选择的选择框的值
             * @param flag startTime：开始时间选择框  endTime：结束时间选择框
             */
            $scope.timeChange = function (flag, startTime, endTime) {
                // 如果开始时间改变了，自动设置结束时间为开始时间加一小时
                if (flag === 'startTime' && startTime !== '' && startTime != null) {
                    const today = dayjsService.format(dayjsService.now());
                    const startDateTime = `${today} ${startTime}:00`;

                    // 检查开始时间是否接近或等于23:00，超过这个时间不再自动设置结束时间
                    const hourValue = parseInt(startTime.split(':')[0]);
                    if (hourValue < 23) {
                        // 添加一小时
                        const endDateTime = dayjsService.add(startDateTime, 1, 'hour');
                        // 格式化为HH:mm
                        const formattedEndTime = dayjsService.format(endDateTime, 'HH:mm');

                        // 更新结束时间
                        $scope.changeParam.endTime = formattedEndTime;
                        endTime = formattedEndTime;
                    }
                }

                if (startTime !== '' && endTime !== '' && startTime != null && endTime != null) {
                    const today = dayjsService.format(dayjsService.now());
                    const startDateTime = `${today} ${startTime}:00`;
                    const endDateTime = `${today} ${endTime}:00`;

                    // 使用dayjsService比较时间
                    if (!dayjsService.isBefore(startDateTime, endDateTime)) {
                        inform.common('会议结束时间必须大于会议开始时间，请重新选择！！！');
                    }
                }
            };
            /**
             * 新增一个关键角色
             */
            $scope.addNewBind = function () {
                //评委信息
                var judge = {
                    role: '参会人员',
                    keyFigure: '',
                    flag: true,
                };
                $scope.keyFigureList.push(judge);
                var div = document.getElementById('up');
                div.scrollTop = div.scrollHeight;
            };

            //取消某一行人物信息
            $scope.deleteNewBind = function (index) {
                if (index >= 0) {
                    $scope.keyFigureList.splice(index, 1);
                }
            };
            //取消一行会议室信息（表格内部删除会议）
            $scope.delMeetingBind = function (index) {
                const item = $scope.meetingList[index];
                if (!item?.meetingId) {
                    // 如果没有meetingId，说明是未保存的记录，直接删除
                    if (index >= 0) {
                        $scope.meetingList.splice(index, 1);
                    }
                    // 如果会议列表为空，添加一条默认空白记录
                    if ($scope.meetingList.length === 0) {
                        var meetingListItem = {
                            seletedTime: $scope.seletedTime,
                            meetingPlace: null,
                            startTime: null,
                            endTime: null,
                            meetingRoom: [],
                            meetingRoomStatus: '未选择',
                        };
                        $scope.meetingList.push(meetingListItem);
                        $timeout(function () {}, 0);
                    }
                    return;
                }

                // 判断会议状态：已结束、中途取消还是正常取消
                const currentTime = dayjsService.now().format('YYYY-MM-DD HH:MM:ss');
                // 日期
                const { meetingDay } = $scope.changeParam;
                const startDateTime = `${meetingDay} ${item.startTime}:00`;
                const endDateTime = `${meetingDay} ${item.endTime}:00`;

                // 判断会议是否已结束
                const isMeetingEnded = dayjsService.isAfter(currentTime, endDateTime);

                // 如果会议已经结束，直接删除不调用取消接口
                if (isMeetingEnded) {
                    if (index >= 0) {
                        $scope.meetingList.splice(index, 1);
                    }
                    // 如果会议列表为空，添加一条默认空白记录
                    if ($scope.meetingList.length === 0) {
                        var meetingListItem = {
                            seletedTime: $scope.seletedTime,
                            meetingPlace: null,
                            startTime: null,
                            endTime: null,
                            meetingRoom: [],
                            meetingRoomStatus: '未选择',
                        };
                        $scope.meetingList.push(meetingListItem);
                        $timeout(function () {}, 0);
                    }
                    return;
                }

                // 如果当前时间在会议开始和结束之间，则为中途取消
                const isMidwayCancellation =
                    dayjsService.isAfter(currentTime, startDateTime) && dayjsService.isBefore(currentTime, endDateTime);

                // 设置取消类型信息
                const cancellationType = isMidwayCancellation ? '中途取消' : '正常取消';
                const confirmMessage = '该会议已预定成功，确定取消预定吗？';

                layer.confirm(
                    confirmMessage,
                    {
                        title: false,
                        btn: ['确定', '取消'],
                    },
                    function (result) {
                        layer.close(result);
                        cancelMeeting(item.meetingId, cancellationType, index);
                    },
                    function (index) {
                        layer.close(index);
                    }
                );
            };

            /**
             * 调用接口取消会议
             * @param {Number} id oa返回的id
             * @param {String} cancellationType 取消类型
             */
            function cancelMeeting(id, cancellationType, index) {
                const urlData = {
                    meetingid: id,
                };
                const api =
                    cancellationType === '中途取消' ? meetingService.cancelMeetingMidway : meetingService.cancelMeeting;
                api(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (index >= 0) {
                                $scope.meetingList.splice(index, 1);
                            }
                            $scope.meetingSeeList = [];
                            inform.common('取消会议成功');
                            // 如果会议列表为空，添加一条默认空白记录
                            if ($scope.meetingList.length === 0) {
                                var item = {
                                    seletedTime: $scope.seletedTime,
                                    meetingPlace: null,
                                    startTime: null,
                                    endTime: null,
                                    meetingRoom: [],
                                    meetingRoomStatus: '未选择',
                                };
                                $scope.meetingList.push(item);
                            }
                            $scope.getMeetingInfoByTime(false);
                            $timeout(function () {}, 0);
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }

            /**
             * 返回按钮
             */
            $scope.backModal = function () {
                $state.go('app.office.meetingController', { type: 2 });
            };

            /**
             * 会议日期时间
             */
            $scope.meetingDayOpen = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.meetingDayOne = true;
            };

            /**
             * 打开弹窗
             * 修改会议室信息
             */
            $scope.getMeetingInfoByTime = function (showModel = true) {
                showModel && $('#add_meeting').modal('show');
                $scope.MeetingTimeList = [];
                if ($scope.seletedTime == null || $scope.seletedTime === '') {
                    //默认查询时间
                    $scope.seletedTime = $scope.changeParam?.meetingDay || now;
                }

                // 如果会议列表为空，添加一条默认空白记录
                if ($scope.meetingList.length === 0) {
                    var item = {
                        seletedTime: $scope.seletedTime,
                        meetingPlace: null,
                        startTime: null,
                        endTime: null,
                        meetingRoom: [],
                        meetingRoomStatus: '未选择',
                    };
                    $scope.meetingList.push(item);
                }

                var urlData = {
                    begindate: $scope.seletedTime,
                    enddate: $scope.seletedTime,
                };
                meetingService.getMeetingInfoByTime(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            // 会议信息列表
                            $scope.meetingInfoList = data.data.map((i) => {
                                i.address = i.address.split(',').map((i) => Number(i));
                                return i;
                            });
                            $scope.getMeetingRoomStatus($scope.meetingList[0], '', '', 'dateChange');
                            // 在弹窗打开后，自动添加一条空白记录
                            $timeout(function () {}, 0);
                        } else {
                            $scope.meetingInfoList = [];
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };

            /**
             * 会议室是否被预定
             * @param {Number} id 会议室id
             * @param {String} period 开始的时间段
             * @returns {Boolean} 是否被预定
             */
            $scope.getTimePeriodList = (id, period) => {
                // 00：00 - 8：00和20：00 - 24：00 一定没有会议室占用
                if (!period) return false;

                const orderedMeetingRoom = $scope.meetingInfoList
                    ? $scope.meetingInfoList.filter((i) => i.address.includes(id))
                    : [];

                let isOrdered = false;
                const curDay = dayjsService.format($scope.seletedTime);

                orderedMeetingRoom.forEach((i) => {
                    const startTimer = `${curDay} ${i.begintime}:00`;
                    const endTimer = `${curDay} ${i.endtime}:00`;

                    const startPeriod = `${curDay} ${period}:00`;
                    // 计算结束时间
                    const endPeriodDateTime = dayjsService.add(startPeriod, 30, 'minute');
                    const endPeriod = `${curDay} ${dayjsService.format(endPeriodDateTime, 'HH:mm')}:00`;

                    // 判断两个时间段是否重叠
                    const isOverlap =
                        dayjsService.isBefore(startPeriod, endTimer) && dayjsService.isAfter(endPeriod, startTimer);

                    if (isOverlap) {
                        isOrdered = true;
                    }
                });
                return isOrdered;
            };

            // 获取会议室地点
            $scope.getMeetingRoomPlace = function (item) {
                const relevantMeetings = $scope.meetingRoomList.filter((meeting) =>
                    item.meetingRoom.includes(meeting.id)
                );
                return relevantMeetings.map((i) => `${i.mybuilding}${i.myfloor}`).join(',');
            };

            //获取会议室状态
            $scope.getMeetingRoomStatus = function (item, index, mail, tip) {
                // 检查输入参数
                if (!item || !item.meetingRoom || !item.startTime || !item.endTime || item.length === 0) {
                    if (index) {
                        $scope.meetingList[index].meetingRoomStatus = '不可用';
                    } else {
                        $scope.meetingList[0].meetingRoomStatus = '不可用';
                    }
                    if (tip !== 'dateChange') {
                        inform.common('请填写完整的会议室和时间信息');
                    }
                    return;
                }

                // 会议室ID可能是单个值或数组
                const roomIds = Array.isArray(item.meetingRoom) ? item.meetingRoom : [item.meetingRoom];
                const meetingDate = $scope.seletedTime || $scope.spec.meetingDay || now;
                const curDay = dayjsService.format(meetingDate);

                // 处理时间格式
                const startTimer = `${curDay} ${item.startTime}:00`;
                const endTimer = `${curDay} ${item.endTime}:00`;

                // 检查时间有效性
                if (dayjsService.isAfter(startTimer, endTimer)) {
                    if (index) {
                        $scope.meetingList[index].meetingRoomStatus = '不可用';
                    } else {
                        $scope.meetingList[0].meetingRoomStatus = '不可用';
                    }
                    inform.common('会议结束时间必须大于会议开始时间');
                    return;
                }

                // 默认状态为可用
                let isAvailable = true;

                // 检查每个会议室是否可用
                if ($scope.meetingInfoList && $scope.meetingInfoList.length > 0) {
                    // 筛选出所选会议室的预订记录
                    const relevantMeetings = $scope.meetingInfoList.filter((meeting) =>
                        // 判断两个数组是否有交集 - 只要有一个元素重叠即可
                        meeting.address.some((addr) => roomIds.includes(addr))
                    );

                    // 检查是否有时间重叠
                    relevantMeetings.forEach((meeting) => {
                        const meetingStartTime = `${curDay} ${meeting.begintime}:00`;
                        const meetingEndTime = `${curDay} ${meeting.endtime}:00`;

                        // 时间段重叠判断
                        const isOverlap =
                            dayjsService.isBefore(startTimer, meetingEndTime) &&
                            dayjsService.isAfter(endTimer, meetingStartTime);

                        if (isOverlap) {
                            isAvailable = false;
                        }
                    });
                }

                // 设置状态
                if (index) {
                    $scope.meetingList[index].meetingRoomStatus = isAvailable ? '可用' : '不可用';
                } else {
                    item.meetingRoomStatus = isAvailable ? '可用' : '不可用';
                }

                return isAvailable ? '可用' : '不可用';
            };

            //删除外部编辑页面某一行的信息
            $scope.deleteMeetingItem = function (index) {
                const item = $scope.meetingSeeList[index];
                if (!item?.meetingId) {
                    if (index >= 0) {
                        $scope.meetingSeeList.splice(index, 1);
                        $scope.meetingList = $scope.meetingSeeList;
                    }
                    return;
                }

                // 判断会议状态：已结束、中途取消还是正常取消
                const currentTime = dayjsService.now().format('YYYY-MM-DD HH:MM:ss');
                // 日期
                const { meetingDay } = $scope.changeParam;
                const startDateTime = `${meetingDay} ${item.startTime}:00`;
                const endDateTime = `${meetingDay} ${item.endTime}:00`;

                // 判断会议是否已结束
                const isMeetingEnded = dayjsService.isAfter(currentTime, endDateTime);

                // 如果会议已经结束，直接删除不调用取消接口
                if (isMeetingEnded) {
                    if (index >= 0) {
                        $scope.meetingSeeList.splice(index, 1);
                        $scope.meetingList = $scope.meetingSeeList;
                    }
                    return;
                }

                // 如果当前时间在会议开始和结束之间，则为中途取消
                const isMidwayCancellation =
                    dayjsService.isAfter(currentTime, startDateTime) && dayjsService.isBefore(currentTime, endDateTime);

                // 设置取消类型信息
                const cancellationType = isMidwayCancellation ? '中途取消' : '正常取消';
                const confirmMessage = '该会议已预定成功，确定取消预定吗？';

                layer.confirm(
                    confirmMessage,
                    {
                        title: false,
                        btn: ['确定', '取消'],
                    },
                    function (result) {
                        layer.close(result);
                        cancelMeeting(item.meetingId, cancellationType, index);
                    },
                    function (index) {
                        layer.close(index);
                    }
                );
            };

            $scope.changeMeetingRoom = function (item) {
                if (!item.meetingRoom || item.meetingRoom.length === 0) {
                    if ($scope.meetingList.length > 0) {
                        $scope.meetingList[0].meetingRoomStatus = '未选择';
                        $scope.meetingList[0].meetingPlace = '';
                    }
                    return;
                }

                // 如果已经选择了开始和结束时间，则更新状态
                if (item.startTime && item.endTime) {
                    $scope.meetingList[0].meetingRoomStatus = $scope.getMeetingRoomStatus(item);
                }
                $scope.meetingList[0].meetingPlace = $scope.getMeetingRoomPlace(item);
            };

            //当会议时间下拉框值改变时
            $scope.changeMeetingTime = function (field, item, index) {
                // 如果选择的是开始时间，自动设置结束时间为开始时间+1小时
                if (field === 'startTime' && item.startTime) {
                    // 检查开始时间是否接近或等于23:00，超过这个时间不再自动设置结束时间
                    const hourValue = parseInt(item.startTime.split(':')[0]);
                    if (hourValue < 23) {
                        const today = dayjsService.format(dayjsService.now());
                        const startDateTime = `${today} ${item.startTime}:00`;
                        // 添加一小时
                        const endDateTime = dayjsService.add(startDateTime, 1, 'hour');
                        // 格式化为HH:mm
                        const formattedEndTime = dayjsService.format(endDateTime, 'HH:mm');
                        // 更新结束时间
                        item.endTime = formattedEndTime;
                    }
                }
                // 确保开始时间和结束时间都已设置
                if (item.startTime && item.endTime) {
                    // 校验开始时间必须小于结束时间
                    const startTimeArray = item.startTime.split(':');
                    const endTimeArray = item.endTime.split(':');
                    if (
                        startTimeArray[0] > endTimeArray[0] ||
                        (startTimeArray[0] === endTimeArray[0] && startTimeArray[1] >= endTimeArray[1])
                    ) {
                        inform.common('会议结束时间必须大于会议开始时间，请重新选择！');
                        // 清空当前修改的时间字段
                        if (field === 'startTime') {
                            item.startTime = null;
                        } else {
                            item.endTime = null;
                        }
                        return;
                    }
                    if (item.meetingRoom && item.meetingRoom.length > 0) {
                        // 更新会议室状态
                        item.meetingRoomStatus = $scope.getMeetingRoomStatus(item, index);
                    }
                }
            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        },
    ]);
})();

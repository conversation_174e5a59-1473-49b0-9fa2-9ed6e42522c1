  (function() {
      app.controller("dataPermissionGroupDetails", ['dataPermissionGroupService', '$rootScope', '$scope', '$state', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
          function(dataPermissionGroupService, $rootScope, $scope, $state, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {
              /**
               * *************************************************************
               *             初始化部分                                 开始
               * *************************************************************
               */
              $scope.limitList = AgreeConstant.limitList; // 正则校验配置
              $scope.selectResource = {
                  participants: []
              };
              $scope.selectResource = {
                  userNameSelect: []
              };
              $scope.jsonData = {};
              //新增参数
              $scope.formInsert = {
                  name: '', //组名称
                  description: '', //描述      
                  userList: '', //人员列表
                  resourceList: '' //受限资源
              };
              $scope.changeParam = {
                  name: '', //组名称
                  description: '', //描述      
                  userList: '', //人员列表
                  resourceList: '' //受限资源
              };
              /**
               * *************************************************************
               *              初始化部分                                 结束
               * *************************************************************
               */
              /**
               * *************************************************************
               *              方法声明部分                                 开始
               * *************************************************************
               */
              //修改页面
              $scope.updateGroup = function() {
                  if (!$scope.changeParam.name) {
                      inform.common(Trans("组名称不能为空！"));
                      return false;
                  } else if (!$scope.changeParam.description) {
                      inform.common(Trans("描述不能为空！"));
                      return false;
                  } else if (!$scope.changeParam.userList) {
                      inform.common(Trans("访问人员不能为空！"));
                      return false;
                  } else if (!$scope.changeParam.resourceList) {
                      inform.common(Trans("数据资源不能为空！"));
                      return false;
                  } else {
                      var urlData = {
                          "id": $scope.changeParam.id, //组id
                          "name": $scope.changeParam.name, //组名称
                          "description": $scope.changeParam.description, //描述 
                          "userList": $scope.changeParam.userList, //访问人员
                          "permissionIdsList": $scope.changeParam.resourceList //受限资源
                      };
                      $("#seeAllMessage").modal('hide');
                      $("#edit_modal").modal('hide');
                      var modalInstance = $modal.open({
                          templateUrl: 'myModalContent.html',
                          controller: 'ModalInstanceCtrl',
                          size: "sm",
                          resolve: {
                              items: function() {
                                  return "确定要修改吗！";
                              }
                          }
                      });
                      modalInstance.result.then(function() {
                          dataPermissionGroupService.updateGroup(urlData).then(function(data) {
                              if (data.code === AgreeConstant.code) {
                                  inform.common(data.message);
                                  $state.go("app.office.dataPermissionGroup");
                               } else if(data.code ==="0004"){
                                      $("#add_modal").modal('hide');
                                        var modalInstance = $modal.open({
                                            templateUrl: 'myModalContent.html',
                                            controller: 'ModalInstanceCtrl',
                                            size: "sm",
                                            resolve: {
                                                items: function() {
                                                    return "组名重复，请重新填写！";
                                                }
                                            }
                                        });
                                        modalInstance.result.then(function() {
                                        })
                                    } 
                                else {
                                  inform.common(data.message);
                              }
                             
                          }, function(error) {
                              inform.common(Trans("tip.requestError"));
                          });
                      });
                  }
              };
              //新增页面
              $scope.addGroup = function() {
                  $scope.addGroupList = [];
                  if (!$scope.formInsert.name) {
                      inform.common(Trans("组名称不能为空！"));
                      return false;
                  } else if (!$scope.formInsert.description) {
                      inform.common(Trans("描述不能为空！"));
                      return false;
                  } else if (!$scope.formInsert.userList) {
                      inform.common(Trans("访问人员不能为空！"));
                      return false;
                  } else if (!$scope.formInsert.resourceList) {
                      inform.common(Trans("数据资源不能为空！"));
                      return false;
                  } else {
                      var urlData = {
                          'name': $scope.formInsert.name,
                          'description': $scope.formInsert.description,
                          'userList': $scope.formInsert.userList,
                          'permissionIdsList': $scope.formInsert.resourceList
                      };
                      $("#add_modal").modal('hide');
                      var modalInstance = $modal.open({
                          templateUrl: 'myModalContent.html',
                          controller: 'ModalInstanceCtrl',
                          size: "sm",
                          resolve: {
                              items: function() {
                                  return "确定要添加吗！";
                              }
                          }
                      });
                      modalInstance.result.then(function() {
                          dataPermissionGroupService.addGroup(urlData).then(function(data) {
                              if (data.code === AgreeConstant.code) {
                                  inform.common(data.message);

                                  //初始化新增参数
                                  $scope.selectResource = {
                                      participants: []
                                  };
                                  $scope.selectResource = {
                                      userNameSelect: []
                                  };
                                 $state.go("app.office.dataPermissionGroup");
                              } else if(data.code ==="0004"){
                                        $("#add_modal").modal('hide');
                                        var modalInstance = $modal.open({
                                            templateUrl: 'myModalContent.html',
                                            controller: 'ModalInstanceCtrl',
                                            size: "sm",
                                            resolve: {
                                                items: function() {
                                                    return "组名重复，请重新填写！";
                                                }
                                            }
                                        });
                                        modalInstance.result.then(function() {
                                        })
                                } else {
                                   inform.common(data.message);
                              }
                             
                          }, function(error) {
                              inform.common(Trans("tip.requestError"));
                          });
                      });
                  }
              };
              //受限数据资源列表
              $scope.resourceSelect = function() {
                  $scope.resourceList = [];
                  dataPermissionGroupService.getResourceList().then(function(data) {
                      if (data.data) {
                          $scope.resourceList = data.data;
                          $scope.userNameSelect();
                      }
                  });
              };
              //获取数据权限白名单
              $scope.userNameSelect = function() {
                  $scope.WhiteList = [];
                  dataPermissionGroupService.getWhiteList().then(function(data) {
                      if (data.data) {
                          $scope.WhiteList = data.data;
                          if ($stateParams.item) {
                              $scope.changeParam = JSON.parse($stateParams.item);
                              $scope.changeParam.resourceList = $scope.changeParam.permissionIdsList;
                          }
                      }
                  });
              };


              $scope.goback = function() {
                  $state.go("app.office.dataPermissionGroup");
              };
              /**
               * *************************************************************
               *              方法声明部分                                 结束
               * *************************************************************
               */
              $scope.resourceSelect();
          }
      ]);
  })();
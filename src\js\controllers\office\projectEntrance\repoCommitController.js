//# sourceURL=js/controllers/office/projectEntrance/repoCommitController.js
(function () {
    app.controller("repoCommit", [ '$scope','$rootScope','$stateParams','$state','LocalCache','inform','Trans','teamEntranceDetailsService','AgreeConstant','$modal',
        function ($scope,$rootScope, $stateParams,$state,LocalCache,inform,Trans,teamEntranceDetailsService,AgreeConstant,$modal) {
             /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //分页
            $scope.pages = inform.initPages(); // 初始化分页数据
            $scope.projectId = $stateParams.projectId;
            $scope.repoName = $stateParams.repoName;
            $scope.startTime = $stateParams.startTime
            $scope.endTime = $stateParams.endTime
            //设置列表的高度
            // setDivHeight();
            // //窗体大小变化时重新计算高度
            // $(window).resize(setDivHeight);

            
            //获取缓存
            $scope.formRefer = LocalCache.getObject('repoCommit_formRefer');
            //对原缓存进行覆盖
            LocalCache.setObject('repoCommit_formRefer', {});
            $scope.getData = getData; 			// 分页相关函数
            getData();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            function initTime() {
                //设置默认时间
                if ($scope.formRefer.endTime==null){
                    var now = new Date();
                    var endDate = inform.format(now, 'yyyy-MM-dd');
                    var startDate = inform.format(now,"yyyy-MM-01");
                    //默认开始时间
                    $scope.formRefer.endTime = endDate;
                    $scope.formRefer.startTime = startDate;
                }
            }

            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (165 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 80);
            }
           //获取所有数据以分页的形式
           function getData(pageNum) {
            $scope.save=false;
            var urlData ={
                    'repoName': $scope.repoName,// 仓库名称
                    'startTime': $scope.startTime,//开始时间
                    'endTime': $scope.endTime, //结束时间
                    'realName':$scope.formRefer.pushcnuser, //执行状态
                    'projectId': $scope.projectId,//团队
                    'page': pageNum,//当前页数
                    'pageSize': $scope.pages.size//每页显示条数
                };
            teamEntranceDetailsService.getRepoCommitData(urlData).then(function(data) {
                        if (data.code===AgreeConstant.code) {
                            // 项目详情
                            $scope.repoCommitList = data.data.list;
                            if ($scope.repoCommitList.length===0) {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages();
                            } else {
                                // 分页信息设置
                                $scope.pages.total = data.data.total;
                                $scope.pages.star = data.data.startRow;
                                $scope.pages.end = data.data.endRow;
                                $scope.pages.pageNum = data.data.pageNum;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
        }
  
              /**
             * 重置
             */
              $scope.reset = function() {
                $scope.formRefer={};
            }

            //返回工程数据
            $scope.gobackProjectData = function(){
                $state.go("app.office.teamEntranceDetail", {type:11});
            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
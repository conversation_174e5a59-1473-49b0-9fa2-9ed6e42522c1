//# sourceURL=js/services/office/trackingSheet/trackingSheetService.js
(function () {
    'use strict';
    app.factory('trackingSheetService', trackingSheetService);
    trackingSheetService.$inject = ["HttpService", '$rootScope'];

    function trackingSheetService(HttpService, $rootScope) {

        var service = {
            getData:getData,
            getRelatedData:getRelatedData,
            getUnRelatedList:getUnRelatedList,
            manageDetail:manageDetail,
            keepOnFile:keepOnFile,
            delData:delData,
            toTask:toTask,
            updateTrackingSheetZenTao:updateTrackingSheetZenTao,
            seeTask:seeTask,
            getProductByProjectId:getProductByProjectId,
            getBugDetailByBugId:getBugDetailByBugId,
            addBugInfo:addBugInfo,
            syncTrackingSheetDetail:syncTrackingSheetDetail,
            getZentaoBugDetail:getZentaoBugDetail,
            getConfirmPersonProjectList:getConfirmPersonProjectList,
            saveConfirmPerson:saveConfirmPerson,
            updateReopenReason:updateReopenReason
        };
        return service;

        function getUnRelatedList(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'trackingSheet/getUnRelatedData', urlData);
        }
        function getRelatedData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'trackingSheet/getRelatedData', urlData);
        }
        /**
         * 获取跟踪单信息
         */
        function getData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'trackingSheet/getData', urlData);
        }
        /**
         * 维护跟踪单信息
         */
        function manageDetail(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'trackingSheet/manageDetail', urlData);
        }
        /**
         * 存档
         */
        function keepOnFile(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'trackingSheet/keepOnFile', urlData);
        }
        /**
         * 删除
         */
        function delData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'trackingSheet/delData', urlData);
        }
        /**
         * 转任务
         */
        function toTask(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'zentaoTask/toTask', urlData);
        }
        /**
         * 根据转完的任务ID 更新表
         */
         function updateTrackingSheetZenTao(urlData){
             return HttpService.post($rootScope.getWaySystemApi + 'trackingSheet/updateTrackingSheetZenTao', urlData);
         }
       /**
        * 查看任务详情
        */
        function seeTask(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'zentaoTask/seeTask', urlData);
        }
       /**
        * 同步
        */
        function syncTrackingSheetDetail(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'trackingSheet/syncTrackingSheetDetail', urlData);
        }
        /**
         * 通过项目id获取所属产品
         */
        function getProductByProjectId(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'trackingSheetToBug/getProductByProjectId', urlData);
        }
        /**
         * 通过bugId获取bug信息
         */
        function getBugDetailByBugId(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'trackingSheetToBug/getBugDetailByBugId', urlData);
        }
        /**
         * 新建禅道bug
         */
        function addBugInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'trackingSheetToBug/addBugInfo', urlData);
        }
        /**
         * 查看禅道bug详情
         */
        function getZentaoBugDetail(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'trackingSheetToBug/getZentaoBugDetail', urlData);
        }

        /**
         * 获取确认人与项目的对应关系
         */
        function getConfirmPersonProjectList(){
            return HttpService.post($rootScope.getWaySystemApi + 'trackingSheet/getConfirmPersonProjectList');
        }

        /**
         * 保存确认人与项目的对应关系
         */
        function saveConfirmPerson(data){
            return HttpService.post($rootScope.getWaySystemApi + 'trackingSheet/saveConfirmPerson',data);
        }

        function updateReopenReason(data){
            return HttpService.post($rootScope.getWaySystemApi + 'trackingSheet/updateReopenReason',data);
        }
    }
})();

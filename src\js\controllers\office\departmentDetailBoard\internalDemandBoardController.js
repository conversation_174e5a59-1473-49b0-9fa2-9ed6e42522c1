(function () {
  app.controller('internalDemandBoardController', [
    'LocalCache',
    'inform',
    'Trans',
    '$rootScope',
    'comService',
    'AgreeConstant',
    '$scope',
    '$stateParams',
    '$state',
    'deptBoardFactory',
    'PLMService',
    'internalDemandBoardService',
    function (
      LocalCache,
      inform,
      Trans,
      $rootScope,
      comService,
      AgreeConstant,
      $scope,
      $stateParams,
      $state,
      deptBoardFactory,
      PLMService,
      internalDemandBoardService
    ) {
      // 初始化
      deptBoardFactory.init($scope, '延期未完成');
      //初始化查询
      $scope.getData = getData;

      //初始化信息
      function getData() {
        getTotalData();
        $scope.changeType($scope.type);
        getInternalDemandInfo($scope.type);
      }
      //重置
      $scope.resetParam = function () {
        deptBoardFactory.initTime($scope, '本年度');
      };

      //获取完成情况状态
      $scope.getCompleteStatus = function (status) {
        if (status === 1) {
          return '是';
        } else if (status === 0) {
          return '否';
        } else {
          return '';
        }
      };

      // 获取总体指标以及页签数字
      function getTotalData() {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          departmentId: $scope.formRefer.orgCode,
        };
        $scope.showStatisticsInfo = false;
        internalDemandBoardService.getInternalDemandBoardTotal(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              //总体指标数据
              $scope.InternalDemandStatisticsData = result.data.statisticsData;
              //获取每个页签的数字
              $scope.top5 = {};
              var wrapperData = result.data.wrapperData;
              for (let i = 0; i < wrapperData.length; i++) {
                if (wrapperData[i].title === '延期未完成') {
                  $scope.top5.delayNoFinishData = wrapperData[i].number1;
                } else if (wrapperData[i].title === '临期') {
                  $scope.top5.temporaryData = wrapperData[i].number1;
                } else if (wrapperData[i].title === '未到期') {
                  $scope.top5.unexpiredData = wrapperData[i].number1;
                } else if (wrapperData[i].title === '尚未制定计划') {
                  $scope.top5.noPlanYetData = wrapperData[i].number1;
                } else if (wrapperData[i].title === '延期完成') {
                  $scope.top5.DelayFinishData = wrapperData[i].number1;
                } else if (wrapperData[i].title === '已拒绝') {
                  $scope.top5.rejected = wrapperData[i].number1;
                } else if (wrapperData[i].title === '按时完成') {
                  $scope.top5.planFinishData = wrapperData[i].number1;
                }
              }
              $scope.showStatisticsInfo = true;
            } else {
              inform.common(result.message);
              $scope.showStatisticsInfo = true;
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
            $scope.showStatisticsInfo = true;
          }
        );
      }

      // 切换点击的页签的完成情况并查询
      $scope.changeType = function (type) {
        $scope.type = type;
        getInternalDemandInfo(type);
      };

      //查询每个页签的详细信息
      function getInternalDemandInfo(type) {
        var UrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          departmentId: $scope.formRefer.orgCode,
          demandCompleteMessage: type,
        };
        internalDemandBoardService.getInternalDemandByType(UrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.sheetData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }

      // 页面加载后触发
      $scope.$watch('$viewContentLoaded', function () {
        //读缓存
        var localFormRefer = LocalCache.getObject('departmentList_formRefer');
        if (Object.keys(localFormRefer).length > 0) {
          $scope.formRefer = localFormRefer;
          $scope.butFlag = localFormRefer.searchTimeString;
        }
        if ($stateParams.orgCode) {
          $scope.formRefer.orgCode = $stateParams.orgCode;
        }
        console.log(localFormRefer);
        getData();
      });

      $scope.title = '';
      $scope.desc = true;
      // 排序
      $scope.order = (str) => {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      };
    },
  ]);
})();

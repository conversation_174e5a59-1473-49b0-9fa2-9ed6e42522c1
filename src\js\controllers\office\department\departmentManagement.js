(function () {
    app.controller("departmentManagement", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','departmentService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope,comService,$scope,$state,$stateParams, $modal,departmentService,inform,Trans,AgreeConstant,LocalCache,$http) {
       	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
        $scope.getData = getData; //获取信息
        initEmployee(); //初始化人员名单
        initPrimaryDeptList();//初始化一级部门列表
        $scope.primaryDept = '';//初始化一级部门code

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
        /**
         * 设置列表的高度
         */
        function setDivHeight(){
            //网页可见区域高度
             var clientHeight = document.body.clientHeight;
             var divHeight = clientHeight - (180);
             $("#divTBDis").height(divHeight);
             $("#subDivTBDis").height(divHeight - 65);
        }
        /**
         * 初始化根据用户名获取一级部门列表
         */
        function initPrimaryDeptList() {
            $scope.primaryDeptList = [];
            comService.getOrgChildren('0002').then(function(data) {
                 if (data.data) {
                     $scope.primaryDeptList = data.data;
                 }
                 getData();
            });
        }


         /**
         * 初始化员工信息
         */
        function initEmployee() {
            //获取员工信息
            $scope.employeeList = [];
            comService.getEmployeesByOrgId('').then(function(data) {
                if (data.data) {
                    $scope.employeeList = data.data;
                }
            });
        }

        //查询一级部门下的数据
        $scope.selectData = function (){
            getData();
        }
    	/**
         * 获取所有部门信息
         */
        function getData() {
            var urlData = {
                'parent': $scope.primaryDept,//父级部门
            };
            departmentService.getOrgChildren(urlData).then(function (data) {
            	if (data.code===AgreeConstant.code) {
                	if(data.data.length===0){
                		$scope.depList = {};
                		inform.common(Trans("tip.noData"));
                	} else {
                		//项目详情
                		$scope.depList = data.data;
                 	}
                 } else {
                    inform.common(data.message);
                 }
            },
            function () {
                 inform.common(Trans("tip.requestError"));
            });
        }
        /**
   	  	 * 更新部门信息
   	  	 */
        $scope.saveInfo = function (m) {
        	if (m.orgName==="" || m.orgName==null){
        		inform.common("请输入部门名称");
        		return;
        	}
        	if (m.orgManager==="" || m.orgManager==null){
        		inform.common("请选择部门负责人");
        		return;
        	}

            var urlData = {
                'orgName': m.orgName,//部门名称
                'orgCode': m.orgCode,//部门代码--不可改
                'orgDesc': m.orgDesc,//描述
                'orgManager': m.orgManager,//部门主要负责人
                'viceOrgManagers': m.viceOrgManagers//部门次要负责人集合
            };
            departmentService.upInfo(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    layer.confirm(data.message,{
                        title:false,
                        btn:['确定']
                    },function(result){
                        layer.close(result);

                    });
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        };
	 		/**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */	
		
	}]);
})();
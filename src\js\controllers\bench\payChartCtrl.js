(function () {
    'use strict';
    app.controller("payChartCtrl", ['$scope', '$rootScope', 'inform', 'Trans','AgreeConstant',
        function ($scope, $rootScope, inform, Trans, AgreeConstant) {
    	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	//设置饼状图
    	payTypeOptionFun();
    	/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
        /**
         * 设置饼状图数据
         */
        function payTypeOptionFun() {
        	$scope.myChartsPayType = $scope.myChartsPayType ? $scope.myChartsPayType : echarts.init(document.getElementById('payChart'));
        	$scope.payOption = {
        		//标题
        		title:{
        			text:'个人工时信息统计-总工时：1200小时',
        		    top:'bottom',
        		    left:'center',
        		    textStyle:{
        		    	fontSize: 14,
        		        fontWeight: '',
        		        color: '#333'
        		    },
        		},
        		//提示框，鼠标悬浮交互时的信息提示
            	tooltip: {
                	trigger: 'item',
                	formatter: '{a} <br/>{b}: {c} ({d}%)'
            	},
            	//图例属性，以饼状图为例，用来说明饼状图每个扇区，data与下边series中data相匹配
               /*legend: {
                    orient: 'horizontal',
                    left: 10,
                    data: ['低级质量信息','考勤','工时','低级质量次数', '研发事故次数','请假','迟到早退','管理', '需求', '设计', '开发', '测试', '研究', '技术支持', '开发过程bug修复', '评审问题修复', '线上问题修复', '不符合项问题修复', '事务', '其他']
                },*/
                series: [
                    {
                    	name: '总工时：1200小时',//tooltip提示框中显示内容
                    	type: 'pie',//图形类型，如饼状图，柱状图等
                    	radius: ['35%', '65%'],//饼图的半径，数组的第一项是内半径，第二项是外半径。支持百分比。
                    	center: ["50%", "50%"], //这个属性调整图像的位置
                    	//饼图图形上的文本标签，可用于说明图形的一些数据信息，比如值，名称等。
                    	label: {
                    		formatter: '{a|{a}}{abg|}\n{hr|}\n  {b|{b}：}{c}  {per|{d}%}  ',
                    		backgroundColor: '#eee',
                    		borderColor: '#aaa',
                    		borderWidth: 1,
                    		borderRadius: 4,
                    		rich: {
                    			a: {
                    		    	color: '#999',
                    		        lineHeight: 22,
                    		        align: 'center'
                    		    },
                    		     hr: {
                    		     	borderColor: '#aaa',
                    		        width: '100%',
                    		        borderWidth: 0.5,
                    		        height: 0
                    		     },
                    		     b: {
                    		        fontSize: 16,
                    		        lineHeight: 33
                    		     },
                    		     per: {
                    		        color: '#eee',
                    		        backgroundColor: '#334455',
                    		        padding: [2, 4],
                    		        borderRadius: 2
                    		     }
                    		 }
                    	},
                    	data: [
                    		{value: 300, name: '管理'},
                    		{value: 248, name: '需求'},
                    		{value: 100, name: '设计'},
                    		{value: 400, name: '开发'},
                    		{value: 100, name: '测试'},
                    		{value: 50, name: '技术支持'},
                    		{value: 50, name: '开发过程bug修复'},
                    		{value: 50, name: '评审问题修复'},
                    		{value: 50, name: '线上问题修复'},
                    		{value: 50, name: '不符合项问题修复'}
                    	]
                    }
                ]
           };
           $scope.myChartsPayType.setOption($scope.payOption, true);
       }
       /**
        * 当窗体大小变化时，修改图例大小
        */
       window.addEventListener("resize", function () {
           if ($scope.myChartsPayType) { $scope.myChartsPayType.resize(); }
       });
       /**
		 * *************************************************************
		 *              方法声明部分                                 结束
		 * *************************************************************
		 */	
        }
    ]);
})();
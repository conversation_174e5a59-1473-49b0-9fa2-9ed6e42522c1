(function () {
    app.controller("customerStoryDetailController", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','customerStoryService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,customerStoryService,inform,Trans,AgreeConstant,LocalCache,$http) {

		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
        $scope.getData = getData;
        var id=$stateParams.item;
        initInfo();
        getData();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
		 function initInfo(){
             //产品线、产品分类与产品名称
             $scope.productLineAndType=[];
             $scope.productLineAndTypeMap={};
             comService.getParamList('PRODUCT_TYPE','').then(function (data) {
                 if (data.data) {
                     $scope.productLineAndType=data.data;
                     angular.forEach($scope.productLineAndType,function (item) {
                         $scope.productLineAndTypeMap[item["param_code"]]=item["param_value"];
                     });
                 }
             });
         }
    	/**
         * 获取需求基本信息于属性配置
         */
        function getData() {
             var urlData = {
                'storyId':id
             };
        	 customerStoryService.getCustomerStoryData(urlData).then(function (data) {
             	if (data.code === AgreeConstant.code) {
                    //需求详情
                    $scope.item = data.data.list[0];
                    getSplitWorkTimeByStoryId();
                } else {
                    inform.common(data.message);
                }
            }, function () {
                inform.common(Trans("tip.requestError"));
            });
        }
        /**
         * 获取用户需求的拆分工时
         */
         function getSplitWorkTimeByStoryId() {
         	 customerStoryService.getSplitWorkTimeByStoryId(id).then(function (data) {
              	if (data.code === AgreeConstant.code) {
                     //拆分工时
                     $scope.item.splitWorkTime = data.data;
                     $scope.item.workTime = $scope.item.splitWorkTime;
                     $scope.item.tasks = 0;
                     getStoryInfo();
                 } else {
                     inform.common(data.message);
                 }
             }, function () {
                 inform.common(Trans("tip.requestError"));
             });
         }
         /**
          * 获取产品需求信息
          */
         function getStoryInfo() {
         	 customerStoryService.getStoryInfo(id).then(function (data) {
              	if (data.code === AgreeConstant.code) {
                     //产品需求
                     $scope.storyData = data.data;
                     //产品需求总数
                     $scope.item.stories = $scope.storyData.length;
                     for (var i=0;i<$scope.storyData.length;i++){
                        var one=$scope.storyData[i];
                        //任务总数
                        $scope.item.tasks = one.taskCount*1+$scope.item.tasks*1;
                        //总工时
                        $scope.item.workTime = one.taskConsumed*1+$scope.item.workTime*1;
                     }
                 } else {
                     inform.common(data.message);
                 }
             }, function () {
                 inform.common(Trans("tip.requestError"));
             });
         }
         /**
          * 获取产品需求子需求
          */
         $scope.getSonStoryInfo = function (id){
            customerStoryService.getSonStoryInfo(id).then(function (data) {
             	if (data.code === AgreeConstant.code) {
                    //子产品需求
                    $scope.sonStoryData = data.data;
                } else {
                    inform.common(data.message);
                }
            }, function () {
                inform.common(Trans("tip.requestError"));
            });
         }
	 		/**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */

	}]);
})();
(function() {
	'use strict';
	app
	.factory('CustomerService', customerService);
	customerService.$inject=['HttpService','$rootScope'];
	function customerService(HttpService,$rootScope){
		var service={
			addCustomer:addCustomer,
            getCookie:getCookie,
            getAllCustomer:getAllCustomer,
            customerUpdate:customerUpdate,
            getCustomerByName:getCustomerByName,
            getCustomerByCond:getCustomerByCond,
            deleteCustomer:deleteCustomer
		};
		return service;
    function getCookie(name) 
            { 
                var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
             
                if(arr=document.cookie.match(reg))
             
                    return unescape(arr[2]); 
                else 
                    return null; 
            } 

     function addCustomer(customerName,orgId){
			return HttpService.post($rootScope.gateInfoApi+'lms/customer/addCustomer?customerName='+customerName+'&orgId='+orgId
				+'&name='+getCookie('name'),{});
		}

	 function getAllCustomer(){
			return HttpService.post($rootScope.gateInfoApi+'lms/customer/getAllCustomer?name='+getCookie('name'),{});
		}
	 function customerUpdate(customerId,customerName){
			return HttpService.post($rootScope.gateInfoApi+'lms/customer/customerUpdate?customerName='+customerName+'&customerId='+customerId
				+'&name='+getCookie('name'),{});
		}
     function getCustomerByName(customerName){
			return HttpService.post($rootScope.gateInfoApi+'lms/customer/getCustomerByName?customerName='+customerName+'&name='+getCookie('name'),{});
		} 
	 function getCustomerByCond(customerName,orgId){
	 	return HttpService.post($rootScope.gateInfoApi+'lms/customer/getCustomerByCond?orgId='+orgId+'&customerName='+customerName+'&name='+getCookie('name'),{});
	 }
	 function deleteCustomer(customerId){
	 	return HttpService.post($rootScope.gateInfoApi+'lms/customer/delete?&customerId='+customerId
				+'&name='+getCookie('name'),{});
	 }
	}
})();
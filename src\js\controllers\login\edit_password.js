/*
 * @Author: fubaole
 * @Date:   2017-10-19 11:34:45
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-29 14:38:26
 */
(function() {
  'use strict';

  app.controller("edit_password", ['$rootScope', '$scope', '$state', 'inform', 'LocalCache', 'LoginService','Trans','AgreeConstant','Md5',
    function($rootScope, $scope, $state, inform, LocalCache, LoginService,Trans,AgreeConstant,Md5) {
      $scope.limitList = AgreeConstant.limitList;
      $scope.oldPassword = '';
      $scope.newPassword = '';
      $scope.againPassword = '';
      $scope.editSubmit = editSubmit;

      var wrapper = document.getElementById('wrapper-login');
      // 禁止从外面复制东西黏贴到页面内
      wrapper.onkeydown = function(){
        if (event.ctrlKey && window.event.keyCode==86){
          return false;
        }
      };

      function editSubmit() {
        if ($scope.oldPassword === $scope.newPassword) {
          inform.common(Trans('login.oldSameNew'));
          return false;
        }
        if ($scope.newPassword != $scope.againPassword) {
          inform.common(Trans('login.psdNoEquals'));
          return false;
        }
        $scope.oldPassword = Md5.hex_md5($scope.oldPassword);
        $scope.newPassword = Md5.hex_md5($scope.newPassword);
        console.log($scope.oldPassword);
        console.log($scope.newPassword);
        LoginService.changePassword($scope.oldPassword, $scope.newPassword)
          .then(function(data) {
            if(data.code=== AgreeConstant.resultCode){
                LocalCache.clearDate();
                $state.go("login.index");
            }else{
                inform.common(data.message);
            }
          }, function() {
            inform.common(Trans('login.psdEditError'));
          });
      }
    }
  ]);
})();
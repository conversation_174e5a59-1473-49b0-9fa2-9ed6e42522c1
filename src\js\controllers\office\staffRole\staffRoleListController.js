
(function () {
	app.controller("staffRoleListController", ['comService', '$http', '$rootScope', '$state', '$scope', '$filter', '$modal', 'staffRoleListService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache',
		function (comService, $http, $rootScope, $state, $scope, $filter, $modal, staffRoleListService, inform, Trans, AgreeConstant, LocalCache) {
			//仓库查询条件
			$scope.formRefer = LocalCache.getObject('formRefer') || {};
			//设置列表的高度
			setDivHeight();
			//窗体大小变化时重新计算高度
			$(window).resize(setDivHeight);
			//被勾选的集合
			$scope.selecteds = [];
			//用来取消的角色信息集合
			$scope.staffRoleInfoForDelete = [];
			//查询历史集合
			$scope.historyData = [];
			//全选
			$scope.select_all = false;
			//取消日期
			$scope.itemForDeleteRole = {
				applyDate: '',
				openOnboardTime: false
			}
			//角色下拉框列表
			$scope.rolesList = [];
			//职级列表
			$scope.titleLevelList = [];
			//显示在历史表头上的文字
			$scope.employeeNameForHistory = "";
            //初始化一级部门列表
            initPrimaryDeptList();
			// 初始化分页数据
			$scope.pages = inform.initPages();
			//查询页面
			$scope.getData = getData;
			//单选方法
			$scope.selectOne = selectOne;
			//取消角色方法
			$scope.deleteRole = deleteRole;
			//保存取消角色方法
			$scope.deleteStaffRolePresent = deleteStaffRolePresent;
			//查看历史方法
			$scope.showHistory = showHistory;
			//初始化按钮权限
			$scope.addStaffRole = false;//新增角色
			$scope.saveStaffRole = false;//修改角色
			$scope.deleteStaffRole = false;//取消角色
			
			//刷新页面时就执行getData($scope.pages.pageNum)方法
			getData($scope.pages.pageNum)
			
			/**
             * 初始化根据用户名获取一级部门列表
             */
			 function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function(data) {
                    if (data.data) {
                        $scope.primaryDeptList = data.data;
                    }
                });
            }

			/**
             * 初始化二级部门列表
             */
			 function initSecDeptList() {
                //获取二级部门
                setDept();

            }

            function setDept(){
                //获取二级部门
                $scope.deptList = [];
                comService.getOrgChildren($scope.formRefer.primaryDept).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.deptList = data.data;
                    }
                });

            }

            //修改一级部门，二级部门进行联动
            $scope.changeDept = function(){
                setDept();
				//获取系研职称信息
				getTitleList($scope.formRefer.primaryDept);
            };
			//获取系研职称信息
            function getTitleList(primaryDept) {
				//获取系研职称信息
				$scope.titleList = [];
				comService.getParamList('STAFF_TITLE','NEW').then(function(data) {
				   $scope.titleList = data.data;
				});
			  }
			//判断按钮是否具有权限
			getButtonPermission();
			//初始化页面信息
			initPages();

			/**
			 * 页面初始化
			 */
			function initPages() {
				//权限控制
				getDepartmentCode();
				//初始化为删除使用的列表
				$scope.staffRoleInfoForDelete = [];
				//获取地区
				$scope.areaList = [];
				comService.getParamList('AREA_TYPE', 'AREA_TYPE').then(function (data) {
					if (data.data) {
						$scope.areaList = data.data;
					}
				});
				//获取角色下拉框
				$scope.rolesList = AgreeConstant.roleList;
				//获取角色职级下拉框
				$scope.titleLevelList = AgreeConstant.titleLevelList;
			}

			//设置列表的高度
			setDivHeight();
			//窗体大小变化时重新计算高度
			$(window).resize(setDivHeight);
			/**
             * 设置列表的高度
             */
			 function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 210);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 40);
            }

			/**
			 * 按钮权限管理
			 */
			 function getButtonPermission(){
				var buttons = {
					'Button-StaffRole-addStaffRole':'addStaffRole',//新增
					'Button-StaffRole-saveStaffRole':'saveStaffRole',//修改
					'Button-StaffRole-deleteStaffRole':'deleteStaffRole'//取消
				};
				var urlData = {
					'userId':LocalCache.getSession("userId"),
					'parentPermission':'Button-StaffRole',
					'buttons':buttons
				};
			   comService.getButtonPermission(urlData,$scope);
			}

			/**
			 * 根据分页显示仓库
			 * pageNum 当前页数
			 */
			function getData(pageNum) {
				LocalCache.setObject('formRefer', $scope.formRefer);

				if (LocalCache.getObject('formRefer')) {
					$scope.formRefer = LocalCache.getObject('formRefer'); // 初始化查询数据
				} else {
					$scope.formRefer = {
						employeeName: '',//员工姓名
						primaryDept: '',//一级部门
						department: '',//二级部门
						title: '',//职称
						level: '',//职级
						role: '',//角色
						area: ''//地区
					}
				}
				var urlData = {
					'employeeName': $scope.formRefer.employeeName,//用户姓名
					'primaryDept': $scope.formRefer.primaryDept,//一级部门
					'department': $scope.formRefer.department,//二级部门
					'title': $scope.formRefer.title,//职称
					'level': $scope.formRefer.level,//职级
					'area': $scope.formRefer.area,//地区
					'role': $scope.formRefer.role,//角色
					'currentPage': pageNum,//当前页数
					'pageSize': $scope.pages.size//每页显示条数
				};
				staffRoleListService.findStaffRoleListByPage(urlData).then(function (data) {
					if (data.code === AgreeConstant.code) {
						var jsonData = data.data;
						$scope.dataList = angular.fromJson(data.data.list);
						if ($scope.dataList.length === 0) {
							inform.common(Trans("tip.noData"));
							$scope.pages = inform.initPages();
						} else {
							//分页信息设置
							$scope.pages.total = jsonData.total;
							$scope.pages.star = jsonData.startRow;
							$scope.pages.end = jsonData.endRow;
							$scope.pages.pageNum = jsonData.pageNum;
							//初始化所有角色状态为非选中
							angular.forEach($scope.dataList, function (m) {
								m.roleCount = m.staffRoleList.length;
								angular.forEach(m.staffRoleList, function (i) {
									i.getDate = i.applyDate;
									i.checked = false;
								});
							});
						}
					}else {
							inform.common(data.message);
					}
				}, function (error) {
					inform.common(Trans("tip.requestError"));
				});
			}

			


			/**
				* 开始时间
				*/
			$scope.openDateStart = function ($event) {
				$event.preventDefault();
				$event.stopPropagation();
				$scope.openedStart = true;    //开始时间
				$scope.openedEnd = false;
			};

			/**
			 *  结束时间
			 */
			$scope.openDateEnd = function ($event) {
				$event.preventDefault();
				$event.stopPropagation();
				$scope.openedStart = false;
				$scope.openedEnd = true;    //结束时间
			};
			
			/**
			  * 下载当前数据Excel
			  */
			 $scope.excelDataPresent = function () {

				//拼装查询条件
				var urlData = {
					'employeeName': $scope.formRefer.employeeName,//用户姓名
					'primaryDept': $scope.formRefer.primaryDept,//一级部门
					'department': $scope.formRefer.department,//二级部门
					'title': $scope.formRefer.title,//职称
					'level': $scope.formRefer.level,//职级
					'area': $scope.formRefer.area,//地区
					'role': $scope.formRefer.role//角色
				};
				var modalInstance = $modal.open({
					templateUrl: 'myModalContent',
					controller: 'ModalInstanceCtrl',
					size: "sm",
					resolve: {
						items: function () {
							return "是否确定下载？";
						}
					}
				});
				modalInstance.result.then(function () {
					inform.downLoadFile('staffRole/loadDataExcelPresent', urlData, '员工当前角色表.xlsx');
					
				});
			};


			/**
			  * 下载历史数据Excel
			  */
			 $scope.excelDataHistory = function () {

				//拼装查询条件
				var urlData = {
					'employeeName': $scope.formRefer.employeeName,//用户姓名
					'primaryDept': $scope.formRefer.primaryDept,//一级部门
					'department': $scope.formRefer.department,//二级部门
					'title': $scope.formRefer.title,//职称
					'level': $scope.formRefer.level,//职级
					'area': $scope.formRefer.area,//地区
					'role': $scope.formRefer.role//角色
				};
				var modalInstance = $modal.open({
					templateUrl: 'myModalContent',
					controller: 'ModalInstanceCtrl',
					size: "sm",
					resolve: {
						items: function () {
							return "是否确定下载？";
						}
					}
				});
				modalInstance.result.then(function () {
					inform.downLoadFile('staffRole/loadDataExcelHistory', urlData, '员工历史角色表.xlsx');
					
				});
			};

			/**
			 * 单选
			 */
			 function selectOne(m,i) {
				$scope.select_all = false;
				i.checked = !i.checked;
				var index = $scope.selecteds.indexOf(i);
				if (index === -1 && i.checked) {
					$scope.selecteds.push(i);
					var staffRolesAndInfo = {
						'employeeNo':m.employeeNo,
						'title':m.title,
						'level':m.level,
						'role':i.role,
						'duty':i.duty,
						'getType':i.getType,
						'cancelReason':'',
						'applyType':'取消',
						'id':i.id
					}
					$scope.staffRoleInfoForDelete.push(staffRolesAndInfo);
				} else if (index !== -1 && !i.checked) {
					$scope.selecteds.splice(index, 1);
					$scope.staffRoleInfoForDelete.splice(index, 1);
				}
			}

			// 全选函数
			$scope.selectAll = function() {
				if ($scope.select_all) {
					$scope.selecteds = [];
					angular.forEach($scope.dataList, function (m) {
						angular.forEach(m.staffRoleList, function (i) {
							i.checked = true;
							var staffRolesAndInfo = {
								'employeeNo':m.employeeNo,
								'title':m.title,
								'level':m.level,
								'role':i.role,
								'duty':i.duty,
								'getType':i.getType,
								'cancelReason':'',
								'applyType':'取消',
								'id':i.id
							}
							$scope.selecteds.push(i);
							$scope.staffRoleInfoForDelete.push(staffRolesAndInfo);
						});
					});
				} else {
					angular.forEach($scope.dataList, function (m) {
						angular.forEach(m.staffRoleList, function (i) {
							i.checked = false;
						})
					});
					$scope.selecteds = [];
					$scope.staffRoleInfoForDelete = [];
				}
			}

			//重置查询条件
            $scope.clearParams = function() {
                if($scope.flagAuth){
                   $scope.formRefer.department = "";
                   $scope.formRefer.primaryDept = "";
                }
				$scope.formRefer.title = "";
				$scope.formRefer.level = "";
				$scope.formRefer.area = "";
				$scope.formRefer.employeeName = "";
				$scope.formRefer.role = "";
                
            };

			/**
		   *  取消角色
		   */
			 function deleteRole() {
				if ($scope.selecteds.length === 0) {
					$("#deleteRoleDialog").modal('hide');
					inform.common('请选择角色进行操作！');
					return;
				} else {
					$("#deleteRoleDialog").modal('show');
				}

			}

			/**
		   *  展示历史
		   */
			 function showHistory(m) {
				$scope.employeeNameForHistory = m.employeeName + "历史记录";
				$("#showHistoryDialog").modal('show');
				var urlData = {
					'employeeNo':m.employeeNo
				}
				staffRoleListService.selectStaffRoleHistory(urlData).then(function (data) {
					if (data.code === AgreeConstant.code) {
						$scope.historyData = angular.fromJson(data.data);
						if ($scope.historyData.length === 0) {
							inform.common(Trans("tip.noData"));
						}
					}else {
						inform.common(data.message);
					}
				}, function (error) {
					inform.common(Trans("tip.requestError"));
				});


			}

			/**
			* 取消时间
			*/
			$scope.openOnboardTimeForDeleteRole = function ($event) {
				$event.preventDefault();
				$event.stopPropagation();
				$scope.itemForDeleteRole.openOnboardTime = true;
			}

			

			/**
		   *  保存取消角色
		   */
			 function deleteStaffRolePresent() {
				angular.forEach($scope.staffRoleInfoForDelete,function(item){
					item.applyDate = $filter('date')($scope.itemForDeleteRole.applyDate,'yyyy-MM-dd');
					item.cancelReason = $scope.itemForDeleteRole.cancelReason;
				});
				$("#deleteRoleDialog").modal('hide');
				var modalInstance = $modal.open({
					templateUrl: 'myModalContent',
					controller: 'ModalInstanceCtrl',
					size: "sm",
					resolve: {
						items: function () {
							return "是否确定取消角色？";
						}
					}
				});
				var urlData = {
					'staffRoleList':$scope.staffRoleInfoForDelete
				};
				modalInstance.result.then(function () {
					staffRoleListService.deleteStaffRole(urlData).then(function (data) {
						if (data.code === AgreeConstant.code) {
							inform.common("取消角色成功");
							//刷新
							getData($scope.pages.pageNum);
							//清空已选集合
							$scope.selecteds = [];
						}else {
							inform.common(data.message);
						}
					}, function (error) {
						inform.common(Trans("tip.requestError"));
					});
				});

				

			}

			/**
				 * 跳转至修改页面
				 */
			$scope.toUpdate = function (m) {
				var mJson = JSON.stringify(m);
				$state.go("app.office.staffRoleUpdate", { 'dataDetails': mJson });
			};

			/**
			   * 跳转至新增页面
			*/
			$scope.toAdd = function () {
				$state.go("app.office.staffRoleAdd");
			};


			function getDepartmentCode(){
				//判断是否为中心办
				comService.isCenterOffice().then(function (res) {
				   if(res.code === "0000" && res.data.code === '01'){
					 //设置为可编辑
					 $scope.flagAuth = true;
					 //查询条件可以改变一二级部门
					 $scope.flagForSelect = true;
					 getData(1);
					 return;
				   }else{
					 comService.validAuthentication("0001").then(function (result) {
						  if(result.code === '0000'){
							  if(result.data.code === '00'){
								  $state.go('app.office.unAuthority');
								  return;
							  }
							  if(result.data.code === '01'){
								  //超级管理员 设置为可编辑
								  $scope.flagAuth = true;
								  //查询条件可以改变一二级部门
					 			  $scope.flagForSelect = true;
								  getData(1);
								  return;
							  }else{
								  //查询条件不可以改变一二级部门
								  $scope.flagForSelect = false;
									$scope.formRefer.primaryDept = res.data.primaryDeptCode;
									$("#primaryDeptName").attr("disabled","disabled");
									initSecDeptList();
								if(res.data.departmentCode){
									$scope.formRefer.department = res.data.departmentCode;
									$("#departmentName").attr("disabled","disabled");
								}
								getData(1);
							  }
							
		 
		 
						  }
					   });
		 
		 
				   }
				});
		 
		 }


		}]);
})();
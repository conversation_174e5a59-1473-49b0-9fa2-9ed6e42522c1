//# sourceURL=js/services/office/validCode/commitListService.js
(function () {
    'use strict';
    app.factory('commitListService', commitListService);
    commitListService.$inject = ["HttpService", '$rootScope'];

    function commitListService(HttpService, $rootScope) {
        var service = {
            getPersonalCommitList:getPersonalCommitList
        };
        return service;
        /**
         * 获取提交明细
         */
        function getPersonalCommitList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'validCodeData/getPersonalCommitList', urlData);
        }
       
    }
})();
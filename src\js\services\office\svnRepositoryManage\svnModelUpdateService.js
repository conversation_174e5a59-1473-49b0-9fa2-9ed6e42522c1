
(function() {
    'use strict';
  app.factory('svnModelUpdateService', svnModelUpdateService);
  svnModelUpdateService.$inject=["HttpService",'$rootScope'];

  function svnModelUpdateService(HttpService,$rootScope){
    var service={
		addModule:addModule,
		addModuleEmployee:addModuleEmployee,
		getModulesAndEmployees:getModulesAndEmployees,
		saveModule:saveModule
    };
    return service;

	/**
	 * 新增一条模块数据
	 */
	function addModule(urlData) {
		return HttpService.post($rootScope.getWaySystemApi+'storageManagement/addModuleData',urlData);
	}

	/**
	 * 新增模块与人员关系
	 */
	function addModuleEmployee(urlData) {
		return HttpService.post($rootScope.getWaySystemApi+'svnModule/insertModuleEmployee',urlData);
	}


	/**
     * 获取仓库下所有的模块与开发者信息
     */
    function getModulesAndEmployees(urlData) {
		return HttpService.post($rootScope.getWaySystemApi+'storageManagement/getModuleData', urlData);
	}

	/**
     * 保存修改后的模块信息
     */
    function saveModule(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi+'storageManagement/updateModule',urlData);
    }

  }
})();

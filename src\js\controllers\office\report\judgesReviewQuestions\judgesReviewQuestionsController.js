(function(){
   app.controller("judgesReviewQuestionsController",['judgesReviewQuestionsService', 'comService', '$http', 'LocalCache', '$rootScope', '$state', '$stateParams', '$scope', '$modal', 'inform', 'Trans', 'AgreeConstant',
     function(judgesReviewQuestionsService,comService, $http, LocalCache, $rootScope, $state, $stateParams, $scope, $modal, inform, Trans, AgreeConstant){
      /**
      * *************************************************************
      *             初始化部分                                   开始
      * *************************************************************
      */

       
      //设置列表的高度
      setDivHeightTab();
      //窗体大小变化时重新计算高度
	  $(window).resize(setDivHeightTab);
      $(window).resize(showDataTable1);
	  //查询列表
      $scope.getData=getData;
      $scope.type = '1';
      $scope.employeeName = LocalCache.getSession('employeeName');
	  //初始化
      initPage();
      //初始化评审会议总数排序
      $scope.orderReviewNumFlag = false;
      //初始化总问题数量排序
      $scope.orderAllNumFlag = false;

      /**
       * *************************************************************
       *              初始化部分                                  结束
       * *************************************************************
       */

      /**
       * *************************************************************
       *              方法声明部分                                 开始
       * *************************************************************
       */

        //设置列表的高度
        function setDivHeightTab(){
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (150 + 175);
            $("#divTBDisTab").height(divHeight);
            $("#subDivTBDisTab").height(divHeight - 50);
            $("#divTBDisTab2").height(divHeight);
            $("#subDivTBDisTab2").height(divHeight - 50);
            $("#divTBDisTab3").height(divHeight);
            $("#subDivTBDisTab3").height(divHeight - 50);
        }

        /**
			*调用DataTable组件冻结表头和左侧及右侧的列
			*/
            function showDataTable1() {
				$('#fixedLeftAndTop1').DataTable({
					//可被重新初始化
					retrieve: true,
					//自适应高度
					scrollY: 'calc(100vh - 370px)',
					scrollX: true,
					scrollCollapse: false,
					//控制每页显示
					paging: false,
					//冻结列（默认冻结左1）
					fixedColumns: {
						leftColumns: 3,
						rightColumns: 1
					},
					//search框显示
					searching: false,
					//排序箭头
					ordering: false,
					//底部统计数据
					info: false,
				});
			}

        //查询按钮
        function getData(){
            if($scope.type === '1') {
                getDataByProductLine();
            } else if ($scope.type === '2') {
                getDataByTitle();
            } else {
                selectDataByEmployeeName();
            }

        }


        //重置

        $scope.rest = function(){
            $scope.startTime = inform.format(new Date(),"yyyy")+'-01-01';
            $scope.endTime = '';
            $scope.employeeName = LocalCache.getSession('employeeName');
            $scope.companyTitle = '';
            $scope.isReview = '';
            $scope.reviewType = '';
            $scope.department = '';
            $scope.productLine = '';
        };


        //根据级别获取统计列表信息
        function getDataByTitle(){
            //销毁表格
			$scope.showDataTable1 = 0;
            var params = {
               startTime : inform.format($scope.startTime, 'yyyy-MM-dd'),
               endTime : inform.format($scope.endTime, 'yyyy-MM-dd'),
               isReview: $scope.isReview
            };
            judgesReviewQuestionsService.selectDateByTitle(JSON.stringify(params)).then(
               function (result) {
                    //重新生成Table
				    $scope.showDataTable1 = 1;
                   if (result.code === '0000') {
                       $scope.pageData = result.data;
                       if (null ==result.data || result.data.length === 0) {
                           $scope.pages = inform.initPages();
                       }
                      var numSum = 0;
                      var slightNumSum = 0;
                      var generalNumSum = 0;
                      var severityNumSum = 0;
                      var fatalityNumSum = 0;
                      var interrogationInterpretationNumSum = 0;
                      var allNum = 0;
                      angular.forEach($scope.pageData, function (data) {//遍历信息做汇总
                          numSum = numSum + parseInt(data.num, 10);
                          slightNumSum = slightNumSum + parseInt(data.slightNum, 10);
                          generalNumSum = generalNumSum + parseInt(data.generalNum, 10);
                          severityNumSum = severityNumSum + parseInt(data.severityNum, 10);
                          fatalityNumSum = fatalityNumSum + parseInt(data.fatalityNum, 10);
                          interrogationInterpretationNumSum = interrogationInterpretationNumSum + parseInt(data.interrogationInterpretationNum, 10);
                          allNum = allNum + parseInt(data.allNum, 10);
                      });
                      var sum = {
                          'companyTitle': '汇总',
                          'num': numSum,
                          'allNum': allNum,
                          'slightNum': slightNumSum,
                          'generalNum': generalNumSum,
                          'severityNum': severityNumSum,
                          'fatalityNum': fatalityNumSum,
                          'interrogationInterpretationNum': interrogationInterpretationNumSum
                      };
                      $scope.pageData.push(sum);

                   } else {
                         inform.common(result.message);
                   }
                   setTimeout(showDataTable1, 100);
               }, function (reason) {
                   console.log("error");
               }


            );
        }

        //根据产品线获取统计列表信息
        function getDataByProductLine(){
            var params = {
               startTime : inform.format($scope.startTime, 'yyyy-MM-dd'),
               endTime : inform.format($scope.endTime, 'yyyy-MM-dd'),
               isReview: $scope.isReview
            };
            judgesReviewQuestionsService.selectDateByProductLine(JSON.stringify(params)).then(
               function (result) {
                   if (result.code === '0000') {
                       $scope.pageDataByProductLine = result.data;
                       if (null ==result.data || result.data.length === 0) {
                           $scope.pages = inform.initPages();
                       }
                      var slightNumSum = 0;
                      var generalNumSum = 0;
                      var severityNumSum = 0;
                      var fatalityNumSum = 0;
                      var interrogationInterpretationNumSum = 0;
                      var allNum = 0;
                      angular.forEach($scope.pageDataByProductLine, function (data) {//遍历信息做汇总
                          slightNumSum = slightNumSum + parseInt(data.slightNum, 10);
                          generalNumSum = generalNumSum + parseInt(data.generalNum, 10);
                          severityNumSum = severityNumSum + parseInt(data.severityNum, 10);
                          fatalityNumSum = fatalityNumSum + parseInt(data.fatalityNum, 10);
                          interrogationInterpretationNumSum = interrogationInterpretationNumSum + parseInt(data.interrogationInterpretationNum, 10);
                          allNum = allNum + parseInt(data.allNum, 10);
                      });
                      var sum = {
                          'productLine': '汇总',
                          'allNum': allNum,
                          'slightNum': slightNumSum,
                          'generalNum': generalNumSum,
                          'severityNum': severityNumSum,
                          'fatalityNum': fatalityNumSum,
                          'interrogationInterpretationNum': interrogationInterpretationNumSum
                      };
                      $scope.pageDataByProductLine.push(sum);

                   } else {
                         inform.common(result.message);
                   }
               }, function (reason) {
                   console.log("error");
               }


            );
        }


        //根据姓名获取统计列表信息
        function selectDataByEmployeeName(){
            //默认根据问题总数倒序排列
            if (null ==$scope.orderby) {
                $scope.orderby = "ifnull(a.reviewNum,0) DESC";
                $scope.orderReviewNumFlag = false;
            }
            var params = {
               startTime : inform.format($scope.startTime, 'yyyy-MM-dd'),
               endTime : inform.format($scope.endTime, 'yyyy-MM-dd'),
               employeeName: $scope.employeeName,
               companyTitle:$scope.companyTitle,
               isReview: $scope.isReview,
               reviewType: $scope.reviewType,
               orderByParam: $scope.orderby,
               department:$scope.department
            };
            judgesReviewQuestionsService.selectDataByEmployeeName(JSON.stringify(params)).then(
               function (result) {
                   if (result.code === '0000') {
                       $scope.pageDataList = result.data;
                       if (null ==result.data || result.data.length === 0) {
                           $scope.pages = inform.initPages();
                       }


                   } else {
                         inform.common(result.message);
                   }
               }, function (reason) {
                   console.log("error");
               }


            );
        }

            /**
             * 根据评审会议总数排序
             */
            $scope.orderOfReviewNum = function(orderby) {
                //根据点击次数判别排序规则
                if (!$scope.orderReviewNumFlag) {
                    $scope.orderby = orderby + " desc";
                } else {
                    $scope.orderby = orderby + " asc";
                }
                $scope.orderReviewNumFlag = !$scope.orderReviewNumFlag;
                selectDataByEmployeeName();
            };
            /**
             * 根据评审总问题数排序
             */
            $scope.orderOfAllNum = function(orderby) {
                //根据点击次数判别排序规则
                if (!$scope.orderAllNumFlag) {
                    $scope.orderby = orderby + " desc";
                } else {
                    $scope.orderby = orderby + " asc";
                }
                $scope.orderAllNumFlag = !$scope.orderAllNumFlag;
                selectDataByEmployeeName();
            };

        //初始化页面
        function initPage() {
            //初始化查询条件 设置查询条件的默认时间
            $scope.startTime = inform.format(new Date(),"yyyy")+"-01-01";
        	$scope.endTime = "";
            if($scope.type === '1') {
                getDataByProductLine();
            } else if ($scope.type === '2') {
                getDataByTitle();
            } else {
                //根据产品线获取列表信息
                getDataByProductLine();
            }
            comService.getTitleList().then(function (data) {
                $scope.titleList = angular.fromJson(data.data);
            });
			//获取部门
			$scope.departmentList = [];
            comService.getOrgChildren('D010053').then(function(data) {
                $scope.departmentList = comService.getDepartment(data.data);
            });
            //获取产品线
            $scope.lineMap = [];
            comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                $scope.lines = angular.fromJson(data.data);
                angular.forEach($scope.lines, function (res, index) {
                    $scope.lineMap[res.param_code] = res.param_value;
                });
            });
        }


        //评审时间 -开始
        $scope.openDateStart = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = true;
            $scope.openedEnd = false;
        };
        //评审时间 -结束
        $scope.openDateEnd = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = false;
            $scope.openedEnd = true;
        };


        //下载评委评审问题统计信息表
        $scope.toExcel = function() {
            inform.modalInstance("确定要下载该统计报表吗？").result.then(function() {
              var params = {
                  startTime : inform.format($scope.startTime, 'yyyy-MM-dd'),
                  endTime : inform.format($scope.endTime, 'yyyy-MM-dd'),
                  isReview: $scope.isReview
              }
              inform.downLoadFile('JudgesReviewQuestions/downloadExcel',params,"评委评审问题统计报表.xlsx");
            });
        };
        //下载个人统计信息表
        $scope.toExcelPerson = function() {
            inform.modalInstance("确定要下载该统计报表吗？").result.then(function() {
              var params = {
                  startTime : inform.format($scope.startTime, 'yyyy-MM-dd'),
                  endTime : inform.format($scope.endTime, 'yyyy-MM-dd'),
                  employeeName: $scope.employeeName,
                  companyTitle:$scope.companyTitle,
                  isReview: $scope.isReview,
                  reviewType: $scope.reviewType,
                  department:$scope.department
              }
              inform.downLoadFile('JudgesReviewQuestions/downloadExcelPerson',params,"评委个人统计报表.xlsx");
            });
        };


       /**
       * *************************************************************
       *              方法声明部分                                 结束
       * *************************************************************
       */




     }]);
})();
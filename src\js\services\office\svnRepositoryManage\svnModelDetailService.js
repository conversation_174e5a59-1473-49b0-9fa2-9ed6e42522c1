(function() {
    'use strict';
  app.factory('svnModelDetailService', svnModelDetailService);
	svnModelDetailService.$inject=["HttpService",'$rootScope'];

  function svnModelDetailService(HttpService,$rootScope){
    var service={
        getStaffDetails:getStaffDetails,
    };
    return service;

      function getStaffDetails(urlData) {
          return HttpService.post($rootScope.getWaySystemApi+'storageManage/getStaffDetails',urlData);
      }
  }
})();

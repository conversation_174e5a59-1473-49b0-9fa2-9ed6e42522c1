(function() {
    'use strict';
  app.factory('costChartModule', costChartModule);
  costChartModule.$inject=["comService","costMonitoringService","AgreeConstant","inform","Trans"];

  function costChartModule(comService,costMonitoringService,AgreeConstant,inform,Trans){
    var service={
		 initModule:initModule
    };
    return service;

    //回调方法
    var callBackFun = null;
    //scope
    var moduleScope = null;
    //初始化传入的参数
    var initParams = [];
    /**
     * 初始化弹框
     *  参数结构
     * dataInfo= {
                    projectId:'项目id'
                    }
     */
    function initModule(dataInfo,scope) {
        scope.toCharts = toCharts;
        initParams = dataInfo;
        moduleScope = scope;

        moduleScope.projectId = dataInfo.projectId;
        toCharts();
    }

     function toCharts(){
        //获取当前项目的开始时间、结束时间、并以周作为间隔，查询人力预算，平均到每周
    	var urlData={
        	'projectId':moduleScope.projectId
        }
    	costMonitoringService.getProjectCostChartData(urlData).then(function (data) {
            if (data.code === AgreeConstant.code) {
            	moduleScope.projectCostChartData = data.data
                setChartData();
            } else {
                inform.common(data.message);
            }
    	}, function (error) {
            inform.common(Trans("tip.requestError"));
        });
     }
    function setChartData(){
        var xLength = 0;
        if(moduleScope.projectCostChartData.allWeeksActual>moduleScope.projectCostChartData.allWeeksPre){
            xLength = moduleScope.projectCostChartData.allWeeksActual;
        }else {
            xLength = moduleScope.projectCostChartData.allWeeksPre;
        }
        moduleScope.xData = [];
        for(var i=0;i<xLength*1+1;i++){
            moduleScope.xData.push('第'+i+'周');
        }
        moduleScope.legendData = [];
        var titleText='';
        if(moduleScope.projectCostChartData.budgetAddWeekHr){
            moduleScope.legendData.push('预算人力');
            titleText = '预算投入总人力：'+moduleScope.projectCostChartData.budgetAddWeekHr[moduleScope.projectCostChartData.budgetAddWeekHr.length-1]+'人天';
        }
        if(moduleScope.projectCostChartData.actualAddWeekHr){
            moduleScope.legendData.push('实际人力');
            if(titleText){
                titleText = titleText + '，实际投入总人力：'+moduleScope.projectCostChartData.actualAddWeekHr[moduleScope.projectCostChartData.actualAddWeekHr.length-1]+'人天';
            }else {
                titleText = '实际投入总人力：'+moduleScope.projectCostChartData.actualAddWeekHr[moduleScope.projectCostChartData.actualAddWeekHr.length-1]+'人天';
            }
        }
        moduleScope.myCodeCharts = moduleScope.myCodeCharts ? moduleScope.myCodeCharts : echarts.init(document.getElementById('costChart'));
        var option = {
            title: {
                text: titleText,
                left: '32%',
                top:'5%',
              //  textAlign: 'center',
                textStyle:{
                    fontWeight: '',
                    fontSize: 15
                }
            },
            tooltip: {
                trigger: 'axis',
                formatter: formatterCall//用formatter回调函数显示多项数据内容
            },
            legend: {
                data: moduleScope.legendData//['预算人力', '实际人力']
            },
            grid: {
                left: '3%',
                right: '5%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {

            },
            xAxis: {
                type: 'category',
                name:'日期',
                boundaryGap: false,
                data: moduleScope.xData,
                axisLabel:{
                    rotate:35
                }
            },
            yAxis: {
                type: 'value',
                name:'人天',
            },
            series: [
                {
                    name: '预算人力',
                    type: 'line',
                    smooth:false,//关键点，为true是不支持虚线，实线就用true
                    //线条样式
                    itemStyle:{
                        normal:{
                            lineStyle:{
                                type:'dashed'  //'dashed'网格线类型 'dotted'虚线 'solid'实线
                            }
                        }
                    },
                    color:['#66ffcc'],
                    data: moduleScope.projectCostChartData.budgetAddWeekHr
                },
                {
                    name: '实际人力',
                    type: 'line',
                    color:['blue'],
                    data:moduleScope.projectCostChartData.actualAddWeekHr
                }
            ]
        };
        moduleScope.myCodeCharts.setOption(option, true);
        //显示弹框
        $("#cost").click();
    }
	 /**
	 * 用formatter回调函数自定义显示多项数据内容
		 **/
	function formatterCall (params, ticket, callback) {
        var htmlStr = '';
        for(var i=0;i<params.length;i++){
            var param = params[i];
            var xName = param.name;//x轴的名称
            var seriesName = param.seriesName;//图例名称
            var value = param.value;//y轴值
            var color = param.color;//图例颜色
            if (!value) {
                continue;
            }

            htmlStr +='<div>';
            //为了保证和原来的效果一样，这里自己实现了一个点的效果
            htmlStr += '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:'+color+';"></span>';
            if(i===0 && seriesName ==='预算人力'){
                htmlStr += "当前参考投入总人力：" +value+ '人天<br/>';//x轴的名称
                moduleScope.currentReferenceHrPre = value;
            }
            if(i===1 || seriesName ==='实际人力'){
                htmlStr += "已投入总人力：" +value+ '人天<br/>';//x轴的名称
                var dto = moduleScope.projectCostChartData.actualAvgWeekHrMap[xName];
                htmlStr += "本周投入人力："+dto.actualWeekHr+"人天<br/>";
                htmlStr += "本周结束日期："+dto.actualWeekEndTime+"<br/>";
                if(params.length === 2){
                    htmlStr += "参考偏差：" +((moduleScope.currentReferenceHrPre*1-value*1)/moduleScope.currentReferenceHrPre*1*100).toFixed(2)+ '%<br/>';//x轴的名称
                }
            }
            htmlStr += '</div>';
        }
        return htmlStr;
    }
  }
})();
/*
 * @Author: liyunmeng
 * @Date:   2020-08-03
 */
(function() {
    'use strict';
  app.factory('personHoursService', personHoursService);
  personHoursService.$inject=["HttpService",'$rootScope'];

  function personHoursService(HttpService,$rootScope){
    var service={
        getPersonHoursList: getPersonHoursList
    };
    return service;
    /**
     * 查询
     */
    function getPersonHoursList(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'personHours/getPersonHoursList', urlData);
    }
  }
})();

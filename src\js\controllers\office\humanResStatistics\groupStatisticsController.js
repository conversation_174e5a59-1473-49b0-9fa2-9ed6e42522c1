
(function () {
    app.controller("groupStatisticsController", [ '$scope','$rootScope','$stateParams','$state','LocalCache','comService','inform','Trans','attendanceStatisticsService','AgreeConstant','$modal',
        function ($scope,$rootScope, $stateParams,$state,LocalCache,comService,inform,Trans,attendanceStatisticsService,AgreeConstant,$modal) {

    		$scope.formRefer = {};
    		$scope.formRefer.orderType = 'desc'
    		$scope.formRefer.orderField = 'workIntensityTotal';
    		$scope.formRefer.orderTypeDetail = 'desc'
    		$scope.formRefer.orderFieldDetail = 'workIntensityTotal';
    		$scope.timeSelect=['前一月','前一季度','上半年','下半年','本年度','上年度'];
            //页面分页信息
            $scope.pages = {
                pageNum : 1,   //分页页数
                size : 100,      //分页每页大小
                total : ''      //数据总数
            };
            // 初始化分页数据
            $scope.pages = inform.initPages();
            //设置列表的高度
            setTimeout(setDivHeight,500);
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.initTime = initTime;
    		$scope.resetParam = resetParam;
    		$scope.resetParamOfChart = resetParamOfChart;
    		$scope.getData = getData;
    		resetParam();
    		resetParamOfChart();
    		initData();
            getData(1);

            //设置列表的高度
            function setDivHeight(){
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 190);
                $("#divTBDisGroupData").height(divHeight);
                $("#subDivTBDisGroupData").height(divHeight - 70);

                //网页可见区域宽度
                var clientWidth = document.body.clientWidth;
                var divWidth = clientWidth - 220;
                $("#divTBDisGroupData").width(divWidth);
                $("#subDivTBDisGroupData").width(divWidth);
            }
            function initData(){
                //初始化部门
				$scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentList = comService.getDepartment(data.data);
                });
				//小组集合
				$scope.groupList = [];
                comService.getGroupInfoList().then(function (data) {
					if (data.data) {
						$scope.groupList = data.data;
					}
				});
            }
            function resetParamOfChart(){
                var date = new Date();
                var endDate = date.setMonth(date.getMonth() - 1);
                $scope.formRefer.endTimeOfChart = inform.format(endDate,"yyyy-MM");
                var startDate = date.setMonth(date.getMonth() - 11);
                $scope.formRefer.startTimeOfChart = inform.format(startDate,"yyyy-MM");
                $scope.formRefer.groupId='';
            }
            function resetParam(){
                initTime('前一月');
                $scope.formRefer.department='';
            }
            function initTime(flag){
                $scope.butFlag = flag;
                var date = new Date();
                var y = date.getFullYear();  //当前年份
                //设置为1号，防止31号时获取到当月
                date.setDate(1);
                if('前一月'===$scope.butFlag){
                    date.setMonth(date.getMonth()-1);
                    $scope.formRefer.startTime = inform.format(date,"yyyy-MM");
                    $scope.formRefer.endTime = inform.format(date,"yyyy-MM");
                }
                if('前一季度'===$scope.butFlag){
                    //当前月份
                    var m = new Date().getMonth();
                    //当前季度
                    var q = parseInt(m / 3);
                    //上一季度的开始日期
                    $scope.formRefer.startTime = inform.format(new Date(y, (q - 1) * 3, 1),"yyyy-MM");
                    //上一季度的结束日期
                    $scope.formRefer.endTime = inform.format(new Date(y, q * 3, 0),"yyyy-MM");
                }
                if('上半年'===$scope.butFlag){
                    $scope.formRefer.startTime = y+'-01';
                    $scope.formRefer.endTime = y+'-06';
                }
                if('下半年'===$scope.butFlag){
                    $scope.formRefer.startTime = y+'-07';
                    $scope.formRefer.endTime = y+'-12';
                }
                if('本年度'===$scope.butFlag){
                    $scope.formRefer.startTime = y+'-01';
                    $scope.formRefer.endTime = y+'-12';
                }
                if('上年度'===$scope.butFlag){
                    $scope.formRefer.startTime = parseInt(y) - 1+'-01';
                    $scope.formRefer.endTime = parseInt(y) - 1+'-12';
                }
                setTimeout(setButton,300);
            }
            //设置按钮颜色
            function setButton(){
                //获取所有快捷按钮
                var buttonBoxes = document.getElementsByName("buttons");
                angular.forEach(buttonBoxes, function (but) {
                    if($scope.butFlag === but.id){
                        $("#"+but.id).css("background-color", "#16a8f8");
                    }else{
                        $("#"+but.id).css("background-color", "#CDCDC1");
                    }
                });
            }

            function getData(pageNum){
                var urlData={
                    'statisDateStart':$scope.formRefer.startTime,
                    'statisDateEnd':$scope.formRefer.endTime,
                    'deptCode':$scope.formRefer.department,
                    'orderField':$scope.formRefer.orderField,
                    'orderType':$scope.formRefer.orderType,
                    'currentPage':pageNum,
                    'pageSize':$scope.pages.size
                }
                $scope.groupData=[];
                attendanceStatisticsService.getGroupInfoList(urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        $scope.groupData = data.data.list;
                        if ($scope.groupData.length===0) {
                            $scope.pages = inform.initPages(); 			//初始化分页数据
                            inform.common(Trans("tip.noData"));
                        } else {
                        // 分页信息设置
                            $scope.pages.total = data.data.total;           // 页面数据总数
                            $scope.pages.star = data.data.startRow;         // 页面起始数
                            $scope.pages.end = data.data.endRow;            // 页面结束数
                            $scope.pages.pageNum = data.data.pageNum;       //页号
                        }
                    } else {
                        inform.common(data.message);
                    }
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            //查看小组详情信息
            $scope.getGroupPersonDetails = function(m){
                 if(m){
                    $scope.currentTeamId=m.teamId;
                    $scope.statisDateStart=m.statisCycle;
                    $scope.statisDateEnd=m.statisCycle;
                 }
                 var urlData={
                     'statisDateStart':$scope.statisDateStart,
                     'statisDateEnd':$scope.statisDateEnd,
                     'teamId':$scope.currentTeamId,
                     'orderField':$scope.formRefer.orderFieldDetail,
                     'orderType':$scope.formRefer.orderTypeDetail,
                     'currentPage':1,
                     'pageSize':100
                 }
                 $scope.groupDetailData=[];
                attendanceStatisticsService.getGroupDetailInfoList(urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        $scope.groupDetailData = data.data.list;
                    } else {
                        inform.common(data.message);
                    }
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
        $scope.goOrder = function(orderField){
            $scope.formRefer.orderField = orderField;
            if($scope.formRefer.orderType === 'desc'){
                $scope.formRefer.orderType = 'asc';
            }else{
                $scope.formRefer.orderType = 'desc'
            }
            getData(1);
        }
        //明细排序
        $scope.goOrderDetail = function(orderField){
            $scope.formRefer.orderFieldDetail = orderField;
            if($scope.formRefer.orderTypeDetail === 'desc'){
                $scope.formRefer.orderTypeDetail = 'asc';
            }else{
                $scope.formRefer.orderTypeDetail = 'desc'
            }
            $scope.getGroupPersonDetails();
        }

        $scope.downloadGroupStatistics = function(){
             inform.modalInstance("确定要下载小组考勤统计信息表吗？").result.then(function() {
                var urlData={
                    'statisDateStart':$scope.formRefer.startTime,
                    'statisDateEnd':$scope.formRefer.endTime,
                    'deptCode':$scope.formRefer.department,
                    'orderField':$scope.formRefer.orderField,
                    'orderType':$scope.formRefer.orderType
                }
                 inform.downLoadFile('hr_download/for_team_attend',urlData,"小组考勤统计信息表"+inform.format(new Date(),'yyyy-MM-dd')+".xlsx");
            });
        }

        $scope.getWorkStrengthChart = function(){
            //获取 小组工作强度

            eChartForWorkStrength();
        }
            //工作强度图表显示
            function eChartForWorkStrength(){
                $scope.currentWorkStrengthChart = echarts.init(document.getElementById('workStrengthChart'));

                var xData=['2021-02','2021-03','2021-04','2021-05','2021-06','2021-07','2021-08','2021-09','2021-10','2021-11','2021-12','2022-01'];
                var workHourDelayNormalAverage=['10','15','8','10','15','8','12','6','10','9','10','9'];
                var workHourDelayTotalAverage=['20','25','28','20','25','28','22','26','20','29','20','29'];
                var workIntensityNormal=['112','109','105','112','109','105','101','106','111','108','111','108'];
                var workIntensityTotal=['118','119','114','118','119','114','109','108','113','110','113','110'];
                var option = {
                  tooltip: {
                    trigger: 'axis',
                    formatter:formatterCall,
                    axisPointer: {
                      type: 'cross',
                      crossStyle: {
                        color: '#999'
                      }
                    }
                  },
                  legend: {
                    data: ['平日延时打卡工时', '总延时打卡工时', '平日工作强度','总工作强度'],
                  },
                  xAxis: [
                    {
                      type: 'category',
                      data:xData,
                      axisPointer: {
                        type: 'shadow'
                      },
                      axisLabel:{
                        rotate:30
                      }
                    }
                  ],
                  yAxis: [
                    {
                      type: 'value',
                      name: '小时数',
                      nameTextStyle:{
                        padding:[0,0,0,-58]
                      },
                      axisLabel: {
                        formatter: '{value}'
                      }
                    },
                    {
                      type: 'value',
                      name: '工作强度',
                      min:100,
                      nameTextStyle:{
                        padding:[0,0,0,55]
                      },
                      axisLabel: {
                        formatter: '{value}%'
                      }
                    }
                  ],
                  series: [
                    {
                      name: '平日延时打卡工时',
                      type: 'bar',
                      label: {
                        show: true,
                      },
                      data: workHourDelayNormalAverage
                    },
                    {
                      name: '总延时打卡工时',
                      type: 'bar',
                      label: {
                        show: true,
                      },
                      data: workHourDelayTotalAverage

                    },
                    {
                      name: '平日工作强度',
                      type: 'line',
                      yAxisIndex: 1,
                      label:{
                          show:true,
                          formatter: '{c}%'
                      },
                      data: workIntensityNormal
                    },
                    {
                      name: '总工作强度',
                      type: 'line',
                      yAxisIndex: 1,
                      label:{
                          show:true,
                          formatter: '{c}%'
                      },
                      data: workIntensityTotal
                    }

                  ]
                };

                $scope.currentWorkStrengthChart.setOption(option, true);
            }
        //自定义鼠标悬浮样式
		 function formatterCall (params, ticket, callback) {
            var htmlStr = '';
            for(var i=0;i<params.length;i++){
                var param = params[i];
                var xName = param.name;//x轴的名称
                var seriesName = param.seriesName;//图例名称
                var value = param.value;//y轴值
                var color = param.color;//图例颜色
                if(i===0){
                    htmlStr += xName + '<br/>';//x轴的名称
                }
                htmlStr +='<div>';
                //为了保证和原来的效果一样，这里自己实现了一个点的效果
                htmlStr += '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:'+color+';"></span>';
                //圆点后面显示的文本
                htmlStr += seriesName;
                htmlStr += '<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">';
                if('平日工作强度'===seriesName || '总工作强度'===seriesName){
                    htmlStr += +value+ '%';
                }else{
                    htmlStr += +value;
                }

                htmlStr += '</span>';
                htmlStr += '</div>';
            }
            return htmlStr;
         }
        //开始时间
        $scope.openSearchOnboardTime = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openSearchOnboardTime1 = true;
            $scope.openStopOnboardTime1 = false;
        };
        //截止时间
         $scope.openStopOnboardTime = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openSearchOnboardTime1 = false;
            $scope.openStopOnboardTime1 = true;
        };

      }]);
})();
(function () {
    app.controller("attendanceController", ['attendanceService','comService', '$rootScope', '$scope', '$modal', 'inform', 'LocalCache', 'Trans', 'AgreeConstant', '$http','$state',
        function (attendanceService, comService, $rootScope, $scope, $modal, inform, LocalCache, Trans, AgreeConstant, $http,$state) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //查询条件
            $scope.formInput = {};
            //绑定文件控件改变事件
            $("#filesImg").change(submitForm);
            $("#filesImg").change(fileChangeEvent);


            setDivHeight();//设置列表的高度
            initPrimaryDeptList();//初始化根据用户名获取一级部门列表

            $(window).resize(setDivHeight);//窗体大小变化时重新计算高度
            $scope.pages = {
                pageNum: '', 		// 分页页数
                size: '', 			// 分页每页大小
                total: '' 			// 数据总数
            };
            //批量插入按钮显示控制
            $scope.flag = false;
            //设置员工地区的访问权限
            $scope.areaCodeFlag = true;
            $scope.pages = inform.initPages(); 	// 初始化分页数据
            var paramObj = {
                primaryDeptId:'#primaryDeptName',
            	primaryDeptScopeModel:'primaryDeptCode',
            	primaryDeptList:'primaryDeptList',
            	subDeptList:'departmentList',
            	subDeptScopeModel:'departmentCode'
            };
            //权限控制
            comService.checkAuthentication($scope,paramObj,departmentCallBack,LocalCache.getSession('loginName'));
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */


            $scope.selectFile = function() {
                document.getElementById("filesImg").click();
            };



            /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function(data) {
                     if (data.data) {
                         $scope.primaryDeptList = data.data;
                     }
                });

                $scope.areaList = [];
                comService.getParamList('AREA_TYPE', 'AREA_TYPE').then(function (data) {
                    $scope.areaList = data.data;
                    $scope.areaMap = {};
                    for(var j = 0; j < data.data.length; j++) {
                        $scope.areaMap[data.data[j].param_code] = data.data[j].param_value;
                    }
                    //获取日期
                    $scope.initDate();
                });
            }

            //修改一级部门，二级部门进行联动
            $scope.changeDept = function(){
                //获取二级部门
                $scope.deptList = [];
                comService.getOrgChildren($scope.primaryDeptCode).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.departmentList = data.data;
                    }
                });
            };

            /**
             * 设置列表的高度
             */
            function setDivHeight(){
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 65);
            }

            //初始化日期，当前月份-1，即统计上月的考勤记录
            $scope.initDate = function () {
                var date = new Date();
                //设置为1号，防止31号时获取到当月
                date.setDate(1);
                date.setMonth(date.getMonth()-1);
                $scope.formInput.startTime = inform.format(date,"yyyy-MM");
                $scope.formInput.endTime = inform.format(date,"yyyy-MM");
            };

            //重置
            $scope.reset = function () {
                 var areaCode = $scope.formInput.region;
                $scope.formInput = {};
                //区域权限
                if ($scope.areaCodeFlag) {
                    $scope.formInput.region = areaCode;
                }
                $scope.initDate();
                //移除文件名称
                fileChangeReset();
            };

            /**
             * 选择上传文件后事件
             */
            function fileChangeEvent(e){
                var fileName = "文件名称：" + e.currentTarget.files[0].name;
                $("#fileNameDis").text(fileName);
            }

            /**
             * 选择上传文件后事件
             */
            function fileChangeReset(){
                var fileName = "";
                $("#fileNameDis").text(fileName);
                //通过表单元素的reset方法实现选择文件的重置
                $("#uploadForm")[0].reset();
            }

            //以分页的形式获取所有数据
            $scope.getData = function (pageNum) {
                //删除已加载冻结头部和部分列的HTML模板
                $scope.dataTableShow = 0;
                $scope.itemList = [];
                if(!$scope.formInput.startTime || !$scope.formInput.endTime){
                    inform.common("请输入统计起止时间");
                    return;
                }
                var urlData = {
                    'startDate': inform.format($scope.formInput.startTime, 'yyyy-MM'),//起始时间
                    'endDate': inform.format($scope.formInput.endTime, 'yyyy-MM'),    //结束时间
                    'department': $scope.departmentCode, 			            //二级部门
                    'primaryDept': $scope.primaryDeptCode,                    //一级部门
                    'realName': $scope.formInput.realName,                          //员工姓名
                    'employeeNo': $scope.formInput.employeeNo,                      //员工工号
                    'region': $scope.areaMap[$scope.formInput.region],                              //员工地域
                    'page': pageNum, 								                // 分页页数
                    'size': $scope.pages.size    						            // 分页每页大小
                };
                attendanceService.selectAttendanceInfo(urlData).then(function(data){
                   if (data.code === AgreeConstant.code) {
                       if(!data.data){
                          $scope.avgOvertimeTotalHour = 0;
                          $scope.avgWorkOvertime = 0;
                          $scope.avgWeekendOvertime = 0;
                       }else{
                          $scope.avgOvertimeTotalHour = data.data.avgOvertimeTotalHour;
                          $scope.avgWorkOvertime = data.data.avgWorkOvertime;
                          $scope.avgWeekendOvertime =data.data.avgWeekendOvertime;

                       }

                   }else{
                         inform.common(data.message);
                   }

                });
                attendanceService.getAttendanceByMap(urlData).then(function (data) {
                        //重新加载冻结头部和部分列的HTML模板
                        $scope.dataTableShow = 1;
                        if (data.code === AgreeConstant.code) {
                            var jsonData = data.data;
                            $scope.itemList = jsonData.list;
                            if ($scope.itemList.length === 0) {
                                inform.common("无符合条件的考勤信息");
                                $scope.pages = inform.initPages(); 			        //初始化分页数据
                            } else {
                                // 分页信息设置
                                $scope.pages.total = jsonData.total;		// 页面总数
                                $scope.pages.star = jsonData.startRow;  	//页面起始数
                                $scope.pages.end = jsonData.endRow;  		//页面大小数
                                $scope.pages.pageNum = jsonData.pageNum;  	//页面页数
                            }
                            //调用DataTable组件冻结表头和左侧及右侧的列
                            setTimeout(showDataTable,300);
                        }else{
                              inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });

            };
            /**
            *调用DataTable组件冻结表头和左侧及右侧的列
            */
            function showDataTable(){
                $('#fixedLeftAndTop').DataTable( {
                    //可被重新初始化
                    retrieve:       true,
                    //自适应高度
                    scrollY:        'calc(100vh - 350px)',
                    scrollX:        true,
                    scrollCollapse: true,
                    //控制每页显示
                    paging:         false,
                    //冻结列（默认冻结左1）
                    fixedColumns:   {
                        leftColumns: 4,
                        rightColumns: 0
                    },
                    //search框显示
                    searching:      false,
                    //排序箭头
                    ordering:       false,
                    //底部统计数据
                    info:           false
                } );


            }
             /**
             *部门控件的回调处理
             **/
            function departmentCallBack(result){
                 if(result.code === '00'){
                     $state.go('app.office.unAuthority');
                     return;
                 }
                 if(result.code === '01'){
                    //01全部权限
                    $scope.flag = true;
                 }
                 if(result.code === '02'){
                    //部门权限控制
                     $scope.flag = false;
                 }
                 if (result.code === '03') {
                    //地区权限控制
                     $scope.flag = false;
                     $scope.areaCodeFlag = true;
                     $('#region').attr('disabled', true);
                     $scope.formInput.region = result.areaCode;
                 }
                $scope.getData(1);
            }


            //下载员工考勤信息的Excel模板
            $scope.toTemplateExcel = function() {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function() {
                            return "确定要下载吗！";
                        }
                    }
                });
                modalInstance.result.then(function() {
                    //开启遮罩层
                    inform.showLayer("下载中。。。。。。");
                    var params = {
                    };
                    $http.post(
                        $rootScope.getWaySystemApi+'attendance/toTemplateExcel',params,
                        {
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization':'Bearer ' + LocalCache.getSession("token")||''
                            },
                            responseType: 'arraybuffer'//防止中文乱码
                        }
                    ).success(function(data){
                        //如果是IE浏览器
                        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                            var csvData = new Blob([data], {type:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
                            window.navigator.msSaveOrOpenBlob(csvData,'员工考勤信息模板.xlsx');
                        }
                        //google或者火狐浏览器
                        else{
                            var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
                            var objectUrl = URL.createObjectURL(blob);
                            var aForExcel = $("<a download='员工考勤信息模板.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href",objectUrl);
                            $("body").append(aForExcel);
                            $(".forExcel").click();
                            aForExcel.remove();
                        }
                        // 关闭遮罩层
                        inform.closeLayer();
                        inform.common("下载成功!");
                    });
                });

            };

            //生成Excel表格
            $scope.toExcel = function () {

                var urlData = {
                    'startDate': inform.format($scope.formInput.startTime, 'yyyy-MM'),
                    'endDate': inform.format($scope.formInput.endTime, 'yyyy-MM'),
                    'department': $scope.departmentCode,  			            //部门名称
                    'realName': $scope.formInput.realName,                              //员工姓名
                    'employeeNo': $scope.formInput.employeeNo,                          //员工工号
                    'region': $scope.areaMap[$scope.formInput.region],                                   //员工地域
                    'page': 1, 								                             // 分页页数
                    'size': 1    						                                 // 分页每页大小
                };
                //先判断查询出来有无结果，如果无结果直接提示，否则执行下载
                attendanceService.getAttendanceByMap(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (data.data.list.length === 0) {
                                inform.common("查询结果无内容")
                            } else {
                                var modalInstance = $modal.open({
                                    templateUrl: 'myModalContent.html',
                                    controller: 'ModalInstanceCtrl',
                                    size: "sm",
                                    resolve: {
                                        items: function () {
                                            return "确定要下载吗！";
                                        }
                                    }
                                });
                                modalInstance.result.then(function () {
                                    //开启遮罩层
                                    inform.showLayer("下载中。。。。。。");
                                    $http.post(
                                        $rootScope.getWaySystemApi + 'attendance/toExcel',
                                        {
                                            'startDate': inform.format($scope.formInput.startTime, 'yyyy-MM'),
                                            'endDate': inform.format($scope.formInput.endTime, 'yyyy-MM'),
                                            'department': $scope.departmentCode, 			                //二级部门
                                            'primaryDept': $scope.primaryDeptCode,                        //一级部门
                                            'realName': $scope.formInput.realName,                              //员工姓名
                                            'employeeNo': $scope.formInput.employeeNo,                          //员工工号
                                            'region': $scope.areaMap[$scope.formInput.region]                   //员工地域
                                        },
                                        {
                                            headers: {
                                                'Content-Type': 'application/json',
                                                'Authorization': 'Bearer ' + LocalCache.getSession("token") || ''
                                            },
                                            responseType: 'arraybuffer'//防止中文乱码
                                        }
                                    ).success(function (data) {
                                        //如果是IE浏览器
                                        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                                            var csvData = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
                                            window.navigator.msSaveOrOpenBlob(csvData, '员工考勤统计.xlsx');
                                        }
                                        //google或者火狐浏览器
                                        else {
                                            var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
                                            var objectUrl = URL.createObjectURL(blob);
                                            var aForExcel = $("<a download='员工考勤统计.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href", objectUrl);
                                            $("body").append(aForExcel);
                                            $(".forExcel").click();
                                            aForExcel.remove();
                                        }

                                        // 关闭遮罩层
                                        inform.closeLayer();
                                        inform.common("下载成功!");
                                    });
                                });
                            }
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });

            };

            /**
             * 上传文件
             */
           function submitForm() {

                //表单id  初始化表单值
                var formData = new FormData(document.getElementById("uploadForm"));
                var file = document.querySelector('input[type=file]').files[0]; //获取文档中有类型为file的第一个input元素
                if (!file) {
                    inform.common("请先选择文件!");
                    return false;
                }
                formData.append('file', file);
                var a = file.type;
                if (a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
                    inform.common("请选择.xlsx类型的文档进行上传!");
                    return false;
                } else {
                    var modalInstance = $modal.open({
                        templateUrl: 'myModalContent.html',
                        controller: 'ModalInstanceCtrl',
                        size: "sm",
                        resolve: {
                            items: function () {
                                return "确定要上传文件吗！";
                            }
                        }
                    });
                    modalInstance.result.then(function () {
                        //开启遮罩层
                        inform.showLayer("上传中。。。。。。");
                        $.ajax({
                            url: $rootScope.getWaySystemApi + 'attendance/uploadExcel',
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            beforeSend: function (request) {
                                request.setRequestHeader("Authorization", 'Bearer ' + LocalCache.getSession("token") || '');
                            },
                            success: function (result) {
                                if (result.code === AgreeConstant.code) {
                                    // 关闭遮罩层
                                    inform.closeLayer();
                                    $modal.open({
                                        templateUrl: 'errorModel.html',
                                        controller: 'ModalInstanceCtrl',
                                        size: "lg",
                                        resolve: {
                                            items: function () {
                                                return result.message;
                                            }
                                        }
                                    });
                                    //移除文件名称
                                    fileChangeReset();
                                    $scope.getData(1);
                                } else {
                                    inform.closeLayer();
                                    inform.common("上传失败!");
                                }
                                // 关闭遮罩层
                                inform.closeLayer();

                            },
                            error: function (error) {
                                inform.common(Trans("tip.requestError"));
                            }
                        });
                    });
                }

            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法初始化调用部分                           开始
             * *************************************************************
             */



            /**
             * *************************************************************
             *              方法初始化调用部分                           结束
             * *************************************************************
             */
        }]);
})();
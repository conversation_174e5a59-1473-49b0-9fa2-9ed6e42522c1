(function () {
    'use strict';
    app.controller("earlyWarningController", ['$scope','$state','comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','quartzScheduledService','$stateParams','LocalCache', '$modal','$http',
        function ($scope,$state,comService, $rootScope, inform, Trans, AgreeConstant,quartzScheduledService,$stateParams,LocalCache, $modal,$http) {
    	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */ 
    		//获取缓存
        	$scope.formRefer = LocalCache.getObject('earlyWarning_formRefer');
        	//对原缓存进行覆盖
        	LocalCache.setObject("earlyWarning_formRefer",{});
			//任务状态map
			$scope.jobStatusMap = {
				"0":"正常",
				"1":"执行中",
				"2":"暂停"
			};
			$scope.jobDisabledSelect = [{
				value: '0',
				label: '启用'
			},{
				value: '1',
				label: '禁用'
			}];

			//任务结果map
			$scope.jobResoultMap = {
				"0":"成功",
				"1":"失败"
			};

			//任务结果下拉框
			$scope.jobResoultSelect = [{
				value: '0',
				label: '成功'
			},{
				value: '1',
				label: '失败'
			}];

			//页面分页信息
			$scope.pages = {
				pageNum : 1,   //分页页数
				size : '100',      //分页每页大小
				total : 0      //数据总数
			};

			//设置列表的高度
			setDivHeight();
			//窗体大小变化时重新计算高度
			$(window).resize(setDivHeight);

			$scope.getData = getData;
			getData('1');

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */
			/**
			  * 设置列表的高度
			  */
			function setDivHeight(){
				//网页可见区域高度
				var clientHeight = document.body.clientHeight;
				var divHeight = clientHeight - (150 + 220);
				$("#divTBDis").height(divHeight );
				$("#subDivTBDis").height(divHeight);
			}
   	 	/**
   	 	 * 修改信息时执行
   	 	 */
        $scope.popModal = function (item){
            LocalCache.setObject('earlyWarning_changeParam',item);
            LocalCache.setObject('earlyWarning_formRefer',$scope.formRefer);
            $state.go('app.office.earlyWarningUpController',null);
        };
        /**
         * 立即执行信息时执行
         */
        $scope.runModal = function (item){
            $scope.runParam = angular.copy(item);
            $scope.runJob();
        };

		/**
		* 获取任务产品线数据
		*/
		function getData(pageNum) {
			var urlData = {
				'jobName': $scope.formRefer.jobName,
				'disabled': $scope.formRefer.disabled,
				'lastResult':$scope.formRefer.lastResult,
				'page':pageNum,
				'pageSize':$scope.formRefer.pageSize,
				'type':1
			};
			quartzScheduledService.getJobInfo(urlData).then(function (data) {
				if (data.code === AgreeConstant.code) {
					$scope.jobData = data.data.list;
					// 分页信息设置
					$scope.pages.total = data.data.total; 			// 页面数据总数
					$scope.pages.star = data.data.startRow; 		// 页面起始数
					$scope.pages.end = data.data.endRow;	 		// 页面结束数
					$scope.pages.pageNum = data.data.pageNum;       //页号
				} else {
					inform.common(data.message);
				}
			},
			function (error) {
				inform.common(Trans("tip.requestError"));
			});
		}
		/**
		 * 启用任务
		 * @param item 当前行的信息
		 */
		$scope.resumeJob = function (item) {
			$scope.confirm("你确定要启用该任务吗？",function () {
				quartzScheduledService.resumeJob(item).then(function (data) {
					callBackFunction(data);
				},
				function (error) {
					inform.common(Trans("tip.requestError"));
				});
			});
		};
		/**
		* 禁用任务
		* @param item 当前行的信息
		*/
		$scope.stopJob = function(item) {
			$scope.confirm("你确定要禁用该任务吗？",function () {
				quartzScheduledService.stopJob(item).then(function (data) {
						callBackFunction(data);
					},
					function (error) {
						inform.common(Trans("tip.requestError"));
					});
			});
		};

		function callBackFunction(data){
            if (data.code === AgreeConstant.code) {
                getData('1');
            } else {
                inform.common(data.message);
            }
        }

		/**
		 * 立即执行
		 * @param item 当前行的信息
		 */
		$scope.runJob = function() {
			var jobParam = {
				'id':$scope.runParam.id,
				'jobName':$scope.runParam.jobName,
				'beanName':$scope.runParam.beanName,
				'afterTaskIds':$scope.runParam.afterTaskIds //后置任务
			};
			quartzScheduledService.runJob(jobParam).then(function (data) {
				common(data.message,function () {
					getData('1');
				});
			},
			function (error) {
				inform.common(Trans("tip.requestError"));
			});
		};

		/**
		* 前往历史信息页
		*/
		$scope.jobHistory = function (m) {
			LocalCache.setObject('earlyWarning_formRefer',$scope.formRefer);
			$state.go('app.office.earlyWarningHistory',{
				'id':m.id
			});
		};
		/**
		* 提示信息
		* @param str  提示信息
		* @param func 确定时执行的函数
		*/
		function  common(str,func){
			layer.confirm(str,{
				title:false,
				btn:['确定']
			},function(result){
				layer.close(result);
				if(typeof (func) !== 'undefined'){
					func();
				}
			});
		}

		/**
		 * 确认弹框
		 */
		$scope.confirm = function (str,func) {
			var modalInstance = $modal.open({
				templateUrl: 'myModalContent.html',
				controller: 'ModalInstanceCtrl',
				size: "sm",
				resolve: {
					items: function () {
						return Trans(str);
					}
				}
			});
			modalInstance.result.then(function () {
				func();
			});
		};

        /**
         * 修改的开始时间按钮
         */
        $scope.startTime = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.startTime1 = true;
            $scope.startTime_update1 = false;
            $scope.endTime1 = false;
            $scope.endTime_update1 = false;
        };
        /**
         * 修改的结束时间
         */
        $scope.endTime = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.startTime1 = false;
            $scope.startTime_update1 = false;
            $scope.endTime1 = true;
            $scope.endTime_update1 = false;
        };
	     /**
          * *************************************************************
          *              方法声明部分                                 结束
          * *************************************************************
          */
         } 
    ]);
})();
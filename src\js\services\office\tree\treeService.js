/*
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date:   2019-01-10 15:20:05
 * @Last Modified by:   liu<PERSON><PERSON>
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
    'use strict';
  app.factory('treeService', treeService);
  treeService.$inject=["HttpService",'$rootScope'];

  function treeService(HttpService,$rootScope){
    
    var service={
      getLog:getLog,
      getOne:getOne,
      getFirst:getFirst,
      copySvnTagFile:copySvnTagFile
    };
    return service;
   /**
    * 获取下一级目录结构
    * @param   path [文件路径]
    * @return       [下一级目录结构信息]
    * */  
    function getFirst() {
        return HttpService.get($rootScope.getWaySystemApi+'tree/getFirst',null);
    }
      /**
       * 获取下一级目录结构
       * @param   path [文件路径]
       * @return       [下一级目录结构信息]
       * */
      function getOne(path) {
          return HttpService.post($rootScope.getWaySystemApi + 'tree/getOne', path);
      }
      /**
       * 获取日志信息
       * @param   path [文件路径]
       * @return       [文件日志信息]
       */
      function getLog(path) {
        return HttpService.post($rootScope.getWaySystemApi+'tree/getLog',path);
      }
    /**
     * 获取日志信息
     * @param   path [文件路径]
     * @return       [文件日志信息]
     */
    function copySvnTagFile(path) {
      return HttpService.post($rootScope.getWaySystemApi+'tree/copySvnTagFile',path);
    }
  }
})();

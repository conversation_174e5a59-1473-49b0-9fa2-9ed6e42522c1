/*
* @Author: fubaole
* @Date:   2017-09-05 10:05:00
* @Last Modified by:   fubaole
* @Last Modified time: 2017-09-20 15:34:22
*/
(function() {
    'use strict';
  app.factory('inform', inform);

  inform.$inject=[];

  function inform(){
    var service={
      showLayer:showLayer,
      closeLayer:closeLayer,
      common:common,
      unique:unique
    };

    return service;

    // 显示遮罩层
    function showLayer(){
      return layer.load(1, {
        content:'加载中...',
        shade: [0.6,'#000'],
        success: function(layero){
          layero.find('.layui-layer-content').css({'padding-top':'70px'});
        }
      });
    }
    // 关闭遮罩层
    function closeLayer(){
      layer.closeAll();
    }
    // 加载完成提示toast
    function common(txt){
      layer.msg(txt);
    }

     // 对象去重
    function unique(obj) {
      // n为hash表，r为临时数组
      var n = {}, r = [];
      for (var i = 0; i < obj.length; i++) {
          // 如果hash表中没有当前项
          if (!n[obj[i].id]) {
              // 存入hash表
              n[obj[i].id] = true;
              // 把当前数组的当前项push到临时数组里面
              r.push(obj[i]);
          }
      }
      return r;
    }
  }
})();
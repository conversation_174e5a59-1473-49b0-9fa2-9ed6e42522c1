(function() {
  'use strict';
  angular.module('app')
    .directive('uiNav', ['$timeout', function($timeout) {
      return {
        restrict: 'AC',
        link: function(scope, el, attr) {
          var _window = $(window),
            _mb = 768,
            wrap = $('.app-aside'),
            next,
            backdrop = '.dropdown-backdrop';
          // unfolded
          el.on('click', 'a', function(e) {
            if(next){next.trigger('mouseleave.nav');}
            // next && next.trigger('mouseleave.nav');
            var _this = $(this),
              _parent = _this.parent();

            _this.parents(".one-flag").addClass('sm-active');
            _this.parents(".one-flag").siblings(".sm-active").toggleClass('sm-active');

            _parent.siblings(".active").toggleClass('active');
            if(_this.next().is('ul')){
              if( _parent.toggleClass('active')){
                e.preventDefault();
              }
            }
            //_this.next().is('ul') && _parent.toggleClass('active') && e.preventDefault();
            // mobile
            if( !_this.next().is('ul')){
              if(_window.width() < _mb){
                $('.app-aside').removeClass('show off-screen');
              }
            }
            // _this.next().is('ul') || ((_window.width() < _mb) && $('.app-aside').removeClass('show off-screen'));
            _this.parents(".two-flag").siblings(".sm-active").toggleClass('sm-active');
            if (_parent.is(".two-flag") || _parent.is(".one-flag")) {
              _parent.removeClass('sm-active');
              return false;
            } else {
              _this.parents(".two-flag").siblings(".sm-active").toggleClass('sm-active');
              _this.parents(".two-flag").addClass('sm-active');
            }
          });

          // folded & fixed
          el.on('mouseenter', 'a', function(e) {
            if(next){next.trigger('mouseleave.nav');}
            // next && next.trigger('mouseleave.nav');
            $('> .nav', wrap).remove();
            if (!$('.app-aside-fixed.app-aside-folded').length || (_window.width() < _mb) || $('.app-aside-dock').length) return;
            var _this = $(e.target),
              top,
              w_h = $(window).height(),
              offset = 50,
              min = 150;
            if(!_this.is('a')){_this = _this.closest('a');}
            // !_this.is('a') && (_this = _this.closest('a'));
            if (_this.next().is('ul')) {
              next = _this.next();
            } else {
              return;
            }

            _this.parent().addClass('active');
            top = _this.parent().position().top + offset;
            next.css('top', top);
            if (top + next.height() > w_h) {
              next.css('bottom', 0);
            }
            if (top + min > w_h) {
              next.css('bottom', w_h - top - offset).css('top', 'auto');
            }
            next.appendTo(wrap);

            next.on('mouseleave.nav', function(e) {
              $(backdrop).remove();
              next.appendTo(_this.parent());
              next.off('mouseleave.nav').css('top', 'auto').css('bottom', 'auto');
              _this.parent().removeClass('active');
            });
            if($('.smart').length){
              $('<div class="dropdown-backdrop"/>').insertAfter('.app-aside').on('click', function(next) {
                if(next){next.trigger('mouseleave.nav');}
              });
            }
            // $('.smart').length && $('<div class="dropdown-backdrop"/>').insertAfter('.app-aside').on('click', function(next) {
            //   next && next.trigger('mouseleave.nav');
            // });

          });

          wrap.on('mouseleave', function(e) {
            if(next){next.trigger('mouseleave.nav');}
            // next && next.trigger('mouseleave.nav');
            $('> .nav', wrap).remove();
          });
        }
      };
    }]);
})();
(function () {
    app.controller("budgetChangeApplyController", ['budgetChangeApplyService','budgetService','comService','$rootScope', '$scope','$state','$stateParams','$modal','inform','Trans','AgreeConstant','LocalCache','$http','OfficeFileTool',
        function (budgetChangeApplyService,budgetService,comService,$rootScope, $scope,$state,$stateParams, $modal,inform,Trans,AgreeConstant,LocalCache,$http,OfficeFileTool) {
     	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		// 正则校验配置
        $scope.limitList = AgreeConstant.limitList;
        $scope.select={};
		$scope.hrList=[];
		$scope.feeList=[];
		var num=0;
		//初始化人力预算
        $scope.initPersonBudget = '0';
    	$scope.projectName = $stateParams.projectName;
    	$scope.projectManager = $stateParams.projectManager;
    	$scope.setData=setData;
		//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		 $(window).resize(setDivHeight);
		 $scope.spec = {};
         if ($stateParams.projectId==null){
            //初始化预算状态
            $scope.budgetStatusMap={
            		'0':'草稿',
            		'1':'待结算',
            		'2':'结算申请',
            		'3':'已结算'
            };
            getData();
         } else {
            initPages();
         }

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */

	    //设置列表的高度
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var clientWidth = document.body.clientWidth;
 			var divHeight = clientHeight - (150 + 180);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 70);
 			$("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
 			$("#buttonStyle").width(350);
 		}

        //获取所有数据以分页的形式
		function getData(){
            budgetChangeApplyService.getProjectBudget().then(function(data){
				if(data.code===AgreeConstant.code){
					$scope.projectBudgetList = data.data;
					if ($scope.projectBudgetList.length===0) {
							inform.common(Trans("tip.noData"));
					} else {
						for(var i = 0;i<$scope.projectBudgetList.length;i++){
							$scope.projectBudgetList[i].personBudget = inform.removeZero($scope.projectBudgetList[i].personBudget);
							$scope.projectBudgetList[i].feeBudget = inform.formatMoney($scope.projectBudgetList[i].feeBudget);
						}
					}
				}
        	},
        	function(error) {
				inform.common(Trans("tip.requestError"));
			});
		}
        /**
		 * 跳转至预算变更页面
		 */
		$scope.toChange = function(m){
			$state.go('app.office.budgetChangeApplyDetails', {
        		projectId:m.projectId,
        		projectName:m.projectName,
        		projectManager:m.projectManager,
        		version:m.version
            });
		}
        /**
         * 初始化页面
         */
        function initPages() {
            //获取所有级别，用作下拉框
            $scope.levelSelect=[];
            budgetChangeApplyService.getAllLevel().then(function (data) {
                if (data.data) {
                    $scope.levelSelect = data.data;
                    num++;
                    setData();
                }
            });
            var urlData ={
    				'projectId':$stateParams.projectId,
    				'plmUpgradeId':'0',
    				'version':$stateParams.version,
    				'changeApplyStatus':'0'
        	};
        	$scope.changeLog={
        	        'changeReason':'',
        	        'changeInfluence':'',
        	        'certificate':""
        	};
            //查询是否有变更记录，有：changeApplyStatus=0；无：changeApplyStatus=1
            budgetChangeApplyService.getChangeLog(urlData).then(function (data) {
                if (data.data !== null) {
                    $scope.changeLog = data.data;
                    urlData.changeApplyStatus = '0';
        	    }else {
        	        urlData.changeApplyStatus = '1';
        	    }
                //获取所有岗位与级别
                $scope.titleLevelList = [];
                //级别集合-表头
                $scope.levelList=[];
                //级别集合-表值
                $scope.levelListNum=[];
                budgetChangeApplyService.getAllTitleLevelList(urlData).then(function (data) {
                    if (data.data) {
                        $scope.titleLevelList = data.data;
                        for(var i = 0;i<$scope.titleLevelList.length;i++){
                            for(var j = 0;j<$scope.titleLevelList[i].level.length;j++){
                                $scope.levelListNum.push($scope.titleLevelList[i].level[j]);
                                if(i>=4){
                                    $scope.levelList.push($scope.titleLevelList[i].level[j]);
                                }
                            }
                        }
                        num++;
                        setData();
                    }
                });
                //获取阶段
                $scope.stageList = [];
                budgetChangeApplyService.getAllStageList().then(function (data) {
                    if (data.data) {
                        $scope.stageList = data.data;
                        num++;
                        setData();
                    }
                });

                //获取项目人力预算明细
                $scope.personBudgetInfo = [];
                //查询人力投入
                budgetService.getPersonBudgetInfo(urlData).then(function(data){
                    if(data.code===AgreeConstant.code){
                        $scope.personBudgetInfo = data.data;
                    }
                    num++;
                    setData();
                   });
                //按项目阶段获取人力预算总计
                $scope.personBudgetByStage = [];
                budgetChangeApplyService.getPersonBudgetByStage(urlData).then(function(data){
                    if(data.code===AgreeConstant.code){
                        $scope.personBudgetByStage = data.data;
                    }
                    num++;
                    setData();
                });
                //获取费用预算
                $scope.feeBudgetInfo = [];
                //（项目费用预算）
                budgetService.getFeeBudgetInfo(urlData).then(function(data){
                    if(data.code===AgreeConstant.code){
                        $scope.feeBudgetInfo = data.data;
                    }
                    num++;
                    setData();
                });
                //获取差旅费用明细预算
                $scope.travelFeeBudgetInfo = [];
                budgetService.travelFeeBudgetInfo(urlData).then(function(data){
                    if(data.code===AgreeConstant.code){
                        $scope.travelFeeBudgetInfo = data.data;
                        $scope.travelFeeBudgetInfoOld = JSON.parse(JSON.stringify(data.data));
                    }
                    num++;
                    setData();
                });

                //获取费用类型
               $scope.feeList = [];
               comService.getParamList('COSTFEE_TYPE','COSTFEE_TYPE').then(function(data) {
                    if(data.data) {
                        $scope.feeList = data.data;
                    }
                    num++;
                    setData();
               });
            });
         }

        function setData () {
            if(num !== 8){
                return;
            }
            //给人力预算明细赋值
            setTimeout(setPersonDetailData,1100);
            //给人力预算汇总赋值(按阶段)
            setTimeout(setPersonBudgetByStage,1200);
			//给总计的input设置只读
			setTimeout(setReadonlyForInput,1300);
        }
        function setPersonDetailData(){
			//遍历list，取到map的key，value，给控件赋值
		    angular.forEach($scope.personBudgetInfo, function (personBudgetInfo, index) {
                var id = personBudgetInfo['codes'];
	            //1：项目经理；4：PPQA；5：架构设计师；7：产品工程师
				var codeArray = id.split("_");
				if(codeArray[1] === "1"){
				    $scope.select.managementLevel = codeArray[2];
				    document.getElementById("managementLevel").value = codeArray[2]-1;
				}else if(codeArray[1] === "4"){
				    $scope.select.ppqaLevel = codeArray[2];
				    document.getElementById("ppqaLevel").value = codeArray[2]-1;
				}else if(codeArray[1] === "5"){
				    $scope.select.frameworkLevel = codeArray[2];
				    document.getElementById("frameworkLevel").value = codeArray[2]-1;
                }else if(codeArray[1] === "7"){
                    $scope.select.productLevel = codeArray[2];
                    document.getElementById("productLevel").value = codeArray[2]-1;
                }
			    var value = personBudgetInfo['workload'];
				document.getElementById(id).value = inform.removeZero(value);
			});
		}
			//给人力预算汇总赋值(按阶段)
        function setPersonBudgetByStage(){
				//遍历list，取到map的key，value，给控件赋值
				angular.forEach($scope.personBudgetByStage, function (personBudgetByStage, index) {
                    var id = personBudgetByStage['codes'];
					var value = personBudgetByStage['workload'];
					document.getElementById(id).value = inform.removeZero(value);
				});
                //计算变更后总计(没有变更数据时，总计为之前值的合计)
                for(var j=1;j<15;j++){
                    $scope.changeToatlByStage(j,"_99_2");
                }
			}
			//给总计的input设置只读
        function setReadonlyForInput(){
            var object = document.getElementsByName("personBudgetPro");
            angular.forEach(object, function (obj, index) {
                var currentId = obj.id;
                if(currentId.indexOf("99") !== -1){
                    $("#"+currentId).attr("disabled","disabled");
                }
            });
        }
        /**
         * 修改级别→改变input的id
         */
        $scope.changeInputId = function (titleCode,levelCode) {
            var object = document.getElementsByName("personBudgetPro");
            angular.forEach(object, function (obj, index) {
                var currentId = obj.id;
                if(currentId.indexOf("_"+titleCode+"_") !== -1){
                    var codeArray = currentId.split("_");
                    var newId = codeArray[0]+"_"+codeArray[1]+"_"+levelCode;
                    document.getElementById(currentId).id=newId;
                }
            });
            //重新计算总计
            for(var j=1;j<15;j++){
                $scope.changeToatlByStage(j,"_99_2");
            }
        };
        //数值改变，重新计算变更后的总计(按阶段分组的)
        $scope.changeToatlByStage = function(stageCode,code){
            var object = document.getElementsByName("personBudgetPro");
            var sum=0;
            angular.forEach(object, function (obj, index) {
                var currentId = obj.id;
                if(currentId.indexOf(stageCode+"_") === 0 && currentId.indexOf(stageCode+"_99") === -1){
                     sum = sum*1 + document.getElementById(currentId).value*1;
                }
            });
            document.getElementById(stageCode+code).value=sum;
        }
        //计算出差费用总计-按城市
        $scope.changeTravelFeeTotalByCity = function (m) {
            m.travelFeeTotal = m.travelPresonNum*m.everyoneTrafficFee+m.travelDays*m.everydayFee;
        };
		//获取差旅费用明细的修改
        $scope.getTravelFeeInfo = function () {
            $scope.travelFeeBudgetInfo = JSON.parse(JSON.stringify($scope.travelFeeBudgetInfoOld));
        };
        //确认差旅费用明细的修改
        $scope.changeTravelFeeTotal = function () {
            var sum = 0;
            //遍历明细，计算出总计
            for(var i=0;i<$scope.travelFeeBudgetInfo.length;i++){
        		sum = sum*1+$scope.travelFeeBudgetInfo[i].travelFeeTotal*1;
        	}
            //遍历费用集合，获取到差旅并给总计赋值
            angular.forEach($scope.feeBudgetInfo, function (feeBudgetInfo, index) {
                if(feeBudgetInfo.feeType==="COSTFEE_TYPE_1"){
                    feeBudgetInfo.feeAmount = sum;
                }
            });
        	$scope.travelFeeBudgetInfoOld = JSON.parse(JSON.stringify($scope.travelFeeBudgetInfo));
            $("#travelFeeDetail_modal").modal("hide");
        };

        /**
         * 新增一个变更费用明细
         */
        $scope.addNewBindFee = function () {
            //费用信息
            var fee = {
                'feeType': '',
                'feeAmount': '0',
                'feeAmountBefore': '0',
                'feeDescription':'',
                'lineDel':true
            };
            $scope.feeBudgetInfo.push(fee);
        };
        /**
         * 取消一行费用明细
         */
         $scope.deleteNewBindFee = function (index) {
            if (index >= 0) {
                $scope.feeBudgetInfo.splice(index, 1);
            }
        }

		/**
		 * 保存
		 */
		$scope.toSave = function (){
            //校验数值
            var returnValue = judgeData();
            if(!returnValue){
                return;
            }
            var data ={
    				'projectId':$stateParams.projectId,
    				'plmUpgradeId':'0',
    				'version':$stateParams.version,
    				'budgetStatus':'1', //已确认预算，但并未申请结算等
    				'changeApplyStatus':"0"
        	};
        	//后台操作（插入新的预算变更数据，与预算表中的正式数据区分开）
            var urlData = {
        			'personBudgetInfo':$scope.personBudgetInfo,
        			'feeBudgetInfo':$scope.feeBudgetInfo,
        			'travelFeeBudgetInfo':$scope.travelFeeBudgetInfo,
        			'changeLog':$scope.changeLog,
        			'budget':data
        	};
            budgetChangeApplyService.saveBudgetChangeApply(urlData).then(function (data) {
                layer.confirm(data.message,{
                    title:false,
                    btn:['确定']
                },function(result){
                    layer.close(result);
                });
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
		}
        /**
		 * 保存并下载
		 */
		$scope.toExcelChange = function (){
            //校验数值
            var returnValue = judgeData();
            if(!returnValue){
                return;
            }
            var data ={
    				'projectId':$stateParams.projectId,
    				'projectName':$stateParams.projectName,
    				'projectManager':$scope.projectManager,
    				'plmUpgradeId':'0',
    				'version':$stateParams.version,
    				'budgetStatus':'1', //已确认预算，但并未申请结算等
    				'changeApplyStatus':"0"
        	};
        	//获取项目经理、ppqa、架构设计师、产品工程师对应的级别，用于给excel人力表头赋值
        	var excelLevels = {};
            //遍历级别集合，获取到对应的级别
            angular.forEach($scope.levelSelect, function (level, index) {
                if(level.code===$scope.select.managementLevel){
                    excelLevels["项目经理"]=level.name;
                }
                if(level.code===$scope.select.ppqaLevel){
                    excelLevels["PPQA"]=level.name;
                }
                if(level.code===$scope.select.frameworkLevel){
                    excelLevels["架构设计师"]=level.name;
                }
                if(level.code===$scope.select.productLevel){
                    excelLevels["产品"]=level.name;
                }
            });
        	//后台操作（插入新的预算变更数据，与预算表中的正式数据区分开）
            var urlData = {
        			'personBudgetInfo':$scope.personBudgetInfo,
        			'feeBudgetInfo':$scope.feeBudgetInfo,
        			'travelFeeBudgetInfo':$scope.travelFeeBudgetInfo,
        			'changeLog':$scope.changeLog,
                    'excelLevels':excelLevels,
        			'budget':data
        	};
            budgetChangeApplyService.saveBudgetChangeApply(urlData).then(function (data) {
                layer.confirm(data.message,{
                    title:false,
                    btn:['确定']
                },function(result){
                    layer.close(result);
                    $scope.confirm('是否确认下载？', function () {
                        inform.downLoadFile('budgetChangeApply/toExcelChange',urlData,'CM_'+$scope.projectName+'_成本预算变更申请单_'+ inform.format(new Date(), 'yyyy-MM-dd')+'.xlsx');
                    });
                });
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
		}

		function judgeData(){
            //检验人力预算personBudget
        	$scope.personBudgetInfo = {};
        	var boxes = document.getElementsByName("personBudgetPro");
        	for(var i=0;i<boxes.length;i++){
        		if(boxes[i].value==null || boxes[i].value==='0' || boxes[i].value===''){
	   				continue;
	   			}
	   			var id = boxes[i].id;
	   			//判断项目经理、ppqa、架构设计师、产品工程师是否有选择正确的级别
                if(id.indexOf("undefined") !== -1 || id.indexOf("_0") !== -1){
                    layer.confirm("请检查并选择对应的级别！",{
                               btn:['确定']
                           },function(result){
                              layer.close(result);
                    });
                    return false;
                }
	   			$scope.personBudgetInfo[id] = boxes[i].value;
        	}
            //检验费用预算
        	var list = [];
            for(var item=0;item<$scope.feeBudgetInfo.length;item++){
            		if($scope.feeBudgetInfo[item].feeType==null || $scope.feeBudgetInfo[item].feeAmount==null){
            			layer.confirm("请选择费用类型并填写费用金额！",{
                               btn:['确定']
                           },function(result){
                              layer.close(result);
                        });
                		return false;
            		}
            		//判断是否有重复的费用类型
            		var num = list.indexOf($scope.feeBudgetInfo[item].feeType);
	      	        if (num > -1){
	      	            layer.confirm("费用类型有重复，请修正！",{
                              btn:['确定']
                            },function(result){
                                layer.close(result);
                           });
	      	              return false;
	      	        }
	      	        list.push($scope.feeBudgetInfo[item].feeType);
			}
            //判断是否有差旅费用，如果没有，则将差旅明细的值都设置为0
            if(list.indexOf("COSTFEE_TYPE_1") < 0){
                for(var m=0;m<$scope.travelFeeBudgetInfo.length;m++){
                    $scope.travelFeeBudgetInfo[m].travelFeeTotal = 0;
                    $scope.travelFeeBudgetInfo[m].travelPresonNum = 0;
                    $scope.travelFeeBudgetInfo[m].travelDays = 0;
                }
            }
            return true;
		}
		//删除预算变更申请
		$scope.deleteChangeApply = function(m){
            var urlData ={
    				'projectId':m.projectId,
    				'plmUpgradeId':'0',
    				'version':m.version,
    				'changeApplyStatus':"0"
        	};
            $scope.confirm('是否确认删除？', function () {
                budgetChangeApplyService.deleteChangeApply(urlData).then(function (data) {
                    layer.confirm(data.message,{
                        title:false,
                        btn:['确定']
                    },function(result){
                        layer.close(result);
                        getData();
                    });
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
		    });
		}

        /**
         * 确认弹框
         */
        $scope.confirm = function (str, func) {
            var modalInstance = $modal.open({
                templateUrl: 'myModalContent.html',
                controller: 'ModalInstanceCtrl',
                size: "sm",
                resolve: {
                    items: function items() {
                        return Trans(str);
                    }
                }
            });
            modalInstance.result.then(function () {
                func();
            });
        };
	     /**
	 	  * *************************************************************
	 	  *              方法声明部分                                 结束
	 	  * *************************************************************
	 	  */

	}]);
})();
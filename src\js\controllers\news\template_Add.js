/*
 * @Author: fubaole
 * @Date:   2017-09-25 11:38:33
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-19 10:09:01
 */
(function() {
    'use strict';
    app.controller("template_Add", ['$rootScope', '$state', '$scope', '$timeout', '$stateParams', 'MessageService', 'inform', 'Trans', 'AgreeConstant',
        function($rootScope, $state, $scope, $timeout, $stateParams, MessageService, inform, Trans, AgreeConstant) {
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.getDropDownData = getDropDownData; // 获取字典数据
            getDropDownData();
            $scope.addNewsInfo = {}; // 根据Id获取消息模板信息

            // 获取字典数据
            $scope.widgetCategory = [];
            $scope.alarm_notice_method = [];
            getDropDownData("widgetCategory"); // 获取模板类型数据
            getDropDownData("alarm_notice_method"); // 获取消息类型数据
            $scope.onSubmit = onSubmit; // 保存修改消息模板信息

            // 获取字典数据
            function getDropDownData(str) {
                MessageService.getDictValueListByDictTypeCode(str)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            if (str==="widgetCategory") {
                                $scope.widgetCategory = data.result;
                                console.log($scope.widgetCategory);
                            }
                            if (str==="alarm_notice_method") {
                                $scope.alarm_notice_method = data.result;
                                console.log($scope.alarm_notice_method);
                            }
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 保存修改消息模板信息
            function onSubmit() {
                MessageService.saveOrUpdateMessageTemplate($scope.addNewsInfo)
                    .then(function(data) {
                        // console.log(data)
                        if (data.code===AgreeConstant.resultCode) {
                            inform.common(Trans("tip.saveSuccess"));
                            $state.go("app.news.news_template");
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }
        }
    ]);
})();
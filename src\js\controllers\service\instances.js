(function() {
  'use strict';
  app.controller("instances", ['$rootScope', '$scope', '$interval', '$stateParams', 'inform', 'Microservice', 'Trans',
    function($rootScope, $scope, $interval, $stateParams, inform, Microservice, Trans) {

      $scope.size = 10;
      $scope.getRoutes = getRoutes;
      $scope.getInstances = getInstances;
      $scope.changeRouteInfo = changeRouteInfo;
      $scope.timer = 0;
      $scope.servicesStats = {};
      $scope.getRoutes();
      $scope.appName = "register";
      $scope.result = [{ "appName": "register" }];

      $scope.$watch('$scope.timer', function(newValue, oldValue) {
        if (newValue !== oldValue) {
          $interval.cancel($scope.interval);
          if (newValue !== '0') {
            $scope.interval = $interval(function() {
              // console.log($scope.timer);
              $scope.getRoutes();
              $scope.getInstances();
            }, $scope.timer);
          }
        }
      });

      $scope.$on('$destroy', function() {
        $interval.cancel($scope.interval);
        //$interval.stop(interval);
      });

      $scope.getBadgeClass = function(statusState) {
        if (statusState && statusState === 'UP') {
          return 'label-success';
        } else {
          return 'label-danger';
        }
      };

      function getRoutes() {
        Microservice.getRoutes()
          .then(function(data) {
            for (var i = 0; i < data.length; i++) {
              $scope.result.push(data[i]);
            }
            console.log(data);
            $scope.getInstances();
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      function getInstances() {
        Microservice.getInstances($scope.appName)
          .then(function(data) {
            console.info(data);
            $scope.instances = data;
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      function changeRouteInfo() {
        getInstances();
      }

      $scope.$watch('$scope.instances', function(newValue) {
        $scope.servicesStats = {};
        if (newValue) {
          angular.forEach(newValue.timers, function(value, key) {
            if (key.indexOf('web.rest') !== -1 || key.indexOf('service') !== -1) {
              $scope.servicesStats[key] = value;
            }
          });
        }
      });

    }
  ]);
})();

(function() {
    'use strict';
  app.factory('staffRoleListService', staffRoleListService);
  staffRoleListService.$inject=["HttpService",'$rootScope'];

  function staffRoleListService(HttpService,$rootScope){
    var service={
      findStaffRoleListByPage:findStaffRoleListByPage,
      deleteStaffRole:deleteStaffRole,
      selectStaffRoleHistory:selectStaffRoleHistory,
      loadDataExcelPresent:loadDataExcelPresent,
      loadDataExcelHistory:loadDataExcelHistory
    };
    return service;	
    /**
	    * 获取根据条件查询员工角色岗位信息
	  */
    function findStaffRoleListByPage(urlData) {
      return HttpService.post($rootScope.getWaySystemApi+'staffRole/selectStaffRole',urlData);
    }


    /**
	    * 取消角色
	  */
     function deleteStaffRole(urlData) {
      return HttpService.post($rootScope.getWaySystemApi+'staffRole/deleteStaffRole',urlData);
    }

    /**
	    * 查询历史
	  */
     function selectStaffRoleHistory(urlData) {
      return HttpService.post($rootScope.getWaySystemApi+'staffRole/selectStaffRoleHistory',urlData);
    }

    /**
     * Excel导出（当前）
     */
     function loadDataExcelPresent(urlData) {
    	return HttpService.get($rootScope.getWaySystemApi+'staffRole/loadDataExcelPresent',urlData);
     }


     /**
     * Excel导出（历史）
     */
      function loadDataExcelHistory(urlData) {
        return HttpService.get($rootScope.getWaySystemApi+'staffRole/loadDataExcelHistory',urlData);
       }

    
	
	
  }
})();

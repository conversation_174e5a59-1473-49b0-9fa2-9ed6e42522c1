/*
 * @Author: liyunmeng
 * @Date:   2020-08-31
 */
(function () {
    app.controller("measureHisController", ['comService', '$rootScope', '$scope', 'measureHisService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, measureHisService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //获取缓存
            $scope.formRefer = LocalCache.getObject('measureHis_formRefer');
            //对原缓存进行覆盖
            LocalCache.setObject("measureHis_formRefer",{});
            //对原跳转缓存进行覆盖
            LocalCache.setObject("measureHis_formRefer",{});
            $scope.getData = getData; 			// 分页相关函数
            $scope.reset = reset;
            // 初始化数据
            $scope.measuerHisList = []
            //初始化下拉框信息
            getInitData();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            // 是否显示采集按钮
            $scope.displayShow = false;
            // 是否显示结束时间
            $scope.endTimeShow = false;
            //项目状态
            $scope.classList = [{
                value: 'project',
                label: '平台项目'
            },{
                value: 'project_pro',
                label: '禅道项目'
            }]
            //下拉框默认值
            $scope.formRefer.type = "project";
            //项目投入程度
            $scope.projectInputDegreeList = [{
                value: '0',
                label: '用例支持'
            },{
                value: '1',
                label: '全部支持'
            },{
                value: '2',
                label: '不支持'
            }];
            //在刷新页面时调用该方法
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /*
            * 获取下拉框信息
            * */
            function getInitData() {
                //获取产品线
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.projectLine = data.data;
                    }
                });
                //获取系统集成研发的下级部门信息
                $scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentList = comService.getDepartment(data.data);
                });
                //获取测试人员名单('7' 表示测试部门)
                $scope.testerEmployeesNameList = [];
                comService.getEmployeesByOrgId('7').then(function(data) {
                    $scope.testerEmployeesNameList = data.data;
                });
            }
            /**
             * 获取项目和测试人员名单
             */
            function getData(){
                //获取项目和测试人员名单
                $scope.projectDetailList=[];
                var urlData = {
                    'objectType': $scope.formRefer.type,
                    'cname': $scope.formRefer.projectName,
                    'measureName': $scope.formRefer.measureName,
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                    'selectType': $scope.formRefer.selectType
                };
                measureHisService.selectData(urlData).then(function(data) {
                    $scope.measuerHisList = angular.fromJson(data.data);
                });
            }
            /**
             * excel下载
             */
            $scope.toExcel = function() {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function() {
                            return "确定要下载吗！";
                        }
                    }
                });
                modalInstance.result.then(function() {
                    var fileNameStr = '度量元数据表';
                    if ($scope.formRefer.selectType === '1' && $scope.formRefer.startTime && $scope.formRefer.endTime) {
                        fileNameStr = fileNameStr + '(' + inform.format($scope.formRefer.startTime, 'yyyy-MM-dd') + '至' + inform.format($scope.formRefer.endTime, 'yyyy-MM-dd') + ')';
                    }
                    //拼装下载内容
                    var urlData={
                        'objectType': $scope.formRefer.type,
                        'cname': $scope.formRefer.projectName,
                        'measureName': $scope.formRefer.measureName,
                        'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                        'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                        'selectType': $scope.formRefer.selectType
                    };
                    inform.downLoadFile ('measureHis/toExcel',urlData,fileNameStr + '.xlsx');
                });
            }
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (285);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 52);
            }
            /*
            * 数据采集类型发生改变
            * */
            $scope.selectTypeChange = function () {
                if ($scope.formRefer.selectType === '1') {
                    // 显示采集按钮
                    $scope.displayShow = true;
                    // 显示采集按钮
                    $scope.endTimeShow = true;
                } else {
                    $scope.displayShow = false;
                    $scope.endTimeShow = false;
                    $scope.startTimeChange();
                }
            }
            /*
            * 采集时间发生改变
            * */
            $scope.startTimeChange = function () {
                if ($scope.formRefer.selectType !== '1' && $scope.formRefer.startTime) {
                    var endDate = new Date($scope.formRefer.startTime);
                    endDate.setDate(endDate.getDate() + 1);
                    $scope.formRefer.endTime = inform.format(new Date(endDate), 'yyyy-MM-dd');
                }
            }
            /*
           * 时间段采集度量元
           * */
            $scope.measureExecute = function () {
                if (!($scope.formRefer.startTime && $scope.formRefer.endTime)) {
                    inform.common('请填写采集时间段！');
                    return;
                }
                var urlData={
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd')//结束时间
                };
                measureHisService.measureExecute(urlData).then(function(data) {
                    inform.common(data.message);
                });
            }
            /*
            * 重置查询数据
            * */
            function reset() {
                $scope.formRefer={};
                //下拉框默认值
                $scope.formRefer.type = "project";
                $scope.displayShow = false;
                $scope.endTimeShow = false;
            }
            /**
             * 开始时间
             */
            $scope.openDateStart = function ($event) {
                $scope.openedStart = true;    //开始时间
            };
            /**
             * 开始时间
             */
            $scope.openDateEnd = function ($event) {
                $scope.openedEnd = true;    //开始时间
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();
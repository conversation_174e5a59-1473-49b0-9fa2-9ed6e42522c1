(function () {
    app.controller("repositoryActivityDetailController", ['comService','$rootScope', '$scope','$state','$stateParams','$modal','repositoryActivityDetailService','inform','$window','Trans','AgreeConstant',
        function (comService,$rootScope, $scope,$state,$stateParams, $modal,repositoryActivityDetailService,inform,$window,Trans,AgreeConstant) {
            //查询输入框
            $scope.formRefer={};
            $scope.formRefer.repositoryid = '';
            // 初始化分页数据
            $scope.pages = inform.initPages();

            function getData() {
                var urlData = {
                    repositoryName: $scope.formRefer.repositoryid,
                    repositoryType: $scope.formRefer.repositoryType
                }
                repositoryActivityDetailService.getAuthorizationDetail(urlData).then(function (data) {
                    $scope.showTable = 1;
                    if (data.code === AgreeConstant.code) {
                        var jsonData = data.data;
                        $scope.tableDataList1 = jsonData.docAuthorizationList;
                        $scope.tableDataList2 = jsonData.codeAuthorizationList;
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            $scope.backRepositoryPermission = function () {
                $state.go("app.office.repositoryPermissionMain", { type: '2' });
            }

            $scope.$watch('$viewContentLoaded', function () {
                if ($stateParams.repositoryid) {
                    $scope.formRefer.repositoryid = $stateParams.repositoryid;
                    $scope.formRefer.repositoryType = $stateParams.repositoryType;
                }
                getData();
            });
        }]);
})();

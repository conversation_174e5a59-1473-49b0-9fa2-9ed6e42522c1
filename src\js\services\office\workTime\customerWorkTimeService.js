
(function() {
    'use strict';
  app.factory('customerWorkTimeService', customerWorkTimeService);
  customerWorkTimeService.$inject=["HttpService",'$rootScope'];

  function customerWorkTimeService(HttpService,$rootScope){

    var service={
        getCustomerWorkTimeList:getCustomerWorkTimeList,
        getCustomerCompanyProWorkTime:getCustomerCompanyProWorkTime,
        getOtherCompanyPro:getOtherCompanyPro,
        getXyProjectList:getXyProjectList,
        getPersonWorkTimeDetail:getPersonWorkTimeDetail,
        getSupportWorkTimeDetail:getSupportWorkTimeDetail,
        getStoryWorkTimeDetail:getStoryWorkTimeDetail,
        getPLMWorkTimeDetail:getPLMWorkTimeDetail,
        getAllCustomerWorkTime:getAllCustomerWorkTime
    };
    return service;

    /**
     * 分页查询客户工时统计
     */
    function getCustomerWorkTimeList(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'customerWorkTimeManagement/getCustomerWorkTimeList', urlData);
    }
    /**
     * 获取指定客户下，公司立项项目工时投入情况
     */
    function getCustomerCompanyProWorkTime(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'customerWorkTimeManagement/getCustomerCompanyProWorkTime', urlData);
    }
    /**
     * 获取指定客户下，其他公司立项项目工时投入情况
     */
    function getOtherCompanyPro(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'customerWorkTimeManagement/getOtherCompanyPro', urlData);
    }
    /**
     * 获取指定客户下的平台项目
     */
    function getXyProjectList(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'customerWorkTimeManagement/getXyProjectList', urlData);
    }
    //获取个人工时情况
    function getPersonWorkTimeDetail(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'customerWorkTimeManagement/getPersonWorkTimeDetail', urlData);
    }
    //获取技术支持工时情况
    function getSupportWorkTimeDetail(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'customerWorkTimeManagement/getSupportWorkTimeDetail', urlData);
    }
    //获取用户需求工时情况
    function getStoryWorkTimeDetail(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'customerWorkTimeManagement/getStoryWorkTimeDetail', urlData);
    }
    //获取用户需求PLM工时情况
    function getPLMWorkTimeDetail(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'customerWorkTimeManagement/getPLMWorkTimeDetail', urlData);
    }
    //获取客户总工时
    function getAllCustomerWorkTime(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'customerWorkTimeManagement/getAllCustomerWorkTime', urlData);
    }
  }
})();

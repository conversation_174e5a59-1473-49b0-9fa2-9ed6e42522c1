(function() {
    'use strict';
    app.factory('historyRepositoryInfoService', historyRepositoryInfoService);
    historyRepositoryInfoService.$inject=["HttpService",'$rootScope'];

    function historyRepositoryInfoService(HttpService,$rootScope){
        var service={
            getDocAuthorizationHis:getDocAuthorizationHis,
            getCodeAuthorizationHis:getCodeAuthorizationHis,
            getNowProjectChangeInfo:getNowProjectChangeInfo,
            getHisProjectChangeInfo:getHisProjectChangeInfo
        };
        function getDocAuthorizationHis(urlData) {
            return HttpService.get($rootScope.getWaySystemApi+'storageManage/getDocAuthorizationHis',urlData);
        }
        function getCodeAuthorizationHis(urlData) {
            return HttpService.get($rootScope.getWaySystemApi+'storageManage/getCodeAuthorizationHis',urlData);
        }
        function getNowProjectChangeInfo(urlData) {
            return HttpService.get($rootScope.getWaySystemApi+'storageManage/getNowProjectChangeInfo',urlData);
        }
        function getHisProjectChangeInfo(urlData) {
            return HttpService.get($rootScope.getWaySystemApi+'storageManage/getHisProjectChangeInfo',urlData);
        }
        return service;
    }
})();

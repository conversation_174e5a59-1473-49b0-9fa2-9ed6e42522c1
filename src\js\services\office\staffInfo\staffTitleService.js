(function() {
    'use strict';
  app.factory('staffTitleService', staffTitleService);
  staffTitleService.$inject=["HttpService",'$rootScope'];

  function staffTitleService(HttpService,$rootScope){
    
    var service={
        selectTitleByParam:selectTitleByParam,
        addTitleByParam:addTitleByParam,
        updateTitleByParam:updateTitleByParam,
        deleteTitleByIds:deleteTitleByIds,
        getStaffAllName : getStaffAllName,
        getStaffId:getStaffId,
        selectTitleDetail:selectTitleDetail,
        updateTitleById:updateTitleById
    };
    return service;
  
    /************************************************************************************************/
    /**
     * 员工绩效信息
     */
    function selectTitleByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffTitle/selectTitleByParam', urlData);
    }

    function addTitleByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffTitle/insertTitleByParam', urlData);
    }

    function updateTitleByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffTitle/updateTitleByParam', urlData);
    }

    function deleteTitleByIds(urlData) {
        return HttpService.get($rootScope.getWaySystemApi + 'staffTitle/deleteTitleByParam?id='+urlData);
    }

    function getStaffAllName(){
        return HttpService.post($rootScope.getWaySystemApi + 'staffInfo/getAllStaffName');
    }

    function getStaffId(employeeName){
        return HttpService.post($rootScope.getWaySystemApi + 'staffInfo/selectStaffId',employeeName);
    }

    function selectTitleDetail(employeeId){
        return HttpService.post($rootScope.getWaySystemApi + 'staffTitle/selectPersonTitleDetail',employeeId);
    }

    function updateTitleById(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'staffTitle/updateTitleById',urlData);
    }
  }
})();
(function () {
    app.controller("customerStoryManagementController", ['plmModule','companyProjectModule','hardwareModeModule','customerModule','productTypeNameModule','$rootScope','comService', '$scope','$state','$stateParams','$modal','customerStoryService','inform','Trans','AgreeConstant','LocalCache','$http',
        function (plmModule,companyProjectModule,hardwareModeModule,customerModule,productTypeNameModule,$rootScope, comService,$scope,$state,$stateParams, $modal,customerStoryService,inform,Trans,AgreeConstant,LocalCache,$http) {

		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
        initInfo();
        $scope.getData = getData;
        //如果只选择了一个进行修改就返现，批量修改不返现
        var ids=$stateParams.item.split(',');
        var flag=0;
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */

		/**
		 * 初始化
		 */
    	function initInfo() {
    	    $scope.item={};
            //获取项目归属
            $scope.projectAttributionList = [];
            $scope.projectAttributionMap = {};
            comService.queryEffectiveParam('WEEKLY_REPORT', 'AFFILIATION').then(function (data) {
                $scope.projectAttributionList = data.data;
                angular.forEach($scope.projectAttributionList,function (item) {
                    $scope.projectAttributionMap[item["paramCode"]]=item["paramValue"];
                });
                if (ids.length===1){
                    flag++;
                    getData();
                }
            });
            //获取行业
            $scope.professionListCustomer = ['金融','物流','新零售','新兴'];
            //产品线、产品分类与产品名称
            $scope.productLineAndType=[];
            $scope.productLineAndTypeMap={};
            comService.getParamList('PRODUCT_TYPE','').then(function (data) {
                if (data.data) {
                    $scope.productLineAndType=data.data;
                    angular.forEach($scope.productLineAndType,function (item) {
                        $scope.productLineAndTypeMap[item["param_code"]]=item["param_value"];
                    });
                    if (ids.length===1){
                        flag++;
                        getData();
                    }
                }
            });
            //获取选中的用户需求名称
           var urlData = {
              'storyId':$stateParams.item
           };
           customerStoryService.getCustomerStoryName(urlData).then(function (data) {
                if (data.data) {
                    $scope.customerStoryNames = data.data;
                }
            });
    	}
    	/**
         * 获取某个用户需求
         */
        function getData() {
             if (flag!==2){
                return;
             }
             var urlData = {
                'storyId':ids[0]
             };
        	 customerStoryService.getCustomerStoryData(urlData).then(function (data) {
             	if (data.code === AgreeConstant.code) {
                    //需求详情
                    $scope.item = data.data.list[0];
                    $scope.item.product = ($scope.item.productLine?$scope.productLineAndTypeMap[$scope.item.productLine]+"、":'')
                                         +($scope.item.productType?$scope.productLineAndTypeMap[$scope.item.productType]+"、":'')
                                         +($scope.item.productSubType?$scope.productLineAndTypeMap[$scope.item.productSubType]:'');
                    $scope.item.profession = $scope.item.profession?$scope.item.profession.split(',')[0]:'';

                } else {
                    inform.common(data.message);
                }
            }, function () {
                inform.common(Trans("tip.requestError"));
            });
        }
        /**
         * PLM
         */
        $scope.initPLMModule = function (){
            var data={
                'productLine':$scope.item.productLine,
                'upgradeId':$scope.item.plmUpgradeId
            };
            plmModule.initModule(data,$scope,setPLMInfo);
        }
        /**
         * 根据所选中的PLM回填信息
         */
        function setPLMInfo(data){
            $scope.item.plmUpgradeId = data.upgradeId;
        }
        /**
         * 客户
         */
        $scope.initCustomerModule = function (){
            var data={
                'keyWord':$scope.item.customer
            };
            customerModule.initModule(data,$scope,setCustomerInfo);
        }
        /**
         * 根据所选中的客户回填信息
         */
        function setCustomerInfo(data){
            $scope.item.customer = data.name;
            $scope.item.customerId = data.id;
            $scope.item.profession = data.profession.split(',')[0]
        }
        /**
         * 公司立项项目
         */
        $scope.initCompanyProjectModule = function (){
            var data={
                'projectName':'',
                'projectStatus':''
            };
            companyProjectModule.initModule(data,$scope,setCompanyProjectInfo);
        }
        /**
         * 根据所选中的公司立项项目回填信息
         */
        function setCompanyProjectInfo(data){
            $scope.item.companyProject = data.projectName;
            $scope.item.companyProjectId = data.projectId;
            $scope.item.hardwareModeNo = data.hardwareModeNo;
            $scope.item.customer = data.customerName;
            $scope.item.customerId = data.customerId;
            $scope.item.profession = data.xqsshy.split(',')[0];
            $scope.item.projectAttribution = data.xmgs;
            $scope.item.productLine = data.productLine;
            $scope.item.productType = data.productType;
            $scope.item.productSubType = data.productSubType;
            if ($scope.productLineAndTypeMap[$scope.item.productLine]) {
             $scope.item.product = $scope.productLineAndTypeMap[$scope.item.productLine];
            }
            if ($scope.productLineAndTypeMap[$scope.item.productType]) {
             $scope.item.product = $scope.item.product + "、"
                                             +$scope.productLineAndTypeMap[$scope.item.productType];
            }

            $scope.item.product = $scope.item.product+"、"
                                 +($scope.item.productSubType?$scope.productLineAndTypeMap[$scope.item.productSubType]:'');
        }
        /**
         * 型号
         */
         $scope.initHardwareModeModule = function (){
             var data={
                 'productLine': $scope.item.productLine,
                 'productType': $scope.item.productType,
                 'productName': $scope.item.productSubType,
                 'hardwareModeNo':$scope.item.hardwareModeNo
             };
             hardwareModeModule.initModule(data,$scope,setHardwareModeInfo);
         }
         /**
          * 根据所选中的硬件型号回填信息
          */
         function setHardwareModeInfo(data){
             $scope.item.hardwareModeNo = data.name;
         }
         /**
          * 产品类别-名称
          */
          $scope.initProductTypeNameModule = function (){
              var data={
                  'productLine': $scope.item.productLine,
                  'productType': $scope.item.productType,
                  'productName': $scope.item.productSubType
              };
              productTypeNameModule.initModule(data,$scope,setProductTypeNameInfo);
          }
          /**
           * 根据所选中的产品回填信息
           */
          function setProductTypeNameInfo(data){
              $scope.item.productLine = data.productLine;
              $scope.item.productType = data.productType;
              $scope.item.productSubType = data.productName;
              $scope.item.product = $scope.productLineAndTypeMap[$scope.item.productLine]+"、"
                                  +$scope.productLineAndTypeMap[$scope.item.productType]+"、"
                                  +$scope.productLineAndTypeMap[$scope.item.productSubType];
          }
        /**
         * 提交详情信息,更新
         */
        $scope.manageRelation = function (){
            if (!$scope.item.customer) {
                inform.common("请选择客户信息。");
                return;
            }
            //公司立项
            if(!$scope.item.companyProjectId){
                inform.common("请选择公司立项项目。");
                return;
            }
            var urlData={
                'ids':ids,
                'companyProject':$scope.item.companyProjectId,
                'hardwareModeNo':$scope.item.hardwareModeNo,
                'customer':$scope.item.customerId,
                'profession':$scope.item.profession,
                'projectAttribution':$scope.item.projectAttribution,
                'productLine':$scope.item.productLine,
                'productType':$scope.item.productType,
                'productSubType':$scope.item.productSubType,
                'plmUpgradeId':$scope.item.plmUpgradeId
            }
            customerStoryService.manageRelation(urlData).then(function (result) {
                if (result.code === AgreeConstant.code) {
                        inform.common('维护用户需求信息成功！');
                        window.history.go(-1);
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
	 		/**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */

	}]);
})();

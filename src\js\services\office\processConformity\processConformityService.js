/*
 * @Author: sun<PERSON><PERSON><PERSON>
 * @Date:   2019-05-23 17:50:05
 * @Last Modified by:   sunqixian
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
    'use strict';
  app.factory('processConformityService', processConformityService);
  processConformityService.$inject=["HttpService",'$rootScope'];

  function processConformityService(HttpService,$rootScope){
    
	var service={
			getLineProcessConformity:getLineProcessConformity,
			getDeptProcessConformity:getDeptProcessConformity
			
	};
    return service;
    
    /**
     * 获取所有产品线的信息
     */
    function getLineProcessConformity(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'processConformity/getLineProcessConformity', urlData);
    }
    
    /**
     * 获取所有部门的信息
     */
    function getDeptProcessConformity(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'processConformity/getDeptProcessConformity', urlData);
    }

  }
})();

(function() {
    'use strict';
    app.factory('deptContributionService', deptContributionService);
    deptContributionService.$inject=["HttpService",'$rootScope'];

    function deptContributionService(HttpService,$rootScope){
        function getDeptContributionTotalInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptContribution/getDeptContributionTotalInfo',urlData);
        }

        function getTrainByTeam(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptContribution/getTrainByTeam',urlData);
        }

        function getTrainByStaff(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptContribution/getTrainByStaff',urlData);
        }

        function getKnowledgeByTeam(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptContribution/getKnowledgeByTeam',urlData);
        }

        function getKnowledgeByStaff(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptContribution/getKnowledgeByStaff',urlData);
        }

        function getKnowledgeTableInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptContribution/getKnowledgeTableInfo',urlData);
        }

        function getTrainingTableInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptContribution/getTrainingTableInfo',urlData);
        }

        return {
            getDeptContributionTotalInfo: getDeptContributionTotalInfo,
            getTrainByTeam: getTrainByTeam,
            getTrainByStaff: getTrainByStaff,
            getKnowledgeByTeam: getKnowledgeByTeam,
            getKnowledgeByStaff: getKnowledgeByStaff,
            getKnowledgeTableInfo: getKnowledgeTableInfo,
            getTrainingTableInfo: getTrainingTableInfo,
        };
    }
})();
/*
 * @Author: fubaole
 * @Date:   2017-09-25 15:31:02
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-02-28 09:36:05
 */

(function() {
    'use strict';
    app.controller("receive_newsDetail", ['$rootScope', '$scope', '$stateParams', 'MessageService', 'Trans', 'inform', 'AgreeConstant',
        function($rootScope, $scope, $stateParams, MessageService, Trans, inform, AgreeConstant) {

            // 排序
            $scope.title = 'id';
            $scope.order = order;

            // 根据消息id获取接收消息信息
            MessageService.getReceiveMessageForUpdate($stateParams.messageId)
                .then(function(data) {
                    if (data.code===AgreeConstant.resultCode) {
                        $scope.resultData = data.result;
                        console.log($scope.resultData);
                        if ($scope.resultData.length===0) {
                            inform.common(Trans('tip.noData'));
                        }
                    } else {
                        inform.common(data.message);
                    }
                }, function() {
                    inform.common(Trans("tip.requestError"));
                });

            // 排序
            function order(str) {
                $scope.title = str;
                $scope.desc = !$scope.desc;
            }
        }
    ]);
})();
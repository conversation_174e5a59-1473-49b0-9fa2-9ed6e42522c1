/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date:   2018-02-26 10:16:34
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-19 10:10:31
 */

(function() {
  'use strict';
  app.controller("model_Management", ['$rootScope', '$scope', 'inform', 'SystemService', 'Trans', 'AgreeConstant',
    function($rootScope, $scope, inform, SystemService, Trans, AgreeConstant) {

      var interfaceMap = {};
      $scope.limitList = AgreeConstant.limitList; // 正则校验配置
      $scope.getData = getData; // 初始化函数
      $scope.pages = inform.initPages(); // 初始化分页数据
      $scope.getData($scope.pages.pageNum); // 初始化请求数据
      $scope.map = {}; // 查询条件
      $scope.searchData = searchData; // 查询
      $scope.popModal = popModal; // 弹框修改字典类型
      $scope.popAddValue = popAddValue; // 弹框新增字典值
      $scope.onSubmitType = onSubmitType; // 保存新增修改字典类型信息
      $scope.onSubmitVal = onSubmitVal; // 保存新增字典值信息
      $scope.inputData = {
        row: [{
          name: 'dict.typeName',
          model: '',
          holder: 'dict.placeholderTypeName',
          type: 'search',
          selectList: ''
        }]
      };

      // 排序
      $scope.title = 'id';
      $scope.order = order;

      // 排序
      function order(str) {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      }

      // 查询
      function searchData(str) {
        $scope.map.typeName = str.row[0].model;
        interfaceMap = angular.copy($scope.map);
        getData(AgreeConstant.pageNum);
      }

      // 获取表格数据
      function getData(num) {
        if (!num) { inform.common(Trans('tip.pageNumTip')); return; }
        SystemService.getDictTypeByMap(JSON.stringify(interfaceMap), parseInt(num), $scope.pages.size)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.pages.goNum = null;
              var jsonData = angular.fromJson(data.result);
              $scope.Models = jsonData.list;
              if ($scope.Models.length===0) {
                inform.common(Trans("tip.noData"));
                $scope.pages = inform.initPages();
              } else {
                $scope.pages.total = jsonData.total;
                $scope.pages.star = (jsonData.pageNum - 1) * jsonData.pageSize + 1;
                $scope.pages.end = jsonData.pageNum * jsonData.pageSize;
                $scope.pages.pageNum = jsonData.pageNum;
              }
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 获取父级字典值
      function getParentValue() {
        SystemService.getDictValueByMap({})
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.parentData = data.result;
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      //  弹框修改字典类型
      function popModal(item) {
        $scope.flag = true;
        $scope.dicType = angular.copy(item);
      }

      // 弹框新增字典值
      function popAddValue(m) {
        getParentValue();
        $scope.dicVal = {
          typeCode: m.typeCode,
          valueName: "",
          valueCode: "",
          valueDesc: ""
        };
      }

      // 保存新增修改字典类型信息
      function onSubmitType() {
        if ($scope.flag) {
          $scope.isUpdate = AgreeConstant.editDicSave;
        } else {
          $scope.isUpdate = AgreeConstant.addDicSave;
        }
        SystemService.saveOrupdateDictType($scope.isUpdate, $scope.dicType)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              inform.common(Trans("tip.saveSuccess"));
              $('#add_dicType').modal('hide');
              getData(AgreeConstant.pageNum);
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 保存新增字典值信息
      function onSubmitVal() {
        if (!$scope.dicVal.parentId) {
          $scope.dicVal.parentId = AgreeConstant.dicParentId;
        }
        SystemService.saveOrupdateDictValue($scope.dicVal)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              inform.common(Trans("tip.saveSuccess"));
              $('#add_DicValue').modal('hide');
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

    }
  ]);
})();
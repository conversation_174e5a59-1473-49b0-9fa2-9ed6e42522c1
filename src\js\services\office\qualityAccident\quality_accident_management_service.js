/**
 * 商品信息管理
 */
(function () {
    'use strict';
    app.factory('qualityAccidentManagementService', qualityAccidentManagementService);
    qualityAccidentManagementService.$inject=["HttpService",'$http','$rootScope'];
    
    function qualityAccidentManagementService(HttpService,$http,$rootScope){
    	
    	var service={
    		getForPages: getForPages,
    		getForQualityPages:getForQualityPages,
    		add:add,
    		update:update,
    		deleteById:deleteById,
            selectRepPerson:selectRepPerson
    	};
    	return service;
      
    	
        // 分页查询
        function getForPages(param){
            return HttpService.post($rootScope.getWaySystemApi + 'qualityAccident/getForPages',param);
        }
        //根据查询台条件分页查询质量事故(看板)
        function getForQualityPages(param){
            return HttpService.post($rootScope.getWaySystemApi + 'qualityAccident/getForQualityPages',param);
        }
        // 新增数据
        function add(param){
            return HttpService.post($rootScope.getWaySystemApi + 'qualityAccident/add', param);
        }
        // 修改数据
        function update(param){
            return HttpService.post($rootScope.getWaySystemApi+'qualityAccident/update', param);
        }
        // 删除数据
        function deleteById(id) {
            return HttpService.get($rootScope.getWaySystemApi + 'qualityAccident/delete/'+id);
        }

        // 查询当前项目的负责人信息
        function selectRepPerson(param) {
            return HttpService.get($rootScope.getWaySystemApi + 'qualityAccident/selectRepPerson/'+ param);
        }
    }
})();
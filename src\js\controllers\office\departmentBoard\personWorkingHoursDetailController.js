(function () {
    app.controller("personWorkingHoursDetailController", ['$ocLazyLoad', '$rootScope','comService', '$scope', '$stateParams', '$state', 'workingHoursBoardFactory', 'personWorkingDetailService', 'AgreeConstant', 'inform', 'Trans',
        function ($ocLazyLoad, $rootScope,comService,$scope, $stateParams, $state, workingHoursBoardFactory, personWorkingDetailService, AgreeConstant, inform, Trans) {
            $scope.chartsList = [];
            $scope.showTableDataByTypes = true;
            $scope.getData = getData;
            function getData() {
                getPersonGatherHoursInfo();
                getWorkingByType();
                getChartData();
            }
            // 重置部分
            $scope.resetParam = resetParam;
            function resetParam(){
                $scope.formRefer={
                    loginName: $stateParams.employeeName
                };
                workingHoursBoardFactory.initTime($scope, '本年度');
            }
            
            function getPersonGatherHoursInfo() {
                var urlData = {
                    'account':$scope.formRefer.loginName,
                    'startDate':$scope.formRefer.startTime,
                    'endDate': $scope.formRefer.endTime
                }
                $scope.showGatherHoursInfo = false;
                personWorkingDetailService.getPersonGatherHoursInfo(urlData).then(function (data) {
                    if (data.code===AgreeConstant.code) {
                        if (null !== data.data) {
                            $scope.totalData = data.data;
                            $scope.showGatherHoursInfo = true;
                        } else {
                            inform.common(Trans("tip.noData"));
                            $scope.showGatherHoursInfo = true;
                        }
                    } else {
                        inform.common(data.message);
                        $scope.showGatherHoursInfo = true;
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    $scope.showGatherHoursInfo = true;
                });
            }
            
            function getWorkingByType() {
                var urlData = {
                    'account':$scope.formRefer.loginName,
                    'startDate':$scope.formRefer.startTime,
                    'endDate': $scope.formRefer.endTime //结束时间
                }
                $scope.tableDataByTypes = [];
                $scope.showTableDataByTypes = true;
                personWorkingDetailService.getPersonHourTypeHoursInfo(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                angular.forEach(data.data, function (res) {
                                    $scope.tableDataByTypes.push({
                                        value: res.hours,
                                        name: res.statisDimensionName
                                    });
                                });
                                if (data.data.length) {
                                    setTimeout(echartsForByTypes,500);
                                } else {
                                    $scope.showTableDataByTypes = false;
                                    var currentCharts = echarts.init(document.getElementById('personHourTypeChart'));
                                    workingHoursBoardFactory.chartHideClear(currentCharts);
                                }
                            } else {
                                $scope.showTableDataByTypes = false;
                                inform.common(Trans("tip.noData"));
                            }
                        } else {
                            $scope.showTableDataByTypes = false;
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            function getDateRange(year, month, getMonthEnd) {
                getMonthEnd = getMonthEnd || false;
                var myDate2 = new Date(year, month, 0);
                var backDate = year + '-' + (month < 10 ? "0" + month : month);
                if (getMonthEnd) {
                    return backDate + '-' + myDate2.getDate();
                }
                return backDate + '-' + '01';
            }
            function getChartData() {
                var urlData = {
                    'loginName':$scope.formRefer.loginName,
                    'startTime':getDateRange(parseInt($scope.formRefer.startTime.split('-')[0]), parseInt($scope.formRefer.startTime.split('-')[1])),
                    'endTime': getDateRange(parseInt($scope.formRefer.endTime.split('-')[0]), parseInt($scope.formRefer.endTime.split('-')[1]), true)
                }
                $scope.tableData = [];
                personWorkingDetailService.getPersonalWorkingHoursData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.tableData = data.data;
                                setTimeout(setCharts,500);
                            } else {
                                $scope.tableData = [];
                                inform.common(Trans("tip.noData"));
                            }
                        } else {
                            $scope.tableData = [];
                            inform.common(data.message);
                        }
                    },
                    function () {
                        $scope.tableData = [];
                        inform.common(Trans("tip.requestError"));
                    });
            }

            $scope.checkChartData = checkChartData;
            function checkChartData(data) {
                var bol = true
                for (let i = 0; i < data.length; i++) {
                    if (data[i].hours !== null) {
                        bol = false
                        break;
                    }
                }
                return bol
            }

            function setCharts(){
                angular.forEach($scope.tableData,function(item){
                    echartsForWorkingHours(item);
                });
            }

            function echartsForByTypes() {
                var titleText = '按工时类型统计';
                var currentCharts = echarts.init(document.getElementById('personHourTypeChart'));
                var option = {
                    title: {
                        text: titleText,
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: "{b} : {c}人天 ({d}%)"
                    },
                    series: [
                        {
                            type: 'pie',
                            radius: '50%',
                            data: $scope.tableDataByTypes,
                            label:{
                                normal:{
                                    show: true,
                                    formatter: "{b} : {c}人天 ({d}%)"
                                }
                            }
                        }
                    ]
                };
                currentCharts.setOption(option, true);
                $scope.chartsList.push(currentCharts);
            }

            function echartsForWorkingHours(m) {
                if (m.hours && m.hours.length) {
                    var titleText = m.projectName;
                    var currentCharts = echarts.init(document.getElementById('current_'+m.projectId));

                    var option = {
                        title: {
                            text: titleText,
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'item',
                            formatter: "{b} : {c}人天 ({d}%)"
                        },
                        series: [
                            {
                                type: 'pie',
                                radius: '50%',
                                data: m.hours,
                                label:{
                                    normal:{
                                        show: true,
                                        formatter: "{b} : {c}人天 ({d}%)"
                                    }
                                }
                            }
                        ]
                    };
                    currentCharts.setOption(option, true);
                    $scope.chartsList.push(currentCharts);
                }
            }

            window.addEventListener("resize", chartResize);
            window.removeEventListener('resize', chartResize)
            function chartResize() {
                angular.forEach($scope.chartsList,function (charts) {
                    if (charts) { charts.resize(); }
                });
            }

            $scope.loadSuccess = function (){
                $ocLazyLoad.load(
                    [
                        'library/component/echarts.min.js'
                    ]).then(function (){
                    workingHoursBoardFactory.init($scope, '人天');
                    workingHoursBoardFactory.initTime($scope, $stateParams.searchTimeString);
                    $scope.formRefer.startTime = $stateParams.startTime;
                    $scope.formRefer.endTime = $stateParams.endTime;
                    $scope.formRefer.loginName = $stateParams.employeeName;
                    $scope.getData();
                });
            }
        }]);
})();

(function () {
    app.controller("projectWeeklyManagement", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','projectWeeklyService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,projectWeeklyService,inform,Trans,AgreeConstant,LocalCache,$http) {
       	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		//分页
    	$scope.pages = inform.initPages(); // 初始化分页数据
    	
		//设置列表的高度
        setDivHeight();
        //窗体大小变化时重新计算高度
        $(window).resize(setDivHeight);
        //获取当前时间
        $scope.currentDate=inform.format(new Date(),"yyyy-MM-dd");
        //默认导出时间(前一周)
        $scope.downLoadTime = inform.format(new Date().getTime()-168*60*60*1000,"yyyy-MM-dd");
        //获取缓存
        $scope.formRefer = LocalCache.getObject('projectWeekly_formRefer');
        //对原缓存进行覆盖
        LocalCache.setObject("projectWeekly_formRefer",{});
        //登陆人产品线
        $scope.loginProductLine = LocalCache.getSession('productLine');
        
        //初始化
        initProductLines();
        //项目状态下拉框数据源
        $scope.states = [{
            value: '0',
            label: '启用'
        }, {
            value: '1',
            label: '禁用'
        }];
        
        //如果不是刻意查禁用，默认显示启用
        if ($scope.formRefer.state==null){
        	$scope.formRefer.state = '0';//默认显示启用的        	
        }

        $scope.getData = getData; 			// 分页相关函数    
        getData();
        //获取时间信息
        $scope.week = inform.getWeek($scope.downLoadTime);
        $scope.year = inform.getWeekDay($scope.downLoadTime,7,"yyyy-MM-dd").substr(0,4);
        $scope.weekTime = inform.getWeekDay($scope.downLoadTime,1,"yyyy-MM-dd") +"-"+inform.getWeekDay($scope.downLoadTime,7,"yyyy-MM-dd");
        getProductLineCode();//自定义产品线编码（仅用于下载）
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
        /**
         * 重置
         */
		$scope.rest = function() {
			$scope.formRefer.projectName = '';
			$scope.formRefer.productLine = $scope.loginProductLine;
			$scope.formRefer.state = '0';
			//默认导出时间 前一周
			$scope.downLoadTime = inform.format(new Date().getTime()-168*60*60*1000,"yyyy-MM-dd");
            $scope.week = inform.getWeek($scope.downLoadTime);
            $scope.year = inform.getWeekDay($scope.downLoadTime,7,"yyyy-MM-dd").substr(0,4);
            $scope.weekTime = inform.getWeekDay($scope.downLoadTime,1,"yyyy-MM-dd") +"-"+inform.getWeekDay($scope.downLoadTime,7,"yyyy-MM-dd");
		}
        /**
         * 设置列表的高度
         */
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (150 + 185);
            $("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight - 50);
        }
		/**
		 * 初始化
		 */
    	function initProductLines() {
    		//获取产品线
    		$scope.productLines = [];
    		comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.productLines =  data.data;
        		}
            });
            //初始化产品线条件
            $scope.formRefer.productLine = $scope.formRefer.productLine? $scope.formRefer.productLine:$scope.loginProductLine;
    	}

    	/**
         * 自定义产品线编码
         */
        function getProductLineCode() {
            var map = new Map();
            map.set("0011","01");//打印识别
            map.set("0012","02");//自助终端
            map.set("0013","03");//自动化
            map.set("0014","04");//智能柜
            map.set("0015","05");//自助售货
            map.set("0016","06");//系统集成
            return map;

        }




    	/**
         * 获取所有周报项目信息
         */
        function getData(pageNum) {
            var urlData = {
                'cname': $scope.formRefer.projectName,//项目名称
                'productLine': $scope.formRefer.productLine,//产品线
                'status': $scope.formRefer.state, //项目状态
                'page': pageNum,//当前页数
                'pageSize': $scope.pages.size//每页显示条数
            };
            projectWeeklyService.selectData(urlData).then(function (data) {
                  //项目详情
                  $scope.tableData = data.data.list;
                  if ($scope.tableData.length === 0) {
                      inform.common(Trans("tip.noData"));
                      $scope.pages = inform.initPages();
                  } else {
                      //分页信息设置
                      $scope.pages.total = data.data.total;
                      $scope.pages.star = data.data.startRow;
                      $scope.pages.end = data.data.endRow;
                      $scope.pages.pageNum = data.data.pageNum;
                  }
             },
             function () {
                 inform.common(Trans("tip.requestError"));
             });
        }


        /**
         * 判断启用还是禁用
         */
        $scope.jude = function (item) {
        	//启用
        	if (item.status==='0'){
        		return "fa fa-check-circle green";
        	}
        	//禁用
        	return "fa fa-ban";
        };
        /**
         * 修改项目状态
         */
        $scope.checkInfo = function (m) {
        	var status = (m.status==='0'?'禁用':'启用');
        	var modalInstance = $modal.open({
				templateUrl: 'myModalContent.html',
				controller: 'ModalInstanceCtrl',
				size: "sm",
				resolve: {
					items: function() {
						return "是否确认"+status;
					}
				}
			});
			modalInstance.result.then(function() {
				var urlData = {
						'projectId': m.id,
						'status': (m.status === '0') ? '1' : '0'
				};
				projectWeeklyService.updateInfo(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                    	m.status = (m.status === '0') ? '1' : '0';
                    } else {
                        inform.common("启用，禁用失败");
                    }
                },
                function (error) {
                    inform.common(Trans("tip.requestError"));
                });
			});
        };
        //获取时间改变后的一周的起止时间
        $scope.sureWeekTime = function(){
           $scope.week = inform.getWeek($scope.downLoadTime);
           $scope.year = inform.getWeekDay($scope.downLoadTime,7,"yyyy-MM-dd").substr(0,4);
           $scope.weekTime = inform.getWeekDay($scope.downLoadTime,1,"yyyy-MM-dd") +"-"+inform.getWeekDay($scope.downLoadTime,7,"yyyy-MM-dd");

        };
        /**
         * 跳转详情
         */
        $scope.details = function(m) {
        	LocalCache.setObject('projectWeekly_formRefer',$scope.formRefer);
        	//保存项目名称
        	LocalCache.setObject('projectWeekly_projectName',m.cname);
			$state.go("app.office.projectWeeklyDetail",
			{projectId: m.id,projectTypeFlag:m.projectTypeFlag});
        } 
        /**
         * 新增周报项目
         */
        $scope.add = function() {
        	LocalCache.setObject('projectWeekly_formRefer',$scope.formRefer);
			$state.go("app.office.projectWeeklyAddManagement");
        }

        //选择需要导出周报的时间
        $scope.openTime = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openTime1 = true;
        };
        //设置一周开始时间 默认周天0
        $scope.dateOptions ={
            startingDay:1
        }
        /**
		 * excel下载
		 */
		$scope.toExcel = function() {
			var modalInstance = $modal.open({
				templateUrl: 'myModalContent.html',
				controller: 'ModalInstanceCtrl',
				size: "sm",
				resolve: {
					items: function() {
						return "确定要下载吗！";
					}
				}
			});
			modalInstance.result.then(function() {
				//拼装下载内容
				var urlData={
					'cname': $scope.formRefer.projectName,//项目名称
		            'productLine': $scope.formRefer.productLine,//产品线
		            'status': $scope.formRefer.state, //项目状态
		            'week':$scope.week,
		            'year':$scope.year,
		            'weekTime':$scope.weekTime
				};
				//查询所符合条件的产品线
				projectWeeklyService.selectProductLine(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        if(data.data.length===1){
                            var code =  getProductLineCode().get($scope.formRefer.productLine);
                            inform.downLoadFile('projectExpand/downloadExcel',urlData,
                            code+data.data[0].productLineName+'项目周报'+$scope.weekTime+'--系统集成研发中心.xlsx');
                        }else{
                            inform.downLoadFile('projectExpand/downloadExcel',urlData,'项目周报信息.zip');
                        }
                    } else {
                           inform.common("查询符合条件的产品线失败");
                       }
                    },
                    function (error) {
                       inform.common(Trans("tip.requestError"));
                    });
			});
		}
	 		/**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */	
		
	}]);
})();
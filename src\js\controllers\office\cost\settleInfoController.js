(function () {
    'use strict';
    app.controller("settleInfoController", ['$scope','$state', 'comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','costMonitoringService', 'settleService','$stateParams','LocalCache', '$modal','$http', 'OfficeFileTool',
        function ($scope,$state, comService, $rootScope, inform, Trans, AgreeConstant,costMonitoringService, settleService,$stateParams,LocalCache, $modal,$http, OfficeFileTool) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            var status=['1','2'];
            var seclist=['预算','开发','超出预算'];
            $scope.finishBtn = true;
            $scope.projectName = $stateParams.projectName;
            $scope.showData = '1';
            $scope.spec = {};
            $scope.spec.attachmentAddress = [];
            $scope.spec.attachmentSize = [];
            $scope.spec.attachmentAddressID=[];
            //审请
            $scope.apply=false;
            //取消
            $scope.cancel=false;
            //确认
            $scope.finish=false;
            //导出
            $scope.toexcel=false;
            //创建文件上传组件
            var paramObj ={listId:'thelist',
                removeCall:function (id) {
                    var index = $scope.spec.attachmentAddressID.indexOf(id);
                    $scope.spec.attachmentAddress.splice(index,1);
                    $scope.spec.attachmentSize.splice(index,1);
                    $scope.spec.attachmentAddressID.splice(index,1);
                },
                getFilePathCall:function (fileId) {
                    var index = $scope.spec.attachmentAddressID.indexOf(fileId);
                    var filePath = $scope.spec.attachmentAddress[index];
                    return filePath;
                },
                getSizeOfFiles:function () {
                    var size = 0;
                    for (var i = 0; i <  $scope.spec.attachmentSize.length; i++) {
                        size = size + parseInt($scope.spec.attachmentSize[i]);
                    }
                    return size;
                },
                uploadSuccess:function (file,response) {
                    $scope.spec.attachmentAddress.push(response.data);
                    $scope.spec.attachmentAddressID.push(file.id);
                    $scope.spec.attachmentSize.push(file.size);
                }
            };
            OfficeFileTool.createUploader(paramObj,'cost');
            $scope.type;
            //项目相关
            if ($stateParams.plm === '0') {
                $scope.type = 'project';
            } else {
                $scope.type = 'plm';
            }

            $scope.status = $stateParams.status;
            //判断按钮是否具有权限
            getButtonPermission();
            //人员级别
            getRelatedLevel($stateParams.projectId, $stateParams.plm);
            // 汇总数据
            getSum();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * 获取按钮权限
             */
            function getButtonPermission(){
                var buttons = {
                    'Button-settleInfoController-apply':'apply',
                    'Button-settleInfoController-cancel':'cancel',
                    'Button-settleInfoController-finish':'finish',
                    'Button-settleInfoController-toexcel':'toexcel'
                };
                var urlData = {
                    'userId':LocalCache.getSession("userId"),
                    'parentPermission':'settleInfoController',
                    'buttons':buttons
                };
                comService.getButtonPermission(urlData,$scope);
            }
            /**
             * 获取人员级别
             */
            function getRelatedLevel(projectId, plm){
                var urlData = {
                    'plm': plm,
                    'projectId': projectId,
                    'status': status
                };
                costMonitoringService.getRelatedLevel(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        //项目表头
                        $scope.levelList = data.data;
                        $scope.secLevelList=[];
                        for (var k = 0; k <= Object.keys($scope.levelList).length; k++) {
                            for (var j=0;j<seclist.length;j++){
                                if (k === Object.keys($scope.levelList).length && j === (seclist.length - 1)) {
                                    $scope.secLevelList.push('偏差比例');
                                } else {
                                    $scope.secLevelList.push(seclist[j]);
                                }
                            }
                        }
                        //获取人员岗位
                        getRelatedTitle(projectId, plm);
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 获取人员岗位
             */
            function getRelatedTitle(projectId, plm){
                var urlData = {
                    'plm': plm,
                    'projectId': projectId,
                    'status': status
                };
                costMonitoringService.getRelatedTitle(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        //项目行
                        $scope.titleList = data.data;
                        //如果项目不存在 人力岗位 或 人力级别数据 则不需要请求人力明细
                        if ($scope.titleList.length === 0 || $scope.secLevelList.length === 0) {
                            inform.common("项目暂时不存在人力投入.");
                        } else {
                            //获取单元格（再单元格后添加调用费用）
                            getHrDetail(projectId, plm);
                        }
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 获取单元格
             */
            function getHrDetail(projectId, plm){
                var urlData = {
                    'plm': plm,
                    'projectId': projectId,
                    'status': status
                };
                costMonitoringService.getHrDetail(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        //项目行
                        var list = data.data;
                        $scope.infoList=[];
                        packageInfo(list,$scope.titleList,$scope.levelList,seclist,$scope.infoList);
                        if ($scope.infoList.length === 0) {
                            return;
                        }
                        //求和
                        $scope.infoList = sumFun($scope.infoList);
                        //人力费用
                        getAmountDetail();
                        $scope.infoList.forEach(function (item) {
                            item.level.forEach(function (ele,i) {
                                // 合计的超出预算是百分比
                                if (i !== (item.level.length - 1)) {
                                    item.level[i] = inform.removeZero(ele)
                                }
                            })
                        })
                        //项目相关需要查询费用信息
                        if ($scope.type === "project") {
                            //项目费用
                            getProjectFee(projectId, plm);
                        }

                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 获取人力费用
             */
            function getAmountDetail(){
                costMonitoringService.getAmountDetail($stateParams.projectId,$stateParams.plm).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        var infoList = data.data;
                        var itemList = [];
                        angular.forEach($scope.levelList,function (element) {
                            var isAdd = false;
                            infoList.forEach(function (item) {
                                if (item.level === element) {
                                    itemList.push(item.budget,item.develop,item.exceedBudget);
                                    isAdd = true;
                                }
                            });
                            if (!isAdd) {
                                itemList.push(0, 0, 0);
                            }
                        })
                        var obj = {'level':itemList,'title': '成本合计'}
                        sumHor(obj);
                        obj.level.forEach(function (item, i) {
                            if (i !== (obj.level.length - 1)) {
                                obj.level[i] = inform.formatMoney(item);
                            }
                        })
                        $scope.infoList.push(obj);
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /*
            * 合计数据
            * */
            function sumFun(list) {
                // 纵向求和
                var sum = {
                    title: '人力合计',
                    level: []
                };
                if (!list[0]) {
                    return;
                }
                list[0].level.forEach(function (item) {
                    return sum.level.push(0);
                });
                list.forEach(function (item) {
                    item.level.forEach(function (ele, i) {
                        if (i % 3 === 2) {
                            sum.level[i] = sum.level[i-1] - sum.level[i-2] > 0 ? sum.level[i-1] - sum.level[i-2] : 0;
                        }else {
                            sum.level[i] += ele * 1;
                        }
                    });
                });
                list.push(sum);
                // 横向求和
                list.forEach(function (item) {
                    sumHor(item);
                })
                return list;
            }
            /*
            * 横向求和
            * */
            function sumHor(item) {
                var i1 = 0,
                    i2 = 0,
                    i3 = 0;
                item.level.forEach(function (ele, i) {
                    if (i % 3 === 0) {
                        i1 += ele * 1;
                    }
                    if (i % 3 === 1) {
                        i2 += ele * 1;
                    }
                });
                //计算偏差比例
                var num = i1 === 0 ? 1 : i1;
                i3 = (((i1 - i2) / num)*100);
                i3 = i3.toFixed(0) + '%';

                item.level.push(i1, i2, i3);
            }
            /**
             * 组装数据
             */
            function packageInfo (list,titles,levels,secs,finList){
                //岗位
                angular.forEach(titles, function (title, i) {
                    //一个岗位一条记录
                    var info = {
                        title: title,
                        level: []
                    };
                    //级别
                    angular.forEach(levels, function (level, ji) {
                        //状态
                        angular.forEach(secs, function (sec, zi) {
                            //默认无匹配
                            var flag=false;
                            for(var j=0;j<list.length;j++){
                                var one = list[j];
                                if(one.title === title){
                                    if (one.level === level){
                                        if(one.type===sec){
                                            info.level.push(inform.removeZero(one.workload));
                                            //如果有匹配的
                                            flag=true;
                                            continue;
                                        }
                                    }
                                }
                            }
                            if (flag===false){
                                info.level.push("0.0");
                            }
                        });
                    });
                    finList.push(info);
                });
            }
            /**
             * 获取项目费用
             */
            function getProjectFee(projectId, plm){
                var urlData = {
                    'plm': plm,
                    'projectId': projectId,
                    'status': status
                };
                costMonitoringService.getProjectFee(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.feeList = data.data;
                        $scope.feeList.forEach(function (item) {
                            item.feeCost = inform.formatMoney(item.feeCost);
                            item.feePre = inform.formatMoney(item.feePre);
                            item.feeDiff = inform.formatMoney(item.feeDiff);
                        })
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * 项目申请结算
             */
            $scope.applySettle = function () {
                var urlData = {
                    'projectId': $stateParams.projectId,
                    'plm': $stateParams.plm
                };
                settleService.applySettle(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        //项目点击结算申请成功后状态需要进行转换
                        $scope.status = '2';
                    }
                    inform.common(data.message);
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             *
             * 导出项目结算表
             */
            $scope.toExcel = function () {
                var urlData = {
                    'plm': $stateParams.plm,
                    'projectId': $stateParams.projectId,
                    'status': status,
                    'projectName': $scope.projectName
                };

                inform.downLoadFile('costSettle/exportSettleInfo',urlData,'结算申请表.xlsx');

            }

            /**
             * 项目取消结算申请
             */
            $scope.cancelSettle = function () {
                var urlData = {
                    'projectId': $stateParams.projectId,
                    'plm': $stateParams.plm
                };
                settleService.cancelSettle(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        //项目点击取消结算申请成功后状态需要进行转换
                        $scope.status = '1';
                    }
                    inform.common(data.message);
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * 项目完成结算申请
             */
            $scope.finishSettle = function () {
                //未上传文件
                if($scope.spec.attachmentAddress.length === 0) {
                    inform.common("请上传结算材料");
                    return;
                }

                var urlData = {
                    'projectId': $stateParams.projectId,
                    'plm': $stateParams.plm,
                    'address': $scope.spec.attachmentAddress
                };
                settleService.finishSettle(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.finishBtn = false;
                    }
                    inform.common(data.message);
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 文件选择事件
             */
            $scope.selectFile = function() {
                document.getElementById("files").click();
            };

            //返回结算页面
            $scope.goBack = function() {
                var type;

                if ($stateParams.plm === '0') {
                    //从项目结算进入
                    type = '1';
                } else {
                    //从plm进入
                    type = '2';
                }
                $state.go("app.office.settleController",{
                    type: type
                });
            }
            /*
            * 汇总
            * */
            function getSum(){
                var urlData={
                    'projectId':$stateParams.projectId,
                    'plm':$stateParams.plm,
                    'status':['0']
                }
                costMonitoringService.getSum(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.amountList = data.data
                        $scope.amountList.forEach(function (item) {
                            item.budget = inform.formatMoney(item.budget);
                            item.develop = inform.formatMoney(item.develop);
                        })
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /*
            * 详细与统计切换
            * */
            $scope.showDataFun = function () {
                if ($scope.showData === '0') {
                    $scope.showData = '1';
                } else {
                    $scope.showData = '0';
                }
            }
            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }
    ]);
})();

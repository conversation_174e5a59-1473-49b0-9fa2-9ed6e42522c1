(function() {
    'use strict';
  app.factory('staffCertificateService', staffCertificateService);
  staffCertificateService.$inject=["HttpService",'$rootScope'];

  function staffCertificateService(HttpService,$rootScope){

    var service={
       selectByParam:selectByParam,
       getStaffAllName:getStaffAllName,
       getStaffId:getStaffId,
       addByParam:addByParam,
       deleteByIds:deleteByIds,
       getCertificateInfo:getCertificateInfo,
       updateByParam:updateByParam,
       getPicture:getPicture,
       delPicByParam:delPicByParam,
       downloadPicture:downloadPicture,
       selectById:selectById
    };
    return service;
    /**
     * 查询员工证书信息
     */
    function selectByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffCertificate/selectByParam', urlData);
    }

     /**
     * 新增员工证书信息
     */
    function addByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffCertificate/insertObj', urlData);
    }

     /**
     * 删除员工证书信息
     */
    function deleteByIds(id) {
        return HttpService.get($rootScope.getWaySystemApi + 'staffCertificate/delById?id='+id);
    }

    /**
     * 根据id号查询员工证书信息
     */
    function getCertificateInfo(id) {
        return HttpService.get($rootScope.getWaySystemApi + 'staffCertificate/getCertificateInfoById?id='+id);
    }

    /**
     * 根据id号更新员工证书信息
     */
    function updateByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffCertificate/updateObj',urlData);
    }

    function getStaffAllName(){
        return HttpService.post($rootScope.getWaySystemApi + 'staffInfo/getAllStaffName');
    }

    function getStaffId(employeeName){
        return HttpService.post($rootScope.getWaySystemApi + 'staffInfo/selectStaffId',employeeName);
    }

    /**
     * 根据id号更新员工证书信息
     */
    function getPicture(id) {
        return HttpService.get($rootScope.getWaySystemApi+'picture/selectPictureById',{'id':id});
    }

    //根据id删除员工证书照片
    function delPicByParam(id) {
        return HttpService.post($rootScope.getWaySystemApi + 'picture/delPhoto', id);
    }

    //根据id下载员工证书照片
    function downloadPicture(id) {
        return HttpService.get($rootScope.getWaySystemApi+'picture/downloadPicture',
            				{'id':id});
    }

    //根据id下载员工证书照片
    function selectById(id) {
        return HttpService.get($rootScope.getWaySystemApi+'staffCertificate/selectById',
                            {'id':id});
    }

  }
})();
(function () {
    app.controller('confirmationSheetUpdateManagement', [
        'comService',
        '$rootScope',
        '$scope',
        'inform',
        'confirmationSheetService',
        'Trans',
        'AgreeConstant',
        '$modal',
        '$state',
        '$stateParams',
        'LocalCache',
        'reportService',
        function (
            comService,
            $rootScope,
            $scope,
            inform,
            confirmationSheetService,
            Trans,
            AgreeConstant,
            $modal,
            $state,
            $stateParams,
            LocalCache,
            reportService
        ) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //获取缓存
            $scope.formRefer = {};
            $scope.formRefer = LocalCache.getObject('confirmationSheet_formRefer');
            $scope.type = '1';
            $scope.remark =
                '用于状态为成功时修改\n只能修改发布版本号、配置项管理和评审详情\n注意：须分别在对应Tab页签点击保存修改';
            //评审选择对象
            $scope.sheetSelected = [];
            $scope.classify = ['新增需求', '修改bug', '功能优化', '版本定制'];
            //评审主题Map
            $scope.themeMap = reviewThemeListConfig;
            $scope.fileTypes = ['发布包', '脚本', '手册', '源代码'];
            $scope.distributions = ['否', '是'];
            $scope.repository = [];
            //查询评审列表
            $scope.reviewList = [];
            //评审结果展示Map
            $scope.resMap = {
                0: '有条件通过',
                1: '通过',
                2: '不通过',
                3: '未评审',
            };
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //替换HTML标签的表达式
            var regxHTML = /<[^>]*>|<\/[^>]*>/gm;
            //禅道产品线下拉框
            $scope.proProductLineList = AgreeConstant.proProductLineList;
            //初始化按钮权限
            $scope.saveInfoWhenSuccess = false; //状态为成功时的保存修改按钮权限初始化
            getButtonPermission(); //按钮权限校验

            //获取数据
            $scope.getData = getData;
            getData();
            initPages();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */

            /**
             * 页面初始化
             */
            function initPages() {
                //获取员工信息
                $scope.employeesList = [];
                $scope.employees = [];
                comService.getParamList('项目管理员', '').then(function (data) {
                    if (data.data) {
                        $scope.employeesList = data.data;
                    }
                });
                //获取产品线
                $scope.projectLine = [];
                comService.queryEffectiveParam('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.projectLine = data.data;
                    }
                });

                //仓库集合
                $scope.repositoryList = [];
                confirmationSheetService.getRepositoryList().then(function (data) {
                    if (data.data) {
                        angular.forEach(data.data, function (i) {
                            $scope.repositoryList.push(i.repositoryName + '@' + i.type);
                        });
                    }
                });
            }

            /**
             * 按钮权限管理
             */
            function getButtonPermission() {
                var buttons = {
                    'Button-ConfirmationSheet-saveInfoIfSucc': 'saveInfoWhenSuccess', //状态为成功时修改
                };
                var urlData = {
                    userId: LocalCache.getSession('userId'),
                    parentPermission: 'Button-ConfirmationSheet',
                    buttons: buttons,
                };
                comService.getButtonPermission(urlData, $scope);
            }

            /**
             * 获取确认单详情
             */
            function getData() {
                var urlData = {
                    id: $scope.formRefer.testReportId, //测试报告Id
                };
                confirmationSheetService.getConfirmationSheetInfo(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.confirmationSheetInfo = data.data;
                            $scope.testSpec = $scope.confirmationSheetInfo.testSpec;
                            //发布/上线风险说明
                            if ($scope.confirmationSheetInfo.riskNote) {
                                $scope.confirmationSheetInfo.riskNote = $scope.confirmationSheetInfo.riskNote
                                    .replace(regxHTML, '')
                                    .replace('/&nbsp;/g', ' ');
                            }
                            //内部验收测试情况说明
                            if ($scope.confirmationSheetInfo.reportSummary) {
                                $scope.confirmationSheetInfo.reportSummary = $scope.confirmationSheetInfo.reportSummary
                                    .replace(regxHTML, '')
                                    .replace('/&nbsp;/g', ' ');
                            }
                            setConfirmationSheetType();
                            setRepository();
                            getReviewList();
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            //查询评审列表
            function getReviewList() {
                var urlData1 = {
                    projectId: $scope.confirmationSheetInfo.projectId,
                    currentPage: 1,
                    pageSize: 400,
                    orderByParam: "DATE_FORMAT(PLAN_COMPLETION_DATE, '%Y-%m-%d') desc",
                };

                reportService.getReportInfo(urlData1).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            //测试评审id字符串拼接起来
                            var reviewIdStr =
                                $scope.confirmationSheetInfo.testPlanReviewIdStr +
                                $scope.confirmationSheetInfo.testCaseReviewIdStr +
                                $scope.confirmationSheetInfo.testReportReviewIdStr;
                            var idArray = [];

                            if (reviewIdStr !== '') {
                                //按照逗号分隔成数组
                                idArray = reviewIdStr.split(',');
                            }

                            data.data = angular.fromJson(data.data.list);

                            var filter = data.data.filter(function (item) {
                                return (
                                    item.REVIEW_THEME === '系统测试用例' ||
                                    item.REVIEW_THEME === '系统测试报告' ||
                                    item.REVIEW_THEME === '系统测试计划' ||
                                    item.REVIEW_THEME === '集成测试用例' ||
                                    item.REVIEW_THEME === '集成测试报告' ||
                                    item.REVIEW_THEME === '集成计划'
                                );
                            });

                            angular.forEach(filter, function (res) {
                                if (idArray.length !== 0) {
                                    //将关联上的评审id 值变为选中
                                    for (var i = 0; i < idArray.length; i++) {
                                        if (idArray[i] === res.ID) {
                                            res.checked = true;
                                        }
                                    }
                                }
                                $scope.reviewList.push(res);
                            });
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            //单选项目
            $scope.selectOneReview = function (i) {
                var item = i;
                var param1 = '';
                if (item.REVIEW_THEME.indexOf('计划') !== -1) {
                    param1 = '计划';
                } else if (item.REVIEW_THEME.indexOf('报告') !== -1) {
                    param1 = '报告';
                } else {
                    param1 = '用例';
                }
                if (item.REVIEW_CONTENT.indexOf(param1) === -1) {
                    inform.common('该评审内容与评审类别不符，请核对评审类别是否正确！');
                    item.checked = false;
                    return false;
                }

                var index = $scope.sheetSelected.indexOf(i);
                if (index === -1) {
                    $scope.sheetSelected.push(i);
                } else if (index !== -1) {
                    $scope.sheetSelected.splice(index, 1);
                }
            };
            //设置已关联的仓库
            function setRepository() {
                if ($scope.confirmationSheetInfo.relationList.length > 0) {
                    angular.forEach($scope.confirmationSheetInfo.relationList, function (i) {
                        $scope.repository.push(i.unique);
                    });
                }
            }
            //设置确认单类型及发布风险、同意发布的反显
            function setConfirmationSheetType() {
                //确认单类型
                if ($scope.confirmationSheetInfo.confirmType === 1) {
                    var update = document.getElementById('update');
                    update.checked = true;
                } else if ($scope.confirmationSheetInfo.confirmType === 2) {
                    var online = document.getElementById('online');
                    online.checked = true;
                } else {
                    var publish = document.getElementById('publish');
                    publish.checked = true;
                }

                //是否存在发布风险
                if ($scope.confirmationSheetInfo.riskFlag === 1) {
                    var hasPublishRisk = document.getElementById('hasPublishRisk');
                    hasPublishRisk.checked = true;
                    $scope.riskValue = '是';
                } else {
                    var noHasPublishRisk = document.getElementById('noHasPublishRisk');
                    noHasPublishRisk.checked = true;
                    $scope.riskValue = '否';
                }

                //是否同意发布
                if ($scope.confirmationSheetInfo.agreeFlag === 1) {
                    var noAgreePublish = document.getElementById('noAgreePublish');
                    noAgreePublish.checked = true;
                    $scope.agreeValue = '否';
                } else {
                    var agreePublish = document.getElementById('agreePublish');
                    agreePublish.checked = true;
                    $scope.agreeValue = '是';
                }
            }

            //返回
            $scope.goBack = function () {
                $state.go('app.office.confirmationSheet');
            };

            $scope.getConfigList = function () {
                $scope.configSelected = JSON.parse(JSON.stringify($scope.confirmationSheetInfo.configList));
                $scope.configSelectedName = [];
                angular.forEach($scope.configSelected, function (select) {
                    $scope.configSelectedName.push(select.url);
                });
                var date = new Date();
                $scope.configEndDate = inform.format(date, 'yyyy-MM-dd');
                var startDate = date.setDate(date.getDate() - 3);
                $scope.configStartDate = inform.format(startDate, 'yyyy-MM-dd');
                if ($scope.repository.length > 0) {
                    //获取第一个仓库的配置项集合
                    $scope.selectConfigList($scope.repository[0]);
                }
            };

            $scope.selectConfigList = function (m) {
                if (m) {
                    $scope.currentRepository = m;
                }
                //修改被选中的字体颜色
                var repositoryItem = document.getElementsByName('repositoryItem');
                angular.forEach(repositoryItem, function (item) {
                    if (item.id === $scope.currentRepository) {
                        item.style.color = '#F27739';
                    } else {
                        item.style.color = 'black';
                    }
                });
                var sp = $scope.currentRepository.split('@');
                var urlData = {
                    startTime: $scope.configStartDate,
                    endTime: $scope.configEndDate,
                    type: sp[1],
                    repositoryName: sp[0],
                    projectNumber: $scope.confirmationSheetInfo.projectNumber,
                    name: $scope.name,
                };
                //查询当前仓库的标签集合
                confirmationSheetService.getCurrentRepositoryConfig(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.configDataList = data.data;
                            //被选中的配置项反显
                            showSelectedConfig();
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };

            //保存
            $scope.saveConfirmationSheet = function (CallBack) {
                //设置单选框的值
                setRadioValue();
                //校验必填项
                if (!$scope.confirmationSheetInfo.versionNumber) {
                    inform.common('请输入发布版本号！');
                    return;
                }
                if (!$scope.confirmationSheetInfo.product) {
                    inform.common('请选择产品线！');
                    return;
                }
                if (!$scope.confirmationSheetInfo.administrator) {
                    inform.common('请选择项目管理员！');
                    return;
                }
                if ($scope.confirmationSheetInfo.riskFlag === '1' && !$scope.confirmationSheetInfo.riskNote) {
                    inform.common('请输入发布/上线风险说明！');
                    return;
                }

                //设置仓库集合对象
                setRepositoryList();

                $scope.confirmationSheetInfo.relationList = $scope.relationList;
                $scope.confirmationSheetInfo.reviewList = [];
                $scope.confirmationSheetInfo.testReportReviewIdStr = '';
                $scope.confirmationSheetInfo.testPlanReviewIdStr = '';
                $scope.confirmationSheetInfo.testCaseReviewIdStr = '';
                angular.forEach($scope.reviewList, function (res) {
                    if (res.checked) {
                        $scope.confirmationSheetInfo.reviewList.push(res);
                        if (res.REVIEW_THEME.indexOf('报告') !== -1) {
                            $scope.confirmationSheetInfo.testReportReviewIdStr =
                                $scope.confirmationSheetInfo.testReportReviewIdStr + res.ID + ',';
                        }
                        if (res.REVIEW_THEME.indexOf('计划') !== -1) {
                            $scope.confirmationSheetInfo.testPlanReviewIdStr =
                                $scope.confirmationSheetInfo.testPlanReviewIdStr + res.ID + ',';
                        }
                        if (res.REVIEW_THEME.indexOf('用例') !== -1) {
                            $scope.confirmationSheetInfo.testCaseReviewIdStr =
                                $scope.confirmationSheetInfo.testCaseReviewIdStr + res.ID + ',';
                        }
                    }
                });

                confirmationSheetService.saveConfirmSheet($scope.confirmationSheetInfo).then(
                    function (data) {
                        inform.common(data.message);
                        if (CallBack) {
                            //校验是否关联了测试计划、测试用例、测试报告的评审
                            if (
                                $scope.confirmationSheetInfo.testPlanReviewIdStr === '' ||
                                $scope.confirmationSheetInfo.testCaseReviewIdStr === '' ||
                                $scope.confirmationSheetInfo.testReportReviewIdStr === ''
                            ) {
                                inform.modalInstance('未完成评审关联，请确认是否提交? ').result.then(function () {
                                    CallBack();
                                });
                            } else {
                                CallBack();
                            }
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            //反显已选中的配置项
            function showSelectedConfig() {
                angular.forEach($scope.configDataList, function (configData) {
                    angular.forEach($scope.configSelected, function (configSelect) {
                        if (configSelect.url === configData.url) {
                            configData.checked = true;
                        }
                    });
                });
            }
            /*** 全选函数*/
            $scope.selectAll = function () {
                if ($scope.select_all) {
                    angular.forEach($scope.configDataList, function (i) {
                        i.checked = true;
                        if ($scope.configSelectedName.indexOf(i.url) === -1) {
                            $scope.configSelectedName.push(i.url);
                            $scope.configSelected.push(i);
                        }
                    });
                } else {
                    angular.forEach($scope.configDataList, function (i) {
                        i.checked = false;
                        var index = $scope.configSelectedName.indexOf(i.url);
                        if (index !== -1) {
                            $scope.configSelectedName.splice(index, 1);
                            angular.forEach($scope.configSelected, function (select) {
                                if (i.url === select.url) {
                                    $scope.configSelected.splice($scope.configSelected.indexOf(select), 1);
                                }
                            });
                        }
                    });
                }
            };
            /*** 单选项目*/
            $scope.selectOne = function (i) {
                $scope.select_all = false;
                var index = $scope.configSelectedName.indexOf(i.url);
                if (index === -1 && i.checked) {
                    $scope.configSelectedName.push(i.url);
                    $scope.configSelected.push(i);
                } else if (index !== -1 && !i.checked) {
                    $scope.configSelectedName.splice(index, 1);
                    angular.forEach($scope.configSelected, function (configSelect) {
                        if (i.url === configSelect.url) {
                            $scope.configSelected.splice($scope.configSelected.indexOf(configSelect), 1);
                        }
                    });
                }
            };

            /*
             * 点击删除按钮
             * */
            $scope.deleteConfigInfo = function (index) {
                $scope.confirmationSheetInfo.configList.splice(index, 1);
            };
            //刷新配置项列表
            $scope.configRefresh = function () {
                //将未选中的删除
                angular.forEach($scope.confirmationSheetInfo.configList, function (config) {
                    var flag = 0;
                    angular.forEach($scope.configSelected, function (configSelect) {
                        if (config.url === configSelect.url) {
                            flag = 1;
                        }
                        if ('源代码' === configSelect.fileType) {
                            configSelect.isDistribution = '否';
                        } else {
                            configSelect.isDistribution = '是';
                        }
                    });
                    if (flag === 0) {
                        $scope.confirmationSheetInfo.configList.splice(
                            $scope.confirmationSheetInfo.configList.indexOf(config),
                            1
                        );
                    }
                });
                //将选择但不存在的添加
                angular.forEach($scope.configSelected, function (configSelect) {
                    var flag = 0;
                    angular.forEach($scope.confirmationSheetInfo.configList, function (config) {
                        if (config.url === configSelect.url) {
                            flag = 1;
                        }
                        if ('源代码' === configSelect.fileType) {
                            configSelect.isDistribution = '否';
                        } else {
                            configSelect.isDistribution = '是';
                        }
                    });
                    if (flag === 0) {
                        $scope.confirmationSheetInfo.configList.push(configSelect);
                    }
                });
                $('#config_modal').modal('hide');
            };
            //设置仓库保存对象
            function setRepositoryList() {
                $scope.relationList = [];
                angular.forEach($scope.repository, function (i) {
                    var sp = i.split('@');
                    var date = {
                        repositoryName: sp[0],
                        type: sp[1],
                    };
                    $scope.relationList.push(date);
                });
            }

            function setRadioValue() {
                //确认单类型
                var confirmationSheetTypes = document.getElementsByName('confirmationSheetType');
                angular.forEach(confirmationSheetTypes, function (confirmationSheetType) {
                    if (confirmationSheetType.checked) {
                        $scope.confirmationSheetInfo.confirmType = confirmationSheetType.value;
                    }
                });
                //是否存在发布风险
                var publishRisks = document.getElementsByName('publishRisk');
                angular.forEach(publishRisks, function (publishRisk) {
                    if (publishRisk.checked) {
                        $scope.confirmationSheetInfo.riskFlag = publishRisk.value;
                    }
                });
                //是否同意发布
                var agreePublishs = document.getElementsByName('agreePublish');
                angular.forEach(agreePublishs, function (agreePublish) {
                    if (agreePublish.checked) {
                        $scope.confirmationSheetInfo.agreeFlag = agreePublish.value;
                    }
                });
            }

            $scope.change = function (m) {
                if (m === '1') {
                    $scope.confirmationSheetInfo.testSpec = '不符合';
                } else {
                    $scope.confirmationSheetInfo.testSpec = $scope.testSpec;
                }
            };
            $scope.startOaFlow = function () {
                //校验必填项
                if (!$scope.confirmationSheetInfo.versionNumber) {
                    inform.common('请输入发布版本号！');
                    return;
                }
                if (!$scope.confirmationSheetInfo.product) {
                    inform.common('请选择产品线！');
                    return;
                }
                if (!$scope.confirmationSheetInfo.administrator) {
                    inform.common('请选择项目管理员！');
                    return;
                }
                if ($scope.confirmationSheetInfo.riskFlag === '1' && !$scope.confirmationSheetInfo.riskNote) {
                    inform.common('请输入发布/上线风险说明！');
                    return;
                }
                $scope.saveConfirmationSheet(startFlowCallBack);
            };

            /**
             * 保存成功后跳转到钉钉发起界面
             **/
            function startFlowCallBack(result) {
                $state.go('app.office.confirmationSheetConfirm', { id: $scope.formRefer.testReportId });
            }
            //下载
            $scope.downloadConfirmationSheet = function () {
                //设置单选框的值
                setRadioValue();
                //校验必填项
                if (!$scope.confirmationSheetInfo.versionNumber) {
                    inform.common('请输入发布版本号！');
                    return;
                }
                if (!$scope.confirmationSheetInfo.product) {
                    inform.common('请选择产品线！');
                    return;
                }
                if (!$scope.confirmationSheetInfo.administrator) {
                    inform.common('请选择项目管理员！');
                    return;
                }
                if ($scope.confirmationSheetInfo.riskFlag === '1' && !$scope.confirmationSheetInfo.riskNote) {
                    inform.common('请输入发布/上线风险说明！');
                    return;
                }
                //设置仓库集合对象
                setRepositoryList();

                $scope.confirmationSheetInfo.relationList = $scope.relationList;
                inform.modalInstance('确定要下载吗?').result.then(function () {
                    inform.downLoadFile(
                        'confirmAction/toExcel',
                        $scope.confirmationSheetInfo,
                        $scope.confirmationSheetInfo.projectName +
                            '+' +
                            $scope.confirmationSheetInfo.versionNumber +
                            '+发布确认单.xlsx'
                    );
                });
            };
            $scope.saveInfoAfterSuccess = function () {
                var urlData;
                if ($scope.type === '1') {
                    //只能修改发布版本号
                    urlData = {
                        testReportId: $scope.formRefer.testReportId,
                        versionNumber: $scope.confirmationSheetInfo.versionNumber,
                        status: $scope.type,
                    };
                } else {
                    var review_test_plan = '';
                    var review_test_report = '';
                    var review_test_case = '';
                    var reviewList = [];
                    angular.forEach($scope.reviewList, function (res) {
                        if (res.checked) {
                            reviewList.push(res);
                        }
                    });
                    angular.forEach(reviewList, function (item) {
                        if (item.REVIEW_THEME === '系统测试计划' || item.REVIEW_THEME === '集成计划') {
                            review_test_plan = review_test_plan + item.ID + ',';
                        } else if (item.REVIEW_THEME === '集成测试报告' || item.REVIEW_THEME === '系统测试报告') {
                            review_test_report = review_test_report + item.ID + ',';
                        } else if (item.REVIEW_THEME === '集成测试用例' || item.REVIEW_THEME === '系统测试用例') {
                            review_test_case = review_test_case + item.ID + ',';
                        }
                    });
                    urlData = {
                        testReportId: $scope.formRefer.testReportId,
                        testPlanReviewIdStr: review_test_plan,
                        testReportReviewIdStr: review_test_report,
                        testCaseReviewIdStr: review_test_case,
                        status: $scope.type,
                    };
                }
                confirmationSheetService.updateAfterSuccess(urlData).then(
                    function (data) {
                        inform.common(data.message);
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            //刷新
            $scope.refreshConfig = function () {
                if ($scope.currentRepository === undefined) {
                    inform.common('请先选择项目已关联的仓库');
                    return;
                }
                var sp = $scope.currentRepository.split('@');
                var urlData = {
                    startTime: $scope.configStartDate,
                    endTime: $scope.configEndDate,
                    type: sp[1],
                    repositoryName: sp[0],
                    projectNumber: $scope.confirmationSheetInfo.projectNumber,
                    name: $scope.name,
                };
                //刷新当前仓库的标签集合
                confirmationSheetService.refreshProjectRepository(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.configDataList = data.data;
                            //被选中的配置项反显
                            showSelectedConfig();
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        },
    ]);
})();

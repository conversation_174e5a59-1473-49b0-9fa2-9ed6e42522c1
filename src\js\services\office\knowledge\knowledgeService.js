
(function() {
    'use strict';
  app.factory('knowledgeService', knowledgeService);
  knowledgeService.$inject=["HttpService",'$rootScope'];

  function knowledgeService(HttpService,$rootScope){
    
    var service={
    	toExcel:toExcel,
    	getData:getData,
        getMessage:getMessage
    };
    return service;
    /**
     * 将前台表格内容进行下载
     */
    function toExcel(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'knowledge/toExcel', urlData);
    }
    /**
     * 获取知识库数据
     */
    function getData(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'knowledge/getData', urlData);
    }

      /**
       * 获取知识库信息
       */
      function getMessage(urlData){
          return HttpService.post($rootScope.getWaySystemApi + 'knowledge/getMessage', urlData);
      }
  }
})();

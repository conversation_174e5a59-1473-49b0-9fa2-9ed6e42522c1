
(function() {
	app.controller("hardwareModeManagement", ['comService', '$rootScope', '$scope', 'hardwareModeManagementService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', '$stateParams','LocalCache',
		function(comService, $rootScope, $scope, hardwareModeManagementService, inform, Trans, AgreeConstant, $modal, $state, $stateParams,LocalCache) {
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	//获取缓存
		$scope.formRefer ={};
        $scope.formRefer = LocalCache.getObject('hardwareModeManagement_formRefer');
        //对原缓存进行覆盖
        LocalCache.setObject("hardwareModeManagement_formRefer",{});
        //页面分页信息
        $scope.pages = {
            pageNum : '',   //分页页数
            size : '',      //分页每页大小
            total : ''      //数据总数
        };
        //硬件型号状态下拉框数
		$scope.hardwareModeStatusSelect = [{
			value: '0',
			label: '启用'
		},{
			value: '1',
			label: '停用'
		}];
		//状态展示Map
		$scope.hardwareModeStatusMap = {
			"0":'启用',
			"1":'停用'
		};
		$scope.formRefer.hardwareModeStatus = "0";
    	// 初始化分页数据
    	$scope.pages = inform.initPages();
        //设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		//获取数据
		$scope.getData = getData;
		getData();
		//初始化页面信息
        initPages();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */
        function initPages() {
				//获取产品线-下拉框
				$scope.projectLine = [];
                comService.queryEffectiveParam('PRODUCT_TYPE','PRODUCT_TYPE').then(function (data) {
					if (data.data) {
						$scope.projectLine = data.data;
					}
				});
				//获取产品线-产品类别-产品名称集合
                $scope.productLineAndType = [];
                $scope.productLineAndTypeMap = {};
                comService.getParamList('PRODUCT_TYPE','').then(function (data) {
					if (data.data) {
						$scope.productLineAndType = data.data;
                        angular.forEach($scope.productLineAndType,function (item) {
						    $scope.productLineAndTypeMap[item["param_code"]] = item["param_value"];
					    });
					}
				});
			}
		/**
		 * 获取项目
		 */
		function getData(pageNum) {
			var urlData ={
			    'hardwareModeNo':$scope.formRefer.hardwareModeNo,//硬件型号名称
			    'productLine':$scope.formRefer.productLine,//产品线
			    'hardwareModeStatus':$scope.formRefer.hardwareModeStatus,//状态
                'page':pageNum,
                'pageSize':$scope.pages.size
			};
			hardwareModeManagementService.getHardwareModeInfoList(urlData).then(function(data) {
				if (data.code===AgreeConstant.code) {
                    //硬件型号信息
                    $scope.hardwareModeData = data.data.list;
                    if ($scope.hardwareModeData.length===0) {
						$scope.pages = inform.initPages(); 			//初始化分页数据
						inform.common(Trans("tip.noData"));
                    } else {
                    // 分页信息设置
                    	$scope.pages.total = data.data.total;           // 页面数据总数
                    	$scope.pages.star = data.data.startRow;         // 页面起始数
                    	$scope.pages.end = data.data.endRow;            // 页面结束数
                    	$scope.pages.pageNum = data.data.pageNum;       //页号
                    }
				} else {
					inform.common(data.message);
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
		}
		/**
    	 * 设置列表的高度
    	 */
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 185);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 85);
 		}

	   /**
	    * 修改信息弹框，str存在，就是新增
	    */
       $scope.popModal = function (item,str){
        if(str==="0"){
                //新增
          $scope.hardwareModeInfoParam = {};
        }else{
        //修改
          $scope.hardwareModeInfoParam = angular.copy(item);
        }
        LocalCache.setObject('hardwareModeManagement_formRefer', $scope.formRefer);
        $state.go('app.office.hardwareModeManagementUpdate',{
        	hardwareModeInfoParam:JSON.stringify($scope.hardwareModeInfoParam),
        	isAdd:str
        });
      };

        /**
	     * *************************************************************
	     *              方法声明部分                                结束
	     * *************************************************************
	     */
		} ]);
})();
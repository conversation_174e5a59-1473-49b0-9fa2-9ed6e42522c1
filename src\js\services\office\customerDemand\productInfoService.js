/*
 * @Author: lixiang
 * @Date:   2019-09-17
 * @Last Modified by:   lixiang
 * @Last Modified time:
 */
(function() {
    'use strict';
  app.factory('productInfoService', productInfoService);
  productInfoService.$inject=["HttpService",'$rootScope'];

  function productInfoService(HttpService,$rootScope){

	var service={
			getAllMessage:getAllMessage,
			addMessage:addMessage,
			updateMessage:updateMessage,
			delMessage:delMessage
	};
    return service;



    /**
     * 获取产品线下所有的信息
     */
    function getAllMessage(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'productInfo/getAllProductInfo', urlData);
    }

    /**
     * 添加信息
     */
    function addMessage(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'productInfo/addProductInfo', urlData);
    }


    /**
     * 修改信息
     */
    function updateMessage(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'productInfo/updateProductInfo', urlData);
    }

     /**
         * 删除信息
     */
    function delMessage(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'productInfo/delProductInfo', urlData);
    }






  }
})();

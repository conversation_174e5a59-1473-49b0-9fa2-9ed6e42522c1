/*
 * @Author: fubaole
 * @Date:   2018-04-25 09:58:03
 * @Last Modified by: anxia
 * @Last Modified time: 2020-04-02 13:24:01
 * 使用方法：点击菜单时，保存当前url；
 */
(function() {
    'use strict';
    angular.module('app')
        .directive('setUrl', ['$timeout', '$state', 'LocalCache','$location', function($timeout, $state, LocalCache,$location) {
            return {
                restrict: 'A',
                link: function(scope, ele, att) {


                    // 点击事件
                    ele.bind("click", function() {
                        //  清除查询条件
                        // LocalCache.remove('searchMap');
                        // LocalCache.remove('pages');

                        // var orgUrl = DomainCookie.getCookie('stateUrl');
                        // 保存当前路径
                        // DomainCookie.SetUserCookie('stateUrl', att.setUrl);


                        // 跳转方式匹配
                        var reg = /^https|http|ftp/;
                        if (att.setUrl.indexOf('tvFrame') > -1 ) {
                        	$state.go(att.setUrl);
                        } else	if (att.setUrl.indexOf('app') === -1) {
                            // 打开新窗口页面
                            window.open(att.setUrl);
                        } else if (reg.test(att.setUrl)) {
                            // 全路径跳转
                            window.location.href = att.setUrl;
                        } else {
//                            if (att.setUrl === orgUrl) {
//                                // 路由跳转，重载
//                                $state.go(att.setUrl, {}, { reload: true });
//                            } else {
//                                // 路由跳转
//                                $state.go(att.setUrl);
//                            }
                            $state.go(att.setUrl);
                        }


                    });

                }
            };
        }]);
})();
(function () {
    app.controller("feedbackProblemController", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','feedbackProblemService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,feedbackProblemService,inform,Trans,AgreeConstant,LocalCache,$http) {

		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		$scope.formRefer ={};
		//分页
    	$scope.pages = inform.initPages();
    	//绑定导入excel文件控件改变事件
        $("#filesUpload").change(submitForm);
		//设置列表的高度
        setDivHeight();
        //窗体大小变化时重新计算高度
        $(window).resize(setDivHeight);

        //初始化数据
        initInfo();
        //严重程度
        $scope.problemSeveritySelect = ['事件','轻微线上问题','一般线上问题','严重线上问题'];
        //紧急程度
        $scope.problemUrgencySelect = ['极高','高','中','低'];
        //反馈类别
        $scope.feedbackTypeSelect = ['支持事项','新需求','线上bug','运维停机'];
        //状态
        $scope.approvalStatusSelect = ['未开始','进行中','临时解决','已完成','已暂停','已关闭','不解决'];
        //临时解决
        $scope.isTemporarySolutionSelect = ['是','否','不需要'];
        $scope.getData = getData;
        //重置
        $scope.rest = function () {
            $scope.formRefer = {};
            $scope.formRefer.startDate = inform.format(new Date(),"yyyy")+'-01-01';
        };
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
        /**
         * 设置列表的高度
         */
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (150 + 250);
            $("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight - 80);
        }
        /**
		 * 跳转修改
		 */
		$scope.go = function(item,flag) {
		    LocalCache.setObject('feedbackProblem_formRefer',$scope.formRefer);
			if(null ==item){
				$state.go("app.office.feedbackProblemManagement", null);
			} else {
                $state.go("app.office.feedbackProblemManagement", {item: item.problemId,flag:flag});
			}
		};

		 /**
		 * 跳转生成钉钉跟踪单流程确认界面
		 */
		$scope.goCreateTrackingSheet = function(item) {
		    LocalCache.setObject('feedbackProblem_formRefer',$scope.formRefer);
            $state.go("app.office.createTrackingSheet", {item: item.problemId,projectName:item.projectName});

		};
        
        $scope.goCreateOnlineServerEvent = function(item) {
		    LocalCache.setObject('feedbackProblem_formRefer',$scope.formRefer);
            $state.go("app.office.createOnlineServerEvent", {item: item.problemId,projectName:item.projectName});

		};
		/**
		 * 初始化
		 */
    	function initInfo() {
    	     //1 显示跳转项目周报界面按钮 0 不显示
             $scope.goProjectWeekFlag = 0;
    	    //获取山东新北洋集团的下级部门信息
            $scope.departmentList = [];
            comService.getOrgChildren('D010053').then(function(data) {
            	$scope.departmentList = comService.getDepartment(data.data);
            	//获取缓存
                $scope.formRefer = LocalCache.getObject('feedbackProblem_formRefer');
                //初始化默认日期
                if(!$scope.formRefer.startDate) {
                    $scope.formRefer.startDate=inform.format(new Date(),'yyyy')+'-01-01';
                }

                getData();
             });

            var  projectId = LocalCache.getSession('projectWeekly_projectId');
            //projectId 存在就显示跳转项目周报界面按钮
            if(projectId && projectId !== "null") {
                $scope.goProjectWeekFlag = 1;
            }
    	}
    	/**
         * 获取所有、某个反馈问题
         */
        function getData(pageNum) {
        	 var urlData = $scope.formRefer;
             urlData.currentPage=pageNum;//当前页数
             urlData.pageSize=$scope.pages.size;//每页显示条数
        	 feedbackProblemService.getData(urlData).then(function (data) {
             	if (data.code === AgreeConstant.code) {
                 		if(null==data.data){
                 			$scope.tableData = {};
                 			inform.common(Trans("tip.noData"));
                 			$scope.pages = inform.initPages();
                 		} else {
                 			//项目详情
                 			$scope.tableData = data.data.list;
                            //分页信息设置
                            $scope.pages.total = data.data.total;
                            $scope.pages.star = data.data.startRow;
                            $scope.pages.end = data.data.endRow;
                            $scope.pages.pageNum = data.data.pageNum;
                        }
                     } else {
                         inform.common(data.message);
                     }
                 },
                 function () {
                     inform.common(Trans("tip.requestError"));
                 });
        }
        /**
         * 跳转项目周报界面
         */
        $scope.goProjectWeek = function() {
             var  projectIdParam = LocalCache.getSession('projectWeekly_projectId');
            //清空
            LocalCache.setSession('projectWeekly_projectId',null);
            //跳转项目周报界面
            $state.go("app.office.projectWeeklyDetail", {
                projectId: projectIdParam,
                type: '3'
            });
        }
        /**
		 * excel下载反馈问题
		 */
		$scope.toExcel = function() {
			inform.modalInstance("确定要下载吗?").result.then(function () {
                inform.downLoadFile('feedbackProblem/toExcel',$scope.formRefer,'反馈问题.xlsx');
            });
		}
        
        /**
		 * excel下载停机事件
		 */
		$scope.toExcelOnlineServerEvent = function() {
			inform.modalInstance("确定要下载吗?").result.then(function () {
                inform.downLoadFile('feedbackProblem/toExcelOnlineServerEvent',$scope.formRefer,'停机事件.xlsx');
            });
		}
        /**
    	 * 下载反馈问题录入模板
    	 */
         $scope.toExcelModule = function () {
            inform.modalInstance("确定要下载吗?").result.then(function () {
                inform.downLoadFile('feedbackProblem/toExcelModule',{},'反馈问题录入模板.xlsx');
            });
        };
       /**
        * 导入反馈问题
        */
        $scope.selectFile = function() {
               document.getElementById("filesUpload").click();
        }

        /**
	     * 反馈问题上传文件
	     */
	    function submitForm(e){
	    	//表单id  初始化表单值
            var formData = new FormData();
            //获取文档中有类型为file的第一个input元素
            var file = document.querySelector('input[type=file]').files[0];
            if (!file) {
                inform.common("请先选择文件!");
                return false;
            }
            if (file.size > AgreeConstant.fileSize) {
                inform.common("上传文件大小不能超过2M");
                fileChangeReset();
                return false;
            }
            formData.append('fileName', file);
            var a = file.type;
            if (a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
                inform.common("请选择.xlsx类型的文档进行上传!");
                return false;
            } else {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function items() {
                            return "确定要导入吗！";
                        }
                    }
                });
                var uploadUrl = $rootScope.getWaySystemApi + 'feedbackProblem/uploadExcel';

                modalInstance.result.then(function () {
                    //开启遮罩层
                    inform.showLayer("上传中。。。。。。");
                    $.ajax({
                        url: uploadUrl,
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        beforeSend: function beforeSend(request) {
                            request.setRequestHeader("Authorization", 'Bearer ' + LocalCache.getSession("token") || '');
                        },
                        success: function success(result) {
                            if (result.code === AgreeConstant.code) {
                                // 关闭遮罩层
                                inform.closeLayer();
                                layer.confirm(result.message,{
                                    title:false,
                                    btn:['确定']
                                },function(confirmMsg){
                                    layer.close(confirmMsg);
                                });
                            } else {
           	                    inform.closeLayer();
                                $modal.open({
                                    templateUrl: 'tpl/common/errorModel.html',
                                    controller: 'ModalInstanceCtrl',
                                    size: "lg",
                                    resolve: {
                                         items: function() {
                                             return result.message;
                                        }
                                    }
                                });
                            }
                        //移除文件名称 通过表单元素的reset方法实现选择文件的重置
                        $("#formUpload")[0].reset();
                        getData();
                        },
                        error: function error(_error) {
                            inform.common(Trans("tip.requestError"));
                        }
                    });
                });
            }
	     }
	 		/**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */

	}]);
})();
/*
 * @Author: fubaole
 * @Date:   2017-09-28 13:45:34
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-19 10:11:23
 */
(function() {
    'use strict';
    app.controller("personnel_Add", ['$rootScope', '$scope', '$state', '$stateParams', 'inform', 'Trans', 'SystemService', 'AgreeConstant',
        function($rootScope, $scope, $state, $stateParams, inform, Trans, SystemService, AgreeConstant) {
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.person = {};
            $scope.person.isUser = AgreeConstant.setUsergroup; // 默认 设置用户 是
            getAllUserGroup(); // 获取用户组信息
            getAllRole(); //获取所有的角色信息
            $scope.openDate = openDate; // 日期插件
            $scope.toggleMin = toggleMin;
            $scope.toggleMin();
            $scope.person.roleIds = []; // 角色id
            $scope.person.userGroupIds = []; // 用户组id
            $scope.updataType = updataType;
            $scope.onSubmit = onSubmit; // 保存修改操作
            $scope.isTrue = isTrue; // 设置为用户的默认值
            // 跳转页面判断
            function isTrue(str){
                if(str){
                    $scope.gotoUser = true;
                }else{
                    $scope.gotoUser = false;
                }
            }
            // 日期插件
            function openDate($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.opened = true;
            }

            $scope.dateOptions = {
                formatYear: 'yy',
                class: 'datepicker',
                showWeeks: false
            };

            function toggleMin() {
                $scope.currentDate = $scope.currentDate ? null : new Date();
            }

            // 获取所有角色信息
            function getAllRole() {
                SystemService.getRoleByLoginUserIdMap()
                    .then(function(data) {
                        if (data.code === AgreeConstant.resultCode) {
                            $scope.roleTypeData = data.result;
                        } else {
                            inform.common(data.message);
                        }
                    }, function() {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 获取用户组信息
            function getAllUserGroup() {
                //  授权修改之前的接口：SystemService.getGroupByMapWithoutPage()
                SystemService.getGroupByLoginId()
                    .then(function(data) {
                        if (data.code === AgreeConstant.resultCode) {
                            $scope.userGroupInfo = data.result;
                        } else {
                            inform.common(data.message);
                        }
                    }, function() {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            function updataType() {
                angular.forEach($scope.roleTypeData, function(i) {
                    var index = $scope.person.roleIds.indexOf(i.roleId);
                    if (i.selected && index === -1) {
                        $scope.person.roleIds.push(i.roleId);
                    } else if (!i.selected && index !== -1) {
                        $scope.person.roleIds.splice(index, 1);
                    }
                });
            }

            // 保存人员信息
            function onSubmit() {
            	//真实名默认值为空
                $scope.person.givenName = "";
                //手机号码默认值设置
            	$scope.person.phone = "17777777777";

                setPersonnelInfo();
            }

            // 保存人员信息
            function setPersonnelInfo() {
                SystemService.saveEmployeeExt($scope.person, $stateParams.orgId)
                    .then(function(data) {
                        if (data.code === AgreeConstant.resultCode) {
                            inform.common(Trans("tip.saveSuccess"));
                            if ($scope.gotoUser) {
                                $state.go("app.system.user_Add", { "userId": data.result, 'pageName': 'personnel_Add' });
                            } else {
                                $state.go("app.system.personnel_Management");
                            }
                        } else {
                            inform.common(data.message);
                        }
                    }, function() {
                        inform.common(Trans("tip.requestError"));
                    });
            }

        }
    ]);
})();
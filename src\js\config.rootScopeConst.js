/*
 * @Author: fubaole
 * @Date:   2018-03-19 09:32:55
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-29 17:59:20
 */
(function () {
    'use strict';
    angular.module('app').run([
        '$rootScope',
        function ($rootScope) {
            // 获取当前所在环境地址
            const { hostname } = window.location;

            $rootScope.authorityName = ''; //  授权模块
            $rootScope.messageName = ''; //  消息模块
            $rootScope.boardName = ''; //  工作台模块
            $rootScope.extName = ''; //  公共模块
            $rootScope.wcpSystemApi = 'http://wiki.xtjc.net/wcp/';
            $rootScope.zentaoSystemApi = 'http://*************/';
            // 根据不同环境（本地/测试/生产），使用不同地址
            if (hostname === '127.0.0.1' || hostname === 'localhost' || hostname === '**************') {
                $rootScope.getWaySystemApi = 'http://**************:7788/';
            } else {
                $rootScope.getWaySystemApi = 'http://************:7788/';
            }

            $rootScope.gateInfoApi = 'http://127.0.0.1:7788/';
            $rootScope.imageDownload = $rootScope.getWaySystemApi + 'fileUpload/download?fileName=';
            // 新北洋邮箱后缀
            $rootScope.snbcMailSuffix = '@newbeiyang.com';
            // RSA加密的公钥
            $rootScope.RSAPublicKey =
                'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC3LJvQYmPxZka4V9veALFTgBJvZvDm3Y6p95TD11mbMEJvlUm9NM3HE4MMUw6XLIw9luPm8qJpNf3NhKwhFZMtWZS5L+mgM3bE8a6NXVruk3mpglDSUrBQHlHYwCpbjDj5j8bcUmqvSTZl5fvMNrI2y7hMYwKvpN/e5NVn5cWvuwIDAQAB';
            // RSA解密的私钥
            $rootScope.RSAPrivateKey =
                'MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAIWz2spP022uqKbM83r0Q2/WmSlGuex+GyNufZPM0CfAohCvCOfJTmpSA2G1uq4NOuVqiNLYX2BCIxzMM1MsqM5E+yqHSzzwmipckuSdv/dpHiQ8qRjNEla2S7w11YS0lUEDjUPVkBxoRqiCv8j1pbNRcirgQsWKU5Pd94HNRp0VAgMBAAECgYBDA/hJaaOPGXl9ScNMwLxAhW71qnasnDTcGJinCYTYOAzDkghDrbo5PmxlgGJWtVjs/u4fyMZdiCjzWULskLj3EW8Hcle1k/BfrCRprOFLlYNqLX+R4kJuLA8MKOWduBeddX4TXn78tgtJI9Vz9LcvpnKHvsgoACoPrM03HF0UYQJBAPdr8Uu8KxMLKo88Lf0xYWoL1ZWxOBR5stReDpkshWzrsfIAVO4T+cXgMSR8KOqfN5ObLcdhMUTpcRdHFDCKQkkCQQCKVpFTEeE/B2pg3S/lOy/YGPkNT55u4qguOvRGDj/5Tnma6u+x1158dnrdz7nOYpK8BccabMybH8Ynk1YyZURtAkAoU+W+jGsQ7Y8ATbTJQhU4rbkgbwRPAg8N4k7K1KDiwf/9C+TL8WGSyRn5cmtQ4qayXma1yre3Hb2bJ7C4wLBRAkA+Ux4KBOYsFVUx6rTnW7EEdMvP2W0RnAAVQ+5Fmans0hQrXWk8AEHLZT2ZrTqS5wogt6GqIBZWVQxbTB/reIBJAkBGvjYGB/ninBQCqZBWbHujQXhEPvw8uBbI0tukx3uGmO+pGJzm7ozgIEVwD3FRRDQ/SpoP05zNYQ8XSxG8L+H3';
            // 封装公钥RSA加密的方法
            // 使用方法：$rootScope.RSAEncrypt("需加密的字符串");
            $rootScope.RSAEncrypt = function (str) {
                var encrypt = new JSEncrypt();
                encrypt.setPublicKey($rootScope.RSAPublicKey);
                return encrypt.encrypt(str);
            };
            $rootScope.RSADecrypt = function (str) {
                var encrypt0 = new JSEncrypt();
                encrypt0.setPrivateKey($rootScope.RSAPrivateKey);
                return encrypt0.decrypt(str);
            };
            window.addEventListener('resize', function () {
                if ($rootScope.bar) {
                    $rootScope.bar.resize();
                }
                if ($rootScope.pie) {
                    $rootScope.pie.resize();
                }
                if ($rootScope.autoBar) {
                    $rootScope.autoBar.resize();
                }
                if ($rootScope.lineBar) {
                    $rootScope.lineBar.resize();
                }
            });
        },
    ]);
})();

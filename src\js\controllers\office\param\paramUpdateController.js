/**
** 参数配置新增页面
**/
(function(){
   'use strict'
   app.controller("paramUpdateController",['$scope','$state','paramService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','$stateParams','LocalCache',
   function ($scope,$state,paramService, $rootScope, inform, Trans, AgreeConstant,$stateParams,LocalCache) {
   /**
     * *************************************************************
     *             初始化部分                                 开始
     * *************************************************************
     */
   $scope.limitList = AgreeConstant.limitList; // 正则校验配置
   $scope.change = {};

   //设置列表的高度
   setDivHeight();
   //窗体大小变化时重新计算高度
   $(window).resize(setDivHeight);
   //新增时明细列表
   $scope.jList = [];
   $scope.change.paramValue = $stateParams.paramValue;
   $scope.change.paramCode = $stateParams.paramCode;
   $scope.change.paramDesc = $stateParams.paramDesc;
   //根据状态显示按钮标志
   $scope.flagStatus = false;
   $scope.list = [];
   $scope.flag = false;
   selectData();
   /**
     * *************************************************************
     *              初始化部分                                 结束
     * *************************************************************
     */

    /**
     * *************************************************************
     *              方法声明部分                                 开始
     * *************************************************************
     */

    /**
    * 设置列表的高度
    */
    function setDivHeight(){
        //网页可见区域高度
        var clientHeight = document.body.clientHeight;
        var divHeight = clientHeight - (150 + 180);
        $("#divTBDis").height(divHeight);
        $("#subDivTBDis").height(divHeight - 50);
    }

    /**
    ** 根据字典名称、类型编码查询
    **/
    function selectData(){
        var param = {
           "paramTypeCode":$stateParams.paramCode,
           "paramName":$stateParams.paramName
        };
        paramService.queryParam(param).then(function(result){
            if (result.code === AgreeConstant.code) {
               $scope.paramList = result.data;

               if($scope.paramList.length===0){
                   $scope.pages = inform.initPages();
                   inform.common(Trans("tip.noData"));
               }
               if($scope.paramList.length===0){
                   $scope.flagStatus = true;
                   return;
               }
               for(var i = 0;i<$scope.paramList.length;i ++){
                  if("0"===$scope.paramList[i].paramStatus){
                     $scope.flagStatus = true;
                     return;
                  }else{
                     $scope.flagStatus = false;
                  }
               }

            }else{
                inform.common(result.message);
            }
        });
    }
     /**
     * 判断启用还是禁用
     */
    $scope.jude = function (item) {
        //启用 0为启用，1为禁用
        if (item.paramStatus === '0'){
            return "fa fa-check-circle green";
        }
        //禁用
        return "fa fa-ban";
    };

     /**
    * 保存信息
    */
    $scope.saveInfo = function () {
     if ($scope.list.length === 0){
         inform.common("请输入字典明细");
         return;
     }
     var list = [];
     $scope.flagList = true;
     //循环明细，查看是否有重复信息
     angular.forEach($scope.list, function (one, index) {
         one.paramName = $stateParams.paramName;
         one.paramCode = $stateParams.paramCode;
         one.paramTypeCode = $stateParams.paramTypeCode==="root"?$stateParams.paramName:$stateParams.paramCode;
         var paramValue = one.paramValue;
         //查看list中不存在 子类型编码-排列顺序
         var num = list.indexOf(paramValue);
         if (num > -1){
            $scope.flagList = false;
            return;
         }
         list.push(paramValue);
     });
     //如果存在重复信息
     if (!$scope.flagList){
         inform.common("明细中【子字典名称】存在重复信息,请修正。");
         return;
     }

     //新增
     addInfo($scope.list);

    };

    /**
    * 添加信息
    */
    function addInfo(urlData) {
         paramService.addParamByObj(urlData).then(function (data) {
              if (data.code === AgreeConstant.code) {
                  layer.confirm(data.message,{
                      title:false,
                      btn:['确定']
                  },function(result){
                      layer.close(result);
                      $scope.list = [];
                     $("#add_param").modal('hide');
                     selectData();
                  });
              } else {
                  inform.common(data.message);
              }
         }, function (error) {
              inform.common(Trans("tip.requestError"));
         });
    }


   /**
    * 修改参数状态
    */
    $scope.checkParamInfo = function (status) {
        var paramStatus = (status === '0')?"启用":"禁用";
        inform.modalInstance("是否全部"+paramStatus+"该参数").result.then(function() {
            var urlData = {
                    'paramName': $stateParams.paramName,
                    'paramTypeCode': $stateParams.paramTypeCode==="root"?"":$stateParams.paramCode,
                    'paramStatus': status
            };
            paramService.updateBatchByParam(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    $scope.flagStatus = !$scope.flagStatus;
                    inform.common(paramStatus+"成功");
                    selectData();
                } else {
                    inform.common(paramStatus+"失败");
                }
            },
            function (error) {
                inform.common(Trans("tip.requestError"));
            });
        });
    };

    //点击新增新增一行
    $scope.addParam = function(){
         $scope.addNewBind(0);
         $scope.deleteNewBind(1);
    }


    /**
     * 新增一个数据字典明细
     */
    $scope.addNewBind = function (index) {
        $scope.flag = true;
        //参数明细
        var judge = {
            'paramName':'',
            'paramTypeCode': '',
            'paramCode': '',//参数编码
            'paramValue': '',//参数名称
            'paramDesc': '',//描述
            'sort': '', //排列顺序
            'paramStatus':"0",//参数状态 默认为0，启用
            'isSystem':"1",//是否为系统参数 默认为1，业务参数
            'createUser':LocalCache.getSession('currentUserName')
        };
        $scope.list.push(judge);

    };
    //取消一行
    $scope.deleteNewBind = function (index) {
        if (index >= 0) {
            $scope.list.splice(index, 1);
        }
    };


    /**
     * 修改单个参数状态
     */
    $scope.checkInfo = function (m) {
        var status = (m.paramStatus==='0'?'禁用':'启用');
        inform.modalInstance("是否确认"+status+"该参数").result.then(function() {
            var urlData = {
                    'paramId': m.paramId,
                    'paramTypeCode': $stateParams.paramTypeCode==="root"?$stateParams.paramName:$stateParams.paramCode,
                    'paramName': $stateParams.paramName,
                    'paramCode':m.paramCode,
                    'paramStatus': (m.paramStatus === '0') ? '1' : '0'
            };
            paramService.updateByParam(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    m.paramStatus = (m.paramStatus === '0') ? '1' : '0';
                    selectData();
                    inform.common("启用，禁用成功");
                } else {
                    inform.common("启用，禁用失败");
                }
            },
            function (error) {
                inform.common(Trans("tip.requestError"));
            });
        });
    };


    //返回主页面
    $scope.goBack = function(){
        $state.go('app.office.paramController');
    };
    //两条记录更换顺序
    function setChangeSort(item,flag){
       if (flag === '-') {
            if(item >= $scope.paramList.length-1){
                return;
            }
            var sort = $scope.paramList[item];
            var sort1 = $scope.paramList[item + 1];
            $scope.paramList[item + 1] = sort;
            $scope.paramList[item] = sort1;

       }else{
           if(item === 0){
              return;
           }
           var sort2 = $scope.paramList[item];
           var sort3 = $scope.paramList[item - 1];
           $scope.paramList[item - 1] = sort2;
           $scope.paramList[item] = sort3;

       }

    }


    //更新明细排列顺序
    $scope.setNum = function (item,flag){
       //两条记录更换顺序
       setChangeSort(item,flag);
       var paramList = $scope.paramList;
       paramService.updateParamSort(paramList).then(function (result) {
           if (result.code === '0000') {
               selectData();
           }else{
               inform.common(result.message);
           }

       });


    };





    //更新参数
    $scope.updateData = function (item) {

        if(!item.paramValue){
           inform.common("子字典名称不能为空");
           return;
        }
        var param = {

            'paramTypeCode': item.paramTypeCode,
            'paramName': $stateParams.paramName,
            'paramId':item.paramId,
            'paramCode': item.paramCode,
            'paramValue': item.paramValue,
            'paramDesc':item.paramDesc,
            'sort': item.sort
        };
        inform.modalInstance("确定要更新该条记录吗？").result.then(function() {
            paramService.updateByParam(param).then(function (result) {
                if (result.code === '0000') {
                   layer.confirm(result.message,{
                         title:false,
                         btn:['确定']
                   },function(res){
                         layer.close(res);
                         $scope.list=[];
                         selectData();
                   });

                }else{
                    inform.common(result.message);
                }

            });
        });
    }












   }
   ]);
})();
//# sourceURL=js/services/office/projectSupport/projectSupportService.js
(function() {
    'use strict';
    app.factory('projectSupportService', projectSupportService);
    projectSupportService.$inject = ["HttpService", '$rootScope'];

    function projectSupportService(HttpService, $rootScope) {
        var service = {
            getProjectSupportInfo: getProjectSupportInfo,
             deleteProjectSupportInfo: deleteProjectSupportInfo,
             addProjectSupportInfo: addProjectSupportInfo,
             updateProjectSupportInfo: updateProjectSupportInfo
        };
        return service;
        /**
         * 分页查询低级质量问题
         */
        function getProjectSupportInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectSupport/getProjectSupportInfo', urlData);
        }
        /**
         * 删除低级质量问题
         */
        function deleteProjectSupportInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectSupport/deleteProjectSupportInfo', urlData);
        }
        /**
         * 新增低级质量问题
         */
        function addProjectSupportInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectSupport/addProjectSupportInfo', urlData);
        }
        /**
         * 修改低级质量问题
         */
        function updateProjectSupportInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectSupport/updateProjectSupportInfo', urlData);
        }
    }
})();
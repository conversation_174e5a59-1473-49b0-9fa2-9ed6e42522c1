(function() {
  'use strict';
  app.controller("user_Add", ['$stateParams', '$scope', '$state', '$timeout', 'inform', '$modal', 'SystemService', 'ConfigService','Trans', 'AgreeConstant',
    function($stateParams, $scope, $state, $timeout, inform, $modal, SystemService, ConfigService,Trans, AgreeConstant) {

      $scope.checkTreeNode = []; // 存放选中树节点
      $scope.addUserData = []; // 存放用户选中列表
      $scope.userList = []; // 存放所有用户信息
      $scope.paramChecked = []; // 存放路由参数
      $scope.treeData = []; //存放树结构数据

      $scope.getUserList = getUserList; // 获取用户列表
      $scope.addUserList = addUserList; // 获取选中用户信息
      $scope.selectAll = selectAll; // 用户全选
      $scope.selectOne = selectOne; // 用户单选
      $scope.onSubmit = onSubmit; // 提交
      $scope.open = open; // 删除选中数据
      $scope.returnPrev = returnPrev; // 页面跳转
      var Leftsetting = angular.copy(ConfigService.checkboxConfig);
      Leftsetting.callback.onCheck = onCheck; // 点击节点回调

      getTreeData(); //获取树结果数据

      // 设置侧边的高度,随窗口变动
      inform.autoHeight();
      window.onresize = inform.autoHeight;

      // 路由参数处理
      if ($stateParams.userId) {
        if ($stateParams.userId.indexOf(',')===-1) {
          $scope.paramChecked.push($stateParams.userId);
        } else {
          $scope.paramChecked = $stateParams.userId.split(',');
        }
        angular.forEach($scope.paramChecked, function(i) {
          getUserById(i);
        });
      }

      // 根据参数Id 获取用户信息
      function getUserById(id) {
        SystemService.getUser(id)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.addUserData.push(data.result);
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans('tip.requestError'));
          });
      }

      // 获取全部地域数据
      function getTreeData() {
        SystemService.getAllRegion()
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.treeData = [];
              // 多个参数userId 树没法设置地域，直接设置为没有默认选中的项
              if ($scope.paramChecked.length > 1 || $scope.paramChecked.length===0) {
                angular.forEach(data.result, function(res, i) {
                  var jsonTree = {
                    "id": res.regionId,
                    "pId": res.parentId,
                    "name": res.regionName,
                    "open": res.parentId==null ? true : false
                  };
                  data.result[i] = angular.extend(jsonTree, res);
                });
                $scope.treeData = data.result;
                $.fn.zTree.init($("#leftTree"), Leftsetting, $scope.treeData);
              } else {
                getRegionById(data.result, $scope.paramChecked.join());
              }
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 根据用户Id 获取权限
      function getRegionById(data, id) {
        SystemService.getRegionByUserId(id)
          .then(function(result) {
            if (result.code===AgreeConstant.resultCode) {
              angular.forEach(data, function(res, i) {
                var jsonTree = {
                  "id": res.regionId,
                  "pId": res.parentId,
                  "name": res.regionName
                };
                data[i] = angular.extend(jsonTree, res);
                angular.forEach(result.result, function(json, j) {
                  if (json.regionId===res.regionId) {
                    data[i].checked = true;
                    data[i].open = true;
                    $scope.checkTreeNode.push(json.regionId);
                  }
                });
              });
              $scope.treeData = data;
              $.fn.zTree.init($("#leftTree"), Leftsetting, $scope.treeData);
            } else {
              inform.common(result.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 获取树结构选中数据
      function onCheck(e, treeId, treeNode) {
        var treeObj = $.fn.zTree.getZTreeObj("leftTree");
        throttle(pushData, null, 10, treeObj);
      }

      function throttle(fn, context, delay, treeObj) {
        // 函数节流 解决频繁调用该函数
        $timeout.cancel(fn.timeoutId);
        fn.timeoutId = $timeout(function() {
          fn.call(context, treeObj);
        }, delay);
      }

      function pushData(treeObj) {
        $scope.checkTreeNode = []; // 存放选中的树节点（修改所在组织机构）
        var nodes = treeObj.getCheckedNodes(true);
        angular.forEach(nodes,function(res){
          $scope.checkTreeNode.push(res.id);
        });
        console.log($scope.checkTreeNode);
      }

      // 选择用户操作
      function getUserList() {
        $scope.checked = [];
        $scope.userList = [];
        // 获取用户列表
        SystemService.getUserListByMap(JSON.stringify({}))
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              // 取data.result 和 $scope.addUserData的差集
              angular.forEach(data.result, function(i) {
                var flag = true;
                angular.forEach($scope.addUserData, function(j) {
                  if (i.userId===j.userId) {
                    flag = false;
                  }
                });
                if (flag) {
                  $scope.userList.push(i);
                }
              });
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans('tip.requestError'));
          });
      }

      // 保存选择的用户到$scope.addUserData中
      function addUserList() {
        angular.forEach($scope.checked, function(i) {
          getUserById(i);
          $scope.checked = [];
          $scope.select_all = false;
        });
      }

      // 存放弹框选中的用户信息
      function selectAll() {
        if ($scope.select_all) {
          $scope.checked = [];
          angular.forEach($scope.userList, function(i) {
            $scope.checked.push(i.userId);
            i.checked = true;
          });
        } else {
          angular.forEach($scope.userList, function(i) {
            i.checked = false;
          });
          $scope.checked = [];
        }
      }

      function selectOne(i) {
        var index = $scope.checked.indexOf(i.userId);
        if (index === -1 && i.checked) {
          $scope.checked.push(i.userId);
        } else if (index !== -1 && !i.checked) {
          $scope.checked.splice(index, 1);
        }
        if ($scope.userList.length === $scope.checked.length) {
          $scope.select_all = true;
        } else {
          $scope.select_all = false;
        }
      }

      // 提交数据
      function onSubmit() {
        if (!$scope.checkTreeNode.length) {
          inform.common(Trans("region.chooseRegion"));
          return false;
        }
        if (!$scope.addUserData.length) {
          inform.common(Trans("region.chooseUser"));
          return false;
        }
        var userIds = [];
        angular.forEach($scope.addUserData, function(i) {
          userIds.push(i.userId);
        });

        var map = {
          regions: $scope.checkTreeNode.join(),
          userIds: userIds.join()
        };
        SystemService.saveUserToRegion(map)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              inform.common(Trans("tip.saveSuccess"));
              $state.go("app.system.user_Management");
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 删除用户
      function open(item) {
        var modalInstance = $modal.open({
          templateUrl: 'myModalContent.html',
          controller: 'ModalInstanceCtrl',
          size: "sm",
          resolve: {
            items: function() {
              return Trans("common.deleteTip");
            }
          }
        });
        modalInstance.result.then(function() {
          // 删除掉数据
          $scope.addUserData.splice(item, 1);
        });
      }

      // 跳转页面
      function returnPrev() {
        if ($stateParams.pageName) {
          $state.go("app.system.personnel_Management");
        } else {
          $state.go("app.system.user_Management");
        }
      }
    }
  ]);
})();
(function () {
    app.controller("bugOnlineDetailController", ['bugOnlineService', '$rootScope', '$scope',  '$stateParams','$state','inform', 'Trans', 'AgreeConstant', 'comService', 'LocalCache',
        function (bugOnlineService, $rootScope, $scope, $stateParams,$state, inform, Trans, AgreeConstant,comService,LocalCache) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
    		$scope.limitList = AgreeConstant.limitList; // 正则校验配置
    		var flag = 0;
            $scope.getData = getData;
            //初始化页面信息
            initPages();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.whether = [{
                value: '0',
                label: '是'
            }, {
                value: '1',
                label: '否'
            }];
            $scope.num = ['0','1','2','3','4','5','6','7','8','9','10'];
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 210);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 65);
                var clientWidth = document.body.clientWidth;
                $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
            }
            
            function initPages() {
                //获取所有项目名称
                $scope.projectList = [];
                comService.getProjectsName().then(function (data) {
                    $scope.projectList = angular.fromJson(data.data);
                    flag++;
                    getData();
                });
                //获取员工信息
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function(data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                        flag++;
                        getData();
                    }
                });
              //bug状态
                $scope.bugStatus = {};
                $scope.bugStatusMap = [];
                comService.getParamList('BugStatus', 'BugOnline').then(function (data) {
                    if (data.data) {
                        $scope.bugStatus = data.data;
                        angular.forEach($scope.bugStatus, function (res, index) {
                            $scope.bugStatusMap[res.param_code] = res.param_value;
                        });
                        flag++;
                        getData();
                    }
                });
                //bug类型
                $scope.bugType = {};
                $scope.bugTypeMap = [];
                comService.getParamList('BugType', 'BugOnline').then(function (data) {
                    if (data.data) {
                        $scope.bugType = data.data;
                        angular.forEach($scope.bugType, function (res, index) {
                            $scope.bugTypeMap[res.param_code] = res.param_value;
                        });
                        flag++;
                        getData();
                    }
                });
                //bug级别
                $scope.bugSeverity = {};
                $scope.bugSeverityMap = [];
                comService.getParamList('BugSeverity', 'BugOnline').then(function (data) {
                    if (data.data) {
                        $scope.bugSeverity = data.data;
                        angular.forEach($scope.bugSeverity, function (res, index) {
                            $scope.bugSeverityMap[res.param_code] = res.param_value;
                        });
                        flag++;
                        getData();
                    }
                });
             }

            //获取所有数据以分页的形式
            function getData() {
            	if (flag !== 5){
            		return;
            	}
                var urlData = {
                    'id':  $stateParams.id,  			//项目
                };
                bugOnlineService.getBugOnlineDetailInfo(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                        	$scope.paramInfo = data.data;
                        	$scope.paramInfo.appearTime=($scope.paramInfo.appearTime == null ||$scope.paramInfo.appearTime === '')?'0':$scope.paramInfo.appearTime;
                        	document.getElementById("comment").innerHTML = $scope.paramInfo.comment;
                        	document.getElementById("steps").innerHTML = $scope.paramInfo.steps;
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            /**
             * 跳转至汇总
             */
            $scope.bugOnline = function () {
            	$state.go('app.office.bugOnlineController');
            }
            //更新线上bug信息
            $scope.adviceSynchronize = function () {
            	var itemList=[];
            	itemList.push($scope.paramInfo);
                bugOnlineService.updateBugOnlineInfo(itemList).then(function (data) {
                    layer.confirm(data.message,{
                        title:false,
                        btn:['确定']
                    },function(result){
                        layer.close(result);
                        $scope.bugOnline();
                    });
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });

            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
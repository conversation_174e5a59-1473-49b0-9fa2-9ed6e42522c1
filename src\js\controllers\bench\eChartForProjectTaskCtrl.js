(function () {
    'use strict';
    app.controller("eChartForProjectTaskCtrl", ['$scope', '$rootScope', 'inform', 'Trans','AgreeConstant',
        function ($scope, $rootScope, inform, Trans, AgreeConstant) {
    	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	//设置柱状图
		eChartForProjectTaskFun();
    	/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
        /**
         * 设置柱状图数据
         */
        function eChartForProjectTaskFun() {
        	$scope.eChartForProjectTaskType = $scope.eChartForProjectTaskType ? $scope.eChartForProjectTaskType : echarts.init(document.getElementById('eChartForProjectTask'));
        	$scope.barOption = {
				//标题
				title:{
					text:'项目任务信息统计',
					top:'bottom',
					left:'center',
					textStyle:{
						fontSize: 14,
						fontWeight: '',
						color: '#333'
					},
				},
				tooltip: {
					trigger: 'axis',
					axisPointer: {            // 坐标轴指示器，坐标轴触发有效
						type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				legend: {
					data: ['总计', '已完成', '剩余']
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},
				xAxis: [
					{
						type: 'value'
					}
				],
				yAxis: [
					{
						type: 'category',
						axisTick: {
							show: false
						},
						data: ['任务数（个）', 'bug数（个）']
					}
				],
				series: [
					{
						name: '总计',
						type: 'bar',
						label: {
							normal: {
								show: true,
								position: 'inside'
							}
						},
						data: [ 357, 220]
					},
					{
						name: '已完成',
						type: 'bar',
						stack: '总量',
						label: {
							normal: {
								show: true
							}
						},
						data: [ 346, 220]
					},
					{
						name: '剩余',
						type: 'bar',
						stack: '总量',
						label: {
							normal: {
								show: true,
								position: 'left'
							}
						},
						data: [ -11, ]
					}
				]
			};

			$scope.eChartForProjectTaskType.setOption($scope.barOption, true);
       }
       /**
        * 当窗体大小变化时，修改图例大小
        */
       window.addEventListener("resize", function () {
           if ($scope.eChartForProjectTaskType) { $scope.eChartForProjectTaskType.resize(); }
       });
       /**
		 * *************************************************************
		 *              方法声明部分                                 结束
		 * *************************************************************
		 */	
        }
    ]);
})();
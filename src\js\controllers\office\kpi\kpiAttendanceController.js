(function () {
    app.controller("kpiAttendanceController", ['$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'kpiAttendanceService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function ($rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, kpiAttendanceService, inform, Trans, AgreeConstant, LocalCache, $http) {
            init();
            function init() {
                $scope.formRefer = {};
                $scope.timeSelect=['上半年', '下半年', '本年度', '上一年度'];
                $scope.initTime = function (m){
                    initTime(m)
                };
                initTime('本年度');
            }
            $scope.resetParam = resetParam;
            function resetParam(){
                initTime('本年度');
            }

            function initTime(flag){
                $scope.butFlag = flag;
                var date = new Date();
                var y = date.getFullYear();
                date.setDate(1);
                if('上半年'===$scope.butFlag){
                    $scope.formRefer.startTime = y+'-02';
                    $scope.formRefer.endTime = y+'-07';
                }
                if('下半年'===$scope.butFlag){
                    $scope.formRefer.startTime = y+'-08';
                    $scope.formRefer.endTime = parseInt(y) + 1 +'-01';
                }
                if('本年度'===$scope.butFlag){
                    $scope.formRefer.startTime = y+'-01';
                    $scope.formRefer.endTime = y+'-12';
                }
                if('上一年度'===$scope.butFlag){
                    $scope.formRefer.startTime = parseInt(y) - 1+'-01';
                    $scope.formRefer.endTime = parseInt(y) - 1+'-12';
                }
            }

            $scope.getData = getData;
            initData();

            function initData() {
                //获取当前登录者的empId
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ? $scope.sessionEmpId : $stateParams.empId;
                //获取当前登录者
                $scope.formRefer.employeeName = LocalCache.getSession('employeeName');
                var person = LocalCache.getObject('personDataBoardEmployee');
                if(person.name){
                    $scope.formRefer.employeeName = person.name;
                }
                getData();
            }

            function getData() {
                getChart();
                getTableList();
            }

            window.addEventListener("resize", chartResize);
            $scope.$on("$destroy", function() {
                window.removeEventListener('resize', chartResize);
            })
            function chartResize() {
                if ($scope.currentChart) { $scope.currentChart.resize(); }
            }
            function getChart() {
                var urlData = {
                    "attendCycleStart": $scope.formRefer.startTime,
                    "attendCycleEnd": $scope.formRefer.endTime,
                    "empCode": $scope.formRefer.empId
                }
                kpiAttendanceService.getPersonAttendanceChartInfo(urlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        const chartInfoData = result.data;
                        $scope.currentChart = echarts.init(document.getElementById('attendanceChart'));
                        showChart($scope.currentChart, chartInfoData)
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            function showChart(currentChart,data){
                var xData=[];
                var yData=[];
                var option = {};
                if (data) {
                    angular.forEach(data.personalData, function(i) {
                        xData.push(parseFloat(i))
                    });
                    angular.forEach(data.departmentData, function(i) {
                        yData.push(parseFloat(i))
                    });
                    option = {
                        title: {
                            text: '考勤数据',
                            textStyle: {
                                fontSize: 13,
                                color: '#333'
                            }
                        },
                        tooltip: {
                            trigger: 'axis'
                        },
                        legend: {
                            left: '70',
                            data: [
                                '部门考勤数据',
                                '个人考勤数据'
                            ]
                        },
                        radar: [
                            {
                                indicator: [
                                    { text: '平日延时打卡工时' ,max:xData[0]>yData[0]?xData[0]:yData[0] ,min:xData[0]>yData[0]?yData[0]-2:xData[0]-2},
                                    { text: '总延时打卡工时' ,max:xData[1]>yData[1]?xData[1]:yData[1],min:xData[1]>yData[1]?yData[1]-2:xData[1]-2},
                                    { text: '平日工作强度',max:xData[2]>yData[2]?xData[2]:yData[2],min:xData[2]>yData[2]?yData[2]-2:xData[2]-2},
                                    { text: '总工作强度',max:xData[3]>yData[3]?xData[3]:yData[3],min:xData[3]>yData[3]?yData[3]-2:xData[3]-2},
                                    { text: '出勤率',max:xData[4]>yData[4]?xData[4]:yData[4],min:xData[4]>yData[4]?yData[4]-2:xData[4]-2}
                                ],
                                center: ['40%', '50%'],
                                radius: 80
                            }
                        ],
                        series: [
                            {
                                type: 'radar',
                                tooltip: {
                                    trigger: 'item'
                                },
                                data: [
                                    {
                                        value: xData,
                                        name: '个人考勤数据'
                                    },
                                    {
                                        value: yData,
                                        name: '部门考勤数据'
                                    }
                                ]
                            }
                        ]
                    };
                } else {
                    option = {
                        title: [{
                            text: '考勤数据',
                            textStyle:{
                                fontSize: 13,
                                color: '#333'
                            },
                        },{
                            text: "暂无数据",
                            left: 'center',
                            top: 'center',
                            color: '#333',
                            textStyle: {
                                fontSize: 20
                            }
                        }]
                    }
                }
                currentChart.setOption(option, true);
            }
            function getTableList() {
                var urlData = {
                    "attendCycleStart": $scope.formRefer.startTime,
                    "attendCycleEnd": $scope.formRefer.endTime,
                    "empCode": $scope.formRefer.empId
                }
                kpiAttendanceService.getPersonAttendanceList(urlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.attendanceTableList = result.data;
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            $scope.goback = function () {
                $state.go('app.office.personKpi');
            };
        }]);
})();

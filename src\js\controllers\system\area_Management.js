/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date:   2018-02-26 10:16:34
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-19 10:09:23
 */

(function() {
  'use strict';
  app.controller("area_Management", ['$rootScope', '$scope', 'inform', '$modal', 'Trans', 'SystemService', 'ConfigService','AgreeConstant',
    function($rootScope, $scope, inform, $modal, Trans, SystemService, ConfigService,AgreeConstant) {
      $scope.limitList = AgreeConstant.limitList; // 正则校验配置
      $scope.treeData = []; // 存放地域树数据
      $scope.parentId = AgreeConstant.treeRootNode; // 默认父节点
      $scope.getData = getData; // 初始化函数
      $scope.getData($scope.parentId); // 根据父节点获取数据
      $scope.popModal = popModal; // 修改弹框信息
      $scope.onSubmit = onSubmit; // 保存新增修改信息
      $scope.deleteItem = deleteItem; // 删除数据
      $scope.open = open; // 删除弹框

      // 排序
      $scope.title = 'regionId';
      $scope.order = order;

      // 左侧树配置
      var Leftsetting = angular.copy(ConfigService.dataAndCb);
      Leftsetting.callback.beforeClick = nodeSelect; // 点击节点前回调
      Leftsetting.callback.beforeExpand = zTreeBeforeExpand; // 展开节点前回调
      Leftsetting.callback.onExpand = zTreeOnExpand; // 展开节点回调
      Leftsetting.callback.onCollapse = zTreeOnCollapse; // 折叠节点回调

      // 设置侧边的高度,随窗口变动
      inform.autoHeight();
      window.onresize = inform.autoHeight;

      // 排序
      function order(str) {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      }

      // 获取地域数据
      function getData(parentId) {
        SystemService.getRegionListByParentId(parentId)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              if (data.result.length) {
                angular.forEach(data.result, function(res, index) {
                  var jsonTree = {
                    "id": res.regionId,
                    "pId": res.parentId,
                    "name": res.regionName,
                    "open": res.parentId==null ? true : false,
                    "isParent": res.hasChild
                  };
                  data.result[index] = angular.extend(jsonTree, res);
                  $scope.treeData.push(data.result[index]);
                });
                $scope.treeData = inform.unique($scope.treeData);
                $.fn.zTree.init($("#leftTree"), Leftsetting, $scope.treeData);
                $scope.result = data.result;
              }

            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 获取最后一层数据
      function isLastData(id) {
        SystemService.getRegion(id)
          .then(function(res) {
            if (res.code===AgreeConstant.resultCode) {
              $scope.result = [];
              $scope.result.push(res.result);
            } else {
              inform.common(res.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 获取选中树节点
      function nodeSelect(treeId, treeNode) {
        // getData(treeNode.regionId);
        isLastData(treeNode.regionId);
      }

      // 展开前数据获取操作
      function zTreeBeforeExpand(treeId, treeNode) {
        if (!treeNode.children) {
          getData(treeNode.regionId);
        }
      }

      // 展开操作
      function zTreeOnExpand(event, treeId, treeNode) {
        angular.forEach($scope.treeData, function(res, index) {
          if (res.regionId===treeNode.regionId) {
            res.open = true;
          }
        });
      }

      // 折叠操作
      function zTreeOnCollapse(event, treeId, treeNode) {
        angular.forEach($scope.treeData, function(res, index) {
          if (res.regionId===treeNode.regionId) {
            res.open = false;
          }
        });
      }

      // 修改弹框信息
      function popModal(item, str) {
        if (str) {
          $scope.parent = item;
          $scope.flag = false;
          $scope.perData = {
            regionName: "",
            regionCode: "",
            regionDesc: ""
          };
        } else {
          $scope.parent = null;
          $scope.flag = true;
          $scope.perData = angular.copy(item);
        }
      }

      // 保存新增修改信息
      function onSubmit() {
        if ($scope.parent) {
          $scope.perData.parentId = $scope.parent.regionId;
        }
        SystemService.saveOrupdateRegion($scope.perData)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              inform.common(Trans("tip.saveSuccess"));
              $('#edit_area').modal('hide');
              $scope.treeData = [];
              getData(AgreeConstant.treeRootNode);
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 删除数据
      function deleteItem(item) {
        SystemService.removeRegion(item.regionId)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              inform.common(Trans("tip.delSuccess"));
              $scope.treeData = [];
              getData(AgreeConstant.treeRootNode);
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 删除弹框
      function open(item) {
        var modalInstance = $modal.open({
          templateUrl: 'myModalContent.html',
          controller: 'ModalInstanceCtrl',
          size: "sm",
          resolve: {
            items: function() {
              return Trans("common.deleteTip");
            }
          }
        });

        modalInstance.result.then(function() {
          if (item) {
            // 根据ID 查询子节点数据
            SystemService.getRegionListByParentId(item.regionId)
              .then(function(res) {
                if (res.code===AgreeConstant.resultCode) {
                  // 最后一级
                  if (res.result.length===0) {
                    deleteItem(item);
                  } else {
                    inform.common(Trans("common.deleteChild"));
                  }

                }
              }, function() {
                inform.common(Trans("tip.requestError"));
              });
          }
        });
      }

    }
  ]);
})();
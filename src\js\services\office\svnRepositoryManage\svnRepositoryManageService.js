
(function() {
    'use strict';
  app.factory('svnRepositoryManageService', svnRepositoryManageService);
  svnRepositoryManageService.$inject=["HttpService",'$rootScope'];

  function svnRepositoryManageService(HttpService,$rootScope){
    var service={
      findRepositoryListByPage:findRepositoryListByPage,
		findStaffListByPage:findStaffListByPage,
		getHisData:getHisData,
	    loadRepositoryToExcel:loadRepositoryToExcel,
      deleteModuleEmployee:deleteModuleEmployee,
      callOutModuleEmployee:callOutModuleEmployee,
		dealStaff:dealStaff
    };
    return service;
    /**
	 * 获取根据条件查询出来的仓库的信息
	 */
	function findRepositoryListByPage(urlData) {
		// return HttpService.post($rootScope.getWaySystemApi+'svnRepository/selectRepositoryByMap',urlData);
		return HttpService.post($rootScope.getWaySystemApi+'storageManage/getStorageData',urlData);
	}

	/**
	 * 获取根据条件查询出来的调离人员的信息
	 */
	function findStaffListByPage(urlData) {
		// return HttpService.post($rootScope.getWaySystemApi+'svnRepository/selectRepositoryByMap',urlData);
		return HttpService.post($rootScope.getWaySystemApi+'storageManage/getStaffData',urlData);
	}

	  /**
	   * 获取根据条件查询出来的历史记录汇总的信息
	   */
	  function getHisData(urlData) {
		  // return HttpService.post($rootScope.getWaySystemApi+'svnRepository/selectRepositoryByMap',urlData);
		  return HttpService.post($rootScope.getWaySystemApi+'storageManagement/getHisData',urlData);
	  }

	/**
     * Excel导出
     */
    function loadRepositoryToExcel(urlData) {
    	return HttpService.get($rootScope.getWaySystemApi+'svnRepository/loadRepositoryToExcel',urlData);
     }

  /**
	 * 删除模块与人员关系
	 */
	function deleteModuleEmployee(urlData) {
		return HttpService.post($rootScope.getWaySystemApi+'svnModule/deleteModuleEmployee',urlData);
	}

  /**
	 * 调出模块与人员关系
	 */
	function callOutModuleEmployee(urlData) {
		return HttpService.post($rootScope.getWaySystemApi+'storageManage/operateStaff',urlData);
	}

	/**
	 * 调出模块与人员关系
	 */
	function dealStaff(urlData) {
		return HttpService.get($rootScope.getWaySystemApi+'storageManage/dealStaff',urlData);
	}



  }
})();

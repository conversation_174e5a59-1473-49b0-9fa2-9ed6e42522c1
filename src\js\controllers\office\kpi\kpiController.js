(function () {
    app.controller("kpiController", ['kpiService','$rootScope', '$state','$scope','$modal','inform','LocalCache','Trans','AgreeConstant','$http',
        function (kpiService,$rootScope,$state, $scope,$modal,inform,LocalCache,Trans,AgreeConstant,$http) {
     	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */ 
		$scope.formRefer ={
				startTime:'',//查询时间
		};
    	//表头重复14遍
    	$scope.secondTitle = [];
    	for(var i=0;i<14;i++){
	    		$scope.secondTitle.push("目标值");
	    		$scope.secondTitle.push("实际值");
	    		$scope.secondTitle.push("完成情况");
	    	}
		$scope.getData = getData; 			// 分页相关函数     
		$scope.rest = rest;
		rest();

		getData();		//在刷新页面时调用该方法

		setDivHeight();//设置列表的高度

		$(window).resize(setDivHeight);//窗体大小变化时重新计算高度
        $(window).resize(tryResetPage);
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
	  	
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */	


		//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
	    //设置列表的高度
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 180);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 65);
 		}

		//重置
		function rest() {
			$scope.formRefer.startTime=inform.format(new Date(),'yyyy-MM');//统计时间
		}
		
		//获取所有数据
		function getData(){
			var nowTime = inform.format(new Date(),'yyyy-MM');
			if ($scope.formRefer.startTime !== nowTime){
				getKpiHistory();
				return;
			}
		   	$scope.itemList = [];
        	var urlData ={
        		'startDate':inform.format($scope.formRefer.startTime,'yyyy-MM')
        	};
        	kpiService.getKpiByMap(urlData).then(function(data){
				if(data.code === AgreeConstant.code){
					var jsonData = data.data;
					$scope.itemList = jsonData;
				}
				$scope.itemList.sort(function(a,b){
                    // order是规则  objs是需要排序的数组
                    var order = ["平台开发研究室", "项目管理办公室","产品开发一室", "产品开发二室", "产品开发三室", "产品开发四室", "测试研究室",
                        "项目管理办公室","汇总"];
                    return order.indexOf(a.orgNAME) - order.indexOf(b.orgNAME);
                 });
                 setTimeout(tryResetPage,300);
        	},
        	function(error) {
				inform.common(Trans("tip.requestError"));
			});		
		}
		function tryResetPage(){
            $('#fixedLeftAndTop').DataTable( {
                //可被重新初始化
                retrieve:       true,
                //自适应高度
                scrollY:        'calc(100vh - 400px)',
                scrollX:        true,
                scrollCollapse: true,
                //控制每页显示
                paging:         false,
                //冻结列（默认冻结左1）
                fixedColumns:   {
                    leftColumns: 1
                },
                //search框显示
                searching:      false,
                //排序箭头
                ordering:       false,
                //底部统计数据
                info:           false
            } );
        }
		//获取查询月份的数据
		function getKpiHistory(){
		   	$scope.itemList = [];
        	var urlData ={
        		'startDate':inform.format($scope.formRefer.startTime,'yyyy-MM')
        	};
        	kpiService.getKpiHistory(urlData).then(function(data){
				if(data.code === AgreeConstant.code){
					$scope.itemList = data.data;
				}		
        	},
        	function(error) {
				inform.common(Trans("tip.requestError"));
			});		
		}

		//生成Excel表格
		$scope.toExcel = function() {
			var modalInstance = $modal.open({
				templateUrl: 'myModalContent.html',
	            controller: 'ModalInstanceCtrl',
	            size: "sm",
	            resolve: {
	                items: function() {
	                return "确定要下载吗！";
	                }
	            }
			});
	       	modalInstance.result.then(function() {
				//开启遮罩层
				inform.showLayer("下载中。。。。。。");
				$http.post(
				$rootScope.getWaySystemApi+'KPI/toExcel',
				{	
					'startDate':inform.format($scope.formRefer.startTime,'yyyy-MM')
				},
	    		 {headers: {
							'Content-Type': 'application/json',
							'Authorization':'Bearer ' + LocalCache.getSession("token")||''
						},
				  responseType: 'arraybuffer'//防止中文乱码
				 }
	    		 ).success(function(data){
	    			//如果是IE浏览器
	    			if (window.navigator && window.navigator.msSaveOrOpenBlob) {
	    				var csvData = new Blob([data], {type:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
	    				window.navigator.msSaveOrOpenBlob(csvData,'kpi数据统计汇总.xlsx');		   
	    			}
	    			//google或者火狐浏览器
	    			else{
	    				var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
						var objectUrl = URL.createObjectURL(blob);
						var aForExcel = $("<a download='kpi数据统计汇总.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href",objectUrl);
						$("body").append(aForExcel);
						$(".forExcel").click();
						aForExcel.remove();
	    			}

	    			// 关闭遮罩层
						inform.closeLayer();
						inform.common("下载成功!");
	    		});
		    });
        			
	    };

	     	/**
	  		 * *************************************************************
	  		 *              方法声明部分                                 结束
	  		 * *************************************************************
	  		 */	
	
	}]);
})();
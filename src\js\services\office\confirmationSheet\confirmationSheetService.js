
(function() {
    'use strict';
  app.factory('confirmationSheetService', confirmationSheetService);
  confirmationSheetService.$inject=["HttpService",'$rootScope'];

  function confirmationSheetService(HttpService,$rootScope){

    var service={
        getTestReportList:getTestReportList,
        getConfirmationSheetInfo:getConfirmationSheetInfo,
        getRepositoryList:getRepositoryList,
        getCurrentRepositoryConfig:getCurrentRepositoryConfig,
        saveConfirmSheet:saveConfirmSheet,
        refreshProjectRepository:refreshProjectRepository,
        startOaProcess:startOaProcess,
        updateAfterSuccess:updateAfterSuccess
    };
    return service;

      /**
       * 发起oa流程
       */
      function startOaProcess(urlData) {
          return HttpService.post($rootScope.getWaySystemApi  + 'confirmAction/startOaProcess', urlData);
      }
    /**
     * 分页查询公司立项项目工时统计
     */
    function getTestReportList(urlData) {
        return HttpService.post($rootScope.getWaySystemApi  + 'confirmAction/getTestReport', urlData);
    }

    /**
     * 获取确认单详情
     */
    function getConfirmationSheetInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'confirmAction/getConfirmSheet', urlData);
    }
    //获取所有仓库集合
    function getRepositoryList(){
        return HttpService.post($rootScope.getWaySystemApi + 'confirmAction/getConfigList');
    }
    //查询当前仓库下的配置项集合
    function getCurrentRepositoryConfig(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'confirmAction/selectProjectRepository', urlData);
    }
    //保存确认单信息
    function saveConfirmSheet(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'confirmAction/saveConfirmSheet', urlData);
    }
    //刷新仓库下的标签
    function refreshProjectRepository(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'confirmAction/refreshProjectRepository', urlData);
    }
    //当成功时修改保存
    function updateAfterSuccess(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'confirmAction/updateAfterSuccess', urlData);
    }
  }
})();

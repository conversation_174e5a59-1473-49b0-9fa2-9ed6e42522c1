(function() {
  'use strict';
  app.controller("service-state", ['$interval', '$rootScope', '$scope', '$timeout', 'inform', '$stateParams', 'Microservice', 'Trans',
    function($interval, $rootScope, $scope, $timeout, inform, $stateParams, Microservice, Trans) {

      $scope.getRoutes = getRoutes;
      $scope.getHealth = getHealth;

      $scope.getLabelClass = getLabelClass;
      $scope.baseName = baseName;
      $scope.subSystemName = subSystemName;
      $scope.changeRouteInfo = changeRouteInfo;
      $scope.getRoutes();
      var separator = '.';
      $scope.serviceStateTimer = 0;
      $scope.appName = "register";
      $scope.result = [{ "appName": "register" }];

      $scope.$watch('$scope.serviceStateTimer', function(newValue, oldValue) {
        if (newValue !== oldValue) {
          $interval.cancel($scope.interval);
          if (newValue !== '0') {
            $scope.interval = $interval(function() {
              // console.log($scope.timer);
              $scope.getRoutes();
              $scope.getHealth();
            }, $scope.serviceStateTimer);
          }
        }
      });

      function changeRouteInfo() {
        getHealth();
      }
      $scope.$on('$destroy', function() {
        $interval.cancel($scope.interval);
        //$interval.stop(interval);
      });


      // 获取应用
      function getRoutes() {
        Microservice.getRoutes()
          .then(function(data) {
            for (var i = 0; i < data.length; i++) {
              $scope.result.push(data[i]);
            }

            console.log(data);
            $scope.getHealth();
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      function getHealth() {
        Microservice.getHealth($scope.appName)
          .then(function(data) { //得到后台数据
            $scope.healthData = transformHealthData(data);
            console.log($scope.healthData);
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      function baseName(name) {
        if (name) {
          var split = name.split('.');
          return split[0];
        }
      }

      function transformHealthData(data) { //对后台得到的数据进行转换
        var response = [];
        flattenHealthData(response, null, data);
        return response;
      }

      function flattenHealthData(result, path, data) {
        for (var key in data) {
          if (data.hasOwnProperty(key)) {
            var value = data[key];
            if (isHealthObject(value)) {
              if (hasSubSystem(value)) {
                addHealthObject(result, false, value, getModuleName(path, key));
                flattenHealthData(result, getModuleName(path, key), value);
              } else {
                addHealthObject(result, true, value, getModuleName(path, key));
              }
            }
          }
        }
        return result;
      }

      function isHealthObject(healthObject) {
        var result = false;
        for (var key in healthObject) {
          if (healthObject.hasOwnProperty(key)) {
            if (key === 'status') {
              result = true;
            }
          }
        }
        return result;
      }

      function hasSubSystem(healthObject) {
        var result = false;
        for (var key in healthObject) {
          if (healthObject.hasOwnProperty(key)) {
            var value = healthObject[key];
            if (value && value.status) {
              result = true;
            }
          }
        }
        return result;
      }

      function addHealthObject(result, isLeaf, healthObject, name) {
        var healthData = {
          name: name
        };
        var details = {};
        var hasDetails = false;
        for (var key in healthObject) {
          if (healthObject.hasOwnProperty(key)) {
            var value = healthObject[key];
            if (key === 'status' || key === 'error') {
              healthData[key] = value;
            } else {
              if (!isHealthObject(value)) {
                details[key] = value;
                hasDetails = true;
              }
            }
          }
        }
        // Add the details
        if (hasDetails) {
          healthData.details = details;
        }
        // Only add nodes if they provide additional information
        if (isLeaf || hasDetails || healthData.error) {
          result.push(healthData);
        }
        return healthData;
      }

      function getModuleName(path, name) {
        var result;
        if (path && name) {
          result = path + separator + name;
        } else if (path) {
          result = path;
        } else if (name) {
          result = name;
        } else {
          result = '';
        }
        return result;
      }

      function subSystemName(name) {
        if (name) {
          var split = name.split('.');
          split.splice(0, 1);
          var remainder = split.join('.');
          return remainder ? ' - ' + remainder : '';
        }
      }

      function getLabelClass(statusState) {
        if (statusState === 'UP') {
          return 'label-success';
        } else {
          return 'label-danger';
        }
      }
    }
  ]);
})();
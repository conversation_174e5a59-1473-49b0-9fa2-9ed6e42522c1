(function () {
    app.controller("place_Management", ['$rootScope', '$scope', '$http', '$stateParams', 'inform',
        function ($rootScope, $scope, $http, $stateParams, inform) {

            $scope.star = "";//开始条数
            $scope.end = "";//结束条数
            $scope.size = "10";//每页条数
            $scope.page = 1;//默认页 传递时 需要-1
            $scope.maxSize = 3;//默认个数
            $scope.totalElements = "";//总体条数

            $scope.title = 'id';
            $scope.desc = 0;
            $scope.key = '';
            $scope.machineData =[
            {"id":1,"lineName":"xiao","areaName":"haidian","containerType":"controller1","manufacturer":11},
            {"id":2,"lineName":"zhang","areaName":"chaoyang","containerType":"controller2","manufacturer":12},
            {"id":3,"lineName":"li","areaName":"changping","containerType":"controller3","manufacturer":14},
            {"id":4,"lineName":"wang","areaName":"dongcheng","containerType":"controller4","manufacturer":14},
            {"id":5,"lineName":"fu","areaName":"xicheng","containerType":"controller5","manufacturer":15},
            {"id":6,"lineName":"lv","areaName":"tongzhou","containerType":"controller6","manufacturer":16},
            {"id":7,"lineName":"lv","areaName":"tongzhou","containerType":"controller6","manufacturer":16},
            {"id":8,"lineName":"lv","areaName":"tongzhou","containerType":"controller6","manufacturer":16},
            {"id":9,"lineName":"lv","areaName":"tongzhou","containerType":"controller6","manufacturer":16},
            {"id":10,"lineName":"lv","areaName":"tongzhou","containerType":"controller6","manufacturer":16},
            {"id":11,"lineName":"lv","areaName":"tongzhou","containerType":"controller6","manufacturer":16},
            {"id":12,"lineName":"lv","areaName":"tongzhou","containerType":"controller6","manufacturer":16},
            ];
            $scope.change = function(){
                // vendingmachine.vemsPagePageSizeSizeGet($scope.page - 1, $scope.size)
                //     .success(function (data) {
                //         $scope.machineData = data.body.content;
                //         $scope.totalElements = data.body.pageInfo.totalElements;
                //         $scope.star = data.body.pageInfo.number * data.body.pageInfo.size + 1;
                //         $scope.end = (data.body.pageInfo.number + 1) * data.body.pageInfo.size;
                //     });
            };
        }]);
})();

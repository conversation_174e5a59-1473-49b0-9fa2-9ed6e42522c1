//# sourceURL=js/controllers/office/projectSupport/projectSupportController.js
(function() {
    app.controller("projectSupportController", ['projectSupportService', '$state', 'comService', '$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$stateParams', '$modal', '$http', '$state',
        function(projectSupportService, state, comService, $rootScope, $scope, inform, Trans, AgreeConstant, LocalCache, $stateParams, $modal, $http, $state) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
             // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //页面分页信息
            $scope.pages = {
                pageNum: '', //分页页数
                size: '100', //分页每页大小
                total: '' //数据总数
            };
            $scope.supportItemDownloadFlagMap = {
                "0":'下载',
                "1":'不下载'
            };
            //初始化页面信息
            getData(1);
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 130);
                $("#divTBDis3").height(divHeight);
                $("#subDivTBDis3").height(divHeight - 50);
            }
            /**
             * 获取需支援事项信息
             *
             */
            function getData(pages) {
                var urlData = {
                    'projectId': $stateParams.projectId,
                    'page': pages,
                    'pageSize': $scope.pages.size
                };
                projectSupportService.getProjectSupportInfo(urlData).then(function(data) {
                    if (data.code === '0000') {
                        for (var i = 0; i < data.data.list.length; i++) {
                            if (data.data.list[i].timeActualStart !== null && data.data.list[i].timeActualEnd !== null) {
                               data.data.list[i].timeActualStart = data.data.list[i].timeActualStart + "~" + data.data.list[i].timeActualEnd; //实际支援时间
                            }else if(data.data.list[i].timeActualStart==null){
                                data.data.list[i].timeActualStart = data.data.list[i].timeActualEnd; //实际支援时间
                            }
                          data.data.list[i].timeRequiredStart = data.data.list[i].timeRequiredStart + "~" + data.data.list[i].timeRequiredEnd; //要求支援时间
                        }
                        $scope.projectSupportInfo = data.data.list;
                        // 分页信息设置
                        $scope.pages.total = data.data.total;
                        $scope.pages.star = data.data.startRow;
                        $scope.pages.end = data.data.endRow;
                        $scope.pages.pageNum = data.data.pageNum;
                        //设置列表的高度
                        setDivHeight();
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 跳转修改
             */
            $scope.go = function(item) {
                if (null ==item) {
                    $state.go("app.office.projectSupportAddController", {
                        projectId: $stateParams.projectId
                    });
                } else {
                    $state.go("app.office.projectSupportAddController", {
                        item: item.id,
                        projectId: $stateParams.projectId
                    });
                }
            };
            /**
             * 删除弹框
             *
             */
            $scope.open = function(item) {
                inform.modalInstance("确定要删除吗?").result.then(function() {
                    $scope.deleteProjectSupportInfo(item);
                });
            };
            /**
             * 删除数据 
             * 
             */
            $scope.deleteProjectSupportInfo = function(item) {
                projectSupportService.deleteProjectSupportInfo(item).then(function(data) {
                    if (data.code === "0000") {
                        inform.common(Trans("tip.delSuccess"));
                        getData(1);
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            };
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        }
    ]);
})();
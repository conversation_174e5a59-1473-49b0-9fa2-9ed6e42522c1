
(function() {
    'use strict';
  app.factory('teamEntranceDetailsService', teamEntranceDetailsService);
  teamEntranceDetailsService.$inject=["HttpService",'$rootScope'];

  function teamEntranceDetailsService(HttpService,$rootScope){

    var service={
        selectIterationList:selectIterationList,
        selectCurrentIterationBug:selectCurrentIterationBug,
        selectHistoryIterationBug:selectHistoryIterationBug,
        selectCurrentIterationInfo:selectCurrentIterationInfo,
        getOnlineBug:getOnlineBug,
        getOneYearInfo:getOneYearInfo,
        getKpiInfo:getKpiInfo,
        getHistoryBugDetail:getHistoryBugDetail,
        getReopenBugDetail:getReopenBugDetail,
        getJenkinsData:getJenkinsData,
        getBuildData:getBuildData,
        getJenkinsStatistic:getJenkinsStatistic,
        getSonarData:getSonarData,
        getSonarDataStatistic:getSonarDataStatistic,
        getCodeDataList:getCodeDataList,
        getCodeDataStatistic:getCodeDataStatistic,
        getRepoCommitData:getRepoCommitData
    };
    return service;

    /**
     * 获取当前团队的迭代版本集合
     */
    function selectIterationList(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'teamBoard/selectIterationList', urlData);
    }
    //当前迭代版本的每日bug情况
    function selectCurrentIterationBug(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'teamBoard/selectCurrentIterationBug', urlData);
    }
    //历史迭代版本的bug情况
    function selectHistoryIterationBug(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'teamBoard/selectHistoryIterationBug', urlData);
    }
    //当前迭代概况
    function selectCurrentIterationInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'teamBoard/selectCurrentIterationInfo', urlData);
    }
    //每月线上bug情况
    function getOnlineBug(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'teamBoard/statistic_onlinebug_bymonth', urlData);
    }
    //一年内团队概况
    function getOneYearInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'teamBoard/get_basic_info', urlData);
    }
    //团队kpi信息
    function getKpiInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'teamBoard/get_kpi_info', urlData);
    }
    //历史迭代bug情况页签内容
    function getHistoryBugDetail(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'teamBoard/selectHistoryIterationBugDetail', urlData);
    }
    //获取reopen详情页签内容
    function getReopenBugDetail(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'teamBoard/selectReopenInfo', urlData);
    }

     /**
     * 获取当前团队的jenkins构建数据
     */
     function getJenkinsData(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'jenkinsData/getJenkinsData', urlData);
    }

    /**
     * 获取流水线构建详情
     */
    function getBuildData(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'jenkinsData/getBuildData', urlData);
    }

    /**
     * 获取jenkins统计数据
     */
    function getJenkinsStatistic(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'jenkinsData/getJenkinsStatistic', urlData);
    }

    /**
     * 获取sonar数据
     */
    function getSonarData(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'sonarData/getSonarData', urlData);
    }

    /**
     * 获取sonar统计数据
     */
    function getSonarDataStatistic(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'sonarData/getSonarStatistic', urlData);
    }

    /**
     * 获取code数据
     */
    function getCodeDataList(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getCommitListOfProject', urlData);
    }

    /**
     * 获取code统计数据
     */
    function getCodeDataStatistic(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getCommitStatisticOfProject', urlData);
    }
    
     /**
     * 获取仓库code提交明细
     */
     function getRepoCommitData(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getCommitDetailOfRepo', urlData);
    }
    

  }
})();

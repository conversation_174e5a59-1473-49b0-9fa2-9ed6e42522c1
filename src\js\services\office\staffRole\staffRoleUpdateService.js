
(function() {
    'use strict';
  app.factory('staffRoleUpdateService', staffRoleUpdateService);
  staffRoleUpdateService.$inject=["HttpService",'$rootScope'];

  function staffRoleUpdateService(HttpService,$rootScope){
    var service={
      saveStaffRole:saveStaffRole
    };
    return service;	
    /**
	 * 获取根据条件查询出来的仓库的信息
	 */
	function saveStaffRole(urlData) {
		return HttpService.post($rootScope.getWaySystemApi+'staffRole/saveStaffRole',urlData);
	}
	
	
  }
})();

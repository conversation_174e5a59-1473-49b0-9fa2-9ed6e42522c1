(function () {
    app.controller("customerDataManagement", ['comService', '$rootScope', '$scope', '$modal', 'customerDataService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, $modal, customerDataService, inform, Trans, AgreeConstant, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置

            $scope.updateEmployeename = LocalCache.getSession('currentUserName');   //获取当前的登录用户名
            $scope.loginId = LocalCache.getSession('userId');  //获取当前登录者的id
            $scope.formInput = {
                department:'',         //部门
                productLine: '',   //产品线id
                documentTitle:'',       //plm主题
                finishState: '',		  //完成情况
                documentId: '',		  //文档编号
                upgradeId:''         //plm升级编号
            };

            $scope.formInsert = {
                creator: []
            };
            //完成状态选择
            $scope.finishStateSelect = ['按时完成','延期完成','延期未完成','未到期','临期','退回市场','退回产品经理','不涉及系研','尚未制定计划'];
            //初始化
            $scope.formInsert = {};

            $scope.pages = {
                pageNum: '', 		// 分页页数
                size: '', 			// 分页每页大小
                total: '' 			// 数据总数
            };
            $scope.pages = inform.initPages(); 	// 初始化分页数据
            $scope.getData = getData; 			// 分页相关函数
            $scope.taskList = [];				// 保存所有信息的集合
            getData($scope.pages.pageNum);		//在刷新页面时调用该方法

            $scope.currentTime = getPreMonth(inform.format(new Date(), 'yyyy-MM-dd'));	    //初始化上传时间
            $scope.updateInfo = updateInfo;     // 修改一条信息
            setCurrentDate();//设置当前时间
            $scope.startTime = '';//下载工时时间
            $scope.endTime = '';
            $scope.timeOld = '';

            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //初始化页面信息
            initPages();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 190);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 70);
            }

            //获得当前时间（yyyy-MM）
            function setCurrentDate() {
                $scope.currentDate = $scope.currentDate ? null : inform.format(new Date(), "yyyy-MM");
            }


            //时间选择
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;
                $scope.develReceiveTime = false;
                $scope.setPlanFinishTime = false;
                $scope.planFinishTime = false;
                $scope.demandSolutionTime = false;
                $scope.relFinishTime = false;
                $scope.timeDemand = false;
            };
            $scope.openDateStart1 = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.develReceiveTime = true;
                $scope.setPlanFinishTime = false;
                $scope.planFinishTime = false;
                $scope.demandSolutionTime = false;
                $scope.relFinishTime = false;
                $scope.timeDemand = false;
            };

            $scope.openDateStart2 = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.develReceiveTime = false;
                $scope.setPlanFinishTime = true;
                $scope.planFinishTime = false;
                $scope.demandSolutionTime = false;
                $scope.relFinishTime = false;
                $scope.timeDemand = false;
            };
            $scope.openDateStart3 = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.develReceiveTime = false;
                $scope.setPlanFinishTime = false;
                $scope.planFinishTime = true;
                $scope.demandSolutionTime = false;
                $scope.relFinishTime = false;
                $scope.timeDemand = false;
            };
            $scope.openDateStart4 = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.develReceiveTime = false;
                $scope.setPlanFinishTime = false;
                $scope.planFinishTime = false;
                $scope.demandSolutionTime = true;
                $scope.relFinishTime = false;
                $scope.timeDemand = false;
            };
            $scope.openDateStart5 = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.develReceiveTime = false;
                $scope.setPlanFinishTime = false;
                $scope.planFinishTime = false;
                $scope.demandSolutionTime = false;
                $scope.relFinishTime = true;
                $scope.timeDemand = false;
            };
            $scope.openDateStart6 = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.develReceiveTime = false;
                $scope.setPlanFinishTime = false;
                $scope.planFinishTime = false;
                $scope.demandSolutionTime = false;
                $scope.relFinishTime = false;
                $scope.timeDemand = true;
            };

            /**
             * 获取上一个月
             *
             * @date 格式为yyyy-mm-dd的日期，
             */
            function getPreMonth(date) {
                var arr = date.split('-');
                var year = arr[0]; //获取当前日期的年份
                var month = arr[1]; //获取当前日期的月份
                var year2 = year;
                var month2 = parseInt(month) - 1;
                if (month2 === 0) {//如果是1月份，则取上一年的12月份
                    year2 = parseInt(year2) - 1;
                    month2 = 12;
                }
                if (month2 < 10) {
                    month2 = '0' + month2;//月份填补成2位。
                }
                return year2 + '-' + month2;
            }


            //重置
            $scope.rest = function () {
                $scope.formInput.department = '';
                $scope.formInput.productLine = '';
                $scope.formInput.documentTitle = '';
                $scope.formInput.documentId = '';
                $scope.formInput.finishState = '';
                $scope.formInput.upgradeId = '';
                $scope.uploadTime = getPreMonth(inform.format(new Date(), 'yyyy-MM-dd'));
                $("#form")[0].reset();
                $("#fileNameDis").text("");
            };


            function initPages() {

                //获取产品线
                $scope.productLineList = [];
                $scope.productLineListForUpdate = [];
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.productLineListForUpdate = JSON.parse(JSON.stringify(data.data));
                        $scope.productLineList = data.data;
                        var emptyProductLine = {"param_code":"无","param_value":"无","param_desc":"无"};
                        $scope.productLineList.push(emptyProductLine);
                    }
                });

                $scope.employeeList = [];
                comService.getEmployeesName()
                    .then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.employeeList = data.data;
                        }
                    });
				//获取部门
				$scope.departmentList = [];
				$scope.departmentListForUpdate = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentListForUpdate = JSON.parse(JSON.stringify(data.data));
                    $scope.departmentList = comService.getDepartment(data.data);
                    var emptyDepartmentLine = {"orgCode":"无","orgName":"无","orgId":"无"};
                    $scope.departmentList.push(emptyDepartmentLine);
                });
            }

            //单选select触发修改时调用的方法,item存在则为修改
            $scope.selectEmployee = function (item) {
                if (!item) {
                    if (!$scope.formInsert.creator) {
                        return;
                    }
                    //已经在弹窗弹出时做了处理，可以省略
                    if (!$scope.formInsert.creator) {
                        $scope.formInsert.creator = [];
                    }
                    if ($scope.formInsert.creator.indexOf($scope.formInsert.creator) === -1) {
                        $scope.formInsert.creator.push($scope.formInsert.creator);
                    }
                } else {
                    if (!$scope.changeParam.creator) {
                        return;
                    }
                    //已经在弹窗弹出时做了处理，可以省略
                    if (!$scope.changeParam.creator) {
                        $scope.changeParam.creator = [];
                    }
                    if ($scope.changeParam.creator.indexOf($scope.changeParam.creator) === -1) {
                        $scope.changeParam.creator.push($scope.changeParam.creator);
                    }
                }
            };


            /**
             * 页面选中的修改信息复制
             */
            $scope.update = function (m) {

                $scope.changeParam = angular.copy(m);
            };

            //修改
            function updateInfo(changeParam) {
                $scope.addList = [];
                if (!changeParam.documentId) {
                    inform.common(Trans("文档编号不能为空！"));
                    return false;
                } else if (!changeParam.upgradeId) {
                    inform.common(Trans("请求升级编号不能为空！"));
                    return false;
                } else if (!changeParam.productLine) {
                    inform.common(Trans("产品线不能为空！"));
                    return false;
                }else if (!changeParam.department) {
                    inform.common(Trans("部门不能为空！"));
                    return false;
                } else {
                    var urlData = {
                    //    'id': changeParam.id,
                        'documentId': changeParam.documentId,//文档编号
                        'upgradeId': changeParam.upgradeId,//请求升级编号
                        'setPlanFinishTime': changeParam.setPlanFinishTime,//计划制定完成时间
                        'planFinishTime': inform.format(changeParam.planFinishTime, 'yyyy-MM-dd'),//计划完成时间
                        'demandSolutionTime': inform.format(changeParam.demandSolutionTime, 'yyyy-MM-dd'),//需求处理完成时间
                        'remarks': changeParam.remarks,//备注
                        'relFinishTime': inform.format(changeParam.relFinishTime, 'yyyy-MM-dd'),//实际完成时间
                        'productLine': changeParam.productLine,//产品线id
                        'productLineName': changeParam.productLineName,//产品线名称
                        'department': changeParam.department,//部门id
//	    			 'timeDemand':changeParam.timeDemand,//时间要求
                        'timeDemandOld': changeParam.timeDemandOld,//时间要求
                        'projectManager': changeParam.projectManager,//项目经理
                        'managerResCycle': changeParam.managerResCycle,//项目经理响应周期
                        'develResCycle': changeParam.develResCycle,//开发代表响应周期
                        'managerTimeLiness': changeParam.managerTimeLiness,//项目经理响应及时性
                        'develTimeLiness': changeParam.develTimeLiness,//开发代表响应及时性
                        'intrvalTime': changeParam.intrvalTime,//间隔时间
                        'creator':changeParam.creator,//创建者
                        'managerReceiveTime': changeParam.managerReceiveTime,//项目经理接收时间
                        'createDate': inform.format(changeParam.createDate, 'yyyy-MM-dd'),//创建时间
                        'currentProcessor':changeParam.currentProcessor,//当前处理人
                        'finishState': changeParam.finishState//完成情况
                    };

                    customerDataService.updateMessage(urlData).then(function (data) {
                            if (data.code === AgreeConstant.code) {
                                $("#edit_modal").modal('hide');
                                inform.common(Trans("信息修改成功！"));
                            } else {
                                $("#edit_modal").modal('hide');
                                inform.common(Trans("信息修改失败！"));
                            }
                            getData(1);
                            $scope.changeParam = {};
                        },
                        function (error) {
                            inform.common(Trans("tip.requestError"));
                        });
                }
            }


            //获取所有数据以分页的形式
            function getData(pageNum) {
                $scope.itemList = [];
                var stats = '';
                angular.forEach($scope.formInput.finishState, function (finishState, i) {
                    stats = stats +finishState +',';
                });
                var urlData = {
                    'department':$scope.formInput.department,//部门
                    'productLine': $scope.formInput.productLine,  			//产品线名称
                    'documentTitle':$scope.formInput.documentTitle, //plm主题
                    'documentId': $scope.formInput.documentId,
                    'finishState': stats,
                    'upgradeId':$scope.formInput.upgradeId,
                    'fromFlag':'0',
                    'currentPage': pageNum, 								// 分页页数
                    'pageSize': $scope.pages.size    						// 分页每页大小
                };
                customerDataService.getAllMessage(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            var jsonData = data.data;
                            $scope.itemList = jsonData.list;
                            if ($scope.itemList.length === 0) {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages(); 			//初始化分页数据
                            } else {
                                // 分页信息设置
                                $scope.pages.total = jsonData.total;		// 页面总数
                                $scope.pages.star = jsonData.startRow;  	//页面起始数
                                $scope.pages.end = jsonData.endRow;  		//页面大小数
                                $scope.pages.pageNum = jsonData.pageNum;  	//页面页数
                            }

                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            $scope.changeStats = function(){
                if($scope.changeParam.finishState === '退回市场' || $scope.changeParam.finishState === '退回产品经理'){
                    $scope.timeOld = inform.format($scope.changeParam.demandSolutionTime, 'yyyy-MM-dd');
                    $scope.changeParam.demandSolutionTime = '';
                }else {
                    $scope.changeParam.demandSolutionTime = $scope.timeOld;
                }
                //弹框提示
                inform.common("按时完成、延期完成、未到期、临期、延期未完成均通过系统计算得出，无法修改；退回市场、退回产品经理默认将’需求处理完成时间‘置空");
            }
            // 修改信息弹框，str存在，就是新增
       /*     $scope.popModalInfo = function (item, str) {
                if (str) {
                    $scope.spec = {
                        'affiliatedGroup': '',
                        'target': ''
                    };
                } else {
                    $scope.changeParam = angular.copy(item);
                }
            };*/
            // 修改信息弹框，str存在，就是新增
/*            $scope.popModalDetail = function (item, str) {
                if (!str) {
                    $scope.changeParam = angular.copy(item);
                    //移除participants最后的“，”,并将其转化为数组，由于新增时做了校验，不可能为空
                    var creator = item.creator.slice(0, item.creator.length - 1);
                    $scope.changeParam.creator = creator.split(',');

                } else {
                    //新增时，将其声明为数组
                    $scope.formInsert = {
                        creator: []
                    };
                }
            };*/

            /**
             * 查询不符合命名规范的PLM客户需求
             */
            $scope.selectNconsistentNamingSpecification = function () {
                customerDataService.selectNconsistentNamingSpecification().then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        //不符合命名规范的PLM客户需求
                        $scope.nconsistentNamingSpecificationData = data.data;
                        if ($scope.nconsistentNamingSpecificationData.length === 0) {
                            inform.common(Trans("没有不符合命名规范的客户需求"));
                            return;
                        }
                        $("#nconsistentNamingSpecification_modal").modal('show');
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
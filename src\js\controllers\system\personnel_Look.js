/*
 * @Author: fubaole
 * @Date:   2018-01-18 15:31:59
 * @Last Modified by:   haoh<PERSON><PERSON>
 * @Last Modified time: 2018-02-28 11:57:42
 */

(function() {
    'use strict';
    app.controller("personnel_Look", ['$rootScope', '$scope', '$state', '$stateParams', 'inform', 'Trans', 'SystemService', 'AgreeConstant',
        function($rootScope, $scope, $state, $stateParams, inform, Trans, SystemService, AgreeConstant) {

            getData(); // 获取人员信息

            // 根据ID 获取人员信息
            function getData() {
                SystemService.getEmployeeById($stateParams.employeeId)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.person = data.result;
                        } else {
                            inform.common(data.message);
                        }
                    }, function() {
                        inform.common(Trans("tip.requestError"));
                    });
            }
        }
    ]);
})();
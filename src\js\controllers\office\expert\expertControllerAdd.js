(function () {
    app.controller("expertControllerAdd", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','expertService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,inform,Trans,AgreeConstant,LocalCache,$http) {

		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		//设置列表的高度
        setDivHeight();
        $scope.changeParam={};
        var flag = false;//判断chexbox初始化是否完成

        $(window).resize(setDivHeight);//窗体大小变化时重新计算高度
        initPerson();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
        /**
         * 设置列表的高度
         */
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (150 + 185);
            $("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight - 50);
        }
        /**
         * 初始化信息
         */
    	function initPerson() {
    		//获取员工信息
            $scope.employeeList = [];
            comService.getEmployeesByOrgId('').then(function(data) {
                if (data.data) {
                    $scope.employeeList = data.data;
                    flag++;
                    setValue();
                }
            });
            //获取行业领域
            $scope.dataList = [];
            expertService.getTerritoryList().then(function(data) {
            	 if (data.code === AgreeConstant.code) {
            		 $scope.dataList = data.data;
            		 flag++;
         			 setValue();
            	 } else {
                     inform.common(data.message);
                 }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });

    	}
    	/**
         * 全选函数
         */
    	$scope.selectAll = function() {
    		var boxes = document.getElementsByName("experts");
            if ($scope.select_all) {
            	//获取页面所有领域
				for(var i=0;i<boxes.length;i++){
					boxes[i].checked = true;
				}
            } else {
            	//获取页面所有领域
				for(var j=0;j<boxes.length;j++){
					boxes[j].checked = false;
				}
            }
        }
    	/**
    	 * 若为更新 则赋值
    	 */
    	function setValue() {
    		//初始化未完成
    		if (flag!==2){
    			return;
    		}
    		if ($stateParams.item==null){
    			$scope.type = "add";
    			$scope.bigName = "新增人才信息";
    			return;
    		}
    		$scope.type = "up";
    		$scope.bigName = "编辑人才信息";
    		$scope.changeParam.name = $stateParams.item;
    		var urlData = {
        		'employeeId':$scope.changeParam.name,
        	}
    		expertService.selectOne(urlData).then(function(data) {
    			 if (data.code === AgreeConstant.code) {
    				var boxes = document.getElementsByName("experts");
    				//获取页面所有领域
    				for(var i=0;i<boxes.length;i++){
    					//据有的领域
    					for(var j=0;j<data.data.length;j++){
    						if (boxes[i].value === data.data[j]){
    							boxes[i].checked = true;
    						}
    					}
    				}
    				//名字不可修改
    				$("#name").prop('disabled',true).trigger("chosen:updated");
    			 } else {
                     inform.common(data.message);
                 }
    		}, function (error) {
                inform.common(Trans("tip.requestError"));
            });
    	 }
    	 /**
    	  * 保存信息
    	  */
    	 $scope.saveInfo = function () {
    		 var boxes = document.getElementsByName("experts");
    		 var details = [];
    		 angular.forEach(boxes, function (detail, i) {
    			 if (boxes[i].checked){
    				 details.push(boxes[i].value);
    			 }
    		 });
    		 if(details.length===0){
    			 inform.common("请选择行业领域");
    			 return;
    		 }
    		 var urlData = {
    			'employeeId':$scope.changeParam.name,
    			'territoryDetail':details
    		 }
             //新增
             if ($stateParams.item==null){
            	 addInfo(urlData);
             } else {
            	 //修改
            	 upInfo(urlData)
        	 }
         };
         /**
          * 添加信息
          */
         function addInfo(urlData) {
        	 expertService.addInfo(urlData).then(function (data) {
                 callbackFunction(data);
             }, function (error) {
                 inform.common(Trans("tip.requestError"));
             });
          }

        function callbackFunction(data){
            if (data.code === AgreeConstant.code) {
               layer.confirm(data.message,{
                   title:false,
                   btn:['确定']
               },function(result){
                   layer.close(result);
                   $scope.goback();
               });
            } else {
               inform.common(data.message);
            }

        }


          /**
           * 修改信息
           * @param urlData
           */
          function upInfo(urlData) {
        	  expertService.upInfo(urlData).then(function (data) {
                  callbackFunction(data);
              }, function (error) {
                  inform.common(Trans("tip.requestError"));
              });
           }
          /**
           * 返回
           */
        $scope.goback=function () {
     		$state.go("app.office.expertController",null);
         }
	 		/**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */

	}]);
})();

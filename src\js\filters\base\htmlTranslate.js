/*
* @Author: fubaole
* @Date:   2017-11-02 09:34:13
* @Last Modified by:   fubaole
* @Last Modified time: 2017-12-05 19:45:32
* 标签属性及JS中国际化文字
* 使用方法: 属性：    placeholder="{{'blocks.placeholderName' | Trans}}"       js：Trans('tip.saveSuccess');
*/

(function () {
  'use strict';
  app.filter("Trans", ['$translate', function($translate) {
      return function(key) {
          if(key){
              return $translate.instant(key);
          }
      };
  }]);

})();
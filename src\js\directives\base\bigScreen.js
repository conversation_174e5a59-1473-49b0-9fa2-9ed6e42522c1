(function () {
    'use strict';
    angular.module('app')
        // 2022年第一季度评审贡献光荣榜
        .directive('dealRightInfo', function () {
            return {
                link: function(scope, element, attrs) {
                    let clientWidth = document.body.clientWidth;
                    element[0].style.paddingLeft = parseInt(clientWidth / 71) + 'px';
                }
            }
        })
        .directive('dealMessage', function () {
            return {
                link: function(scope, element, attrs) {
                    let clientWidth = document.body.clientWidth;
                    // element[0].style.paddingTop = parseInt(clientWidth / 213) + 'px';
                    element[0].style.fontSize = parseInt(clientWidth / 106) + 'px';
                }
            }
        })
        .directive('dealNameAndDept', function () {
            return {
                link: function(scope, element, attrs) {
                    let clientWidth = document.body.clientWidth;
                    element[0].style.fontSize = parseInt(clientWidth / 91) + 'px';
                }
            }
        })
        .directive('dealImage', function () {
            return {
                link: function(scope, element, attrs) {
                    let clientWidth = document.body.clientWidth;
                    element[0].style.width = parseInt(clientWidth / 26) + 'px';
                    element[0].style.height = parseInt(clientWidth / 21) + 'px';
                }
            }
        })
        .directive('dealStar', function () {
            return {
                link: function(scope, element, attrs) {
                    let clientWidth = document.body.clientWidth;
                    element[0].style.fontSize = parseInt(clientWidth / 109) + 'px';
                }
            }
        })
        .directive('dealQuestionCount', function () {
            return {
                link: function(scope, element, attrs) {
                    let clientWidth = document.body.clientWidth;
                    element[0].style.fontSize = parseInt(clientWidth / 62) + 'px';
                }
            }
        })
        // 2022年第一季度培训光荣榜
        .directive('dealTh', function () {
            return {
                link: function(scope, element, attrs) {
                    let clientWidth = document.body.clientWidth;
                    // element[0].style.fontSize = parseInt(clientWidth / 71) + 'px';
                    element[0].style.fontSize = parseInt(clientWidth / 85) + 'px';
                }
            }
        })
        .directive('dealTd', function () {
            return {
                link: function(scope, element, attrs) {
                    let clientWidth = document.body.clientWidth;
                    // element[0].style.fontSize = parseInt(clientWidth / 85) + 'px';
                    element[0].style.fontSize = parseInt(clientWidth / 107) + 'px';
                }
            }
        });
})();
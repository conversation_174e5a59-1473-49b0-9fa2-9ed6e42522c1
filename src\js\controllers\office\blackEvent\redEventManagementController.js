(function () {
  app.controller('redEventManagementController', [
    'redEventService',
    'comService',
    '$rootScope',
    '$scope',
    '$state',
    '$stateParams',
    '$modal',
    'inform',
    'Trans',
    'AgreeConstant',
    'LocalCache',
    '$http',
    function (
      redEventService,
      comService,
      $rootScope,
      $scope,
      $state,
      $stateParams,
      $modal,
      inform,
      Trans,
      AgreeConstant,
      LocalCache,
      $http
    ) {
      /**
       * *************************************************************
       *             初始化部分                                 开始
       * *************************************************************
       */
      // 获取缓存
      $scope.formInput = {
        department: '', // 部门名称
        rewardPerson: '', // 被奖励人
        startDate: inform.format(new Date(), 'yyyy-MM-dd').split('-')[0] + '-01' + '-01', // 起始时间
        endDate: '', // 结束时间
        redMonthForUpload: '',
        uploadMonth: '',
      };
      // 绑定预算文件控件改变事件
      $scope.$on('$includeContentLoaded', function () {
        $('#redFilesUpload').off('change').change(submitFormRed);
      });
      $scope.pages = {
        pageNum: '', // 当前页数
        size: '', // 每页显示数量
        total: '', // 数据总数
      };
      $scope.pages = inform.initPages(); // 初始化分页数据
      $scope.getData = getData;
      // 初始化按钮权限
      $scope.importRedEvent = false;
      // 按钮权限校验
      getButtonPermission();
      // 初始化页面信息
      initPages();
      // 在刷新页面时调用该方法
      getData($scope.pages.pageNum);
      // 设置列表的高度
      setDivHeight();
      // 窗体大小变化时重新计算高度
      $(window).resize(setDivHeight);

      /**
       * *************************************************************
       *              初始化部分                                 结束
       * *************************************************************
       */

      /**
       * *************************************************************
       *              方法声明部分                                 开始
       * *************************************************************
       */

      // 设置列表的高度
      function setDivHeight() {
        // 网页可见区域高度
        var clientHeight = document.body.clientHeight;
        var divHeight = clientHeight - (150 + 200);
        $('#divTBDis').height(divHeight);
        $('#subDivTBDis').height(divHeight - 80);
        $('#subDivTBDis0').height(divHeight - 80);
      }

      // 重置
      $scope.reset = function () {
        $scope.formInput.department = '';
        $scope.formInput.rewardPerson = '';
        $scope.formInput.redMonthForUpload = '';
        $scope.formInput.uploadMonth = '';
        initTime();
      };

      /**
       * 按钮权限管理
       */
      function getButtonPermission() {
        var buttons = {
          'Button-RedEvent-importRedEvent': 'importRedEvent', // 导入红事件
        };
        var urlData = {
          userId: LocalCache.getSession('userId'),
          parentPermission: 'Button-BlackEvent',
          buttons: buttons,
        };
        comService.getButtonPermission(urlData, $scope);
      }

      function initPages() {
        //获取山东新北洋集团的下级部门信息
        $scope.departmentList = [];
        comService.getOrgChildren('D010053').then(function (data) {
          $scope.departmentList = comService.getDepartment(data.data);
        });

        initTime();
      }
      function initTime() {
        var time = inform.format(new Date(), 'yyyy-MM').split('-'); //获取当前系统时间
        var start;
        var month = time[1] * 1 - 1 + '';
        if (month !== '0') {
          if ((time[1] * 1 - 1 + '').length === 1) {
            start = time[0] + '-0' + (time[1] * 1 - 1);
          } else {
            start = time[0] + '-' + (time[1] * 1 - 1);
          }
        } else {
          start = time[0] - 1 + '-12';
        }
        $scope.formInput.redMonthForUpload = inform.format(start, 'yyyy-MM');
        //对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
      }
      //获取所有数据以分页的形式
      function getData(pageNum) {
        //删除已加载冻结头部和部分列的HTML模板
        $scope.dataTableShow = 0;
        var urlData = {
          department: $scope.formInput.department,
          rewardPerson: $scope.formInput.rewardPerson,
          uploadMonth: $scope.formInput.uploadMonth,
          currentPage: pageNum, // 分页页数
          pageSize: $scope.pages.size, // 分页每页大小
        };
        setTimeout(showDataTable, 300);
        redEventService.getRedEventList(urlData).then(
          function (data) {
            $scope.dataTableShow = 1;
            //重新加载冻结头部和部分列的HTML模板
            if (data.code === AgreeConstant.code) {
              $scope.redEventList = data.data.list;
              if ($scope.redEventList.length === 0) {
                $scope.pages = inform.initPages(); //初始化分页数据
                inform.common(Trans('tip.noData'));
              } else {
                // 分页信息设置
                $scope.pages.total = data.data.total; // 页面总数
                $scope.pages.star = data.data.startRow; //页面起始数
                $scope.pages.end = data.data.endRow; //页面大小数
                $scope.pages.pageNum = data.data.pageNum; //页面页数
              }
              //调用DataTable组件冻结表头和左侧及右侧的列
              setTimeout(showDataTable, 300);
            } else {
              inform.common(data.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      /**
       *调用DataTable组件冻结表头和左侧及右侧的列
       */
      function showDataTable() {
        $('#fixedLeftAndTop').DataTable({
          //可被重新初始化
          retrieve: true,
          //自适应高度
          scrollY: 'calc(100vh - 350px)',
          scrollX: true,
          scrollCollapse: true,
          //控制每页显示
          paging: false,
          //冻结列（默认冻结左1）
          fixedColumns: {
            leftColumns: 1,
            rightColumns: 0,
          },
          //search框显示
          searching: false,
          //排序箭头
          ordering: false,
          //底部统计数据
          info: false,
        });
      }

      /**
       * 导入红事件
       */
      $scope.selectFile = function () {
        $('#red_config_modal').modal('hide');
        document.getElementById('redFilesUpload').click();
      };

      /**
       * 红事件上传文件
       */
      function submitFormRed(e) {
        //表单id  初始化表单值
        var formData = new FormData();
        //获取文档中有类型为file的第一个input元素
        var file = document.querySelector('input[type=file]').files[0];
        if (!file) {
          inform.common('请先选择文件!');
          $scope.formInput.projectIdForUpload = '';
          return false;
        }
        if (file.size > AgreeConstant.fileSize) {
          inform.common('上传文件大小不能超过2M');
          fileChangeReset();
          $scope.formInput.projectIdForUpload = '';
          return false;
        }
        formData.append('file', file);
        var a = file.type;
        if (a !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          inform.common('请选择.xlsx类型的文档进行上传!');
          $scope.formInput.projectIdForUpload = '';
          return false;
        } else {
          var modalInstance = $modal.open({
            templateUrl: 'myModalContent.html',
            controller: 'ModalInstanceCtrl',
            size: 'sm',
            resolve: {
              items: function items() {
                return '确定要导入吗！';
              },
            },
          });
          var uploadUrl = $rootScope.getWaySystemApi + 'redEvent/uploadRedEventExcel';
          formData.append('monthForUpload', $scope.formInput.redMonthForUpload);
          modalInstance.result.then(
            function () {
              //开启遮罩层
              inform.showLayer('上传中。。。。。。');
              $.ajax({
                url: uploadUrl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                beforeSend: function beforeSend(request) {
                  request.setRequestHeader('Authorization', 'Bearer ' + LocalCache.getSession('token') || '');
                },
                success: function success(result) {
                  if (result.code === AgreeConstant.code) {
                    // 关闭遮罩层
                    inform.closeLayer();
                    if (result.data !== null) {
                      var budgetDto = result.data;
                      $scope.confrimBudget($scope.type, budgetDto);
                    } else {
                      layer.confirm(
                        result.message,
                        {
                          title: false,
                          btn: ['确定'],
                        },
                        function (result) {
                          layer.close(result);
                          getData(1);
                        }
                      );
                    }
                  } else {
                    inform.closeLayer();
                    $modal.open({
                      templateUrl: 'tpl/common/errorModel.html',
                      controller: 'ModalInstanceCtrl',
                      size: 'lg',
                      resolve: {
                        items: function () {
                          return result.message;
                        },
                      },
                    });
                  }
                  //移除文件名称 通过表单元素的reset方法实现选择文件的重置
                  $('#formUploadRed')[0].reset();
                },
                error: function error(_error) {
                  inform.common(Trans('tip.requestError'));
                },
              });
            },
            function (reason) {
              // 处理关闭之后的逻辑
              if (reason === 'cancel') {
                //移除文件名称 通过表单元素的reset方法实现选择文件的重置
                $('#formUploadRed')[0].reset();
              }
            }
          );
        }
      }

      /**
       * *************************************************************
       *              方法声明部分                                 结束
       * *************************************************************
       */
    },
  ]);
})();

(function () {
    app.controller("wcpController", ['$rootScope','comService', '$scope','$state','$stateParams','$location','$modal','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams,$location, $modal,inform,Trans,AgreeConstant,LocalCache,$http) {
       	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		// 显示遮罩层
        inform.showLayer("跳转中...")
    	//查看是否登陆过
    	$scope.wcpLoginFlag = LocalCache.getObject('wcpManagement_wcpLoginFlag');
    	//上一次登录时间
    	var timeLogin = LocalCache.getSession('wcpLoginTime');
    	var timeNow = new Date().getTime();
    	var minute = parseInt(timeNow-timeLogin*1)/1000/60;
        //若未登录，或者现在时间超过登录时间25分钟，跳转去登陆界面
        if ($scope.wcpLoginFlag.flag==null||minute>25){
        	//获取到当前路由
            var pathUrl = $location.path();
            pathUrl = pathUrl.substr(1,pathUrl.length).replace(/\//g,'.');
            //设置需要返回缓存
            LocalCache.setObject("wcpManagement_return",pathUrl);
            //跳转
        	$state.go("app.office.wcpLoginController");
        	return;
        }
        var frame = document.getElementById('wcpSearchPage');
    	window.open($rootScope.wcpSystemApi+"websearch/PubHome.html");
    	inform.closeLayer();
    	/*setDivSize();
        var str = frame.src.split('\/');
    	
    	if (str.length>5){
    		frame.src = $rootScope.wcpSystemApi+str[3]+"/"+str[4]+"/"+str[5];
    	} else {
    		frame.src = $rootScope.wcpSystemApi+str[3]+"/"+str[4];
    	}
    	
    	//窗体大小变化时重新计算宽度
        $(window).resize(setDivSize);*/
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
        /**
         * 设置列表的宽度
         */
       /* function setDivSize() {
            frame.width = document.body.clientWidth - 200;
            frame.height = document.body.clientHeight - 40;
        }*/
        /**
	 	 * *************************************************************
	 	 *              方法声明部分                                 结束
	 	 * *************************************************************
	 	 */	
		
	}]);
})();
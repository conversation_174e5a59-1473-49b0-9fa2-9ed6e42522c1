/*
 * @Author: fubaole
 * @Date:   2017-07-28 09:32:34
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-02-26 18:07:49
 */

(function() {
  'use strict';
  app.controller("resources-detail", ['$rootScope', 'Microservice', '$scope', '$timeout', '$stateParams', 'inform', 'Trans',
    function($rootScope, Microservice, $scope, $timeout, $stateParams, inform, Trans) {

      $scope.threadDumpAll = 0;
      $scope.threadDumpBlocked = 0;
      $scope.threadDumpRunnable = 0;
      $scope.threadDumpTimedWaiting = 0;
      $scope.threadDumpWaiting = 0;
      $scope.getLabelClass = getLabelClass;

      $scope.getThreadDump = getThreadDump;
      console.log($stateParams.appName);
      $scope.getThreadDump();

      function getLabelClass(threadState) {
        if (threadState === 'RUNNABLE') {
          return 'label-success';
        } else if (threadState === 'WAITING') {
          return 'label-info';
        } else if (threadState === 'TIMED_WAITING') {
          return 'label-warning';
        } else if (threadState === 'BLOCKED') {
          return 'label-danger';
        }
      }

      function getThreadDump() {
        Microservice.threadDump($stateParams.appName)
          .then(function(data) {
            console.info(data);
            $scope.threadDump = data;
            angular.forEach($scope.threadDump, function(value) {
              if (value.threadState === 'RUNNABLE') {
                $scope.threadDumpRunnable += 1;
              } else if (value.threadState === 'WAITING') {
                $scope.threadDumpWaiting += 1;
              } else if (value.threadState === 'TIMED_WAITING') {
                $scope.threadDumpTimedWaiting += 1;
              } else if (value.threadState === 'BLOCKED') {
                $scope.threadDumpBlocked += 1;
              }
              $scope.threadDumpAll = $scope.threadDumpRunnable + $scope.threadDumpWaiting + $scope.threadDumpTimedWaiting + $scope.threadDumpBlocked;
            });
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }
    }
  ]);
})();
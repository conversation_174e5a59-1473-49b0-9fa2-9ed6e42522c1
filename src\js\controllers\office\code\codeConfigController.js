/*
 * @Author: fubaole
 * @Date:   2017-09-18 14:53:05
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-01-15 17:20:32
 */
(function () {
    app.controller("codeConfigController", ['comService', '$rootScope', '$scope', 'codeConfigService', 'codeService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, codeConfigService, codeService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {
    	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	$scope.limitList = AgreeConstant.limitList;
            //页面查询条件
            $scope.formInsert = {
                language: '',	//语言
                projectName: '',//sonar名查询
                pm: '',//项目经理
                checked: '',//启用禁用
                project: '',//项目名称
                team:'',//团队名称
                type: ''//模板类型
            };
            //权限控制
            $scope.TE=false;
            $scope.timerTask=false;
            $scope.config=false;
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            //页面分页信息
            $scope.pages = {
                pageNum: '',	//分页页数
                size: '20',		//分页每页大小
                total: ''		//数据总数
            };
            //项目生成报告的启用禁用类型
            $scope.checkedType = [{
            	'code': 0 ,
            	'value':'启用'
            }, {
        		'code': 1 ,
        		'value':'禁用'
            }];
            //项目生成报告的类型
            $scope.excelType = ['java', 'C++', 'javaScript', 'Android', 'C#','无关联'];
            $scope.excelTableType = ['java', 'C++', 'javaScript', 'Android', 'C#'];
            //初始化分页数据
            $scope.pages = inform.initPages();
            $scope.getData = getData;
            //初始化项目信息集合
            $scope.teamIdList = [];
            $scope.projectIdList = [];
            initTeam();
            initProject();
            $scope.number = $scope.pages.pageNum;
            $scope.pages.size = "20";
            //获取数据
            initDepartment();
            //判断按钮是否具有权限
            getButtonPermission();
            getData($scope.pages.pageNum);
            /**
    		 * *************************************************************
    		 *              初始化部分                                 结束
    		 * *************************************************************
    		 */	
    		
    		/**
    		 * *************************************************************
    		 *              方法声明部分                                 开始
    		 * *************************************************************
    		 */
            
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 160);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
            }
            /**
             * 重置
             */
            $scope.rest = function () {
                $scope.formInsert.language = "";
                $scope.formInsert.projectName = "";
                $scope.formInsert.pm = "";
                $scope.formInsert.checked = "";
                $scope.formInsert.type = "";
                $scope.formInsert.project = "";
                $scope.formInsert.team = "";
                $scope.formInsert.department = "";
            };
            /**
             * 判断启用还是禁用
             */
            $scope.jude = function (item) {
            	//启用
            	if (item.checked==='0'){
            		return "fa fa-check-circle green";
            	}
            	//禁用
            	return "fa fa-ban";
            };

            //被选择项目集合
            $scope.moduleSelected = [];
            $scope.selectAll = selectAll;
            // 全选函数
            function selectAll() {
                if ($scope.select_all) {
                    $scope.moduleSelected = [];
                    angular.forEach($scope.projectList, function (i) {
                        i.checked = true;
                        $scope.moduleSelected.push(i.name + ',' + i.cName + ',' + i.type);
                    });
                    console.log($scope.moduleSelected.length);
                } else {
                    angular.forEach($scope.projectList, function (i) {
                        i.checked = false;
                    });
                    $scope.moduleSelected = [];
                }
            }

            //单选项目
            $scope.selectOne = function (i) {
                $scope.select_all = false;
                if (i.type !== "") {
                    var index = $scope.moduleSelected.indexOf(i.name + ',' + i.cName + ',' + i.type);
                    if (index === -1 && i.checked) {
                        $scope.moduleSelected.push(i.name + ',' + i.cName + ',' + i.type);
                        if ($scope.moduleSelected.length == $scope.projectList.length){
                            $scope.select_all = true;
                        }
                    } else if (index !== -1 && !i.checked) {
                        $scope.moduleSelected.splice(index, 1);

                    }
                } else {
                    inform.common(Trans("请选择要配置的sonar模块！"));
                }

            };

            /**
             * 获取按钮权限
             */
            function getButtonPermission(){
                var buttons = {
                    'Button-codeConfigController-TE':'TE',
                    'Button-codeConfigController-TimerTask':'timerTask',
                    'Button-codeConfigController-config':'config'
                };
                var urlData = {
                    'userId':LocalCache.getSession("userId"),
                    'parentPermission':'ButtoncodeConfigController',
                    'buttons':buttons
                };
                comService.getButtonPermission(urlData,$scope);
            }
            /**
    		 * 初始化
    		 */
        	function initDepartment() {
        		//获取山东新北洋集团的下级部门信息
        		$scope.departmentList = [];
        		comService.getOrgChildren('D010053').then(function(data) {
        			$scope.departmentList = comService.getDepartment(data.data);
                 });
        		//获取员工信息
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function(data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                    }
                });
                //获取技术评价相关下拉框
                codeConfigService.TEselectMap().then(function(data) {
                    if (data.data) {
                        $scope.businessComplexity = changeMap(data.data[0],[]);
                        $scope.moduleSize =changeMap(data.data[1],[]);
                        $scope.codeReading = changeMap(data.data[2],[]);
                        $scope.otherMap = changeMap(data.data[3],[]);
                    }
                });
                //获取及格分数配置
                comService.queryEffectiveParam('CODE_REPORT_SCORE','CODE_REPORT_SCORE').then(function(data) {
            		if (data.data) {
            			$scope.infoList =  data.data;
            		}
                });
        	}

            function initTeam(){
                //获取项目信息
        		$scope.teamIdList = [];
                codeConfigService.getTeamList().then(function (data) {
                    $scope.teamIdList =data.data;
                });
            }

            function initProject(){
                //获取项目信息
        		$scope.projectIdList = [];
                codeConfigService.getProjectList().then(function (data) {
                    $scope.projectIdList =data.data;

                });
            }
        	/**
        	 * 保存模块及格分数
        	 */
        	$scope.saveScoreConfig = function(){
        		for (var i=0;i<$scope.infoList.length;i++){
        			$scope.infoList[i].paramName='CODE_REPORT_SCORE';
        			$scope.infoList[i].paramTypeCode='CODE_REPORT_SCORE';
        			comService.upParamValue($scope.infoList[i]);
        		}
        		$("#config_modal").modal("hide");
        	}
        	
        	
        	function changeMap(map,selectMap){
                for (var key in map) {
                    var mapForSelect = {
                        value:map[key],
                        lable:key,
                    };
                    selectMap.push(mapForSelect);
                }
                return selectMap;
            }
            /**
             * 更新
             */
            $scope.updateInfo = function (m) {
                var urlData = {
                    'cName': m.cname,
                    'manager': m.manager,
                    'type': m.type,
                    'department': m.department,
                    'name': m.name,
                    "teamId": m.teamId,
                    "id" : m.id
                };
                codeConfigService.updateInfo(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            inform.common("同步完成");
                        } else {
                            inform.common("同步失败");
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            };
            /**
             * 检查是否启用
             */
            $scope.checkInfo = function (m) {
            	var checked = (m.checked==='0'?'禁用':'启用');
            	var modalInstance = $modal.open({
    				templateUrl: 'myModalContent.html',
    				controller: 'ModalInstanceCtrl',
    				size: "sm",
    				resolve: {
    					items: function() {
    						return "是否确认"+checked;
    					}
    				}
    			});
    			modalInstance.result.then(function() {
    				var urlData = {
    						'name': m.name,
    						'checked': (m.checked === '0') ? '1' : '0'
    				};
    				codeConfigService.updateInfo(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                        	m.checked = (m.checked === '0') ? '1' : '0';
                        } else {
                            inform.common("启用，禁用失败");
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
    			});
            };

            /**
             * 同步代码质量数据
             */
            $scope.TimerTask = function () {
                //自定义弹出框
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function () {
                            return "确认同步代码质量数据？";
                        }
                    }
                });
                modalInstance.result.then(function () {
                    codeService.codeManagementTimerTask().then(function (data) {
                            inform.common(data.message);
                        },
                        function (error) {
                            inform.common(Trans("tip.requestError"));
                        });
                });
            };

            $scope.popModal = function() {
                if(!$scope.moduleSelected || $scope.moduleSelected.length <=0){
                    inform.common('请选择sonar模块！');
                    return;
                }

                $("#config_modal").click();
            };

             /**
              * 批量配置sonar项目
              * @returns 
              */
            $scope.batchUpdateInfo = function() {
                $scope.cNameList = []; 
                $scope.nameList = [];
                if(($scope.m.id=='' || $scope.m.id ==null) && ($scope.m.teamId=='' || $scope.m.teamId ==null) ){
                    inform.common('请选择所属项目或团队！');
                    return;
                }
                if($scope.moduleSelected==null||$scope.moduleSelected.length == 0){
                    inform.common('请选择sonar模块');
                    return;
                }
                angular.forEach($scope.moduleSelected, function (i) {
                    sonarList = i.split(",");
                    $scope.nameList.push(sonarList[0]);
                    $scope.cNameList.push(sonarList[1]);
                    
                });

                var urlData = {
                    'cnamelist': $scope.cNameList,
                    'nameList': $scope.nameList,
                    'manager': $scope.m.manager,
                    'department': $scope.m.department,
                    "id" : $scope.m.id,
                    "teamId": $scope.m.teamId
                };
                codeConfigService.batchUpdateInfo(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            inform.common("同步完成");
                            $("#config_project_modal").modal("hide");
                            getData();
                        } else {
                            inform.common("同步失败");
                        }
                        $scope.m = {};
                        $scope.cNameList = [];
                        $scope.nameList = [];
                        $scope.moduleSelected = [];
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            };

            /**
             * 获取项目
             */
            function getData(pageNum) {
                $scope.select_all = false;
                // $scope.teamIdList = [];
                // codeConfigService.getTeamList().then(function (data) {
                //     $scope.teamIdList =data.data;
                // });
                // $scope.projectIdList = [];
                // codeConfigService.getProjectList().then(function (data) {
                //     $scope.projectIdList =data.data;
                getSonarData(pageNum)
                // });
                

            }

            /**
             * 获取项目
             */
            function getSonarData(pageNum) {
                $scope.projectList = [];
                if($scope.formInsert.checked !== ''){
                    $scope.formInsert.checked === '禁用' ? '1' : '0'
                }

                var urlData = {
                    'language': $scope.formInsert.language.key ? $scope.formInsert.language.key : "",			// 语言类型
                    'projectName': $scope.formInsert.projectName,			// 模块名称
                    'pm': $scope.formInsert.pm,								//项目经理名称
                    'department': $scope.formInsert.department, //部门
                    'team': $scope.formInsert.team, //团队
                    'project': $scope.formInsert.project,               //项目
                    'type': $scope.formInsert.type,    						//模板类型
                    'checked': $scope.formInsert.checked,//启用禁用
                    'currentPage': pageNum 								// 分页页数

                };

                codeService.getAllProjectConfig(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {

                            data.data = angular.fromJson(data.data);
                            angular.forEach(data.data.list, function (res, index) {
                                var jsonNode = {
                                    id:res.id,
                                    name: res.name,
                                    cname: res.cName,
                                    type: res.type,
                                    department: res.department,
                                    manager: res.manager,
                                    checked: res.checked,
                                    team: res.teamId
                                };
                                $scope.projectList.push(angular.extend(jsonNode, res));
                            });
                            $scope.projectSelected = [];
                            if ($scope.projectList.length === 0) {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages(); 	// 初始化分页数据
                            } else {
                                // 分页信息设置
                                $scope.pages.total = data.data.total; 			// 页面数据总数
                                $scope.pages.star = data.data.startRow; 		// 页面起始数
                                $scope.pages.end = data.data.endRow;	 		// 页面分页大小
                                $scope.pages.pageNum = data.data.pageNum;  		// 第几页
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            /**
             * 根据项目id设置部门名称和项目经理
             * @param m
             */
           $scope.setDepartAndMange = function(m) {
               angular.forEach($scope.projectIdList, function (res){
                   if(m.id === res.id){
                       m.department = res.productLine;
                       m.manager = res.projectManager;
                   }
               });
            };

            /**
             * 查询项目的技术评价信息
             * @param m
             */
            $scope.selectTE = function(m) {
                var urlData = {
                    'name': m.name
                };
                codeService.getTEInfo(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.addParam = data.data;
                            $scope.addParam.name = m.name;

                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });

            };

            /**
             * 保存项目的技术评价信息
             */
            $scope.saveAddDataTE = function() {
                var urlData = angular.copy($scope.addParam);
                codeService.saveAddDataTE(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $("#TE_form").modal('hide');
                         inform.common(data.message);
                    } else {
                        inform.common(data.message);
                    }
                },
                function (error) {
                    inform.common(Trans("tip.requestError"));
                });

            };
           /**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */	
		
        }]);
})();
(function () {
    app.controller("processDataManagement", ['comService', '$scope','$modal','processDataService','inform','Trans','AgreeConstant','LocalCache',
        function (comService, $scope, $modal,processDataService,inform,Trans,AgreeConstant,LocalCache) {
       	
    	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */   
		//初始化页面信息
    	initPages();
    	$scope.updateEmployeename = LocalCache.getSession('currentUserName');   //获取当前的登录用户名

		$scope.loginId = LocalCache.getSession('userId');  //获取当前登录者的id
		$scope.formInput ={
				moduleName:'',
				departMentName:'',
				taskName:''
		};
		
	
 		
    	$scope.formInsert = {
    			id:'',                  //id	
        		module:'',				//产品xianid
        		moduleName:'',			//产品线名称
        		departMentName:'', 		//部门名称
        		taskName:'', 			//项目名称
        		allCheckup:'',			//检查总数
        		pass:'',				//通过总数
        		conformity:'',			//不符合项目问题数
        		close:'',				//关闭数
        		repairNum:'',			//按计划修复问题数
        		setProject:'',			//立项
        		plan:'',				//计划
        		demandDevelopment:'',	//需求开发
        		demandManage:'',		//需求管理
        		technicalSolution:'',	//技术解决方案
        		productIntegration:'',//产品集成
        		validate:'',//验证
        		makeSure:'',//确认
        		finishProject:'',//结项
        		projectSee:'',//项目监控
        		review:'',//同行评审
        		qualityManage:'',//质量管理
        		riskManage:'',//风险管理
        		configManage:'',//配置管理
        		ztTask:'',//禅道
        		opd:'',
        		opf:'',
        		createTime:'',//创建时间
        		regenerTime:'',//修改时间
        		creator:'',//创建者
        		regenerator:''   //修改者
    	};
    	$scope.pages = {
				pageNum : '', 		// 分页页数
				size : '', 			// 分页每页大小
				total : '' 			// 数据总数
		};
		//下载工时时间
		$scope.startTime = '';
		$scope.endTime = '';
		
    	$scope.pages = inform.initPages(); 	// 初始化分页数据
		$scope.getData = getData; 			// 分页相关函数         				
		$scope.taskList = [];				// 保存所有信息的集合               

		getData($scope.pages.pageNum);		//在刷新页面时调用该方法
	  	$scope.addInfo = addInfo;           // 新增一条信息
	  	$scope.updateInfo = updateInfo;     // 修改一条信息

		setDivHeight();//设置列表的高度

		$(window).resize(setDivHeight);//窗体大小变化时重新计算高度
	  	
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
	  	
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */	

	    //设置列表的高度
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 180);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 50);
 		}
	  	
		//开始时间
		$scope.openDateStart = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.openedStart = true;    //开始时间
			$scope.openedEnd = false;
		};
		
		//结束时间
		$scope.openDateEnd = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.openedStart = false;    
			$scope.openedEnd = true;    //结束时间
		};
		
		//重置
		$scope.rest = function() {
			$scope.formInput.moduleName = '';
			$scope.formInput.departMentName = '';
			$scope.formInput.taskName='';
		}
		
		/**
		 * 获取产品线code
		 */
	    function initPages() {
	    //获取产品线
	    $scope.productLineList = [];
	    comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
	        if (data.data) {
	        	$scope.productLineList =  data.data;
	        }
	     });

	    }
		
	     /**
         * 页面选中的修改信息复制
         */
     	$scope.update = function (m) {
     		$scope.changeParam = angular.copy(m);
     	}

     	/**
     	 * 修改页面的信息
     	 * changeParam 复制的集合
     	 */
     	function updateInfo(changeParam){
			$scope.addList = [];
			if(!changeParam.module){
				inform.common(Trans("产品线名称不能为空"));
				return false;
			} else if(!changeParam.departMentName){
				inform.common(Trans("部门名称名称不能为空"));
				return false;
			} else if(!changeParam.taskName){
				inform.common(Trans("任务名称不能为空"));
				return false;
			} else {
			var urlData = {
					'id':changeParam.id,
					'module':changeParam.module,
		    		'departMentName':changeParam.departMentName,
		    		'taskName':changeParam.taskName, 
		    		'allCheckup':changeParam.allCheckup,
		    		'pass':changeParam.pass,
		    		'conformity':changeParam.conformity,
		    		'close':changeParam.close,
		    		'repairNum':changeParam.repairNum,
		    		'setProject':changeParam.setProject,
		    		'plan':changeParam.plan,
		    		'demandDevelopment':changeParam.demandDevelopment,
		    		'demandManage':changeParam.demandManage,
		    		'technicalSolution':changeParam.technicalSolution,
		    		'productIntegration':changeParam.productIntegration,
		    		'validate':changeParam.validate,
		    		'makeSure':changeParam.makeSure,
		    		'finishProject':changeParam.finishProject,
		    		'projectSee':changeParam.projectSee,
		    		'review':changeParam.review,
		    		'qualityManage':changeParam.qualityManage,
		    		'riskManage':changeParam.riskManage,
		    		'configManage':changeParam.configManage,
		    		'ztTask':changeParam.ztTask,
		    		'opd':changeParam.opd,
		    		'opf':changeParam.opf,
		    		'regenerator':$scope.updateEmployeename
			};
			$("#seeAllMessage").modal('hide');
			$("#edit_modal").modal('hide');
			var modalInstance = $modal.open({
				  templateUrl: 'myModalContent.html',
                controller: 'ModalInstanceCtrl',
                size: "sm",
                resolve: {
                  items: function() {
                  return "确定要修改吗！";
                  }
               }
			});
	        modalInstance.result.then(function() {
	        	processDataService.updateMessage(urlData).then(function(data){
	        		callBackFunction(data);
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});	
	        });
			}
     	}
     	
		
		/**
		 * 新增页面信息
		 * 
		 */
		function addInfo(){
			$scope.addList = [];
			if(!$scope.formInsert.module){
				inform.common(Trans("产品线名称不能为空"));
				return false;
			} else if(!$scope.formInsert.departMentName){
				inform.common(Trans("部门名称名称不能为空"));
				return false;
			} else if(!$scope.formInsert.taskName){
				inform.common(Trans("任务名称不能为空"));
				return false;
			}  else {
				angular.forEach($scope.formInsert,function(res,i){
					if(res === 0||!res){
					 $scope.formInsert[i] = 0;
					}
				})
			var urlData = {
					'module':$scope.formInsert.module,
		    		'departMentName':$scope.formInsert.departMentName,
		    		'taskName':$scope.formInsert.taskName, 
		    		'allCheckup':$scope.formInsert.allCheckup,
		    		'pass':$scope.formInsert.pass,
		    		'conformity':$scope.formInsert.conformity,
		    		'close':$scope.formInsert.close,
		    		'repairNum':$scope.formInsert.repairNum,
		    		'setProject':$scope.formInsert.setProject,
		    		'plan':$scope.formInsert.plan,
		    		'demandDevelopment':$scope.formInsert.demandDevelopment,
		    		'demandManage':$scope.formInsert.demandManage,
		    		'technicalSolution':$scope.formInsert.technicalSolution,
		    		'productIntegration':$scope.formInsert.productIntegration,
		    		'validate':$scope.formInsert.validate,
		    		'makeSure':$scope.formInsert.makeSure,
		    		'finishProject':$scope.formInsert.finishProject,
		    		'projectSee':$scope.formInsert.projectSee,
		    		'review':$scope.formInsert.review,
		    		'qualityManage':$scope.formInsert.qualityManage,
		    		'riskManage':$scope.formInsert.riskManage,
		    		'configManage':$scope.formInsert.configManage,
		    		'ztTask':$scope.formInsert.ztTask,
		    		'opd':$scope.formInsert.opd,
		    		'opf':$scope.formInsert.opf,
		    		'creator':$scope.updateEmployeename

			};
			$("#add_modal").modal('hide');
			var modalInstance = $modal.open({
				  templateUrl: 'myModalContent.html',
                controller: 'ModalInstanceCtrl',
                size: "sm",
                resolve: {
                  items: function() {
                  return "确定要添加吗！";
                  }
               }
			});
	        modalInstance.result.then(function() {
	        	processDataService.addMessage(urlData).then(function(data){
				callBackFunction(data);
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});	
	        });
			}	
		}

		function callBackFunction(data){
		   if(data.data===0){
                inform.common(Trans("请输入正确的部门名称！"));
           } else {
                inform.common(Trans("信息添加成功！"));
           }
		}
		
		/**
		 * 获取所有的信息
		 * pageNum 页面页数
		 */
		function getData(pageNum){
		   	$scope.itemList = [];
        	var urlData ={
					'moduleName':$scope.formInput.moduleName,  			//产品线名称
					'departMentName':$scope.formInput.departMentName,  	//部门名称
					'taskName':$scope.formInput.taskName,					//项目名称
					'currentPage' : pageNum, 								// 分页页数
					'pageSize' : $scope.pages.size    						// 分页每页大小
        	};
        	processDataService.getAllMessage(urlData).then(function(data){
				if(data.code===AgreeConstant.code){
					var jsonData = data.data;
					$scope.itemList = jsonData.list;
					if ($scope.itemList.length===0) {
							$scope.pages = inform.initPages(); 			//初始化分页数据
					} else {
						// 分页信息设置
						$scope.pages.total = jsonData.total;		// 页面总数
						$scope.pages.star = jsonData.startRow;  	//页面起始数
						$scope.pages.end = jsonData.endRow;  		//页面大小数
						$scope.pages.pageNum = jsonData.pageNum;  	//页面页数
					}
				
				}		
        	},
        	function(error) {
				inform.common(Trans("tip.requestError"));
			});		

		}
		
		/**
 		 * *************************************************************
 		 *              方法声明部分                                 结束
 		 * *************************************************************
 		 */	
	
	}]);
})();
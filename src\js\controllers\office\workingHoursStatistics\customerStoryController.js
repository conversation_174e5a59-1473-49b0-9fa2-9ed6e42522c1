(function () {
    app.controller("customerStoryController", ['comService','LocalCache','$state','$rootScope', '$scope','customerStoryService','inform','Trans','AgreeConstant',
        function (comService,LocalCache,$state,$rootScope, $scope,customerStoryService,inform,Trans,AgreeConstant) {

		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		//当前登录人
		$scope.employeeName = LocalCache.getSession('employeeName');
		//产品线下拉框
		$scope.proProductLineList = AgreeConstant.proProductLineList;
		//平台产品线与禅道产品线对应关系
		var productLineRelation = AgreeConstant.productLineRelation;
		//登陆人产品线
		var loginProductLine = LocalCache.getSession('productLine');
		//登陆人禅道产品线
		$scope.loginProProductLine = productLineRelation[loginProductLine];
		//分页
    	$scope.pages = inform.initPages();
		//设置列表的高度
        setDivHeight();
        //获取缓存
        $scope.formRefer = LocalCache.getObject('customerStory_formRefer');
        //对原缓存进行覆盖
        LocalCache.setObject("customerStory_formRefer",{});
        initInfo();
        //窗体大小变化时重新计算高度
        $(window).resize(setDivHeight);
        //需求状态下拉框数
        $scope.statusSelect = ['已配置','未配置'];
        $scope.plmSelect = ['是','否']
        //被选择跟踪单集合
        $scope.storySelected = [];
        $scope.getData = getData; 			// 分页相关函数
        $scope.getProductList = getProductList;
        $scope.getModuleList = getModuleList;
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
		/**
         * 初始化检索条件
         */
        function initTime() {
            //仅第一次进来时开始时间会为空，后期开始时间不允许为空，创建人 怎么输入就怎么算
            $scope.formRefer.openedBy = $scope.formRefer.startDate?$scope.formRefer.openedBy:$scope.employeeName;
            //需求状态 同理
            $scope.formRefer.status = $scope.formRefer.startDate?$scope.formRefer.status:'未配置';
            //产品线 同理
            $scope.formRefer.productLine = $scope.formRefer.startDate?$scope.formRefer.productLine:$scope.loginProProductLine;
            var date = new Date();
            //开始日期向前推三个月（90天）
            date.setMonth(date.getMonth() - 3);
            //对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
            $scope.formRefer.startDate = $scope.formRefer.startDate?$scope.formRefer.startDate:inform.format(date, 'yyyy-MM-dd');
        }
        function initInfo(){
            //获取产品线
            var productLines=[];
            $scope.productLinesMap={};
            comService.queryEffectiveParam('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
                productLines = angular.fromJson(data.data);
                angular.forEach(productLines,function (item) {
                    $scope.productLinesMap[item["paramCode"]]=item["paramValue"];
                });
            });
            //初始化默认值
            initTime();
            //获取默认产品线下的产品名称
            getProductList('init');
        }
        /**
         * 获取禅道产品线下的产品名称
         */
         function getProductList(init){
            $scope.formRefer.productId=null;
            customerStoryService.getProductList($scope.formRefer.productLine?$scope.formRefer.productLine:'null').then(function (data) {
             	if (data.code === AgreeConstant.code) {
                    //产品名称下拉列表
                    $scope.productData = data.data;
                    if(init){
                        //获取产品下模块信息
                        getModuleList(init);
                    }
                } else {
                    inform.common(data.message);
                }
            }, function () {
                inform.common(Trans("tip.requestError"));
            });
         }
        /**
         * 获取产品下模块
         */
         function getModuleList(init){
            $scope.formRefer.moduleId=null;
            if ((!$scope.formRefer.productId) && (!init)){
                return;
            }
            if ((!$scope.formRefer.productId) && (init)){
               getData();
               return;
            }
            customerStoryService.getModuleList($scope.formRefer.productId).then(function (data) {
             	if (data.code === AgreeConstant.code) {
                    //模块名称下拉列表
                    $scope.moduleList = data.data;
                    if(init){
                        //查询
                        getData();
                    }
                } else {
                    inform.common(data.message);
                }
            }, function () {
                inform.common(Trans("tip.requestError"));
            });
         }
        /**
         * 设置列表的高度
         */
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight= clientHeight - (150 + 240);
            $("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight - 80);
        }
		//重置
		$scope.reset = function() {
		    $scope.formRefer = {};
		    initTime();
		}
        /**
		 * 跳转修改
		 */
		$scope.go = function(item) {
		    if (item == null && $scope.storySelected.length===0){
		        inform.common("请选择批量操作的需求");
		        return;
		    }
		    // 设置滚动条高度
		    $scope.formRefer.subDivTBDisScrollTop = $('#subDivTBDis').scrollTop();
		    LocalCache.setObject('customerStory_formRefer',$scope.formRefer);
		    var items=[];
            if (item == null){
                items=$scope.storySelected;
            } else {
                items.push(item.storyId);
            }
            $state.go("app.office.customerStoryManagement", {item: items});
		};
		/**
		 *
		 */
		$scope.toDetail = function(item) {
		    // 设置滚动条高度
        	$scope.formRefer.subDivTBDisScrollTop = $('#subDivTBDis').scrollTop();
        	LocalCache.setObject('customerStory_formRefer',$scope.formRefer);
        	$state.go("app.office.customerStoryDetail", {item: item.storyId});
		};
		//开始时间
        $scope.openDateStart = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = true;    //开始时间
            $scope.openedEnd = false;
        };

        //结束时间
        $scope.openDateEnd = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = false;
            $scope.openedEnd = true;    //结束时间
        };
    	/**
         * 获取所有用户需求信息
         */
        function getData(pageNum) {
            if (!$scope.formRefer.startDate){
                inform.common("请选择创建时间");
                return;
            }
            var urlData = $scope.formRefer;
            urlData.currentPage=pageNum;//当前页数
            urlData.pageSize=$scope.pages.size;//每页显示条数
            urlData.startDate=inform.format($scope.formRefer.startDate, 'yyyy-MM-dd');
            urlData.endDate=inform.format($scope.formRefer.endDate, 'yyyy-MM-dd');
            customerStoryService.getCustomerStoryData(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    if(null==data.data){
                    	$scope.storyData = {};
                    	inform.common(Trans("tip.noData"));
                    	$scope.pages = inform.initPages();
                    } else {
                    	//用户需求详情
                    	$scope.storyData = data.data.list;
                        //分页信息设置
                        $scope.pages.total = data.data.total;
                        $scope.pages.star = data.data.startRow;
                        $scope.pages.end = data.data.endRow;
                        $scope.pages.pageNum = data.data.pageNum;
                    }
                    // 获取到跟踪单信息之后滚动条跳至缓存中保存的scrollTop
                    setTimeout(function () {
                        if ($scope.formRefer.subDivTBDisScrollTop) {
                            $('#subDivTBDis').animate({scrollTop: $scope.formRefer.subDivTBDisScrollTop},10)
                        }
                    },500)
                } else {
                   inform.common(data.message);
                }
            },function () {
               inform.common(Trans("tip.requestError"));
            });
        }
        // 全选函数
        $scope.selectAll = function() {
            if ($scope.select_all) {
                $scope.storySelected = [];
                angular.forEach($scope.storyData, function (i) {
                    if(i.proProductLine*1===$scope.loginProProductLine || $scope.employeeName===i.openedBy){
                        i.checked = true;
                        $scope.storySelected.push(i.storyId);
                    }
                });
            } else {
                angular.forEach($scope.storyData, function (i) {
                    i.checked = false;
                });
                $scope.storySelected = [];
            }
        }

        //单选项目
        $scope.selectOne = function (i) {
            $scope.select_all = false;
            var index = $scope.storySelected.indexOf(i.storyId);
            if (index === -1 && i.checked) {
                $scope.storySelected.push(i.storyId);
            } else if (index !== -1 && !i.checked) {
                $scope.storySelected.splice(index, 1);
            }
        };

      }]);
})();
(function () {
    app.controller("settleController",
        ['settleService','comService','$rootScope', '$scope','$state','$stateParams','$modal','inform','Trans','AgreeConstant','LocalCache','$http',
        function (settleService,comService,$rootScope, $scope,$state,$stateParams, $modal,inform,Trans,AgreeConstant,LocalCache,$http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //获取缓存
            $scope.formInput = LocalCache.getObject('settle_formInput');
            //对原缓存进行覆盖
            LocalCache.setObject("settle_formInput",{});
            //一进来，默认选中并显示项目预算管理
            $scope.type = $stateParams.type === null ? 1 : $stateParams.type;

            $scope.pages = inform.initPages(); 	// 初始化分页数据
            $scope.getData = getData;
            //初始化页面信息
            initPages();
            //在刷新页面时调用该方法
            getData($scope.pages.pageNum);
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            //设置列表的高度
            function setDivHeight(){
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 70);
                $("#subDivTBDis0").height(divHeight - 70);
            }

            //重置
            $scope.reset = function() {
                $scope.formInput= {};
            }


            function initPages() {
                //获取山东新北洋集团的下级部门信息
                $scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentList = comService.getDepartment(data.data);
                });

                $scope.lineList = [];
                $scope.lineMap = {};
                comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
                    if(data.data) {
                        $scope.lineList = data.data;
                        $scope.lineList.forEach( function (item) {
                            var key = item.param_code;
                            $scope.lineMap[key] = item.param_value;
                        })
                    }
                });
                //结算状态
                $scope.statusList=[
                    {
                        'code':'1',
                        'value':'待结算',
                    },{
                        'code':'2',
                        'value':'结算申请',
                    },{
                        'code':'3',
                        'value':'已结算',
                    }];
                $scope.statusMap={
                    '1':'待结算',
                    '2':'结算申请',
                    '3':'已结算'
                }
            }

            //获取所有数据以分页的形式
            function getData(pageNum){
                $scope.plmSettleList = [];
                $scope.projectSettleList = [];
                var start = inform.format($scope.formInput.startDate,'yyyy-MM-dd');
                var end = inform.format($scope.formInput.endDate,'yyyy-MM-dd');
                if (start > end) {
                    inform.common(Trans("申请的结束时间必须大于开始时间！"));
                    return false;
                }
                var urlData ={
                    'departmentCode':$scope.formInput.department,
                    'productLineCode':$scope.formInput.productLine,
                    'projectName':$scope.formInput.projectName,
                    'projectManager':$scope.formInput.proManagerName,
                    'plm':$scope.formInput.plm,
                    'startDate':inform.format($scope.formInput.startDate,'yyyy-MM-dd'),
                    'endDate':inform.format($scope.formInput.endDate,'yyyy-MM-dd'),
                    'status' :$scope.formInput.status,
                    'currentPage' : pageNum, 								// 分页页数
                    'pageSize' : $scope.pages.size    						// 分页每页大小
                };
                if ($scope.type === '2'){
                    urlData.plm= 'plm'
                }
                if ($scope.type === '1'){
                    urlData.plm= '0'
                }
                //获取项目预算
                settleService.getProjectSettle(urlData).then(function(data){
                        if(data.code===AgreeConstant.code){
                            var getata = data.data;
                            $scope.projectBudgetList = getata.list;
                            if ($scope.projectBudgetList.length===0) {
                                $scope.pages = inform.initPages(); 			//初始化分页数据
                                inform.common(Trans("tip.noData"));
                            } else {
                                // 分页信息设置
                                $scope.pages.total = getata.total;		// 页面总数
                                $scope.pages.star = getata.startRow;  	//页面起始数
                                $scope.pages.end = getata.endRow;  		//页面大小数
                                $scope.pages.pageNum = getata.pageNum;  	//页面页数
                            }

                        }
                    },
                    function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            /**
             * 结算成本明细预览
             */
            $scope.settleInfo = function (projectId, plm, status, projectName){
                LocalCache.setObject("settle_formInput",$scope.formInput);
                $state.go("app.office.settleInfo", { projectId:projectId, plm:plm, status:status, projectName: projectName });
            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();


(function () {
	app.controller("projectManagementUpdate", ['companyProjectModule','companyProjectManagementService','comService', '$rootScope', '$scope', 'projectManagementService', '$stateParams', 'inform', 'Trans', '$modal','AgreeConstant','LocalCache', '$state',
	function (companyProjectModule,companyProjectManagementService,comService, $rootScope, $scope, projectManagementService, $stateParams, inform, Trans, $modal,AgreeConstant, LocalCache,$state) {
		/**
   * *************************************************************
   *             初始化部分                                 开始
   * *************************************************************
   */
		// 正则校验配置
		$scope.limitList = AgreeConstant.limitList;
		//绑定项目导入excel文件控件改变事件
        $("#filesUpload").change(submitForm);
        //项目信息与里程碑信息的切换标志
		$scope.type = '1';
		//是否显示里程碑信息0：是，1：否
		$scope.isSee = '1'
		//增加还是修改0：增加，1：修改
		$scope.isAdd = $stateParams.isAdd;

        $scope.param = JSON.parse($stateParams.projectInfoParam);
        $scope.specDetail = {
			'reportProhibitions': []
		};
        //为报表禁用控制标识赋值
        popModalDetail($scope.param.reportProhibitions);
        $scope.projectId = null;
        $scope.flag = 0;
        //是否为增加时的修改0：是，1：否
        $scope.isAddUpdate = "1";
		//项目状态
        $scope.projectStatusSelect = AgreeConstant.projectStatusList;
		//项目规模下拉框数
		$scope.groupTypeSelect = [{
			value: '0',
			label: '小型项目'
		}, {
			value: '1',
			label: '中型项目'
		}, {
			value: '2',
			label: '大型项目'
		}];
        $scope.setData = setData;
        //开发模型
        $scope.typeSelect = ['瀑布', '敏捷'];
		//立项类型
		$scope.projectApprovalList = ['公司立项', '系研立项', '预研立项'];
		//立项等级
		$scope.approvalGradeList = ['Ⅰ', 'Ⅱ', 'Ⅲ', '无'];
		//评审等级
		$scope.reviewGradeList = ['一级', '二级', '三级'];
		//项目来源
        $scope.projectOriginList = ['年初规划', '上年延续', '规划外新增'];
        //项目规划类型
        $scope.projectPlanTypeList = ['自主规划', '客户定制', '市场导入'];
        //地区
        $scope.areaList = ['威海主导', '北京主导', '西安主导','深圳主导'];
        //行业
        $scope.professionList = ['金融行业','物流行业','新零售行业','新兴行业'];
		//设置列表的高度
		setDivHeight();
		//初始化页面信息
		initPages();
		var projectManagerName = ""; //项目经理姓名
		$(window).resize(setDivHeight); //窗体大小变化时重新计算高度
		/**
   * *************************************************************
   *              初始化部分                                 结束
   * *************************************************************
   */

		/**
   * *************************************************************
   *              方法声明部分                                开始
   * *************************************************************
   */

		/**
    	 * 设置列表的高度
    	 */
		function setDivHeight() {
			//网页可见区域高度
			var clientHeight = document.body.clientHeight;
			var divHeight = clientHeight - (150 + 185);
			$("#divTBDis").height(divHeight);
			$("#subDivTBDis").height(divHeight - 50);
			var clientWidth = document.body.clientWidth;
			$("#buttonStyle").css(inform.getButtonStyle(clientHeight, clientWidth));
		}
		/**
   * 页面初始化
   */
		function initPages() {
            //获取员工信息
			$scope.employeesList = [];
			$scope.employees = [];
			comService.getEmployeesByOrgId('','1').then(function (data) {
				if (data.data) {
					$scope.employeesList = data.data;
					$scope.flag++;
                    $scope.setParams();
				}
			});
            //获取产品线
			$scope.projectLine = [];
			comService.queryEffectiveParam('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
				if (data.data) {
					$scope.projectLine = data.data;
					$scope.flag++;
                    $scope.setParams();
				}
			});
            //获取山东新北洋集团的下级部门信息
			$scope.orgList = [];
			comService.getOrgChildren('D010053').then(function (data) {
				$scope.orgList = comService.getDepartment(data.data);
				$scope.flag++;
                $scope.setParams();
			});
            //获取项目归属
			$scope.projectAttributionList = [];
			comService.queryEffectiveParam('WEEKLY_REPORT', 'AFFILIATION').then(function (data) {
				if (data.data) {
					$scope.projectAttributionList = data.data;
				}
			});
            //获取项目难度等级
			$scope.productScaleList = [];
			comService.queryEffectiveParam('PROJECT_SCALE', 'PROJECT_SCALE').then(function (data) {
				if (data.data) {
					$scope.productScaleList = data.data;
					$scope.flag++;
                    $scope.setParams();
				}
			});
            //获取报表信息
			$scope.reportLine = [];
			comService.queryEffectiveParam('REPORT', 'REPORT').then(function (data) {
				if (data.data) {
					$scope.reportLine = data.data;
				}
			});
            //获取公司立项项目信息
			$scope.companyProject = {};
			if($scope.param.companyProjectApprovalId){
                var urlData ={
                    'projectId':$scope.param.companyProjectApprovalId//公司立项项目编号
                };
                companyProjectManagementService.getCompanyProjectInfoList(urlData).then(function (data) {
                    if (data.data) {
                        $scope.companyProject = data.data.list[0];
                        if($scope.companyProject) {
                            $scope.param.companyProjectApprovalName = $scope.companyProject.projectName;
                            $scope.param.marketDecisionName = $scope.companyProject.marketDecisionName;
                            $scope.param.projectApprovalTime = $scope.companyProject.marketDecisionAcceptDate;
                        }

                    }
                });
			}

		}

		/**修改项目经理
        **/
		$scope.changeManager = function () {

            for (var i = 0; i < $scope.employeesList.length; i++) {
                if ($scope.employeesList[i].employeeNo === $scope.param.projectManagerNo) {
                    projectManagerName = $scope.employeesList[i].realName;
                    break;
                }
            }
		};

        $scope.setParams = function () {
            if($scope.isAdd==="0" && $scope.isAddUpdate ==="1"){
                return;
            }
            if ($scope.flag===4) {
                $scope.changeEmployee();
                angular.forEach($scope.projectLine, function (line, i) {//遍历产品线
                  if (line.paramValue===$scope.param.productLine) {
                      $scope.param.productLine = line.paramCode;
                      $scope.changeProductTypeName(line.paramCode);
                  }
               });
                angular.forEach($scope.orgList, function (org, i) {//遍历部门
                    if (org.orgName===$scope.param.department) {
                       $scope.param.department = org.orgCode;
                    }
                });
                angular.forEach($scope.productScaleList, function (scale, i) {//遍历项目难度等级
                   if (scale.paramValue===$scope.param.projectScale) {
                       $scope.param.projectScale = scale.paramCode;
                   }
               });

                $scope.projectId = $scope.param.id;
                //设置选择框的值
                $scope.setCheckBosValue();

            }
        };

        $scope.changeEmployee = function(){
            angular.forEach($scope.employeesList, function (employee, i) {//遍历人员
                    if (employee.realName===$scope.param.projectAssistant) { //PPQA
                        $scope.param.projectAssistant = employee.employeeNo;
                    }
                    if (employee.realName===$scope.param.qualityEngineer) { //质量工程师
                        $scope.param.qualityEngineer = employee.employeeNo;
                    }
                    if (employee.realName===$scope.param.productManager) { //Product Owner
                         $scope.param.productManager = employee.employeeNo;
                    }
               });
        }

        $scope.setCheckBosValue = function(){
            setTimeout(setData,3000);
        }

        //初始化checkbox的值
         function setData() {
                if($scope.isAdd==="0" && $scope.isAddUpdate ==="1"){
                        return;
                }
                var boxes = document.getElementsByName("products");
                if($scope.param.companyProductTypeNameCode == null || $scope.param.companyProductTypeNameCode ===""){
                    return;
                }
                var dataList = $scope.param.companyProductTypeNameCode.split(";");
            	//获取页面所有产品名称
            	for(var i=0;i<boxes.length;i++){
            	    boxes[i].checked = false;
            		//现有的产品名称
            		for(var j=0;j<dataList.length;j++){
            			if (boxes[i].value === dataList[j]){
            				boxes[i].checked = true;
            			}
            		}
            	}
        }

        //改变产品类别及名称
		$scope.changeProductTypeName = function (productLine) {
            //获取产品类别及名称
			$scope.productTypeNameList = [];
            projectManagementService.getProductTypeNameList(productLine).then(function (data) {
				if (data.data) {
					$scope.productTypeNameList = data.data;
				}
			});
		};

		/**
   * 修改信息
   */
		$scope.updateInfo  = function () {
			if (!$scope.param.startTime) {
				inform.common("请填写开始时间");
			} else if ($scope.param.projectStatus === '结项' && !$scope.param.endTime) {
				inform.common("请填写结束时间");
			} else if ($scope.isAdd==="1" && $scope.param.projectStatus == null) {
                    inform.common("请选择项目状态");
            } else {
            angular.forEach($scope.employeesList, function (employee, i) {//遍历人员
                                                    if (employee.employeeNo===$scope.param.projectAssistant) { //PPQA
                                                        $scope.param.projectAssistant = employee.realName;
                                                    }
                                                    if (employee.employeeNo===$scope.param.qualityEngineer) { //质量工程师
                                                        $scope.param.qualityEngineer = employee.realName;
                                                    }
                                });
				var urlData = {
				    'projectStatus':$scope.isAdd==="1" ? $scope.param.projectStatus:"进行中",
				    'id': $scope.projectId,
				    'projectNumber': $scope.param.projectNumber,
					'cname': $scope.param.cname,
					'projectManager': projectManagerName, //项目经理姓名
                    'projectManagerNo': $scope.param.projectManagerNo, //项目经理工号
					'testLeader': $scope.param.testLeader, //测试负责人
                    'operations': null == $scope.param.operations ?null:$scope.param.operations.join(','), //运维工程师
                    'projectAssistant': $scope.param.projectAssistant,//PPQA
					'qualityEngineer': $scope.param.qualityEngineer,//质量工程师
					'productManager': $scope.param.productManager,//Product Owner
					'startTime': inform.format($scope.param.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.param.endTime, 'yyyy-MM-dd'),
                    'planEndTime': inform.format($scope.param.planEndTime, 'yyyy-MM-dd'),
                    'higherUp': $scope.param.higherUp,//直属上级

                    'productLine': $scope.param.productLine,//软件产品线
                    'companyProductTypeNameCode':$scope.param.companyProductTypeNameCode,

                    'department': $scope.param.department,   //部门
                    'projectAttribution': $scope.param.projectAttribution,//项目归属
                    'projectOrigin': $scope.param.projectOrigin,//项目来源
                    'projectPlanType':$scope.param.projectPlanType,//项目规划类型
                    'type': "瀑布",//$scope.param.type,//开发模型
                    'projectApproval': $scope.param.projectApproval,//项目立项类型
					'area':$scope.param.area,//地区
					'profession':$scope.param.profession,//行业

                    'groupType': $scope.param.groupType,//项目规模
                    'projectScale': $scope.param.projectScale,//项目难度等级
                    'approvalGrade': $scope.param.approvalGrade,//公司立项等级
                    'reviewGrade': $scope.param.reviewGrade,//项目级别
                    'projectTypeFlag': "P",//项目类型标志

                    'companyProjectApprovalId': $scope.param.companyProjectApprovalId,//公司立项项目id
                    'hardwareProductManager': $scope.param.hardwareProductManager,//硬件Product Owner
                    'hardwareProjectManager': $scope.param.hardwareProjectManager,//硬件项目经理
                    'hardwareGroup': $scope.param.hardwareGroup,//硬件团队代表

                    'projectDescribe': $scope.param.projectDescribe,//项目来源
                    'softwareCopyright': $scope.param.softwareCopyright,//软件著作权要求
                    'patent': $scope.param.patent,//专利要求
                    'reportProhibitions': $scope.specDetail.reportProhibitions.join(',')
				};
				projectManagementService.updateProjectInfo(urlData).then(function (data) {
					if (data.code === AgreeConstant.code) {
						inform.common(data.message);
						$scope.goback();
					} else {
					    //弹框提示
					    if(data.message.indexOf("项目id=")>-1){
			                    //选择是否覆盖项目(新增)
                                inform.modalInstance("项目已存在，是否覆盖？").result.then(function () {
                                    $scope.projectId = data.message.split("=")[1];
                                    $scope.updateInfo();
                                });

					    }else{
					        inform.common(data.message);
					    }
					}
				}, function (error) {
					inform.common(Trans("tip.requestError"));
				});
			}
		};

        $scope.notSelect = function () {
			if ($scope.zerochecked) {
				$scope.zerochecked = false;
			}
			if ($scope.onechecked) {
				$scope.onechecked = false;
			}
		};
		/**
       * 报表禁止范围控制
       */
		$scope.selectAll = function () {
			angular.forEach($scope.reportLine, function (report) {
				if ($scope.specDetail.reportProhibitions.indexOf(report.paramCode) === -1) {
					$scope.specDetail.reportProhibitions.push(report.paramCode);
				}
			});
			$scope.zerochecked = true;
			$scope.onechecked = false;
		};
		$scope.clearAll = function () {
			$scope.specDetail.reportProhibitions = [];
			$scope.onechecked = true;
			$scope.zerochecked = false;
		};
        //报表禁用控制赋值
            function popModalDetail(item) {
                if (null ==item) {
                    item = '';
                }
                var participants = item.slice(0, item.length);
                $scope.specDetail.reportProhibitions = participants.split(',');
            }
        /*
        * 项目状态发生改变,将结束时间改为当前时间
        * */
        $scope.projectStatusChange = function () {
             if($scope.param.projectStatus === '结项') {
                $scope.param.endTime = inform.format(new Date(),'yyyy-MM-dd');
             } else {
                $scope.param.endTime ='';
             }
        }

		/**
   * 返回项目信息历
   */
		$scope.goback = function () {
			$state.go('app.office.projectManagement');
		};

		//配置产品类别
		$scope.configProductType = function () {
            $("#configProductType_modal").modal("hide");
            var boxes = document.getElementsByName("products");
       		 var details = "";
       		 angular.forEach(boxes, function (detail, i) {
       			 if (boxes[i].checked){
       				 details = details+boxes[i].value+";";
       			 }
       		 });
       		 $scope.param.companyProductTypeNameCode = details.substr(0,details.length-1);
       		 //设置companyProductTypeName
		}

		/**
   *  新增的开始时间按钮
   */
		$scope.startTime_update = function ($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.startTime1 = false;
			$scope.startTime_update1 = true;
			$scope.endTime1 = false;
			$scope.endTime_update1 = false;
			$scope.planEndTime_update1 = false;
		};
		/**
        *  新增的修改时间按钮
        */
		$scope.endTime_update = function ($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.startTime1 = false;
			$scope.startTime_update1 = false;
			$scope.endTime1 = false;
			$scope.endTime_update1 = true;
			$scope.planEndTime_update1 = false;
		};
		/**
         *  新增的计划结束时间按钮
        */
		$scope.planEndTime_update = function ($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.startTime1 = false;
			$scope.startTime_update1 = false;
			$scope.endTime1 = false;
			$scope.endTime_update1 = false;
			$scope.planEndTime_update1 = true;
		};

		/**
        * 导入预算
        */
        $scope.selectFile = function() {
               document.getElementById("filesUpload").click();
        }

        /**
	     * 项目信息上传文件
	     */
	    function submitForm(e){
	    	//表单id  初始化表单值
            var formData = new FormData();
            //获取文档中有类型为file的第一个input元素
            var file = document.querySelector('input[type=file]').files[0];
            if (!file) {
                inform.common("请先选择文件!");
                $scope.formInput.projectIdForUpload='';
                return false;
            }
            if (file.size > AgreeConstant.fileSize) {
                inform.common("上传文件大小不能超过2M");
                fileChangeReset();
                $scope.formInput.projectIdForUpload='';
                return false;
            }
            formData.append('file', file);
            var a = file.type;
            if (a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
                inform.common("请选择.xlsx类型的文档进行上传!");
                $scope.formInput.projectIdForUpload='';
                return false;
            } else {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function items() {
                            return "确定要导入吗！";
                        }
                    }
                });
                var uploadUrl = $rootScope.getWaySystemApi + 'projectmanagement/uploadProjectInfo';

                modalInstance.result.then(function () {
                    //开启遮罩层
                    inform.showLayer("上传中。。。。。。");
                    $.ajax({
                        url: uploadUrl,
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        beforeSend: function beforeSend(request) {
                            request.setRequestHeader("Authorization", 'Bearer ' + LocalCache.getSession("token") || '');
                        },
                        success: function success(result) {
                            if (result.code === AgreeConstant.code) {
                                // 关闭遮罩层
                                inform.closeLayer();
                                layer.confirm(result.message,{
                                    title:false,
                                    btn:['确定']
                                },function(confirmMsg){
                                    layer.close(confirmMsg);
                                    $scope.isAddUpdate ="0";
                                    //反显导入的数据
                                    $scope.param = result.data["projectInfo"];
                                    $scope.projectId = $scope.param.id;
                                    //里程碑信息
                                    $scope.projectDetails = result.data["detailList"];
                                    $scope.changeProductTypeName($scope.param.productLine);
                                    $scope.changeEmployee();
                                    //里程碑总权重
                                    $scope.allWeight = result.data["allWeight"];
                                    $scope.showMsg();
                                });
                            } else {
                                //校验不通过,弹框提示
           					    if(result.message.indexOf("项目id=")>-1){
           					        // 关闭遮罩层
                                      inform.closeLayer();
                                       //选择是否覆盖项目
                                      inform.modalInstance("项目已存在，是否覆盖？").result.then(function () {
                                            $scope.projectId = result.message.split("=")[1];
                                          var urlData = {
                                          		'id': $scope.projectId
                                          };
                                           projectManagementService.addProjectInfo(urlData).then(function (data) {
                            					if (data.code === AgreeConstant.code) {
                            						inform.common(data.message);
                            						$scope.isAddUpdate ="0";
                            						//反显导入的数据
                            						$scope.param = data.data["projectInfo"];
                            						//里程碑信息
                            						$scope.projectDetails = data.data["detailList"];
                            						//里程碑总权重
                                                    $scope.allWeight = data.data["allWeight"];
                            						$scope.changeProductTypeName($scope.param.productLine);
                            						$scope.changeEmployee();
                            						$scope.showMsg();
                            					} else {
                                                    $modal.open({
                                                              templateUrl: 'tpl/common/errorModel.html',
                                                              controller: 'ModalInstanceCtrl',
                                                              size: "lg",
                                                              resolve: {
                                                                  items: function() {
                                                                     return data.message;
                                                                  }
                                                              }
                                                    });
                            					}
                            				}, function (error) {
                            					inform.common(Trans("tip.requestError"));
                            				});
                                       });
           					    }else{
           	                        inform.closeLayer();
                                    $modal.open({
                                         templateUrl: 'tpl/common/errorModel.html',
                                         controller: 'ModalInstanceCtrl',
                                         size: "lg",
                                         resolve: {
                                             items: function() {
                                                return result.message;
                                             }
                                         }
                                    });
           					    }
                            }
                            //移除文件名称 通过表单元素的reset方法实现选择文件的重置
                            $("#formUpload")[0].reset();
                        },
                        error: function error(_error) {
                            inform.common(Trans("tip.requestError"));
                        }
                    });
                });
            }
	     }

	     $scope.showMsg = function(){
            if((100.00-$scope.allWeight) !==0){
                $modal.open({
                    templateUrl: 'tpl/common/errorModel.html',
                    controller: 'ModalInstanceCtrl',
                    size: "lg",
                    resolve: {
                       items: function() {
                            return "当前项目里程碑总权重为"+$scope.allWeight+"%，请到‘研发管理数据维护/项目进度/项目计划进度偏差/项目里程碑计划详情’中进行修改维护！";
                       }
                    }
                });
            }
	     }
        /**
         * 公司立项项目
         */
        $scope.initCompanyProjectModule = function (){
            var data={
                'projectName':$scope.param.companyProjectApprovalName
            };
            companyProjectModule.initModule(data,$scope,checkCompanyProjectInfo);
        }
         /**
          * 检查公司立项项目是否已关联过项目
          */
         function checkCompanyProjectInfo(paramData){
            var urlData = {
                'projectId':paramData.projectId,
                'type':'1'
            }
            projectManagementService.getRelationByCompanyProjectId(urlData).then(
                function (data) {
                    if (data.code === AgreeConstant.code) {

                        if(data.data && data.data.length > 0) {
                            inform.common("该公司立项项目已关联"+data.data[0]['note']);
                            return;
                        }

                         setCompanyProjectInfo(paramData)

                    } else {
                        inform.common("查询公司立项项目关联信息失败。");
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                }
            );

         }

        /**
         * 根据所选中的公司立项项目回填信息
         */
        function setCompanyProjectInfo(data){
            $scope.param.companyProjectApprovalId = data.projectId;
            $scope.param.companyProjectApprovalName = data.projectName;
            $scope.param.marketDecisionName = data.marketDecisionName;
            $scope.param.projectApprovalTime = data.marketDecisionAcceptDate;
        }
        //清空公司立项项目信息
        $scope.clearCompanyProject = function(){
            $scope.param.companyProjectApprovalId = "";
            $scope.param.companyProjectApprovalName = "";
            $scope.param.marketDecisionName = "";
            $scope.param.projectApprovalTime = "";
        }
		/**
     * *************************************************************
     *              方法声明部分                                结束
     * *************************************************************
     */
	}]);
})();

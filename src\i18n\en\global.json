{"tip": {"saveSuccess": "save success !", "saveError": "save error !", "delSuccess": "delete success!", "delError": "delete error!", "upError": "upload error!", "upSuccess": "upload success!", "requestError": "Request Error!", "nameIsUser": "Company name has been occupied north!", "noData": "No data has been found！", "psdRest": "Password reset successfully!", "sendMessage": "Message sent successfully！", "selfFreeze": "You can not defrost / freeze yourself!", "pageNumTip": "Please input page number!"}, "header": {"navbar": {"title": "BBPF", "index": "home", "MegaTitle": "Management", "MegaList": {"mainSysManage": "System", "sysManage": {"compManage": "Company", "userManage": "User", "userGroup": "UserGroup", "personnelManage": "Personnel", "poweManage": "Authority", "regionManage": "Area", "organManage": "Organization", "roleManage": "Character", "placeManage": "placeManage", "parameterManage": "parameter", "logManage": "Log", "dictionaryManage": "Dictionary", "workManage": "Bench", "indexEdit": "Index Config", "blocksManage": "Blocks", "templateManage": "Template", "benchManage": "Work Bench"}, "mainServManage": "Service", "servMagement": {"monitoring": "Monitor", "gateway": "Gateway", "tracker": "Tracker", "API": "API", "logs": "Logs", "Administrators": "Admin", "applicationMetrics": "Metrics", "health": "Health", "configure": "Configure", "mainConfig": "Config", "SSH": "SSH", "SpringCloud": "Cloud config", "serviceCluster": "Cluster", "applicationInstances": "Instances", "History": "History", "Replicas": "Replicas"}, "mainNewsManage": "News", "newsManage": {"Template": "Template", "SendNews": "Send", "SendNewsDetail": "Send Detail", "ReceiveNews": "Receive", "ReceiveNewsDetail": "Receive Detail"}}}}, "common": {"tips": "Tips", "application": "application：", "searchHeader": "Query By Criteria", "searchBtn": "Query", "resetBtn": "Reset", "highSearch": "Advanced Query", "queryList": "Query List", "add": "add", "edit": "Edit", "preview": "Preview", "detail": "Detail", "currentYearDetails": "Current Year Details List", "upload": "upload", "delete": "Delete", "download": "download", "chooseOrg": "choose orgin", "chooseArea": "choose area", "choosePower": "choose permission", "addPerson": "add", "addUser": "adduser", "bindRegion": "binding region", "freeze": "freeze", "unfreeze": "unfreeze", "passwordReset": "password reset", "enabled": "enabled", "notUse": "disabled", "addDicType": "add type", "sendAgain": "send again", "disabled": "disabled", "save": "save", "return": "return", "done": "done", "cancel": "cancel", "choose": "Please Choose", "returnTop": "Top", "filter": "Filter：", "settings": "Settings", "profile": "Change password", "logout": "Logout", "help": "Help", "notifications": "Notifications", "seeAll": "See more the message", "have": "You have", "fixHeader": "Fixed header", "fixAside": "Fixed aside", "foldAside": "Folded aside", "dockAside": "Dock aside", "boxLayout": "Boxed layout", "No": "No", "handle": "<PERSON><PERSON>", "uploadFile": "Upload File", "placeholderFile": "Choose <PERSON>", "uploadAction": "Uploading", "column": "Column optional", "close": "Close", "clear": "Clear", "today": "Today", "definedList": "Defined List", "deleteTip": "You sure you want to delete it？", "deleteChild": "Please delete submenu first!", "chooseRolePerm": "please choose role permission!", "chooseOneOrg": "please choose one organization!", "chooseOneOrEditOrg": "please choose or edit one organization!", "chooseOneOpt": "please choose one operation!", "parentMenu": "parentMenu", "addChildren": "Add Children", "order": "Adjust the order", "dragOrder": "Please drag the adjustment order"}, "company": {"addTitle": "add company", "editTitle": "edit company", "detailTitle": "company detail", "name": "Name", "placeholderName": "Please Input Company Name", "type": "Type", "placeholderType": "Please select at least one company type", "number": "Number", "placeholderNum": "Please Input Company Number", "tel": "Tel", "placeholderTel": "Please Input Company Tel/Phone", "linkName": "LinkName", "placeholderLinkName": "Please Input LinkName", "linkPhone": "LinkPhone", "placeholderLinkPhone": "Please Input LinkPhone", "linkTel": "LinkTel", "placeholderLinkTel": "Please Input LinkTel", "address": "Address", "placeholderAddress": "Please Input Company Address", "linkEmail": "LinkEmail", "placeholderLinkEmail": "Please Input LinkEmail", "describe": "Describe", "placeholderDescribe": "Please Input Describe"}, "organization": {"addTitle": "Add Organization", "editTitle": "Edit Organization", "name": "Name", "placeholderName": "Please Input Company Name", "number": "Number", "placeholderNum": "Please Input Company Number", "describe": "Describe", "placeholderDescribe": "Please Input Company Describe"}, "personnel": {"workNum": "workNum", "placeholderWorkNum": "Please Input Work Number", "name": "Name", "placeholderName": "Please Input Personnel Name", "familyName": "LastName", "placeholderFamilyName": "please input last name", "firstName": "FirstName", "placeholderFirstName": "please input first name", "gender": "Gender", "man": "Man", "woman": "Woman", "birthday": "Birthday", "address": "Address", "placeholderAddress": "please input address", "tel": "Tel", "placeholderTel": "please input Tel/Phone", "email": "Email", "placeholderEmail": "please input email", "setUser": "setUser", "userYes": "Yes", "userNo": "No", "describe": "Describe", "placeholderDescribe": "please input describe", "organization": "Organization", "isUser": "isUser", "status": "Status", "editPersonInfo": "EditPersonInfo", "lookPersonInfo": "PersonInfo Detail", "editOrgInfo": "EditOrganization", "sureOn": "Are you sure you want to enable it?", "sureOff": "Are you sure you want to disable it?", "roleName": "<PERSON><PERSON><PERSON>", "selectGroup": "userGroup", "placeholderRoleName": "please choose role name", "placeholdersetUser": "please choose set user", "choseRoleOrUserGroup": "User group and role name of a second choice", "choseDisabled": "Please select a disabled person", "choseEnabled": "Please select the people who are enabled", "notChosenOpt": "System administrator can not disable / enable", "loginUserNotOpt": "Login user can not disable", "saveBindArea": "save and go to bind area"}, "log": {"clear": "Clear", "export": "Export", "name": "Name", "placeholderName": "please input user name", "type": "Type", "startTime": "StartTime", "endTime": "EndTime", "createTime": "CreateTime", "target": "Target", "remark": "Remark", "URL": "URL", "IP": "IP", "all": "All"}, "param": {"addParam": "Add Parameter", "editParam": "Edit Parameter", "name": "Name", "placeholderName": "Please input parameter name", "serial": "Serial", "placeholderSerial": "Please input parameter serial", "value": "Value", "placeholderValue": "Please input parameter value", "typeSerial": "TypeSerial", "placeholderTypeSerial": "Please input parameter TypeSerial", "typeValue": "TypeValue", "placeholderTypeValue": "Please input parameter TypeValue", "createTime": "CreateTime", "content": "Content"}, "dict": {"editDict": "Edit Dict Type", "addDict": "Add Dict Type", "addDictValue": "Add Dict Value", "editDictValue": "Edit Dict Value", "typeName": "TypeName", "placeholderTypeName": "Please input dict TypeName", "typeSerial": "TypeSerial", "placeholderTypeSerial": "Please input dict TypeSerial", "valueName": "ValueName", "placeholderValueName": "Please input dict value name", "value": "Value", "placeholderValue": "Please input dict value", "parentValue": "parentValue", "describe": "describe", "placeholderDescribe": "Please input dict describe", "LookValue": "Look Dict Value", "valueDetail": "Dict Detail", "status": "Status"}, "region": {"chooseRegion": "Please select the area!", "chooseUser": "Please add users!", "addRegion": "Add Region", "editRegion": "Edit Region", "name": "Name", "placeholderName": "Please input region name", "serial": "Serial", "placeholderSerial": "Please input region serial", "describe": "Describe", "placeholderDescribe": "Please input region describe"}, "permission": {"addPermission": "Add Permission", "editPermission": "Edit Permission", "name": "Name", "icon": "Icon", "placeholderName": "Please input permission name", "code": "Code", "placeholderCode": "Please input permission code", "URL": "URL", "placeholderURL": "Please input permission url", "describe": "Describe", "placeholderDescribe": "Please input permission describe", "resourceType": "Type", "orderBy": "orderBy", "placeholderOrderBy": "Please input positive integer"}, "role": {"name": "Name", "placeholderName": "Please input role name", "serial": "Serial", "placeholderSerial": "Please input role serial", "createName": "CreateName", "createRole": "CreateRole", "content": "Content", "placeholderContent": "Please input role content", "editInfo": "EditRole", "roleDetail": "Role Detail", "addRole": "Add Role", "editPermission": "EditPermission", "grantRole": "RoleAuthorization", "grantGroup": "GroupAuthorization", "grantPromission": "PrommissionAuthorization", "grantData": "DataAuthorization", "selfGrantData": "CustomDataAuthorization", "chooseGrantPromission": "Please choose promission authorization", "lookGrantPromission": "Click View Permission", "chooseGrantData": "Please choose data authorization", "lookGrantData": "Click View Data", "selfGrantDataTitle": "Custom organization authority", "selfGrantProTitle": "Custom permissions authorization", "lookGrantPermission": "Look permissions authorization ", "GrantOrgNotSpace": "Data authorization can not be empty", "chooseRoleNeeded": "Please choose role"}, "userGroup": {"addUserGroup": "Add User Group", "editUserGroup": "Edit User Group", "userGroupDetail": "User Group Detail", "groupInfo": "Group Info", "userInfo": "User Info", "userDetail": "User Detail", "phone": "Phone", "name": "Name", "placeholderName": "Please input group name", "serial": "Serial", "placeholderSerial": "Please input group serial", "describe": "Describe", "placeholderDescribe": "Please input group describe", "chooseRole": "ChooseRole", "placeholderRole": "Please choose role", "chooseUser": "Choose User", "userName": "User", "userRole": "User Role", "userGroupRole": "User Group Role", "placeholderUserName": "Please input user name", "organization": "Organization", "placeholderOrganization": "Please input organization", "workNum": "WorkNum", "placeholderWorkNum": "Please input work number", "personName": "PersonName", "status": "Status", "bindArea": "Bound Region", "freeze": "Are you sure you want to freeze?", "unfreeze": "Are you sure you want to unfreeze?", "restPsd": "Are you sure you want to reset your password?", "choseFreezePerson": "Please select a frozen person", "choseNormalPerson": "Please select the normal state of personnel", "notChosenFreezeOrNormal": "System administrator can not freeze / thaw"}, "blocks": {"editBlocks": "Edit Blocks", "addBlocks": "Add Blocks", "name": "Name", "placeholderName": "Please input blocks name", "type": "Type", "classify": "Classify", "code": "Code", "placeholderCode": "Please input blocks code", "url": "URL", "placeholderUrl": "Please input blocks url", "modal": "<PERSON><PERSON><PERSON>", "width": "<PERSON><PERSON><PERSON>", "way": "Way", "blocksThumbnail": "<PERSON><PERSON><PERSON><PERSON>", "describe": "Describe", "placeholderDescribe": "Please input blocks describe", "setTemplate": "Configure template", "choseSetTemplate": "Please select the configured template", "templateName": "TemplateName", "placeholderTplName": "Please enter the name of the template", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>"}, "monitor": {"statusTitle": "System Status", "env": "Environment", "dataCenter": "Data Center", "currentTime": "Current Time", "upTime": "System Uptime", "threshold": "Below Renew Threshold", "registerTitle": "Instances Registered", "app": "App", "instance": "InstanceID", "status": "Status", "generalInfo": "General Info", "total": "Total Available Memory", "usage": "Current Memory Usage", "CPU": "Number of CPU", "address": "Instance IP Address", "instanceStatus": "Instance status", "health": "Health"}, "metrics": {"title": "Application Metrics", "refresh.button": "Refresh", "updating": "Updating...", "jvm": {"memory": {"title": "Memory", "total": "Total Memory(M/M)", "heap": "Heap Memory(M/M)", "nonheap": "Non-Heap Memory(M/M)"}, "threads": {"title": "Threads", "all": "All", "runnable": "Runnable", "timedwaiting": "Timed waiting", "waiting": "Waiting", "blocked": "Blocked", "dump": {"title": "Threads dump", "id": "Id: ", "blockedtime": "Blocked Time", "blockedcount": "Blocked Count", "waitedtime": "Waited Time", "waitedcount": "Waited Count", "lockname": "Lock name", "stacktrace": "Stacktrace", "show": "Show Stacktrace", "hide": "<PERSON>de Stacktrace"}}, "gc": {"title": "Garbage collections", "marksweepcount": "<PERSON> Sweep count", "marksweeptime": "<PERSON>weep time", "scavengecount": "Scavenge count", "scavengetime": "Scavenge time"}, "http": {"title": "HTTP requests (events per second)", "active": "Active requests:", "total": "Total requests:", "table": {"code": "Code", "count": "Count", "mean": "Mean", "average": "Average"}, "code": {"ok": "Ok", "notfound": "Not found", "servererror": "Server Error"}}}, "servicesstats": {"title": "Services statistics (time in millisecond)", "table": {"name": "Service name", "count": "Count", "mean": "Mean", "min": "Min", "max": "Max", "p50": "p50", "p75": "p75", "p95": "p95", "p99": "p99"}}, "cache": {"title": "Cache statistics", "cachename": "Cache name", "hits": "<PERSON><PERSON>", "misses": "<PERSON><PERSON>", "gets": "<PERSON><PERSON>", "puts": "<PERSON><PERSON>", "removals": "<PERSON><PERSON>", "evictions": "<PERSON><PERSON>", "hitPercent": "<PERSON><PERSON> Hit %", "missPercent": "<PERSON><PERSON> %", "averageGetTime": "Average get time (µs)", "averagePutTime": "Average put time (µs)", "averageRemoveTime": "Average remove time (µs)"}, "datasource": {"usage": "Usage", "title": "DataSource statistics (time in millisecond)", "name": "Pool usage", "count": "Count", "mean": "Mean", "min": "Min", "max": "Max", "p50": "p50", "p75": "p75", "p95": "p95", "p99": "p99"}}, "gateway": {"title": "Gateway", "routes": {"title": "Current routes", "url": "URL", "service": "service", "servers": "Available servers", "error": "Warning: no server available!"}, "refresh": {"button": "Refresh"}}, "tracker": {"title": "Real-time user activities", "table": {"userlogin": "User", "ipaddress": "IP Address", "userAgent": "User agent", "page": "Current page", "time": "Time"}}, "sessions": {"title": "Active sessions for [<b>{{username}}</b>]", "table": {"ipaddress": "IP address", "useragent": "User Agent", "date": "Date", "button": "Invalidate"}, "messages": {"success": "<strong>Session invalidated!</strong>", "error": "<strong>An error has occurred!</strong> The session could not be invalidated."}}, "health": {"title": "Health Checks", "refresh.button": "Refresh", "stacktrace": "Stacktrace", "details": {"details": "Details", "properties": "Properties", "name": "Name：", "value": "Value：", "error": "Error"}, "indicator": {"discoveryComposite": "Discovery Composite", "refreshScope": "Microservice Refresh Scope", "configServer": "Microservice Config Server", "hystrix": "Hystrix", "diskSpace": "Disk space", "mail": "Email", "db": "Database"}, "table": {"service": "Service name", "status": "Status"}, "status": {"UP": "UP", "DOWN": "DOWN"}}, "configuration": {"title": "Configuration", "filter": "Filter (by prefix)", "table": {"prefix": "Prefix", "properties": "Properties"}}, "activate": {"title": "Activation", "messages": {"success": "<strong>Your user account has been activated.</strong> Please <a class=\"alert-link\" href=\"\" ng-click=\"vm.login()\">sign in</a>.", "error": "<strong>Your user could not be activated.</strong> Please use the registration form to sign up."}}, "logs": {"title": "Logs", "nbloggers": "loggers.", "filter": "Filter", "table": {"name": "Name", "level": "Level"}}, "newsTpl": {"addTpl": "Add News Template", "editTpl": "Edit News Template", "name": "Name", "placeholderName": "Please input template name", "type": "TplType", "newsType": "NewsType", "content": "Content", "placeholdreContent": "Please input template content", "quote": "Quote", "placeholderQuote": "Please input quote template"}, "News": {"title": "Title", "placeholderTitle": "Please input news title", "type": "NewsType", "sendStatus": "SendStatus", "readStatus": "ReadStatus", "content": "Content", "sendTime": "SendTime", "readTime": "ReadTime", "newsDetail": "News Detail", "receiver": "Receiver", "doneRead": "SetToRead", "choseUnread": "Please select an unread message"}}
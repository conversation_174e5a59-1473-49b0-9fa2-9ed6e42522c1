(function () {
    app.controller("peerReviewController", ['comService', '$rootScope', '$scope', '$state', '$stateParams', '$modal', 'peerReviewService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, $state, $stateParams, $modal, peerReviewService, inform, Trans, AgreeConstant, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //季度下拉框数据源
            $scope.quarterSelect = [{
                value: '0',
                label: '第1季度'
            }, {
                value: '1',
                label: '第2季度'
            }, {
                value: '2',
                label: '第3季度'
            }, {
                value: '3',
                label: '第4季度'
            }];

            //季度展示Map
            $scope.quarterMap = {
                "0": '第1季度',
                "1": '第2季度',
                "2": '第3季度',
                "3": '第4季度'
            }

            //评审级别下拉框数据源
            $scope.levelSelect = [{
                value: '0',
                label: '一级'
            }, {
                value: '1',
                label: '二级'
            }, {
                value: '2',
                label: '三级'
            }];

            //评审级别展示Map
            $scope.levelMap = {
                "0": '一级',
                "1": '二级',
                "2": '三级'
            }

            //产品线下拉框数据源
            $scope.lineSelect = [{
                value: '0',
                label: '金融'
            }, {
                value: '1',
                label: '物流'
            }, {
                value: '2',
                label: '新零售'
            }, {
                value: '3',
                label: '平台'
            }, {
                value: '4',
                label: '经典创新'
            }];

            //产品线展示Map
            $scope.lineMap = {
                "0": '金融',
                "1": '物流',
                "2": '新零售',
                "3": '平台',
                "4": '经典创新'
            };

            //评审结果下拉框数据源
            $scope.resSelect = [{
                value: '0',
                label: '有条件通过'
            }, {
                value: '1',
                label: '通过'
            }, {
                value: '2',
                label: '不通过'
            }, {
                value: '3',
                label: '未评审'
            }];

            //评审结果展示Map
            $scope.resMap = {
                "0": '有条件通过',
                "1": '通过',
                "2": '不通过',
                "3": '未评审'
            };

            //是否跟踪下拉框数据源
            $scope.trackSelect = [{
                value: '0',
                label: '是'
            }, {
                value: '1',
                label: '否'
            }];

            //是否跟踪展示Map
            $scope.trackMap = {
                "0": '是',
                "1": '否'
            };
            //下载时间

            $scope.datepicker = {};
            $scope.toggleMin = toggleMin;
            toggleMin();

            //初始化页面信息
            initTime();
            initPages();
            $scope.dataList = [];
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */


            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */


            //时间格式化
            function format(time, format) {
                if (!time) {
                    return null;
                }
                var t = new Date(time);
                var tf = function (i) {
                    return (i < 10 ? '0' : '') + i
                };
                return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
                    switch (a) {
                        case 'yyyy':
                            return tf(t.getFullYear());
                            break;
                        case 'MM':
                            return tf(t.getMonth() + 1);
                            break;
                        case 'mm':
                            return tf(t.getMinutes());
                            break;
                        case 'dd':
                            return tf(t.getDate());
                            break;
                        case 'HH':
                            return tf(t.getHours());
                            break;
                        case 'ss':
                            return tf(t.getSeconds());
                            break;
                    }
                })
            }

            //开始时间
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;    //开始时间
                $scope.openedEnd = false;
            };

            //结束时间
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;    //结束时间
            };

            //重置
            $scope.rest = function () {
            	initTime();
            }
            //获取当前选定时间
            function toggleMin() {
                $scope.datepicker.currentDate = $scope.datepicker.currentDate ? null : new Date();
            }
            function initPages() {
                //获取产品线
                $scope.productLineList = [];
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    $scope.getData();
                });
            }
            /**
    		 * 初始化检索条件开始时间 及 结束时间
    		 */
            function initTime(endDate){
    			if (endDate==null || endDate==="" ){
    				$scope.endTime = inform.format(new Date(),'yyyy-MM-dd');//获取当前系统时间
    			} 
    			var time = $scope.endTime.split("-");
    			var start = time[0]+"/01"+"/01";
    			$scope.startTime = inform.format(start,'yyyy-MM-dd');
    			//对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
            }
            /**
             * 获取所有数据以分页的形式
             */
            $scope.getData = function() {

                $scope.dataList = [];
                var urlData = {
                    'startTime': format($scope.startTime, 'yyyy-MM-dd'),
                    'endTime': format($scope.endTime, 'yyyy-MM-dd')
                };
                peerReviewService.getData(urlData).then(function (data) {
                    if (data.code===AgreeConstant.code) {
                        data.data = angular.fromJson(data.data);

                        angular.forEach(data.data, function (res, index) {
                            $scope.dataList.push(res);
                        });
                    }

                },
                function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            };


            //生成Excel表格
            $scope.toBasicsExcel = function () {

                var urlData = {
                    'startTime': format($scope.startTime, 'yyyy-MM-dd'),
                    'endTime': format($scope.endTime, 'yyyy-MM-dd')
                };

                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function () {
                            return "确定要下载吗！";
                        }
                    }
                });
                modalInstance.result.then(function () {
                    //开启遮罩层
                    inform.showLayer("数据过多请耐心等待。。。");
                    $http.post(
                        $rootScope.getWaySystemApi + 'peer/loadBasicsExcel',
                        urlData,
                        {
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': 'Bearer ' + LocalCache.getSession("token") || ''
                            },
                            responseType: 'arraybuffer'//防止中文乱码
                        }
                    ).success(function (data) {
                        //如果是IE浏览器
                        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                            var csvData = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
                            window.navigator.msSaveOrOpenBlob(csvData, '04 基础数据表.xlsx');
                        }
                        //google或者火狐浏览器
                        else {
                            var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
                            var objectUrl = URL.createObjectURL(blob);
                            var aForExcel = $("<a download='04 基础数据表.xlsx'><span class='forExcel'>下载基础数据表</span></a>").attr("href", objectUrl);
                            $("body").append(aForExcel);
                            $(".forExcel").click();
                            aForExcel.remove();
                        }

                        // 关闭遮罩层
                        inform.closeLayer();
                        inform.common("下载成功!");
                    });


                });
//			} else {
//				inform.common(Trans("开始时间不得大于结束时间,且不得大于当前日期！"));
//			}
            };
            $scope.toExcel = function () {

                var urlData = {
                    'excelName': 'app.office.report_0004',
                    'startTime': format($scope.startTime, 'yyyy-MM-dd'),
                    'endTime': format($scope.endTime, 'yyyy-MM-dd')
                };

                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function () {
                            return "确定要下载吗！";
                        }
                    }
                });
                modalInstance.result.then(function () {
                    //开启遮罩层
                    inform.showLayer("下载中。。。。。。");
                    $http.post(
                        $rootScope.getWaySystemApi + 'peer/loadExcel',
                        urlData,
                        {
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': 'Bearer ' + LocalCache.getSession("token") || ''
                            },
                            responseType: 'arraybuffer'//防止中文乱码
                        }
                    ).success(function (data) {
                        //如果是IE浏览器
                        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                            var csvData = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
                            window.navigator.msSaveOrOpenBlob(csvData, '04 同行评审情况.xlsx');
                        }
                        //google或者火狐浏览器
                        else {
                            var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
                            var objectUrl = URL.createObjectURL(blob);
                            var aForExcel = $("<a download='04 同行评审情况.xlsx'><span class='forExcel'>下载评审报表</span></a>").attr("href", objectUrl);
                            $("body").append(aForExcel);
                            $(".forExcel").click();
                            aForExcel.remove();
                        }

                        // 关闭遮罩层
                        inform.closeLayer();
                        inform.common("下载成功!");
                    });


                });
//			} else {
//				inform.common(Trans("开始时间不得大于结束时间,且不得大于当前日期！"));
//			}
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
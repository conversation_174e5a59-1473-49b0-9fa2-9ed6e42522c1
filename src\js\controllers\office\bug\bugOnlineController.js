
(function () {
    app.controller("bugOnlineController", ['bugOnlineService', '$rootScope', '$scope','$state', 'inform', 'Trans', 'AgreeConstant', 'comService', 'LocalCache',
        function (bugOnlineService, $rootScope, $scope,$state, inform, Trans, AgreeConstant,comService,LocalCache) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //获取缓存
        	$scope.formInput = LocalCache.getObject('bugOnline_formRefer');
        	//对原缓存进行覆盖
        	LocalCache.setObject("bugOnline_formRefer",{});
        	//设置默认时间
        	if ($scope.formInput.startTime==null){
        		var date = inform.format(new Date(), 'yyyy-MM-dd').split('-');
        		 //默认开始时间
                $scope.formInput.startTime = date[0]+'-01-01';
        	}
        	//修改按钮和删除按钮的权限
            $scope.save=false;
            $scope.delete=false;

            $scope.pages = {
                goNum:null, // 初始化跳转页码
                star:0, //开始条数
                end:0, //结束条数
                total:0, // 总条数
                size:'20', //每页条数
                pageNum:AgreeConstant.pageNum //默认页
            };
            $scope.isLinkProjectMap = [{
                value: '0',
                label: '已关联'
            }, {
                value: '1',
                label: '未关联'
            }];

            $scope.getData = getData; 			// 分页相关函数

            getData($scope.pages.pageNum);		//在刷新页面时调用该方法

            //初始化页面信息
            initPages();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //判断按钮是否具有权限
            getButtonPermission();

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 获取按钮权限
             */
            function getButtonPermission(){
                var buttons = {
                    'Button-bugOnlineController-save':'save',
                    'Button-bugOnlineController-delete':'delete'
                };
                var urlData = {
                    'userId':LocalCache.getSession("userId"),
                    'parentPermission':'ButtonBugOnlineController',
                    'buttons':buttons
                };
                comService.getButtonPermission(urlData,$scope);
            }

            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 210);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 75);
            }

            //重置
            $scope.rest = function () {
                $scope.formInput = {};
                //默认开始时间
                var date = inform.format(new Date(), 'yyyy-MM-dd').split('-');
                $scope.formInput.startTime = date[0]+'-01-01';
            };

            function initPages() {
                //获取所有项目名称
                $scope.projectList = [];
                comService.getProjectsName().then(function (data) {
                    $scope.projectList = angular.fromJson(data.data);
                });
                //获取员工信息
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function(data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                    }
                });
                //bug状态
                $scope.bugStatus = {};
                $scope.bugStatusMap = [];
                comService.getParamList('BugStatus', 'BugOnline').then(function (data) {
                    if (data.data) {
                        $scope.bugStatus = data.data;
                        angular.forEach($scope.bugStatus, function (res, index) {
                            $scope.bugStatusMap[res.param_code] = res.param_value;
                        });
                    }
                });
                //bug类型
                $scope.bugType = {};
                $scope.bugTypeMap = [];
                comService.getParamList('BugType', 'BugOnline').then(function (data) {
                    if (data.data) {
                        $scope.bugType = data.data;
                        angular.forEach($scope.bugType, function (res, index) {
                            $scope.bugTypeMap[res.param_code] = res.param_value;
                        });
                    }
                });
                //bug级别
                $scope.bugSeverity = {};
                $scope.bugSeverityMap = [];
                comService.getParamList('BugSeverity', 'BugOnline').then(function (data) {
                    if (data.data) {
                        $scope.bugSeverity = data.data;
                        angular.forEach($scope.bugSeverity, function (res, index) {
                            $scope.bugSeverityMap[res.param_code] = res.param_value;
                        });
                    }
                });
              //获取产品线
        		$scope.productLines = [];
        		comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
            		if (data.data) {
            			$scope.productLines =  data.data;
            		}
                });
            }

            //获取所有数据以分页的形式
            function getData(pageNum) {
                $scope.itemList = [];
                var urlData = {
                    'projectId': $scope.formInput.projectId,  			//项目
                    'title': $scope.formInput.title,                    //bug标题
                    'id': $scope.formInput.bugId,                    //bug编号
                    'developer': $scope.formInput.developer,            //开发负责人
                    'tester': $scope.formInput.tester,                  //测试负责人
                    'isLinkProject': $scope.formInput.isLinkProject,    //是否关联office项目
                    'proProjectName':$scope.formInput.proProjectName,
                    'productLine':$scope.formInput.productLine,			//产品线
                    'startTime': inform.format($scope.formInput.startTime, 'yyyy-MM-dd'),//开始时间
                    'endTime': inform.format($scope.formInput.endTime, 'yyyy-MM-dd'), //结束时间
                    'page': pageNum, 							// 分页页数
                    'pageSize': $scope.pages.size    					// 分页每页大小
                };
                bugOnlineService.getBugOnlineInfo(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            var jsonData = data.data;
                            $scope.itemList = jsonData.list;
                            if ($scope.itemList.length === 0) {
                                $scope.pages = inform.initPages(); 			//初始化分页数据
                                $scope.pages.size = '20';
                            } else {
                                // 分页信息设置
                                $scope.pages.total = jsonData.total;		// 页面总数
                                $scope.pages.star = jsonData.startRow;  	//页面起始数
                                $scope.pages.end = jsonData.endRow;  		//页面大小数
                                $scope.pages.pageNum = jsonData.pageNum;  	//页面页数
                            }

                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });

            }
            /**
             * 跳转至详情
             */
            $scope.bugDetail = function (id) {
            	LocalCache.setObject('bugOnline_formRefer', $scope.formInput);
            	$state.go('app.office.bugOnlineDetailController',{id:id});
            }

            /**
             * 获取下载信息
             */
            $scope.toExcel = function (){
                var urlData = {
                    'projectId': $scope.formInput.projectId,  			//项目
                    'title': $scope.formInput.title,                    //bug标题
                    'developer': $scope.formInput.developer,            //开发负责人
                    'tester': $scope.formInput.tester,                  //测试负责人
                    'isLinkProject': $scope.formInput.isLinkProject,    //是否关联office项目
                    'proProjectName':$scope.formInput.proProjectName,
                    'productLine':$scope.formInput.productLine,			//产品线
                    'startTime': inform.format($scope.formInput.startTime, 'yyyy-MM-dd'),//开始时间
                    'endTime': inform.format($scope.formInput.endTime, 'yyyy-MM-dd') //结束时间
                };
                inform.modalInstance("确定要下载吗?").result.then(function () {
                    inform.downLoadFile('bugOnline/toExcel',urlData,'线上问题数据.xlsx');
                });
            };

            /**
             * 删除线上问题信息
             */
            $scope.bugDel = function (id) {
                inform.modalInstance("确定要删除吗？").result.then(function () {
                    $scope.itemList = [];
                    var urlData = {
                        'id': id  			//线上问题id;
                    };
                    bugOnlineService.deleteBugOnlineInfo(urlData).then(function (data) {
                            if (data.code === AgreeConstant.code) {
                                getData(1);
                            }
                            inform.common(data.message);
                        },
                        function (error) {
                            inform.common(Trans("tip.requestError"));
                        });
                });
            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

(function () {
    app.controller("performanceAssessController", ['comService','kpiRelationService','attendanceStatisticsService','$rootScope', '$state','$window','$scope','$stateParams', '$modal','inform','LocalCache','Trans','AgreeConstant','$http',
        function (comService,kpiRelationService,attendanceStatisticsService,$rootScope,$state,$window,$scope,$stateParams,$modal,inform,LocalCache,Trans,AgreeConstant,$http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //查询条件
            $scope.formRefer = {};
            //页面分页信息
            $scope.pages = {
                pageNum : '',   //分页页数
                size : '',      //分页每页大小
                total : ''      //数据总数
            };
            //折叠按钮控制
            $scope.down = true;
            $scope.Dshow = true;
            $scope.totalResult = true;
            $scope.titleFlag = false;
            // 初始化分页数据
            $scope.pages = inform.initPages();
            $scope.pages.size = '50';
            $scope.quarterSelect = [
                {
                    value: '上半年',
                    label: '上半年'
                },{
                    value: '全年',
                    label: '全年'
                }];
            $scope.quarterMonthMap = [{
                value: '上半年', statisDateStart: '01', statisDateEnd: '06'
            }, {
                value: '全年', statisDateStart: '01', statisDateEnd: '12'
            }];
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            initInfo();
            $scope.getData = getData;
            //最终考核结果选项
            $scope.LevelList = [
                {
                    value: 'A',
                    label: 'A'
                },{
                    value: 'B',
                    label: 'B'
                },{
                    value: 'C',
                    label: 'C'
                },{
                    value: 'D',
                    label: 'D'
                },{
                    value: 'E',
                    label: 'E'
                }];
            $scope.evaluateStatus = [{
                value: '1', label: '已评价'
            }, {
                value: '0', label: '未评价'
            }];
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150);
                $("#divTBDis").height(divHeight);
            }
            /**
             * 初始化
             */
            function initInfo() {
                initTime();
                //获取地区信息
                $scope.areaList = [];
                comService.getAreaList().then(function(data) {
                    $scope.areaList = data.data;
                });
                //获取员工岗位信息
                $scope.staffTitleList = [];
                comService.getParamList('STAFF_INFO_TITLE', 'NEW').then(function (data) {
                    $scope.staffTitleList = data.data;
                });
                //获取员工职称信息
                $scope.companyTitleList = [];
                comService.getParamList('STAFF_TITLE', 'NEW').then(function (data) {
                    $scope.companyTitleList = data.data;
                });
                //获取山东新北洋集团的下级部门信息
                $scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentList = data.data;
                });
                //查询条件读取缓存
                if(JSON.stringify(LocalCache.getObject('performanceAssessResult')) !== "{}"){
                    $scope.formRefer = LocalCache.getObject('performanceAssessResult');
                    LocalCache.setObject('performanceAssessResult', {});
                }
                getData();
            }
            /**
             * 初始化检索条件年度与季度
             */
            function initTime(){
                //当前时间（Date类型）
                var date = new Date();
                //得到上一个季度的第一天
                var lastQuarterFirstDay = new Date(date.getFullYear() , date.getMonth() - 3 , 1);
                var day = inform.format(lastQuarterFirstDay, 'yyyy-MM-dd').split("-");
                $scope.formRefer.year = $scope.formRefer.year != null ? $scope.formRefer.year : day[0];
                //设置季度
                var month = lastQuarterFirstDay.getMonth() + 1;
                //如果当前月份大于6 则默认考核周期为全年，否则为上半年
                $scope.formRefer.quarter = month>6?'全年':'上半年';
            }
            //查看考核详情
            $scope.selectAssessDetail = function (m) {
                getAssessDetailData(m);
                $("#assessDetail").click();
            }
            //查询考核详情
            function getAssessDetailData(m) {
                var urlData = {
                    'year':m.year,
                    'quarter':m.quarter,
                    'employeeId': m.employeeId
                };
                kpiRelationService.getAssessDetailData(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                             $scope.assessDetail=[];
                             $scope.sourceAssessDetail='';
                             $scope.lastAssess='';
                             //如果最终结果为D或者E,弹窗时 显示最终结果和考核结果说明
                             angular.forEach(data.data,function (index) {
                                 if(index.evaluateType === '资源考核人') {
                                     $scope.assessDetail.push(index);
                                     $scope.sourceAssessDetail = angular.copy(index);
                                     $scope.sourceAssessDetail.result = m.finalResult;
                                 }else if(index.evaluateType === '上次考核评价') {
                                     $scope.lastAssess = index;
                                 }else{
                                     $scope.assessDetail.push(index);
                                 }
                             });
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            //根据考核周期获取开始月份
            function getMonthByQuarterStart(year,quarter){
                for(var i= 0; i<$scope.quarterMonthMap.length;i++){
                    if($scope.quarterMonthMap[i].value  === quarter){
                        return year+'-'+$scope.quarterMonthMap[i].statisDateStart;
                    }
                }
                return '';
            }
            //根据考核周期获取结束月份
            function getMonthByQuarterEnd(year,quarter){
                for(var i= 0; i<$scope.quarterMonthMap.length;i++){
                    if($scope.quarterMonthMap[i].value  === quarter){
                        return year+'-'+$scope.quarterMonthMap[i].statisDateEnd;
                    }
                }
                return '';
            }
            //获取系研平均工作强度
            function getAttendanceTotalInfo(){
                var urlData={
                    'deptCodeList':['D010045','D010047','D010130','D010134','D010129','D010131'],
                    'statisDateStart':getMonthByQuarterStart($scope.formRefer.year,$scope.formRefer.quarter),
                    'statisDateEnd':getMonthByQuarterEnd($scope.formRefer.year,$scope.formRefer.quarter)
                }
                $scope.totalData=[];
                attendanceStatisticsService.getAttendanceTotalInfo(urlData).then(function(data) {
                        if (data.code===AgreeConstant.code) {
                            $scope.totalData = data.data;
                        }
                    },
                    function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            //获取表格数据
            function getData(pageNum) {
                //删除已加载冻结头部和部分列的HTML模板
                $scope.dataTableShow = 0;
                getAreaData();
                getDepartmentData();
                getTitleData();
                getManagerData();
                getAttendanceTotalInfo();
                var urlData = {
                    'employeeName':$scope.formRefer.employeeName,
                    'companyTitle':$scope.formRefer.companyTitle,
                    'department':$scope.formRefer.department,
                    'job':$scope.formRefer.job,
                    'area':$scope.formRefer.area,
                    'finalResult': $scope.formRefer.finalResult,
                    'year':$scope.formRefer.year,
                    'quarter':$scope.formRefer.quarter,
                    'finalAssessEvaluate':$scope.formRefer.finalAssessEvaluate,
                    'deviation':$scope.formRefer.deviation,
                    'currentPage':pageNum,
                    'pageSize':$scope.pages.size
                };
                kpiRelationService.getPerformanceAssessData(urlData).then(function (data) {
                        //重新加载冻结头部和部分列的HTML模板
                        $scope.dataTableShow = 1;
                        if (data.code===AgreeConstant.code) {
                            $scope.dataList = data.data.list;

                            if ($scope.dataList.length===0) {
                                $scope.pages = inform.initPages(); 			//初始化分页数据
                                inform.common(Trans("tip.noData"));
                            } else {
                                // 分页信息设置
                                $scope.pages.total = data.data.total;           // 页面数据总数
                                $scope.pages.star = data.data.startRow;         // 页面起始数
                                $scope.pages.end = data.data.endRow;            // 页面结束数
                                $scope.pages.pageNum = data.data.pageNum;       //页号
                            }
                            // 获取到跟踪单信息之后滚动条跳至缓存中保存的scrollTop
                            setTimeout(function () {
                                $window.scrollTo(0, $scope.formRefer.pageOffset || 0);
                                if ($scope.formRefer.subDivTBDisScrollTop) {
                                    $('#fixedLeftAndTop').parent().animate({scrollTop: $scope.formRefer.subDivTBDisScrollTop},10);
                                }

                            },500);
                            //调用DataTable组件冻结表头和左侧及右侧的列
                            setTimeout(showDataTable,300);
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

        /**
        *调用DataTable组件冻结表头和左侧及右侧的列
        */
        function showDataTable(){
            $('#fixedLeftAndTop').DataTable( {
                //可被重新初始化
                retrieve:       true,
                //自适应高度
                scrollY:        'calc(100vh - 350px)',
                scrollX:        true,
                scrollCollapse: true,
                //控制每页显示
                paging:         false,
                //冻结列（默认冻结左1）
                fixedColumns:   {
                    leftColumns: 3,
                    rightColumns: 2
                },
                //search框显示
                searching:      false,
                //排序箭头
                ordering:       false,
                //底部统计数据
                info:           false
            } );

        }
            //获取header数据
            function getAssessAnalysisHeader(pageNum) {
                var urlData = {
                    'employeeName':$scope.formRefer.employeeName,
                    'companyTitle':$scope.formRefer.companyTitle,
                    'department':$scope.formRefer.department,
                    'job':$scope.formRefer.job,
                    'area':$scope.formRefer.area,
                    'finalResult': $scope.formRefer.finalResult,
                    'year':$scope.formRefer.year,
                    'quarter':$scope.formRefer.quarter,
                    'finalAssessEvaluate':$scope.formRefer.finalAssessEvaluate,
                    'deviation':$scope.formRefer.deviation,

                };
                kpiRelationService.getAssessAnalysisHeader(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                            $scope.headerProportion = data.data.proportion;
                            $scope.dList = data.data.demployeeList;
                            $scope.dCount = data.data.dcount;
                            $scope.eList = data.data.eemployeeList;
                            $scope.eCount = data.data.ecount;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            //获取考核结果区域分析
            function getAreaData(pageNum) {
                getAssessAnalysisHeader();
                var urlData = {
                    'employeeName':$scope.formRefer.employeeName,
                    'companyTitle':$scope.formRefer.companyTitle,
                    'department':$scope.formRefer.department,
                    'job':$scope.formRefer.job,
                    'area':$scope.formRefer.area,
                    'finalResult': $scope.formRefer.finalResult,
                    'year':$scope.formRefer.year,
                    'quarter':$scope.formRefer.quarter,
                    'finalAssessEvaluate':$scope.formRefer.finalAssessEvaluate,
                    'deviation':$scope.formRefer.deviation,

                };
                kpiRelationService.getAnalysisDataByArea(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                            $scope.areaData = data.data;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            //获取考核结果部门分析
            function getDepartmentData(pageNum) {
                var urlData = {
                    'employeeName':$scope.formRefer.employeeName,
                    'companyTitle':$scope.formRefer.companyTitle,
                    'department':$scope.formRefer.department,
                    'job':$scope.formRefer.job,
                    'area':$scope.formRefer.area,
                    'finalResult': $scope.formRefer.finalResult,
                    'year':$scope.formRefer.year,
                    'quarter':$scope.formRefer.quarter,
                    'finalAssessEvaluate':$scope.formRefer.finalAssessEvaluate,
                    'deviation':$scope.formRefer.deviation,
                };
                kpiRelationService.getAnalysisDataByDepartment(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                            $scope.departmentData = data.data;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            //获取考核结果职称分析
            function getTitleData(pageNum) {
                var urlData = {
                    'employeeName':$scope.formRefer.employeeName,
                    'companyTitle':$scope.formRefer.companyTitle,
                    'department':$scope.formRefer.department,
                    'job':$scope.formRefer.job,
                    'area':$scope.formRefer.area,
                    'finalResult': $scope.formRefer.finalResult,
                    'year':$scope.formRefer.year,
                    'quarter':$scope.formRefer.quarter,
                    'finalAssessEvaluate':$scope.formRefer.finalAssessEvaluate,
                    'deviation':$scope.formRefer.deviation,
                    'currentPage':pageNum,
                    'pageSize':$scope.pages.size
                };
                kpiRelationService.getAnalysisDataByTitle(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                            $scope.titleData = data.data;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            //获取管理岗考核结果分析
            function getManagerData(pageNum) {
                var urlData = {
                    'employeeName':$scope.formRefer.employeeName,
                    'companyTitle':$scope.formRefer.companyTitle,
                    'department':$scope.formRefer.department,
                    'job':$scope.formRefer.job,
                    'area':$scope.formRefer.area,
                    'finalResult': $scope.formRefer.finalResult,
                    'year':$scope.formRefer.year,
                    'quarter':$scope.formRefer.quarter,
                    'finalAssessEvaluate':$scope.formRefer.finalAssessEvaluate,
                    'deviation':$scope.formRefer.deviation,
                    'currentPage':pageNum,
                    'pageSize':$scope.pages.size
                };
                kpiRelationService.getManagerAnalysisData(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                            $scope.managerData = data.data;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            /**
             *下载考核结果分析表
             */
            $scope.downloadExcel = function() {
                var urlData = {
                    'employeeName':$scope.formRefer.employeeName,
                    'companyTitle':$scope.formRefer.companyTitle,
                    'department':$scope.formRefer.department,
                    'job':$scope.formRefer.job,
                    'area':$scope.formRefer.area,
                    'finalResult': $scope.formRefer.finalResult,
                    'year':$scope.formRefer.year,
                    'quarter':$scope.formRefer.quarter,
                    'finalAssessEvaluate':$scope.formRefer.finalAssessEvaluate,
                    'deviation':$scope.formRefer.deviation
                };
                inform.modalInstance("确定要下载考核结果分析表吗？").result.then(function() {
                    inform.downLoadFile('kpiRelation/downloadExcel',urlData,"考核结果分析表"+'-'+$scope.formRefer.year+$scope.formRefer.quarter+".xlsx");
                });
            };
            //点击“姓名”跳转到个人看板页面
            $scope.toEvaluate = function (m){
                $scope.formRefer.subDivTBDisScrollTop = $('#fixedLeftAndTop').parent().scrollTop();
                $scope.formRefer.pageOffset = $window.pageYOffset;

                //保存查询条件
                LocalCache.setObject('performanceAssessResult', $scope.formRefer);
                LocalCache.setObject('personDataBoardEmployee',
                {'name':m.employeeName,'loginName':m.loginName});
                //跳转到个人看板
                $state.go('app.index_bench',{
                    empId: m.employeeId,
                    years:m.year,
                    quarter:m.quarter === '全年'?5:6});
            }
            //切换header折叠
            $scope.getDetail =  function () {
                $scope.Dshow = $scope.Dshow !== true;
            }
            /**
             * 重置
             */
            $scope.clearParams = function() {
                $scope.formRefer = {};
                initTime();
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
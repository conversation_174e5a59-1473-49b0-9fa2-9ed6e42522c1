
(function () {
    app.controller("attendanceDetailController", [ '$scope','$rootScope','$stateParams','$state','LocalCache','comService','inform','Trans','attendanceStatisticsService','AgreeConstant','$modal',
        function ($scope,$rootScope, $stateParams,$state,LocalCache,comService,inform,Trans,attendanceStatisticsService,AgreeConstant,$modal) {

    		$scope.formRefer = {};
    		$scope.formRefer.orderType = 'desc'
            $scope.formRefer.orderField = 'workIntensityTotal';
            $scope.staffTitleSelect = [
            {'value':'全部'},
            {'value':'副部长'},
            {'value':'部长助理'},
            {'value':'主任'},
            {'value':'副主任'},
            {'value':'架构师'},
            {'value':'产品经理'},
            {'value':'项目经理'}];
            //页面分页信息
            $scope.pages = {
                pageNum : '',   //分页页数
                size : '',      //分页每页大小
                total : ''      //数据总数
            };
            // 初始化分页数据
            $scope.pages = inform.initPages();
            //设置列表的高度
            setTimeout(setDivHeight,500);
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
    		$scope.resetParam = resetParam;
    		$scope.getData = getData;
    		initData();
            getData();

            //设置列表的高度
            function setDivHeight(){
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 205);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 85);
                //网页可见区域宽度
                var clientWidth = document.body.clientWidth;
                var divWidth = clientWidth - 220;
                $("#divTBDis").width(divWidth);
                $("#subDivTBDis").width(divWidth);
            }
            function initData(){
                //初始化部门
				$scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentList = comService.getDepartment(data.data);
                });
				$scope.projectLine = [];
                comService.queryEffectiveParam('PRODUCT_TYPE','PRODUCT_TYPE').then(function (data) {
					if (data.data) {
						$scope.projectLine = data.data;
					}
				});
				$scope.areaList = [];
                comService.getAreaList().then(function (data) {
					if (data.data) {
					    var areaMap = data.data;
                        angular.forEach(areaMap, function (area) {
                            var dot={
                                'paramCode':area['paramCode'],
                                'paramValue':area['paramName']
                            }
                            $scope.areaList.push(dot);
                        });

					}
				});
				//小组集合
				$scope.groupList = [];
                comService.getGroupInfoList().then(function (data) {
					if (data.data) {
						$scope.groupList = data.data;
					}
				});
                resetParam();
            }
            function resetParam(){
                initTime();
                $scope.formRefer.department='';
                $scope.formRefer.area='';
                $scope.formRefer.productLine='';
                $scope.formRefer.groupCode='';
                $scope.formRefer.personName='';
                $scope.formRefer.staffTitle='';
            }
            function initTime(){
                var date = new Date();
                //设置为1号，防止31号时获取到当月
                date.setDate(1);
                date.setMonth(date.getMonth()-1);
                $scope.formRefer.startTime = inform.format(date,"yyyy-MM");
                $scope.formRefer.endTime = inform.format(date,"yyyy-MM");
            }

            function getData(pageNum){
                var title = '';
                angular.forEach($scope.formRefer.staffTitle, function (staffTitle, i) {
                    title = title +staffTitle +',';
                });
                var urlData={
                    'attendCycleStart':$scope.formRefer.startTime,
                    'attendCycleEnd':$scope.formRefer.endTime,
                    'deptCode':$scope.formRefer.department,
                    'regionCode':$scope.formRefer.area,
                    'productLineCode':$scope.formRefer.productLine,
                    'teamCode':$scope.formRefer.groupCode,
                    'personName':$scope.formRefer.personName,
                    'staffTitle':title,
                    'orderField':$scope.formRefer.orderField,
                    'orderType':$scope.formRefer.orderType,
                    'currentPage':pageNum,
                    'pageSize':$scope.pages.size
                }
                $scope.attendanceDetailInfo = [];
                attendanceStatisticsService.getAttendanceDetailInfoList(urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        $scope.attendanceDetailInfo = data.data.list;
                        if ($scope.attendanceDetailInfo.length===0) {
                            $scope.pages = inform.initPages(); 			//初始化分页数据
                            inform.common(Trans("tip.noData"));
                        } else {
                        // 分页信息设置
                            $scope.pages.total = data.data.total;           // 页面数据总数
                            $scope.pages.star = data.data.startRow;         // 页面起始数
                            $scope.pages.end = data.data.endRow;            // 页面结束数
                            $scope.pages.pageNum = data.data.pageNum;       //页号
                        }
                    } else {
                        inform.common(data.message);
                    }
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            //排序
        $scope.goOrder = function(orderField){
            $scope.formRefer.orderField = orderField;
            if($scope.formRefer.orderType === 'desc'){
                $scope.formRefer.orderType = 'asc';
            }else{
                $scope.formRefer.orderType = 'desc'
            }
            getData(1);
        }

        $scope.downloadAttendanceDetail = function(){
             inform.modalInstance("确定要下载考勤明细统计表吗？").result.then(function() {
                var urlData={
                    'attendCycleStart':$scope.formRefer.startTime,
                    'attendCycleEnd':$scope.formRefer.endTime,
                    'deptCode':$scope.formRefer.department,
                    'regionCode':$scope.formRefer.area,
                    'productLineCode':$scope.formRefer.productLine,
                    'teamCode':$scope.formRefer.groupCode,
                    'orderField':$scope.formRefer.orderField,
                    'orderType':$scope.formRefer.orderType
                }
                 inform.downLoadFile('hr_download/for_emp_attend',urlData,"考勤明细统计表"+inform.format(new Date(),'yyyy-MM-dd')+".xlsx");
            });
        }
        //开始时间
        $scope.openSearchOnboardTime = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openSearchOnboardTime1 = true;
            $scope.openStopOnboardTime1 = false;
        };
        //截止时间
         $scope.openStopOnboardTime = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openSearchOnboardTime1 = false;
            $scope.openStopOnboardTime1 = true;
        };

      }]);
})();
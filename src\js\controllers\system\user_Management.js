(function() {
  'use strict';
  app.controller("user_Management", ['$rootScope', '$scope', '$stateParams', '$modal', 'inform', 'Trans', 'SystemService', 'LocalCache','AgreeConstant',
    function($rootScope, $scope, $stateParams, $modal, inform, Trans, SystemService, LocalCache,AgreeConstant) {
      var interfaceMap ={};

      $scope.loginUserId = LocalCache.getSession('userId');
      // 排序
      $scope.title = 'userId';
      $scope.desc = true;
      $scope.freeze = AgreeConstant.freezeStatus;
      $scope.unfreeze = AgreeConstant.unfreezeStatus;
      $scope.resetPsd = 'psd';

      $scope.map = {}; //用户条件
      $scope.goBindAreaArray =[];
      $scope.checked = [];
      // 查询条件
      $scope.inputData ={
        row:[
          {
            name:'登录账号',
            model:'',
            holder:'请输入登录账号',
            type:'search',
            selectList:''
          },
          {
            name:'userGroup.userName',
            model:'',
            holder:'userGroup.placeholderUserName',
            type:'search',
            selectList:''
          }
        ],
        unfirstRow:[
          {
            name:'userGroup.organization',
            model:'',
            holder:'userGroup.placeholderOrganization',
            type:'search',
            selectList:''
          }
        ]
      };

      $scope.saveRoleSelected = saveRoleSelected; // 存放选中角色数据
      $scope.open = open; // 状态改变弹框
      $scope.order = order; // 排序函数
      $scope.getData = getData; // 初始化函数
      $scope.searchData = searchData; // 查询函数
      $scope.getAllRole = getAllRole; // 获取所有角色
      $scope.getPersonData = getPersonData; // 获取人员列表
      // $scope.advQuery = advQuery; // 高级查询函数
      $scope.selectAll = selectAll; // 全选函数
      $scope.selectOne = selectOne; // 单选函数
      $scope.getLabelClass = getLabelClass; // 状态class样式
      // $scope.reset = reset; // 重置

      $scope.pages = inform.initPages(); // 初始化分页数据
      getData($scope.pages.pageNum);

      function order(str) {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      }

      // -----------------用户数据 start-----------------
      // 查询
      function searchData(str){
        $scope.map.employeeNo = str.row[0].model;
        $scope.map.realName = str.row[1].model;
        $scope.map.orgName = str.unfirstRow[0].model;
        interfaceMap = angular.copy($scope.map);
        getData(AgreeConstant.pageNum);
      }

      // 获取用户表格数据
      function getData(num) {
        // 清空复选框内容
        $scope.goBindAreaArray =[];
        $scope.checked = [];
        $scope.select_all = false;
        if (!num){inform.common(Trans('tip.pageNumTip'));return;}

        SystemService.getUserListByMapWithPage(interfaceMap, parseInt(num), $scope.pages.size)
          .then(function(data) {
            if (data.code === AgreeConstant.resultCode) {
              $scope.pages.goNum = null;
              var jsonData = angular.fromJson(data.result);
              $scope.resultData = jsonData.list;
              if ($scope.resultData.length === 0) {
                inform.common(Trans('tip.noData'));
                $scope.pages = inform.initPages();
              } else {
                $scope.pages.total = jsonData.total;
                $scope.pages.star = (jsonData.pageNum - 1) * jsonData.pageSize + 1;
                $scope.pages.end = jsonData.pageNum * jsonData.pageSize;
                $scope.pages.pageNum = jsonData.pageNum;
              }
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 用户数据全选
      function selectAll() {
        if ($scope.select_all) {
          $scope.checked = [];
          angular.forEach($scope.resultData, function(i) {
            i.checked = true;
            $scope.goBindAreaArray.push(i.userId);
            $scope.checked.push(i);
          });
        } else {
          angular.forEach($scope.resultData, function(i) {
            i.checked = false;
          });
          $scope.checked = [];
          $scope.goBindAreaArray =[];
        }
      }

      // 复选框一个一个选
      function selectOne() {
        angular.forEach($scope.resultData, function(i) {
          var index = $scope.checked.indexOf(i);
          if (index === -1 && i.checked) {
            $scope.checked.push(i);
          } else if (index !== -1 && !i.checked) {
            $scope.checked.splice(index, 1);
          }

          var indexId = $scope.goBindAreaArray.indexOf(i.userId);
          if (index === -1 && i.checked) {
            $scope.goBindAreaArray.push(i.userId);
          } else if (index !== -1 && !i.checked) {
            $scope.goBindAreaArray.splice(indexId, 1);
          }
        });
        if ($scope.resultData.length === $scope.checked.length) {
          $scope.select_all = true;
        } else {
          $scope.select_all = false;
        }
      }

      // -----------------人员数据 start-----------------
      $scope.person = {}; // 人员查询条件
      $scope.modalChecked = []; //存放选中人员数据
      $scope.gotoPersonList = gotoPersonList; // 根据当前登录人ID，查询人员列表
      $scope.selectModalAll = selectModalAll; // 全选功能
      $scope.selectModalOne = selectModalOne; // 单选功能
      $scope.addUser = addUser; // 创建用户

      // 获取当前登录人信息
      SystemService.getUser($scope.loginUserId)
        .then(function(data) {
          if (data.code === AgreeConstant.resultCode) {
            $scope.userData = data.result;
          }else{
            inform.common(data.message);
          }
      });

      function gotoPersonList(){
        $scope.person ={};
        getPersonData();
      }

      // 根据当前登录人ID，查询人员列表
      function getPersonData() {
        $scope.modalChecked = [];
        $scope.select_modal_all=false;
        SystemService.getEmployeeListNotIsUserByMap($scope.userData.employeeId, JSON.stringify($scope.person))
          .then(function(data) {
            if (data.code === AgreeConstant.resultCode) {
              $scope.personData = angular.fromJson(data.result);
              if ($scope.personData.length === 0) {
                inform.common(Trans('tip.noData'));
              }
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 人员数据中的全选功能
      function selectModalAll() {
        if ($scope.select_modal_all) {
          $scope.modalChecked = [];
          angular.forEach($scope.personData, function(i) {
            $scope.modalChecked.push(i.employeeId);
            i.checked = true;
          });
        } else {
          angular.forEach($scope.personData, function(i) {
            i.checked = false;
          });
          $scope.modalChecked = [];
        }
      }

      // 人员数据中的单选功能
      function selectModalOne(i) {
        var index = $scope.modalChecked.indexOf(i.employeeId);
        if (index === -1 && i.checked) {
          $scope.modalChecked.push(i.employeeId);
        } else if (index !== -1 && !i.checked) {
          $scope.modalChecked.splice(index, 1);
        }
        if ($scope.personData.length === $scope.modalChecked.length) {
          $scope.select_modal_all = true;
        } else {
          $scope.select_modal_all = false;
        }
      }

      // 创建用户操作
      function addUser() {
        if ($scope.modalChecked.length) {
          SystemService.saveToUser($scope.modalChecked.join())
            .then(function(data) {
              if (data.code === AgreeConstant.resultCode) {
                getData(1);
                inform.common(Trans('tip.saveSuccess'));
                $("#add_user").modal("hide");
                $scope.select_modal_all = false;
                $scope.modalChecked = [];
              } else {
                inform.common(data.message);
              }
            }, function() {
              inform.common(Trans("tip.requestError"));
            });
        } else {
          inform.common(Trans('common.chooseOneOpt'));
        }
      }

      // -----------------人员数据 end-----------------


      // -----------------角色弹框 start ----------------
      // 获取所有角色
      function getAllRole(id) {
        var roleId = LocalCache.getSession('roleId');
        if(roleId === '1'){
          SystemService.getAllRole()
              .then(function(data) {
                if (data.code === AgreeConstant.resultCode) {
                  $scope.roleData = data.result;
                  getRoleById(id);
                } else {
                  inform.common(data.message);
                }
              }, function() {
                inform.common(Trans("tip.requestError"));
              });
          return;
        }

        SystemService.getRoleByLoginUserIdMap()
          .then(function(data) {
            if (data.code === AgreeConstant.resultCode) {
              $scope.roleData = data.result;
              getRoleById(id);
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 根据ID 获取该用户角色信息
      function getRoleById(id) {
        $scope.userId = id;
        SystemService.getRoleListMapByUserId(id)
          .then(function(res) {
            if (res.code === AgreeConstant.resultCode) {
              $scope.groupRoleList = res.result.groupRoleList;
              angular.forEach($scope.roleData, function(i) {
                angular.forEach(res.result.userRoleList, function(j) {
                  if (i.roleId === j.roleId) {
                    i.selected = true;
                  }
                });
              });
            } else {
              inform.common(res.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 保存角色修改信息
      function saveChangeRole(str) {
        SystemService.saveUserToRole($scope.userId, str)
          .then(function(data) {
            if (data.code === AgreeConstant.resultCode) {
              inform.common(Trans('tip.saveSuccess'));
              $("#setRole").modal("hide");
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      function saveRoleSelected() {
        // 收集选中项到数组
        var selected = [];
        angular.forEach($scope.roleData, function(i) {
          if (i.selected) {
            selected.push(i.roleId);
          }
        });
        if (selected.length) {
          saveChangeRole(selected.join()); // 保存角色修改信息
        } else {
          inform.common(Trans("common.chooseOneOpt"));
        }
      }
      // -----------------角色弹框 end ---------------


      // -----------------删除弹框-----------------
      // 判断数据
      function judgeData(str) {
        var checkedIds =[];
        angular.forEach($scope.checked,function(res){
          if(res.islock=== str && str=== $scope.unfreeze){
            inform.common(Trans("userGroup.choseFreezePerson"));
            return;
          }
          if(res.islock=== str && str === $scope.freeze){
            inform.common(Trans("userGroup.choseNormalPerson"));
            return;
          }
          if(res.loginName === AgreeConstant.superAdmin && str!== $scope.resetPsd){
            inform.common(Trans("userGroup.notChosenFreezeOrNormal"));
            return;
          }
          checkedIds.push(res.userId);
        });
        return checkedIds;
      }

      function open(str) {
        if ($scope.checked.length) {
          var userIds = judgeData(str);
          if(userIds.length === $scope.checked.length){
            var modalInstance = $modal.open({
              templateUrl: 'myModalContent.html',
              controller: 'ModalInstanceCtrl',
              size: "sm",
              resolve: {
                items: function() {
                  if (str === $scope.freeze) {
                    return Trans('userGroup.freeze');
                  }
                  if (str === $scope.unfreeze) {
                    return Trans('userGroup.unfreeze');
                  }
                  if (str === $scope.resetPsd) {
                    return Trans('userGroup.restPsd');
                  }
                }
              }
            });
            modalInstance.result.then(function() {
              saveUserStatus(userIds, str);
            });
          }
        } else {
          inform.common(Trans('common.chooseOneOpt'));
        }
      }

      // 重置密码
      function resetPsd(ids) {
        SystemService.resetPassword(ids)
          .then(function(data) {
            if (data.code === AgreeConstant.resultCode) {
              inform.common(Trans("tip.psdRest"));
              $scope.checked =[];
              $scope.goBindAreaArray=[];
              getData(AgreeConstant.pageNum);
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 状态改变
      function changeStatus(ids, status) {
        SystemService.changeUserStatus(ids, status)
          .then(function(data) {
            if (data.code === AgreeConstant.resultCode) {
              $scope.checked =[];
              $scope.goBindAreaArray=[];
              getData(AgreeConstant.pageNum);
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 保存用户状态及密码重置操作
      function saveUserStatus(ids, status) {
        if (status === $scope.resetPsd) {
          resetPsd(ids);
        } else {
          if (ids.indexOf(parseInt($scope.loginUserId)) === -1) {
            changeStatus(ids, status);
          } else {
            inform.common(Trans('tip.selfFreeze'));
          }
        }
      }

      // 重置按钮
      // function reset() {
      //   $scope.map.employeeNo = "";
      //   $scope.map.realName = "";
      //   $scope.map.orgName = "";
      // }

      // //高级查询功能按钮
      // function advQuery() {
      //   $scope.isOpen = !$scope.isOpen;
      //   $scope.map.orgName = "";
      // }

      // 文字颜色设置
      function getLabelClass(str) {
        if (str === AgreeConstant.unfreezeStatus) {
          return "green";
        } else {
          return "red";
        }
      }

    }
  ]);
})();
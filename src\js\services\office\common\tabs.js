(function() {
    'use strict';
    app.factory('tabsModule', tabsModule);

    function tabsModule(){
        var service={
            initModule:initModule,
            clickItem:clickItem,
            first:first,
            last:last,
            next:next,
            pre:pre,
            isClicked:isClicked
        };
        return service;
        //回调方法
        var callBackFunction = null;
        //scope
        var tabScope = null;
        /**
         * 初始弹框
         * 参数结构
         * condition = { maxShow: 10 最大展示数量, label: tabTitle 展示属性名, list: [....] 展示列表}
         */
        function initModule(condition,scope,callback) {

            var size = condition.list.length;
            if(size === 0){
                return;
            }
            //属性赋值
            scope.tabList = condition.list;

            if (condition.maxShow != null) {
                scope.maxShow = condition.maxShow;
            } else {
                scope.maxShow = 5;
            }

            scope.startIndex = 0;
            scope.currentIndex = 0;
            scope.title =  condition.label;
            //设置结束位置
            if (size >= scope.maxShow) {
                //数组下标从0开始 故-1
                scope.endIndex = scope.maxShow - 1;
            } else {
                scope.endIndex = size;
            }
            //方法赋值
            scope.click = click;
            scope.next = next;
            scope.pre = pre;
            scope.first = first;
            scope.last = last;
            scope.clickItem = clickItem;
            scope.isClicked = isClicked;

            tabScope = scope;

            callBackFunction = callback;
            click(scope.currentIndex);
        }

        function isClicked(item) {
            if (deepCompare(item, tabScope.tabList[tabScope.currentIndex])) {
                return true;
            } else {
                return false;
            }
        }

        function clickItem(item) {

            for (var i = tabScope.startIndex; i <= tabScope.endIndex; i++) {
                if (deepCompare(item, tabScope.tabList[i])) {
                    tabScope.currentIndex = i;
                    click(i);
                    return;
                }
            }
        }

        function click (index) {
            callBackFunction(tabScope.tabList[index]);
        }

        function first() {
            //调整下标位置
            tabScope.startIndex = 0;
            tabScope.currentIndex = tabScope.startIndex;
            //调整结束位置
            if (tabScope.tabList.length >= tabScope.maxShow) {
                tabScope.endIndex = tabScope.maxShow - 1;
            } else {
                tabScope.endIndex = tabScope.tabList.length - 1;
            }
            click(tabScope.currentIndex);
        }

        function last() {
            tabScope.endIndex = tabScope.tabList.length - 1;
            tabScope.currentIndex = tabScope.endIndex;
            //设置开始位置
            if (tabScope.tabList.length >= tabScope.maxShow) {
                tabScope.startIndex = tabScope.tabList.length  - tabScope.maxShow;
            } else {
                tabScope.startIndex = 0;
            }
            click(tabScope.currentIndex);
        }

        function next() {
            //已经是最后一个 就不能进行next操作
            if (isLast(tabScope.currentIndex)) {
                return;
            }
            //当前元素已经是当前展示的最后一个元素
            if(tabScope.currentIndex === tabScope.endIndex) {
                //展示元素整体后移一位
                tabScope.endIndex = tabScope.currentIndex + 1;
                tabScope.startIndex = tabScope.startIndex + 1;
                tabScope.currentIndex = tabScope.endIndex;
            } else {
                //不是展示最后一个元素 当前位置右移一位
                tabScope.currentIndex = tabScope.currentIndex + 1;
            }
            click(tabScope.currentIndex);
        }

        function pre() {
            //当前页是第一页就不能执行pre操作
            if(isFirst(tabScope.currentIndex)) {
                return;
            }
            //当前元素已经是当前展示的第一个元素
            if (tabScope.currentIndex === tabScope.startIndex) {
                //整体前移一位
                tabScope.startIndex = tabScope.currentIndex - 1;
                tabScope.endIndex = tabScope.endIndex - 1;
                tabScope.currentIndex =  tabScope.startIndex;
            } else {
                //不是当前展示的第一个元素 只需要当前位置前移一位
                tabScope.currentIndex = tabScope.currentIndex - 1;
            }
            click(tabScope.currentIndex);
        }

        //当前下标是不是第一个
        function isFirst(index) {
            if (index <= 0) {
                return true;
            } else {
                return false;
            }
        }

        //当前下标是不是最后一个
        function isLast(index) {
            if(index + 1 >= tabScope.tabList.length) {
                return true;
            } else {
                return false;
            }
        }



        function deepCompare(x, y) {
            var i, l, leftChain, rightChain;

            function compare2Objects(x, y) {
                var p;

                // remember that NaN === NaN returns false
                // and isNaN(undefined) returns true
                if (isNaN(x) && isNaN(y) && typeof x === 'number' && typeof y === 'number') {
                    return true;
                }

                // Compare primitives and functions.
                // Check if both arguments link to the same object.
                // Especially useful on the step where we compare prototypes
                if (x === y) {
                    return true;
                }

                // Works in case when functions are created in constructor.
                // Comparing dates is a common scenario. Another built-ins?
                // We can even handle functions passed across iframes
                if ((typeof x === 'function' && typeof y === 'function') ||
                    (x instanceof Date && y instanceof Date) ||
                    (x instanceof RegExp && y instanceof RegExp) ||
                    (x instanceof String && y instanceof String) ||
                    (x instanceof Number && y instanceof Number)) {
                    return x.toString() === y.toString();
                }

                // At last checking prototypes as good as we can
                if (!(x instanceof Object && y instanceof Object)) {
                    return false;
                }

                if (x.isPrototypeOf(y) || y.isPrototypeOf(x)) {
                    return false;
                }

                if (x.constructor !== y.constructor) {
                    return false;
                }

                if (x.prototype !== y.prototype) {
                    return false;
                }

                // Check for infinitive linking loops
                if (leftChain.indexOf(x) > -1 || rightChain.indexOf(y) > -1) {
                    return false;
                }

                // Quick checking of one object being a subset of another.
                for (p in y) {
                    if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {
                        return false;
                    } else if (typeof y[p] !== typeof x[p]) {
                        return false;
                    }
                }

                for (p in x) {
                    if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {
                        return false;
                    } else if (typeof y[p] !== typeof x[p]) {
                        return false;
                    }

                    switch (typeof(x[p])) {
                        case 'object':
                        case 'function':

                            leftChain.push(x);
                            rightChain.push(y);

                            if (!compare2Objects(x[p], y[p])) {
                                return false;
                            }

                            leftChain.pop();
                            rightChain.pop();
                            break;

                        default:
                            if (x[p] !== y[p]) {
                                return false;
                            }
                            break;
                    }
                }

                return true;
            }

            if (arguments.length < 1) {
                return true; //Die silently? Don't know how to handle such case, please help...
                // throw "Need two or more arguments to compare";
            }

            for (i = 1, l = arguments.length; i < l; i++) {

                leftChain = [];
                rightChain = [];

                if (!compare2Objects(arguments[0], arguments[i])) {
                    return false;
                }
            }

            return true;
        }

    }
})();

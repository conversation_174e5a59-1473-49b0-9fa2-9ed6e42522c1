/*
* @Author: fubaole
* @Date:   2018-01-17 17:08:00
* @Last Modified by:   fubaole
* @Last Modified time: 2018-03-29 17:51:00
*/

(function() {
  'use strict';
  app.controller("index_edit", ['$scope', '$rootScope', 'inform', '$stateParams', '$state','Trans','SystemService','AgreeConstant','FileUploader','LocalCache',
    function($scope, $rootScope, inform, $stateParams, $state,Trans,SystemService,AgreeConstant,FileUploader,LocalCache) {
      $scope.screenFlag = false;
      $scope.settingModul =[];
      $scope.map = {};
      $scope.isDefault = 1;

      $scope.addModule = addModule;
      $scope.removeModule = removeModule;
      $scope.saveUserPage = saveUserPage;
      $scope.openScreen = openScreen;
      $scope.getClass = getClass;
      $scope.rest = rest;

      if($stateParams.isDefault){
        $scope.isDefault = 0; // 非默认页
      }else{
        $scope.isDefault = 1; // 默认页
      }
      if($stateParams.pageId){
        searchPageById($stateParams.pageId);
      }else{
        $scope.map.pageNumber = $scope.isDefault ;
        $scope.map.rowList = [
          {rowNumber:1,containerList:[{containerNumber:1,blockContentList:[]}]}
        ];
        getDataButton();
      }
      // 获取区块列表
      function getDataButton() {
        SystemService.getBlockContentListByMap()
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.aside = data.result;
              angular.forEach($scope.aside,function(res){
                angular.forEach($scope.settingModul,function(item){
                  if(item.blockContentId===res.blockContentId){
                    res.selected = true;
                  }
                });
              });
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 查询对应id的配置页面内容
      function searchPageById(id){
        SystemService.getPageByPageId(id)
        .then(function(data){
           if (data.code===AgreeConstant.resultCode) {
              $scope.map.pageId = data.result.pageId;
              $scope.map.pageName = data.result.pageName;
              $scope.map.pageImage = data.result.pageImage;
              $scope.map.imageName = data.result.imageName;
              // 块
              $scope.map.rowList = data.result.rowList;
              if(data.result.rowList && data.result.rowList.length){
                $scope.settingModul = data.result.rowList[0].containerList[0].blockContentList;
              }
              getDataButton();
            } else {
              inform.common(data.message);
            }
        },function(error){
           inform.common(Trans("tip.requestError"));
        });
      }

      // 添加模块
      function addModule(item) {
        if (item.selected) {
          $scope.settingModul.push(item);
        }
      }

      // 删除模块
      function removeModule(item, i) {
        $scope.settingModul.splice(i, 1);
        angular.forEach($scope.aside, function(data) {
          if (data.blockContentId===item.blockContentId) {
            data.selected = false;
          }
        });
      }

      // 提交数据
      function submitData(result) {
        if (result) {
          $scope.map.pageImage = result.result[$scope.map.imageName];
        }
        angular.forEach($scope.settingModul,function(des,index,array){
          des.blockNum = index;
        });
        $scope.map.rowList[0].containerList[0].blockContentList = $scope.settingModul;
        
        SystemService.saveOrUpdateUserPage($scope.isDefault,$scope.map)
        .then(function(data){
           if (data.code===AgreeConstant.resultCode) {
              inform.common(data.message);
              $state.go('app.index_bench');
            } else {
              inform.common(data.message);
            }
        },function(error){
           inform.common(Trans("tip.requestError"));
        });
      }

      // 保存配置页面
      function saveUserPage(){
        if(!$scope.map.pageName){
          inform.common(Trans('blocks.placeholderTplName'));
          return ;
        }
        if ($scope.selectedImage) {
          submitForm();
        }else{
          submitData();
        }
        
      }

      // 占屏比例
      function getClass(str) {
        var classname = '';
        switch (str) {
          case AgreeConstant.widthTwoOne:
            classname = 'col-sm-6'; // 1/2
            break;
          case AgreeConstant.widthThreeOne:
            classname = 'col-sm-4'; // 1/3
            break;
          case AgreeConstant.widthTwoThree:
            classname = 'col-sm-8'; // 2/3
            break;
          case AgreeConstant.widthFourOne:
            classname = 'col-sm-3'; // 1/4
            break;
          case AgreeConstant.widthThreeFour:
            classname = 'col-sm-9'; // 3/4
            break;
          default:
            classname = 'col-sm-12'; // 1
        }
        return classname;
      }

      function rest() {
        $scope.map = {};
      }

      // 全屏
      function openScreen(str){
        $scope.screenData = str;
        $scope.screenFlag = true;
      }

      //监听图片选择后变化
      $("#filesImg").change(function(){
        var arrUrl = $(this).val().split('\\');
        $scope.map.imageName = arrUrl[arrUrl.length-1];
        $scope.$apply();
        $scope.selectedImage = true;
        });

      //上传图片
      function submitForm(){
        var formData = new FormData(document.getElementById("form"));//表单id
        var file = document.querySelector('input[type=file]').files[0];
        formData.append('fileName', file);

        $.ajax({
           url: $rootScope.getWaySystemApi+'fileUpload/fileUpload',
           type: 'POST',
           data: formData,
           processData: false ,
           contentType : false,
           beforeSend: function(request) {
            request.setRequestHeader("Authorization", 'Bearer ' + LocalCache.getSession("token")||'');
           },
           success: function (result) {
            if (result.code===AgreeConstant.resultCode) {
              submitData(result);
            } else {
              inform.common(result.message);
            }
            
           },
           error:function(error){
            inform.common(Trans("tip.requestError"));
           }
        });

      }

    }
  ]);
})();
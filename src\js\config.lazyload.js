(function() {
  'use strict';
  // lazyload config

  angular.module('app')
    /**
     * jQuery plugin config use ui-jq directive , config the js and css files that required
     * key: function name of the jQuery plugin
     * value: array of the css js file located
     */
    .constant('JQ_CONFIG', {
      easyPieChart: ['library/jquery/charts/easypiechart/jquery.easy-pie-chart.js'],
      sparkline: ['library/jquery/charts/sparkline/jquery.sparkline.min.js'],
      plot: ['library/jquery/charts/flot/jquery.flot.min.js',
        'library/jquery/charts/flot/jquery.flot.resize.js',
        'library/jquery/charts/flot/jquery.flot.tooltip.min.js',
        'library/jquery/charts/flot/jquery.flot.spline.js',
        'library/jquery/charts/flot/jquery.flot.orderBars.js',
        'library/jquery/charts/flot/jquery.flot.pie.min.js'
      ],
      slimScroll: ['library/jquery/slimscroll/jquery.slimscroll.min.js'],
      sortable: ['library/jquery/sortable/jquery.sortable.js'],
      nestable: ['library/jquery/nestable/jquery.nestable.js',
        'library/jquery/nestable/nestable.css'
      ],
      filestyle: ['library/jquery/file/bootstrap-filestyle.min.js'],
      slider: ['library/jquery/slider/bootstrap-slider.js',
        'library/jquery/slider/slider.css'
      ],
      chosen: ['library/jquery/chosen/chosen.jquery.min.js',
        'library/jquery/chosen/chosen.css'
      ],
      TouchSpin: ['library/jquery/spinner/jquery.bootstrap-touchspin.min.js',
        'library/jquery/spinner/jquery.bootstrap-touchspin.css'
      ],
      wysiwyg: ['library/jquery/wysiwyg/bootstrap-wysiwyg.js',
        'library/jquery/wysiwyg/jquery.hotkeys.js'
      ],
      dataTable: ['library/jquery/datatables/jquery.dataTables.min.js',
        'library/jquery/datatables/dataTables.bootstrap.js',
        'library/jquery/datatables/dataTables.bootstrap.css'
      ],
      vectorMap: ['library/jquery/jvectormap/jquery-jvectormap.min.js',
        'library/jquery/jvectormap/jquery-jvectormap-world-mill-en.js',
        'library/jquery/jvectormap/jquery-jvectormap-us-aea-en.js',
        'library/jquery/jvectormap/jquery-jvectormap.css'
      ],
      footable: ['library/jquery/footable/footable.all.min.js',
        'library/jquery/footable/footable.core.css'
      ]
    })
    // oclazyload config
    .config(['$ocLazyLoadProvider', function($ocLazyLoadProvider) {
      // We configure ocLazyLoad to use the lib script.js as the async loader
      $ocLazyLoadProvider.config({
        debug: false,
        events: true,
        modules: [{
            name: 'ui.select',
            files: [
              'library/angular/angular-ui-select/select.min.js',
              'library/angular/angular-ui-select/select.min.css'
            ]
          },
          {
            name: 'ui.tree',
            files: [
              'library/angular/angular-ui-tree/angular-ui-tree.min.js',
              'library/angular/angular-ui-tree/angular-ui-tree.min.css'
            ]
          },
          {
            name: 'ngLocale',
            files: [
              'library/angular/angular-translate/angular-locale_zh-cn.js'
            ]
          },
           {
            name: 'Encrypt',
            files: [
              'library/angular/encrypt.js'
            ]
          },
          {
            name: 'ngGrid',
            files: [
              'library/angular/ng-grid/ng-grid.min.js',
              'library/angular/ng-grid/ng-grid.min.css',
              'library/angular/ng-grid/theme.css'
            ]
          },
          {
            name: 'angularFileUpload',
            files: [
              'library/angular/angular-file-upload/angular-file-upload.min.js'
            ]
          },
          {
            name: 'ui.calendar',
            files: ['library/angular/angular-ui-calendar/calendar.js']
          },
          {
            name: 'angularBootstrapNavTree',
            files: [
              'library/angular/angular-bootstrap-nav-tree/abn_tree_directive.js',
              'library/angular/angular-bootstrap-nav-tree/abn_tree.css'
            ]
          },
          {
            name: 'angular-sortable-view',
            files: [
              'library/angular/angular-sortable-view/angular-sortable-view.min.js'
            ]
          },
          {
            name: 'toaster',
            files: [
              'library/angular/angularjs-toaster/toaster.js',
              'library/angular/angularjs-toaster/toaster.css'
            ]
          },
          {
            name: 'textAngular',
            files: [
              'library/angular/textAngular/textAngular-sanitize.min.js',
              'library/angular/textAngular/textAngular.min.js'
            ]
          },
          {
            name: 'vr.directives.slider',
            files: [
              'library/angular/angular-slider/angular-slider.min.js',
              'library/angular/angular-slider/angular-slider.css'
            ]
          }
        ]
      });
    }]);
})();
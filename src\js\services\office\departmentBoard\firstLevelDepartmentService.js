(function() {
    'use strict';
    app.factory('firstLevelDepartmentService', firstLevelDepartmentService);
    firstLevelDepartmentService.$inject=["HttpService",'$rootScope'];

    function firstLevelDepartmentService(HttpService,$rootScope){

        // 获取汇总信息
        function getGatherHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyWorkingHours/getGatherHoursInfo',urlData);
        }
        // 返工工时投入情况
        function getReworkHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptWorkingHours/getReworkHoursInfo',urlData);
        }
        // 公司立项项目统计TOP5
        function getCompanyProWorkTimeList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'companyProWorkTimeManagement/getCompanyProWorkTimeList',urlData);
        }
        // 客户统计TOP5
        function getCustomerWorkTimeList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'customerWorkTimeManagement/getCustomerWorkTimeList',urlData);
        }
        // 获取技术支持 TOP5情况
        function getTopTecSupportHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyWorkingHours/getTopTecSupportHoursInfo',urlData);
        }
        // 获取PLM工时
        function getTopPlmHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyWorkingHours/getTopPlmHoursInfo',urlData);
        }
        // 获取工时类型分布情况
        function getHoursInfoByType(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyWorkingHours/getHoursInfoByType',urlData);
        }
        // 获取部门工作投入情况
        function getHoursInfoInDept(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyWorkingHours/getHoursInfoInDept',urlData);
        }
        // 获取产品线分类情况
        function getHoursInfoInLine(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyWorkingHours/getHoursInfoInLine',urlData);
        }
        // 获取产品用途分类情况
        function getHoursInfoInProjectPurpose(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyWorkingHours/getHoursInfoInProjectPurpose',urlData);
        }
        // 获取软件价值分类情况
        function getHoursInfoInSoftwareValue(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyWorkingHours/getHoursInfoInSoftwareValue',urlData);
        }
        // 获取部门工时
        function getDepartmentHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyWorkingHours/getDepartmentHoursInfo',urlData);
        }

        return {
            getGatherHoursInfo: getGatherHoursInfo,
            getReworkHoursInfo: getReworkHoursInfo,
            getTopTecSupportHoursInfo: getTopTecSupportHoursInfo,
            getTopPlmHoursInfo: getTopPlmHoursInfo,
            getHoursInfoByType: getHoursInfoByType,
            getHoursInfoInDept: getHoursInfoInDept,
            getHoursInfoInLine: getHoursInfoInLine,
            getHoursInfoInProjectPurpose: getHoursInfoInProjectPurpose,
            getHoursInfoInSoftwareValue: getHoursInfoInSoftwareValue,
            getDepartmentHoursInfo: getDepartmentHoursInfo,
            getCompanyProWorkTimeList: getCompanyProWorkTimeList,
            getCustomerWorkTimeList: getCustomerWorkTimeList,
        };
    }
})();
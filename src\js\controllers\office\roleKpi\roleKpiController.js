(function () {
    app.controller("roleKpiController", ['$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'roleKpiService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function ($rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, roleKpiService, inform, Trans, AgreeConstant, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //设置列表的高度
            setDivHeight();

            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //校验变量
            $scope.limitList = AgreeConstant.limitList;
            //获取缓存
            $scope.formRefer = LocalCache.getObject('roleKpiController_formRefer');
            $scope.personFormRefer = LocalCache.getObject('roleKpiController_personFormRefer');
            //对原缓存进行覆盖
            LocalCache.setObject("roleKpiController_formRefer", {});
            LocalCache.setObject("roleKpiController_personFormRefer", {});
            //初始化信息
            initData();
            if ($stateParams.type == null) {
                $scope.type = '1';
            } else {
                $scope.type = $stateParams.type;
            }
            $scope.getDetails = getDetails;

            //获取二级指标
            if (null != $scope.formRefer.kpiCode) {
                getDetails();
            }
            // 跳转修改页面
            $scope.go = go;
            // 显示/隐藏二级指标
            $scope.showSubKpiCode = showSubKpiCode;
            // 显示更新指标关联信息
            $scope.upRoleKpi = upRoleKpi;
            // 人员关联跳转修改
            $scope.goPerson = goPerson;
            // 重置人员关联名单
            $scope.resetData = resetData;
            // 保存指标关联信息
            $scope.saveRoleKpi = saveRoleKpi;
            //获取指标关联数据
            $scope.getData = getData;
            // 获取人员关联数据
            $scope.getPersonData = getPersonData;
            // 配置考核指标
            $scope.save = save;
            getPersonData();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 配置考核指标
             * @param tableData 考核指标
             */
            function save(tableData) {
                roleKpiService.save(tableData).then(function (data) {
                    inform.common(data.message);
                }, function () {
                    inform.common(Trans("tip.requestError"));
                })
            }

            /**
             * 初始化
             */
            function initData() {
                //获取角色
                $scope.roleCodeList = [];
                $scope.personRoleCodeList = [];
                comService.queryEffectiveParam('ROLE_LIBRARY', 'ROLE_LIBRARY').then(function (data) {
                    if (data.data) {
                        $scope.roleCodeList = data.data;
                        $scope.personRoleCodeList = data.data;
                    }
                    if($scope.formRefer.roleCode == null) {
                        $scope.formRefer.roleCode = $scope.roleCodeList[0].paramCode;
                    }
                    getData();
                });

                //获取一级指标
                $scope.kpiCodeList = [];
                comService.queryEffectiveParam('KPI_LIBRARY', '2000').then(function (data) {
                    if (data.data) {
                        $scope.kpiCodeList = data.data;
                    }
                });
            }

            /**
             * 获取二级指标
             */
            function getDetails() {
                //获取二级指标
                $scope.subKpiCodeList = [];
                comService.queryEffectiveParam('KPI_LIBRARY', $scope.formRefer.kpiCode).then(function (data) {
                    if (data.data) {
                        $scope.subKpiCodeList = data.data;
                    }
                });
            }

            /**
             * 获取所有指标关联信息
             */
            function getData() {

                var urlData = {
                    'roleCode': $scope.formRefer.roleCode,
                    'kpiCode': $scope.formRefer.kpiCode,
                    'subKpiCode': $scope.formRefer.subKpiCode
                };
                roleKpiService.selectData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null == data.data) {
                                $scope.tableData = {};
                                inform.common(Trans("tip.noData"));
                            } else {
                                //指标关联信息
                                $scope.tableData = data.data;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            /*
            * 获取人员关联信息
            * */
            function getPersonData() {
                $scope.personList = [];
                var urlData = {
                    'roleCode': $scope.personFormRefer.roleCode  //角色名字
                };
                roleKpiService.getPersonDate(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null == data.data) {
                                $scope.tableData = {};
                                inform.common(Trans("tip.noData"));
                            } else {
                                //人员关联信息
                                $scope.personList = data.data;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 210);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 40);
                $("#divTBDis1").height(divHeight);
                $("#subDivTBDis1").height(divHeight - 40);
            }

            /**
             * 指标关联跳转修改
             */
            function go (item) {
                LocalCache.setObject('roleKpiController_formRefer', $scope.formRefer);
                $state.go("app.office.roleKpiUpController", {item: item.roleCodeId});
            }
            /*
            * 显示/隐藏二级指标
            * */
            function showSubKpiCode (roleCode, kpiCode) {
                var i = 0;
                angular.forEach($scope.tableData, function (item) {
                    if (item.roleCode === roleCode && item.kpiCode === kpiCode && item.subKpiCodeId !== '0') {
                        i++;
                        //动态效果
                        $timeout(function () {
                            item.isShow = !item.isShow;
                        }, i * 80);
                    }
                })
            }
            /*
            * 显示更新指标关联信息（遮罩层）
            * */
            function upRoleKpi (item) {
                $scope.upParam = item;
                $scope.subKpiListDetail = [];
                var urlData = {
                    'roleCode': item.roleCodeId,
                    'kpiCode': item.kpiCodeId
                };
                roleKpiService.selectData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null == data.data) {
                                $scope.subKpiListDetail = {};
                                inform.common(Trans("tip.noData"));
                            } else {
                                $scope.subKpiListDetail = data.data;
                                angular.forEach($scope.subKpiListDetail, function (element) {
                                    if (element.subKpiCodeId === '0') {
                                        $scope.upParam = element;
                                    }
                                });
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            /*
            * 人员关联跳转修改
            * */
            function goPerson (item) {
                LocalCache.setObject('roleKpiController_personFormRefer', $scope.personFormRefer);
                $state.go("app.office.roleKpiPersonUpController", {roleName: item.roleName});
            }
            /*
            * 重置人员关联名单
            * */
            function resetData () {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function () {
                            return "确定要重置员工数据吗！";
                        }
                    }
                });
                // 重置名单
                modalInstance.result.then(function () {
                    roleKpiService.resetData().then(function (data) {
                        layer.confirm(data.message, {
                            title: false,
                            btn: ['确定']
                        }, function (result) {
                            layer.close(result);
                            //刷新页面信息
                            getPersonData();
                        });
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
                });
            }
            /*
            * 保存指标关联信息（遮罩层）
            * */
            function saveRoleKpi () {
                //二级指标权重之和应为100
                var count = 0;
                angular.forEach($scope.subKpiListDetail, function (item) {
                    if (item.subKpiCodeId !== '0') {
                        count += Number.parseFloat(item.weight);
                    }
                })

                // 若二级指标之和等于100，则保存信息
                roleKpiService.upInfo($scope.subKpiListDetail).then(function (data) {
                    layer.confirm(data.message, {
                        title: false,
                        btn: ['确定']
                    }, function (result) {
                        layer.close(result);
                        // 刷新指标关联信息
                        getData();
                        if (data.code === AgreeConstant.code) {
                            $('#up_roleKpi').modal('hide')
                        }
                    });
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });

            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

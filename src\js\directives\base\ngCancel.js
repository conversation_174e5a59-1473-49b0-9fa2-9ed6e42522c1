/*
* @Author: fubaole
* @Date:   2017-12-27 10:03:47
* @Last Modified by:   fubaole
* @Last Modified time: 2017-12-27 14:04:39
*/

(function () {
    'use strict';
    angular.module('app')
        .directive('ngCancel', ['$timeout', function ($timeout) {
            return {
                restrict: 'A',
                link: function (scope, ele, att) {
                  // 事件
                  ele.bind("click", function () {
                    var errorEle = $('.form-control-feedback');
                    var successEle = $('.form-vertical');
                    if(errorEle.length){
                      errorEle.remove();
                    }
                    if(successEle.length){
                      successEle.remove();
                    }
                  });

                }
            };
        }]);
})();

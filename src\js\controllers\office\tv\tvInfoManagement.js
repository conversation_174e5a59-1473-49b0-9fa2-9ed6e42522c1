
(function () {
	app.controller("tvInfoManagement", ['$rootScope', 'comService', 'SystemService', 'tvService', '$scope', '$state', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
		function ($rootScope, comService, SystemService, tvService, $scope, $state, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {

	/**
	* *************************************************************
	*             初始化部分                                 开始
	* *************************************************************
	*/
		//初始化页面信息
		$scope.getData = getData;
		//初始化下拉框信息
		getDataInfo();
		// 判断是否是ie浏览器
		isIE();
	/**
	* *************************************************************
	*              初始化部分                                 结束
	* *************************************************************
	*/

	/**
	* *************************************************************
	*              方法声明部分                                 开始
	* *************************************************************
	*/
	/**
	* 获取所有信息
	*/
		function getData() {
			$scope.infoList = [];
			tvService.getTvMessage().then(function (result) {
				if (result.code === AgreeConstant.code) {
					$scope.infoList = angular.fromJson(result.data);
					// 格式化信息
					$scope.infoList.forEach(function (item) {
						if (item.role) {
							item.role = item.role.split(',').map(Number);
						}
						if (item.areaCode) {
							item.areaCode = item.areaCode.split(',');
						}
						item.fontSize = item.fontSize.toString();
						item.saveIsShow = false;
						item.playSpeedShow = item.showForm === '0';
					});
				} else {
					inform.common(result.message);
				}
			}, function (error) {
				inform.common(Trans("tip.requestError"));
			});
		}
	/*
	* 初始化下拉框信息
	* */
		function getDataInfo() {
			var count = 0;
			// 获取地区列表
			comService.getAreaList().then(function (result) {
				if (result.code === AgreeConstant.code) {
					$scope.areaList = angular.fromJson(result.data);
					count++;
					if(count === 2) {
						getData();
					}
				} else {
					inform.common(result.message);
				}
			}, function (error) {
				inform.common(Trans("tip.requestError"));
			});
			// 获取角色列表
			SystemService.getAllRole().then(function (result) {
				if (result.code === AgreeConstant.resultCode) {
					$scope.roles = angular.fromJson(result.result);
					count++;
					if(count === 2) {
						getData();
					}
				} else {
					inform.common(result.message);
				}
			}, function (error) {
				inform.common(Trans("tip.requestError"));
			});
		}
	/**
	* 确认弹框
	*/
		$scope.confirm = function (str, func) {
			var modalInstance = $modal.open({
				templateUrl: 'tpl/common/modal.html',
				controller: 'ModalInstanceCtrl',
				size: "sm",
				resolve: {
					items: function items() {
						return Trans(str);
					}
				}
			});
			modalInstance.result.then(function () {
				func();
			});
		};
	/**
	* 启用任务
	* @param item 当前行的信息
	*/
		$scope.resumeJob = function (item) {
			$scope.confirm("确定要启用该消息吗？", function () {
				var urlData = {
					'id': item.id
				};
				tvService.messageResumeJob(urlData).then(function (result) {
					fun(result)
				}, function (error) {
					inform.common(Trans("tip.requestError"));
				});
			});
		};
	/**
	* 禁用任务
	* @param item 当前行的信息
	*/
		$scope.stopJob = function (item) {
			$scope.confirm("确定要禁用该消息吗？", function () {
				var urlData = {
					'id': item.id
				};
				tvService.messageStopJob(urlData).then(function (result) {
					fun(result)
				}, function (error) {
					inform.common(Trans("tip.requestError"));
				});
			});
		};
	/*
	* 展现形式发生改变
	* */
	$scope.showFromChange = function(item) {
		item.saveIsShow = true;
		if (item.showForm === '1') {
			item.playSpeed = '0';
			item.playSpeedShow = false;
		} else {
			item.playSpeedShow = true;
		}
	}
	/**
	* 添加消息
	*/
		$scope.addNewBind = function () {
			$scope.infoList.push({
				saveIsShow: true,
				fontColor: '#000000'
			});
		};
	/**
	* 删除消息
	* @param index 当前行的位置
	*/
		$scope.deleteNewBind = function (index) {
			$scope.infoList.splice(index, 1);
		};
		/**
   	 	 * 保存信息时执行
   	 	 */
		$scope.updateDataInfo = function (item) {
			if (!(item.content && item.fontSize && item.fontColor && item.showForm && item.areaCode && item.playDuration && item.playSpeed && item.role)) {
				inform.common("请完整填写此行内容！");
				return;
			}
			var urlData = {
				'id': item.id,
				'content': item.content,
				'fontSize': item.fontSize,
				'fontColor': item.fontColor,
				'showForm': item.showForm,
				'playDuration': item.playDuration,
				'playSpeed': item.playSpeed,
				'role': item.role.toString(),
				'areaCode': item.areaCode.toString()
			};
			if (item.id) {
				//修改
				tvService.updateTvMessage(urlData).then(function (result) {
					if (result.code === AgreeConstant.code) {
						item.saveIsShow = false;
					} else {
						inform.common(result.message);
					}
				}, function (error) {
					inform.common(Trans("tip.requestError"));
				});
			} else {
				//新增
				urlData.state = '0';// 添加状态为启用
				tvService.addTvMessage(urlData).then(function (result) {
					if (result.code === AgreeConstant.code) {
						// 保存按钮置灰
						item.saveIsShow = false;
						// 添加状态为启用
						item.state = '0';
						// 返显id
						item.id = result.data;
					} else {
						inform.common(result.message);
					}
				}, function (error) {
					inform.common(Trans("tip.requestError"));
				});
			}
		};
	/**
	* 更新排列顺序
	*/
		$scope.setOrderNum = function (item, element) {
			if ($scope.infoList.filter(function (item) {
				return item.saveIsShow === true;
			}).length) {
				inform.common('请先保存页面中所有数据再调整顺序！');
				return;
			}
			if (!element) {
				inform.common("不需要此操作！");
				return;
			}
			//两条记录更换顺序
			var urlData = Array(item, element);
			urlData.forEach(function (ele) {
				ele.areaCode = ele.areaCode.toString();
			});
			tvService.messageSetOrderNum(urlData).then(function (result) {
				fun(result)
			});
		};
	/*
	* 通用函数
	* */
		function fun(result) {
			if (result.code === AgreeConstant.code) {
				getData();
			} else {
				inform.common(result.message);
			}
		}
	/**
	* 判断是否是ie浏览器
	* */
		function isIE() {
			$scope.ieIsShow = !!window.ActiveXObject || "ActiveXObject" in window;
		}
	/**
	* *************************************************************
	*              方法声明部分                                 结束
	* *************************************************************
	*/
	}]);
})();
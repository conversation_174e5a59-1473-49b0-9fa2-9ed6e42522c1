//# sourceURL=js/controllers/office/projectScheduleDeviation/scheduleDeviationDetailManagement.js
(function () {
    app.controller("scheduleDeviationDetailManagement", ['comService', '$rootScope', '$scope','$stateParams', 'scheduleDeviationService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache',
        function (comService, $rootScope, $scope,$stateParams, scheduleDeviationService, inform, Trans, AgreeConstant, $modal, $state, LocalCache) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //获取缓存数据
            $scope.param = LocalCache.getObject('scheduleDeviationManagement_param');
            if (typeof ($scope.param) !== 'undefined' && typeof ($scope.param.writeFlag) !== 'undefined' ) {
                if($scope.param.type !== 'entrance'){
                    LocalCache.setObject('scheduleDeviationManagement_param', {});
                }
            } else {
                $scope.param = {
                    'writeFlag': true,
                    'searchParam': {}
                };
            }
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            $scope.isMarketTask='全部';
            //保存查询出的产品线信息
            $scope.jsonData = []; //项目详情
            $scope.type = 'info';
            $scope.pausedSelect = [{
                value: '0',
                label: '正常'
            }, {
                value: '1',
                label: '暂停'
            }, {
                value: '2',
                label: '取消'
            }, {
                value: '3',
                label: '废止'
            }];
            //是否是市场节点
            $scope.isMarketTaskSelect =['是','否'];
            $scope.pausedMap = {
                "1": "暂停",
                "2": "取消",
                "3": "废止",
                "0": "正常"
            };

            $scope.planSvFlagSelect = [{
                value: '1',
                label: '按计划结束时间计算'
            }, {
                value: '0',
                label: '按原计划结束时间计算'
            }];

            $scope.planSvFlagMap = {
                "0": "按原计划结束时间计算",
                "1": "按计划结束时间计算"
            };
            $scope.datepicker = {};
            $scope.datepicker.currentDate = $scope.datepicker.currentDate ? null : new Date();

            //初始化里程碑计划来源
            initMilestonePlanResource();
            //获取数据
            $scope.getData = getData;
            //设置列表的高度
            $scope.setDivHeight = setDivHeight;
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $(window).resize(tryResetPage);
            //里程碑计划来源赋值
            $scope.milestonePlanResource = $scope.param.milestonePlanResource == null ? '' : $scope.param.milestonePlanResource;
            $scope.planChangeNo = $scope.param.planChangeNo == null ? '' : $scope.param.planChangeNo;
            $scope.brief = $scope.param.brief == null ? '' : $scope.param.brief;
            getData();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - 315;
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight-15);
                $("#subDivTBDis2").height(divHeight-80);
            }
            /**
             * 初始化里程碑计划来源
             */
            function initMilestonePlanResource() {
                //初始化里程碑计划来源
                $scope.MilestonePlanResource = [];
                comService.queryEffectiveParam('MILESTONE_PLAN_RESOURCE', 'MILESTONE_PLAN_RESOURCE').then(function (data) {
                    if (data.data) {
                        $scope.MilestonePlanResource = data.data;

                        //判断里程碑计划是否需要隐藏
                        if ($scope.param.writeFlag === false) {
                            $('#milestonePlanResource').attr("disabled", "disabled");
                            $('#brief').attr("disabled", "disabled");
                            $('#planChangeNo').attr("disabled", "disabled");
                        }
                    }
                });
            }

            /**
             * 获取项目
             */
            function getData() {
                //删除已加载里程碑列表的HTML模板
                $scope.deviationListShow = 0;
                var urlData = {
                    id: $scope.param.id,
                    isMarketTask:$scope.isMarketTask
                };
                scheduleDeviationService.getProjectDetailsInformation(urlData).then(function (data) {
                    //重新加载里程碑列表的HTML模板
                    $scope.deviationListShow = 1;
                    if (data.code === AgreeConstant.code) {
                        //项目详情
                        $scope.jsonData = data.data.statusReportVO;
                        $scope.allWeight = data.data.allWeight;
                        if($scope.param.type !== 'entrance'){
                            if((100.00-$scope.allWeight) !==0){
                                 $modal.open({
                                      templateUrl: 'tpl/common/errorModel.html',
                                      controller: 'ModalInstanceCtrl',
                                      size: "lg",
                                      resolve: {
                                          items: function() {
                                                return "当前项目里程碑总权重为"+$scope.allWeight+"，请进行修改维护！";
                                          }
                                      }
                                 });
                            }
                        }
                        if ($scope.jsonData.length === 0) {
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages(); //初始化分页数据
                        }
                        setTimeout(tryResetPage,300);
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            function tryResetPage(){
                $('#fixedLeftAndTop').DataTable( {
                    //可被重新初始化
                    retrieve:       true,
                    //自适应高度
                    scrollY:        'calc(100vh - 350px)',
                    scrollX:        true,
                    scrollCollapse: true,
                    //控制每页显示
                    paging:         false,
                    //冻结列（默认冻结左1）
                    fixedColumns:   {
                        leftColumns: 1,
                        rightColumns: 1
                    },
                    //search框显示
                    searching:      false,
                    //排序箭头
                    ordering:       false,
                    //底部统计数据
                    info:           false
                } );
            }
            //更新里程碑计划来源
            $scope.updateData = function () {
                //判断是否计划变更次数为正整数
                var regPos = /^[0-9]{1,10}$/;
                if (null == $scope.planChangeNo || $scope.planChangeNo === '') {
                    $scope.planChangeNo = 0;
                }
                if (!regPos.test($scope.planChangeNo)) {
                    inform.common("计划变更次数必须为正整数");
                    return;
                }
                var urlData = {
                    'id': $scope.param.id,
                    'milestonePlanResource': $scope.milestonePlanResource,
                    'planChangeNo': $scope.planChangeNo,
                    'brief': $scope.brief
                };
                scheduleDeviationService.updateMilestonePlanResource(urlData).then(function (data) {
                    inform.common(data.message);
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });

            };
            /**
             * 修改信息
             */
            $scope.updateInfo = function () {
                if ("" === $scope.changeParam.planStartTime || "" === $scope.changeParam.planEndTime || null == $scope.changeParam.planStartTime || null == $scope.changeParam.planEndTime) {
                    inform.common("请填写计划开始时间、计划结束时间");
                } else {
                    var urlData = {
                        'cname': $scope.param.id,
                        'id': $scope.changeParam.id,
                        'taskName': $scope.changeParam.taskName,
                        'responsiblePerson': $scope.changeParam.responsiblePerson,
                        'initialPlanStartTime': inform.format($scope.changeParam.initialPlanStartTime, 'yyyy-MM-dd'),
                        'initialPlanEndTime': inform.format($scope.changeParam.initialPlanEndTime, 'yyyy-MM-dd'),
                        'planStartTime': inform.format($scope.changeParam.planStartTime, 'yyyy-MM-dd'),
                        'planEndTime': inform.format($scope.changeParam.planEndTime, 'yyyy-MM-dd'),
                        'evaluationPlanStartTime': inform.format($scope.changeParam.evaluationPlanStartTime, 'yyyy-MM-dd'),
                        'evaluationPlanEndTime': inform.format($scope.changeParam.evaluationPlanEndTime, 'yyyy-MM-dd'),
                        'endTime': inform.format($scope.changeParam.endTime, 'yyyy-MM-dd'),
                        'brief': $scope.changeParam.brief,
                        'planChangeNo': $scope.changeParam.planChangeNo,
                        'demandBrief': $scope.changeParam.demandBrief,
                        'paused': $scope.changeParam.paused,
                        'planSvFlag': $scope.changeParam.planSvFlag,
                        'weight':$scope.changeParam.weight,
                        'isMarketTask':$scope.changeParam.isMarketTask //是否为市场节点
                    };
                    scheduleDeviationService.updateProjectDetailInfo(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            inform.common(data.message);
                            $scope.getData();
                            $("#edit_Module").modal("hide");
                            //局部刷新
                            $('#fixedLeftAndTop').dataTable().fnDestroy();
                        } else {
                            inform.common(data.message);
                        }
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
                }
            };
            /**
             * 添加信息
             */
            $scope.addInfo = function () {
                if ("" === $scope.spec.planStartTime || "" === $scope.spec.planEndTime || null == $scope.spec.planStartTime || null == $scope.spec.planEndTime) {
                    inform.common("请填写计划开始时间、计划结束时间");
                } else {
                    var urlData = {
                        'cname': $scope.param.id,
                        'taskName': $scope.spec.taskName,
                        'responsiblePerson': $scope.spec.responsiblePerson,
                        'planStartTime': inform.format($scope.spec.planStartTime, 'yyyy-MM-dd'),
                        'planEndTime': inform.format($scope.spec.planEndTime, 'yyyy-MM-dd'),
                        'endTime': inform.format($scope.spec.endTime, 'yyyy-MM-dd'),
                        'brief': $scope.spec.brief,
                        'demandBrief': $scope.spec.demandBrief,
                        'paused': $scope.spec.paused,
                        'planSvFlag': '0',
                        'weight':$scope.spec.weight, //权重
                        'isMarketTask':$scope.spec.isMarketTask //是否为市场节点
                    };
                    scheduleDeviationService.addProjectDetailInfo(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            inform.common(data.message);
                            $scope.getData();
                            $("#add_modal").modal("hide");
                        } else {
                            inform.common(data.message);
                        }
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
                }
            };
            /**
             * 删除数据 JSON.stringify(removeParam)
             */
            $scope.removeCustomer = function (item) {
                scheduleDeviationService.deleteProjectDetailInfo(item).then(function (data) {
                    if (data.code === "0000") {
                        inform.common(Trans("tip.delSuccess"));
                        $scope.getData(AgreeConstant.pageNum);
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            };
            /**
             * 删除弹框
             */
            $scope.open = function (item) {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function () {
                            return Trans("common.deleteTip");
                        }
                    }
                });
                modalInstance.result.then(function () {
                    if (null != item && item !== "") {
                        $scope.removeCustomer(item);
                    }
                });
            };
            /**
             * 修改信息弹框，str存在，就是新增
             */
            $scope.popModal = function (item, str) {
                if (str) {
                    $scope.spec = {
                        'taskName': '',
                        'responsiblePerson': '',
                        'planStartTime': '',
                        'planEndTime': '',
                        'endTime': '',
                        'brief': '',
                        'estimateEndTime': '',
                        'demandBrief': '',
                        'weight':'',
                        'isMarketTask':'否'
                    };
                } else {
                    $scope.changeParam = angular.copy(item);
                }
            };
            /**
             * 查询里程碑的历史信息
             * @param m 里程碑信息
             */
            $scope.selectHistory = function (m) {
                var urlData = {
                    id: m.id,
                    cname: m.cname
                };
                scheduleDeviationService.getProjectDetailsHistory(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        //里程碑的历史信息
                        $scope.historyData = data.data;
                        if ($scope.historyData.length === 0) {
                            inform.common(Trans("该里程碑没有历史数据"));
                            return;
                        }
                        $("#history_modal").modal('show');
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            };

            /**
             * 跳转至项目信息管理
             */
            $scope.goback = function () {
                LocalCache.setObject('scheduleDeviationDetailManagement_param', {
                    cname: $scope.param.cname,
                    productLine: $scope.param.productLine,
                    type: $scope.param.type,
                    projectManager: $scope.param.projectManager,
                    department: $scope.param.department,
                    writeFlag: $scope.param.writeFlag,
                    searchParam: $scope.param.searchParam
                });
                $state.go('app.office.scheduleDeviationManagement');
            };

            /**
             * 计划结束时间按钮
             */
            $scope.planEndTimeUpdate = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.planStartTime = false;
                $scope.planEndTime = true;
                $scope.endTime = false;
                $scope.estimateEndTime = false;
            };
            /**
             * 计划开始时间按钮
             */
            $scope.planStartTimeUpdate = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.planStartTime = true;
                $scope.planEndTime = false;
                $scope.endTime = false;
                $scope.estimateEndTime = false;
            };
            /**
             * 实际完成时间按钮
             */
            $scope.endTimeUpdate = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.planStartTime = false;
                $scope.planEndTime = false;
                $scope.endTime = true;
                $scope.estimateEndTime = false;
            };
            /**
             * 计划结束时间按钮
             */
            $scope.estimateEndTimeUpdate = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.planStartTime = false;
                $scope.planEndTime = false;
                $scope.endTime = false;
                $scope.estimateEndTime = true;
            };
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        }
    ]);
})();
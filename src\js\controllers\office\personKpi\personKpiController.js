(function() {
    app.controller("personKpiController", ['$state', 'comService', 'personKpiService','$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$stateParams', '$modal', '$http', '$state',
        function(state, comService,personKpiService, $rootScope, $scope, inform, Trans, AgreeConstant, LocalCache, $stateParams, $modal, $http, $state) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //默认选中初级研发工程师
            $scope.personType = 'ROLE_LIBRARY_9';
            LocalCache.setObject('personDataBoardFlag', '');
            LocalCache.setObject('personDataBoardEmployee', {});
    		$scope.formRefer={};
			if (LocalCache.getObject('personKpi') != null) {
				$scope.formRefer = LocalCache.getObject('personKpi');
			}
    		//初级研发工程师
    		$scope.ROLE_LIBRARY_9=true;
    		//中级研发工程师
            $scope.ROLE_LIBRARY_10=true;
            //高级研发工程师
            $scope.ROLE_LIBRARY_11=true;
            //项目经理
            $scope.ROLE_LIBRARY_12=true;
            //Team Leader
            $scope.ROLE_LIBRARY_8=true;
            $scope.ROLE_LIBRARY_4_1=true;
            $scope.ROLE_LIBRARY_4_2=true;
            $scope.ROLE_LIBRARY_4_3=true;
            $scope.ROLE_LIBRARY_5=true;
            $scope.ROLE_LIBRARY_6=true;
            $scope.ROLE_LIBRARY_7=true;

            //判断按钮是否具有权限
			getButtonPermission();
	    	//季度下拉框数据源
	        $scope.quarterSelect = [
            {
                value: '6',
                label: '上半年'
            },{
                value: '7',
                label: '下半年'
            }, {
				value: '5',
				label: '年度'
			}, {
                value: '1',
                label: '第1季度'
	        }, {
	            value: '2',
	            label: '第2季度'
	        }, {
	            value: '3',
	            label: '第3季度'
	        }, {
	            value: '4',
	            label: '第4季度'
	        }];
	        $scope.getData = getData;
	        $scope.getHead = getHead;
	        //窗体大小变化时重新计算高度
	        $(window).resize(setDivHeight);
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
			$scope.clickModal = function (m) {
				var quarter = null;
				if ($scope.formRefer.quarter !== '5') {
					quarter = $scope.formRefer.quarter;
				}
				$state.go('app.index_bench',{
				    empId: m.empId,
                    years:$scope.formRefer.years,
                    quarter:quarter});
				LocalCache.setObject('personKpi', $scope.formRefer);
                LocalCache.setObject('personKpi_type', $scope.personType);
                LocalCache.setObject('personDataBoardEmployee', m);
			};
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 220);
                $("#divTBD").height(divHeight);
                $("#subDivTBDis").height(divHeight - 85);
            }
            function initPages() {
            	 //获取地区
                $scope.areaList = [];
                comService.getAreaList().then(function(data) {
                	 $scope.areaSelect = data.data;
                 });
                 //获取部门信息
                 $scope.departmentSelect = [];
                 comService.getOrgChildren('D010053').then(function(data) {
                     $scope.departmentSelect = comService.getDepartment(data.data);
                 });
                 initFormRefer();

            }
            /**
             * 获取按钮权限
             */
            function getButtonPermission(){
            	var list=['ROLE_LIBRARY_9','ROLE_LIBRARY_10','ROLE_LIBRARY_11','ROLE_LIBRARY_12',
            	'ROLE_LIBRARY_8','ROLE_LIBRARY_4_1','ROLE_LIBRARY_4_2','ROLE_LIBRARY_4_3','ROLE_LIBRARY_5','ROLE_LIBRARY_6','ROLE_LIBRARY_7']
            	var buttons = {
                	'Button-PersonKpiManagement-one':'ROLE_LIBRARY_1',
                    'Button-PersonKpiManagement-two':'ROLE_LIBRARY_2',
                    'Button-PersonKpiManagement-three':'ROLE_LIBRARY_4',
                    'Button-PersonKpiManagement-four':'ROLE_LIBRARY_5',
                    'Button-PersonKpiManagement-five':'ROLE_LIBRARY_6',
                    'Button-PersonKpiManagement-six':'ROLE_LIBRARY_7',
                    'Button-PersonKpiManagement-eight':'ROLE_LIBRARY_8'
                };
                var urlData = {
                    'userId':LocalCache.getSession("userId"),
                    'parentPermission':'PersonKpiManagement',
                    'buttons':buttons
                };
               comService.getButtonPermissionName(urlData).then(function (data) {

           			//设置默认值
                   for (var i=0;i<list.length;i++){
                	   var name=list[i];
                	   if($scope[name]){
                		   $scope.personType=list[i];
                		   //获取地区与部门下拉框
              		    	initPages() ;
              		    	//设置列表的高度
              		    	setDivHeight();
                		   return;
                	   }
                   }
           	     }, function (error) {
           	         inform.common(Trans("tip.requestError"));
           	     });

            }
            /**
             * 初始化查询条件
             */
            function initFormRefer(){

                if (JSON.stringify(LocalCache.getObject('personKpi_type')) !== "{}") {
                    $scope.personType = LocalCache.getObject('personKpi_type');
                }
                initTime();
                getHead();
            }
            /**
     		 * 初始化检索条件年度与季度
     		 */
     		function initTime(){
     			//当前时间（Date类型）
     			var date = new Date();
     			//得到上一个季度的第一天
     	        var lastQuarterFirstDay = new Date(date.getFullYear() , date.getMonth() - 3 , 1);
     	        var day = inform.format(lastQuarterFirstDay, 'yyyy-MM-dd').split("-");
     	        $scope.formRefer.years = $scope.formRefer.years != null ? $scope.formRefer.years : day[0];
                //设置季度
     	        var month = lastQuarterFirstDay.getMonth() + 1;
     	        $scope.formRefer.quarter = month>6?'5':'6';
     		}
     		/**
             * 重置
             */
            $scope.reset = function() {
            	// 初始化查询数据
                $scope.formRefer.department='';
                $scope.formRefer.name='';
                $scope.formRefer.area='';
                initTime();
            };
            /**
             * 动态获取表头
             */
            function getHead(){
            	if (null ==$scope.formRefer.years||''===$scope.formRefer.years||null ==$scope.formRefer.quarter||''===$scope.formRefer.quarter){
       	   			inform.common("请选择需要查询的年度与季度");
       	   			return;
       	   		}
            	var urlData = {
         	        'year':$scope.formRefer.years,
         	        'quarter':$scope.formRefer.quarter,
         	        'department':$scope.formRefer.department,
         	        'name':$scope.formRefer.name,
         	        'area':$scope.formRefer.area,
         	        'role':$scope.personType
         	    };
            	personKpiService.getHead(urlData).then(function (data) {
          	         if (data.code === AgreeConstant.code) {
          	            if(null==data.data){
          	         		inform.common(Trans("tip.noData"));
          	         	} else {
          	         		$scope.oneHead = data.data.one;
          	         		$scope.twoHead = data.data.two;
          	         		getData(urlData);
          	            }
          	         } else {
          	             inform.common(data.message);
          	         }
          	     }, function (error) {
          	         inform.common(Trans("tip.requestError"));
          	     });
            }
         /**
   	      * 获取人员详情
   	      */
   	   	 function getData(urlData){
   	   	 	personKpiService.getData(urlData).then(function (data) {
   	         if (data.code === AgreeConstant.code) {
   	            if(null==data.data){
   	         		$scope.paramList = {};
   	         		inform.common(Trans("tip.noData"));
   	         	} else {
   	         		$scope.paramList = data.data;
   	                setDivHeight();
   	            }
   	         } else {
   	             inform.common(data.message);
   	         }
   	     }, function (error) {
   	         inform.common(Trans("tip.requestError"));
   	     });
   	    }
   	   	 /**
	   	  * excel下载
	   	  */
	   	 $scope.toExcel = function() {
			var modalInstance = $modal.open({
				templateUrl: 'myModalContent.html',
				controller: 'ModalInstanceCtrl',
				size: "sm",
				resolve: {
					items: function() {
						return "确定要下载吗！";
					}
				}
			});
			modalInstance.result.then(function() {
				//拼装下载内容
				var urlData={
			   	    'year':$scope.formRefer.years,
	      	        'quarter':$scope.formRefer.quarter,
	      	        'department':$scope.formRefer.department,
	      	        'name':$scope.formRefer.name,
	      	        'area':$scope.formRefer.area,
	      	        'role':$scope.personType
				};
				inform.downLoadFile ('personKpi/toExcel',urlData,'个人指标考核信息.xlsx');
			});
		};

		$scope.downloadDeveloperKpi = function(){
             inform.modalInstance("开发工程师考核数据吗？").result.then(function() {
				var urlData={
			   	    'year':$scope.formRefer.years,
	      	        'quarter':$scope.formRefer.quarter
				};
                inform.downLoadFile('personKpi/downloadDeveloperKpi',urlData,"开发工程师考核数据"+inform.format(new Date(),'yyyy-MM-dd')+".xlsx");
            });
		}
			/**
			 * 获取考核周期名称
			 */
			function getQuarter(value){
				for(var i = 0; i < $scope.quarterSelect.length; i ++){
					if($scope.quarterSelect[i].value === value){
						return '-'+$scope.quarterSelect[i].label;
					}
				}
				return '';
			}
		/**
		 *下载开发工程师考核数据
		 */
		$scope.downloadExcel = function() {
			inform.modalInstance("确定要下载开发工程师KPI表吗？").result.then(function() {
				var urlData={
					'year':$scope.formRefer.years,
					'quarter':$scope.formRefer.quarter,
					'role' : 'ROLE_LIBRARY_9'
				}
				inform.downLoadFile('personKpi/downloadDeveloperExcel',urlData,"开发工程师KPI表"+'-'+$scope.formRefer.years+getQuarter($scope.formRefer.quarter)+".xlsx");
			});
		};
		/**
		 *下载测试工程师考核数据
		 */
		$scope.downloadTestExcel = function() {
			inform.modalInstance("确定要下载测试工程师KPI表吗？").result.then(function() {
				var urlData={
					'year':$scope.formRefer.years,
					'quarter':$scope.formRefer.quarter,
					'role' : 'ROLE_LIBRARY_4_1'
				};
				inform.downLoadFile('personKpi/downloadTestExcel',urlData,"测试工程师KPI表"+'-'+$scope.formRefer.years+getQuarter($scope.formRefer.quarter)+".xlsx");
			});
		};
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        }
    ]);
})();

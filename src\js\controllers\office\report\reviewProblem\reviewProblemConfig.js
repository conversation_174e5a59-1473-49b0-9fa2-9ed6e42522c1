/**
 * 获取评审主题类别清单
 *
 */
var reviewThemeListConfig = [
  '项目立项',
  '项目计划',
  '用户需求',
  '产品需求',
  '整体解决方案',
  '概要设计',
  '详细设计',
  '集成计划',
  '集成测试用例',
  '集成测试报告',
  '系统测试计划',
  '系统测试用例',
  '系统测试报告',
  '专项工作方案',
  '专项工作报告',
  '迭代回顾',
  '手册类',
  '运维方案',
  'EPG评审',
  '流程规范类',
];

/**
 *问题所属过程
 */
var problemProcessConfig = {
  项目立项: '立项/计划',
  项目计划: '立项/计划',
  用户需求: '需求',
  产品需求: '需求',
  整体解决方案: '设计',
  概要设计: '设计',
  详细设计: '设计',
  集成计划: '测试',
  集成测试用例: '测试',
  集成测试报告: '测试',
  系统测试计划: '测试',
  系统测试用例: '测试',
  系统测试报告: '测试',
  手册类: '设计',
  运维方案: '设计',
  EPG评审: '需求',
  流程规范类: '需求',
  迭代回顾: '其他',
};
/**
 * 评审问题分类
 */
var problemTypeConfig = {
  '立项/计划': [
    '建议性意见、排版或书写问题',
    '立项或计划存在描述错误或遗漏点',
    '市场里程碑节点缺失或与市场未达成一致',
    '存在严重问题导致评审不通过',
    '疑问解释',
    '其他',
  ],
  需求: [
    '建议性意见、排版或书写问题',
    '需求细节调整或小的修改点',
    '非核心功能的需求描述错误或存在遗漏点',
    '主要功能的需求分析错误或缺失',
    '存在严重问题导致评审不通过',
    '疑问解释',
    '其他',
  ],
  设计: [
    '建议性意见、排版或书写问题',
    '设计不合理或不规范，对功能实现影响较小',
    '缺失部分细节信息',
    '设计错误，导致主要功能失效',
    '缺失必要设计，或遗漏需求场景',
    '存在严重问题导致评审不通过',
    '疑问解释',
    '其他',
  ],
  测试: [
    '建议性意见、排版或书写问题',
    '非核心或次要测试策略、测试项、测试场景、描述缺失，或者设计错误',
    '某个功能或场景缺少部分测试用例、测试设计、编写错误',
    '核心或者关键测试策略、测试项、测试场景、描述缺失，或者设计错误',
    '整体或者大块测试用例缺失',
    '存在严重问题导致评审不通过',
    '疑问解释',
    '其他',
  ],
  其他: ['其他'],
};
/**
 * 评审问题级别
 */
var problemLevelConfig = ['轻微', '一般', '严重', '致命', '疑问解释'];

/**
 * 评审问题分类与级别关系
 */
var problemTypeLevelConfig = {
  '建议性意见、排版或书写问题': '轻微',
  立项或计划存在描述错误或遗漏点: '一般',
  市场里程碑节点缺失或与市场未达成一致: '严重',
  存在严重问题导致评审不通过: '致命',
  需求细节调整或小的修改点: '一般',
  非核心功能的需求描述错误或存在遗漏点: '一般',
  主要功能的需求分析错误或缺失: '严重',
  '设计不合理或不规范，对功能实现影响较小': '一般',
  缺失部分细节信息: '一般',
  '设计错误，导致主要功能失效': '严重',
  '缺失必要设计，或遗漏需求场景': '严重',
  '非核心或次要测试策略、测试项、测试场景、描述缺失，或者设计错误': '一般',
  '某个功能或场景缺少部分测试用例、测试设计、编写错误': '一般',
  '核心或者关键测试策略、测试项、测试场景、描述缺失，或者设计错误': '严重',
  整体或者大块测试用例缺失: '严重',
  疑问解释: '疑问解释',
  其他: '',
};

(function () {
    app.controller("codeDataReport", ['$rootScope','comService', '$scope', '$stateParams', '$state',
        function ($rootScope,comService,$scope, $stateParams, $state) {
            // 顶部部门选择
            if($stateParams.typeSelect){
                $scope.typeSelect = $stateParams.typeSelect;
            } else {
                $scope.typeSelect = '1';
            }
           
            $scope.toOther = function (typeSelect) {
                $state.go('app.office.codeDataReport', {
                    'typeSelect': typeSelect
                   
                });
            }
        }]);
})();
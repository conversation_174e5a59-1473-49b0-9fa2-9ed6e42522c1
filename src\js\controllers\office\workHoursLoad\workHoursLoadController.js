(function () {
    app.controller("workHoursLoadController", ['comService','workHoursLoadService','$rootScope', '$state','$scope','$stateParams', '$modal','inform','LocalCache','Trans','AgreeConstant','$http',
        function (comService,workHoursLoadService,$rootScope,$state, $scope,$stateParams,$modal,inform,LocalCache,Trans,AgreeConstant,$http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //查询条件
            $scope.formRefer = {};
            $scope.menu = $stateParams.menu;
            $scope.departmentSortParamFlag = false;
            $scope.primaryDepartmentSortParamFlag = false;
            $scope.orderByDisposableWorkHoursFlag  = false;
            $scope.orderByLeftWorkHoursTotalFlag  = false;
            $scope.orderByWorkHoursLoadRateFlag  = false;
            $scope.sortType = "1";
            //默认按可支配工时降序排序
            $scope.sortName = "4";
            //页面分页信息
            $scope.pages = {
                pageNum : '',   //分页页数
                size : '',      //分页每页大小
                total : ''      //数据总数
            };
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //初始化时间
            initTime();
            initInfo();
            $scope.getData = getData;
            $scope.selectEmployeesByDepartment = selectEmployeesByDepartment;
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
             function initTime() {
                 //设置默认时间
                 var startDate = inform.format(new Date(), 'yyyy-MM-dd');
                 var endDate = inform.format(new Date().getTime() + 7*24*60*60*1000,"yyyy-MM-dd");
                 //默认开始时间
                 $scope.formRefer.endTime = endDate;
                 $scope.formRefer.startTime = startDate;
             }
            //重置
            $scope.resetData = function() {
                $scope.sortName = "4";
                $scope.sortType = "1";
                $scope.formRefer = {};
                initTime();
            }
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 250);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 80);
            }
            /**
             * 初始化
             */
            function initInfo() {
                //获取禅道部门
                $scope.primaryDepartment = [];
                workHoursLoadService.getProDeptChildren('0').then(function(data) {
                    $scope.primaryDepartment = data.data;
                })
                $scope.departmentList = [];
                //获取禅道产品线
                $scope.productLineList = [];
                workHoursLoadService.getZentaoProductLineList().then(function(data) {
                    $scope.productLineList = data.data;
                })
                $scope.projectList = [];
                workHoursLoadService.getProjectListByLine().then(function(data) {
                    $scope.projectList = data.data;
                });
                //获取员工信息
                $scope.employeeList = [];
                selectEmployeesByDepartment();
                getData();
            }
            //一级部门改变时，查询所属下级部门
            $scope.getDepartmentData = function (){
                workHoursLoadService.getProDeptChildren($scope.formRefer.primaryDepartment).then(function(data) {
                    $scope.departmentList = data.data;
                });
            }
            //产品线改变时，查询所属项目
            $scope.getProjectList = function (){
                workHoursLoadService.getProjectListByLine($scope.formRefer.productLine).then(function(data) {
                    $scope.projectList = data.data;
                });
            }
            /**
             * 开始时间
             */
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true; //开始时间
                $scope.openedEnd = false;
            };

            /**
             *
             *  结束时间
             */
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true; //结束时间
            };
            //获取部门所有员工
            function selectEmployeesByDepartment() {
                var urlData = {
                    'primaryDepartment':$scope.formRefer.primaryDepartment,
                    'department':$scope.formRefer.department
                };
                workHoursLoadService.selectEmployeesByDepartment(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                            $scope.employeeList = data.data;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            //获取所有数据
            function getData() {
                var urlData = {
                    'primaryDepartment':$scope.formRefer.primaryDepartment,
                    'department':$scope.formRefer.department,
                    'productLine':$scope.formRefer.productLine,
                    'project':$scope.formRefer.project,
                    'assignedTo':$scope.formRefer.assignedTo,
                    'startTime':inform.format($scope.formRefer.startTime,'yyyy-MM-dd'),
                    'endTime':inform.format($scope.formRefer.endTime,'yyyy-MM-dd'),
                    'sortType':$scope.sortType,
                    'sortName':$scope.sortName
                };
                workHoursLoadService.selectWorkHoursLoadData(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                            $scope.dataList = data.data;
                            angular.forEach(data,function (index){
                                index.showDetail = false;
                                index.detailList = [];
                            })
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            /**
             *下载工时负载率
             */
            $scope.downloadRateExcel = function() {
                var urlData = {
                    'primaryDepartment':$scope.formRefer.primaryDepartment,
                    'department':$scope.formRefer.department,
                    'productLine':$scope.formRefer.productLine,
                    'project':$scope.formRefer.project,
                    'assignedTo':$scope.formRefer.assignedTo,
                    'startTime':inform.format($scope.formRefer.startTime,'yyyy-MM-dd'),
                    'endTime':inform.format($scope.formRefer.endTime,'yyyy-MM-dd'),
                    'sortType':$scope.sortType,
                    'sortName':$scope.sortName
                };
                inform.modalInstance("确定要下载工时负载率数据表吗？").result.then(function() {
                    inform.downLoadFile('zentaoTask/downloadRateExcel',urlData,"工时负载率.xlsx");
                });
            };
            //刷新工时负载率基础数据
            $scope.refreshEffortRate = function (item) {
                workHoursLoadService.refreshEffortRate().then(function (data) {
                            inform.common(data.message);
                        }
                    );
            }
            //修改预估时间
            $scope.updateEstimate = function (item) {
                if(item.estimate === '' || item.estimate == null){
                    inform.common("内容不允许为空");
                    return;
                }
                if((item.estimate - item.consumed) < 0){
                    inform.common("剩余时间不能小于0");
                    return;
                }
                var urlData ={};
                //“已消耗工时=0”的任务应确保【剩余工时】=【预计工时】；
                if (item.consumed === 0) {
                    urlData = {
                        'id': item.taskId,
                        'estimatedWorkHours': item.estimate,
                        'consumedWorkHours': item.consumed,
                        'leftWorkHours': item.estimate
                    };
                } else {
                    urlData = {
                        'id': item.taskId,
                        'estimatedWorkHours': item.estimate,
                        'consumedWorkHours': item.consumed,
                        'leftWorkHours': (item.estimate - item.consumed)
                    };
                }

                workHoursLoadService.updateEstimate(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                            if(item.consumed !== 0){
                                inform.common("任务:"+item.taskName+"，预计时间修改为"+urlData.estimatedWorkHours);
                            }else{
                                inform.common("任务:"+item.taskName+"，预计时间修改为"+urlData.estimatedWorkHours
                                +",剩余时间修改为"+urlData.leftWorkHours);
                                item.left = urlData.leftWorkHours;
                            }
                            item.estimatedEdit = false;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });

            }//修改剩余时间
            $scope.updateLeft = function (item) {
                if(item.consumed === 0){
                    inform.common("已消耗时间为0时，剩余时间不允许修改");
                    return;
                }
                if(item.left === '' || item.left == null){
                    inform.common("内容不允许为空");
                    return;
                }
                var urlData = {
                    'id':item.taskId,
                    'consumedWorkHours':item.consumed,
                    'leftWorkHours':item.left
                };
                workHoursLoadService.updateLeft(urlData).then(function (data) {
                        if (data.code===AgreeConstant.code) {
                            inform.common("id为"+item.taskId+"的记录，剩余时间修改为"+item.left);
                            item.leftEdit = false;
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });

            }
            //切换折叠
            $scope.getDetail =  function (m) {
                m.showDetail = m.showDetail !== true;
                if (m.showDetail) {
                    //查询任务明细
                    var urlData = {
                        'startTime':inform.format($scope.formRefer.startTime,'yyyy-MM-dd'),
                        'endTime':inform.format($scope.formRefer.endTime,'yyyy-MM-dd'),
                        'taskIds':m.taskIds
                    };
                    workHoursLoadService.selectDetailList(urlData).then(function (data) {
                            if (data.code===AgreeConstant.code) {
                                m.detailList = data.data;
                            } else {
                                inform.common(data.message);
                            }
                        });
                }
            }
            /**
             * 按部门排序
             */
            $scope.orderByPrimary = function () {
                $scope.primaryDepartmentSortParamFlag = !$scope.primaryDepartmentSortParamFlag;
                $scope.sortName = "1";
                //判断排序规则
                if ($scope.primaryDepartmentSortParamFlag) {
                    $scope.sortType = "1";
                } else {
                    $scope.sortType = "0";
                }
                getData();
            };
            /**
             * 按小组排序
             */
            $scope.orderByDepartment = function () {
                $scope.departmentSortParamFlag = !$scope.departmentSortParamFlag;
                $scope.sortName = "2";
                //判断排序规则
                if ($scope.departmentSortParamFlag) {
                    $scope.sortType = "1";
                } else {
                    $scope.sortType = "0";
                }
                getData();
            };
            /**
             * 按总剩余工时排序
             */
            $scope.orderByLeftWorkHoursTotal = function () {
                $scope.orderByLeftWorkHoursTotalFlag = !$scope.orderByLeftWorkHoursTotalFlag;
                $scope.sortName = "3";
                //判断排序规则
                if ($scope.orderByLeftWorkHoursTotalFlag) {
                    $scope.sortType = "1";
                } else {
                    $scope.sortType = "0";
                }
                getData();
            };
            /**
             * 按工时负载率排序
             */
            $scope.orderByWorkHoursLoadRate = function () {
                $scope.orderByWorkHoursLoadRateFlag = !$scope.orderByWorkHoursLoadRateFlag;
                $scope.sortName = "5";
                //判断排序规则
                if ($scope.orderByWorkHoursLoadRateFlag) {
                    $scope.sortType = "1";
                } else {
                    $scope.sortType = "0";
                }
                getData();
            };
            /**
             * 按可支配工时排序
             */
            $scope.orderByDisposableWorkHours = function () {
                $scope.orderByDisposableWorkHoursFlag = !$scope.orderByDisposableWorkHoursFlag;
                $scope.sortName = "4";
                //判断排序规则
                if ($scope.orderByDisposableWorkHoursFlag) {
                    $scope.sortType = "1";
                } else {
                    $scope.sortType = "0";
                }
                getData();
            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
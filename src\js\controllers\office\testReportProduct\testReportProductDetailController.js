(function () {
    app.controller("testReportDetailController", ['testReportProductService','$stateParams','comService', '$rootScope', '$scope', '$state', '$modal', 'inform', 'Trans', 'AgreeConstant', '$http',
        function (testReportProductService,$stateParams,comService, $rootScope, $scope, $state, $modal, inform, Trans, AgreeConstant, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //初始化页面信息
            initPages();
            $scope.one={};
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var  divHeight = clientHeight -200;
                $("#divTBDisDetails").height(divHeight);
                $("#subDivTBDisDetails").height(divHeight-50);
            }

            function initPages() {
            	 //详情数据
                $scope.itemInfo = reportListConfig[$stateParams.functionName];
                $scope.headMap = $scope.itemInfo.subHeadMap[$stateParams.index];
                getData();
            }
            /**
             * 查询明细信息
             */
           function getData(){
        	   var details= JSON.parse($stateParams.param);
      		 	//拼装下载内容
				var urlData={
					'param': details,//参数
	            	'className': $scope.itemInfo.className,//类名
	            	'function': [$scope.itemInfo.subGetData[$stateParams.index]], //方法名
	            	'size':10000
				};
        		urlData.param.product = $stateParams.product;
        		urlData.param.account = $stateParams.account;
        		//求合计
        		if ($scope.itemInfo.calculateSum!=null){
					urlData.calculateSum = $scope.itemInfo.calculateSum;
				}
        		//求比率
        		if ($scope.itemInfo.calculateRate!=null){
					urlData.calculateRate = $scope.itemInfo.calculateRate;
				}
				testReportProductService.getData(urlData).then(function(data) {
					if (data.code===AgreeConstant.code) {
						angular.forEach(data.data, function (detail, i) {
							//根据放入的名称不同，赋值给不同的变量
							$scope.tableList = detail.list;
		      		 	});
					} else {
						inform.common(data.message);
					}
					//设置列表的高度
		            setDivHeight();
				},
				function(error) {
					inform.common(Trans("tip.requestError"));
				});
           }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();
(function() {
	'use strict';
	app
	.factory('HistoryDataService', historyDataService);
	historyDataService.$inject=['HttpService','$rootScope'];
	function historyDataService(HttpService,$rootScope){
		var service={
			getAllHistoryData:getAllHistoryData,
			delHistoryData:delHistoryData,
			historyDataByConditon:historyDataByConditon,
            getCookie:getCookie
		};
		return service;
        function getCookie(name) 
            { 
                var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
             
                if(arr=document.cookie.match(reg))
             
                    return unescape(arr[2]); 
                else 
                    return null; 
            } 
        function getAllHistoryData(){
			return HttpService.post($rootScope.gateInfoApi+'lms/historyData/getAllHistoryData?name='+getCookie('name'),{});
		}
		function delHistoryData(registerCode){
			return HttpService.post($rootScope.gateInfoApi+'lms/historyData/delHistoryData?name='+getCookie('name')+'&registerCode='+registerCode,{});
		}
		function historyDataByConditon(organizationId,organizationName,startTime,endTime,bankName){
			var param='?organizationId='+organizationId+'&organizationName='+organizationName+'&startTime='+startTime+'&endTime='+endTime+'&bankName='+bankName+'&name='+getCookie('name');
			return HttpService.post($rootScope.gateInfoApi+'lms/historyData/historyDataByConditon'+param,{});
		}
	}
})();
(function () {
    app.controller("report_0004InAdvanceAddController", ['comService', '$http', 'LocalCache', '$rootScope', '$state', '$stateParams', '$scope', '$modal', 'reportService', 'inform', 'Trans', 'AgreeConstant',
        function (comService, $http, LocalCache, $rootScope, $state, $stateParams, $scope, $modal, reportService, inform, Trans, AgreeConstant) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */

            //控制评审时间字段是否必填
            $scope.planCompletionDatFlag =false;
            //校验变量
            $scope.limitList = AgreeConstant.limitList;
            //页面分页信息
            $scope.pages = {
                pageNum: '',	//分页页数
                size: '',		//分页每页大小
                total: ''		//数据总数
            };
            //新增表单绑定的参数
            $scope.spec = {};
            $scope.spec.REVIEW_RESULT = '1';
            //评委列表
            $scope.jList = [];

            //评审主题Map
            $scope.themeMap = reviewThemeListConfig;

            //产品线下拉框数据源
            $scope.lineSelect = [];

            //产品线展示Map
            $scope.lineMap = {};

            //评审级别下拉框数据源
            $scope.levelSelect = [{
                value: '0',
                label: '一级'
            }, {
                value: '1',
                label: '二级'
            }, {
                value: '2',
                label: '三级'
            }];

            //是否跟踪下拉框数据源
            $scope.trackSelect = [{
                value: '0',
                label: '是'
            }, {
                value: '1',
                label: '否'
            }];

            //评审结果下拉框数据源
            $scope.resSelect = [{
                value: '0',
                label: '有条件通过'
            }, {
                value: '1',
                label: '通过'
            }, {
                value: '2',
                label: '不通过'
            }, {
                value: '3',
                label: '未评审'
            }];

            //是否跟踪下拉框数据源
            $scope.typeMap = [{
                value: '0',
                label: '邮件'
            }, {
                value: '1',
                label: '会议'
            }];

            //初始化页面信息
            initPages();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
                var clientWidth = document.body.clientWidth;
                $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
            }

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            //当新增数据时选择日期，自动将季度回填
            $scope.dateToQuarter = function () {
                if(typeof ($scope.spec.PLAN_COMPLETION_DATE)==='undefined'
                    || '' === $scope.spec.PLAN_COMPLETION_DATE || null == $scope.spec.PLAN_COMPLETION_DATE){
                    $scope.spec.QUARTER = '';
                    return;
                }
                var month = $scope.spec.PLAN_COMPLETION_DATE.getMonth() + 1;
                $scope.spec.QUARTER = inform.dateToQuarter(month);
            };

            /**
             * 页面初始化
             */
            function initPages() {

                //获取最近关闭或者进行中的所有项目名称
                $scope.projectList = [];
                var urlData = {flag: 'doing'};
                comService.getProjectsNameByParams(urlData).then(function (data) {
                    $scope.project = angular.fromJson(data.data);
                    angular.forEach($scope.project, function (res, index) {
                        $scope.projectList.push(res);
                    })
                });
                //获取所有员工名称
                $scope.employeeList = [];
                comService.getEmployeesName().then(function (data) {
                    $scope.employee = angular.fromJson(data.data);
                    angular.forEach($scope.employee, function (res, index) {
                        $scope.employeeList.push(res.realName);
                    })
                });

                //获取员工信息
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function(data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                        $scope.employeeMap = {};
                        for(var i = 0;i < $scope.employeeList.length; i++) {
                            $scope.employeeMap[$scope.employeeList[i].realName] = $scope.employeeList[i].companyTitleLevel;
                        }
                    }
                });
            }

            /**
             * 校验评委列表中是否存在重复
             * 规则：
             * 1.重复则提示"评委×××,×××重复,请修改"并返回false
             * 2.无重复则返回true
             * @returns {boolean}
             */
            function verifyJudgeList() {
                var verifyList = [];
                var duplicate = "";
                for (var i = 0; i < $scope.jList.length; i++) {
                    if (verifyList.indexOf($scope.jList[i].judgeName) > -1
                        && duplicate.indexOf($scope.jList[i].judgeName) < 0) {
                        duplicate = duplicate.concat($scope.jList[i].judgeName).concat(",");
                    }
                    verifyList.push($scope.jList[i].judgeName);
                }
                //如果为空,说明无重复则返回true;
                if (!duplicate) {
                    return true;
                }
                //不为空,则提示哪些评委重复,并返回false
                inform.common("评委" + duplicate.substring(0, duplicate.length - 1) + "存在重复,请修改");
                return false;
            }

            //添加信息
            $scope.addInfo = function () {
                //校验jList中是否存在相同的评委
                if (!verifyJudgeList()) {
                    return;
                }
                var urlData = {
                    'excelName': 'app.office.report_0004',
                    'ID': '',
                    'QUARTER': $scope.spec.QUARTER,
                    'PLAN_COMPLETION_DATE': inform.format($scope.spec.PLAN_COMPLETION_DATE, 'yyyy-MM-dd'),
                    'PRODUCT_LINE': $scope.spec.productLine,
                    'TEM_NAME': $scope.spec.TEM_NAME,
                    'REVIEW_LEVEL': $scope.spec.REVIEW_LEVEL,
                    'IS_TRACK_ONZENTAO': $scope.spec.IS_TRACK_ONZENTAO,
                    'DOC_PAGES': $scope.spec.DOC_PAGES,
                    'PARTICIPANTS': $scope.spec.PARTICIPANTS,
                    'REVIEW_CONTENT': $scope.spec.REVIEW_CONTENT,
                    'REVIEW_MEETING_TIME': $scope.spec.REVIEW_MEETING_TIME,
                    'LIABLE_PERSON': null == $scope.spec.LIABLE_PERSON?null:$scope.spec.LIABLE_PERSON.join(','),
                    'PROJECT_MANAGER': $scope.spec.PROJECT_MANAGER,
                    'PROJECT_ADMINISTRATORS': $scope.spec.PROJECT_ADMINISTRATORS,
                    'COMMENTS': $scope.spec.COMMENTS,
                    'VERSION': $scope.spec.VERSION,
                    'REVIEW_TYPE': $scope.spec.REVIEW_TYPE,
                    'REVIEW_THEME': $scope.spec.REVIEW_THEME,
                    'judgeList': JSON.stringify($scope.jList),
                    'REVIEW_RESULT': $scope.spec.REVIEW_RESULT,
                    'REVIEW_PROBLEM_NUMBER':'0',
                    'isReview':'1'
                };
                reportService.verifyReviewExist(urlData).then(function (data) {
                    if (data.code !== AgreeConstant.code) {
                        inform.common(data.message);
                        return;
                    }
                    if (data.data > 0) {
                        inform.common("该评审已存在,请勿重复添加");
                        return;
                    }
                    addReviewInfo(urlData);
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });

            };


            /**
             * 当会议结果变化为未评审时
             */
            $scope.reviewResultChange = function (item) {
                //若选项不为“为评审”，直接返回
                if (item !== '3') {
                    $scope.planCompletionDatFlag = false;
                    return;
                }

                $scope.planCompletionDatFlag = true;
                $scope.spec.REVIEW_MEETING_TIME = '0';
                $scope.spec.REVIEW_PROBLEM_NUMBER = '0';
                $scope.spec.DOC_PAGES = '0';
                $scope.spec.REVIEW_TYPE = '1';
                $scope.duringChange();
            };


            /**
             * 新增：持续时间变化时,说明是会议
             *
             * 各评委工作量 = 持续时间
             * 评审工作量 = 各评委工作量相加
             * 评审效率 = 问题 / 评审工作量
             */
            $scope.duringChange = function () {
                //评委工作量赋值
                for (var i = 0; i < $scope.jList.length; i++) {
                    //若评委不参加，直接置为0
                    if($scope.jList[i].judgeJoin === '1'){
                        //评委工作量是会议时长
                        $scope.jList[i].judgeWorkLoad = '0';
                    }else {
                        //评委工作量是会议时长
                        $scope.jList[i].judgeWorkLoad = $scope.spec.REVIEW_MEETING_TIME;
                    }
                }

            };

            /**
             * 选择项目时，回填产品线项目经理、项目助理信息
             * @param projectId
             */
            $scope.projectChange = function(projectId){
                reportService.getProjectInfoById(projectId).then(function (data) {
                    if (data.code !== AgreeConstant.code) {
                        inform.common(data.message);
                        return;
                    }
                    data.data = angular.fromJson(data.data);
                    if (data.data.length === 0) {
                        inform.common("该项目不存在")
                    }else{
                        $scope.spec.productLine = data.data.productLine;
                        $scope.spec.PROJECT_MANAGER = data.data.projectManager;
                        $scope.spec.PROJECT_ADMINISTRATORS = data.data.projectAssistant;
                        $scope.spec.REVIEW_LEVEL = "";
                        for(var i = 0;i < $scope.levelSelect.length;i++){
                            if($scope.levelSelect[i].label === data.data.reviewGrade){
                                $scope.spec.REVIEW_LEVEL = $scope.levelSelect[i].value;
                            }
                        }
                    }

                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            };

            function addReviewInfo(urlData) {
                reportService.addReportInfo(urlData).then( function (data) {
                    if (data.code === AgreeConstant.code) {
                        layer.confirm(data.message, {
                            title: false,
                            btn: ['确定']
                        }, function (result) {
                            layer.close(result);
                            $state.go('app.office.report_0004',{flag:'notMenu'});
                        });
                    } else {
                        inform.common("新增数据失败");
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * 根据关键角色名称，获取其等级
             * @param item
             */
            $scope.setJudgeGrade = function(item){
                if(typeof (item) !== 'undefined') {
                    item.judgeGrade = $scope.employeeMap[item.judgeName];
                }
            };

            /**
             * 根据关键角色名称，遍历以获取其编号
             * @param item
             */
            $scope.setJudgeNo = function(item){
                for(var i = 0;i <$scope.employeeList.length;i++){
                    if($scope.employeeList[i].realName === item.judgeName){
                        item.employeeId = $scope.employeeList[i].employeeNo;
                    }
                }
            };

            /**
             * 新增一个评委，重新计算
             */
            $scope.addNewBind = function () {
                //评委信息
                var judge = {
                    'employeeId':'',
                    'judgeName': '',
                    'judgeGrade': '',
                    'judgeWorkLoad': '0',
                    'judgeJoin':'0'
                };
                $scope.jList.push(judge);
                //不为邮件评审，则更新评委投入工作量
                if($scope.spec.REVIEW_TYPE !== '0'){
                    $scope.duringChange();
                }
            };
            //取消一行
            $scope.deleteNewBind = function (index) {
                if (index >= 0) {
                    $scope.jList.splice(index, 1);
                }
            };

            /**
             * 当新增评审类型变化为邮件时
             */
            $scope.reviewTypeAdd = function () {
                if ($scope.spec.REVIEW_TYPE === '0') {
                    //类型为邮件类型,将会议持续时间置为0
                    $scope.spec.REVIEW_MEETING_TIME = '';
                }
                $scope.duringChange();
            };

            /**
             *  修改评审时间
             */
            $scope.updateDateOne = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.updateTimeOne = true;
            };

            /**
             * 新增评审时间
             */
            $scope.insertDateOne = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.insertTimeOne = true;
            };

            /**
             * 评委是否参与选项改变后触发的事件
             */
            $scope.judgeJoinChange = function(item){
                //状态为参加
                if(item.judgeJoin === '1'){
                    item.judgeWorkLoad = '0';
                }else{
                    //若为会议类型，则为工作量复制，否则清空
                    if($scope.spec.REVIEW_TYPE !== '0'){
                        item.judgeWorkLoad = $scope.spec.REVIEW_MEETING_TIME
                    }else{
                        item.judgeWorkLoad = '';
                    }
                }
            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();

(function () {
    app.controller("trainGloriousController", ['$rootScope', 'comService', 'SystemService', 'tvService', '$scope', '$state','$location', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http', 'listUtil',
        function ($rootScope, comService, SystemService, tvService, $scope, $state,$location, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http, listUtil) {
            listUtil.init();
            //窗体大小变化时重新计算高度
            $(window).resize(setCss);
            // 从后端获取相关数据
            getData();
            // 处理标题当前年份和时间
            $scope.currentQuarter = listUtil.getCurrentQuarter();
            if ($scope.currentQuarter === '四') {
                $scope.currentYear = listUtil.getLastYear();
            } else {
                $scope.currentYear = listUtil.getCurrentYear();
            }
            /**
             * 获取信息
             */
            function getData() {
                tvService.getTrainGloriousData().then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        var data = angular.fromJson(result.data);
                        $scope.itemList = dealTable(data, 'department');
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /*
            处理table表格
             */
            function dealTable(arr, value) {
                let dataInfo = {};
                arr.forEach(item => {
                    let key = item[value];
                    if (!dataInfo[key]) {
                        dataInfo[key] = {
                            key,
                            children: []
                        }
                    }
                    dataInfo[key].children.push(item);
                });
                return Object.values(dataInfo);
            }
            /*
             * 设置css样式
             */
            function setCss() {
                //网页可见区域宽度
                let clientWidth = document.body.clientWidth;
                $('.th').css({'font-size': parseInt(clientWidth / 85)});
                $('.td').css({'font-size': parseInt(clientWidth / 107)});
            }
        }])
})();

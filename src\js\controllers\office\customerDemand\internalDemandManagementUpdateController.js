(function () {
    app.controller("internalDemandManagementUpdateController", ['internalDemandManagementService','comService','$rootScope', '$scope', '$stateParams','$modal', 'inform', 'LocalCache', 'Trans', 'AgreeConstant', '$http','$state', 'proProjectModule',
        function (internalDemandManagementService, comService, $rootScope, $scope,$stateParams, $modal, inform, LocalCache, Trans, AgreeConstant, $http,$state, proProjectModule) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //校验变量
            $scope.limitList = AgreeConstant.limitList;
            $scope.isDemandImportMap = {'0':'否','1':'是'};
            //初始化需求是否导入
            $scope.demandCompleteMap = [{
                     value: '已拒绝',
                     label: '已拒绝'
                  },
                  {  value: '尚未制定计划',
                     label: '尚未制定计划'
                  },
                  {  value: '未到期',
                     label: '未到期'
                  },
                  {  value: '临期',
                     label: '临期'
                  },
                  {  value: '延期未完成',
                     label: '延期未完成'
                  },
                  {  value: '延期完成',
                     label: '延期完成'
                  },
                  {  value: '按时完成',
                     label: '按时完成'
                  }
                  ];
            //初始化页面信息
            initPages();


            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

		    //根据code获取完成情况状态
            $scope.getCompleteStatus = function(status) {
              if (status === 1) {
                return '是';
              } else if (status === 0) {
                return '否';
              } else {
                return '';
              }
            };

            /**
             * 页面初始化
             */
            function initPages() {
                //获得内部需求详情
                getStoryDetail();
            }
            /**
            *获得内部需求详情
            */
            function getStoryDetail(){
                //获取查询页面传来的Json
                var urlParam = {id:$stateParams.id};
                //获得内部需求详情
                internalDemandManagementService.getInternalDemandDetail(urlParam).then(function (data) {
                        $scope.changeParam=data.data;
                        $scope.changeParam.processApprovalStatus = $scope.changeParam.processApprovalStatus + "";
                });
            }
            //修改信息
            $scope.updateInfo = function () {
                var urlData = {
                    'dingdingFlowId':$scope.changeParam.dingdingFlowId,
                    'processApprovalStatus': $scope.changeParam.processApprovalStatus,
                    'teamLeaderReceiveTime': inform.format($scope.changeParam.teamLeaderReceiveTime,"yyyy-MM-dd HH:mm:ss"),
                    'designStaffReceiveTime': inform.format($scope.changeParam.designStaffReceiveTime,"yyyy-MM-dd HH:mm:ss"),
                    'requireEndTime': inform.format($scope.changeParam.requireEndTime,"yyyy-MM-dd"),
                    'storyExpand':{
                        'evaluationPlanEndDate': inform.format($scope.changeParam.storyExpand.evaluationPlanEndDate,"yyyy-MM-dd"),
                        'evaluationRealityEndDate': inform.format($scope.changeParam.storyExpand.evaluationRealityEndDate,"yyyy-MM-dd"),
                        'temporaryVersionPlanEndDate': inform.format($scope.changeParam.storyExpand.temporaryVersionPlanEndDate,"yyyy-MM-dd"),
                        'temporaryVersionRealityEndDate': inform.format($scope.changeParam.storyExpand.temporaryVersionRealityEndDate,"yyyy-MM-dd"),
                        'formalVersionPlanEndDate': inform.format($scope.changeParam.storyExpand.formalVersionPlanEndDate,"yyyy-MM-dd"),
                        'formalVersionRealityEndDate': inform.format($scope.changeParam.storyExpand.formalVersionRealityEndDate,"yyyy-MM-dd"),
                        'noReleasePlanEndDate': inform.format($scope.changeParam.storyExpand.noReleasePlanEndDate,"yyyy-MM-dd"),
                        'noReleaseRealityEndDate': inform.format($scope.changeParam.storyExpand.noReleaseRealityEndDate,"yyyy-MM-dd")
                    },
                    'demandCompleteMessage': $scope.changeParam.demandCompleteMessage
                };

                internalDemandManagementService.updateInternalDemand(urlData).then(function (data) {
                    //请求失败则弹出后台返回信息
                     if (data.code === AgreeConstant.code) {
                     inform.common('维护内部需求信息成功！');
                     window.history.go(-1);
                     } else {
                         inform.common(data.message);
                     }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();

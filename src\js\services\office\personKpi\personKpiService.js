(function() {
    'use strict';
    app.factory('personKpiService', personKpiService);
    personKpiService.$inject = ["HttpService", '$rootScope'];

    function personKpiService(HttpService, $rootScope) {
        var service = {
        		getData: getData,
        		getHead: getHead
        };
        return service;
        /**
         * 查询角色信息
         */
        function getData(urlData) {
            	return HttpService.post($rootScope.getWaySystemApi + 'personKpi/getData', urlData);
        }
        /**
         * 查询表头信息
         */
        function getHead(urlData) {
            	return HttpService.post($rootScope.getWaySystemApi + 'personKpi/getHead', urlData);
        }
    }
})();
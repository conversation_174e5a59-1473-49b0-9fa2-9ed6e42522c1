(function () {
	app.controller("personRepositoryDetailController", ['comService', '$http', '$rootScope', '$state', '$scope', '$modal', 'personRepositoryDetailService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$stateParams',
		function (comService, $http, $rootScope, $state, $scope, $modal, personRepositoryDetailService, inform, Trans, AgreeConstant, LocalCache, $stateParams) {
			//被勾选的集合
			$scope.selecteds = [];
			//全选状态
			$scope.select_all = false;
			// 是否是历史权限
			$scope.isHistory = false;
			//查询页面
			$scope.getData = getData;
			//全选方法
			$scope.selectAll = selectAll;
			//单选方法
			$scope.selectOne = selectOne;
			//初始化页面信息
			initPages();
            //调出人员
            $scope.callOutEmployees = false;
            //判断按钮是否具有权限
            getButtonPermission();
			// 查询条件
			$scope.formRefer = {};

			/**
			 * 页面初始化
			 */
			function initPages() {
				$scope.selecteds = [];
				//初始化选中状态为非选中
				angular.forEach($scope.repositoryList, function (r) {
					r.checked = false
				});
			}


			/**
			 * 按钮权限管理
			 */
			function getButtonPermission(){
				var buttons = {
					'Button-SvnRepositoryManage-callOut':'callOutEmployees' //调出人员
				};
				var urlData = {
					'userId':LocalCache.getSession("userId"),
					'parentPermission':'Button-SvnRepositoryManage',
					'buttons':buttons
				};
				comService.getButtonPermission(urlData,$scope);
			}
			/**
			 * 根据分页显示仓库
			 * pageNum 当前页数
			 */
			function getData() {

				//销毁表格
				$scope.showDataTable = 0;

				$scope.selecteds = []; //初始化被勾选的集合

				//返回的仓库信息列表
				$scope.repositoryList = [];

				var urlData = {
					'repositoryid': $scope.formRefer.repositoryid,
					'employeeId': $scope.formRefer.employeeNo,
					'isHistory': $scope.isHistory
				};

				personRepositoryDetailService.findRepositoryListByPage(urlData).then(function (data) {
					//重新生成Table
					$scope.showDataTable = 1;
					if (data.code === AgreeConstant.code) {
						$scope.repositoryList = angular.fromJson(data.data.list);
						if ($scope.repositoryList.length === 0) {
							inform.common(Trans("tip.noData"));
							$scope.pages = inform.initPages();
						}
					} else {
						inform.common(data.message);
					}
					setTimeout(showDataTable, 500);
				}, function (error) {
					inform.common(Trans("tip.requestError"));
				});
			}

			/**
			 *调用DataTable组件冻结表头和左侧及右侧的列
			 */
			function showDataTable() {
				$('#fixedLeftAndTop').DataTable({
					//可被重新初始化
					retrieve: true,
					//自适应高度
					scrollY: 'calc(100vh - 200px)',
					scrollX: true,
					scrollCollapse: false,
					//控制每页显示
					paging: false,
					//search框显示
					searching: false,
					//排序箭头
					ordering: false,
					//底部统计数据
					info: false,
				});
			}

			/**
			 * 全选
			 */
			function selectAll() {
				$scope.select_all = !$scope.select_all;
				if ($scope.select_all) {
					$scope.selecteds = [];
					angular.forEach($scope.repositoryList, function (i) {
						i.checked = true;
						$scope.selecteds.push(i);
					});
				} else {
					angular.forEach($scope.repositoryList, function (i) {
						i.checked = false;
					});
					$scope.selecteds = [];
				}
			}

			/**
			 * 单选
			 */
			function selectOne(i) {
				i.checked = !i.checked;
				$scope.select_all = false;
				var index = $scope.selecteds.indexOf(i);
				if (index === -1 && i.checked) {
					$scope.selecteds.push(i);
				} else if (index !== -1 && !i.checked) {
					$scope.selecteds.splice(index, 1);
				}
			}

			// 权限回收
			$scope.confirmRecovery = confirmRecovery;
			function confirmRecovery(item) {
				if ($scope.selecteds.length === 0) {
					inform.common('请选择仓库进行操作！');
				} else {
					var modalInstance = $modal.open({
						templateUrl: 'myModalContent',
						controller: 'ModalInstanceCtrl',
						size: "sm",
						resolve: {
							items: function () {
								return "是否确认收回权限";
							}
						}
					});
					var urlData ={
						'repositoryList' : $scope.selecteds,
						'employeeId':$scope.formRefer.employeeNo
					}
					modalInstance.result.then(function () {
						personRepositoryDetailService.callOutModuleEmployee(urlData).then(function (data) {
							if (data.code === AgreeConstant.code) {
								getData();
								inform.common("调出人员成功");
							} else {
								inform.common(data.message);
							}
						}, function () {
							inform.common(Trans("tip.requestError"));
						});
					});
				}
			}

			// 历史权限
			$scope.toPersonHistoryDetail = function () {
				$state.go("app.office.personRepositoryDetail", {
					'repositoryid': $scope.formRefer.repositoryid,
					'employeeNo': $scope.formRefer.employeeNo,
					'isHistory': '1'
				});
			}
			// 返回
			$scope.toPersonDetail = function () {
				$state.go("app.office.personRepositoryDetail", {
					'repositoryid': $scope.formRefer.repositoryid,
					'employeeNo': $scope.formRefer.employeeNo,
					'isHistory': '0'
				});
			}

			$scope.toRepositoryDetail = function (item) {
				if ($scope.isHistory) {
					$state.go("app.office.repositoryEmployeePerHis", { repositoryid: item.repositoryName, employeeNo: $scope.formRefer.employeeNo,repositoryType:item.repositoryType });
				} else {
					$state.go("app.office.repositoryPermissionDetail", { repositoryid: item.repositoryName, employeeNo: $scope.formRefer.employeeNo,repositoryType:item.repositoryType, lastPage: 'personRepository' });
				}
			}

			// 页面加载后触发
			$scope.$watch('$viewContentLoaded', function () {
				if ($stateParams.repositoryid) {
					$scope.formRefer.repositoryid = $stateParams.repositoryid;
				}
				if ($stateParams.employeeNo){
					$scope.formRefer.employeeNo = $stateParams.employeeNo;
				}
				$scope.isHistory = $stateParams.isHistory === '1';
				getData();
			});
		}]);
})();


(function() {
    'use strict';
  app.factory('repositoryService', repositoryService);
  repositoryService.$inject=["HttpService",'$rootScope'];

  function repositoryService(HttpService,$rootScope){
    var service={
      findRepositoryListByPage:findRepositoryListByPage,
      loadRepositoryToExcel:loadRepositoryToExcel,
      addRepository:addRepository,
      addRepositoryEmployee:addRepositoryEmployee,
      addModule:addModule,
      addModuleEmployee:addModuleEmployee,
      addModuleEmployeeChange:addModuleEmployeeChange,
      addRepositoryModule:addRepositoryModule,
      getRepositoryById:getRepositoryById,
      getRepositoryEmployee:getRepositoryEmployee,
      getModule:getModule,
      getModuleEmployee:getModuleEmployee,
      getModulesAndEmployees:getModulesAndEmployees, 
      saveRepository:saveRepository,
      upRepositoryEmployee:upRepositoryEmployee,
      saveModule:saveModule,
      upModuleEmployee:upModuleEmployee,
      deleteModuleEmployee:deleteModuleEmployee,
      getDefaultModuleId:getDefaultModuleId
    };
    return service;
    /**
	 * 获取默认模块ID
	 */
	function getDefaultModuleId(urlData) {
		return HttpService.post($rootScope.getWaySystemApi+'repository/getDefaultModuleId',urlData);
	}
	
    /**
	 * 获取根据条件查询出来的仓库的信息
	 */
	function findRepositoryListByPage(urlData) {
		return HttpService.post($rootScope.getWaySystemApi+'repository/selectRepositoryByMap',urlData);
	}
	
	/**
     * Excel导出
     */
    function loadRepositoryToExcel(urlData) {
    	return HttpService.get($rootScope.getWaySystemApi+'repository/loadRepositoryToExcel?',urlData);
     }
      
    /**
 	 * 新增一条仓库数据
 	 */
 	function addRepository(urlData) {
 		return HttpService.post($rootScope.getWaySystemApi+'repository/insertRepository',urlData);
 	}
	
 	/**
	 * 新增仓库与人员关系
	 */
	function addRepositoryEmployee(urlData) {
		return HttpService.post($rootScope.getWaySystemApi+'repository/insertRepositoryEmployee',urlData);
	}
	
	/**
	 * 新增一条模块数据
	 */
	function addModule(urlData) {
		return HttpService.post($rootScope.getWaySystemApi+'module/insertModule',urlData);
	}
	
	/**
	 * 新增模块与人员关系
	 */
	function addModuleEmployee(urlData) {
		return HttpService.post($rootScope.getWaySystemApi+'module/insertModuleEmployee',urlData);
	}
	
	/**
	 * 新增模块与人员关系到变动表
	 */
	function addModuleEmployeeChange(urlData) {
		return HttpService.post($rootScope.getWaySystemApi+'module/insertModuleANDEmployeeIDchange',urlData);
	}
	
	/**
	 * 新增模块与仓库关系
	 */
	function addRepositoryModule(moduleid,repositoryid) {
		var urlData={'moduleid':moduleid,'repositoryid':repositoryid};
		return HttpService.post($rootScope.getWaySystemApi+'repository/insertRepositoryModule',urlData);
	}
	
	/**
	 * 回填仓库详情
	 */
    function getRepositoryById(repositoryid) {
    	return HttpService.post($rootScope.getWaySystemApi+'repository/getRepositoryById',repositoryid);
    }
	
    /**
	 * 查询所修改的仓库下的人员信息
	 */
	function getRepositoryEmployee(repositoryid) {				
		return HttpService.post($rootScope.getWaySystemApi+'repository/getRepositoryEmployee',repositoryid);
	}
	
	/**
     * 回填仓库下的模块信息
     */
	function getModule(moduleid) {				
		return HttpService.post($rootScope.getWaySystemApi+'module/getModuleById',moduleid);
	}
	
	/**
	 * 查询所修改的模块下的人员信息
	 */
	function getModuleEmployee(moudleid) {				
		return HttpService.post($rootScope.getWaySystemApi+'module/getModuleEmployee',moudleid);
	}
	
	/**
     * 获取仓库下所有的模块与开发者信息
     */
    function getModulesAndEmployees(repositoryid,moduleName) {				
		return HttpService.get($rootScope.getWaySystemApi+'repository/getModulesAndEmployees',{'repositoryid':repositoryid,'moduleName':moduleName});
	}
	
    /**
     * 保存修改后的仓库信息
     */
    function saveRepository(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi+'repository/saveRepository',urlData);
    }
    
    /**
	 * 保存仓库的人员权限
	 */
	function upRepositoryEmployee(urlData) {				
		return HttpService.post($rootScope.getWaySystemApi+'repository/upRepositoryEmployee',urlData);
	}
    
	/**
     * 保存修改后的模块信息
     */
    function saveModule(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi+'module/saveModule',urlData);
    }
    
    /**
	 * 保存模块的人员权限
	 */
	function upModuleEmployee(urlData) {				
		return HttpService.post($rootScope.getWaySystemApi+'module/upModuleEmployee',urlData);
	}
    
	/**
	 * 删除模块与人员关系
	 */
	function deleteModuleEmployee(urlData) {
		return HttpService.post($rootScope.getWaySystemApi+'module/deleteModuleEmployee',urlData);
	}
	
  }
})();

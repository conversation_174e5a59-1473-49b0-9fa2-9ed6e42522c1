<!DOCTYPE html>
<html lang="en" data-ng-app="app">

<head>
    <meta charset="utf-8" />
    <title>OfficeSite</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    <link rel="shortcut icon" type="image/ico" href="img/basic/favicon.ico" />
    <!-- bower:css -->
    <!-- endbower -->
    <link rel="stylesheet" href="library/component/css/bootstrap.css" type="text/css" />
    <link rel="stylesheet" href="library/component/css/animate.css" type="text/css" />
    <link rel="stylesheet" href="library/component/css/font-awesome.min.css" type="text/css" />
    <link rel="stylesheet" href="library/component/css/simple-line-icons.css" type="text/css" />
    <link rel="stylesheet" href="library/component/css/font.css" type="text/css" />
    <link rel="stylesheet" href="library/component/css/chosen.min.css" type="text/css" />
    <link rel="stylesheet" href="library/component/css/zTreeStyle/zTreeStyle.css">
    <link rel="stylesheet" href="library/component/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="css/basic/app.css" type="text/css" />
    <link rel="stylesheet" href="css/basic/login.css" type="text/css" />
    <link rel="stylesheet" href="css/basic/public.css" type="text/css" />
    <link rel="stylesheet" href="library/component/css/jquery-ui.css" type="text/css" />
    <link rel="stylesheet" href="library/component/css/dataTables.jqueryui.min.css" type="text/css" />
    <link rel="stylesheet" href="library/component/css/fixedColumns.jqueryui.min.css" type="text/css" />
    <!--引入多文件上传CSS-->
    <link rel="stylesheet" type="text/css" href="library/component/mail/webuploader.css">
    <!-- **项目新增样式文件 -->
    <link rel="stylesheet" href="library/component/css/zui.calendar.min.css">

    <!-- Vue3 Components CSS -->
    <link rel="stylesheet" href="library/vue-components/vue-components.css">
</head>

<body ng-controller="AppCtrl">
    <div class="app" id="app"
        ng-class="{'app-header-fixed':app.settings.headerFixed, 'app-aside-fixed':app.settings.asideFixed, 'app-aside-folded':app.settings.asideFolded, 'app-aside-dock':app.settings.asideDock, 'container':app.settings.container}"
        ui-view></div>

    <script src="library/jquery/jquery-3.5.1.js"></script>
    <script src="library/jquery/jquery.dataTables.js"></script>
    <script src="library/jquery/dataTables.jqueryui.min.js"></script>
    <script src="library/jquery/dataTables.fixedColumns.min.js"></script>
    <!-- 时间控件 -->
    <script src="library/jquery/bootstrap-datetimepicker.min.js"></script>
    <script src="library/jquery/bootstrap-datetimepicker.zh-CN.js"></script>
    <script src="library/component/ztree/jquery.ztree.all-3.5.js"></script>
    <script src="library/component/ztree/fuzzysearch.js"></script>

    <!-- Day.js时间处理库 -->
    <script src="library/component/dayjs/dayjs.min.js"></script>

    <script src="library/angular/angular.js"></script>
    <script src="library/angular/angular-sanitize/angular-sanitize.js"></script>
    <script src="library/angular/angular-ui-router/angular-ui-router.js"></script>
    <script src="library/angular/ngstorage/ngStorage.js"></script>
    <script src="library/angular/angular-bootstrap/ui-bootstrap-tpls.js"></script>
    <script src="library/angular/angular-translate/angular-locale_zh-cn.js"></script><!-- 日期汉化文件-->
    <script src="library/angular/oclazyload/ocLazyLoad.js"></script>

    <!--  国际化 -->
    <script src="library/angular/angular-translate/angular-translate.js"></script>
    <script src="library/angular/angular-translate/loader-static-files.js"></script>
    <script src="library/angular/angular-translate-loader-partial/angular-translate-loader-partial.min.js"></script>

    <!-- JQurey插件 -->
    <script src="library/jquery/datatables/plugins/treeGrid.js"></script>

    <!-- babel-->
    <script src="library/component/polyfill.min.js"></script>

    <!-- inject ui component -->
    <script src="library/component/bootstrap.js"></script>
    <script src="library/component/layer.js"></script>
    <script src="library/component/chosen.jquery.min.js"></script>
    <script src="library/angular/angular-chosen.js"></script>
    <!--RSA加解密-->
    <script src="library/RSA/jsencrypt.min.js"></script>

    <!-- Vue3 Components Library -->
    <script src="library/vue-components/vue-components.umd.js"></script>

    <!-- App -->
    <script src="js/app.add.js"></script>
    <script src="js/app.js"></script>
    <script src="js/config.js"></script>
    <script src="js/config.lazyload.js"></script>
    <script src="js/config.router.js"></script>
    <script src="js/config.const.js"></script>
    <script src="js/config.rootScopeConst.js"></script>
    <script src="js/config.router.office.js"></script>
    <script src="js/main.js"></script>
    <script src="js/services/base/ui-load.js"></script>
    <!-- inject filter -->
    <script src="js/filters/base/percent.js"></script>
    <script src="js/filters/base/characters.js"></script>
    <script src="js/filters/base/htmlTranslate.js"></script>
    <script src="js/filters/base/millisecondFilter.js"></script>
    <!-- inject directives -->
    <script src="js/directives/base/ui-butterbar.js"></script>
    <script src="js/directives/base/ui-fullscreen.js"></script>
    <script src="js/directives/base/ui-jq.js"></script>
    <script src="js/directives/base/ui-nav.js"></script>
    <script src="js/directives/base/ui-scroll.js"></script>
    <script src="js/directives/base/ui-toggleclass.js"></script>
    <script src="js/directives/base/fileModel.js"></script>
    <script src="js/directives/base/enter.js"></script>
    <script src="js/directives/base/ngVerify.js"></script>
    <script src="js/directives/base/ngCancel.js"></script>
    <script src="js/directives/base/verifyPage.js"></script>
    <script src="js/directives/base/searchTitle.js"></script>
    <script src="js/directives/base/setTypeUrl.js"></script>
    <script src="js/directives/base/md5.js"></script>
    <script src="js/directives/base/bigScreen.js"></script>
    <script src="js/directives/base/auto-resize.js"></script>
    <script src="js/directives/base/click-outside.js"></script>

    <!-- inject services -->
    <script src="js/services/base/jsTranslate.js"></script>
    <script src="js/services/base/localStorage.js"></script>
    <script src="js/services/base/Microservice.js"></script>
    <script src="js/services/base/inform.js"></script>
    <script src="js/services/base/HttpService.js"></script>
    <script src="js/services/base/LoginService.js"></script>
    <script src="js/services/base/systemService.js"></script>
    <script src="js/services/base/messageService.js"></script>
    <script src="js/services/base/ConfigService.js"></script>
    <script src="js/services/base/dayjsService.js"></script>

    <!-- inject controllers -->
    <script src="js/controllers/common/ModalInstanceCtrl.js"></script>
    <script src="js/controllers/login/login.js"></script><!-- 登录-->
    <script src="js/controllers/bench/index_bench.js"></script><!-- 个人工作台-->

    <!-- **项目新增js文件 -->
    <!-- 公共部分 -->
    <script src="library/component/mail/officeFileTool.js"></script>
    <script src="library/component/mail/webuploader.js"></script>
    <!-- office services -->
    <script src="js/services/office/comService.js"></script>
    <script src="js/services/office/report/reportService.js"></script>
    <script src="js/services/office/customerDemand/customerDataService.js"></script>
    <script src="js/services/office/projectScheduleDeviation/scheduleDeviationService.js"></script>
    <script src="js/services/office/projectScheduleDeviation/projectManagementService.js"></script>
    <script src="js/services/office/report/reviewProblem/reviewProblemService.js"></script>
    <script src="js/services/office/code/codeConfigService.js"></script>
    <script src="js/services/office/quartzScheduled/cronGen.min.js"></script>
    <script src="js/services/office/cost/costMonitoring/costMonitoringService.js"></script>
    <script src="js/services/office/lowQualityProblem/lowQualityService.js"></script>

    <!-- 测试报表（产品维度） -->
    <script src="js/controllers/office/testReportProduct/testReportProductController.js"></script>
    <script src="js/controllers/office/testReportProduct/testRepotProductConfig.js"></script>
    <script src="js/services/office/testReportProduct/testReportProductService.js"></script>
    <!--获取访问量-->
    <script src="js/services/office/visits/visitsService.js"></script>
    <!--用户需求-->
    <script src="js/services/office/workingHoursStatistics/customerStoryService.js"></script>
    <script src="js/services/office/common/proProjectModule.js"></script>
    <script src="js/services/office/common/hardwareModeModule.js"></script>
    <script src="js/services/office/common/customerModule.js"></script>
    <script src="js/services/office/common/productTypeNameModule.js"></script>
    <script src="js/services/office/common/plmModule.js"></script>
    <script src="js/services/office/common/companyProjectModule.js"></script>
    <script src="js/services/office/common/costChartModule.js"></script>
    <!--客户信息维护-->
    <script src="js/services/office/workingHoursStatistics/productData/customerManagementService.js"></script>
    <!--硬件型号维护-->
    <script src="js/services/office/workingHoursStatistics/productData/hardwareModeManagementService.js"></script>
    <!--公司立项项目维护-->
    <script src="js/services/office/workingHoursStatistics/productData/companyProjectManagementService.js"></script>
    <!--同行评审配置-->
    <script src="js/controllers/office/report/reviewProblem/reviewProblemConfig.js"></script>
    <script src="library/component/zui.min.js"></script>
    <script src="library/component/zui-calender/zui.calendar.min.js"></script>

    <!--我的徽章配置-->
    <script src="js/controllers/office/personalDataBoard/personalBadgeConfig.js"></script>
    <script src="js/services/office/personalDataBoard/personalBadgeService.js"></script>
</body>

</html>
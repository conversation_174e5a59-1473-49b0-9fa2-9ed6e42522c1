(function () {
    app.controller("jenkinsJobUpController", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','jenkinsJobService','reportService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope,comService,$scope,$state,$stateParams, $modal,jenkinsJobService,reportService,inform,Trans,AgreeConstant,LocalCache,$http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */

            $scope.limitList = AgreeConstant.limitList; // 正则校验配置


            //jenkins作业信息表id
            $scope.id = $stateParams.id;
            //项目id
            $scope.projectId = Number.parseInt($stateParams.projectId);
            //初始化信息
            $scope.paramInfo = {
                productLineName: '',
                projectManager: '',
                projectAssistant: ''
            };
            //开发语言下拉框数据
            $scope.programLanguageList = [{
                value: 'java',
                label: 'java'
            },{
                value: 'C#',
                label: 'C#'
            },{
                value: 'C++',
                label: 'C++'
            },{
                value: 'Android',
                label: 'Android'
            },{
                value: 'javaScript',
                label: 'javaScript'
            }]
            //是否下拉框数据源
            $scope.points = [{
                value: '是',
                label: '是'
            }, {
                value: '否',
                label: '否'
            }];
            /*
            * 项目名称发生改变
            * */
            $scope.setModelCategorys = setModelCategorys;

            //初始化
            initProject();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 初始化
             */
            function initProject() {
                getOne();
                //获取所有项目名称
                $scope.projectList = [];
                comService.getProjectsName().then(function (data) {
                    $scope.project = angular.fromJson(data.data);
                    angular.forEach($scope.project, function (res) {
                        $scope.projectList.push(res);
                    })
                });
            }

            // 项目名称发生改变
            function setModelCategorys(projectId){
                $scope.paramInfo.productLineName = '';
                $scope.paramInfo.projectManager = '';
                $scope.paramInfo.projectAssistant = '';
                if(!projectId) {
                    return;
                }
                reportService.getProjectInfoById(projectId).then(function (data) {
                    if (data.code !== AgreeConstant.code) {
                        inform.common(data.message);
                        return;
                    }
                    data.data = angular.fromJson(data.data);
                    if (data.data != null) {
                        $scope.paramInfo.productLineName = data.data.productLineName;
                        $scope.paramInfo.projectManager = data.data.projectManager;
                        $scope.paramInfo.projectAssistant = data.data.projectAssistant;
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });

            }
            /**
             * 回填信息
             */
            function getOne() {
                var urlData = {
                    'id': $scope.id,//Jenkins作业信息表中id
                };
                jenkinsJobService.selectOne(urlData).then(function (data) {
                        $scope.paramInfo = data.data;
                        setModelCategorys($scope.projectId);
                        $scope.paramInfo.projectId = $scope.projectId;
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    })
            }
            /**
             * 保存信息
             */
            $scope.saveInfo = function(){
                if (!$scope.paramInfo.projectId || 0 === $scope.paramInfo.projectId){
                    inform.common("请选择所属项目");
                    return;
                }
                var urlData = {
                    'projectId':$scope.paramInfo.projectId,//
                    'isSonarCheck':$scope.paramInfo.isSonarCheck,//
                    'isTestPackage':$scope.paramInfo.isTestPackage,//
                    'programLanguage':$scope.paramInfo.programLanguage,//
                    'isUnitTest':$scope.paramInfo.isUnitTest,//
                    'isAutoDeployment':$scope.paramInfo.isAutoDeployment,//
                    'id': $scope.id
                };
                //修改
                upInfo(urlData)
            }
            /**
             * 修改信息
             * @param urlData
             */
            function upInfo(urlData) {
                jenkinsJobService.updateInfo(urlData).then(function (data) {
                    callBackFunction(data);
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            function callBackFunction(data){
                if (data.code === AgreeConstant.code) {
                    layer.confirm(data.message,{
                        title:false,
                        btn:['确定']
                    },function(result){
                        layer.close(result);
                        $state.go("app.office.jenkinsJob");
                    });
                } else {
                    inform.common(data.message);
                }

            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
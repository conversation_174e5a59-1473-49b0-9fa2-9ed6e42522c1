(function () {
    'use strict';
    app.factory('measureHisService', measureHisService);
    measureHisService.$inject = ["HttpService", '$rootScope'];

    function measureHisService(HttpService, $rootScope) {
        var service = {
            selectData:selectData,
            measureExecute: measureExecute
        };
        return service;
        /**
         * 获取度量元数据
         */
        function selectData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'measureHis/selectMeasureData', urlData);
        }
        /*
        * 时间段采集度量元
        * */
        function measureExecute(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'measureHis/measureExecute',urlData);
        }
    }
})();
/*
 * @Author: dongyinggang
 * @Date:   2019-06-13 17:50:05
 * @Last Modified by:   dongyinggang
 * @Last Modified time:  2019-06-13 17:50:05
 */
(function () {
    'use strict';
    app.factory('testTaskService', testTaskService);
    testTaskService.$inject = ["HttpService", '$rootScope'];

    function testTaskService(HttpService, $rootScope) {

        var service = {

            getTestTaskInfo: getTestTaskInfo,
            updateTestTaskInfo: updateTestTaskInfo,
            getNonstandardInfo: getNonstandardInfo,
            saveSpecialType:saveSpecialType,
            getSpecailtypeAndAllProjectList:getSpecailtypeAndAllProjectList

        };
        return service;


        /**
         * 分页查询提测单
         */
        function getTestTaskInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'testTask/getTestTaskInfo', urlData);
        }

        /**
         * 同步提测单
         */
        function updateTestTaskInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'testTask/updateTestTaskInfo', urlData);
        }
        
        /**
         * 查询不规范信息
         */
        function getNonstandardInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'testTask/getNonstandardInfo', urlData);
        }

        /**
         * 保存特殊类型项目信息
         */
        function saveSpecialType(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'testTask/saveSpecialType', urlData);
        }

        /**
         * 查询允许被作为特殊项目的项目信息
         */
        function getSpecailtypeAndAllProjectList(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'testTask/getSpecailtypeAndAllProjectList', urlData);
        }

    }
})();
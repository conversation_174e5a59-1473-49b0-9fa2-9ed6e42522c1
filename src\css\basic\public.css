/*解决导航条 左侧显示问题*/
/*#nav>.nav-sub .nav-sub{
    height: auto !important;
    margin-left: 0;
    overflow: auto;
    opacity: 1;
}*/
input[type=number] {
    -moz-appearance: textfield;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
}

.date-plug[disabled],
.date-plug[readonly],
.bootstrap-filestyle .form-control[disabled],
.bootstrap-filestyle .form-control[readonly] {
    background-color: #fff;
    cursor: pointer;
}

/*折叠左侧菜单 三级菜单显示问题*/
#nav {
    font-size: 12px;
}

#nav .navi {
    background-color: #42485B;
}

#nav>.nav-sub .nav-sub {
    display: none;
}

/*折叠左侧菜单 三级菜单显示问题*/
#nav>.nav-sub .two-flag:hover .nav-sub {
    display: block;
}

/*折叠左侧菜单 三级菜单显示问题*/
#nav>.nav-sub .two-flag .nav-sub li a {
    padding-left: 55px !important;
}

/*解决导航条 上侧三级显示问题*/
.app-aside-dock .app-aside .navi>ul>li .nav-sub .nav-sub {
    position: relative;
}

.app-aside-dock .app-aside .navi>ul>li .nav-sub .nav-sub,
.app-aside-folded .app-aside .navi>ul>li .nav-sub .nav-sub {
    display: none;
}

.app-aside-dock .app-aside .navi>ul>li .nav-sub .two-flag:hover .nav-sub,
.app-aside-folded .app-aside .navi>ul>li .nav-sub .two-flag:hover .nav-sub {
    display: block;
}

.navi ul.nav.nav-sub.dk li.two-flag ul li a {
    padding-left: 70px;
}

/* 展开上侧菜单，三级菜单显示位置*/
.app-aside-dock .app-aside .navi>ul>li .nav-sub .nav-sub a {
    padding-left: 50px !important;
}

/*  折叠上侧菜单，三级菜单显示位置 */
.app-aside-folded .app-aside .navi>ul>li .nav-sub .nav-sub a {
    padding-left: 54px !important;
}

@media (max-width: 991px) {
    .navi ul.nav.nav-sub.dk li.two-flag ul li a {
        padding-left: 85px !important;
    }
}

.app-content-body,
.app-content-body .btn {
    font-size: 12px;
}

/*表格*/
.table {
    margin-bottom: 0px;
    border: 1px solid #e1e6eb;
}

.table.dataTable>thead {
    background-color: #f5f6fa;
}

.table>thead>tr>th {
    padding: 8px 8px;
    font-weight: normal;
    color: #999;
    border-bottom: 1px solid #E1E6EB;
    background-color: #F5F6FA;
    vertical-align: middle;
}

.table>tbody>tr>td.text-right {
    border-top: 1px solid #eaeff0;
}

.table-striped>tbody>tr:nth-child(odd) {
    background-color: #f9fafc;
}

.table-hover>tbody>tr:hover {
    background-color: #f7f7f7;
}

table.dataTable thead .sorting,
table.dataTable thead .sorting_asc,
table.dataTable thead .sorting_desc {
    cursor: pointer;
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:after {
    font-family: FontAwesome;
    color: #ccc;
    position: relative;
    font-weight: normal;
    left: 8px;
}

table.dataTable thead .sorting:after {
    content: "\f0dc";
}

table.dataTable thead .sorting_asc:after {
    content: "\f0de";
}

table.dataTable thead .sorting_desc:after {
    content: "\f0dd";
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:after {
    color: #888;
}

.btn:hover,
.btn:focus,
.btn.focus {
    text-decoration: none;
}

.alert {
    margin-bottom: 0px;
}

.breadcrumb {
    margin-bottom: 0px;
    background-color: #f5f6fa;
    border-radius: 0;
    border-color: #edf1f2;
}

.pagination>.active>a,
.pagination>.active>span,
.pagination>.active>a:hover,
.pagination>.active>span:hover,
.pagination>.active>a:focus,
.pagination>.active>span:focus {
    background-color: #09c;
    border-color: #09c;
}

.col-sm-2 {
    padding-right: 0px;
}

@media (min-width: 768px) {
    .modal-dialog {
        margin: 100px auto;
    }
}

.table-responsive {
    width: 100%;
}

/*导入 导出 新增 按钮*/
.buttonModule {
    display: inline-block;
    float: right;
    margin-right: 24px;
}

.nav .summary {
    display: inline-block;
    line-height: 40px;
    border: 1px solid transparent;
    padding: 0 20px;
}

.buttonModule a {
    display: block;
    line-height: 32px;
    padding: 0 6px;
    float: left;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin: 2px 4px 4px;
}

.buttonModule a.none {
    display: none;
}

.buttonModule a:hover {
    color: #58666e !important;
    background-color: #edf1f2;
    border-color: #c7d3d6;
}

.buttonModule a i {
    display: inline-block;
    width: 22px;
    margin: 0;
    font-size: 12px;
    text-align: center;
    vertical-align: middle;

}

.button-hover i,
td a i {
    -webkit-transition: all 0.2s;
    -o-transition: all 0.2s;
    transition: all 0.2s;
}

.button-hover:hover i,
td a:hover i {
    text-decoration: none;
    -moz-transform: scale(1.4);
    -webkit-transform: scale(1.4);
    -o-transform: scale(1.4);
    -ms-transform: scale(1.4);
    transform: scale(1.4);
    color: #000;
}

/*导航条*/
.nav-tabs>li>a {
    border-top: 2px solid transparent;
}

.search .w-xs {
    width: 110px;
}

/*分页*/
.pagination {
    margin: 0;
}

.wrapper-b-15 {
    padding: 15px 15px 0;
}

.wrapper-b-5 {
    padding:  5px 0 0;
}

.wrapper-bb-5 {
    padding:  5px 5px 0;
}

/*颜色*/
.styleGreen {
    color: green;
}

.styleRed {
    color: red;
}

.green,
.green * {
    color: #69aa46 !important;
}

.red,
.red * {
    color: #dd5a43 !important;
}

.orange {
    color: #ff892a !important;
}

.grey {
    color: #777777 !important;
}

/*表格*/
td a {
    color: #09c;
}

td a i {
    color: #777;
    font-size: 16px !important;
    margin: 0 2px;
}

/*搜索框*/
.form-control {
    height: 28px;
    border-radius: 0px;
    padding: 1px;
    -webkit-transition: none;
    transition: none;
    font-size: 12px;
}

.panel.search {
    margin: 15px 15px 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    border-bottom: 1px solid #ddd;
}

.panel.search.table-opt {
    border-bottom: none;
}

.panel.search .title {
    padding-left: 8px;
    border-left: 2px solid #09c;
}

.panel.search label {
    line-height: 34px;
}

.panel.search label.w-xxs30 {
    text-align: center;
}

.hbox select.col {
    width: 100%;
    border: 1px solid #ccc;
}

.search-list a>i,
.search-list a>span>i {
    color: #09c;
    padding: 0 5px 0 15px;
}

.search-list a:hover>span {
    color: #09c;
}

.unfirst-row {
    margin-top: 15px;
}

.w-c-850 {
    width: 850px;
}

/*弹窗*/
.modal {
    z-index: 1051;
    background-color: rgba(0, 0, 0, 0.5);
}

/*解决火狐返回遮罩层未关闭问题*/
.modal-backdrop {
    display: none !important;
}

.modal-footer {
    padding: 5px 0 15px;
    text-align: center;
    border-top: 0;
}

.modal-content {
    border-radius: 5px;
    overflow: hidden;
}

.modal-header .close {
    margin-top: 0px;
    color: #fff;
    opacity: 0.8;
}

.modal-footer .btn+.btn {
    margin-left: 15px;
}

/*时间搜索框*/
.form-control.time {
    width: 100%;
    display: inline-block;
}

/*下侧所有保存按钮*/
.save-footer {
    text-align: center;
}

.save-footer .btn+.btn {
    margin-left: 15px;
}

.form-vertical {
    color: #f05050;
    position: absolute;
    left: 24px;
    bottom: -26px;
    white-space: nowrap;
}

.form-unit {
    padding-right: 40px;
}

.form-unit label {
    position: absolute;
    top: 6px;
    right: 30px;
}

.form-unit span {
    right: 70px;
}

@media (min-width: 768px) {
    .sm-22 {
        width: 22%;
    }

    .form-group div.col {
        padding-left: 15px;
    }

    .form-horizontal .control-label {
        padding-top: 0px;
    }

    .modal-body .hbox.w50 {
        float: left;
        width: 50%;
    }

    .modal-body .hbox.w50 {
        float: left;
        width: 50%;
    }

    .modal-body .hbox.w33 {
        float: left;
        width: 33.333%;
    }

    .modal-body .col {
        padding-left: 4px !important;
    }
}

.search>div>div {
    padding: 0;
}

.font-white {
    color: #fff !important;
}

.select {
    width: 70% !important;
}

/*公用样式*/
.textDetai {
    border: 1px solid #ccc;
    width: 306px;
    height: 30px;
    line-height: 30px;
    margin-left: 12px;
}

.w-style {
    width: 75%;
    border-color: #ddd;
    height: 34px;
}

.required-icon {
    position: absolute;
    right: -8px;
    top: 0;
    line-height: 32px;
    color: red;
}

/*服务管理开始*/
.resource .col-md-4 {
    padding: 0;
}

.resource .col-md-4:nth-of-type(2) {
    border-right: 1px solid #e1e6eb;
    border-left: 1px solid #e1e6eb;
}

.resource-title {
    background-color: #f9f9f9;
    height: 36px;
    line-height: 36px;
    padding-left: 14px;
}

.service-title .title {
    padding-left: 8px;
    text-align: left;
    line-height: 1.4;
    -border-left: 2px solid #88B7E0;
    border-left: 2px solid #09c;
}

.break {
    white-space: normal;
    word-break: break-all;
}

.bg-black>pre {
    background-color: #373d41;
    color: #f1bb5a;
}

.my-api {
    padding: 5px 15px;
    margin: 10px;
    margin-bottom: 0;
    border: 1px solid #bce8f1;
}

.top-zero {
    margin-top: 0;
    border-top: none;
    margin-bottom: 10px;
    background-color: #d9edf7;
}

.api-item a {
    color: #31708f;
}

.api-item a:hover {
    text-decoration: underline;
}

.my-api .blue-title {
    color: #0f6ab4;
}

.padder-b-sm {
    padding-bottom: 10px;
}

.mytable {
    table-layout: fixed;
    /* 只有定义了表格的布局算法为fixed，下面td的定义才能起作用。 */
}

.mytable td {
    word-break: keep-all;
    /* 不换行 */
    white-space: nowrap;
    /* 不换行 */
    overflow: hidden;
    /* 内容超出宽度时隐藏超出部分的内容 */
    text-overflow: ellipsis;
    /* 当对象内文本溢出时显示省略标记(...) ；需与overflow:hidden;一 起使用。*/
}

/*表格上侧的操作开始*/
.dropdown-btn {
    position: relative;
}

.setting-item.dropdown-menu {
    right: 0;
    left: inherit;
    display: block;
    padding: 0;
    margin: 0;
    font-size: 12px;
}

.setting-item .is-selete {
    margin-bottom: 10px;
}

.setting-item .is-selete:last-child {
    margin-bottom: 0px;
}

.bottomline {
    border-bottom: 1px solid #eaeff0;
}

/*表格上侧的操作结束*/

/* 进度条文字颜色*/
.precent-color {
    color: #666;
}

/*树*/
#aside {
    height: 100%;
}

#aside .pack-up {
    position: absolute;
    top: 50%;
    right: -1px;
    width: 15px;
    height: 32px;
    background-color: #fff;
}

.bg-tree {
    background-color: #d9dee4;
    padding: 10px;
    padding-bottom: 9px;
}

/*日期插件*/
.input-date-btn {
    position: absolute;
    top: 0;
    right: 0;
    line-height: 1;
}

.search-date-btn {
    position: absolute;
    top: 0;
    right: 15px;
    line-height: 1;
}

.input-date-btn>.btn,
.search-date-btn>.btn {
    width: 32px;
    line-height: 30px;
    padding: 0;
}

/*header样式开始*/
.nav .open>a,
.nav .open>a:hover,
.nav .open>a:focus {
    background-color: #eee;
    border-color: #428bca;
}

.navbar-nav>li>a {
    padding-left: 16px;
    padding-right: 16px;
}

.bg-black .navbar-nav>li>a:hover,
.bg-black .navbar-nav>li>a:focus,
.bg-black .navbar-nav>a:hover,
.bg-black .navbar-nav>a:focus {
    background-color: #2a2f32;
    color: #fff;
    border-color: #2a2f32;
}

.bg-dark .navbar-nav>li>a:hover,
.bg-dark .navbar-nav>li>a:focus,
.bg-dark .navbar-nav>a:hover,
.bg-dark .navbar-nav>a:focus {
    color: #fff;
    background-color: #000;
    border-color: #000;
}

.bg-primary .navbar-nav>li>a:hover,
.bg-primary .navbar-nav>li>a:focus,
.bg-primary .navbar-nav>a:hover,
.bg-primary .navbar-nav>a:focus {
    color: #fff;
    background-color: #6658b8;
    border-color: #6658b8;
}

.bg-info .navbar-nav>li>a:hover,
.bg-info .navbar-nav>li>a:focus,
.bg-info .navbar-nav>a:hover,
.bg-info .navbar-nav>a:focus {
    color: #fff;
    background-color: #17b2e2;
    border-color: #17b2e2;
}

.bg-success .navbar-nav>li>a:hover,
.bg-success .navbar-nav>li>a:focus,
.bg-success .navbar-nav>a:hover,
.bg-success .navbar-nav>a:focus {
    color: #fff;
    background-color: #038478;
    border-color: #038478;
}

.bg-danger .navbar-nav>li>a:hover,
.bg-danger .navbar-nav>li>a:focus,
.bg-danger .navbar-nav>a:hover,
.bg-danger .navbar-nav>a:focus {
    color: #fff;
    background-color: #ebaa01;
    border-color: #ebaa01;
}

.bg-white-only .navbar-nav>li>a:hover,
.bg-white-only .navbar-nav>li>a:focus,
.bg-white-only .navbar-nav>a:hover,
.bg-white-only .navbar-nav>a:focus {
    color: #363f44;
    background-color: #eee;
    border-color: #eee;
}

.bg-black .nav>li>a,
.bg-black .nav>a {
    border-right: 1px solid #2a2f32;
}

.bg-dark .nav>li>a,
.bg-dark .nav>a {
    border-right: 1px solid #000;
}

.bg-primary .nav>li>a,
.bg-primary .nav>a {
    border-right: 1px solid #6658b8;
}

.bg-info .nav>li>a,
.bg-info .nav>a {
    border-right: 1px solid #17b2e2;
}

.bg-success .nav>li>a,
.bg-success .nav>a {
    border-right: 1px solid #038478;
}

.bg-danger .nav>li>a,
.bg-danger .nav>a {
    border-right: 1px solid #ebaa01;
}

.bg-white-only .nav>li>a,
.bg-white-only .nav>a {
    border-right: 1px solid #eee;
}

.navbar-nav>.btn {
    margin: 0;
    padding: 14px 16px;
}

/*header样式结束*/


/*菜单样式开始*/
.bg-black .nav .nav>li>a:hover,
.bg-black .nav .nav>li>a:focus,
.bg-black .nav .nav>.two-flag>a:hover,
.bg-dark .nav .nav>li>a:hover,
.bg-dark .nav .nav>li>a:focus,
.bg-dark .nav .nav>.two-flag>a:hover {
    background-color: #4a5064;
}

.bg-black .nav .nav>.two-flag>a:focus,
.bg-dark .nav .nav>.two-flag>a:focus {
    background-color: #00c1de;
}

.bg-black .nav .nav>li:not(.two-flag).active>a,
.bg-dark .nav .nav>li:not(.two-flag).active>a {
    background-color: #00c1de;
}

/*菜单样式结束*/


/*功能导航开始*/
.dropdown-menu {
    color: #333 !important;
}

.bg-dropdown {
    background-color: #fff;
}

.bg-dropdown a,
.bg-dropdown .text-muted {
    color: #333 !important;
}

.list-unstyled li {
    background-color: #f7f8fa;
    padding-left: 10px;
}

.list-unstyled .bgc-unstyled {
    background-color: #FFF;
    margin-bottom: 0;
    padding: 0;
    margin-left: 10px;
}

.list-unstyled li:hover {
    background-color: #00c1de;
}

.list-unstyled .bgc-unstyled:hover {
    background-color: #FFF;
}

.list-unstyled li:hover a,
.list-unstyled li:hover .text-muted {
    color: #fff !important;
}

/*功能导航结束*/

/*页码跳转到开始*/
.goToPage {
    float: right;
    padding-left: 15px;
}

.goToPage>input {
    display: inline-block;
    width: 80px;
    height: 29px;
    margin-right: 10px;
}

.goToPage>button {
    color: #333;
    padding: 5px 8px;
    border: 1px solid #ddd;
    background-color: #f7f7f7;
    vertical-align: top;
}

.goToPage>button:hover {
    background-color: #fff;
}

/*页码跳转到结束*/

/*删除弹框图标*/
.tip-icon {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 30px;
    height: 30px;
    background: url("../../library/component/skin/default/icon.png") no-repeat;
    background-position: 0 0;
}


/*右侧弹出框样式开始*/
.modal-right {
    position: absolute;
    top: -1px;
    right: -1px;
    width: 400px;
    height: 100%;
    margin: 0;
}

.modal-right .modal-content {
    border-radius: 0;
    height: 100%;
}

.modal-right .modal-body {
    height: 88%;
    padding-bottom: 12%;
}

.modal-right .modal-footer {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 400px;
    margin-left: -200px;
    padding-top: 15px;
    border-top: 1px solid #e5e5e5;
    background-color: #fff;
}

.modal.fade .modal-dialog.modal-right {
    -webkit-transition: -webkit-transform .3s ease-out;
    -o-transition: -o-transform .3s ease-out;
    transition: transform .3s ease-out;
    -webkit-transform: translate(125%, 0);
    -ms-transform: translate(125%, 0);
    -o-transform: translate(125%, 0);
    transform: translate(125%, 0);
}

.modal.in .modal-dialog.modal-right {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
}

/*右侧弹出框样式结束*/

/*树结构样式开始*/
#tree-root {
    color: #363f44 !important;
}

.angular-ui-tree-handle>i {
    height: 24px;
    width: 24px;
    line-height: 24px;
    text-align: center;
}

.angular-ui-tree-handle:hover {
    color: #438eb9;
    background: #f4f6f7;
    border-color: #dce2e8;
}

.angular-ui-tree-placeholder {
    width: 100%;
    background: #f0f9ff;
    border: 2px dashed #bed2db;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

tr.angular-ui-tree-empty {
    height: 100px
}

.nodrop {
    background-color: #f2dede;
}

.tree-node-content .tree-icon {
    padding: 1px 5px;
    padding-right: 0;
    display: inline-block;
    width: 12px;
    height: 12px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px;
}

.tree-node-content {
    margin: 10px;
}

.tree-handle {
    padding: 10px;
    background: #428bca;
    color: #FFF;
    margin-right: 10px;
}

.angular-ui-tree-handle {
    font-weight: normal !important;
}

.angular-ui-tree-nodes .angular-ui-tree-nodes {
    padding-left: 21px !important;
}

.angular-ui-tree-node,
.angular-ui-tree-placeholder {
    white-space: nowrap;
}

.group-title {
    background-color: #687074 !important;
    color: #FFF !important;
}

/*树结构样式结束*/
.move-body {
    padding-bottom: 15px;
}

.move-body .move-item {
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 8px;
    border-bottom: none;
}

.move-body .form-group {
    margin-left: 0;
    margin-right: 0;
}


/*模板工作台配置样式*/
.search-bot {
    padding-bottom: 15px;
}

.work-terrace {
    margin: 15px 0 0 20px;
}

.work-terrace h5 {
    color: #000;
    text-align: center;
    margin-bottom: 15px;
}

.add-box {
    height: 160px;
    vertical-align: middle;
    text-align: center;
    margin: 0 auto;
    margin-top: 35px;
}

.add-box a {
    height: 160px;
    display: inline-block;
    color: #09c;
    position: relative;
}

.add-box a:hover {
    color: #00c1de;
}

.add-box a i {
    display: inline-block;
    width: 120px;
    height: 120px;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -60px;
    margin-left: -60px;
    font-size: 120px;
}

.bootstrap-filestyle .group-span-filestyle>label {
    line-height: 18px;
}

.modal-body {
    overflow: inherit;
}

.echarts-height {
    height: 250px;
}

.drag-handle {
    cursor: move;
    float: left;
    width: 90%;
}

.wrapper-xs:hover {
    border-bottom: 1px solid #ddd;
}

.wrapper-xs:hover .none {
    display: block;
}

.plot-lg {
    width: 90%;
}

/*多选框样式*/
.chosen-container-multi .chosen-choices {
    position: relative;
    overflow: auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    width: 100%;
    height: auto !important;
    height: 1%;
    border: 1px solid #cfdadd;
    /* background-color: #fff; */
    background-image: none;
    background-image: none;
    background-image: none;
    background-image: none;
    background-image: none;
    cursor: text;
}

.chosen-container-active .chosen-choices {
    border: 1px solid #00c1de;
    box-shadow: none;
}

/*首页样式*/
.carousel-bottom {
    color: #FFF;
    height: 230px;
    background-color: rgba(0, 0, 0, 0.55);
    overflow-x: auto;
    overflow-y: hidden;
    z-index: 9999;
}

.index-box {
    position: relative;
    text-align: center;
}

.index-box:before {
    content: "";
    width: 0;
    height: 100%;
    background: #000;
    padding: 14px 18px;
    position: absolute;
    top: 0;
    left: 50%;
    opacity: 0;
}

.index-box:hover:before {
    width: 100%;
    left: 0;
    opacity: 0.5;
}

.index-box .index-box-content {
    width: 100%;
    color: #fff;
    position: absolute;
    top: 10%;
}

.index-box-content a {
    width: 52px;
    height: 52px;
    opacity: 0;
    color: #fff;
    background-color: #09c;
    display: inline-block;
    padding: 8px 6px;
    margin: 10px;
    border-radius: 50%;
}

.index-box-content a>i {
    margin-bottom: 4px;
    display: block;
}

.index-add {
    padding-top: 25px;
    height: 185px;
}

.index-box:hover .index-box-content a {
    opacity: 1;
}

.index-box img {
    width: 100%;
    height: 160px;
}

.carousel-control {
    width: 6%;
}

.carousel-inner {
    width: 88%;
    margin: 0 6%;
}

.carousel-indicators {
    display: none;
}

.index-setting {
    padding-bottom: 10px;
    cursor: pointer;
}

/*详情页面样式*/
.media-body {
    color: #363f44;
    padding-left: 8px;
    border-left: 2px solid #09c;
    word-break: break-all;
}

/*角色选择页面样式*/
.form-horizontal .check-item {
    margin-right: -15px;
    margin-bottom: 15px;
}

.label-img {
    position: absolute;
    top: 0;
    right: 0;
    height: 32px;
}

.label-img>.glyphicon-folder-open {
    top: -6px;
}

.input-uploader {
    position: absolute;
    opacity: 0;
}

/*anxia*/
.nav-tabs.border {
    border: 1px solid #ddd;
}

.nav-tabs.border>li {
    margin-left: 0;
    border-top: none;
    border-left: none;
    border-right: none;
    z-index: 0;
}

.border>li>a {
    background: #fff;
}

.border>li.active>a,
.border>li.active>a:hover,
.border>li:hover>a {
    border-color: #dee5e7;
}

.border>li.active>a {
    border-top: 2px solid #1199c4 !important;
}

.border>li.active>a,
.border>li.active>a:focus,
.border>li.active>a:hover {
    color: #555;
    cursor: default;
    background-color: #fff;
    border: 1px solid #ddd;
    border-bottom-color: transparent;
}

.drapdown-list {
    height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
}

.breadcrumb a {
    cursor: default;
    color:#777;
}

.ztree li span.demoIcon{padding:0 2px 0 10px;}
.ztree li span.button.icon01{margin:0;
background: url(../../img/basic/3.png) no-repeat scroll 0 0 transparent;
vertical-align:top;
*vertical-align:middle}
.ztree li span.button.iconCopy{margin:0;
    background: url(../../img/basic/copy.png) no-repeat scroll 0 0 transparent;
    vertical-align:top;
    *vertical-align:middle}
/*确认/返回按钮样式*/
.mask-btn {
    border-radius: 5px;
    height: 28px;
    align-self: center;
    margin-left: 22px;
    margin-right: 22px;
    width: 66px;
    line-height: 28px;
}
.mask-btn:hover{
    background-color: rgba(50,50,50,.50);
}
.wcp-iframe {
    top: -10px;
    position: absolute;
}
.changeBtn {
    display: inline;
    width: 50px;
    height: 20px;
    padding: 5px 10px ;
    border-radius: 3px;
    background-color: #16a8f8;
    color: ghostwhite;
}
.changeBtn:hover{
    background-color: #16c8f8;
    cursor: pointer;
}

//# sourceURL=js/controllers/office/incongruent/incongruentDetailController.js
(function () {
    app.controller("incongruentDetailManagement", ['comService', '$rootScope', '$stateParams', '$scope', 'incongruentService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
        function (comService, $rootScope, $stateParams, $scope, incongruentService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
             // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //获取项目信息
            $scope.loaclParam = LocalCache.getObject('project_detail');
            if($scope.loaclParam.projectInfoParam){
                $scope.projectInfoParam = JSON.parse($scope.loaclParam.projectInfoParam);
            }
            $scope.type = 'detail';
            //添加问题内容
            $scope.formInsertin = {};

            //页面查询条件
            $scope.formRefer = {};


            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //分页
            $scope.pages = inform.initPages(); // 初始化分页数据

            initPages();//初始化页面信息
            $scope.getData = getData;
            getData("");
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
            }

            /**
             * 页面初始化
             */
            function initPages() {
                //获取问题所属过程域
                $scope.productDomainList = [];
                comService.getParamList('PROCESS_DOMAIN', 'PROCESS_DOMAIN').then(function (data) {
                    if (data.data) {
                        $scope.productDomainList = data.data;
                    }
                });
                //获取开发模型
                $scope.productTypeList = [];
                comService.getParamList('INCONGRUENT_TYPE', 'INCONGRUENT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.productTypeList = data.data;
                    }
                });
                //获取项目严重程度
                $scope.productSeverityList = [];
                comService.getParamList('SEVERITY', 'SEVERITY').then(function (data) {
                    if (data.data) {
                        $scope.productSeverityList = data.data;
                    }
                });
                //获取不符合问题状态
                $scope.productStateList = [];
                comService.getParamList('STATE', 'PROBLEM_STATE').then(function (data) {
                    if (data.data) {
                        $scope.productStateList = data.data;
                    }
                });
            }


            /**
             * 重置
             */
            $scope.reset = function () {
                $scope.formRefer = {};
            };

            /**
             * 查询开始时间
             */
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;
                $scope.openedEnd = false;
                $scope.findTime1 = false;
                $scope.plansolveTime1 = false;
                $scope.realsolveTime1 = false;
                $scope.findTime2 = false;
                $scope.plansolveTime2 = false;
                $scope.realsolveTime2 = false;
            };

            /**
             * 查询结束时间
             */
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
                $scope.findTime1 = false;
                $scope.plansolveTime1 = false;
                $scope.realsolveTime1 = false;
                $scope.findTime2 = false;
                $scope.plansolveTime2 = false;
                $scope.realsolveTime2 = false;
            };
            /**
             * 新增发现日期时间按钮
             */
            $scope.addFindTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = false;
                $scope.findTime1 = true;
                $scope.plansolveTime1 = false;
                $scope.realsolveTime1 = false;
                $scope.findTime2 = false;
                $scope.plansolveTime2 = false;
                $scope.realsolveTime2 = false;
            };
            /**
             * 新增计划解决日期时间按钮
             */
            $scope.addPlanSolveTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = false;
                $scope.findTime1 = false;
                $scope.plansolveTime1 = true;
                $scope.realsolveTime1 = false;
                $scope.findTime2 = false;
                $scope.plansolveTime2 = false;
                $scope.realsolveTime2 = false;
            };
            /**
             * 新增实际解决日期时间按钮
             */
            $scope.addRealSolveTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = false;
                $scope.findTime1 = false;
                $scope.plansolveTime1 = false;
                $scope.realsolveTime1 = true;
                $scope.findTime2 = false;
                $scope.plansolveTime2 = false;
                $scope.realsolveTime2 = false;
            };
            /**
             * 修改发现日期时间按钮
             */
            $scope.upFindTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = false;
                $scope.findTime1 = false;
                $scope.plansolveTime1 = false;
                $scope.realsolveTime1 = false;
                $scope.findTime2 = true;
                $scope.plansolveTime2 = false;
                $scope.realsolveTime2 = false;
            };
            /**
             * 修改计划解决日期时间按钮
             */
            $scope.upPlanSolveTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = false;
                $scope.findTime1 = false;
                $scope.plansolveTime1 = false;
                $scope.realsolveTime1 = false;
                $scope.findTime2 = false;
                $scope.plansolveTime2 = true;
                $scope.realsolveTime2 = false;
            };
            /**
             * 修改实际解决日期时间按钮
             */
            $scope.upRealSolveTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = false;
                $scope.findTime1 = false;
                $scope.plansolveTime1 = false;
                $scope.realsolveTime1 = false;
                $scope.findTime2 = false;
                $scope.plansolveTime2 = false;
                $scope.realsolveTime2 = true;
            };

            /**
             * 获取项目下的问题
             */
            function getData(pageNum) {
                var urlData = {
                    'projectId': $stateParams.project,//项目id
                    'state': $scope.formRefer.state,//状态
                    'type': $scope.formRefer.type, //类型
                    'processDomain': $scope.formRefer.processDomain,//问题所属过程域
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'), //发现日期-起始
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //发现日期-结束
                    'currentPage': pageNum,//当前页数
                    'pageSize': $scope.pages.size//每页显示条数
                };
                if($stateParams.project==null){
                    if($scope.projectInfoParam){
                        urlData.projectId = $scope.projectInfoParam.id;
                        urlData.currentPage = 1;
                        urlData.pageSize = 500;
                    }
                }
                incongruentService.getProjectDetailsInformation(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            //项目详情
                            $scope.jsonData = data.data.list;
                            if ($scope.jsonData.length === 0) {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages();
                            } else {
                                //分页信息设置
                                $scope.pages.total = data.data.total;
                                $scope.pages.star = data.data.startRow;
                                $scope.pages.end = data.data.endRow;
                                $scope.pages.pageNum = data.data.pageNum;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                function () {
                    inform.common(Trans("tip.requestError"));
                });
                if($stateParams.project==null){
                    var cname = '';
                    if($scope.projectInfoParam){
                        cname = $scope.projectInfoParam.cname;
                    }
                    //项目看板使用
                    //获取当前项目的总检查数、通过数、过程符合度
                    var projectParams = {
                        'cname': cname,
                        'page': 1,
                        'pageSize': 100,
                    };
                    incongruentService.getProjectInfo(projectParams).then(function (data) {
                            if (data.code === AgreeConstant.code) {
                                //项目报告
                                $scope.projectEntranceIncongruent = data.data.list[0];
                            } else {
                                inform.common(data.message);
                            }
                        },
                        function () {
                            inform.common(Trans("tip.requestError"));
                        });
                }
            }

            /**
             * 添加信息
             */
            $scope.addInfo = function () {
                var urlData = {
                    'project': $stateParams.project,//所属项目
                    'type': $scope.formInsertin.type,//类型
                    'processDomain': $scope.formInsertin.processDomain,//问题所属过程域
                    'subprocedure': $scope.formInsertin.subprocedure,//子过程
                    'prodesc': $scope.formInsertin.prodesc,//问题描述
                    'findTime': inform.format($scope.formInsertin.findTime, 'yyyy-MM-dd'),//发现日期
                    'severity': $scope.formInsertin.severity,//严重程度
                    'state': $scope.formInsertin.state,//状态
                    'solution': $scope.formInsertin.solution,//原因及解决措施
                    'plansolveTime': inform.format($scope.formInsertin.plansolveTime, 'yyyy-MM-dd'),//计划解决日期
                    'chargePerson': $scope.formInsertin.chargePerson,//责任人
                    'realsolveTime': inform.format($scope.formInsertin.realsolveTime, 'yyyy-MM-dd'),//实际解决日期
                    'remark': $scope.formInsertin.remark//备注
                };
                incongruentService.addProjectDetailInfo(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $("#add_modal").modal("hide");
                        $scope.getData();
                        inform.common(data.message);
                        $scope.formInsertin = {};

                    } else {
                        inform.common(data.message);
                    }
                }, function () {
                    inform.common(Trans("tip.requestError"));
                });
            };

            /**
             * 点击修改时信息，对changeParam进行赋值
             */
            $scope.popModal = function (item) {
                $scope.changeParam = angular.copy(item);
                //由于后台返回的列表中存储的是param_value，所以要把该值改为param_code
                //遍历类型
                angular.forEach($scope.productTypeList, function (type) {
                    if (type.param_value === $scope.changeParam.type) {
                        $scope.changeParam.type = type.param_code;
                    }
                });
                //遍历问题所属过程域
                angular.forEach($scope.productDomainList, function (domain) {
                    if (domain.param_value === $scope.changeParam.processDomain) {
                        $scope.changeParam.processDomain = domain.param_code;
                    }
                });
                //遍历严重程度
                angular.forEach($scope.productSeverityList, function (severity) {
                    if (severity.param_value === $scope.changeParam.severity) {
                        $scope.changeParam.severity = severity.param_code;
                    }
                });
                //遍历状态
                angular.forEach($scope.productStateList, function (state) {
                    if (state.param_value === $scope.changeParam.state) {
                        $scope.changeParam.state = state.param_code;
                    }
                });
            };
            /**
             * 修改信息
             */
            $scope.updateInfo = function () {
                var urlData = {
                    'id': $scope.changeParam.id,//问题ID
                    'project': $stateParams.project,//所属项目
                    'type': $scope.changeParam.type,//类型
                    'processDomain': $scope.changeParam.processDomain,//问题所属过程域
                    'subprocedure': $scope.changeParam.subprocedure,//子过程
                    'prodesc': $scope.changeParam.prodesc,//问题描述
                    'findTime': inform.format($scope.changeParam.findTime, 'yyyy-MM-dd'),//发现日期
                    'severity': $scope.changeParam.severity,//严重程度
                    'state': $scope.changeParam.state,//状态
                    'solution': $scope.changeParam.solution,//原因及解决措施
                    'plansolveTime': inform.format($scope.changeParam.plansolveTime, 'yyyy-MM-dd'),//计划解决日期
                    'chargePerson': $scope.changeParam.chargePerson,//责任人
                    'realsolveTime': inform.format($scope.changeParam.realsolveTime, 'yyyy-MM-dd'),//实际解决日期
                    'remark': $scope.changeParam.remark//备注
                };
                incongruentService.updateProjectDetailInfo(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        inform.common(data.message);
                        $scope.getData();
                        $("#edit_Module").modal("hide");
                    } else {
                        inform.common(data.message);
                    }
                }, function () {
                    inform.common(Trans("tip.requestError"));
                });

            };
            /**
             * 删除弹框
             */
            $scope.open = function (item) {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function () {
                            return Trans("common.deleteTip");
                        }
                    }
                });
                modalInstance.result.then(function () {
                    if (item !== null && item !== "") {
                        $scope.removeInCongruentDetail(item);
                    }
                });
            };
            /**
             * 删除数据 JSON.stringify(removeParam)
             */
            $scope.removeInCongruentDetail = function (item) {
                var id = item.id;
                incongruentService.deleteProjectDetailInfo(id)
                    .then(function (data) {
                        if (data.code === "0000") {
                            inform.common(Trans("tip.delSuccess"));
                            $scope.getData(AgreeConstant.pageNum);
                        } else {
                            inform.common(data.message);
                        }
                    }, function () {
                        inform.common(Trans("tip.requestError"));
                    });
            };
            /**
             * 跳转至项目信息管理
             */
            $scope.goBack = function () {
                $state.go('app.office.incongruentManagement', {
                    cname: $stateParams.cname,
                    productLine: $stateParams.productLine,
                    type: $stateParams.type,
                    qualityEngineer: $stateParams.qualityEngineer,
                    department: $stateParams.department,
                    twoDepartment: $stateParams.twoDepartment,
                    projectStatus:$stateParams.projectStatus,
                    enter:'back'
                });
            };

            /**
             * 查询框time校验
             */
            $scope.timeJudge = function () {
                var beginTime = new Date($scope.formRefer.startTime);
                var lastTime = new Date($scope.formRefer.endTime);
                if (beginTime > lastTime) {
                    $scope.formRefer.endTime = "";
                    inform.common("开始时间必须小于结束时间！");

                }
            };

            /**
             * 获取下载信息
             */
            $scope.toExcel = function (){
                var urlData = {
                    'projectid': $stateParams.project,//项目名称
                    'state': $scope.formRefer.state,//状态
                    'type': $scope.formRefer.type, //类型
                    'processDomain': $scope.formRefer.processDomain,//问题所属过程域
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'), //发现日期-起始
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd') //发现日期-结束
                };
                inform.modalInstance("确定要下载吗?").result.then(function () {
                    inform.downLoadFile('incongruent/toExcelForProblem',urlData,'不符合项问题数据.xlsx');
                });
            };
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */

        }]);
})();
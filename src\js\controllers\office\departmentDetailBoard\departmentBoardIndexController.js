(function () {
    app.controller("departmentBoardIndexController", ['LocalCache', '$rootScope','comService', '$scope', '$stateParams', '$state', 'AgreeConstant', 'inform', 'Trans', 'deptBoardFactory', 'departmentBoardIndexService',
        function (LocalCache, $rootScope,comService,$scope, $stateParams, $state, AgreeConstant, inform, Trans, deptBoardFactory, departmentBoardIndexService) {
            // 初始化
            deptBoardFactory.init($scope, '1');
            // 重置部分
            $scope.resetParam = resetParam;
            function resetParam(){
                deptBoardFactory.initTime($scope, '本年度');
            }
            // 渐变区域
            function getGradientAreaData () {
                $scope.showGradientAreaData = false;
                var currentUrlData = {
                    "department": $scope.formRefer.orgCode
                }
                departmentBoardIndexService.getTotalSumInfo(currentUrlData).then(function (result) {
                    if (result.code === AgreeConstant.code) {
                        $scope.gradientAreaData = result.data;
                        $scope.showGradientAreaData = true;
                    } else {
                        inform.common(result.message);
                        $scope.showGradientAreaData = true;
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    $scope.showGradientAreaData = true;
                });
            }
            // 统计区域
            function getStatisticsData() {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                $scope.showStatisticsInfo = false;
                departmentBoardIndexService.getRateInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.statisticsList = result.data;
                        $scope.showStatisticsInfo = true;
                    } else {
                        inform.common(result.message);
                        $scope.showStatisticsInfo = true;
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    $scope.showStatisticsInfo = true;
                });
            }
            // top5标签上面数字
            function getTop5Number() {
                var currentUrlData = {
                    "department": $scope.formRefer.orgCode,
                }
                departmentBoardIndexService.getDeptPLMCount(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.top5 = result.data;
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            // top5部分
            $scope.changeType = function changeType(type) {
                $scope.type = type
                if (type === '1') {
                    // 获取延期未完成数据
                    getDelayNoFinishData('延期未完成');
                } else if (type === '2') {
                    // 获取临期数据
                    getTemporaryData('临期');
                } else if (type === '3') {
                    // 获取未到期数据
                    getUnexpiredData('未到期');
                } else if (type === '4') {
                    // 获取尚未制定计划数据
                    getNoPlanYetData('尚未制定计划');
                }
            }
            function getDelayNoFinishData(finishState){
                var currentUrlData = {
                    "department": $scope.formRefer.orgCode,
                    "finishState": finishState,
                }
                departmentBoardIndexService.getDeptPLMInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.delayNoFinishData = result.data;
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            function getTemporaryData(finishState){
                var currentUrlData = {
                    "department": $scope.formRefer.orgCode,
                    "finishState": finishState,
                }
                departmentBoardIndexService.getDeptPLMInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.temporaryData = result.data;
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            function getUnexpiredData(finishState){
                var currentUrlData = {
                    "department": $scope.formRefer.orgCode,
                    "finishState": finishState,
                }
                departmentBoardIndexService.getDeptPLMInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.unexpiredData = result.data;
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            function getNoPlanYetData(finishState){
                var currentUrlData = {
                    "department": $scope.formRefer.orgCode,
                    "finishState": finishState,
                }
                departmentBoardIndexService.getDeptPLMInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.noPlanYetData = result.data;
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            // 项目进度
            function getProjectScheduleData() {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                departmentBoardIndexService.getProjectInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.projcetScheduleList = result.data;
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            function getTeamScheduleData() {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime + '-31',
                    "startTime": $scope.formRefer.startTime + '-01',
                    "department": $scope.formRefer.orgCode,
                }
                departmentBoardIndexService.getTeamSprintInfo(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.teamScheduleList = result.data;
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            var colorList = AgreeConstant.departmentBoard.departmentFlagColorList;
            $scope.colorList = colorList;
            $scope.checkFlag = function (value) {
                value = parseFloat(value);
                if (value >= 95) {
                    return colorList[0];
                } else if (value >= 90 && value < 95) {
                    return colorList[1];
                } else if (value < 90){
                    return colorList[2];
                }
            }
            $scope.checkCost = function (value) {
                value = Math.abs(parseFloat(value));
                if (value > 10) {
                    return colorList[2];
                } else if (value > 5 && value <= 10) {
                    return colorList[1];
                } else if (value <= 5) {
                    return  colorList[0];
                }
            }
            $scope.checkWorking = function (value) {
                value = parseFloat(value);
                if (value >= 110) {
                    return colorList[0];
                } else if (value >=105 && value < 110) {
                    return colorList[1];
                } else if (value < 105) {
                    return colorList[2];
                }
            }
            $scope.checkSprint = function (value) {
                if (value === '-') {
                    return colorList[0];
                }
                value = parseFloat(value);
                if (value === 100) {
                    return colorList[0];
                } else if (value >= 85 && value < 100) {
                    return colorList[1];
                } else if (value < 85) {
                    return colorList[2];
                }
            }
            $scope.checkTags = function (status) {
                status = parseInt(status);
                if (status === 2 || status === 1) {
                    return colorList[1];
                }
                if (status === 3) {
                    return colorList[2];
                }
            }

            $scope.getData = getData;
            function getData() {
                getStatisticsData();
                // 获取项目进度数据
                getProjectScheduleData();
                // 获取团队进度数据
                getTeamScheduleData();
            }
            // 页面加载后触发
            $scope.$watch('$viewContentLoaded', function () {
                var localFormRefer = LocalCache.getObject('departmentList_formRefer');
                if (Object.keys(localFormRefer).length > 0) {
                    $scope.formRefer = localFormRefer;
                    $scope.butFlag = localFormRefer.searchTimeString;
                }
                if ($stateParams.orgCode) {
                    $scope.formRefer.orgCode = $stateParams.orgCode;
                }
                getGradientAreaData();
                getData();
                getTop5Number();
                // 获取top5的数据
                $scope.changeType($scope.type);
            });
        }]);
})();
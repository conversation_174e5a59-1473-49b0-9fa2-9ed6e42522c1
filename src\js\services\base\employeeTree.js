/*
* @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
* @Date:   2019-01-08 10:05:00
* @Last Modified by:   fubaole
* @Last Modified time: 2018-02-24 13:45:13
*/
(function() {
    'use strict';
  app.factory('employeeTree', employeeTree);
  employeeTree.$inject=["comService","AgreeConstant","inform","Trans"];

  function employeeTree(comService,AgreeConstant,inform,Trans){
    var service={
		 initTree:initTree,
		 initTreeExt:initTreeExt
    };

    return service;

    /**
     * 节点单击处理事件。 加载给节点下的子节点数据
     * 显示部门下的员工信息
     * 
     */
    function nodeClickEvent(event,treeId,treeNode) {

    	//该节点的是否是父节点
    	if (treeNode.isParent) {
    		//改节点已加载 无需再加载
    		if (treeNode.searchedFlag && treeNode.searchedFlag===1) {
    			return;
    		}
    		
    		//获取该部门下级部门信息
    		comService.getOrgChildren(treeNode.orgCode)
                .then(function(data) {
                    if (data.code===AgreeConstant.code) {
                
                    	var nodeS = [];
                    	//查询无结果直接退出
                    	if (!data.data || data.data.length <= 0) {
                    		return;
                    	}
                      
                        angular.forEach(data.data, function(res, index) {
                            var jsonTree = {
                                id: res.orgId,//部门ID
                                pId: null,//上级部门号
                                name: res.orgName,//部门名称
                                orgCode: res.orgCode,//部门编号
                                nocheck:true,//节点前不显示checkBox
                                isParent:true,//设置为父节点
                                open: false
                            };
                            nodeS.push(angular.extend(jsonTree, res));
                        });                       
                        
                        nodeS = inform.unique(nodeS);

                        var treeObj = $.fn.zTree.getZTreeObj("employeeRightTree");
                        treeObj.addNodes(treeNode,nodeS,true);

                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
    		
    		//获取该部门下所有的员工信息
    		comService.getEmployeesByOrgId(treeNode.id)
                .then(function(data) {
                    if (data.code===AgreeConstant.code) {
                
                    	var nodeS = [];
                    	data.data = angular.fromJson(data.data);
                    	//查询无结果直接退出
                    	if (!data.data || data.data.length <= 0) {
                    		return;
                    	}
                        angular.forEach(data.data, function(res, index) {
                            var jsonTreeNode = {
                                id: res.employeeNo,
                                pId: treeNode.id,
                                name: res.realName,
                                nocheck:false
                            };
                            nodeS.push(angular.extend(jsonTreeNode, res));
                        });
                        nodeS = inform.unique(nodeS);

                        //该节点已加载
                        treeNode.searchedFlag = 1;
                        var treeObj = $.fn.zTree.getZTreeObj("employeeRightTree");
                        treeObj.addNodes(treeNode,nodeS,false);
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
    	}
    }
    /**
     * 初始化部门结构树
     * 
     */
    function initTree(callCheck) {
 
    	var treeData = [];
    	var setting = { // 带有复选框树配置
			   data: { simpleData: { enable: true } },
			   callback: { onCheck: "onCheck",
				   onClick:nodeClickEvent,
				   onExpand:nodeClickEvent//用于捕获节点被展开的事件回调函数
			   },
			   check: {
			     enable: true,
			     autoCheckTrigger: true,
			     chkStyle: "checkbox"
			   }
		 };
    	//覆盖onCheck
    	setting.callback.onCheck=callCheck;
    	//获取山东新北洋集团子部门
    	comService.getOrgChildren('0002').then(function(data) {
        	
            if (data.code===AgreeConstant.code) {
                angular.forEach(data.data, function(res, index) {
                    var jsonTree = {
                        id: res.orgId,//部门ID
                        pId: null,//上级部门号
                        name: res.orgName,//部门名称
                        orgCode: res.orgCode,//部门编号
                        nocheck:true,//节点前不显示checkBox
                        isParent:true,//设置为父节点
                        open: false
                    };
                    treeData.push(angular.extend(jsonTree, res));
                });
                treeData = inform.unique(treeData);
                $.fn.zTree.init($("#employeeRightTree"), setting, treeData);
            } else {
                inform.common(data.message);
            }
        }, function() {
            inform.common(Trans("tip.requestError"));
        });

    }
/**
 * **************************************
 * 功能扩展 支持关键字查询 并执行一次性选中多个员工  开始
 * **************************************
 */
    //计算器 0表示zTree加载完毕
    var counter = 0;

    var timeoutObj = null;
    /**
     * 初始化部门结构树 支持关键字查询
     * 
     */
    function initTreeExt(callCheck) {
 
    	var treeData = [];
    	var setting = { // 带有复选框树配置
			   data: { simpleData: { enable: true } },
			   callback: { onCheck: "onCheck"
			   },
			   check: {
			     enable: true,
			     autoCheckTrigger: true,
			     chkStyle: "checkbox"
			   }
		 };
    	//覆盖onCheck
    	setting.callback.onCheck=callCheck;
    	//获取山东新北洋集团子部门
    	comService.getOrgChildren('0002').then(function(data) {
        	
            if (data.code===AgreeConstant.code) {
                angular.forEach(data.data, function(res, index) {
                    var jsonTree = {
                        id: res.orgId,//部门ID
                        pId: null,//上级部门号
                        name: res.orgName,//部门名称
                        orgCode: res.orgCode,//部门编号
                        nocheck:true,//节点前不显示checkBox
                        isParent:true,//设置为父节点
                        open: false
                    };
                    treeData.push(angular.extend(jsonTree, res));
                });
                treeData = inform.unique(treeData);
                $.fn.zTree.init($("#employeeRightTreeExt"), setting, treeData);
                var treeObj = $.fn.zTree.getZTreeObj("employeeRightTreeExt");
                //循环加载各个节点的子节点
                angular.forEach(treeObj.getNodes(), function(node, index) {
                	loadChildrenNodes(node);
                });
                funLazy(); //初始化模糊搜索方法
            } else {
                inform.common(data.message);
            }
        }, function() {
            inform.common(Trans("tip.requestError"));
        });

    }
    
    /**
     * 待ZTree加载完毕后再执行fuzzySearch
     */
	function funLazy() {
		
		if (timeoutObj) { 
			//clear pending task
			clearTimeout(timeoutObj);
		}
		if (counter > 0) {
			
			timeoutObj = setTimeout(function() {
				funLazy(); //lazy load ztreeFilter function 
			}, 500);
		}
		
		timeoutObj = setTimeout(function() {
			fuzzySearch('employeeRightTreeExt','#SearchKey'); //lazy load ztreeFilter function 
		}, 1000);
		
	}   
    /**
     * 通过递归方式一次性加载所有子节点数据
     * 显示部门下的员工信息
     * 
     */
    function loadChildrenNodes(treeNode) {

    	//该节点的是否是父节点
    	if (treeNode.isParent) {
    		counter++;
    		//获取该部门下级部门信息
    		comService.getOrgChildren(treeNode.orgCode)
                .then(function(data) {
                	counter--;
                    if (data.code===AgreeConstant.code) {
                
                    	var nodeS = [];
                    	//查询无结果直接退出
                    	if (!data.data || data.data.length <= 0) {
                    		return;
                    	}
                      
                        angular.forEach(data.data, function(res, index) {
                            var jsonTree = {
                                id: res.orgId,//部门ID
                                pId: null,//上级部门号
                                name: res.orgName,//部门名称
                                orgCode: res.orgCode,//部门编号
                                nocheck:true,//节点前不显示checkBox
                                isParent:true,//设置为父节点
                                open: false
                            };
                            nodeS.push(angular.extend(jsonTree, res));
                        });                       
                        
                        nodeS = inform.unique(nodeS);

                        var treeObj = $.fn.zTree.getZTreeObj("employeeRightTreeExt");
                        treeObj.addNodes(treeNode,nodeS,true);
                        //循环加载各个节点的子节点
                        angular.forEach(treeNode.children, function(node, index) {
                        	loadChildrenNodes(node);
                        });
                        

                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                	counter--;
                    inform.common(Trans("tip.requestError"));
                });
    		counter++;
    		//获取该部门下所有的员工信息
    		comService.getEmployeesByOrgId(treeNode.id)
                .then(function(data) {
                	counter--;
                    if (data.code===AgreeConstant.code) {
                
                    	var nodeS = [];
                    	data.data = angular.fromJson(data.data);
                    	//查询无结果直接退出
                    	if (!data.data || data.data.length <= 0) {
                    		return;
                    	}
                        angular.forEach(data.data, function(res, index) {
                            var jsonTreeNode = {
                                id: res.employeeNo,
                                pId: treeNode.id,
                                name: res.realName,
                                nocheck:false
                            };
                            nodeS.push(angular.extend(jsonTreeNode, res));
                        });
                        nodeS = inform.unique(nodeS);

                        //该节点已加载
                        treeNode.searchedFlag = 1;
                        var treeObj = $.fn.zTree.getZTreeObj("employeeRightTreeExt");
                        treeObj.addNodes(treeNode,nodeS,true);
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                	counter--;
                    inform.common(Trans("tip.requestError"));
                });
    	}
    }
/**
 * ***************************************
 * 功能扩展 支持关键字查询 并执行一次性选中多个员工   结束
 * ***************************************
 */  
  }
})();
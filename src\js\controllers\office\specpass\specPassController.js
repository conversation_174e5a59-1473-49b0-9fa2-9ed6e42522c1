(function () {
    app.controller("specPassManagement", ['$rootScope', '$scope', '$modal', 'specPassService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache','comService', '$http',
        function ($rootScope, $scope, $modal, specPassService, inform, Trans, AgreeConstant, LocalCache,comService, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.getData = getData; 			// 分页相关函数
            $scope.taskList = [];				// 保存所有信息的集合
            $scope.depList = []; 				// 保存部门下信息

            $scope.startTime = '';//下载工时时间
            $scope.endTime = '';
            initTime();
            $scope.datepicker = {};
            $scope.toggleMin = toggleMin;
            toggleMin();

            getData();		//在刷新页面时调用该方法
           
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
    		 * 初始化检索条件开始时间 及 结束时间
    		 */
    		function initTime(endDate){
    			if (endDate==null || endDate==="" ){
    				$scope.endTime = inform.format(new Date(),'yyyy-MM-dd');//获取当前系统时间
    			} 
    			var time = $scope.endTime.split("-");
    			var start = time[0]+"/01"+"/01";
    			$scope.startTime = inform.format(start,'yyyy-MM-dd');
    			//对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
    			//获取产品线
                $scope.productLineList = [];
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.productLineList = data.data;
                    }
                });
                //获取部门
                $scope.departmentList = [];
              	comService.getOrgChildren('D010053').then(function(data) {
          			$scope.departmentList = data.data;
                });
    		}
            //获取当前选定时间
            function toggleMin() {
                $scope.datepicker.currentDate = $scope.datepicker.currentDate ? null : new Date();
            }
            //开始时间
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;    //开始时间
                $scope.openedEnd = false;
            };

            //结束时间
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;    //结束时间
            };

            //重置
            $scope.rest = function () {
                $scope.startTime = '';
                $scope.endTime = '';
                initTime();
            };


            //获取产品线数据
            function getData() {
                $scope.taskList = {};   //保存所有信息的集合
                var start = inform.format($scope.startTime, 'yyyy-MM-dd');
                var end = inform.format($scope.endTime, 'yyyy-MM-dd');
                var urlData = {
                    'startTime': start,
                    'endTime': end
                };
                specPassService.getLineInfo(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.taskList = data.data;
                        angular.forEach($scope.productLineList, function(one, i) {
                            var list = []; 
                            angular.forEach($scope.taskList, function(oneLine, i) {
                            	list.push(oneLine.line);
                            });
                            //如果查询出的产品线中不存在的产品线
                          	if (list.indexOf(one.param_value)===-1){
                          		$scope.taskList.push({
                          			"line":one.param_value,//产品线
                          			"totalNum":0,//总发布数
                          			"passNum":0,//特殊放行数
                          			"spec":'//',//特殊放行率
                          		});
                          	}
                      	 });
                         var totalNumSum = 0*1;
                         var passNumSum = 0*1;
                         angular.forEach($scope.taskList, function(oneLine, i) {
                            totalNumSum += oneLine.totalNum*1;
                            passNumSum += oneLine.passNum*1;
                         });
                         $scope.taskList.push({
                            "line":'汇总',//产品线
                            "totalNum":totalNumSum,
                            "passNum":passNumSum,
                            "spec":totalNumSum===0?'//':((passNumSum/totalNumSum)*100).toFixed(0),
                         });
                         getDep();
                    }
               },
               function (error) {
            	   inform.common(Trans("tip.requestError"));
               });
            }
            
            //获取部门数据
            function getDep() {
                $scope.depList = {};   //保存所有信息的集合
                var start = inform.format($scope.startTime, 'yyyy-MM-dd');
                var end = inform.format($scope.endTime, 'yyyy-MM-dd');
                var urlData = {
                    'startTime': start,
                    'endTime': end
                };
                specPassService.getDepInfo(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                        	$scope.depList = data.data;
                        	angular.forEach($scope.depList,function(res,i){
                        		res.specialPassRate = (res.specialPassRate * 100).toFixed(0);
        					})
        					angular.forEach($scope.departmentList, function(one, i) {
                                 var list = []; 
                                 angular.forEach($scope.depList, function(oneDep, i) {
                                 	list.push(oneDep.orgNAME);
                                 });
                                 //如果查询出的部门中不存在的部门
                               	if (list.indexOf(one.orgName)===-1  && one.orgCode !== 'D010133' && one.orgCode !== 'D010131'){
                               		$scope.depList.push({
                               			"orgNAME":one.orgName,//部门
                               			"releaseTotal":0,//总发布数
                               			"specialPassTotal":0,//特殊放行数
                               			"specialPassRate":'//',//特殊放行率
                               		});
                               	}
                           	 });
                              var totalNumSum = 0*1;
                              var passNumSum = 0*1;
                              angular.forEach($scope.depList, function(oneDep, i) {
                                 totalNumSum += oneDep.releaseTotal*1;
                                 passNumSum += oneDep.specialPassTotal*1;
                              });
                              $scope.depList.push({
                                 "orgNAME":'汇总',//产品线
                                 "releaseTotal":totalNumSum,
                                 "specialPassTotal":passNumSum,
                                 "specialPassRate":totalNumSum===0?'//':((passNumSum/totalNumSum)*100).toFixed(0),
                              });
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }


            //生成Excel表格
            $scope.toExcel = function () {
                var start = inform.format($scope.startTime, 'yyyy-MM-dd');
                var end = inform.format($scope.endTime, 'yyyy-MM-dd');
                var urlData = {
                    'startTime': start,
                    'endTime': end
                };
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function () {
                            return "确定要下载吗！";
                        }
                    }
                });
                modalInstance.result.then(function () {
                    //开启遮罩层
                    inform.showLayer("下载中。。。。。。");
                    $http.post(
                        $rootScope.getWaySystemApi + 'specpass/toExcel',
                        urlData,
                        {
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': 'Bearer ' + LocalCache.getSession("token") || ''
                            },
                            responseType: 'arraybuffer'//防止中文乱码
                        }
                    ).success(function (data) {
                        //如果是IE浏览器
                        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                            var csvData = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
                            window.navigator.msSaveOrOpenBlob(csvData, '05 特殊放行情况.xlsx');
                        }
                        //google或者火狐浏览器
                        else {
                            var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
                            var objectUrl = URL.createObjectURL(blob);
                            var aForExcel = $("<a download='05 特殊放行情况.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href", objectUrl);
                            $("body").append(aForExcel);
                            $(".forExcel").click();
                            aForExcel.remove();
                        }

                        // 关闭遮罩层
                        inform.closeLayer();
                        inform.common("下载成功!");
                    });


                });

            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
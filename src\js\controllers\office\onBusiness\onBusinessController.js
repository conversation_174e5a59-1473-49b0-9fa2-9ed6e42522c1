(function() {
    app.controller("onBusinessController", ['onBusinessService', '$state', 'comService', '$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$stateParams', 'codeConfigService', '$modal', '$http', '$state',
        function(onBusinessService,state, comService, $rootScope, $scope, inform, Trans, AgreeConstant, LocalCache, $stateParams, codeConfigService, $modal, $http, $state) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
             //获取缓存
            $scope.formRefer = LocalCache.getObject('onBusinessController_searchObject');
            //清除缓存
            LocalCache.setObject('onBusinessController_searchObject', {});
            //页面分页信息
            $scope.pages = {
                pageNum: '', //分页页数
                size: '100', //分页每页大小
                total: '' //数据总数
            };
            initPages();
            //初始化排序
            $scope.orderFlag = false;
            $scope.goOffTimeOrderFlag = false;
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //获取数据
            $scope.getData = getData;
            //绑定文件控件改变事件
            $("#filesImg1").change(submitForm);
            $("#filesImg1").change(fileChangeEvent);

            var paramObj = {
                primaryDeptId:'#primaryDeptName',
            	primaryDeptScopeModel:'primaryDeptCode',
            	primaryDeptList:'primaryDeptList',
            	subDeptList:'departmentSelect',
            	subDeptScopeModel:'departmentCode'
            };
            //权限控制
            comService.checkAuthentication($scope,paramObj,departmentCallBack,LocalCache.getSession('loginName'));
            

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 页面初始化
             */
            function initPages() {
                //初始化根据用户名获取一级部门列表
                initPrimaryDeptList();

                //获取员工信息
                $scope.employeesList = [];
                comService.getEmployeesByOrgId('').then(function(data) {
                    $scope.employeeList = data.data;
                    //list转map
                    $scope.employeeMap = {};
                    for (var index in $scope.employeeList) {
                        $scope.employeeMap[$scope.employeeList[index].employeeId] = $scope.employeeList[index].realName;
                    }
                });
                //获取项目信息
                $scope.projectIdList = [];
                codeConfigService.getProjectList().then(function(data) {
                    $scope.projectIdList = data.data;
                    $scope.projectMap = {};
                    for (var index in $scope.projectIdList) {
                        $scope.projectMap[$scope.projectIdList[index].id] = $scope.projectIdList[index].cname;
                    }
                });
                //获取出差目的
                $scope.purposeList = [];
                comService.getParamList('ONBUSINESS', 'ONBUSINESS').then(function(data) {
                    $scope.purposeList = data.data;
                    $scope.purposeMap = {};
                    for (var index in $scope.purposeList) {
                        $scope.purposeMap[$scope.purposeList[index].param_code] = $scope.purposeList[index].param_value;
                    }
                });
                //获取出差方式
                $scope.onBusinessModeList = [];
                comService.getParamList('ONBUSINESS_MODE', 'ONBUSINESS_MODE').then(function(data) {
                    $scope.onBusinessModeList = data.data;
                    $scope.onBusinessModeMap = {};
                    for (var index in $scope.onBusinessModeList) {
                        $scope.onBusinessModeMap[$scope.onBusinessModeList[index].param_code] = $scope.onBusinessModeList[index].param_value;
                    }
                });

                //获取地区
                getAreaList();

            }

             /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function(data) {
                     if (data.data) {
                         $scope.primaryDeptList = data.data;
                     }
                });
            }


            //修改一级部门，二级部门进行联动
            $scope.changeDept = function(){
                //获取二级部门
                $scope.departmentSelect = [];
                comService.getOrgChildren($scope.primaryDeptCode).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.departmentSelect = data.data;
                    }
                });
            };

             /**
             *部门控件的回调处理
             **/
            function departmentCallBack(result){
                 if(result.code === '00'){
                     $state.go('app.office.unAuthority');
                     return;
                 }
                 if(result.code === '01' || result.code === '03'){
                    //01全部权限
                    $scope.flag = true;
                 }
                 if(result.code === '02'){
                    //部门权限控制
                     $scope.flag = false;
                 }
                getData(1);
            }

            //获取地区
            function getAreaList(){
                $scope.areaList = [];
                comService.getParamList('AREA_TYPE','AREA_TYPE').then(function(data) {
                    $scope.areaList = data.data;
                });
            }


            /**
             * 获取出差信息
             *
             * @param {[String]}  [页码]
             */
            function getData(pageNum) {
                //默认根据出差天数倒序排列
                if (null ==$scope.orderby) {
                    $scope.orderby = "onbusiness.set_time desc";
                    $scope.orderFlag = false;
                }
                var urlData = {
                    'primaryDept':$scope.primaryDeptCode,
                    'department': $scope.departmentCode, //部门code
                    'employeeName': $scope.formRefer.name, //出差人员名称
                    'purpose': $scope.formRefer.purpose, //出差目的
                    'area': $scope.formRefer.area, //归属地
                    'orderByParam': $scope.orderby,
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),
                    'page': pageNum,
                    'pageSize': $scope.pages.size
                };
                onBusinessService.getOnBusinessInfo(urlData).then(function(data) {
                    if (data.code === '0000') {
                        //出差信息报告
                        $scope.onBusinessData = data.data.list;
                        if ($scope.onBusinessData.length === 0) {
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages(); //初始化分页数据
                        }
                        // 分页信息设置
                        $scope.pages.total = data.data.total; // 页面数据总数
                        $scope.pages.star = data.data.startRow; // 页面起始数
                        $scope.pages.end = data.data.endRow; // 页面结束数
                        $scope.pages.pageNum = data.data.pageNum;  
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 235);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
            }

            /**
             * 界面切换（新增)
             */
            $scope.openAddModal = function() {
                LocalCache.setObject('onBusinessController_searchObject', $scope.formRefer);
                $state.go('app.office.onBusinessAdd');
            };
            /**
             * 界面切换（修改)
             *
             */
            $scope.popModal = function(item) {
                LocalCache.setObject('onBusinessController_searchObject', $scope.formRefer);
                $state.go('app.office.onBusinessUpdate', {
                    id: item.id
                });
            };
            /**
             * 删除数据 
             * 
             * @param {[map]} [item] [存有当前行信息的对象]
             */
            $scope.remove = function(item) {
                onBusinessService.deleteOnBusinessInfo(item).then(function(data) {
                    if (data.code === "0000") {
                        inform.common(Trans("tip.delSuccess"));
                        $scope.getData(AgreeConstant.pageNum);
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            };
            /**
             * 删除弹框
             *
             *  @param {[map]} [item] [存有当前行信息的对象]
             */
            $scope.open = function(item) {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function() {
                            return Trans("common.deleteTip");
                        }
                    }
                });
                modalInstance.result.then(function() {
                    if (null != item && item !== "") {
                        $scope.remove(item);
                    }
                });
            };
            /**
             * excel下载
             */
            $scope.toExcel = function() {
                inform.modalInstance("确定要下载吗?").result.then(function() {
                    //拼装下载内容
                    var urlData = {
                        'primaryDept':$scope.primaryDeptCode,
                        'department': $scope.departmentCode, //部门code
                        'employeeName': $scope.formRefer.name, //出差人员名称
                        'purpose': $scope.formRefer.purpose, //出差目的
                        'area': $scope.formRefer.area, //归属地
                        'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                        'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd')
                    };
                    inform.downLoadFile('onBusiness/toExcel', urlData, '员工出差信息.xlsx');
                });
            };
            /**
             * 根据出差天数排序
             */
            $scope.order = function(orderby) {
                //根据点击次数判别排序规则
                if (!$scope.orderFlag) {
                    $scope.orderby = orderby + "*1 desc";
                } else {
                    $scope.orderby = orderby + "*1 asc";
                }
                $scope.orderFlag = !$scope.orderFlag;
                getData("1");
            };
            /**
             * 根据出发时间排序
             */
            $scope.goOffTimeOrder = function(orderby) {
                //根据点击次数判别排序规则
                if (!$scope.goOffTimeOrderFlag) {
                    $scope.orderby = orderby + " desc";
                } else {
                    $scope.orderby = orderby + " asc";
                }
                $scope.goOffTimeOrderFlag = !$scope.goOffTimeOrderFlag;
                getData("1");
            };
            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };


            //下载员工出差信息模板
            $scope.toTemplateExcel = function() {
                inform.modalInstance("确定要下载出差信息模板吗？").result.then(function() {
                    var params = {};
                    inform.downLoadFile('onBusiness/downloadTemplateExcel',params,"员工出差信息表模板.xlsx");
                });

            };

            /**
             ** 选择文件
             */
            $scope.selectFile = function() {
                 document.getElementById("filesImg1").click();
            };
             /**
              * 选择上传文件后事件
            */
            function fileChangeEvent(e){
                   var fileName = "文件名称：" + e.currentTarget.files[0].name;
                   $("#fileNameDis").text(fileName);
            }

            //上传文件
            function submitForm(){

                var formData = new FormData(document.getElementById("form"));//表单id  初始化表单值
                var file = document.querySelector('input[type=file]').files[0]; //获取文档中有类型为file的第一个input元素
                if(!file){
                    inform.common("请先选择文件!");
                    return false;
                }
                formData.append('fileName', file);
                var a = file.type;
                if(a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"){
                    $("#form")[0].reset();
                    $("#fileNameDis").text("");
                    inform.common("请选择.xlsx类型的文档进行上传!");
                    return false;
                } else {
                  inform.modalInstance("确定要上传文件吗？").result.then(function() {
                    inform.uploadFile('onBusiness/uploadExcel',formData,function func(result){
                      if (result.code === AgreeConstant.code) {
                           // 关闭遮罩层
                           inform.closeLayer();
                           $modal.open({
                           templateUrl: 'errorModel.html',
                           controller: 'ModalInstanceCtrl',
                           size: "lg",
                           resolve: {
                               items: function () {
                                   return result.message;
                               }
                           }
                           });

                      } else {
                          // 关闭遮罩层
                         inform.closeLayer();
                         inform.common("上传失败！");
                      }
                      getData(1);
                      $("#form")[0].reset();
                      $("#fileNameDis").text("");
                      });

                   });
                }

            }
            
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        }
    ]);
})();
(function() {
	'use strict';
	app.factory('kpiManagemetService', kpiManagemetService);
	kpiManagemetService.$inject = [ "HttpService", '$rootScope' ];

	function kpiManagemetService(HttpService, $rootScope) {
		var service = {
		    getData:getData,
		    saveAppraiser:saveAppraiser,
		    saveKpiWeight:saveKpiWeight,
		    startAssessment:startAssessment,
		    saveAssessment:saveAssessment,
		    cancelAssessment:cancelAssessment,
		    getTeamManagement:getTeamManagement
		};
		return service;

        /**
		 * 获取kpi管理信息
		 */
		function getData(urlData) {
			return HttpService.post($rootScope.getWaySystemApi + 'kpiManagement/getData', urlData);
		}
        /**
		 * 获取团队管理信息
		 */
		function getTeamManagement(urlData) {
			return HttpService.post($rootScope.getWaySystemApi + 'kpiManagement/getTeamManagement', urlData);
		}
		/**
		 * 评价人维护
		 */
		function saveAppraiser(urlData) {
			return HttpService.post($rootScope.getWaySystemApi + 'kpiManagement/saveAppraiser', urlData);
		}
        /**
		 * 保存权重方法
		 */
		function saveKpiWeight(urlData) {
			return HttpService.post($rootScope.getWaySystemApi + 'kpiManagement/saveKpiWeight', urlData);
		}
        /**
		 * 发起考核
		 */
		function startAssessment(urlData) {
			return HttpService.post($rootScope.getWaySystemApi + 'kpiManagement/startAssessment', urlData);
		}
        /**
		 * 考核存档
		 */
		function saveAssessment(urlData) {
			return HttpService.post($rootScope.getWaySystemApi + 'kpiManagement/saveAssessment', urlData);
		}
        /**
		 * 取消考核
		 */
		function cancelAssessment(urlData) {
			return HttpService.post($rootScope.getWaySystemApi + 'kpiManagement/cancelAssessment', urlData);
		}
	}
})();

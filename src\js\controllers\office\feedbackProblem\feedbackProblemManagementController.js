(function () {
  app.controller('feedbackProblemManagementController', [
    '$rootScope',
    'comService',
    '$scope',
    '$state',
    '$stateParams',
    '$modal',
    'feedbackProblemService',
    'inform',
    'Trans',
    'AgreeConstant',
    'LocalCache',
    '$http',
    function (
      $rootScope,
      comService,
      $scope,
      $state,
      $stateParams,
      $modal,
      feedbackProblemService,
      inform,
      Trans,
      AgreeConstant,
      LocalCache,
      $http
    ) {
      /**
       * *************************************************************
       *             初始化部分                                 开始
       * *************************************************************
       */
      $scope.limitList = AgreeConstant.limitList;
      initInfo();
      $scope.getData = getData;
      $scope.setLevel = setLevel;
      if ($stateParams.flag != null) {
        //修改、详情查看
        getData();
      }
      $scope.flag = $stateParams.flag;
      if ($stateParams.flag === 'see') {
        //如果是查看详情，则页面所有控件处于只读状态
        var boxes = document.getElementsByName('feedbackRead');
        for (var i = 0; i < boxes.length; i++) {
          boxes[i].disabled = 'disabled';
        }
      }
      //紧急程度
      $scope.problemUrgencySelect = ['极高', '高', '中', '低'];
      //应急事件等级
      $scope.emergencyLevelSelect = ['严重的', '一般的', '轻微的'];
      //临时解决
      $scope.isTemporarySolutionSelect = ['是', '否', '不需要'];
      //问题来源
      $scope.problemSourceSelect = [
        '客户',
        '市场',
        '测试组',
        '项目组',
        '产品组',
        '售前',
        '售后',
        '运维',
        '荣鑫保障',
        '下游项目组',
        '其他',
      ];
      //严重程度
      $scope.problemSeveritySelect = ['事件', '轻微线上问题', '一般线上问题', '严重线上问题'];
      //是否应急处置
      $scope.isEmergencyResponseSelect = ['是', '否'];
      //反馈类别
      $scope.feedbackTypeSelect = ['支持事项', '新需求', '线上bug', '运维停机'];
      //状态
      $scope.approvalStatusSelect = ['未开始', '进行中', '临时解决', '已完成', '已暂停', '已关闭', '不解决'];
      //是否停机
      $scope.isServerDown = ['是', '否'];
      var flag = 0;

      /**
       * *************************************************************
       *              初始化部分                                 结束
       * *************************************************************
       */

      /**
       * *************************************************************
       *              方法声明部分                                 开始
       * *************************************************************
       */

      /**
       * 初始化
       */
      function initInfo() {
        //获取山东新北洋集团的下级部门信息
        $scope.departmentList = [];
        $scope.departmentMap = {};
        comService.getOrgChildren('D010053').then(function (data) {
          $scope.departmentList = comService.getDepartment(data.data);
          for (var j = 0; j < data.data.length; j++) {
            $scope.departmentMap[data.data[j].orgName] = data.data[j].orgCode;
          }
          if ($stateParams.flag != null) {
            flag++;
            getData();
          }
        });
        //获取员工信息
        $scope.employeeList = [];
        comService.getEmployeesByOrgId('').then(function (data) {
          if (data.data) {
            $scope.employeeList = data.data;
            if ($stateParams.flag != null) {
              flag++;
              getData();
            }
          }
        });
      }
      /**
       * 获取部门下项目
       */
      $scope.getProjectList = function () {
        //获取所有项目名称
        $scope.projectList = [];
        $scope.projectMap = {};
        var urlData = {
          department: $scope.departmentMap[$scope.item.department],
        };
        comService.getProjectsNameByParams(urlData).then(function (data) {
          $scope.projectList = angular.fromJson(data.data);
          for (var j = 0; j < data.data.length; j++) {
            $scope.projectMap[data.data[j].id] = data.data[j].cname;
          }
          if ($scope.flag === 'see') {
            $scope.item.officeProjectId = $scope.projectMap[$scope.item.officeProjectId];
          }
          flag++;
          if (flag === 3) {
            getData();
            getFeedbackProblemById();
          }
        });
      };
      /**
       * 获取所有、某个反馈问题
       */
      function getData() {
        if (flag !== 2 && flag !== 3) {
          return;
        }
        var urlData = {
          problemId: $stateParams.item,
        };
        feedbackProblemService.getData(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              //项目详情
              $scope.item = data.data.list[0];
              setLevel();
              if (flag === 2) {
                $scope.getProjectList();
              } else {
                $scope.item.officeProjectId = $scope.item.officeProjectId * 1;
                if ($scope.flag === 'see') {
                  $scope.item.officeProjectId = $scope.projectMap[$scope.item.officeProjectId];
                }
              }
            } else {
              inform.common(data.message);
            }
          },
          function () {
            inform.common(Trans('tip.requestError'));
          }
        );
      }

      function getFeedbackProblemById(){
        var urlData = {
          problemId: $stateParams.item,
        };
        feedbackProblemService.getFeedbackProblemById(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {             
               //影响的应用
               $scope.checkedApplicationData = data.data.affectAppList;
               var iswarning = getIsWarning(data.data.iswarning);
               $scope.item.iswarning = iswarning;
           } else {
               inform.common(data.message);
           }
       }, function () {
           inform.common(Trans("tip.requestError"));
       });    
        
      }

      function getIsWarning(iswarning){
        var iswarningstr = "否";
        if(iswarning == "1"){
          iswarningstr = "是";
        }
        return iswarningstr
      }
      /**
       * 设置反馈等级
       */
      function setLevel() {
        if ($scope.item.feedbackType === '支持事项') {
          $scope.item.problemSeverity = '请选择';
          $scope.levelEdit = true;
        } else {
          $scope.levelEdit = false;
        }
      }
      /**
       * 提交详情信息,存在就更新，不存在就新增
       */
      $scope.manageDetail = function () {
        var urlData = $scope.item;
        urlData.flag = urlData.problemId == null ? 'add' : '';
        //设置问题序号
        var employeeId = LocalCache.getSession('employeeId');
        var date = inform.format(new Date(), 'yyyy-MM-dd');
        var time = date.split('-');
        var id = employeeId + time[0].substring(2) + time[1] + time[2];
        urlData.problemId = urlData.problemId == null ? id : urlData.problemId;
        feedbackProblemService.manageDetail(urlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              inform.common('维护反馈问题信息成功！');
              window.history.go(-1);
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      };

      /**
       * 根据平台项目，给钉钉项目赋值
       */
      $scope.setDingDingProjectName = function () {
        $scope.item.projectName = $scope.projectMap[$scope.item.officeProjectId];
      };
      /**
       * *************************************************************
       *              方法声明部分                                 结束
       * *************************************************************
       */
    },
  ]);
})();

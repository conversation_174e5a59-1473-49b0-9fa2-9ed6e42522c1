.ngGrid {
  background-color: transparent;
}
.ngVerticalBarVisible {
  background-color: #edf1f2;
}
.ngTopPanel {
  position: relative;
  z-index: 1;
  background-color: #fff;
  border-bottom: 1px solid #edf1f2;
}
.ngTopPanel .ngHeaderContainer{
  border-bottom: 1px solid #edf1f2;
}
.ngGroupPanel {
  background-color: #fff;
  border-bottom: 1px solid #edf1f2;
}
.ngGroupName {
  background-color: #fff;
  border: 1px solid #edf1f2;
}
.ngRow {
  position: absolute;
  border-bottom: 1px solid #edf1f2;
}
.ngRow.odd {
  background-color: #fff;
}
.ngRow.even {
  background-color: #fafbfc;
}
.ngRow.selected {
  background-color: #edf1f2;
}
.ngFooterPanel {
  padding: 0 5px;
  background-color: #fff;
  border-top: 1px solid #edf1f2;
}
.ngPagerFirstBar {
  border-left: 2px solid #99a6ad;
}
.ngPagerFirstTriangle {
  border-color: transparent #99a6ad transparent transparent;
}
.ngPagerLastTriangle {
  border-color: transparent transparent transparent #99a6ad;
}
.ngPagerLastBar {
  border-left: 2px solid #99a6ad;
}
.ngPagerButton {
  background-color: #fff;
  border: 1px solid #dee5e7;
}
.ngHeaderText,
.ngCellText{
  padding: 5px 10px;
}
.ngFooterSelectedItems{padding-top: 0; margin-top: -4px}
.ngGridMaxPagesNumber{position: relative; top: -6px}
.ngGrid input, 
.ngGrid select{
  border: 1px solid #cfdadd;
}
.ngGrid input{line-height: 18px}
.ngViewport{overflow-x:hidden;}
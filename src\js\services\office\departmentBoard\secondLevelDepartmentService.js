(function() {
    'use strict';
    app.factory('secondLevelDepartmentService', secondLevelDepartmentService);
    secondLevelDepartmentService.$inject=["HttpService",'$rootScope'];

    function secondLevelDepartmentService(HttpService,$rootScope){

        // 部门项目投入
        function getDeptProjectInputChartData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptWorkingHours/getDeptProjectHoursInfo',urlData);
        }
        // 获取汇总信息
        function getGatherHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptWorkingHours/getGatherHoursInfo',urlData);
        }
        // 待办事项查询
        function getNeedToBeDoneInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptWorkingHours/getNeedToBeDoneInfo',urlData);
        }
        // 项目工时投入情况
        function getProjectHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptWorkingHours/getProjectHoursInfo',urlData);
        }
        // 产品线投入情况
        function getProductLineHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptWorkingHours/getProductLineHoursInfo',urlData);
        }
        // 返工工时投入情况
        function getReworkHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptWorkingHours/getReworkHoursInfo',urlData);
        }
        // 获取技术支持 TOP5情况
        function getTopTecSupportHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyWorkingHours/getTopTecSupportHoursInfo',urlData);
        }
        // 获取部门工作投入情况
        function getHoursInfoInDept(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyWorkingHours/getHoursInfoInDept',urlData);
        }
        // 获取工时类型分布情况
        function getHoursInfoByType(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'xyWorkingHours/getHoursInfoByType',urlData);
        }

        return {
            getDeptProjectInputChartData: getDeptProjectInputChartData,
            getGatherHoursInfo: getGatherHoursInfo,
            getNeedToBeDoneInfo: getNeedToBeDoneInfo,
            getProjectHoursInfo: getProjectHoursInfo,
            getProductLineHoursInfo: getProductLineHoursInfo,
            getReworkHoursInfo: getReworkHoursInfo,
            getTopTecSupportHoursInfo: getTopTecSupportHoursInfo,
            getHoursInfoInDept: getHoursInfoInDept,
            getHoursInfoByType: getHoursInfoByType,
        };
    }
})();
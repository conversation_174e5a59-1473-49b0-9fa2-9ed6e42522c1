
(function() {
	app.controller("customerWorkTimeManagement", ['comService', '$rootScope', '$scope', 'inform', 'customerWorkTimeService','Trans', 'AgreeConstant', '$modal', '$state', '$stateParams','LocalCache',
		function(comService, $rootScope, $scope,inform, customerWorkTimeService, Trans, AgreeConstant, $modal, $state, $stateParams,LocalCache) {
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	//获取缓存
		$scope.formRefer ={};
        $scope.formRefer = LocalCache.getObject('customerWorkTime_formRefer');
        if($scope.formRefer.type!=='detail'){
        	$scope.formRefer.startTime = inform.format(new Date(),"yyyy")+'-01';
            $scope.formRefer.endTime = inform.format(new Date(),"yyyy")+'-12';
        }

        //对原缓存进行覆盖
        LocalCache.setObject("customerWorkTime_formRefer",{});
		 // 正则校验配置
         $scope.limitList = AgreeConstant.limitList;

        //页面分页信息
        $scope.pages = {
            pageNum : '',   //分页页数
            size : '',      //分页每页大小
            total : ''      //数据总数
        };
        //行业
        $scope.professionSelect = ['金融','物流','新零售','新兴','无'];
    	// 初始化分页数据
    	$scope.pages = inform.initPages();

        //设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		//获取数据
		$scope.getData = getData;
		getData();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */

		$scope.resetParam = function(){
            $scope.formRefer.startTime = inform.format(new Date(),"yyyy")+'-01';
            $scope.formRefer.endTime = inform.format(new Date(),"yyyy")+'-12';
            $scope.formRefer.customerName = '';
            $scope.formRefer.profession = '';
		}
		/**
		 * 获取项目
		 */
		function getData(pageNum) {
		    //用于详情页的统计周期显示
		    $scope.formRefer.startMonth = inform.format($scope.formRefer.startTime,'yyyy-MM');
		    $scope.formRefer.endMonth = inform.format($scope.formRefer.endTime,'yyyy-MM');
			var urlData ={
			    'customerName':$scope.formRefer.customerName,//客户名称
			    'profession':$scope.formRefer.profession,//行业
                'startDate':inform.format($scope.formRefer.startTime,'yyyy-MM'),//开始时间
                'endDate':inform.format($scope.formRefer.endTime,'yyyy-MM'),//结束时间
                'page':pageNum,
                'pageSize':$scope.pages.size
			};
			$scope.allPersonWorks = 0;
			customerWorkTimeService.getCustomerWorkTimeList(urlData).then(function(data) {
				if (data.code===AgreeConstant.code) {
                    //客户
                    $scope.customerWorkTimeData = data.data.list;
                    if ($scope.customerWorkTimeData.length===0) {
						$scope.pages = inform.initPages(); 			//初始化分页数据
						inform.common(Trans("tip.noData"));
                    } else {
                    // 分页信息设置
                    	$scope.pages.total = data.data.total;           // 页面数据总数
                    	$scope.pages.star = data.data.startRow;         // 页面起始数
                    	$scope.pages.end = data.data.endRow;            // 页面结束数
                    	$scope.pages.pageNum = data.data.pageNum;       //页号
                        customerWorkTimeService.getAllCustomerWorkTime(urlData).then(function(data) {
                            if (data.code===AgreeConstant.code) {
                                $scope.allPersonWorks = data.data[0].personWorkTime;
                                $scope.allPersonWorksMonth = ($scope.allPersonWorks/21.75).toFixed(1);
                            }
                        });
                    }
				} else {
					inform.common(data.message);
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
		}

		/**
    	 * 设置列表的高度
    	 */
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 205);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 85);
 		}

        //跳转详情页
        $scope.getCustomerWorkTimeDetail = function(m){
            $scope.formRefer.customerDto = m;
            $scope.formRefer.type='detail';
            LocalCache.setObject("customerWorkTime_formRefer",$scope.formRefer);
            $state.go('app.office.customerWorkTimeDetail');
        }
        /**
	     * *************************************************************
	     *              方法声明部分                                结束
	     * *************************************************************
	     */
		} ]);
})();
(function() {
    'use strict';
    app.factory('depContributionService', depContributionService);
    depContributionService.$inject=["HttpService",'$rootScope'];

    function depContributionService(HttpService,$rootScope){

        var service={
            toExcel:toExcel,
            getData:getData,
            upData:upData,
            delData:delData,
        };
        return service;
        /**
         * 将前台表格内容进行下载
         */
        function toExcel(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'contribution/toExcel', urlData);
        }
        /**
         * 获取部门贡献数据
         */
        function getData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'contribution/getData', urlData);
        }

        /**
         * 更新或新增部门贡献
         */
        function upData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'contribution/updateData', urlData);
        }

        /**
         * 删除部门贡献
         */
        function delData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'contribution/deleteData', urlData);
        }
    }
})();
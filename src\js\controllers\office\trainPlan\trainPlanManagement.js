(function () {
    app.controller("trainPlanManagement", ['comService', '$rootScope', '$scope', '$state', '$stateParams', '$modal', 'trainPlanService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, $state, $stateParams, $modal, trainPlanService, inform, Trans, AgreeConstant, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
    		if ($stateParams.type==null){
    			$scope.type = '1';
    		} else {
    			$scope.type = $stateParams.type;
    		}
            //页面信息
            $scope.formRefer = {};
            //是否从我的星星跳转标志
            $scope.formRefer.flag = '0';
            
            //获取缓存
            $scope.formRefer = LocalCache.getObject('trainPlanManagement_formRefer');
            if($scope.formRefer.flag === '1'){
                $scope.formRefer.startTime = $scope.formRefer.startDate;
                $scope.formRefer.endTime = $scope.formRefer.endDate;
                $scope.formRefer.trainer = $scope.formRefer.employeeName;
                $scope.type = '1';
                console.log($scope.formRefer);
            }
            //清除缓存
            LocalCache.setObject('trainPlanManagement_formRefer', {});
            //按钮初始化
            $scope.addTrainInfo = false;
            $scope.upList = false;
            $scope.downLoadList = false;
            $scope.upExamInfo = false;
            $scope.downloadModule = false;
            $scope.downStudentInfo = false;
            $scope.changeProInfo = false;
            $scope.inspectProInfo = false;
            $scope.deleteProInfo = false;
            $scope.inspectStuInfo = false;
            $scope.deleteStuInfo = false;
            //获取按钮权限
            getButtonPermission();

            //详情数据
            $scope.jsonData = {};
            $scope.joinList=[{
            	'code':'0',
            	'value':'出勤'
            },{
            	'code':'1',
            	'value':'请假'
            },{
            	'code':'2',
            	'value':'旷课'
            }];
            //培训级别
            $scope.trainLevel = [{'code':'0','value':'一级部门'},{'code':'1','value':'二级部门'},{'code':'2','value':'项目/小组'}];
            //页面分页信息
            $scope.pages = inform.initPages();
            //绑定文件控件改变事件
            $("#files").change(submitForm);
            $("#files").change(fileChangeEvent);
            //匿名绑定文件控件改变事件
            $("#filesAnonymity").change(submitFormAnonymity);
            $("#filesAnonymity").change(fileChangeEventAnonymity);
            //考试绑定文件控件改变事件
            $("#filesExam").change(submitFormExam);
            $("#filesExam").change(fileChangeEventExam);
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            //初始化页面信息
            initPages();
            $scope.getData=getData;
            $scope.getTwoDepartment = getTwoDepartment;
            if ( null != $scope.formRefer.department && $scope.formRefer.department !== ''){
            	getTwoDepartment();
            }
            getData();

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 220);
                $("#divTBDisDetail").height(divHeight);
                $("#subDivTBDisDetail").height(divHeight - 80);
            }

            //重置查询条件
            $scope.reset = function () {
                $scope.formRefer = {};
                $scope.twoDepartmentList = [];
                $("#form")[0].reset();
	            $("#fileNameDis").text("");
            };

            function initPages() {
                //获取部门
                $scope.departmentList = [];
                comService.getOrgChildren('0002').then(function (data) {
                    if (data.data) {
                        $scope.departmentList = data.data;
                    }
                });
            }
            function getTwoDepartment(){
            	//获取二级部门
                $scope.twoDepartmentList = [];
                comService.getOrgChildren($scope.formRefer.department).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.twoDepartmentList = data.data;
                    }
                });
            }
            /**
             * 获取组织培训管理数据的分页信息
             */
            function getData(indexNum) {
            	var dep =  [];
            	if ( null != $scope.formRefer.department && $scope.formRefer.department !== ''){
            		//如果查询条件仅有一级部门
                	if($scope.formRefer.twoDepartment === '' || null == $scope.formRefer.twoDepartment){
                        comService.getOrgChildren($scope.formRefer.department).then(function (data) {
                            if (data.code === AgreeConstant.code) {
                                //循环一级部门下的二级部门，放入所有code值
                        		for(var i = 0 ;i<data.data.length;i++) {
                        			dep.push(data.data[i].orgCode);
                                }
                        		//放入一级部门本身
                                dep.push($scope.formRefer.department);
                                getInfo(dep,indexNum);
                            }
                        });
                	} else {
                		//如果有二级部门 就仅查询二级部门
                		dep.push($scope.formRefer.twoDepartment);
                		getInfo(dep,indexNum);
                	}
            	} else {
            		getInfo(dep,indexNum);
            	}
            }
            /**
             * 获取信息
             */
            function getInfo(dep,indexNum){
            	var urlData = {
                        'affiliatedGroups': dep,
                        'temName': $scope.formRefer.cname,
                        'trainer': $scope.formRefer.trainer,
                        'participants': $scope.formRefer.person,
                        'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                        'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),
                        'page': indexNum,
                        'size': $scope.pages.size,
                        'level': $scope.formRefer.level,
                        'trainType': $scope.formRefer.trainType
                    };
                    if ($scope.type === '2'){
                    	urlData.join= $scope.formRefer.join;
                		getParticipant(urlData);
                		return;
                	}
                    getAvgScore(urlData);
                    getTrain(urlData);
            }
            /**
             * 获取平均分
             */
            function getAvgScore(urlData){
            	 trainPlanService.selectAvgInfo(urlData).then(function(data){
                     if (data.code === AgreeConstant.code) {
                         if(!data.data){
                            $scope.hour = 0;
                            $scope.avgScore = 0;
                         }else{
                            $scope.hour = data.data.hour;
                            $scope.avgScore = data.data.avgScore;
                         }
                     }else{
                           inform.common(data.message);
                     }
                  });
            }
            /**
             * 获取培训信息
             */
            function getTrain(urlData){
            	 trainPlanService.getInfo(urlData).then(function (data) {
                     if (data.code === AgreeConstant.code) {
                         $scope.jsonData = data.data.list;
                         // 分页信息设置
                         $scope.pages.total = data.data.total;           // 页面数据总数
                         $scope.pages.star = data.data.startRow;         // 页面起始数
                         $scope.pages.end = data.data.endRow;            // 页面结束数
                         $scope.pages.pageNum = data.data.pageNum;
                     }
                 },
                 function (error) {
                     inform.common(Trans("tip.requestError"));
                 });
            }
            /**
             * 获取人员信息
             */
            function getParticipant(urlData){
            	 trainPlanService.getParticipantInfo(urlData).then(function (data) {
                     if (data.code === AgreeConstant.code) {
                         $scope.participantData = data.data.list;
                         // 分页信息设置
                         $scope.pages.total = data.data.total;           // 页面数据总数
                         $scope.pages.star = data.data.startRow;         // 页面起始数
                         $scope.pages.end = data.data.endRow;            // 页面结束数
                         $scope.pages.pageNum = data.data.pageNum;
                     }
                 },
                 function (error) {
                     inform.common(Trans("tip.requestError"));
                 });
            }

            //页面跳转,str存在则为修改,否则为新增
            $scope.popModalDetail = function (id) {
                LocalCache.setObject("trainPlanManagement_formRefer",$scope.formRefer);
                if (id) {
                    $state.go("app.office.trainPlanManagementUp", {item:id});
                }else{
                    $state.go("app.office.trainPlanManagementAdd");
                }
            };
            //页面跳转到培训评价信息页面
            $scope.goTrainEvaluateView = function (id) {
                 LocalCache.setObject("trainPlanManagement_formRefer",$scope.formRefer);
                     $state.go("app.office.trainEvaluateView", {id:id});
            };
            // 页面跳转到评价修改
            $scope.upPerson = function(m){
            	LocalCache.setObject("trainPlanManagement_formRefer",$scope.formRefer);
            	$state.go("app.office.trainPlanPersonUp",{trainId:m.id,personId:m.participantId,person:m.participants});
            };
            //页面跳转到我的星星--我的星星明细部门贡献详情页
            $scope.getBack = function(){
                $state.go('app.personal_star_department_detail');
            }
            // 删除弹框
            $scope.open = function (item, flag) {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function () {
                            return Trans("common.deleteTip");
                        }
                    }
                });
                modalInstance.result.then(function () {
                	if (flag != null) {
                		//删除培训信息
                    	$scope.removeDetail(item);
                	} else {
                		//删除人员信息
                    	removePersonEvaluation(item);
                	}
                });
            };

            // 删除数据 JSON.stringify(removeParam)
            $scope.removeDetail = function (item) {
                var urlData = {
                    'id': item.id
                };
                trainPlanService.deleteDetail(urlData)
                    .then(function (data) {
                        if (data.code === "0000") {
                            inform.common(Trans("tip.delSuccess"));
                            getData(AgreeConstant.pageNum);
                        } else {
                            inform.common(data.message);
                        }
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            };
            /**
             * 删除人员信息与评价信息
             */
            function removePersonEvaluation(item){
            	var urlData = {
            			"trainId":item.id,
                    	"participantId":item.participantId
                };
            	trainPlanService.removePersonEvaluation(urlData).then(function (data) {
                	if (data.code === AgreeConstant.code) {
                    	inform.common(Trans("tip.delSuccess"));
                    	getData(AgreeConstant.pageNum);
                	} else {
                    	inform.common(data.message);
                	}
                }, function (error) {
                	inform.common(Trans("tip.requestError"));
                });
            }

            //开始时间
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;    //开始时间
                $scope.openedEnd = false;
                $scope.up = false;
                $scope.add = false; //新增时间
            };

            //结束时间
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;    //结束时间
                $scope.up = false;
                $scope.add = false; //新增时间
            };
            /**
    		 * 文件选择事件
    		 */
    	     $scope.selectFile = function() {
    	     	document.getElementById("files").click();
    	     }
    	     /**
    	      * 匿名
    	      */
    	     $scope.selectFileAnonymity = function() {
    	     	document.getElementById("filesAnonymity").click();
    	     }
    	     /**
    	      * 考试
    	      */
    	     $scope.selectFileExam = function() {
    	     	document.getElementById("filesExam").click();
    	     }
    		/**
    		 * 选择上传文件后事件
    		 */
    		function fileChangeEvent(e){
    			var fileName = "文件名称：" + e.currentTarget.files[0].name;
    			$("#fileNameDis").text(fileName);
    		}
    		/**
    		 * 匿名选择上传文件后事件
    		 */
    		function fileChangeEventAnonymity(e){
    			var fileName = "文件名称：" + e.currentTarget.files[0].name;
    			$("#fileNameDisAnonymity").text(fileName);
    		}
    		/**
    		 * 考试选择上传文件后事件
    		 */
    		function fileChangeEventExam(e){
    			var fileName = "文件名称：" + e.currentTarget.files[0].name;
    			$("#fileNameDisExam").text(fileName);
    		}
    	    /**
    	     * 上传文件
    	     */
    	    function submitForm(e){
    	    	var formData = new FormData(document.getElementById("form"));
    	    	var file = e.currentTarget.files[0]; //获取文档中有类型为file的第一个input元素
    	    	if(!file){
                    inform.common("请先选择文件!");
                    return false;
                }else if(file.size > AgreeConstant.fileSize){
                    inform.common("上传文件大小不得超过2M，请分割后重新上传!");
                    $("#myForm")[0].reset();
                    $("#fileNameDis").text("");
                    return false;
                }
    	    	formData.append('file', file);
    	    	var a = file.type;
    	    	if(a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"){
    	        	inform.common("请选择.xlsx类型的文档进行上传!");
    	        	return false;
    	        }
    	        inform.modalInstance("确定要上传文件吗？").result.then(function() {
    	            inform.uploadFile('train/uploadExcel',formData,function func(result){
    	                // 关闭遮罩层
    	                inform.closeLayer();
    	                $modal.open({
    	                    templateUrl: 'errorModel.html',
    	                    controller: 'ModalInstanceCtrl',
    	                    size: "lg",
    	                    resolve: {
    	                        items: function () {
    	                            return result.message;
    	                        }
    	                    }
    	                });
    	               $("#form")[0].reset();
    	               $("#fileNameDis").text("");
    	               getData(1);
    	            });
    	        });
    	     }
    	    /**
    	     * 匿名上传文件
    	     */
    	    function submitFormAnonymity(e){
    	    	var formData = new FormData(document.getElementById("formAnonymity"));
    	    	var file = e.currentTarget.files[0]; //获取文档中有类型为file的第一个input元素
    	    	if(!file){
                    inform.common("请先选择文件!");
                    return false;
                }else if(file.size > AgreeConstant.fileSize){
                    inform.common("上传文件大小不得超过2M，请分割后重新上传!");
                    $("#formAnonymity")[0].reset();
                    $("#fileNameDisAnonymity").text("");
                    return false;
                }
    	    	formData.append('file', file);
    	    	var a = file.type;
    	    	if(a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"){
    	        	inform.common("请选择.xlsx类型的文档进行上传!");
    	        	return false;
    	        }
    	        inform.modalInstance("确定要上传文件吗？").result.then(function() {
    	            inform.uploadFile('train/uploadExcelAnonymity',formData,function func(result){
    	                // 关闭遮罩层
    	                inform.closeLayer();
    	                $modal.open({
    	                    templateUrl: 'errorModel.html',
    	                    controller: 'ModalInstanceCtrl',
    	                    size: "lg",
    	                    resolve: {
    	                        items: function () {
    	                            return result.message;
    	                        }
    	                    }
    	                });
    	               $("#formAnonymity")[0].reset();
    	               $("#fileNameDisAnonymity").text("");
    	               getData(1);
    	            });
    	        });
    	     }
    	    /**
    	     * 考试上传文件
    	     */
    	    function submitFormExam(e){
    	    	var formData = new FormData(document.getElementById("formExam"));
    	    	var file = e.currentTarget.files[0]; //获取文档中有类型为file的第一个input元素
    	    	if(!file){
                    inform.common("请先选择文件!");
                    return false;
                }else if(file.size > AgreeConstant.fileSize){
                    inform.common("上传文件大小不得超过2M，请分割后重新上传!");
                    $("#formExam")[0].reset();
                    $("#fileNameDisExam").text("");
                    return false;
                }
    	    	formData.append('file', file);
    	    	var a = file.type;
    	    	if(a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"){
    	        	inform.common("请选择.xlsx类型的文档进行上传!");
    	        	return false;
    	        }
    	        inform.modalInstance("确定要上传文件吗？").result.then(function() {
    	            inform.uploadFile('train/uploadExcelExam',formData,function func(result){
    	                // 关闭遮罩层
    	                inform.closeLayer();
    	                $modal.open({
    	                    templateUrl: 'errorModel.html',
    	                    controller: 'ModalInstanceCtrl',
    	                    size: "lg",
    	                    resolve: {
    	                        items: function () {
    	                            return result.message;
    	                        }
    	                    }
    	                });
    	               $("#formExam")[0].reset();
    	               $("#fileNameDisExam").text("");
    	               getData(1);
    	            });
    	        });
    	     }
    	    /**
    	     * 下载培训信息
    	     */
    	    $scope.toExcel = function (flag) {
            	var dep =  [];
            	if ( null != $scope.formRefer.department && $scope.formRefer.department !== ''){
            		//如果查询条件仅有一级部门
                	if($scope.formRefer.twoDepartment === '' || null == $scope.formRefer.twoDepartment){
                		comService.getOrgChildren($scope.formRefer.department).then(function (data) {
                            if (data.code === AgreeConstant.code) {
                            	//循环一级部门下的二级部门，放入所有code值
                        		for(var i = 0 ;i<data.data.length;i++) {
                        			dep.push(data.data[i].orgCode);
                                }
                        		//放入一级部门本身
                            	dep.push($scope.formRefer.department);
                            	getExcelInfo(dep,flag);
                            }
                		});
                	} else {
                		//如果有二级部门 就仅查询二级部门
                		dep.push($scope.formRefer.twoDepartment);
                		getExcelInfo(dep,flag);
                	}
            	}else {
            		getExcelInfo(dep,flag);
            	}
            };
            /**
             * 获取下载信息
             */
            function getExcelInfo(dep,flag){
            	var urlData = {
                        'affiliatedGroups': dep,
                        'temName': $scope.formRefer.cname,
                        'trainer': $scope.formRefer.trainer,
                        'participants': $scope.formRefer.person,
                        'trainType': $scope.formRefer.trainType,
                        'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                        'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd')
                    };
                    //判断下载哪个报表
                    if ("count"===flag){
                    	//培训汇总信息
                    	toExcelTrainCount(urlData);
                    }else if ("evaluate"===flag){
                    	//培训评价信息
                    	toExcelTrainEvaluate(urlData);
                    }else if ("participant"===flag){
                    	//培训学员信息
                    	toExcelTrainParticipant(urlData);
                    }else if ("exam"===flag){
                    	//培训考试模板
                    	toExcelTrainExam();
                    } else if("internalTrain"===flag) {
                        //内部培训信息模块
                        toExcelInternalTrainTemplate();
                    }
            }
            /**
             * 下载培训汇总信息
             */
            function toExcelTrainCount(urlData) {
            	inform.modalInstance("确定要下载吗?").result.then(function () {
                	inform.downLoadFile('train/toExcelTrainCount',urlData,'培训汇总信息.xlsx');
            	});
            }
            /**
             * 下载培训评价信息
             */
            function toExcelTrainEvaluate(urlData) {
            	inform.modalInstance("确定要下载吗?").result.then(function () {
                	inform.downLoadFile('train/toExcelTrainEvaluate',urlData,'培训评价信息.xlsx');
            	});
            }
            /**
             * 下载培训学员信息
             */
            function toExcelTrainParticipant(urlData) {
            	urlData.join= $scope.formRefer.join;
            	inform.modalInstance("确定要下载吗?").result.then(function () {
                	inform.downLoadFile('train/toExcelTrainParticipant',urlData,'培训学员信息.xlsx');
            	});
            }
            /**
    	     * 下载培训信息
    	     */
            $scope.toExcelModule = function () {
            	inform.modalInstance("确定要下载吗?").result.then(function () {
                	inform.downLoadFile('train/toExcelModule',{},'培训反馈表模板.xlsx');
            	});
            };
            /**
             * 下载考试信息导入模板
             */
             function toExcelTrainExam () {
            	inform.modalInstance("确定要下载吗?").result.then(function () {
                	inform.downLoadFile('train/toExcelExamModule',{},'培训考试信息表模板.xlsx');
            	});
            }

            function toExcelInternalTrainTemplate(){
                inform.modalInstance("确定要下载吗?").result.then(function () {
                    inform.downLoadFile('train/toExcelExamModule',{},'内部培训信息模板.xlsx');
                });
            }
            /**
             * 获取按钮权限
             */
            function getButtonPermission(){
                var buttons = {
                    'Button-TrainPlanManagement-addPlan':'addTrainInfo',
                    'Button-TrainPlanManagement-upList':'upList',
                    'Button-TrainPlanManagement-downloadList':'downLoadList',
                    'Button-TrainPlanManagement-alterDetails':'changeProInfo',
                    'Button-TrainPlanManagement-inspectDetail':'inspectProInfo',
                    'Button-TrainPlanManagement-deleteDetail':'deleteProInfo',
                    'Button-TrainPlanManagement-importInfo':'upExamInfo',
                    'Button-TrainPlanManagement-downloadExam':'downloadModule',
                    'Button-TrainPlanManagement-downStudent':'downStudentInfo',
                    'Button-TrainPlanManagement-deleteInfo':'deleteStuInfo',
                    'Button-TrainPlanManagement-inspectInfo':'inspectStuInfo'
                };
                var urlData = {
                    'userId':LocalCache.getSession("userId"),
                    'parentPermission':'ButtonTrainPlanManagement',
                    'buttons':buttons
                };
                comService.getButtonPermission(urlData,$scope);
            }
            
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();

/*
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date:   2019-01-10 15:20:05
 * @Last Modified by:   l<PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
    'use strict';
    app.factory('correctService', correctService);
    correctService.$inject=["HttpService",'$rootScope'];

    function correctService(HttpService,$rootScope){

        var service={
            getData:getData
        };
        return service;

        /**
         * 获取svn配置
         *
         * @return       [svn配置信息]
         * */
        function getData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi+'correct/getData',urlData);
        }
    }
})();

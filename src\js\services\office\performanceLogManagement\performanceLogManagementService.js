(function () {
  'use strict';
  app.factory('performanceLogManagementService', performanceLogManagementService);
  performanceLogManagementService.$inject = ['HttpService', '$rootScope'];

  function performanceLogManagementService(HttpService, $rootScope) {
    var service = {
      getAllGroupList,
      getPerformanceLog,
      closeZentaoTask,
      activeZentaoTask,
      getPerformanceLogDetailByTag,
      getPerformanceLogDetailById,
      savePerformanceLogDetail,
      getCommunicationUnfinishedTask,
      getCoachingUnfinishedTask,
      deleteRecord,
    };
    return service;
    /**
     ** 获取所有小组的选项
     */
    function getAllGroupList(params) {
      return HttpService.post($rootScope.getWaySystemApi + 'team/getTeamData', params);
    }
    /**
     ** 查询绩效记录(列表)
     */
    function getPerformanceLog(params) {
      return HttpService.post($rootScope.getWaySystemApi + 'performance_communicate/get_record', params);
    }
    /**
     ** 删除绩效记录（列表）
     */
    function deleteRecord(id) {
      return HttpService.delete($rootScope.getWaySystemApi + `performance_communicate/delete_record?recordId=${id}`);
    }
    /**
     ** 关闭禅道任务
     */
    function closeZentaoTask(params) {
      return HttpService.get($rootScope.getWaySystemApi + 'performance_comm_pro_task/close_pro_task', params);
    }
    /**
     ** 激活禅道任务
     */
    function activeZentaoTask(params) {
      return HttpService.get($rootScope.getWaySystemApi + 'performance_comm_pro_task/activate_pro_task', params);
    }
    /**
     ** 获取绩效沟通的详情（切换近三年页签的时候专用）
     */
    function getPerformanceLogDetailByTag(params) {
      return HttpService.get($rootScope.getWaySystemApi + 'performance_communicate/select_target_detail', params);
    }
    /**
     ** 获取绩效记录的详情（从查询列表跳转到绩效辅导/沟通页面时使用这个）
     */
    function getPerformanceLogDetailById(params) {
      return HttpService.get($rootScope.getWaySystemApi + 'performance_communicate/select_performance_detail', params);
    }
    /**
     ** 保存绩效记录的修改
     */
    function savePerformanceLogDetail(params) {
      return HttpService.post($rootScope.getWaySystemApi + 'performance_communicate/save_performance_comm', params);
    }
    /**
     ** 查询上期未完成目标--绩效沟通
     */
    function getCommunicationUnfinishedTask(params) {
      return HttpService.post(
        $rootScope.getWaySystemApi + '/performance_comm_task/select_unfinished_comm_task',
        params
      );
    }
    /**
     ** 查询上期未完成目标--绩效辅导
     */
    function getCoachingUnfinishedTask(params) {
      return HttpService.post(
        $rootScope.getWaySystemApi + '/performance_comm_task/select_unfinished_guidance_task',
        params
      );
    }
  }
})();

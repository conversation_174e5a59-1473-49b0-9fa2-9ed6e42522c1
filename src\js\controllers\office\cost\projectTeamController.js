(function () {
    app.controller("projectTeamController",
        ['projectTeamService','comService','$rootScope', '$scope','$state','$stateParams','$modal','inform','Trans','AgreeConstant','LocalCache','$http',
            function (projectTeamService,comService,$rootScope, $scope,$state,$stateParams, $modal,inform,Trans,AgreeConstant,LocalCache,$http) {
                /**
                 * *************************************************************
                 *             初始化部分                                 开始
                 * *************************************************************
                 */

                //项目状态
                $scope.projectStatus= AgreeConstant.projectStatusList;
                $scope.formInput ={
                    department:'',   //部门名称
                    productLine:'',    	//产品线
                    projectName:'',		//项目名称
                    proManagerName:'',	//项目经理
                    status:'进行中'      //状态
                };

                //获取缓存
                if (Object.keys(LocalCache.getObject('project_team_formRefer')).length > 0) {
                    $scope.formInput = LocalCache.getObject('project_team_formRefer');
                    //对原缓存进行覆盖
                    LocalCache.setObject("project_team_formRefer",{});
                }

                $scope.pages = inform.initPages(); 	// 初始化分页数据
                $scope.getData = getData;
                //初始化页面信息
                initPages();

                getData($scope.pages.pageNum);		//在刷新页面时调用该方法


                //设置列表的高度
                setDivHeight();
                //窗体大小变化时重新计算高度
                $(window).resize(setDivHeight);

                /**
                 * *************************************************************
                 *              初始化部分                                 结束
                 * *************************************************************
                 */

                /**
                 * *************************************************************
                 *              方法声明部分                                 开始
                 * *************************************************************
                 */

                //设置列表的高度
                function setDivHeight(){
                    //网页可见区域高度
                    var clientHeight = document.body.clientHeight;
                    var divHeight = clientHeight - (150 + 180);
                    $("#divTBDis").height(divHeight);
                    $("#subDivTBDis").height(divHeight - 70);
                }

                //重置
                $scope.reset = function() {
                    $scope.formInput ={
                        department:'',   //部门名称
                        productLine:'',    	//产品线
                        projectName:'',		//项目名称
                        proManagerName:'',	//项目经理
                        status:'进行中'      //状态
                    };
                }


                function initPages() {
                    //获取山东新北洋集团的下级部门信息
                    $scope.departmentList = [];
                    $scope.departmentMap = {};
                    comService.getOrgChildren('D010053').then(function (data) {
                        $scope.departmentList = comService.getDepartment(data.data);
                        $scope.departmentList.forEach(function (item) {
                            var key = item.orgCode;
                            $scope.departmentMap[key] = item.orgName;
                        });
                    });

                    $scope.lineList = [];
                    $scope.lineMap = {};
                    comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                        if (data.data) {
                            $scope.lineList = data.data;
                            $scope.lineList.forEach(function (item) {
                                var key = item.param_code;
                                $scope.lineMap[key] = item.param_value;
                            });
                        }
                    });
                }

                //获取所有数据以分页的形式
                function getData(pageNum){

                    var urlData ={
                        'departmentCode':$scope.formInput.department,
                        'productLineCode':$scope.formInput.productLine,
                        'projectName':$scope.formInput.projectName,
                        'projectManager':$scope.formInput.proManagerName,
                        'projectStatus' :$scope.formInput.status,
                        'currentPage' : pageNum, 								// 分页页数
                        'pageSize' : $scope.pages.size    						// 分页每页大小
                    };
                    //获取项目预算
                    projectTeamService.getProjectTeam(urlData).then(function(data){
                            if(data.code===AgreeConstant.code){
                                var getata = data.data;
                                $scope.projectList = getata.list;
                                if ($scope.projectList.length===0) {
                                    $scope.pages = inform.initPages(); 			//初始化分页数据
                                    inform.common(Trans("tip.noData"));
                                } else {
                                    // 分页信息设置
                                    $scope.pages.total = getata.total;		// 页面总数
                                    $scope.pages.star = getata.startRow;  	//页面起始数
                                    $scope.pages.end = getata.endRow;  		//页面大小数
                                    $scope.pages.pageNum = getata.pageNum;  	//页面页数
                                }

                            }
                        },
                        function(error) {
                            inform.common(Trans("tip.requestError"));
                        });
                }

                /**
                 * 跳转项目团队明细预览
                 */
                $scope.go = function (projectId){
                    LocalCache.setObject("project_team_formRefer",$scope.formInput);
                    $state.go("app.office.projectTeamDetail", { projectId:projectId});
                }
                /**
                 * *************************************************************
                 *              方法声明部分                                 结束
                 * *************************************************************
                 */

            }]);
})();

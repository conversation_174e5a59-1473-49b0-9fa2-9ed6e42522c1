(function() {
   'use strict';
    app.factory('bugStatisticService', bugStatisticService);
    bugStatisticService.$inject=["HttpService",'$rootScope'];

    function bugStatisticService(HttpService,$rootScope){

        // 获取用户千行代码bug
        function getUserBugsKLOC(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getUserBugKLOC', urlData);
        }

        return {
            getUserBugsKLOC: getUserBugsKLOC,
        };
    }
})();
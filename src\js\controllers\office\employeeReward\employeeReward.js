(function () {
    app.controller('employeeReward', [
        'employeeRewardService',
        '$state',
        'comService',
        '$rootScope',
        '$scope',
        'inform',
        'Trans',
        'AgreeConstant',
        'LocalCache',
        '$stateParams',
        '$modal',
        '$state',
        '$http',
        function (
            employeeRewardService,
            state,
            comService,
            $rootScope,
            $scope,
            inform,
            Trans,
            AgreeConstant,
            LocalCache,
            $stateParams,
            $modal,
            $state,
            $http
        ) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //获取缓存
            $scope.searchObject = LocalCache.getObject('employeeReward_searchObject');
            //清除缓存
            LocalCache.setObject('employeeReward_searchObject', {});
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.pages = {
                pageNum: '1', //分页页数
                size: '100', //分页每页大小
                total: '0', //数据总数
            };

            //绑定文件控件改变事件
            $('#uploadFile').change(fileChangeEvent);
            $('#uploadFile').change(submitForm);

            //初始化页面信息
            initPages();
            $scope.initPages = initPages;
            $scope.flag = false;
            //获取数据
            $scope.getData = getData;
            //初始化时设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.itemList = [];

            //查询获奖信息
            getData(1);
            // 获取修改权限信息（位置：按钮权限-员工信息--员工获奖信息修改）
            getButtonPermission();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $('#divTBDis').height(divHeight);
                $('#subDivTBDis').height(divHeight - 65);
            }
            /**
             * 页面初始化：部门下拉表、奖项下拉表、页面初始
             */
            function initPages() {
                //初始化根据用户名获取一级部门列表
                initPrimaryDeptList();
                //获取人员列表
                employeeSelect();
                //奖项列表
                $scope.rewardList = [];
                comService.getParamList('EMPLOYEE_REWARD', 'EMPLOYEE_REWARD').then(function (data) {
                    $scope.rewardList = angular.fromJson(data.data);
                });
            }

            /**
             * 获取获奖人员信息列表
             */
            function employeeSelect() {
                //获取获奖人员信息列表,获取产品线
                $scope.employeeList = [];
                comService.getEmployeesName().then(function (data) {
                    $scope.employeeList = angular.fromJson(data.data);
                    rewardSelect();
                });
            }
            /**
             * 按钮权限管理
             */
            function getButtonPermission() {
                var buttons = {
                    'Button-staffRewardInfo': 'flag',
                    'Button-staffRewardInfo-splitRewardPerson': 'splitRewardPerson',
                };
                var urlData = {
                    userId: LocalCache.getSession('userId'),
                    parentPermission: 'ButtonCodeStaffInfoManagement',
                    buttons: buttons,
                };
                comService.getButtonPermission(urlData, $scope);
            }
            /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function (data) {
                    if (data.data) {
                        $scope.primaryDeptList = data.data;
                    }
                });
            }

            //修改一级部门，二级部门进行联动
            $scope.changeDept = function () {
                //获取二级部门
                $scope.deptList = [];
                comService.getOrgChildren($scope.primaryDeptCode).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.deptList = data.data;
                    }
                });
            };

            /**
             * 重置
             */
            $scope.rest = function () {
                $scope.searchObject.name = ''; //姓名
                $scope.primaryDeptCode = ''; // 部门名称
                $scope.deptNameCode = ''; // 部门名称
                $scope.searchObject.rewardTypeCode = ''; // 部门名称
                $scope.searchObject.rewardName = ''; //  奖项名称
                $scope.searchObject.startTime = ''; //时间起
                $scope.searchObject.endTime = ''; //时间止
            };
            /**
             * 获取数据以分页的形式
             */
            function getData(indexNum) {
                var urlData = {
                    name: $scope.searchObject.name,
                    primaryDept: $scope.primaryDeptCode,
                    department: $scope.deptNameCode,
                    rewardTypeCode: $scope.searchObject.rewardTypeCode,
                    rewardName: $scope.searchObject.rewardName,
                    date: $scope.date,
                    startTime: $scope.searchObject.startTime,
                    endTime: $scope.searchObject.endTime,
                    page: indexNum,
                    pageSize: $scope.pages.size,
                };
                //获取数据
                employeeRewardService.getRewardInfo(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.itemList = data.data.list;
                            if ($scope.itemList.length === 0) {
                                inform.common(Trans('tip.noData'));
                                $scope.pages = inform.initPages(); //初始化分页数据
                            } else {
                                // 分页信息设置
                                $scope.pages.total = data.data.total; // 页面数据总数
                                $scope.pages.star = data.data.startRow; // 页面起始数
                                $scope.pages.end = data.data.endRow; // 页面结束数
                                $scope.pages.pageNum = data.data.pageNum;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            /**
             * 修改信息
             */
            $scope.updateReward = function () {
                var urlData = {
                    id: $scope.changeParam.id,
                    rewardName: $scope.changeParam.rewardName, //奖项名称
                    rewardTypeCode: $scope.changeParam.rewardTypeCode, //奖项名称
                    date: inform.format($scope.changeParam.date, 'yyyy-MM-dd'), //获奖时间
                    bounty: $scope.changeParam.bounty, //奖金
                    cause: $scope.changeParam.cause, //获得奖项原因
                    mark: $scope.changeParam.mark, //备注
                };
                employeeRewardService.updateReward(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            inform.common(data.message);
                            $('#edit_modal').modal('hide');
                            getData(1);
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            /**
             * 删除数据
             *
             */
            $scope.deleteReward = function (item) {
                var urlData = {
                    id: item.id,
                };
                employeeRewardService.deleteReward(urlData).then(
                    function (data) {
                        if (data.code === '0000') {
                            inform.common(Trans('tip.delSuccess'));
                            $scope.getData(1);
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            /**
             * 删除弹框
             *
             */
            $scope.open = function (item) {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: 'sm',
                    resolve: {
                        items: function () {
                            return Trans('common.deleteTip');
                        },
                    },
                });
                modalInstance.result.then(function () {
                    if (null != item && item !== '') {
                        $scope.deleteReward(item);
                    }
                });
            };
            /**
             * 生成Excel表格
             */
            $scope.toExcel = function () {
                inform.modalInstance('确定要下载吗?').result.then(function () {
                    var urlData = {
                        name: $scope.searchObject.name,
                        primaryDept: $scope.primaryDeptCode,
                        department: $scope.deptNameCode,
                        rewardName: $scope.searchObject.rewardName,
                        date: $scope.date,
                        startTime: $scope.searchObject.startTime,
                        endTime: $scope.searchObject.endTime,
                    };
                    inform.downLoadFile('employeeReward/toExcel', urlData, '员工获奖信息.xlsx');
                });
            };
            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.updateTime1 = false;
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };
            /**
             * 修改页面的开始时间
             */
            $scope.updateTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = false;
                $scope.updateTime1 = true;
            };
            //根据id获取获奖信息
            $scope.popModal = function (item) {
                var urlData = {
                    id: item.id,
                };
                employeeRewardService.getRewardInfo(urlData).then(function (data) {
                    //页面赋值
                    $scope.changeParam = data.data.list[0];
                });
            };

            //根据id获取获奖信息
            $scope.splitModal = function (item) {
                $scope.splitParam = item;
                $scope.splitParam.splitPersonList = null;
            };

            /**
             * 拆分信息
             */
            $scope.splitRewardPersonFunc = function () {
                var urlData = {
                    rewardName: $scope.splitParam.rewardName, //奖项名称
                    rewardTypeCode: $scope.splitParam.rewardTypeCode, //奖项名称
                    date: inform.format($scope.splitParam.date, 'yyyy-MM-dd'), //获奖时间
                    bounty: 0, //奖金
                    cause: $scope.splitParam.cause, //获得奖项原因
                    mark: $scope.splitParam.mark, //备注
                    nameList: $scope.splitParam.splitPersonList, //拆分的人员
                };
                employeeRewardService.splitRewardPerson(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            inform.common(data.message);
                            $('#split_modal').modal('hide');
                            getData(1);
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };

            /**
             * 界面切换（新增)
             */
            $scope.openAddModal = function () {
                LocalCache.setObject('employeeReward_searchObject', $scope.searchObject);
                $state.go('app.office.employeeRewardAdd');
            };

            /**
             * 下载模板
             */
            $scope.toTemplateExcel = function () {
                inform.toTemplateExcel('员工奖励信息表模板', 'report_44');
            };

            /**
             * 选择上传文件
             */
            $scope.selectFile = function () {
                document.getElementById('uploadFile').click();
            };

            /**
             * 选择上传文件后事件
             */
            function fileChangeEvent(e) {
                var fileName = '文件名称：' + e.currentTarget.files[0].name;
                $('#fileNameDis').text(fileName);
            }

            /**
             * 上传文件
             */
            function submitForm() {
                var formData = new FormData(document.getElementById('uploadForm'));
                var file = document.querySelector('input[type=file]').files[0]; //获取文档中有类型为file的第一个input元素
                if (!file) {
                    inform.common('请先选择文件!');
                    return false;
                } else if (file.size > AgreeConstant.fileSize) {
                    inform.common('上传文件大小不得超过2M，请分割后重新上传!');
                    $('#uploadForm')[0].reset();
                    $('#fileNameDis').text('');
                    return false;
                }
                formData.append('file', file);
                var a = file.type;
                if (a !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
                    inform.common('请选择.xlsx类型的文档进行上传!');
                    $('#uploadForm')[0].reset();
                    $('#fileNameDis').text('');
                    return false;
                }
                inform.uploadFile('employeeReward/uploadExcel', formData, function func(result) {
                    // 关闭遮罩层
                    inform.closeLayer();
                    $modal.open({
                        templateUrl: 'errorModel.html',
                        controller: 'ModalInstanceCtrl',
                        size: 'lg',
                        resolve: {
                            items: function () {
                                return result.message;
                            },
                        },
                    });
                    $('#uploadForm')[0].reset();
                    $('#fileNameDis').text('');
                    $scope.getData(1);
                });
            }
        },
    ]);
})();

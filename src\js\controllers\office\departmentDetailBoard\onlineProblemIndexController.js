(function () {
  app.controller('onlineProblemIndexController', [
    'LocalCache',
    'inform',
    'Trans',
    '$ocLazyLoad',
    '$rootScope',
    'comService',
    '$scope',
    '$stateParams',
    '$state',
    'deptBoardFactory',
    'AgreeConstant',
    'onlineProblemService',
    function (
      LocalCache,
      inform,
      Trans,
      $ocLazyLoad,
      $rootScope,
      comService,
      $scope,
      $stateParams,
      $state,
      deptBoardFactory,
      AgreeConstant,
      onlineProblemService
    ) {
      // 初始化
      deptBoardFactory.init($scope, '8');
      // 重置部分
      $scope.resetParam = resetParam;
      function resetParam() {
        deptBoardFactory.initTime($scope, '本年度');
      }
      $scope.getData = getData;
      function getData() {
        getStatisticsData();
        getTeamScheduleChartData();
        // 获取top5的数据
        getTop5Number();
        $scope.changeType($scope.type);
      }
      // 统计区域
      function getStatisticsData() {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
        };
        $scope.showStatisticsInfo = false;
        onlineProblemService.getRateInfo(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.onlineProblemStatisticsData = result.data;
              $scope.showStatisticsInfo = true;
            } else {
              inform.common(result.message);
              $scope.showStatisticsInfo = true;
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
            $scope.showStatisticsInfo = true;
          }
        );
      }
      // 图表部分
      $scope.currentOnlineProblemChart = null;
      window.addEventListener('resize', chartResize);
      $scope.$on('$destroy', function () {
        window.removeEventListener('resize', chartResize);
      });
      function chartResize() {
        if ($scope.currentOnlineProblemChart) {
          $scope.currentOnlineProblemChart.resize();
        }
      }
      function getTeamScheduleChartData() {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
        };
        deptBoardFactory.chartHideClear($scope.currentOnlineProblemChart);
        deptBoardFactory.chartShowLoading($scope.currentOnlineProblemChart);
        onlineProblemService.getOnlineBugChart(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.onlineProblemChartInfo = result.data;
              deptBoardFactory.chartHideLoading($scope.currentOnlineProblemChart);
              eChartShowForOnlineProblemBarAndLine(
                $scope.currentOnlineProblemChart,
                $scope.onlineProblemChartInfo,
                '线上问题',
                AgreeConstant.departmentBoard.onlineProblemLegendData
              );
            } else {
              inform.common(result.message);
              deptBoardFactory.chartHideLoading($scope.currentOnlineProblemChart);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
            deptBoardFactory.chartHideLoading($scope.currentOnlineProblemChart);
          }
        );
      }
      function eChartShowForOnlineProblemBarAndLine(currentChart, data, title, legendData) {
        var xData = [];
        var onlineProblemNumberData = [];
        var rateData = [];
        if (data.length) {
          angular.forEach(data, function (eachData) {
            xData.push(eachData.projectName);
            onlineProblemNumberData.push({
              value: eachData.onlineBugCount,
            });
            rateData.push({
              value: eachData.leakRate,
            });
          });
          var option = {
            title: {
              text: title,
              textStyle: {
                fontSize: 18,
                color: '#333',
              },
            },
            grid: {
              left: '2%',
              right: '2%',
              bottom: '0',
              containLabel: true,
            },
            legend: {
              data: legendData,
            },
            xAxis: [
              {
                type: 'category',
                data: xData,
                axisPointer: {
                  type: 'shadow',
                },
                axisLabel: {
                  rotate: 20,
                },
              },
            ],
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'cross',
                crossStyle: {
                  color: '#999',
                },
              },
              formatter: function (params, ticket, callback) {
                return deptBoardFactory.formatterCall(params, ticket, callback, '缺陷逃逸', '个');
              },
            },
            yAxis: [
              {
                type: 'value',
                axisLabel: {
                  formatter: '{value}个',
                },
                minInterval: 1,
              },
              {
                type: 'value',
                axisLabel: {
                  formatter: '{value}%',
                },
              },
            ],
            series: [
              {
                name: legendData[0],
                type: 'bar',
                barWidth: '20%',
                yAxisIndex: 0,
                data: onlineProblemNumberData,
                show: false,
              },
              {
                name: legendData[1],
                type: 'line',
                yAxisIndex: 1,
                data: rateData,
              },
            ],
          };
        } else {
          option = {
            title: [
              {
                text: title,
                textStyle: {
                  fontSize: 12,
                  color: '#333',
                },
              },
              {
                text: '暂无数据',
                left: 'center',
                top: 'center',
                color: '#333',
                textStyle: {
                  fontSize: 20,
                },
              },
            ],
          };
        }

        currentChart.setOption(option, true);
      }

      // top5标签上面数字
      function getTop5Number() {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
        };
        onlineProblemService.getOnlineBugCount(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.top5 = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // top5部分
      $scope.changeType = function changeType(type) {
        $scope.type = type;
        if (type === '8') {
          getApprovalData('审批中');
        } else if (type === '9') {
          getFinishData('已完成');
        }
      };
      function getApprovalData(finishState) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          status: finishState,
        };
        onlineProblemService.getOnlineBugList(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.approvalData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }

      function getFinishData(finishState) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          status: finishState,
        };
        onlineProblemService.getOnlineBugList(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.finishData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }

      function getAllOrg() {
        comService.getOrgChildren('D010053').then(function (data) {
          $scope.departmentList = comService.getDepartment(data.data);
          getData();
        });
      }
      $scope.loadSuccess = function () {
        $ocLazyLoad.load(['library/component/echarts.min.js']).then(function () {
          $scope.currentOnlineProblemChart = echarts.init(document.getElementById('onlineProblemChart'));
          var localFormRefer = LocalCache.getObject('departmentList_formRefer');
          if (Object.keys(localFormRefer).length > 0) {
            $scope.formRefer = localFormRefer;
            $scope.butFlag = localFormRefer.searchTimeString;
          }
          if ($stateParams.orgCode) {
            $scope.formRefer.orgCode = $stateParams.orgCode;
          }
          getAllOrg();
        });
      };
      $scope.title = '';
      $scope.desc = true;
      // 排序
      $scope.order = (str) => {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      };
    },
  ]);
})();

(function() {
	'use strict';
	app.factory('personalOpsDataService', personalOpsDataService);
	personalOpsDataService.$inject = [ "HttpService", '$rootScope' ];

	function personalOpsDataService(HttpService, $rootScope) {
		var service = {
            getOpsDataStatistics:getOpsDataStatistics,
            getLaunchDataProductLine:getLaunchDataProductLine,
            getLaunchDataTeam:getLaunchDataTeam,
            getOpsSupportType:getOpsSupportType,
            getOpsIntegrationType:getOpsIntegrationType
           
		};
		return service;

        function getLaunchDataProductLine(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'opsDataBoard/getLaunchInfoOfProductLine',urlData);
        }
        function getLaunchDataTeam(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'opsDataBoard/getLaunchInfoOfTeam',urlData);
        }
        
        function getOpsSupportType(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'opsDataBoard/getOpsSupportInfoOfType',urlData);
        }

        
        function getOpsIntegrationType(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'opsDataBoard/getIntegrationInfoOfType',urlData);
        }

        //获取运维数据汇总
        function getOpsDataStatistics(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'opsDataBoard/getOpsDataStatistics', urlData);
        }
        
	}
})();
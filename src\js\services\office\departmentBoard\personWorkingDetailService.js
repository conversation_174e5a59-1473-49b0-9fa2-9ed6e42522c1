(function() {
    'use strict';
    app.factory('personWorkingDetailService', personWorkingDetailService);
    personWorkingDetailService.$inject=["HttpService",'$rootScope'];

    function personWorkingDetailService(HttpService,$rootScope){
        //获取工时数据
        function getPersonalWorkingHoursData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'personalDataBoard/getPersonalWorkingHoursData', urlData);
        }

        function getPersonHourTypeHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'personWorkingHours/getPersonHourTypeHoursInfo', urlData);
        }

        function getPersonGatherHoursInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'personWorkingHours/getPersonGatherHoursInfo', urlData);
        }

        return {
            getPersonalWorkingHoursData: getPersonalWorkingHoursData,
            getPersonHourTypeHoursInfo: getPersonHourTypeHoursInfo,
            getPersonGatherHoursInfo: getPersonGatherHoursInfo,
        };
    }
})();

(function() {
	app.controller("companyProWorkTimeManagement", ['comService', '$rootScope', '$scope', 'inform', 'companyProWorkTimeService','Trans', 'AgreeConstant', '$modal', '$state', '$stateParams','LocalCache',
		function(comService, $rootScope, $scope,inform, companyProWorkTimeService, Trans, AgreeConstant, $modal, $state, $stateParams,LocalCache) {
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	//获取缓存
		$scope.formRefer ={};
        $scope.formRefer = LocalCache.getObject('companyProWorkTime_formRefer');
        if($scope.formRefer.type!=='detail'){
        	$scope.formRefer.startTime = inform.format(new Date(),"yyyy")+'-01';
            $scope.formRefer.endTime = inform.format(new Date(),"yyyy")+'-12';
        }

        //对原缓存进行覆盖
        LocalCache.setObject("companyProWorkTime_formRefer",{});
		 // 正则校验配置
         $scope.limitList = AgreeConstant.limitList;

        //页面分页信息
        $scope.pages = {
            pageNum : '',   //分页页数
            size : '',      //分页每页大小
            total : ''      //数据总数
        };
        //产品用途
        $scope.projectPurposeSelect = ['技术规划','市场规划','销售支撑','运营支撑'];
        //软件价值分类
        $scope.softwareValueClassificationSelect = ['软硬一体','以软促硬','硬件配套','技术研发','其他'];
        //行业
        $scope.xqsshySelect = ['金融','物流','新零售','新兴','通用'];
    	// 初始化分页数据
    	$scope.pages = inform.initPages();

        //设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		//获取数据
		$scope.getData = getData;
		getData();
		//初始化页面信息
		initPages();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */

			/**
			 * 页面初始化
			 */
		function initPages() {
			//获取产品线
			$scope.projectLine = [];
            comService.queryEffectiveParam('PRODUCT_TYPE','PRODUCT_TYPE').then(function (data) {
				if (data.data) {
					$scope.projectLine = data.data;
				}
			});
		}

		$scope.resetParam = function(){
            $scope.formRefer.startTime = inform.format(new Date(),"yyyy")+'-01';
            $scope.formRefer.endTime = inform.format(new Date(),"yyyy")+'-12';
            $scope.formRefer.projectName = '';
            $scope.formRefer.productLine = '';
            $scope.formRefer.projectPurpose = '';
            $scope.formRefer.softwareValueClassification = '';
            $scope.formRefer.xqsshy = '';
		}
		/**
		 * 获取项目
		 */
		function getData(pageNum) {
		    //用于详情页的统计周期显示
		    $scope.formRefer.startMonth = inform.format($scope.formRefer.startTime,'yyyy-MM');
		    $scope.formRefer.endMonth = inform.format($scope.formRefer.endTime,'yyyy-MM');
			var urlData ={
			    'projectName':$scope.formRefer.projectName,//项目名称
			    'productLine':$scope.formRefer.productLine,//产品线
                'projectPurpose':$scope.formRefer.projectPurpose,//产品用途
                'softwareValueClassification':$scope.formRefer.softwareValueClassification,//软件价值分类
                'xqsshy':$scope.formRefer.xqsshy,//行业
                'startDate':inform.format($scope.formRefer.startTime,'yyyy-MM'),//开始时间
                'endDate':inform.format($scope.formRefer.endTime,'yyyy-MM'),//结束时间
                'page':pageNum,
                'pageSize':$scope.pages.size
			};
            $scope.allPersonWorks = 0;
			companyProWorkTimeService.getCompanyProWorkTimeList(urlData).then(function(data) {
				if (data.code===AgreeConstant.code) {
                    //项目
                    $scope.companyProWorkTimeData = data.data.list;
                    if ($scope.companyProWorkTimeData.length===0) {
						$scope.pages = inform.initPages(); 			//初始化分页数据
						inform.common(Trans("tip.noData"));
                    } else {
                    // 分页信息设置
                    	$scope.pages.total = data.data.total;           // 页面数据总数
                    	$scope.pages.star = data.data.startRow;         // 页面起始数
                    	$scope.pages.end = data.data.endRow;            // 页面结束数
                    	$scope.pages.pageNum = data.data.pageNum;       //页号
                        companyProWorkTimeService.getAllCompanyProWorkTime(urlData).then(function(data) {
                            if (data.code===AgreeConstant.code) {
                                $scope.allPersonWorks = data.data[0].personWorkTime;
                                $scope.allPersonWorksMonth = ($scope.allPersonWorks/21.75).toFixed(1);
                            }
                        });
                    }
				} else {
					inform.common(data.message);
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
		}

		/**
    	 * 设置列表的高度
    	 */
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 205);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 85);
 		}

        //跳转详情页
        $scope.getCompanyProWorkTimeDetail = function(m){
            $scope.formRefer.projectDto = m;
            $scope.formRefer.type='detail';
            LocalCache.setObject("companyProWorkTime_formRefer",$scope.formRefer);
            $state.go('app.office.companyProWorkTimeDetail');
        }

        $scope.toExcel = function(){
			var urlData ={
                'startDate':inform.format($scope.formRefer.startTime,'yyyy-MM'),//开始时间
                'endDate':inform.format($scope.formRefer.endTime,'yyyy-MM'),//结束时间
			};
            inform.modalInstance("确定要下载吗?").result.then(function () {
                inform.downLoadFile('companyProWorkTimeManagement/toExcel',urlData,'工时信息.xlsx');
            });
        }
        /**
	     * *************************************************************
	     *              方法声明部分                                结束
	     * *************************************************************
	     */
		} ]);
})();
(function() {
    'use strict';
    app.controller("personalDataBoardController", ['comService','$rootScope', '$stateParams', '$scope', 'personalDataBoardService','$modal','inform', 'Trans', 'AgreeConstant','$state', 'LocalCache', '$http',
        function(comService, $rootScope,$stateParams,$scope, personalDataBoardService,$modal,inform, Trans, AgreeConstant,$state, LocalCache, $http ) {
           /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //获得当前时间
            $scope.datepicker = {
               currentDate :  new Date()
            };
            //设置列表的高度
    		setDivHeight();
    		//窗体大小变化时重新计算高度
    		$(window).resize(setDivHeight);
    		// 列表数据
            $scope.pageData = [];
            //获取缓存
            $scope.searchObject = LocalCache.getObject('staffInfo_search');
            $scope.getData=getData;
            $scope.searchObject.primaryDept = "";
            $scope.searchObject.department = "";
            $scope.pages = {
                 pageNum:"1",
                 size:"100"
            };
            initPage();//初始化

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */

            /**
             * 设置列表的高度
             */
            function setDivHeight(){
                //网页可见区域高度
                 var clientHeight = document.body.clientHeight;
                 var divHeight = clientHeight - (100 + 185);
                 $("#divTBDis").height(divHeight + 30);
                 $("#subDivTBDis").height(divHeight - 65);
            }

            //重置查询条件
            $scope.clearParams = function() {
                $scope.searchObject.department = "";
                $scope.searchObject.primaryDept = "";

                $scope.searchObject.area = "";

                $scope.searchObject.employeeName = "";
                $scope.searchObject.companyTitle = "";
                $scope.searchObject.startTime = "";
                $scope.searchObject.endTime = "";
                $scope.searchObject.education = "";
                $scope.searchObject.state = "";

                //清空产品线相关信息
                $scope.searchObject.softwareProductLine = "";
                $scope.searchObject.employeeType = "";

                $scope.softwareProductList = [];

            };

            /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];

                comService.getOrgChildren('0002').then(function(data) {
                     if (data.data) {
                         $scope.primaryDeptList = data.data;
                     }
                });

            }
            function setDept(){
                //获取二级部门
                $scope.deptList = [];
                comService.getOrgChildren($scope.searchObject.primaryDept).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.deptList = data.data;
                    }
                });

            }

            //修改一级部门，二级部门进行联动
            $scope.changeDept = function(){
                setDept();
            };


            //获取地区
            function getAreaList(){
               $scope.areaList = [];
               comService.getParamList('AREA_TYPE','AREA_TYPE').then(function(data) {
                  $scope.areaList = data.data;
               });
            }


             //获取系研职称信息
            function getTitleList() {
                //获取系研职称信息
                $scope.titleList = [];
                comService.getParamList('STAFF_TITLE','NEW').then(function(data) {
                  $scope.titleList = data.data;
                });

            }
            //获取工作状态列表
            function getStateList() {
                //获取系研职称信息
                $scope.stateList = [];
                comService.getParamList('STAFF_STATE','STAFF_STATE').then(function(data) {
                  $scope.stateList = data.data;
                });
            }


            //定义排序对象
            $scope.orderObj = {
                title: '$index',
                desc: true,
                order: function(str) {
                    $scope.orderObj.title = str;
                    $scope.orderObj.desc = !$scope.orderObj.desc;
                }
            };

            //入职时间
            $scope.openSearchOnboardTime = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openSearchOnboardTime1 = true;
                $scope.openStopOnboardTime1 = false;
            };
            //截止时间
             $scope.openStopOnboardTime = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openSearchOnboardTime1 = false;
                $scope.openStopOnboardTime1 = true;
            };

            //分页查询
            function getData(page) {
                if (inform.format($scope.searchObject.startTime,'yyyy-MM') === "NaN-NaN") {
                    $scope.searchObject.startTime='';
                }else{
                    $scope.searchObject.startTime = inform.format($scope.searchObject.startTime,'yyyy-MM');
                }
                if (inform.format($scope.searchObject.endTime,'yyyy-MM') === "NaN-NaN") {
                    $scope.searchObject.endTime='';
                }else{
                    $scope.searchObject.endTime = inform.format($scope.searchObject.endTime,'yyyy-MM');
                }

                // 如果有缓存就直接用缓存中的查询数据
                if (Object.keys(LocalCache.getObject('staffInfo_search')).length > 0) {
                    $scope.searchObject = LocalCache.getObject('staffInfo_search');
                    setDept();
                    //对原缓存进行覆盖
                    LocalCache.setObject("staffInfo_search",{});
                }

                //拼装查询条件
                var params = {
                    area:$scope.searchObject.area,
                    primaryDept:$scope.searchObject.primaryDept,
                    department:$scope.searchObject.department,
                    employeeName:$scope.searchObject.employeeName,
                    companyTitle:$scope.searchObject.companyTitle,
                    startTime:$scope.searchObject.startTime,
                    endTime:$scope.searchObject.endTime,
                    education:$scope.searchObject.education,
                    state:$scope.searchObject.state,
                    page: page,
                    size: $scope.pages.size,
                    employeeType:$scope.searchObject.employeeType,
                    softwareProductLine:$scope.searchObject.softwareProductLine,
                    softwareProduct:$scope.searchObject.softwareProduct
                };
                //获取数据
                personalDataBoardService.getStaffInfoData(JSON.stringify(params)).then(function (result) {
                    if (result.code === '0000') {
                        $scope.pageData = result.data.list;
                        if (null ==result.data.list || result.data.list.length === 0) {
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages();
                        } else {
                              $scope.pages.total = result.data.total;		// 页面总数
                              $scope.pages.star = result.data.startRow;  	//页面起始数
                              $scope.pages.end = result.data.endRow;  		//页面大小数
                              $scope.pages.pageNum = result.data.pageNum;  	//页面页数
                        }
                    } else {
                          inform.common(result.message);
                    }
                }, function (reason) {
                    console.log("error");
                });
            }
        //初始化页面
        function initPage() {
            getTitleList();//职称列表
            getAreaList();//地区
            getStateList();//工作状态
            initPrimaryDeptList();//初始化根据用户名获取一级部门列表

            //获取产品线
            $scope.softwareProductLineList = [];
            $scope.softwareProductLineMap = [];
            comService.queryEffectiveParam('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
                if (data.data) {
                    $scope.softwareProductLineList =  data.data;
                    angular.forEach($scope.softwareProductLineList, function (res, index) {
                        $scope.softwareProductLineMap[res.paramCode] = res.paramValue;
                    });
                }
            });
            //获取人员分类
            $scope.employeeTypeList = [
                {paramCode:'直接开发人员',paramValue:'直接开发人员'},
                {paramCode:'开发共用人员',paramValue:'开发共用人员'},
                {paramCode:'项目管理人员',paramValue:'项目管理人员'},
                {paramCode:'开发试制人员',paramValue:'开发试制人员'},
                {paramCode:'管理支撑人员',paramValue:'管理支撑人员'}
            ];

            getData();
        }
         /**
         * *************************************************************
         *              方法声明部分                                结束
         * *************************************************************
         */

      }
    ]);
})();
(function () {
    app.controller("incongruentQuestionManagement", ['comService', '$rootScope', '$stateParams', '$scope', 'incongruentService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
        function (comService, $rootScope, $stateParams, $scope, incongruentService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */


            $scope.type = 'detail';
            //添加问题内容
            $scope.formInsertin = {};
            //页面查询条件
            $scope.formRefer = {
                productLine:''
            };
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //分页
            $scope.pages = inform.initPages(); // 初始化分页数据
            $scope.getData = getData;
            //初始化页面信息
            initPages();


            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 230);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
            }

            /**
             * 页面初始化
             */
            function initPages() {
                //获取问题所属过程域
                $scope.productDomainList = [];
                comService.getParamList('PROCESS_DOMAIN', 'PROCESS_DOMAIN').then(function (data) {
                    if (data.data) {
                        $scope.productDomainList = data.data;
                    }
                });
                //获取开发模型
                $scope.productTypeList = [];
                comService.getParamList('INCONGRUENT_TYPE', 'INCONGRUENT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.productTypeList = data.data;
                    }
                });
                //获取项目严重程度
                $scope.productSeverityList = [];
                comService.getParamList('SEVERITY', 'SEVERITY').then(function (data) {
                    if (data.data) {
                        $scope.productSeverityList = data.data;
                    }
                });
                //获取不符合问题状态
                $scope.productStateList = [];
                comService.getParamList('STATE', 'PROBLEM_STATE').then(function (data) {
                    if (data.data) {
                        $scope.productStateList = data.data;
                    }
                });
                //获取产品线
                $scope.produclLineMap = [];
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    $scope.produclLineSelect = angular.fromJson(data.data);

                    var type = LocalCache.getSession('processConformityManagement_type');
                    //上个页面传来的参数
                    if(type !== 'year') {
                        $scope.formRefer.startTime = LocalCache.getSession('processConformityManagement_startTime');
                        $scope.formRefer.startTime = paramConversion($scope.formRefer.startTime);
                        $scope.formRefer.endTime = LocalCache.getSession('processConformityManagement_endTime');
                        $scope.formRefer.endTime = paramConversion($scope.formRefer.endTime);
                        $scope.formRefer.flag = 'proj';
                    }else{
                        $scope.formRefer.flag = 'year';
                    }

                    var productLineName = LocalCache.getSession('processConformityManagement_param');
                    //若为汇总行，则直接查询
                    if('汇总' === productLineName){
                        getData("");
                    }
                    angular.forEach($scope.produclLineSelect, function (res, index) {
                        $scope.produclLineMap[res.param_code] = res.param_value;
                        //将从上一个页面传来的产品线名称转换为code，并进行查询
                        if(res.param_value === productLineName){
                            $scope.formRefer.productLine = res.param_code;
                            getData("");
                        }
                    });
                });


            }

            /**
             * 将上个页面传入的时间参数，转换为时间字符串
             * @param param
             * @returns {*}
             */
            function paramConversion(param) {
                if(null == param || 'undefined' === param || '' === param){
                    return '';
                }
                return inform.format(param,'yyyy-MM-dd');

            }


            /**
             * 重置
             */
            $scope.reset = function () {
                $scope.formRefer = {};
            };

            /**
             * 查询开始时间
             */
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };

            /**
             * 查询结束时间
             */
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };


            /**
             * 获取项目下的问题
             */
            function getData(pageNum) {
                var urlData = {
                    'productLine':$scope.formRefer.productLine,
                    'projectName': $scope.formRefer.name,//项目名称
                    'state': $scope.formRefer.state,//状态
                    'type': $scope.formRefer.type, //类型
                    'processDomain': $scope.formRefer.processDomain,//问题所属过程域
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'), //发现日期-起始
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //发现日期-结束
                    'currentPage': pageNum,//当前页数
                    'pageSize': $scope.pages.size,//每页显示条数
                    'flag':  $scope.formRefer.flag //是否可以查看被禁用的项目
                };
                incongruentService.getProjectDetailsInformation(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            //项目详情
                            $scope.jsonData = data.data.list;
                            if ($scope.jsonData.length === 0) {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages();
                            } else {
                                //分页信息设置
                                $scope.pages.total = data.data.total;
                                $scope.pages.star = data.data.startRow;
                                $scope.pages.end = data.data.endRow;
                                $scope.pages.pageNum = data.data.pageNum;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }


            /**
             * 跳转至项目信息管理
             */
            $scope.goBack = function () {
                $state.go('app.office.processConformityManagement');
            };


            $scope.toExcel =function () {

                inform.modalInstance("确定要下载吗！").result.then(function() {
                    if($scope.jsonData.length === 0){
                        inform.common("下载失败，需要下载的数据为空");
                        return;
                    }
                    //拼装下载内容
                    var urlData = {
                        'productLine':$scope.formRefer.productLine,
                        'projectName': $scope.formRefer.name,//项目名称
                        'state': $scope.formRefer.state,//状态
                        'type': $scope.formRefer.type, //类型
                        'processDomain': $scope.formRefer.processDomain,//问题所属过程域
                        'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'), //发现日期-起始
                        'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //发现日期-结束
                        'flag': $scope.formRefer.flag //是否可以查看被禁用的项目
                    };

                    if(null == urlData.productLine || '' === urlData.productLine || urlData.productLine.length <= 0){
                        inform.downLoadFile('incongruent/toExcelForProblem', urlData, '不符合项问题详情报告.rar');
                    }else{
                        inform.downLoadFile('incongruent/toExcelForProblem', urlData, $scope.produclLineMap[urlData.productLine] + '不符合项问题详情报告.xlsx');
                    }
                });

            };
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */

        }]);
})();
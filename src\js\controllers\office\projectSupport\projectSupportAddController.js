//# sourceURL=js/controllers/office/projectSupport/projectSupportAddController.js
(function() {
    app.controller("projectSupportAddController", ['projectSupportService', '$rootScope', 'comService', '$scope', '$state', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function(projectSupportService, $rootScope, comService, $scope, $state, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */

            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //支持事项下载标注拉框数据源
            $scope.supportItemDownloadFlagSelect = [{
                value: '0',
                label: '下载'
            }, {
                value: '1',
                label: '不下载'
            }];
            //初始化页面信息
            initPages();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            function initPages() {
                 //获取员工信息
                $scope.employeesList = [];
                comService.getEmployeesName().then(function(data) {
                    $scope.employeeList = data.data;
                });
                //获取山东新北洋集团的下级部门信息
                $scope.departmentSelect = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentSelect = comService.getDepartment(data.data);
                });
                //支持状态
                $scope.stateList = [];
                comService.getParamList('WEEKLY_REPORT', 'STATE').then(function(data) {
                    $scope.stateList = data.data;
                });
                setValue();
            }
            /**
             * 若为更新 则赋值
             */
            function setValue() {
                if ($stateParams.item==null) {
                    //状态下拉框初始值
                    $scope.changeParam = {
                        stateCode: '0001',
                        supportItemDownloadFlag:'0'
                    };
                    $scope.signName = "新增需支持事项";
                    return;
                }
                $scope.signName = "修改需支持事项";
                var urlData = {
                    'id': $stateParams.item,
                    'projectId': $stateParams.projectId
                };
                projectSupportService.getProjectSupportInfo(urlData).then(function(data) {
                    if (data.code === '0000') {
                        $scope.changeParam = data.data.list[0];
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 保存信息
             */
            $scope.saveInfo = function() {
                var urlData = {
                    'contentPresentation': $scope.changeParam.contentPresentation,
                    'supportDep': $scope.changeParam.supportDepCode,
                    'state': $scope.changeParam.stateCode,
                    'supportItemDownloadFlag':$scope.changeParam.supportItemDownloadFlag,//支持事项下载标志
                    'staff': $scope.changeParam.staffCode,
                    'timeRequiredStart': inform.format($scope.changeParam.timeRequiredStart, 'yyyy-MM-dd'),
                    'timeRequiredEnd': inform.format($scope.changeParam.timeRequiredEnd, 'yyyy-MM-dd'),
                    'conditionPresentation': $scope.changeParam.conditionPresentation,
                    'staffActual': $scope.changeParam.staffActualCode,
                    'timeActualStart': inform.format($scope.changeParam.timeActualStart, 'yyyy-MM-dd'),
                    'timeActualEnd': inform.format($scope.changeParam.timeActualEnd, 'yyyy-MM-dd')
                };
                //新增
                if ($stateParams.item==null) {
                    urlData.projectId = $stateParams.projectId;
                    addInfo(urlData);
                } else {
                    //修改 
                    urlData.id = $scope.changeParam.id;
                    upInfo(urlData)
                }
            };
            /**
             * 新增信息
             */
            function addInfo(urlData) {
                projectSupportService.addProjectSupportInfo(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        inform.common(data.message);
                        $scope.goback();
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 修改信息
             * @param urlData
             */
            function upInfo(urlData) {
                projectSupportService.updateProjectSupportInfo(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        layer.confirm(data.message, {
                            title: false,
                            btn: ['确定']
                        }, function(result) {
                            layer.close(result);
                            $scope.goback();
                        });
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 返回
             */
            $scope.goback = function() {
                $state.go("app.office.projectWeeklyDetail", {
                    projectId: $stateParams.projectId,
                    type: '4'
                });
            };
            /**
             * 新增中的时间
             */
            $scope.requiredDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.requiredStart = true;
                $scope.requiredStartEnd = false;
                $scope.actuallStart = false;
                $scope.actuallEnd = false;
            };
            $scope.requiredDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.requiredStart = false;
                $scope.requiredEnd = true;
                $scope.actualStart = false;
                $scope.actuallEnd = false;
            };
            $scope.actuallDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.requiredStart = false;
                $scope.requiredStartEnd = false;
                $scope.actuallStart = true;
                $scope.actuallEnd = false;
            };
            $scope.actuallDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.requiredStart = false;
                $scope.requiredStartEnd = false;
                $scope.actuallStart = false;
                $scope.actuallEnd = true;
            };
            //事项状态修改，联动修改支持事项下载标志
            $scope.changeStateCode = function(){
                if($scope.changeParam.stateCode === '0003'){
                    $scope.changeParam.supportItemDownloadFlag = '1';
                }
            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }
    ]);
})();
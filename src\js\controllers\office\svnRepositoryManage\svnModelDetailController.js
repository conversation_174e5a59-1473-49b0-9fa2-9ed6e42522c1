(function () {
    app.controller("svnModelDetailController", ['comService','$rootScope', '$scope','$state','$stateParams','$modal','svnModelDetailService','inform','$window','Trans','AgreeConstant',
        function (comService,$rootScope, $scope,$state,$stateParams, $modal,svnModelDetailService,inform,$window,Trans,AgreeConstant) {
		//查询输入框
    	$scope.formRefer={};
    	$scope.formRefer.moduleName = '';
    	$scope.formRefer.staffId = '';
    	$scope.formRefer.storageName = '';
			// 初始化分页数据
		$scope.pages = inform.initPages();

		$scope.getData = getData;
		function getData() {
			var urlData = {
				moduleName: $scope.formRefer.moduleName,
				staffId: $scope.formRefer.staffId,
				storageName: $scope.formRefer.storageName
			}
			svnModelDetailService.getStaffDetails(urlData).then(function (data) {
				$scope.showTable = 1;
				if (data.code === AgreeConstant.code) {
					var jsonData = data.data;
					$scope.tableDataList = angular.fromJson(data.data);
					if ($scope.tableDataList.length === 0) {
						inform.common(Trans("tip.noData"));
						$scope.pages = inform.initPages();
					} else {
						//分页信息设置
						$scope.pages.total = jsonData.total;
						$scope.pages.star = jsonData.startRow;
						$scope.pages.end = jsonData.endRow;
						$scope.pages.pageNum = jsonData.pageNum;
					}
				} else {
					inform.common(data.message);
				}
				setTimeout(tryResetPage, 500);
			}, function (error) {
				inform.common(Trans("tip.requestError"));
			});
		}

		//设置Table格式
		 function tryResetPage(){
			$('#fixedLeftAndTop').DataTable( {
				//可被重新初始化
				retrieve:       true,
				//自适应高度
				scrollY:        'calc(100vh - 280px)',
				scrollX:        true,
				scrollCollapse: false,
				//控制每页显示
				paging:         false,
				//冻结列（默认冻结左1）
				fixedColumns:   {
					leftColumns: 0,
					rightColumns: 0
				},
				//search框显示
				searching:      false,
				//排序箭头
				ordering:       false,
				//底部统计数据
				info:           false
			} );
		}

		$scope.backRepositoryManage = function () {
			$state.go("app.office.svnRepositoryManage", { type: '2', repositoryName: '' });
		}

		$scope.$watch('$viewContentLoaded', function () {
			$scope.formRefer.staffId = $stateParams.staffId;
			$scope.formRefer.storageName = $stateParams.storageName;
			getData();
		});
	}]);
})();

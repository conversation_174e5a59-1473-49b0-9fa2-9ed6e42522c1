(function() {
	'use strict';
	app
	.factory('<PERSON><PERSON><PERSON><PERSON><PERSON>', <PERSON><PERSON><PERSON>ervic);
	AuthServic.$inject=['HttpService','$rootScope'];
	function AuthServic(HttpService,$rootScope){
		var service={
			selectTest:selectTest,
			addAuth:addAuth,
			insertTestjson:insertTest<PERSON>son,
			showAllAuth:showAllAuth,
            updateAuth:updateAuth,
            delAuth:delAuth,
            searchAuthByCode:searchAuthByCode,
            searchAuth:searchAuth,
            downloadAuth:downloadAuth,
            downloadPoint:downloadPoint,
            getCookie:getCookie,
            signCodeResolver:signCodeResolver,
            authStatistic:authStatistic,
            authStatisticInit:authStatisticInit

		};
		return service;
        function getCookie(name) 
            { 
                var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
             
                if(arr=document.cookie.match(reg))
             
                    return unescape(arr[2]); 
                else 
                    return null; 
            } 
       
        function selectTest(){
        	return HttpService.post($rootScope.gateInfoApi+'lms/auth/select?name='+getCookie('name'),{});
        }

        function addAuth(signCode,cpuCode,hardwareCode,machineCode,clientTypeCode,startTime,endTime,applyUser,authorizeUser,applyReason){
            
        	var param='?signCode='+signCode+'&cpuCode='+cpuCode+'&hardwareCode='+hardwareCode+'&machineCode='+machineCode+'&clientTypeCode='+clientTypeCode+'&startTime='+startTime+
        	'&endTime='+endTime+'&applyUser='+applyUser+'&authorizeUser='+authorizeUser+'&applyReason='+applyReason+'&name='+getCookie('name');
            return HttpService.post($rootScope.gateInfoApi+'lms/authOffline/addAuth'+param,{});
        }

        function insertTestjson(authInfo){
        	
            return HttpService.post($rootScope.gateInfoApi+'lms/auth/apply?name='+getCookie('name'),authInfo);
        }

        function showAllAuth(){
            
            return HttpService.post($rootScope.gateInfoApi+'lms/authOffline/showAllAuth?name='+getCookie('name'),{});
        }
         function updateAuth(signCode,cpuCode,hardwareCode,machineCode,clientTypeCode,applyUser,startTime,endTime,authorizeUser,applyReason){
            var param='?signCode='+signCode+'&cpuCode='+cpuCode+'&hardwareCode='+hardwareCode+'&machineCode='+machineCode+'&clientTypeCode='+clientTypeCode+'&applyUser='+applyUser+'&startTime='+startTime+'&endTime='+endTime+'&authorizeUser='+authorizeUser+'&applyReason='+applyReason+'&name='+getCookie('name');
            return HttpService.post($rootScope.gateInfoApi+'lms/authOffline/change'+param,{});
        }
         function delAuth(signCode){
            
            return HttpService.post($rootScope.gateInfoApi+'lms/auth/delAuth?signCode='+signCode+'&name='+getCookie('name'),{});
        }
         function searchAuthByCode(signCode){
            
            return HttpService.post($rootScope.gateInfoApi+'lms/authOffline/searchAuthByCode?signCode='+signCode+'&name='+getCookie('name'),{});
        }

        function searchAuth(machineCode,clientTypeCode,customerName){
            var param='?machineCode='+machineCode+'&clientTypeCode='+clientTypeCode+'&applyUser='+customerName+'&name='+getCookie('name');
            return HttpService.post($rootScope.gateInfoApi+'lms/authOffline/searchAuth'+param,{});
        }

        function downloadAuth(signCode){
            
            return HttpService.post($rootScope.gateInfoApi+'lms/auth/downloadAuthFile?signCode='+signCode+'&name='+getCookie('name'),{});
        }
        function downloadPoint(signCode){
            
            return HttpService.post($rootScope.gateInfoApi+'lms/auth/downloadPointFile?signCode='+signCode+'&name='+getCookie('name'),{});
        }
        function signCodeResolver(signCode){
            
              return HttpService.post($rootScope.gateInfoApi+'lms/authOffline/signCodeResolver?signCode='+signCode+'&name='+getCookie('name'),{});
           // return HttpService.post($rootScope.gateInfoApi+'lms/authOffline/signCodeResolver?name='+getCookie('name'),{'signCode':signCode});
        }
        function authStatistic(clientTypeCode,startTime,endTime){
           
            return HttpService.post($rootScope.gateInfoApi+'lms/authOffline/statistic?clientTypeCode='+clientTypeCode+'&startTime='+startTime+'&endTime='+endTime+'&name='+getCookie('name'),{});
            
        }
        function authStatisticInit(){
             return HttpService.post($rootScope.gateInfoApi+'lms/authOffline/statisticInit?name='+getCookie('name'),{});
        }
	}
})();
(function() {
    'use strict';
    app.factory('lowQualityService', lowQualityService);
    lowQualityService.$inject = ["HttpService", '$rootScope'];

    function lowQualityService(HttpService, $rootScope) {
        var service = {
            getLowQualityInfo: getLowQualityInfo,
            deleteLowQualityInfo: deleteLowQualityInfo,
            addLowQualityInfo: addLowQualityInfo,
            updateLowQualityInfo: updateLowQualityInfo,
            updateLowQualityStatus:updateLowQualityStatus
        };
        return service;
        /**
         * 分页查询低级质量问题
         */
        function getLowQualityInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'LowQualityProblem/getLowQualityInfo', urlData);
        }
        /**
         * 删除低级质量问题
         */
        function deleteLowQualityInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'LowQualityProblem/deleteLowQualityInfo', urlData);
        }
        /**
         * 新增低级质量问题
         */
        function addLowQualityInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'LowQualityProblem/addLowQualityInfo', urlData);
        }
        /**
         * 修改低级质量问题
         */
        function updateLowQualityInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'LowQualityProblem/updateLowQualityInfo', urlData);
        }
        /**
         * 修改低级质量问题审核状态
         */
        function updateLowQualityStatus(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'LowQualityProblem/updateLowQualityStatus', urlData);
        }
    }
})();
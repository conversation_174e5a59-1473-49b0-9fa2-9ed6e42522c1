/*
* @Author: fubaole
* @Date:   2018-01-03 09:34:08
* @Last Modified by:   fubaole
* @Last Modified time: 2018-03-19 10:12:29
*/
(function() {
  'use strict';
  app.controller("role_Add", ['$rootScope', '$scope', '$state', 'inform', 'SystemService','ConfigService','Trans','LocalCache','AgreeConstant',
    function($rootScope, $scope, $state, inform, SystemService,ConfigService,Trans,LocalCache,AgreeConstant) {
      $scope.limitList = AgreeConstant.limitList; // 正则校验配置
      $scope.role = {dataRange:AgreeConstant.departAndChild};
      $scope.permissionIds = [];
      $scope.grantGroupIds =[];
      $scope.grantRoleIds = [];
      $scope.treeInstance = {};

      // 设置树结构多线
      var Leftsetting = angular.copy(ConfigService.checkboxConfig);
      Leftsetting.callback.onCheck = onCheck; // 点击节点回调

      // getRoleGrantData(); // 获取角色授权信息
      // getGroupGrantData(); // 获取用户组授权信息
      getLoginUserInfo(); // 获取登录用户信息
      getPermissionInfo(); // 获取权限信息

      // 获取当前登录人角色信息
      function getLoginUserInfo(){
        SystemService.getRoleListByUserId(LocalCache.getSession('userId'))
          .then(function(data){
            if(data.code === AgreeConstant.resultCode){
              $scope.role.createUserId = LocalCache.getSession('userId');
              $scope.role.createRoleId = data.result[0].roleId;
            }else{
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 获取角色授权信息
      function getRoleGrantData() {
        SystemService.getRoleByLoginUserIdMap()
          .then(function(data){
            if(data.code===AgreeConstant.resultCode){
              $scope.roleList = data.result;
            }else{
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
        });
      }

      // 获取用户组授权信息
      function getGroupGrantData(){
        SystemService.getGroupByLoginId()
          .then(function(data){
            if(data.code === AgreeConstant.resultCode){
              $scope.groupList = data.result;
            }else{
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
        });
      }

      // 获取选中树节点
      function onCheck(e, treeId, treeNode) {
        $scope.permissionIds=[]; // 存放选中的树节点（修改所在组织机构）
        var treeObj = $.fn.zTree.getZTreeObj("leftTree"),
          nodes = treeObj.getCheckedNodes(true);
        for (var i = 0; i < nodes.length; i++) {
          $scope.permissionIds.push(nodes[i].id);
        }
        console.log($scope.permissionIds ); //获取选中节点的值
      }

      // 获取权限信息
      function getPermissionInfo(){
        SystemService.getAllPermission()
          .then(function(data) {
            if (data.code === AgreeConstant.resultCode) {
              angular.forEach(data.result, function(res, i) {
                var jsonTree = {
                  "id": res.permissionId,
                  "pId": res.parentId,
                  "name": res.permissionName,
                  "open":true
                };
                data.result[i] = angular.extend(jsonTree, res);
              });
              $scope.treeData = data.result;
              $.fn.zTree.init($("#leftTree"), Leftsetting, $scope.treeData);
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // =======================================数据授权================================//
      $scope.orgChecked=[]; // 自定义所在组织机构
      // 右侧树配置
      var RightOrgSetting = angular.copy(ConfigService.checkboxConfig);
      RightOrgSetting.callback.onCheck = onRightCheck; // 点击节点回调
      RightOrgSetting.check.chkboxType = { "Y" : "", "N" : "" };

      $scope.getOrgById = getOrgById;
      $scope.saveOrg = saveOrg;

      // 获取登录人有的组织机构信息
      function onRightCheck(){
        $scope.orgChecked=[]; // 自定义所在组织机构
        var treeObj = $.fn.zTree.getZTreeObj("rightOrgTree"),
          nodes = treeObj.getCheckedNodes(true);
        for (var i = 0; i < nodes.length; i++) {
          $scope.orgChecked.push(nodes[i].id);
        }
      }

      // 根据ID获取组织机构
      function getOrgById(str) {
        if(str===AgreeConstant.selfDirective|| !str){
          $("#setting_orgin").modal({backdrop: 'static', keyboard: false});
          if($scope.orgChecked.length){
            angular.forEach($scope.orgTreeData,function(i){
              if($scope.orgChecked.indexOf(i.orgId)!=-1){
                i.checked = true;
              }
            });
          }else{
            getGrandOrgInfo();
          }
        }else{
          $scope.orgChecked =[];
        }
        console.log($scope.role.dataRange);
      }

      // 关闭组织机构弹框
      function saveOrg() {
        $("#setting_orgin").modal("hide");
        if($scope.orgChecked.length!==0){
          $scope.showOrgTxt = true;
        }else{
          $scope.showOrgTxt = false;
        }
      }

      // 获取组织机构数据
      function getGrandOrgInfo() {
        SystemService.getAllOrg()
          .then(function(data){
            if(data.code === AgreeConstant.resultCode){
              angular.forEach(data.result, function(res, i) {
                var jsonTree = {
                  "id": res.orgId,
                  "pId": res.parentId,
                  "name": res.orgName,
                  "nocheck": res.isOrg ? false:true,
                  "open":true
                };
                data.result[i] = angular.extend(jsonTree, res);
              });
              $scope.orgTreeData = data.result;
              $.fn.zTree.init($("#rightOrgTree"), RightOrgSetting, $scope.orgTreeData);
            }else{
              inform.common(data.message);
            }
          },function(){
            inform.common(Trans("tip.requestError"));
          });
      }

      // ==================================权限授权=====================================//

      $scope.proChecked=[]; // 自定义所在权限
      // 右侧树配置
      var RightProSetting = angular.copy(ConfigService.checkboxConfig);
      RightProSetting.callback.onCheck = onRightProCheck; // 点击节点回调

      $scope.getProById = getProById; // 获取改用户权限信息
      $scope.savePro = savePro; // 关闭弹框
      $scope.onSubmit = onSubmit; // 提交数据

      // 获取登录人有的权限信息
      function onRightProCheck(){
        $scope.proChecked=[]; // 自定义所在权限
        var treeObj = $.fn.zTree.getZTreeObj("rightProTree"),
          nodes = treeObj.getCheckedNodes(true);
        for (var i = 0; i < nodes.length; i++) {
          $scope.proChecked.push(nodes[i].id);
        }
      }

      // 根据ID获取权限
      function getProById() {
        $("#setting_promission").modal({backdrop: 'static', keyboard: false});
        if($scope.proChecked.length){
          angular.forEach($scope.proTreeData,function(i){
            if($scope.proChecked.indexOf(i.permissionId)!==-1){
              i.checked = true;
            }
          });
        }else{
          getGrandPerInfo();
        }
      }

      // 获取权限授权信息
      function getGrandPerInfo() {
        SystemService.getAllPermission()
          .then(function(data){
            if(data.code===AgreeConstant.resultCode){
              angular.forEach(data.result, function(res, i) {
                var jsonTree = {
                  "id": res.permissionId,
                  "pId": res.parentId,
                  "name": res.permissionName,
                  // "nocheck": res.permissionId===0? true:false,
                  "open":true
                };
                data.result[i] = angular.extend(jsonTree, res);
              });
              $scope.proTreeData = data.result;
              $.fn.zTree.init($("#rightProTree"), RightProSetting, $scope.proTreeData);
            }else{
              inform.common(data.message);
            }
          },function(){
            inform.common(Trans("tip.requestError"));
          });
      }

      // 关闭权限弹框
      function savePro() {
        $("#setting_promission").modal("hide");
        if($scope.proChecked.length!==0){
          $scope.showPormissionTxt = true;
        }else{
          $scope.showPormissionTxt = false;
        }
      }

      // 保存角色信息
      function saveRoleData() {
        SystemService.saveRoleAndOtherToRole(
            $scope.role,
            $scope.permissionIds,
            $scope.grantGroupIds,
            $scope.grantRoleIds,
            $scope.orgChecked,
            $scope.proChecked
        )
        .then(function(data) {
          if (data.code === AgreeConstant.resultCode) {
            inform.common(Trans("tip.saveSuccess"));
            $state.go("app.system.role_Management");
          } else {
            inform.common(data.message);
          }
        }, function(error) {
          inform.common(Trans("tip.requestError"));
        });
      }

      // 保存新增角色信息
      function onSubmit() {
        $scope.role.usergroupRange ="";
        $scope.role.roleRange ="";
        $scope.role.permissionRange ="";
        if (!$scope.proChecked.length) {
          inform.common(Trans('common.chooseRolePerm'));
          return false;
        }

        if($scope.proChecked.length){
          $scope.permissionIds = $scope.proChecked;
          $scope.role.permissionRange = AgreeConstant.permissionRange;
        }
        if($scope.role.dataRange=== AgreeConstant.selfDirective){
          if($scope.orgChecked.length===0){
            inform.common(Trans('role.GrantOrgNotSpace'));
            return false;
          }
        }
        saveRoleData();
      }

    }
  ]);
})();
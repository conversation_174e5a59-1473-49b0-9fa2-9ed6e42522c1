(function () {
    app.controller("personalOpsDataController", ['$ocLazyLoad', '$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'personalOpsDataService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http','dataReportFactory',
        function ($ocLazyLoad, $rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, personalOpsDataService, inform, Trans, AgreeConstant, LocalCache, $http,dataReportFactory) {           
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //页面数据
			$scope.formRefer = {};
            $scope.getData = getData;
            $scope.reset = reset;
            $scope.timeSelect=['上月', '上年', '本月', '本年'];
            $scope.initTime = function (m){
                initTime(m)
            };
            initTime('本年');
            //初始化信息
            initData();
            $scope.getOpsData = getOpsData;

            $scope.currentlaunchDataProductLineInputChart = null;
            $scope.currentlaunchDataTeamInputChart = null;
            $scope.currentOpsSupportTypeInputChart = null;
            $scope.currentOpsLongLifecycleInputChart = null;
            $scope.currentOpsIntegrationTypeInputChart = null;
            $scope.currentIntegrationLongLifeInputChart = null;
            
            window.addEventListener("resize", chartResize);
            $scope.$on("$destroy", function() {
                window.removeEventListener('resize', chartResize);
            })
            function chartResize() {
                if ($scope.currentlaunchDataProductLineInputChart) { $scope.currentlaunchDataProductLineInputChart.resize(); }
                if ($scope.currentlaunchDataTeamInputChart) { $scope.currentlaunchDataTeamInputChart.resize(); }
                if ($scope.currentOpsSupportTypeInputChart) { $scope.currentOpsSupportTypeInputChart.resize(); }
                if ($scope.currentOpsLongLifecycleInputChart) { $scope.currentOpsLongLifecycleInputChart.resize(); }
                if ($scope.currentOpsIntegrationTypeInputChart) { $scope.currentOpsIntegrationTypeInputChart.resize(); }
                if ($scope.currentIntegrationLongLifeInputChart) { $scope.currentIntegrationLongLifeInputChart.resize(); }
            }

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
           

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 初始化
             */
            function initData() {
                //获取当前登录者的empId
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ? $scope.sessionEmpId : $stateParams.empId;
                //获取当前登录者的中文名
                $scope.formRefer.employeeName = LocalCache.getSession('employeeName');
                var person = LocalCache.getObject('personDataBoardEmployee');
                if(person.name){
                    $scope.formRefer.employeeName = person.name;
                }
                reset();
                getData(); 

            }

            /**
             * 时间段选择
             */
            function initTime(flag) {
                // 快捷键选项
                $scope.butFlag = flag;
                let date = new Date();
                let y = date.getFullYear(); //当前年份
                let lastMonth = date.getMonth() - 1; //上月
                let lastYear = y -1 ; //上年
                if ('上月' === $scope.butFlag) {
                    // 获取上月天数
                    let daysInMonth = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
                    $scope.formRefer.startTime = inform.format(new Date(y, lastMonth, 1), 'yyyy-MM-01');
                    $scope.formRefer.endTime = inform.format(new Date(y, lastMonth, daysInMonth), 'yyyy-MM-dd');
                }
                if ('上年' === $scope.butFlag) {
                    $scope.formRefer.startTime = inform.format(new Date(lastYear, 0, 1), 'yyyy-01-01');
                    $scope.formRefer.endTime = inform.format(new Date(lastYear, 11, 31), 'yyyy-12-31');
                }
                if ('本年' === $scope.butFlag) {
                    $scope.formRefer.startTime = inform.format(date, 'yyyy-01-01');
                    $scope.formRefer.endTime = inform.format(date, 'yyyy-MM-dd');
                }
                if ('本月' === $scope.butFlag) {
                    $scope.formRefer.startTime = inform.format(date, 'yyyy-MM-01');
                    $scope.formRefer.endTime = inform.format(date, 'yyyy-MM-dd');
                }
            }

            /**
             * 获取运维数据统计信息
             */
            function getData() {
                var urlData = {
                    'operatorName':$scope.formRefer.employeeName,
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd')//结束时间
                }
                $scope.tableData = [];
                personalOpsDataService.getOpsDataStatistics(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.statisticData = data.data;
                       
                            } 
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            function getLaunchDataProductLineChartData () {
                var currentUrlData = {
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                    "operatorName":$scope.formRefer.employeeName
                }
                dataReportFactory.chartHideClear($scope.currentlaunchDataProductLineInputChart);
                dataReportFactory.chartShowLoading($scope.currentlaunchDataProductLineInputChart);
                personalOpsDataService.getLaunchDataProductLine(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.launchDataInputInfo = result.data;
                        dataReportFactory.chartHideLoading($scope.currentlaunchDataProductLineInputChart);
                        dataReportFactory.showBarAndLine($scope.currentlaunchDataProductLineInputChart,$scope.launchDataInputInfo, {
                            title: '上线情况-产品线',
                            xType: 'productLine',
                            yType:'launchNum',
                            yTypeLine:'launchSuccessRate',
                            left:'left',
                            fontSize:'12',
                            israte: true
                        },[
                            '上线次数',
                            '上线成功率'
                        ]);
                    } else {
                        inform.common(result.message);
                        dataReportFactory.chartHideLoading($scope.currentlaunchDataProductLineInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    dataReportFactory.chartHideLoading($scope.currentlaunchDataProductLineInputChart);
                });
            }

            function getLaunchDataTeamChartData () {
                var currentUrlData = {
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                    "operatorName":$scope.formRefer.employeeName
                }
                dataReportFactory.chartHideClear($scope.currentlaunchDataTeamInputChart);
                dataReportFactory.chartShowLoading($scope.currentlaunchDataTeamInputChart);
                personalOpsDataService.getLaunchDataTeam(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.teamlaunchDataInputInfo = result.data;
                        dataReportFactory.chartHideLoading($scope.currentlaunchDataTeamInputChart);
                        dataReportFactory.showBarAndLine($scope.currentlaunchDataTeamInputChart,$scope.teamlaunchDataInputInfo, {
                            title: '上线情况-团队',
                            xType: 'project',
                            yType:'launchNum',
                            yTypeLine:'launchSuccessRate',
                            left:'left',
                            fontSize:'12',
                            israte: true
                        },[
                            '上线次数',
                            '上线成功率'
                        ]);
                    } else {
                        inform.common(result.message);
                        dataReportFactory.chartHideLoading($scope.currentlaunchDataTeamInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    dataReportFactory.chartHideLoading($scope.currentlaunchDataTeamInputChart);
                });
            }

            function getOpsSupportTypeChartData () {
                var currentUrlData = {
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                    "operatorName":$scope.formRefer.employeeName
                }
                dataReportFactory.chartHideClear($scope.currentOpsSupportTypeInputChart);
                dataReportFactory.chartShowLoading($scope.currentOpsSupportTypeInputChart);
                dataReportFactory.chartHideClear($scope.currentOpsLongLifecycleInputChart);
                dataReportFactory.chartShowLoading($scope.currentOpsLongLifecycleInputChart);
                
                personalOpsDataService.getOpsSupportType(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.opsSupportTypeDataInputInfo = result.data;
                        dataReportFactory.chartHideLoading($scope.currentOpsSupportTypeInputChart);
                        dataReportFactory.showPie($scope.currentOpsSupportTypeInputChart,$scope.opsSupportTypeDataInputInfo, {
                            title: '运维支持流程分布',
                            type: 'processType',
                            value:'processNum',
                            fontSize:'12',
                            israte: true
                        });
                        dataReportFactory.chartHideLoading($scope.currentOpsLongLifecycleInputChart);
                        dataReportFactory.showBar($scope.currentOpsLongLifecycleInputChart,$scope.opsSupportTypeDataInputInfo, {
                            title: '生命周期大于48H的流程数',
                            xType: 'processType',
                            yType: 'opsLongLifeCycleNum',
                            fontSize:'12',
                            israte: true
                        });
                    } else {
                        inform.common(result.message);
                        dataReportFactory.chartHideLoading($scope.currentOpsSupportTypeInputChart);
                        dataReportFactory.chartHideLoading($scope.currentOpsLongLifecycleInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    dataReportFactory.chartHideLoading($scope.currentOpsSupportTypeInputChart);
                    dataReportFactory.chartHideLoading($scope.currentOpsLongLifecycleInputChart);
                });
            }

            function getOpsIntegrationTypeChartData () {
                var currentUrlData = {
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),//结束时间
                    "operatorName":$scope.formRefer.employeeName
                }
                dataReportFactory.chartHideClear($scope.currentOpsIntegrationTypeInputChart);
                dataReportFactory.chartShowLoading($scope.currentOpsIntegrationTypeInputChart);
                dataReportFactory.chartHideClear($scope.currentOpsLongLifecycleInputChart);
                dataReportFactory.chartShowLoading($scope.currentOpsLongLifecycleInputChart);
                
                personalOpsDataService.getOpsIntegrationType(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.opsIntegrationTypeDataInputInfo = result.data;
                        dataReportFactory.chartHideLoading($scope.currentOpsIntegrationTypeInputChart);
                        dataReportFactory.showPie($scope.currentOpsIntegrationTypeInputChart,$scope.opsIntegrationTypeDataInputInfo, {
                            title: '持续集成流程分布',
                            type: 'processType',
                            value:'processNum',
                            fontSize:'12',
                            israte: true
                        });
                        dataReportFactory.chartHideLoading($scope.currentIntegrationLongLifeInputChart);
                        dataReportFactory.showBar($scope.currentIntegrationLongLifeInputChart,$scope.opsIntegrationTypeDataInputInfo, {
                            title: '生命周期大于48H的流程数',
                            xType: 'processType',
                            yType: 'integrationLongLifeCycleNum',
                            fontSize:'12',
                            israte: true
                        });
                    } else {
                        inform.common(result.message);
                        dataReportFactory.chartHideLoading($scope.currentOpsIntegrationTypeInputChart);
                        dataReportFactory.chartHideLoading($scope.currentIntegrationLongLifeInputChart);
                        
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    dataReportFactory.chartHideLoading($scope.currentOpsIntegrationTypeInputChart);
                    dataReportFactory.chartHideLoading($scope.currentIntegrationLongLifeInputChart);
                });
            }


			function reset() {
                initTime('本年');
                // $scope.formRefer.endTime = '';
            }

            function getOpsData(){
                //获取统计信息
                getData();
                //获取图表信息
                getChartData();
            }

            $scope.goback = function () {
                $state.go('app.office.personKpi');
            };

            //评审时间 -开始
            $scope.openDateStart = function($event) {
                $scope.butFlag = '';
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = true;
                $scope.formRefer.openedEnd = false;
            };
            //评审时间 -结束
            $scope.openDateEnd = function($event) {
                $scope.butFlag = '';
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = false;
                $scope.formRefer.openedEnd = true;
            };
            
            // 页面加载后触发
            $scope.getData = getChartData;

            function getChartData(){
                getLaunchDataProductLineChartData();
                getLaunchDataTeamChartData();
                getOpsSupportTypeChartData();
                getOpsIntegrationTypeChartData();
            }

            $scope.loadSuccess = function (){
                $ocLazyLoad.load(
                    [
                        'library/component/echarts.min.js'
                    ]).then(function (){
                    $scope.currentlaunchDataProductLineInputChart = echarts.init(document.getElementById("launchDataProductLineInputChart"));
                    $scope.currentlaunchDataTeamInputChart = echarts.init(document.getElementById("launchDataTeamInputChart"));
                    $scope.currentOpsSupportTypeInputChart = echarts.init(document.getElementById("opsSupportTypeInputChart"));
                    $scope.currentOpsLongLifecycleInputChart = echarts.init(document.getElementById("opsLongLifecycleInputChart"));
                    $scope.currentOpsIntegrationTypeInputChart = echarts.init(document.getElementById("opsIntegrationTypeInputChart"));
                    $scope.currentIntegrationLongLifeInputChart = echarts.init(document.getElementById("integrationLongLifeInputChart"));                    
                    getChartData();
                    
                });
            }

            $scope.toEstablishProject = function (){
                $state.go('app.office.companyProWorkTime');
            }
            
            $scope.showOpsSupportList = function () {
                $state.go('app.office.opsSupportProcess');
                $scope.formRefer.operatorName = $scope.formRefer.employeeName;
                LocalCache.setObject('opsSupportProcess_formRefer', $scope.formRefer);
                  
            };

            $scope.showOpsIntegrationList = function () {
                $state.go('app.office.opsIntegrationProcess');
                $scope.formRefer.operatorName = $scope.formRefer.employeeName;
                LocalCache.setObject('opsIntegrationProcess_formRefer', $scope.formRefer);
                  
            };

            $scope.showLaunchList = function () {
                $state.go('app.office.versionReleaseManagement');
                LocalCache.setObject('versionReleaseFormRefer', $scope.formRefer);
                  
            };
            
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

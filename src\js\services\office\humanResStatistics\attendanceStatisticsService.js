
(function() {
    'use strict';
  app.factory('attendanceStatisticsService', attendanceStatisticsService);
  attendanceStatisticsService.$inject=["HttpService",'$rootScope'];

  function attendanceStatisticsService(HttpService,$rootScope){
    var service={
        getAttendanceTotalInfo:getAttendanceTotalInfo,
        getDepartmentWorkStrengthInfo:getDepartmentWorkStrengthInfo,
        getAreaWorkStrengthInfo:getAreaWorkStrengthInfo,
        getAreaWorkStrengthByAreaInfo:getAreaWorkStrengthByAreaInfo,
        getDepartmentAttendanceInfo:getDepartmentAttendanceInfo,
        getGroupInfoList:getGroupInfoList,
        getGroupDetailInfoList:getGroupDetailInfoList,
        getAttendanceDetailInfoList:getAttendanceDetailInfoList,
        getManagerAttendanceInfo:getManagerAttendanceInfo,
        getWorkIntensityInfoGroupByTime:getWorkIntensityInfoGroupByTime
    };
    return service;

    //获取系研考勤汇总数据
    function getAttendanceTotalInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_attend/get_gatherinfo_bydeptlist',urlData);
    }
    //获取部门工作强度统计数据
    function getDepartmentWorkStrengthInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_attend/get_workintensity_groupbydept',urlData);
    }
    //区域工作强度统计--汇总
    function getAreaWorkStrengthInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_attend/get_workintensity_groupbyregion',urlData);
    }
    //区域工作强度统计--分区域
    function getAreaWorkStrengthByAreaInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_attend/get_workintensity4region_groupbydept',urlData);
    }
    //获取部门出勤率/工时利用率统计
    function getDepartmentAttendanceInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_attend/get_attendhouruse_groupbydept',urlData);
    }
    //获取小组考勤数据
    function getGroupInfoList(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_attend/get_attendgroupinfo_team',urlData);
    }
    //获取指定小组考勤明细数据
    function getGroupDetailInfoList(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_attend/get_attendgroupinfo_team_detail',urlData);
    }
    //获取考勤明细
    function getAttendanceDetailInfoList(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_attend/get_empattend_pages',urlData);
    }
    //部门管理职能人员考勤统计
    function getManagerAttendanceInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_attend/get_workintensity_manager',urlData);
    }
    //按时间获取部门工作强度
    function getWorkIntensityInfoGroupByTime(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_attend/get_workintensity_groupbytime',urlData);
    }
  }
})();

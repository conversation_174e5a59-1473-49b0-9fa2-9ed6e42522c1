(function () {
    'use strict';
    app.controller("evaluateModifyOrDetailController", ['$scope', '$state', 'comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant', 'evaluateService', '$stateParams', 'LocalCache', '$modal', '$http',
        function ($scope, $state, comService, $rootScope, inform, Trans, AgreeConstant, evaluateService, $stateParams, LocalCache, $modal, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //代码评价多选项
            const mutiSelect = "PROGRAM_10";
            //代码评价sonar分数
            const sonarScore = "PROGRAM_06";
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            // 路由中传参数，区分是修改还是查看
            $scope.flag = $stateParams.flag;
            //查询条件 从缓存中读取
            $scope.evaluateData = LocalCache.getObject('evaluate_topInfo');
            $scope.scoreChange = scoreChange;
            //多选框选中值
            $scope.check={};
            //初始化标志
            $scope.checkFlag = true;
            // 保存得分（下载模板时会用到）
            var oldScore = 0;
            // 查询数据
            getData();
            // 评价code列表
            var evaluateCodeValueList = [{
                type: '0',
                code: 'CUSTOMER',
                kpiCode: 'KPI_0_02',
                subKpiCode: 'KPI_0_02_02'
            }, {
                type: '0',
                code: 'PROGRAM',
                kpiCode: 'KPI_0_03',
                subKpiCode: 'KPI_0_03_03'
            }, {
                type: '0',
                code: 'COURSE',
                kpiCode: 'KPI_0_03',
                subKpiCode: 'KPI_0_03_02'
            }, {
                type: '0',
                code: 'VALUES',
                kpiCode: 'KPI_0_05',
                subKpiCode: 'KPI_0_05_01'
            }, {
                type: '1',
                code: 'TEAM_ABILITY',
                kpiCode: 'KPI_1_01',
                subKpiCode: 'KPI_1_01_02'
            }, {
                type: '1',
                code: 'CUSTOMER',
                kpiCode: 'KPI_0_02',
                subKpiCode: 'KPI_0_02_02'
            }, {
                type: '1',
                code: 'PROGRAM',
                kpiCode: 'KPI_1_03',
                subKpiCode: 'KPI_1_03_04'
            }, {
                type: '1',
                code: 'COURSE',
                kpiCode: 'KPI_1_03',
                subKpiCode: 'KPI_1_03_03'
            }, {
                type: '1',
                code: 'VALUES',
                kpiCode: 'KPI_1_04',
                subKpiCode: 'KPI_1_04_01'
            }/*, {
            type: '1',
            code: 'BUG_ABILITY',
            kpiCode: 'KPI_1_03',
            subKpiCode: 'KPI_1_03_02'
        }*/];
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 获取kpi查询信息
             */
            function getData() {
                var urlData = {
                    'id': $scope.evaluateData.id
                };
                evaluateService.getDetailData(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        //信息
                        $scope.info = data.data;

                        // 过程质量评价获取参考信息
                        if ($scope.evaluateData.evaluateEnumCode === 'COURSE') {
                            courseFun();
                        } else if($scope.evaluateData.evaluateEnumCode === 'TEAM_ABILITY' && $scope.flag === 'detail') {
                            $scope.score = data.data.score;
                        }else {
                            scoreChange();
                        }
                        // 评价得分(下载时用到)
                        oldScore = $scope.score;
                        // 代码评价获取sonar分数信息
                        if ($scope.evaluateData.evaluateEnumCode === 'PROGRAM') {
                            codeSonarInfo();
                        }
                    } else {
                        inform.common(data.message);
                    }
                }, function () {
                    inform.common(Trans("tip.requestError"));
                });

            }
            /*
            * 过程质量评价函数
            * */
            function courseFun() {
                var urlData0 = {
                    'projectType': $scope.evaluateData.type,
                    'projectId': $scope.evaluateData.projectId,
                    'beginTimeTeam': $scope.evaluateData.beginTimeTeam,
                    'endTimeTeam': $scope.evaluateData.endTimeTeam
                };
                evaluateService.referenceData(urlData0).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        var referenceData = data.data;
                        // 默认数据都为暂无，有数据再修改
                        $scope.referenceData = [{
                            typeValue: '暂无',
                            detail: '暂无基础数据'
                        }, {
                            typeValue: '暂无',
                            detail: '暂无基础数据'
                        }, {
                            typeValue: '暂无',
                            detail: '暂无基础数据'
                        }, {
                            typeValue: '暂无',
                            detail: '暂无基础数据'
                        }];
                        // 有数据就修改默认列表
                        $scope.referenceData.forEach(function (item, index) {
                            var element = referenceData.find(function (ele) {
                                return ele.type === '0' + (index + 1).toString();
                            });
                            if (element) {
                                item.typeValue = element.typeValue;
                                item.detail = element.detail;
                            }
                        });
                        // 计算过程符合度评分和度量元数据指标
                        countValueFun();
                        // 计算评价得分
                        scoreChange();
                    } else {
                        inform.common(data.message);
                    }
                }, function () {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /*
            * 代码质量评价函数
            * */
            function codeSonarInfo() {
                var urlData0 = {
                    'projectId': $scope.evaluateData.projectId
                };
                evaluateService.getCodeSonarInfo(urlData0).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.codeSonarData = data.data;
                    } else {
                        inform.common(data.message);
                    }
                }, function () {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 提交评价信息
             *
             * */
            $scope.submitData = function () {

                //后端返回值
                angular.forEach($scope.info.evaluateDetailList, function (item) {
                    if (item.categoryCode === mutiSelect) {
                        item.categoryValue = JSON.stringify($scope.check);
                        item.prompting = "";
                    }
                });
                // 找到评价code项（传参）
                var kpiCodeObj = evaluateCodeValueList.find(function (item) {
                    return item.type === $scope.evaluateData.type && item.code === $scope.evaluateData.evaluateEnumCode;
                });
                // 没有评价code提示提交失败（正常逻辑不会发生，防止数据错误）
                if (!kpiCodeObj || !Object.getOwnPropertyNames(kpiCodeObj).length) {
                    inform.common(Trans("提交失败！"));
                    return;
                }
                // 评价项有提示信息说明评价项格式不正确
                if ($scope.info.evaluateDetailList.find(function (item) {
                    return item.prompting !== '';
                })) {
                    inform.common(Trans("提交信息有误，请检查所填信息！"));
                    return;
                }
                // 评价项为空时提示信息
                if ($scope.info.evaluateDetailList.find(function (item) {

                    if (item.categoryCode === mutiSelect || item.categoryCode ==='VALUES_05') {
                        return false;
                    } else {
                        return item.categoryValue ==='' || item.categoryValue === null;
                    }

                })) {
                    inform.common(Trans("评价项都为必填项，请填写完整"));
                    return;
                }
                if(!$scope.info.remark){
                    inform.common(Trans("评语为必填项，请填写完整"));
                    return;
                }
                var urlData = {
                    'evaluateDetailList': $scope.info.evaluateDetailList,
                    'projectType': $scope.evaluateData.type,
                    'score': $scope.score,
                    'remark': $scope.info.remark,
                    'remark1': $scope.info.remark1,
                    'remark2': $scope.info.remark2,
                    'kpiId': $scope.evaluateData.kpiId,
                    'id': $scope.evaluateData.id,
                    'kpiCode': kpiCodeObj.kpiCode,
                    'subKpiCode': kpiCodeObj.subKpiCode
                };
                evaluateService.updateEvaluateData(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        inform.common(data.message);
                        // 跳转页面
                        $state.go("app.office.evaluateManagement");
                    } else {
                        inform.common(data.message);
                    }
                }, function () {
                    inform.common(Trans("tip.requestError"));
                });
            };

            /**
             * 评价项成绩发生改变，成绩重新计算
             *
             * */
            function scoreChange() {
                // 没有$scope.info说明异步调用还未完成，不需要执行此函数
                if ($scope.info) {
                    $scope.info.evaluateDetailList.forEach(function (item) {
                        // 如果格式不正确显示提示消息
                        if ((Number(item.categoryValue) >= 0 && Number(item.categoryValue) <= 100) || item.categoryValue === '暂无') {
                            item.prompting = '';
                        } else {
                            item.prompting = '输入内容0-100,且只能为整数。';
                        }
                    });
                    // 过程质量评价得分规则不是平均分，单独计算
                    if($scope.evaluateData.evaluateEnumLabel === '过程质量评价') {
                        var score3 = $scope.info.evaluateDetailList[2].categoryValue === null? 0 : $scope.info.evaluateDetailList[2].categoryValue;
                        $scope.score = Number(score3).toFixed(2);
                    }
                    //客户评价不需要平均
                    else if($scope.evaluateData.evaluateEnumLabel === '客户评价' || $scope.evaluateData.evaluateEnumLabel === '团队能力评价'
                        || $scope.evaluateData.evaluateEnumLabel === '部门贡献评价') {
                        $scope.score = $scope.info.evaluateDetailList.map(function (item) {
                            return Number(item.categoryValue);
                        }).reduce(function (a, b) {
                            a += b;
                            return a;
                        }, 0);

                    }
                    else if ($scope.evaluateData.evaluateEnumLabel === '代码质量评价') {
                        var value;
                        angular.forEach($scope.info.evaluateDetailList, function (item) {
                            if (item.categoryCode === mutiSelect) {
                                value = item.categoryValue;
                            }
                        })
                        //多选框分数
                        var programChecks = document.getElementsByName("programCheck");
                        //初始化或刷新页面时直接使用后台分数，将多选框赋值
                        if ($scope.checkFlag && value !== undefined && value !== null) {
                            angular.forEach($scope.info.evaluateDetailList, function (item) {

                                if (item.categoryCode === mutiSelect) {
                                    $scope.check = JSON.parse(item.categoryValue);

                                }
                            });
                            $scope.score = $scope.info.score;
                            $scope.checkFlag = false;
                        } else {
                            //计算除了扣分项
                            var score = $scope.info.evaluateDetailList.map(function (item) {
                                //sonar分数需要*0.25
                                if (item.categoryCode === sonarScore) {
                                    return Number(item.categoryValue) * 0.25;
                                } else if(item.categoryCode === mutiSelect) {
                                    return 0;
                                } else {
                                    return Number(item.categoryValue);
                                }
                            }).reduce(function (a, b) {
                                a += b;
                                return a;
                            }, 0);
                            //计算扣分项
                            angular.forEach(programChecks, function(item, i) {

                                if (programChecks[i].checked) {
                                    score = score + Number(programChecks[i].value);
                                }
                            });
                            $scope.score = score < 0 ? 0 : score;
                        }
                    }
                    else {
                        // 计算平均分
                        var ele = $scope.info.evaluateDetailList.map(function (item) {
                            return Number(item.categoryValue);
                        }).reduce(function (a, b) {
                            a += b;
                            return a;
                        }, 0);
                        $scope.score = (ele / $scope.info.evaluateDetailList.length).toFixed(2);
                    }
                }
            }
            /*
            * 导出评价模板
            * */
            $scope.toExcel = function() {
                //页面从详情信息跳转回来时，保留查询条件
                var urlData = {
                    'id': $scope.evaluateData.id,
                    'projectName': $scope.evaluateData.projectName,
                    'evaluatePersonName': $scope.evaluateData.evaluatePersonName,
                    'evaluateEnumLabel': $scope.evaluateData.evaluateEnumLabel,
                    'score': oldScore,
                    'projectManager': $scope.evaluateData.projectManager
                };
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function() {
                            return "确定要下载吗！";
                        }
                    }
                });
                modalInstance.result.then(function() {
                    //开启遮罩层
                    inform.showLayer("下载中。。。。。。");
                    $http.post(
                        $rootScope.getWaySystemApi+'evaluatePerson/toExcel',
                        urlData,
                        {headers: {
                                'Content-Type': 'application/json',
                                'Authorization':'Bearer ' + LocalCache.getSession("token")||''
                            },
                            responseType: 'arraybuffer'//防止中文乱码
                        }
                    ).success(function(data){
                        //如果是IE浏览器
                        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                            var csvData = new Blob([data], {type:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
                            window.navigator.msSaveOrOpenBlob(csvData,$scope.evaluateData.evaluateEnumLabel + '模板.xlsx');
                        }
                        //google或者火狐浏览器
                        else{
                            var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
                            var objectUrl = URL.createObjectURL(blob);
                            var aForExcel = $("<a download='" + $scope.evaluateData.evaluateEnumLabel + "模板.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href",objectUrl);
                            $("body").append(aForExcel);
                            $(".forExcel").click();
                            aForExcel.remove();
                        }
                        // 关闭遮罩层
                        inform.closeLayer();
                        inform.common("下载成功!");
                    });
                });
            };
            /*
            * 计算过程符合度评分和度量元数据指标
            * */
            function countValueFun() {
                // 计算过程符合度评分
                if ($scope.referenceData[0].typeValue !== '暂无') {
                    // 过程符合度
                    var countBase =  Number.parseInt($scope.referenceData[0].typeValue);
                    // 过程符合度评分
                    var countValue = 0;
                    /**
                     * 80%得80分，超过80%，每增多1%，加1分，最高得100分；
                     * 低于80%，每低1%减4分，直至75%得60分；
                     * 低于75%，每低1%减5分，最低得0分。
                     * */
                    if (countBase < 80 && countBase >= 75) {
                        countValue = 80 - (80 - countBase) * 4;
                    } else if(countBase < 75) {
                        countValue = 60 - (75 - countBase) * 5;
                        if (countValue < 0) {
                            countValue = 0;
                        }
                    } else {
                        countValue = countBase;
                    }
                    $scope.info.evaluateDetailList[0].categoryValue = countValue;
                } else {
                    $scope.info.evaluateDetailList[0].categoryValue = '暂无';
                }
                // 计算度量元数据指标
                if ($scope.referenceData[1].typeValue !== '暂无'
                    && $scope.referenceData[2].typeValue !== '暂无' && $scope.referenceData[3].typeValue !== '暂无') {
                    // 度量元数据指标
                    var countBase1 =  Number.parseInt($scope.referenceData[1].typeValue);
                    var countBase2 =  Number.parseInt($scope.referenceData[2].typeValue);
                    var countBase3 =  Number.parseInt($scope.referenceData[3].typeValue);
                    // 度量元数据指标评分
                    var countValue1 = 0;
                    var countValue2 = 0;
                    var countValue3 = 0;
                    /**
                     * 计算公式：开发过程返工率得分*40%+一次评审通过率得分*40%+特殊放行率得分*20%
                     * （1）开发过程返工比率得分
                     * 10%得分80分，低于10%，比率每减少1%，分数增加2分，最高得100分；
                     * 高于10%，比率每增多1%减5分，最低得0分。
                     * （2）一次评审通过率得分
                     * 80%得80分，超过80%，每超过1%，加1分，最高得100分；
                     * 低于80%，比率每减少1%减5分，最低得0分。
                     * （3）特殊放行率得分
                     * 5%得分80分，低于5%，比率每减少1%，分数增加4分，最高得100分；
                     * 高于5%，比率每增多1%减5分，最低得0分。
                     * */
                    // 开发过程返工比率得分
                    if (countBase1 <= 10) {
                        countValue1 = 80 + (10 - countBase1) * 2;
                    } else {
                        countValue1 = 80 - (countBase1 - 10) * 5;
                        if (countValue1 < 0) {
                            countValue1 = 0;
                        }
                    }
                    // 一次评审通过率得分
                    if (countBase2 < 80) {
                        countValue2 = 80 - (80 - countBase2) * 5;
                        if (countValue2 < 0) {
                            countValue2 = 0;
                        }
                    } else {
                        countValue2 = countBase2;
                    }
                    // 特殊放行率得分
                    if (countBase3 <= 5) {
                        countValue3 = 80 + (5 - countBase3) * 4;
                    } else {
                        countValue3 = 80 - (countBase3 - 5) * 5;
                        if (countValue3 < 0) {
                            countValue3 = 0;
                        }
                    }
                    // 度量数据指标
                    $scope.info.evaluateDetailList[1].categoryValue = Number.parseInt((countValue1 * 0.4) + (countValue2 * 0.4) + (countValue3 * 0.2));
                } else {
                    $scope.info.evaluateDetailList[1].categoryValue = '暂无';
                }
            }

            $scope.gotoTeamBugDetail = function(){
                var data = {
                    "cname":$scope.evaluateData.projectName,
                    "id":$scope.evaluateData.projectId
                };
                LocalCache.setObject('project_detail',{
                     projectInfoParam:JSON.stringify(data)
                });
                	//将项目名称写入同行评审缓存
                    LocalCache.setObject('formRefer_rp_0004',{
                         name:$scope.evaluateData.projectName,
                         type:'entrance'
                    });
                    $state.go('app.office.teamEntranceDetail',{
                    	type:3
                    });
            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();

(function() {
    'use strict';
  app.factory('staffPerformanceService', staffPerformanceService);
  staffPerformanceService.$inject=["HttpService",'$rootScope'];

  function staffPerformanceService(HttpService,$rootScope){
    
    var service={
        selectPerformanceByParam:selectPerformanceByParam,
        addPerformanceByParam:addPerformanceByParam,
        updatePerformanceByParam:updatePerformanceByParam,
        deletePerformanceByIds:deletePerformanceByIds,
        getStaffAllName : getStaffAllName,
        getStaffId:getStaffId,
        /******************** 试用期 ******************************/
        selectProbationPeriodByParam:selectProbationPeriodByParam,
        addStaffProbationPeriodInfo:addStaffProbationPeriodInfo,
        updateStaffProbationPeriodInfo:updateStaffProbationPeriodInfo
    };
    return service;
  
    /************************************************************************************************/
    /**
     * 员工绩效信息
     */
    function selectPerformanceByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffPerform/selectPerformanceByParam', urlData);
    }

    function addPerformanceByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffPerform/insertPerformanceByParam', urlData);
    }
  

    function updatePerformanceByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffPerform/updatePerformanceByParam', urlData);
    }

    function deletePerformanceByIds(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'staffPerform/deletePerformanceByParam', urlData);
    }

    function getStaffAllName(){
        return HttpService.post($rootScope.getWaySystemApi + 'staffInfo/getAllStaffName');
    }

    function getStaffId(employeeName){
        return HttpService.post($rootScope.getWaySystemApi + 'staffInfo/selectStaffId',employeeName);
    }

    /*************************  试用期考核  ************************/
    function selectProbationPeriodByParam(param){
        return HttpService.post($rootScope.getWaySystemApi + 'staffProbationPeriodInfo/selectStaffProbationPeriodInfo',param);
    }

    function addStaffProbationPeriodInfo(param){
        return HttpService.post($rootScope.getWaySystemApi + 'staffProbationPeriodInfo/insertStaffProbationPeriodInfo',param);
    }

    function updateStaffProbationPeriodInfo(param){
        return HttpService.post($rootScope.getWaySystemApi + 'staffProbationPeriodInfo/updateStaffProbationPeriodInfo',param);
    }



  }
})();
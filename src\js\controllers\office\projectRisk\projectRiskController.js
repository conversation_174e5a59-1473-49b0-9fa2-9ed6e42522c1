//# sourceURL=js/controllers/office/projectRisk/projectRiskController.js
(function() {
    app.controller("projectRiskController", ['projectRiskService', '$state', 'comService', '$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$stateParams', '$modal', '$http', '$state',
        function(projectRiskService, state, comService, $rootScope, $scope, inform, Trans, AgreeConstant, LocalCache, $stateParams, $modal, $http, $state) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $(window).resize(setDivHeight);//设置列表的高度,窗体大小变化时重新计算高度
            initPages();//初始化页面信息
            $scope.pages = {
                pageNum: '', //分页页数
                size: '100', //分页每页大小
                total: '' //数据总数
            };
            //风险内容下载标注拉框数据源
            $scope.riskContentDownloadFlagSelect = [{
                value: '0',
                label: '下载'
            }, {
                value: '1',
                label: '不下载'
            }];
            $scope.riskContentDownloadFlagMap = {
                "0":'下载',
                "1":'不下载'
            };
            //获取数据
            getData(1);
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 130);
                $("#divTBDis1").height(divHeight);
                $("#subDivTBDis1").height(divHeight - 50);
            }

            function initPages() {
                //获取员工信息
                $scope.employeesList = [];
                comService.getEmployeesName().then(function(data) {
                    $scope.employeeList = data.data;
                });
                //风险等级
                $scope.riskPriorityList = [];
                comService.getParamList('RISK_PRIORITY', 'RISK_PRIORITY').then(function(data) {
                    if (data.data) {
                        $scope.riskPriorityList = data.data;
                    }
                });
                //风险状态
                $scope.riskStateList = [];
                comService.getParamList('NEED_SUPPORT', 'NEED_SUPPORT').then(function(data) {
                    if (data.data) {
                        $scope.riskStateList = data.data;
                    }
                });
            }
            /**
             * 获取项目
             */
            function getData(pages) {
                var urlData = {
                    'projectId': $stateParams.projectId,
                    'page': pages,
                    'pageSize': $scope.pages.size
                };
                projectRiskService.getProjectRiskInfo(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        //项目详情
                        $scope.jsonData = data.data.list;
                        // 分页信息设置
                        $scope.pages.total = data.data.total;
                        $scope.pages.star = data.data.startRow;
                        $scope.pages.end = data.data.endRow;
                        $scope.pages.pageNum = data.data.pageNum;
                        setDivHeight();
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 判断是新增还是修改
             * @param item 不存在则新增，存在则修改
             */
            $scope.popModal = function(item) {
                if (!item) {
                    //状态下拉框初始值
                    $scope.addFlag = '0';
                    $scope.spec = {
                        riskState: '0001',
                        riskPriority: '0001',
                        riskContentDownloadFlag:'0'
                    };
                } else {
                    $scope.addFlag = '1';
                    getUpdateInfo(item.id);
                }
            };
            /**
             * 添加信息
             */
            $scope.addInfo = function() {
                var urlData = {
                    'projectId': $stateParams.projectId,
                    'riskContent': $scope.spec.riskContent,
                    'riskSubject': $scope.spec.riskSubject,
                    'riskState': $scope.spec.riskState,
                    'riskContentDownloadFlag':$scope.spec.riskContentDownloadFlag,//风险内容下载标志
                    'riskIdentifyDate': inform.format($scope.spec.riskIdentifyDate, 'yyyy-MM-dd'),
                    'riskPriority': $scope.spec.riskPriority,
                    'riskInfuluence': $scope.spec.riskInfuluence,
                    'chargePerson': $scope.spec.chargePerson,
                    'countermeasure': $scope.spec.countermeasure,
                    'requestCompleTime': inform.format($scope.spec.requestCompleTime, 'yyyy-MM-dd')
                };
                projectRiskService.addProjectRiskInfo(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        common(data.message, function() {
                            getData(AgreeConstant.pageNum);
                            $("#add_modal").modal("hide");
                        });
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            };
            /**
             * 修改查询项目信息
             */
            function getUpdateInfo(item) {
                var urlData = {
                    'id': item,
                    'projectId': $stateParams.projectId //项目名称
                };
                projectRiskService.getUpdateInfo(urlData).then(function(data) {
                    $scope.changeParam = data.data[0];
                }, function() {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 修改信息
             */
            $scope.updateInfo = function() {
                var urlData = {
                    'id': $scope.changeParam.id,
                    'riskContent': $scope.changeParam.riskContent,
                    'riskSubject': $scope.changeParam.riskSubject,
                    'riskState': $scope.changeParam.riskState,
                    'riskContentDownloadFlag':$scope.changeParam.riskContentDownloadFlag,//风险内容下载标志
                    'riskIdentifyDate': inform.format($scope.changeParam.riskIdentifyDate, 'yyyy-MM-dd'),
                    'riskPriority': $scope.changeParam.riskPriority,
                    'riskInfuluence': $scope.changeParam.riskInfuluence,
                    'chargePerson': $scope.changeParam.chargePerson,
                    'countermeasure': $scope.changeParam.countermeasure,
                    'requestCompleTime': inform.format($scope.changeParam.requestCompleTime, 'yyyy-MM-dd')
                };
                projectRiskService.updateProjectRiskInfo(urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        common(data.message, function() {
                            getData(1);
                            $("#edit_modal").modal("hide");
                        });
                    } else {
                        inform.common(data.message);
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            };
            /**
             * 删除数据
             */
            $scope.remove = function(item) {
                //删除弹出框
                inform.modalInstance(Trans("common.deleteTip")).result.then(function() {
                    projectRiskService.deleteProjectRiskInfo(item).then(function(data) {
                        if (data.code === "0000") {
                            inform.common(Trans("tip.delSuccess"));
                            getData(AgreeConstant.pageNum);
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
                });
            };
            /**
             * 下拉时间框
             */
            $scope.identifyTime = {};
            $scope.requestTime = {};
            //风险识别
            $scope.identifyTime = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.identifyTime.identifyTime = true;
                $scope.requestTime.requestTime = false;
            };
            // 要求完成
            $scope.requestTime = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.identifyTime.identifyTime = false;
                $scope.requestTime.requestTime = true;
            };
            /**
             * 提示信息
             * @param str  提示信息
             * @param func 确定时执行的函数
             */
            function common(str, func) {
                layer.confirm(str, {
                    title: false,
                    btn: ['确定']
                }, function(result) {
                    layer.close(result);
                    if (typeof(func) !== 'undefined') {
                        func();
                    }
                });
            }
            //风险状态修改，联动修改风险内容下载标志
            $scope.changeRiskState = function(){
                if($scope.addFlag==='0'){
                    if($scope.spec.riskState === '0004'){
                        $scope.spec.riskContentDownloadFlag = '1';
                    }
                }else {
                    if($scope.changeParam.riskState === '0004'){
                        $scope.changeParam.riskContentDownloadFlag = '1';
                    }
                }
            }
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        }
    ]);
})();
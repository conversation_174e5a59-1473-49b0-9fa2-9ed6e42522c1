(function () {
    'use strict';
    app.factory('jenkinsJobService', jenkinsJobService);
    jenkinsJobService.$inject = ["HttpService", '$rootScope'];

    function jenkinsJobService(HttpService, $rootScope) {
        var service = {
            selectData:selectData,
            updateInfo:updateInfo,
            selectOne:selectOne
        };
        return service;
        /**
         * 分页获取Jenkins作业信息
         */
        function selectData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'jenkinsJob/selectData', urlData);
        }
        /**
         * 保存Jenkins作业信息
         */
        function updateInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'jenkinsJob/updateInfo', urlData);
        }
        /*
        * 获取Jenkins作业信息
        * */
        function selectOne(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'jenkinsJob/selectOne', urlData);
        }
    }
})();
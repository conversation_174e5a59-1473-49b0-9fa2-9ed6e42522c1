(function () {
    'use strict';
    app.controller('staffInfoUpdateController', [
        'comService',
        '$rootScope',
        '$stateParams',
        '$scope',
        'staffInfoService',
        '$modal',
        'inform',
        'Trans',
        'AgreeConstant',
        '$state',
        'LocalCache',
        '$http',
        function (
            comService,
            $rootScope,
            $stateParams,
            $scope,
            staffInfoService,
            $modal,
            inform,
            Trans,
            AgreeConstant,
            $state,
            LocalCache,
            $http
        ) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            getPicture(); //获取图片

            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.datepicker = {
                currentDate: new Date(),
            };
            $scope.titleList = [];
            $scope.titleLevelList = AgreeConstant.titleLevelList;
            $scope.personnelTypeList = AgreeConstant.personnelTypeList;

            $scope.flagAuth = false; //权限标识

            //设置列表的高度
            setDivHeight();
            getPersonInfo(); //根据id获取个人信息
            initPrimaryDeptList(); //初始化根据用户名获取一级部门列表
            getTitleList(); //职称列表
            getAreaList(); //地区
            getEthnicGroupList(); //获取系研族群列表
            getStateList(); //工作状态
            getLeavingReasonList();
            getDepartmentCode(); //获取部门权限码

            //获取缓存
            $scope.searchObject = LocalCache.getObject('staffInfo_searchObject');
            $scope.stateLabel = '离职';
            if ($scope.searchObject.state === '04') {
                $scope.stateLabel = '调出';
            }
            $scope.show = $scope.searchObject.state === '05' || $scope.searchObject.state === '04';
            $scope.flag = false;
            var fileTmp = '';
            getStaffGroup(); //获取小组列表

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */

            //获取获得方式列表
            $scope.titleGetTypeList = AgreeConstant.titleGetTypeList;
            //获取图片
            function getPicture() {
                staffInfoService.getPicture($stateParams.employeeId).then(function (data) {
                    if (data.data == null || data.data === '') {
                        $('#img_picture_del').css('display', 'none');
                        $('#img_picture_view').css('display', 'none');
                        $('#prompt3').css('display', 'block');
                    } else {
                        var img_picture_view = $('#img_picture_view');
                        img_picture_view.css('display', 'block');
                        document.getElementById('img_picture_view').src = 'data:image/*;Base64,' + data.data;
                        $('#prompt3').css('display', 'none');
                        $('#img_picture_del').css('display', 'block');
                        img_picture_view.css('height', '128px');
                        img_picture_view.css('width', '104px');
                        fileTmp = data.data;
                    }
                });
            }

            /**
             * 初始化二级部门列表
             */
            function initSecDeptList() {
                //获取二级部门
                $scope.deptList = [];
                comService.getOrgChildren($scope.updateParam.primaryDept).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.deptList = data.data;
                    }
                });
            }
            //初始化细分产品线
            function initSoftwareProductLineList() {
                $scope.subdivisionProductLine = [];
                comService.getParamList('细分产品线', $scope.updateParam.softwareProductLineName).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.subdivisionProductLine = data.data;
                    }
                });
            }
            //初始化岗位
            function initTitleList() {
                $scope.staffTitleList = [];
                comService.getParamList('STAFF_INFO_TITLE', 'NEW').then(function (data) {
                    $scope.staffTitleList = data.data;
                });
            }
            //初始化专业模块
            function initProfessionalModuleList() {
                $scope.professionalModuleList = [];
                comService.getParamList('STAFF_PROFESSIONAL_MODULE', 'NEW').then(function (data) {
                    $scope.professionalModuleList = data.data;
                });
            }
            /**
             * 获取按钮权限
             */
            function getButtonPermission() {
                var buttons = {
                    'Button-staffInfoManagementController-ins': 'specailUpdateType',
                };
                var urlData = {
                    userId: LocalCache.getSession('userId'),
                    parentPermission: 'ButtonCodeStaffInfoManagement',
                    buttons: buttons,
                };
                comService.getButtonPermission(urlData, $scope);
            }

            //获取部门权限码(先判断是否为中心办，如果不是，在判断是否为白名单的人员)
            function getDepartmentCode() {
                //判断是否为中心办,返回01则为全部权限，否则验证白名单
                comService.isCenterOffice().then(function (res) {
                    if (res.code === '0000' && res.data.code === '01') {
                        //01全部权限
                        $scope.flag = true;
                        return;
                    } else {
                        comService.validAuthentication('0001', '2').then(function (result) {
                            if (result.code === '0000') {
                                if (result.data.code === '00') {
                                    $state.go('app.office.unAuthority');
                                    return;
                                }
                                if (result.data.code === '01') {
                                    $scope.flag = true;

                                    return;
                                }
                                $scope.flagAuth = true;
                                $scope.searchObject.primaryDept = res.data.primaryDeptCode;
                                $('#primaryDeptName').attr('disabled', 'disabled');

                                if (res.data.departmentCode) {
                                    $scope.searchObject.department = res.data.departmentCode;
                                    $('#departmentName').attr('disabled', 'disabled');
                                }
                                getButtonPermission();
                            }
                        });
                    }
                });
            }

            //查询小组信息列表
            function getStaffGroup() {
                $scope.staffGroupList = [];
                staffInfoService.selectGroupById().then(function (data) {
                    $scope.staffGroupList = angular.fromJson(data.data);
                });
            }

            //删除该员工的照片
            $scope.img_del = function () {
                inform.modalInstance('确定要删除照片吗？').result.then(function () {
                    staffInfoService.delPicByParam($stateParams.id).then(function (result) {
                        if (result.code === '0000') {
                            inform.common(result.message);
                            getPicture();
                            $('#img_picture_del').css('display', 'none');
                            document.getElementById('img_picture_view').src = '#';
                            var obj = document.getElementById('myFile');
                            obj.outerHTML = obj.outerHTML;
                            fileTmp = '';
                        } else {
                            inform.common(result.message);
                        }
                    });
                });
            };

            /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function (data) {
                    if (data.data) {
                        $scope.primaryDeptList = data.data;
                    }
                });
                //获取软件产品线
                $scope.softwareProductLineList = [];
                comService.queryEffectiveParam('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.softwareProductLineList = data.data;
                    }
                });
                //获取归属模块
                $scope.belongModuleList = [];
                comService.queryEffectiveParam('BELONG_MODULE', 'BELONG_MODULE').then(function (data) {
                    if (data.data) {
                        $scope.belongModuleList = data.data;
                    }
                });
                //获取人员分类
                $scope.employeeTypeList = [
                    { paramCode: '直接开发人员', paramValue: '直接开发人员' },
                    { paramCode: '开发共用人员', paramValue: '开发共用人员' },
                    { paramCode: '项目管理人员', paramValue: '项目管理人员' },
                    { paramCode: '开发试制人员', paramValue: '开发试制人员' },
                    { paramCode: '管理支撑人员', paramValue: '管理支撑人员' },
                ];
            }

            //修改一级部门，二级部门进行联动
            $scope.changeDept = function () {
                setDept();
                getProfessionalModuleList();
                getTitleList();
                getStaffTitleList();
            };
            //获取二级部门
            function setDept() {
                $scope.deptList = [];
                comService.getOrgChildren($scope.updateParam.primaryDept).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.deptList = data.data;
                        //清空二级部门
                        $scope.updateParam.department = '';
                    }
                });
            }

            //获取细分产品线
            $scope.changeSoftwareProductLine = function () {
                $scope.subdivisionProductLine = [];
                // 根据产品线code查找对应的产品线名称
                const softwareProductLineName =
                    $scope.softwareProductLineList.find((i) => i.paramCode === $scope.updateParam.softwareProductLine)
                        ?.paramValue || '';

                comService.getParamList('细分产品线', softwareProductLineName).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.subdivisionProductLine = data.data;
                        //清空细分产品线
                        $scope.updateParam.subdivisionProductLine = '';
                    }
                });
            };
            //根据id获取该员工的信息
            function getPersonInfo() {
                staffInfoService.getPersonInfo($stateParams.employeeId).then(function (data) {
                    $scope.updateParam = data.data;
                    initSecDeptList();
                    initSoftwareProductLineList();
                    initTitleList();
                    initProfessionalModuleList();
                    //获取序列下拉框
                    $scope.sequenceChange($scope.updateParam.ethnicGroup);
                });
            }

            //获取地区
            function getAreaList() {
                $scope.areaList = [];
                comService.getParamList('AREA_TYPE', 'AREA_TYPE').then(function (data) {
                    $scope.areaList = data.data;
                });
            }

            //获取系研员工岗位信息
            function getStaffTitleList() {
                //获取员工岗位信息
                $scope.staffTitleList = [];
                comService.getParamList('STAFF_INFO_TITLE', 'NEW').then(function (data) {
                    $scope.staffTitleList = data.data;
                    //清空岗位
                    $scope.updateParam.post = '';
                });
            }

            //获取系研专业模块列表
            function getProfessionalModuleList() {
                //获取系研职称信息
                $scope.professionalModuleList = [];
                comService.getParamList('STAFF_PROFESSIONAL_MODULE', 'NEW').then(function (data) {
                    $scope.professionalModuleList = data.data;
                    //清空专业模块
                    $scope.updateParam.professionalModule = '';
                });
            }

            //获取员工离职原因列表
            function getLeavingReasonList() {
                //获取系研职称信息
                $scope.leavingReasonList = [];
                comService
                    .getParamList('CLASSIFICATION_REASONS_FOR_LEAVING', 'CLASSIFICATION_REASONS_FOR_LEAVING')
                    .then(function (data) {
                        $scope.leavingReasonList = data.data;
                    });
            }

            //获取系研职称信息
            function getTitleList() {
                //获取系研职称信息
                $scope.titleList = [];
                comService.getParamList('STAFF_TITLE', 'NEW').then(function (data) {
                    $scope.titleList = data.data;
                });
            }

            //获取系研族群列表
            function getEthnicGroupList() {
                //获取系研职称信息
                $scope.ethnicGroupList = [];
                comService.getParamList('STAFF_ETHNIC_GROUP', 'STAFF_ETHNIC_GROUP').then(function (data) {
                    $scope.ethnicGroupList = data.data;
                });
                //获取合同主体信息
                $scope.subjectOfContractList = [];
                comService.getParamList('SUBJECT_CONTRACT', 'SUBJECT_CONTRACT').then(function (data) {
                    $scope.subjectOfContractList = data.data;
                });
            }

            //遍历数组删除指定元素
            function removeData(data) {
                for (var i = 0; i < data.length; i++) {
                    if (data[i].paramCode === '05') {
                        data.splice(i, 1);
                    }
                }
            }

            //获取状态列表
            function getStateList() {
                $scope.stateList = [];
                comService.getParamList('STAFF_STATE', 'STAFF_STATE').then(function (data) {
                    if (!$scope.flag) {
                        removeData(data.data);
                    }
                    $scope.stateList = data.data;
                });
            }

            //选择图片
            $scope.changePic = function () {
                if (!$scope.flagAuth) {
                    var f = document.getElementById('myFile').files[0];
                    var index_int = f.name.lastIndexOf('.');
                    var lastArr = f.name.substring(index_int).toLowerCase();

                    var formData = new FormData($('#formTmp')[0]);
                    var file = document.querySelector('input[type=file]').files[0]; //获取文档中有类型为file的第一个input元素
                    formData.append('file', f);
                    formData.append('employeeId', $stateParams.employeeId);

                    if (
                        lastArr !== '.bmp' &&
                        lastArr !== '.png' &&
                        lastArr !== '.gif' &&
                        lastArr !== '.jpg' &&
                        lastArr !== '.jpeg'
                    ) {
                        layer.confirm(
                            '只支持bmp、png、gif、jpg、jpeg格式上传。',
                            {
                                title: false,
                                btn: ['确定'],
                            },
                            function (result) {
                                if ('' === fileTmp) {
                                    $('#prompt3').css('display', 'block');
                                }
                                layer.close(result);
                            }
                        );
                    } else {
                        $('#prompt3').css('display', 'none');
                        var readsInput = new FileReader();
                        readsInput.readAsDataURL(f);
                        readsInput.onload = function (e) {
                            document.getElementById('img_picture_view').src = this.result;
                            $('#img_picture_view').css('display', 'block');
                            $('#img_picture_del').css('display', 'inline-block');
                            $('#img_picture_view').css('height', '128px');
                            $('#img_picture_view').css('width', '104px');
                        };
                        inform.modalInstance('确定要修改照片吗？').result.then(
                            function () {
                                //上传文件大小不能超过1M (1024*1024 = 1048576)
                                if (f.size > 1048576) {
                                    if ('' === fileTmp) {
                                        $('#prompt3').css('display', 'block');
                                    }
                                    inform.common('上传文件大小不能超过1M');
                                    getPicture();
                                    return;
                                }
                                if (f !== undefined) {
                                    if (!file) {
                                        inform.common('请先选择文件!');
                                        return false;
                                    }
                                    inform.uploadFile('picture/updatePicture', formData, function (result) {
                                        fileTmp = f;

                                        // 关闭遮罩层
                                        inform.closeLayer();
                                        layer.confirm(
                                            '上传图片成功',
                                            {
                                                title: false,
                                                btn: ['确定'],
                                            },
                                            function (result) {
                                                layer.close(result);
                                                //刷新图片
                                                getPicture();
                                            }
                                        );
                                    });

                                    readsInput.readAsDataURL(f);
                                    readsInput.onload = function (e) {
                                        document.getElementById('img_picture_view').src = this.result;
                                        $('#img_picture_del').css('display', 'block');
                                        $('#img_picture_view').css('height', '128px');
                                        $('#img_picture_view').css('width', '104px');
                                    };
                                }
                            },
                            function (reason) {
                                //点击取消或者空白区域刷新图片
                                getPicture();
                            }
                        );
                    }
                } else {
                    layer.confirm('无权限上传图片。', { title: false, btn: ['确定'] });
                }
            };

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (170 + 65);
                var ss = document.getElementById('updateFormId');
                ss.style.height = divHeight + 'px';
                var clientWidth = document.body.clientWidth;
                $('#buttonStyle').css(inform.getButtonStyle(clientHeight, clientWidth));
            }

            //返回
            $scope.goBack = function () {
                $state.go('app.office.staffInfoManagementController');
            };

            /**
             * 校验时间格式正确性，不正确返回空
             * @param date
             * @returns {*}
             */
            function timeAdjust(date) {
                date = inform.format(date, 'yyyy-MM-dd');
                if (date === 'NaN-NaN-NaN' || date === '' || date == null) {
                    return '';
                } else {
                    return date;
                }
            }

            //保存修改数据
            $scope.saveUpdateData = function () {
                $scope.updateParam.graduationTime = timeAdjust($scope.updateParam.graduationTime);
                $scope.updateParam.turnPositiveDate = timeAdjust($scope.updateParam.turnPositiveDate);
                $scope.updateParam.leavingDate = timeAdjust($scope.updateParam.leavingDate);
                $scope.updateParam.birth = timeAdjust($scope.updateParam.birth);
                $scope.updateParam.getNationalTitleTime = timeAdjust($scope.updateParam.getNationalTitleTime);
                $scope.updateParam.leavingDate = timeAdjust($scope.updateParam.leavingDate);
                $scope.updateParam.onDepartmentTime = timeAdjust($scope.updateParam.onDepartmentTime);

                var param = {
                    id: $scope.updateParam.id,
                    area: $scope.updateParam.area, //地区
                    primaryDept: $scope.updateParam.primaryDept, //一级部门
                    paramType: 'NEW',
                    department: $scope.updateParam.department, //二级部门
                    employeeId: $scope.updateParam.employeeId, //工号
                    employeeName: $scope.updateParam.employeeName, //员工姓名
                    professionalModule: $scope.updateParam.professionalModule, //专业模块
                    onboardingTime:
                        inform.format($scope.updateParam.onboardingTime, 'yyyy-MM-dd') === 'NaN-NaN-NaN'
                            ? null
                            : inform.format($scope.updateParam.onboardingTime, 'yyyy-MM-dd'), //入职时间
                    onboardingYear: $scope.updateParam.onboardingYear, //入职年限
                    gender: $scope.updateParam.gender, //性别
                    graduates: $scope.updateParam.graduates, //毕业院校
                    professional: $scope.updateParam.professional, //专业
                    education: $scope.updateParam.education, //文化程度
                    graduationTime: $scope.updateParam.graduationTime, //毕业时间
                    englishLevel: $scope.updateParam.englishLevel, //英语等级
                    mentor: $scope.updateParam.mentor, //导师
                    state: $scope.updateParam.state, //工作状态
                    isRecentGraduates:
                        $scope.updateParam.isRecentGraduates == null ? '' : $scope.updateParam.isRecentGraduates, //应、往届
                    title: $scope.updateParam.title, //国家职称
                    getTitle: $scope.updateParam.getCompanyTitleTime, //获得公司职称时间
                    /*
                     * "companyTitle": $scope.updateParam.companyTitle,//公司职称
                     * */
                    getWay: $scope.updateParam.getWay, //获得方式
                    getTitleYear: $scope.updateParam.getTitleYear, //获得职称年限
                    birth: $scope.updateParam.birth, //出生年月
                    idNumber: $scope.updateParam.idNumber, //身份证号
                    national: $scope.updateParam.national, //民族
                    origin: $scope.updateParam.origin, //籍贯
                    ethnicGroup: $scope.updateParam.ethnicGroup, //族群
                    currentAddress: $scope.updateParam.currentAddress, //现住址
                    commonCall: $scope.updateParam.commonCall, //常用联系方式
                    emergencyCall: $scope.updateParam.emergencyCall, //紧急联系方式
                    maritalStatus: $scope.updateParam.maritalStatus == null ? '' : $scope.updateParam.maritalStatus, //婚姻状况
                    politicalLandscape: $scope.updateParam.politicalLandscape, //政治面貌
                    turnPositiveDate: $scope.updateParam.turnPositiveDate, //转正日期
                    leavingDate: $scope.updateParam.leavingDate, //离职时间
                    leavingReasons: $scope.updateParam.leavingReasons, //离职原因
                    note: $scope.updateParam.note, //备注
                    //                   "staffGroupList":$scope.updateParam.staffGroup,
                    emergencyContact: $scope.updateParam.emergencyContact, //紧急联系人
                    exitClassify: $scope.updateParam.exitClassify == null ? '' : $scope.updateParam.exitClassify, //离职原因分类
                    classificationReasonsForLeaving:
                        $scope.updateParam.classificationReasonsForLeaving == null
                            ? ''
                            : $scope.updateParam.classificationReasonsForLeaving, //离职原因
                    employeeType: $scope.updateParam.employeeType,
                    softwareProductLine: $scope.updateParam.softwareProductLine,
                    subdivisionProductLine: $scope.updateParam.subdivisionProductLine,
                    softwareProductLineName: $scope.updateParam.softwareProductLineName,
                    belongModule: $scope.updateParam.belongModule,
                    softwareProduct: $scope.updateParam.softwareProduct,

                    post: $scope.updateParam.post,
                    subjectOfContract: $scope.updateParam.subjectOfContract,
                    tweOneOneSchoolOrNot: $scope.updateParam.tweOneOneSchoolOrNot,
                    sequence: $scope.updateParam.sequence,
                    originalDuty: $scope.updateParam.originalDuty,
                    onDepartmentTime: $scope.updateParam.onDepartmentTime,
                    highestDegree: $scope.updateParam.highestDegree,
                    titleLevel: $scope.updateParam.titleLevel,
                    personnelType: $scope.updateParam.personnelType,
                };
                staffInfoService.updateByParam(param).then(function (result) {
                    if (result.code === '0000') {
                        $('#update_staffInfo').modal('hide');
                        layer.confirm(
                            result.message,
                            {
                                title: false,
                                btn: ['确定'],
                            },
                            function (res) {
                                layer.close(res);
                                $scope.goBack();
                            }
                        );
                    } else {
                        inform.common(result.message);
                    }
                });
            };

            $scope.sequenceChange = function (ethnicGroup) {
                //获取序列信息
                $scope.sequenceList = [];
                comService.getParamList('SEQUENCE', ethnicGroup).then(function (data) {
                    $scope.sequenceList = data.data;
                });
            };

            //点击选择图片
            $scope.updatePic = function () {
                $('#myFile').trigger('click');
            };

            //入职时间
            $scope.openOnboardingTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = true;
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = false;
                $scope.openSearchOnboardTime1 = false;
                $scope.openStopOnboardTime1 = false;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = false;
            };

            $scope.openOnboardTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;
                $scope.openOnboardTime1 = true;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = false;
                $scope.openSearchOnboardTime1 = false;
                $scope.openStopOnboardTime1 = false;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = false;
            };

            //毕业时间
            $scope.openGraduationTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = true;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = false;
                $scope.openSearchOnboardTime1 = false;
                $scope.openStopOnboardTime1 = false;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = false;
            };
            //出生年月
            $scope.openBirthTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = true;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = false;
                $scope.openSearchOnboardTime1 = false;
                $scope.openStopOnboardTime1 = false;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = false;
            };
            //转正时间
            $scope.openTurnPositiveDate = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = true;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = false;
                $scope.openSearchOnboardTime1 = false;
                $scope.openStopOnboardTime1 = false;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = false;
            };
            //离职时间
            $scope.openLeavingDate = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = true;
                $scope.openGetTitleDate1 = false;
                $scope.openSearchOnboardTime1 = false;
                $scope.openStopOnboardTime1 = false;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = false;
            };
            //获得职称时间
            $scope.openGetTitleDate = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = true;
                $scope.openSearchOnboardTime1 = false;
                $scope.openStopOnboardTime1 = false;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = false;
            };
            //查询条件（截止时间）
            $scope.openSearchOnboardTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = false;
                $scope.openSearchOnboardTime1 = true;
                $scope.openStopOnboardTime1 = false;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = false;
            };

            $scope.openStopOnboardTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = false;
                $scope.openSearchOnboardTime1 = false;
                $scope.openStopOnboardTime1 = true;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = false;
            };
            //获得国家职称时间
            $scope.openGetNationalTitleTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = false;
                $scope.openSearchOnboardTime1 = false;
                $scope.openStopOnboardTime1 = false;
                $scope.openNationalTitleTime1 = true;
                $scope.onDepartmentTime1 = false;
            };

            //入部门时间
            $scope.openOnDepartmentTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openOnboardingTime1 = false;
                $scope.openOnboardTime1 = false;
                $scope.openGraduationTime1 = false;
                $scope.openBirthTime1 = false;
                $scope.openTurnPositiveDate1 = false;
                $scope.openLeavingDate1 = false;
                $scope.openGetTitleDate1 = false;
                $scope.openSearchOnboardTime1 = false;
                $scope.openStopOnboardTime1 = false;
                $scope.openNationalTitleTime1 = false;
                $scope.onDepartmentTime1 = true;
            };

            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        },
    ]);
})();

(function () {
    'use strict';
    app.controller("costMonitoringController", ['$scope','$state','comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','costMonitoringService','$stateParams','LocalCache', '$modal','$http',
        function ($scope,$state,comService, $rootScope, inform, Trans, AgreeConstant,costMonitoringService,$stateParams,LocalCache, $modal,$http) {
    	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */ 
    	$scope.formRefer = LocalCache.getObject('costMonitoring_formRefer');
        //清除缓存
        LocalCache.setObject('costMonitoring_formRefer', {});
    	 //页面分页信息
        $scope.pages = inform.initPages();
        //设置列表的高度
        setDivHeight();
        //窗体大小变化时重新计算高度
        $(window).resize(setDivHeight);
    	//初始化页面信息
        initPages();

		$scope.handleStatusChange = function (){
			LocalCache.setObject("costMonitoring_formRefer",$scope.formRefer);
		}
        $scope.getData=getData;

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */
        
        /**
         * 设置列表的高度
         */
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (150 + 175);
            $("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight - 30);
        }
        /**
         * 重置查询条件
         */
        $scope.reset = function () {
            $scope.formRefer = {};
        };
        /**
         * 初始化页面
         */
        function initPages() {
            //获取部门
            $scope.departmentList = [];
            comService.getOrgChildren('D010053').then(function (data) {
                if (data.data) {
                    $scope.departmentList = data.data;
                }
            });
            //获取产品线
            $scope.productLines=[];
            comService.queryEffectiveParam('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
                $scope.productLines = angular.fromJson(data.data);
            });
            //预算状态
            $scope.statusList=[
                 {
                	 'code':'1',
                	 'value':'待结算',
                 },{
                	 'code':'2',
                	 'value':'结算申请',
                 },{
                	 'code':'3',
                	 'value':'已结算',
                 }];
            $scope.statusMap={
            		'1':'待结算',
            		'2':'结算申请',
            		'3':'已结算'
            }
			//没有缓存，默认为待结算
			$scope.formRefer.status = $scope.formRefer.status || '1';
            getData(1);
        }
        /**
         * 查询成本监控
         */
        function getData(indexNum){
        	var urlData = {
            	'departmentCode': $scope.formRefer.department,
            	'productLineCode': $scope.formRefer.productLine,
            	'projectName': $scope.formRefer.cname,
            	'projectStatus': $scope.formRefer.status,
            	'projectManager': $scope.formRefer.projectManager,
            	'currentPage': indexNum,
            	'pageSize': $scope.pages.size
         	};
        	costMonitoringService.getData(urlData).then(function (data) {
                 if (data.code === AgreeConstant.code) {
                     $scope.dataList = data.data.list;
                     if ($scope.dataList.length === 0) {
                         inform.common("无符合条件的项目信息");
                         $scope.pages = inform.initPages(); 			        //初始化分页数据
                     }else{
                    	 for (var i=0;i<$scope.dataList.length;i++){
                    		 var one = $scope.dataList[i];
                    		 one.feePre = inform.formatMoney(one.feePre);
                    		 one.fee = inform.formatMoney(one.fee);
                    		 one.feeSettle = inform.formatMoney(one.feeSettle);
                    		 one.hrPre = inform.removeZero(one.hrPre);
                    		 one.hrSettle = inform.removeZero(one.hrSettle);
                    		 one.hr = inform.removeZero(one.hr);
                    	 }
                         // 分页信息设置
                         $scope.pages.total = data.data.total; 			// 页面数据总数
                         $scope.pages.star = data.data.startRow; 		// 页面起始数
                         $scope.pages.end = data.data.endRow;	 		// 页面结束数
                         $scope.pages.pageNum = data.data.pageNum;       //页号
                     }
                 } else {
                     inform.common(data.message);
                 }
             }, function (error) {
                 inform.common(Trans("tip.requestError"));
             });
        }
        /**
         * 项目成本投入(项目未结算)
         */
    	$scope.costInfo = function (projectId){
    		LocalCache.setObject("costMonitoring_formRefer",$scope.formRefer);
    		$state.go("app.office.costInfo", {functionType:'costInfo',projectId:projectId});
    	}
    	/**
         * 项目成本投入(项目已结算)
         */
    	$scope.costCloseInfo = function (projectId){
    		LocalCache.setObject("costMonitoring_formRefer",$scope.formRefer);
    		$state.go("app.office.costInfo", {functionType:'costCloseInfo',projectId:projectId});
    	}
    	/**
    	 * 维护成本投入
    	 */
    	$scope.maintainInfo = function (projectId){
    		LocalCache.setObject("costMonitoring_formRefer",$scope.formRefer);
    		$state.go("app.office.costInfo", {functionType:'maintainInfo',projectId:projectId});
    	}
    	/**
    	 * 变更记录 
    	 */
    	$scope.changeInfo = function (projectId){
    		LocalCache.setObject("costMonitoring_formRefer",$scope.formRefer);
    		$state.go("app.office.changeInfo", {functionType:'changeInfo',projectId:projectId});
    	}
	     /**
          * *************************************************************
          *              方法声明部分                                 结束
          * *************************************************************
          */
         } 
    ]);
})();
/**
 * Created by SongM on 2017.3.27.
 */
(function () {
    'use strict';
    app
        .directive('ngEnter', function () {
            return function (scope, elements, attrs) {
                elements.bind('keydown keypress', function (event) {
                    if (event.which === 13) {
                        scope.$apply(function () {
                            scope.$eval(attrs.ngEnter);
                        });
                        event.preventDefault();
                    }
                });
            };
        });
})();

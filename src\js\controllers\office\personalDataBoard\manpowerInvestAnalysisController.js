(function () {
    app.controller("manpowerInvestAnalysisController", ['$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'manpowerInvestAnalysisService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http', 'workingHoursBoardFactory',
        function ($rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, manpowerInvestAnalysisService, inform, Trans, AgreeConstant, LocalCache, $http ,workingHoursBoardFactory) {

            /**
             *             初始化部分                                 开始
             * *************************************************************
             */
            //页面数据
            $scope.formRefer = {};
            //标记数据获取进度
            $scope.flag=0;
            //季度下拉框数据源
            $scope.remark = '半年总结周期一般是上年11月1号到本年6月15号。\n年度总结周期一般是上年11月1号到本年10月31号。'
            $scope.quarterSelect = [
                {
                    value: '8',
                    label: '半年总结'
                },{
                    value: '10',
                    label: '年度总结'
                },{
                    value: '6',
                    label: '自然年上半年'
                },{
                    value: '7',
                    label: '自然年下半年'
                },{
                    value: '5',
                    label: '自然年全年'
                }];
            $scope.getData = getData;
            $scope.reset = reset;
            //初始化信息
            initData();

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */


            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 初始化
             */
            function initData() {

                //获取当前登录者的empId
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ?
                    $scope.sessionEmpId : $stateParams.empId;

                //初始化查询时间,根据当前月份判断
                var date = new Date();
                var month = date.getMonth() + 1;
                var year = date.getFullYear();
                $scope.formRefer.kpiYear = year;
                if(month >= 7 && month <= 10){
                    $scope.formRefer.kpiQuarter = '5';
                }else{
                    $scope.formRefer.kpiQuarter = '8';
                }
                initManpowerData();
                getData();
            }
            function timeout(){
                //表示图表信息全部获取完成
                if(3 === $scope.flag){
                    setTimeout(eChartForProductLineWorkHourChart,500);
                    setTimeout(eChartForTechnicalDevelopChart,500);
                    setTimeout(EchartsForManpowerPieChart,500);
                }
            }
            /**
             * 获取所有指标关联信息
             */
            function getData(flag) {
                if(3 === $scope.flag){
                    $scope.flag=0;
                }
                if ($scope.formRefer.kpiYear == null || $scope.formRefer.kpiYear === "") {
                    inform.common("请选择年度");
                    return;
                }
                if ($scope.formRefer.kpiQuarter == null || $scope.formRefer.kpiQuarter === "") {
                    inform.common("请选择考核周期");
                    return;
                }
                //获取各类人力投入数据
                getAllManpowerData();
                //获取产品研发投入及其子类人力
                getProductManpowerData();
                //获取技术研发投入人力
                getTechnologyManpowerData();
                //获取饼状图各类人力资源数据
                getMainManpowerData();
            }
            function getStartAndEndMonth(){
                var thisYear = parseInt($scope.formRefer.kpiYear);
                var quarter = parseInt($scope.formRefer.kpiQuarter);
                var startMonth='';
                var endMonth='';
                if(quarter === 5){
                    startMonth = thisYear + '-01';
                    endMonth = thisYear + '-12';
                }else if(quarter === 6){
                    startMonth = thisYear + '-01';
                    endMonth = thisYear + '-06';
                }else if(quarter === 7){
                    startMonth = thisYear + '-07';
                    endMonth = thisYear + '-12';
                }else if(quarter === 8){
                    startMonth = (thisYear-1) + '-11';
                    endMonth = thisYear + '-06';
                }else if(quarter === 10){
                    startMonth = (thisYear-1) + '-11';
                    endMonth = thisYear + '-10';
                }
                return {
                    'startMonth': startMonth,
                    'endMonth': endMonth
                };
            }

            /**
             * 获取考核周期名称
             */
            function getQuarter(value){
               for(var i = 0; i < $scope.quarterSelect.length; i ++){
                   if($scope.quarterSelect[i].value === value){
                       return '-'+$scope.quarterSelect[i].label;
                   }
               }
               return '';
            }
            /**
             *下载平台人力投入分析信息表
             */
            $scope.toExcel = function() {
                var urlData = getStartAndEndMonth();
                inform.modalInstance("确定要下载人力投入明细表吗？").result.then(function() {
                    inform.downLoadFile('manpowerAnalysisDetail/toExcel',urlData,"平台人力投入分析信息表"+'-'+$scope.formRefer.kpiYear+getQuarter($scope.formRefer.kpiQuarter)+".xlsx");
                });
            };
            //获取各类人力投入数据
            function getAllManpowerData() {
                $scope.totalData = [];
                var urlData = getStartAndEndMonth();
                console.log(urlData);
                manpowerInvestAnalysisService.getAllManpowerData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.totalData = data.data;
                            } else {
                                inform.common(Trans("tip.noData"));
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            //获取饼状图各类人力数据
            function getMainManpowerData() {
                $scope.MainManpowerData = [];
                var urlData = getStartAndEndMonth();
                manpowerInvestAnalysisService.getMainManpowerData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.MainManpowerData = data.data;
                                console.log($scope.MainManpowerData);
                            } else {
                                inform.common(Trans("tip.noData"));
                            }
                            $scope.flag++;
                            timeout();
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            //获取产品研发投入人力及各类人力数据
            function getProductManpowerData() {
                $scope.ProductManpowerData = [];
                var urlData = getStartAndEndMonth();
                manpowerInvestAnalysisService.getProductManpowerData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.ProductManpowerData = data.data;
                                console.log($scope.ProductManpowerData);
                            } else {
                                inform.common(Trans("tip.noData"));
                            }
                            $scope.flag++;
                            timeout();
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            //获取技术研发投入人力及子类人力数据
            function getTechnologyManpowerData() {
                $scope.TechnologyManpowerData = [];
                var urlData = getStartAndEndMonth();
                manpowerInvestAnalysisService.getTechnologyManpowerData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.TechnologyManpowerData = data.data;
                                console.log($scope.TechnologyManpowerData);
                            } else {
                                inform.common(Trans("tip.noData"));
                            }
                            $scope.flag++;
                            timeout();
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            //初始化顶部人力详细数据
            function initManpowerData(){
                $scope.unit = '人年';
                $scope.showGatherHoursInfo = true;
                $scope.showStatisticsInfo = true;
            }
            //产品线研发投入
            function eChartForProductLineWorkHourChart(){
                $scope.currentProductLineWorkHourChart = echarts.init(document.getElementById('productLineWorkHourChart'));
                //处理项目计划完成率获取到的返回值
                var xData=[];
                //变更次数
                var manpowerNum=[];
                //产品线占比
                var productLineProportion=[];
                //产品线占比最小显示值
                var productLineProportionMin=0;
                //产品线占比基线
                var baseProductLineProportion=17;
                angular.forEach($scope.ProductManpowerData, function (ProductManpowerData) {
                    xData.push(ProductManpowerData.name);
                    manpowerNum.push(ProductManpowerData.value);
                    productLineProportion.push(ProductManpowerData.rate);
                    if(productLineProportionMin > productLineProportion){
                        productLineProportionMin = productLineProportion;
                    }


                });
                if(productLineProportionMin >= baseProductLineProportion){
                    productLineProportionMin = baseProductLineProportion - 5;
                }else if(productLineProportionMin < baseProductLineProportion && productLineProportionMin - 5 >= 0){
                    productLineProportionMin = productLineProportionMin - 5;
                }
                var option = {
                    title:{
                        text:"产品线研发投入人力",
                        textStyle:{
                            fontSize: 13,
                            color: '#333'
                        }
                    },
                    dataZoom:{
                        type:"inside",//slider是出现滚动条，inside是隐藏滚动条，通过鼠标进行放大缩小以及左右滑动
                        zoomLock:true,
                        startValue:0,
                        endValue:16,
                        bottom:7,
                        height:'18',
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: formatterCall,
                        axisPointer: {
                            type: 'cross',
                            crossStyle: {
                                color: '#999'
                            }
                        }
                    },
                    legend: {
                        data: ['投入人力', '投入人力占比'],
                        top:16
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data:xData,
                            axisPointer: {
                                type: 'shadow'
                            },
                            axisLabel:{
                                rotate:16
                            },
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            name: '人年',
                            axisLabel: {
                                formatter: '{value}'
                            }
                        },
                        {
                            type: 'value',
                            name: '占比',
                            axisLabel: {
                                formatter: '{value}%'
                            },
                            min:productLineProportionMin
                        }
                    ],
                    series: [
                        {
                            name: '投入人力',
                            type: 'bar',
                            label: {
                                show: true,
                            },
                            barWidth:'30%',
                            lineStyle:{
                                color:'blue'
                            },
                            data:manpowerNum
                        },
                        {
                            name: '投入人力占比',
                            type: 'line',
                            yAxisIndex: 1,
                            label:{
                                show:true,
                                formatter: '{c}%',
                                textStyle:{
                                    color: 'black'
                                }
                            },
                            lineStyle:{
                                color:'green'
                            },
                            data: productLineProportion,
                            markLine:{symbol:"none",
                                lineStyle: {
                                    normal: {
                                        color: 'green'   // 这儿设置安全基线颜色
                                    }
                                },
                                data: [{
                                    yAxis: baseProductLineProportion,    //这儿定义基准线的数值为多少
                                }],
                            }
                        }
                    ]
                };
                $scope.currentProductLineWorkHourChart.setOption(option, true);
            }
            //技术研发投入
            function eChartForTechnicalDevelopChart(){
                $scope.currentTechnicalDevelopChart = echarts.init(document.getElementById('technicalDevelopChart'));
                //处理项目计划完成率获取到的返回值
                console.log($scope.TechnologyManpowerData);
                var option = {
                    title: {
                        text:"技术研发投入人力",
                        textStyle:{
                            fontSize: 13,
                            color: '#333'
                        }
                    },
                    tooltip: {
                        trigger: 'item',
                        triggerOn: 'mousemove'
                    },
                    series: [
                        {
                            type: 'tree',
                            id: 0,
                            name: 'tree1',
                            data: $scope.TechnologyManpowerData,
                            top: '10%',
                            left: '22%',
                            bottom: '12%',
                            right: '28%',
                            symbolSize: 7,
                            edgeShape: 'polyline',
                            edgeForkPosition: '63%',
                            initialTreeDepth: 3,
                            lineStyle: {
                                width: 2
                            },
                            label: {
                                backgroundColor: '#fff',
                                position: 'left',
                                verticalAlign: 'middle',
                                align: 'right',
                                formatter: function(params) {
                                    return params.name + ' (' + params.value +'人年'+ ')';
                                }
                            },
                            leaves: {
                                label: {
                                    position: 'right',
                                    verticalAlign: 'middle',
                                    align: 'left'
                                }
                            },
                            emphasis: {
                                focus: 'descendant'
                            },
                            expandAndCollapse: true,
                            animationDuration: 550,
                            animationDurationUpdate: 750
                        }
                    ]
                };

                $scope.currentTechnicalDevelopChart.setOption(option, true);
            }
            function EchartsForManpowerPieChart() {
                $scope.currentManpowerPieChart = echarts.init(document.getElementById('ManpowerPieChart'));
                workingHoursBoardFactory.chartHideClear($scope.currentManpowerPieChart);
                workingHoursBoardFactory.chartShowLoading($scope.currentManpowerPieChart);
                workingHoursBoardFactory.chartHideLoading($scope.currentManpowerPieChart);
                workingHoursBoardFactory.showPie($scope.currentManpowerPieChart, $scope.MainManpowerData, {
                    title: '人力投入各类占比(按类统计)',
                    type: 'name',
                    value: 'value',
                    firstContent: '投入人力',
                    firstContentUnit: '人年',
                    needSecondContent: false
                });
            }

            //自定义鼠标悬浮样式
            function formatterCall (params, ticket, callback) {
                var htmlStr = '';
                for(var i=0;i<params.length;i++){
                    var param = params[i];
                    var xName = param.name;//x轴的名称
                    var seriesName = param.seriesName;//图例名称
                    var value = param.value;//y轴值
                    var color = param.color;//图例颜色
                    if(i===0){
                        htmlStr += xName + '<br/>';//x轴的名称
                    }
                    htmlStr +='<div>';
                    //为了保证和原来的效果一样，这里自己实现了一个点的效果
                    htmlStr += '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:'+color+';"></span>';
                    //圆点后面显示的文本
                    htmlStr += seriesName;
                    htmlStr += '<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">';
                    if(!value){
                        value="-";
                    }
                    if('投入人力' ===seriesName){
                        htmlStr += value + '人年';
                    }else{
                        htmlStr += value+ '%';
                    }

                    htmlStr += '</span>';
                    htmlStr += '</div>';
                }
                return htmlStr;
            }
            /**
             * 当窗体大小变化时，修改图例大小
             */
            window.addEventListener("resize", function () {
                if ($scope.currentProductLineWorkHourChart) {$scope.currentProductLineWorkHourChart.resize(); }
                if ($scope.currentTechnicalDevelopChart) {$scope.currentTechnicalDevelopChart.resize(); }
                if ($scope.currentManpowerPieChart) {$scope.currentManpowerPieChart.resize(); }
            });
            /**
             * 初始化检索条件年度与季度
             */
            function initTime(){

                //当前时间（Date类型）
                var date = new Date();
                //得到上一个季度的第一天
                var lastQuarterFirstDay = new Date(date.getFullYear() , date.getMonth() - 3 , 1);
                var day = inform.format(lastQuarterFirstDay, 'yyyy-MM-dd').split("-");
                $scope.formRefer.kpiYear = null != $stateParams.years ? $stateParams.years : day[0];
                //设置季度
                var month = lastQuarterFirstDay.getMonth() + 1;
                $scope.formRefer.kpiQuarter = null != $stateParams.quarter ? $stateParams.quarter : (inform.dateToQuarter(month)*1+1)+"";

                if($scope.formRefer.kpiQuarter === "1" || $scope.formRefer.kpiQuarter === "2") {
                    $scope.formRefer.kpiQuarter = "6";
                }
                if($scope.formRefer.kpiQuarter === "3" || $scope.formRefer.kpiQuarter === "4") {
                    $scope.formRefer.kpiQuarter = "7";
                }
            }

            function reset() {
                initTime();
            }

            $scope.goback = function () {
                $state.go('app.office.personKpi');
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

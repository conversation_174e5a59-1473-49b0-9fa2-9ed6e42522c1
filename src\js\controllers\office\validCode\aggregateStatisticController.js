(function () {
    app.controller("aggregateStatisticController", ['$ocLazyLoad', '$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http', 'aggregateStatisticService','codeDataReportFactory',
        function ($ocLazyLoad, $rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http, aggregateStatisticService,codeDataReportFactory) {
               // 初始化
               codeDataReportFactory.init($scope, '1');

               // 重置部分
               $scope.resetParam = resetParam;
               function resetParam(){
                codeDataReportFactory.initTime($scope,'1');
               }
     


            // 月度数据统计
            $scope.currentMonthCodeDataInputChart = null;
            // 提交次数按岗位统计
            $scope.currentMonthCommitTimesInputChart = null;
            // 人均有效代码按岗位统计
            $scope.currentMonthValidCodeInputChart = null;

            window.addEventListener("resize", chartResize);
            $scope.$on("$destroy", function() {
                window.removeEventListener('resize', chartResize);
            })
            function chartResize() {
                if ($scope.currentMonthCodeDataInputChart) { $scope.currentMonthCodeDataInputChart.resize(); }
                if ($scope.currentMonthCommitTimesInputChart) { $scope.currentMonthCommitTimesInputChart.resize(); }
                if ($scope.currentMonthValidCodeInputChart) { $scope.currentMonthValidCodeInputChart.resize(); }
            }

            // 月度数据统计
            function getMonthCodeDataChartData () {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                codeDataReportFactory.chartHideClear($scope.currentMonthCodeDataInputChart);
                codeDataReportFactory.chartShowLoading($scope.currentMonthCodeDataInputChart);
                aggregateStatisticService.getMonthCodeDataList(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.monthCodeDataInputInfo = result.data;
                        codeDataReportFactory.chartHideLoading($scope.currentMonthCodeDataInputChart);
                        codeDataReportFactory.showBarAndLine($scope.currentMonthCodeDataInputChart,$scope.monthCodeDataInputInfo, {
                            title: '月度数据统计',
                            xType: 'month',
                            yType:'totalCommitTimes',
                            yTypeLine:'avgCommitTimes',
                            left:'left',
                            fontSize:'18'
                        },[
                            '提交代码次数',
                            '人均提交次数'
                        ]);
                    } else {
                        inform.common(result.message);
                        codeDataReportFactory.chartHideLoading($scope.currentMonthCodeDataInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    codeDataReportFactory.chartHideLoading($scope.currentMonthCodeDataInputChart);
                });
            }
            
            //提交次数按岗位统计
            function getMonthCommitTimesChartData () {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                codeDataReportFactory.chartHideClear($scope.currentMonthCommitTimesInputChart);
                codeDataReportFactory.chartShowLoading($scope.currentMonthCommitTimesInputChart);
                aggregateStatisticService.getMonthCommitTimesList(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.monthCommitTimesInputInfo = result.data;
                        codeDataReportFactory.chartHideLoading($scope.currentMonthCommitTimesInputChart);
                        codeDataReportFactory.showCommitTimesBar($scope.currentMonthCommitTimesInputChart,$scope.monthCommitTimesInputInfo, '提交次数按岗位统计',[
                            'java',
                            'android',
                            'c#',
                            '前端',
                            'c++'
                        ]);
                    } else {
                        inform.common(result.message);
                        codeDataReportFactory.chartHideLoading($scope.currentMonthCommitTimesInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    codeDataReportFactory.chartHideLoading($scope.currentMonthCommitTimesInputChart);
                });
            }

            //人均有效代码按岗位统计
            function getMonthValidCodeChartData () {
                var currentUrlData = {
                    "endTime": $scope.formRefer.endTime,
                    "startTime": $scope.formRefer.startTime
                }
                codeDataReportFactory.chartHideClear($scope.currentMonthValidCodeInputChart);
                codeDataReportFactory.chartShowLoading($scope.currentMonthValidCodeInputChart);
                aggregateStatisticService.getMonthCommitTimesList(currentUrlData).then(function (result) {
                    if (result.code===AgreeConstant.code) {
                        $scope.monthValidCodeInputInfo = result.data;
                        codeDataReportFactory.chartHideLoading($scope.currentMonthValidCodeInputChart);
                        codeDataReportFactory.showValidCodeBar($scope.currentMonthValidCodeInputChart,$scope.monthValidCodeInputInfo, '人均有效代码按岗位统计',[
                            'java',
                            'android',
                            'c#',
                            '前端',
                            'c++'
                        ]);
                    } else {
                        inform.common(result.message);
                        codeDataReportFactory.chartHideLoading($scope.currentMonthValidCodeInputChart);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                    codeDataReportFactory.chartHideLoading($scope.currentMonthValidCodeInputChart);
                });
            }
           
            // 页面加载后触发
            $scope.getData = getData;
          

            function getData(){
                getMonthCodeDataChartData();
                getMonthCommitTimesChartData();
                getMonthValidCodeChartData();
            }
            $scope.loadSuccess = function (){
                $ocLazyLoad.load(
                    [
                        'library/component/echarts.min.js'
                    ]).then(function (){
                    $scope.currentMonthCodeDataInputChart = echarts.init(document.getElementById('monthCodeDataInputChart'));
                    $scope.currentMonthCommitTimesInputChart = echarts.init(document.getElementById('monthCommitTimesInputChart'));
                    $scope.currentMonthValidCodeInputChart = echarts.init(document.getElementById('monthValidCodeInputChart'));
                    
                    getData();
                    
                });
            }

            // 跳转到部门工时投入详情
            $scope.toDetailDepartmentHoursData = function () {
                $state.go('app.office.workingHoursBoard', {
                    'typeSelect': '2',
                    'orgCode': null,
                    'sortType': null
                });
            }
        }]);
})();

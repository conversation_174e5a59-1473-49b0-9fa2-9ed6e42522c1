(function() {
    'use strict';
    app.factory('PLMService', PLMService);
    PLMService.$inject=["HttpService",'$rootScope'];

    function PLMService(HttpService,$rootScope){

        function getPLMBoardTotal(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'deptPLM/getPLMBoardTotal',urlData);
        }

        function getDeptPLMCount(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'deptPLM/getDeptPLMCount',urlData);
        }

        function getDeptPLMInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'deptPLM/getDeptPLMInfo',urlData);
        }

        return {
            getPLMBoardTotal: getPLMBoardTotal,
            getDeptPLMCount: getDeptPLMCount,
            getDeptPLMInfo: getDeptPLMInfo,
        };
    }
})();
(function() {
    'use strict';
    app.factory('deptProcessAndCostService', deptProcessAndCostService);
    deptProcessAndCostService.$inject=["HttpService",'$rootScope'];

    function deptProcessAndCostService(HttpService,$rootScope){

        function getProjectProgressList(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectProgress/getProjectProgressList',urlData);
        }

        function getRateInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectProgress/getRateInfo',urlData);
        }

        function getProgressInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectProgress/getProgressInfo',urlData);
        }

        function getProgressChartInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectProgress/getProgressChartInfo',urlData);
        }

        function getCostInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectProgress/getCostInfo',urlData);
        }

        return {
            getProjectProgressList: getProjectProgressList,
            getRateInfo: getRateInfo,
            getProgressInfo: getProgressInfo,
            getProgressChartInfo: getProgressChartInfo,
            getCostInfo: getCostInfo,
        };
    }
})();
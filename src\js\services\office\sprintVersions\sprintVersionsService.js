/*
 * @Author: sun<PERSON><PERSON>a
 * @Date:   2019-05-16 16:37:05
 * @Last Modified by:   sunqixian
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
    'use strict';
  app.factory('sprintVersionsService', sprintVersionsService);
  sprintVersionsService.$inject=["HttpService",'$rootScope'];

  function sprintVersionsService(HttpService,$rootScope){
    
	var service={
			getSprintVersionInfo:getSprintVersionInfo,
			updateSprintVersionInfo:updateSprintVersionInfo,
			getLineManager:getLineManager,
			saveManagerConfig:saveManagerConfig,
			getSprintSuccessRateWithLine:getSprintSuccessRateWithLine,
			getSprintSuccessRateWithOrg:getSprintSuccessRateWithOrg
	};
    return service;
    
    /**
     * 获取冲刺版本信息
     */
    function getSprintVersionInfo(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'sprintVersions/getSprintVersionInfo', urlData);
    }
    
    /**
     * 更新信息
     */
    function updateSprintVersionInfo(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'sprintVersions/updateSprintVersionInfo', urlData);
    }

	/**
	 * 获取参数表中，产品线及Product Owner的对应数据
	 */
	function getLineManager(urlData){
		return HttpService.get($rootScope.getWaySystemApi + 'sprintVersions/getLineManager',{'currentUserName':urlData.currentUserName});
	}
	
	/**
	 * 更新Product Owner配置
	 */
	function saveManagerConfig(urlData){
		return HttpService.post($rootScope.getWaySystemApi + 'sprintVersions/saveManagerConfig', urlData);
	}

	/**
	 * 获取冲刺成功率信息(产品线)
	 */
	function getSprintSuccessRateWithLine(urlData){
		return HttpService.post($rootScope.getWaySystemApi + 'sprintVersions/getSprintSuccessRateWithLine', urlData);
	}

	/**
	 * 获取冲刺成功率信息(部门)
	 */
	function getSprintSuccessRateWithOrg(urlData){
		return HttpService.post($rootScope.getWaySystemApi + 'sprintVersions/getSprintSuccessRateWithOrg', urlData);
	}
  }
})();

(function () {
    app.controller("visitsController", ['visitsService','$rootScope', '$scope','$state','$modal','inform','Trans','AgreeConstant',
        function (visitsService,$rootScope, $scope,$state, $modal,inform,Trans,AgreeConstant) {
     	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
        // 默认显示的时间
        reset();
		$scope.type = '1';
        //页面分页信息
        $scope.pages = {
            pageNum : '',   //分页页数
            size : '',      //分页每页大小
            total : ''      //数据总数
        };
    	// 初始化分页数据
    	$scope.pages = inform.initPages();
    	//弹窗的分页信息
        $scope.detailPages = {
            pageNum : '',   //分页页数
            size : '',      //分页每页大小
            total : ''      //数据总数
        };
    	// 初始化分页数据
    	$scope.detailPages = inform.initPages();
		$scope.getData = getData;
		//在刷新页面时调用该方法
		getData(1);
        //重置查询条件
    	$scope.reset = reset;
		//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */

	    //设置列表的高度
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - 250;
 			var clientWidth = document.body.clientWidth;
 			var divWidth = clientWidth - 450;
            $("#divTBDisByMenu").height(divHeight);
            $("#divTBDisByMenu").width(divWidth);
 			$("#subDivTBDisByMenu").height(divHeight+60);

 			$("#divTBDisByPerson").height(divHeight);
            $("#divTBDisByPerson").width(divWidth);
            $("#subDivTBDisByPerson").height(divHeight+60);

 			$("#visitsChart").height(divHeight-25);
            $("#visitsChart").width(divWidth);
            $("#divChart").width(divWidth);
 		}
		/**
		 * 重置
		 */
        function reset(){
            var nowDate = new Date();
            $scope.formRefer = {
                startTime:inform.format(new Date().setMonth((nowDate.getMonth()-3)),'yyyy-MM-dd'),// 开始时间
                endTime:inform.format(nowDate,"yyyy-MM-dd"),// 结束时间
            }
        }
		/**
		 * 查询开始时间
		 */
        $scope.openDateStart = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = true;
            $scope.openedEnd = false;
        };

        /**
		 * 查询结束时间
		 */
        $scope.openDateEnd = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = false;
            $scope.openedEnd = true;
        };

		//获取访问量数据
		function getData(pageNum){
			var urlData ={
				'startTime':inform.format($scope.formRefer.startTime,'yyyy-MM-dd'),// 开始时间
				'endTime':inform.format($scope.formRefer.endTime,'yyyy-MM-dd'),// 结束时间
				'type':$scope.type === '1'? '1':'2',
                'page':pageNum,
                'pageSize':$scope.pages.size
			};
            visitsService.getVisits(urlData).then(function(data){
            	if(data.code===AgreeConstant.code){
            		$scope.visitsList = data.data.list;
            		if ($scope.visitsList.length===0) {
            		        $scope.pages = inform.initPages(); 			//初始化分页数据
            				inform.common(Trans("tip.noData"));
            		}else {
                    // 分页信息设置
                    	$scope.pages.total = data.data.total;           // 页面数据总数
                    	$scope.pages.star = data.data.startRow;         // 页面起始数
                    	$scope.pages.end = data.data.endRow;            // 页面结束数
                    	$scope.pages.pageNum = data.data.pageNum;       //页号
                    }
            		getVisitsChart();
            	}
            },
            function(error) {
            	inform.common(Trans("tip.requestError"));
            });

       }

        /**
		 * 获得折线图
		 */
        function getVisitsChart(){
            $scope.xData = [];
            $scope.yData = [];
            $scope.length = 0;
            $scope.xname = $scope.type === '1'? '菜单':'姓名';
            // 遍历查询到的数据
            if($scope.visitsList.length < 10){
                $scope.length =$scope.visitsList.length;
            }else {
                $scope.length =10;
            }
            for(var i=0;i<$scope.length;i++){
                if($scope.type === '1'){
                    $scope.xData.push($scope.visitsList[i].business);
                }else {
                    $scope.xData.push($scope.visitsList[i].employeeName);
                }
                $scope.yData.push($scope.visitsList[i].count);
            }
        	// y轴点数
        	$scope.yInterval = 0;
        	if($scope.yData[0]>100){
        		$scope.yInterval = Math.floor($scope.yData[0]/10);
        	}else {
        	    $scope.yInterval = Math.floor($scope.yData[0]/5);
        	}

        	$scope.myCodeCharts = $scope.myCodeCharts ? $scope.myCodeCharts : echarts.init(document.getElementById('visitsChart'));
        	var option = {
            		// 提示框，鼠标悬浮交互时的信息提示
                	tooltip: {
                    	trigger: 'item',
                    	formatter: '{a} <br/>{b}: {c}'
                	},
                	color: ['#61a0a8'],
                    grid:{
                        left:'10%',
                        bottom:'15%'
                    },
                	xAxis: {
                        type: 'category',
                        name:$scope.xname,
                        data: $scope.xData,
                        axisLabel:{
                            interval:$scope.xInterval,
                            rotate:25
                        }
                    },
                    // y轴
                    yAxis: {
                        type: 'value',
                        name:'浏览量(次)',
                        min:0,
                        max:$scope.yData[0],
                        interval:$scope.yInterval
                    },
                    series: [{
                    	name: '浏览量',
                        data: $scope.yData,
                        type: 'bar',
                        showAllSymbol:true
                    }]
        	};
          $scope.myCodeCharts.setOption(option, true);
        }

        $scope.setCurrentObject = function(m,type){
            $scope.currentObj = m;
            $scope.type = type;
            $scope.getVisitsDetail(1);
        }

        $scope.getVisitsDetail = function(pageNum){
            var urlData ={
				'startTime':inform.format($scope.formRefer.startTime,'yyyy-MM-dd'),// 开始时间
				'endTime':inform.format($scope.formRefer.endTime,'yyyy-MM-dd'),// 结束时间
				'type':$scope.type === '1'? '1':'2',
				'page':pageNum,
                'pageSize':$scope.detailPages.size
				//'url':m.url
			};
			if($scope.type === '1'){
			    urlData.url=$scope.currentObj.url;
			}else {
			    urlData.userId=$scope.currentObj.userId;
			}
			//-菜单维度
			visitsService.getVisitsDetail(urlData).then(function(data){
				if(data.code===AgreeConstant.code){
					$scope.visitsDetail = data.data.list;
					if ($scope.visitsDetail.length===0) {
					        $scope.detailPages = inform.initPages(); 			//初始化分页数据
							inform.common(Trans("tip.noData"));
					}else {
                    // 分页信息设置
                    	$scope.detailPages.total = data.data.total;           // 页面数据总数
                    	$scope.detailPages.star = data.data.startRow;         // 页面起始数
                    	$scope.detailPages.end = data.data.endRow;            // 页面结束数
                    	$scope.detailPages.pageNum = data.data.pageNum;       //页号
                    	$scope.detailPages.size = data.data.pageSize;         //页面大小
                    }
				}
        	},
        	function(error) {
				inform.common(Trans("tip.requestError"));
			});
        }

	     /**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */

	}]);
})();
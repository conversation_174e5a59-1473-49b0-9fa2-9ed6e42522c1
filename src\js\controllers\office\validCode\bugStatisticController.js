(function () {
    app.controller("bugStatisticController", ['$ocLazyLoad', '$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http', 'codeDataReportFactory', 'bugStatisticService',
        function ($ocLazyLoad, $rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http, codeDataReportFactory, bugStatisticService) {
            // 初始化
            codeDataReportFactory.init($scope, '3');
            $scope.title = '查询列表';
            $scope.pages = {
                pageNum : '', 		// 分页页数
                size : '', 			// 分页每页大小
                total : '' 			// 数据总数
            };
            $scope.sortColorList = ['#ee1111','#ff8811','#ffbb11','#ffee22','#bbff22','#00C0F0'];
            //初始化分页数据
            $scope.pages = inform.initPages();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.setDivHeight = setDivHeight;
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (170);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 72);
            }

            // 重置部分
            $scope.resetParam = resetParam;
            function resetParam(){
                $scope.formRefer={};
                codeDataReportFactory.initTime($scope, '3');
            }
            $scope.formatDeviation = formatDeviation;
            function formatDeviation(deviation){
                return Number(deviation*100).toFixed(0)+'%';
            }

            // 获取列表数据
            $scope.getData = getData;
            function getData(pageNum) {
                var urlData = {
                    'startTime': $scope.formRefer.startTime,
                    'endTime': $scope.formRefer.endTime,
                    'deviation':$scope.formRefer.deviation,
                    'page': pageNum,
                    'pageSize': $scope.pages.size
                }
                bugStatisticService.getUserBugsKLOC(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.dataList = data.data.list;
                                $scope.sortData($scope.type, $scope.sort);
                                // 分页信息设置
                                $scope.pages.total = data.data.total;           // 页面数据总数
                                $scope.pages.star = data.data.startRow;         // 页面起始数
                                $scope.pages.end = data.data.endRow;            // 页面结束数
                                $scope.pages.pageNum = data.data.pageNum;       //页号
                                $scope.startTimeAfterSearch = $scope.formRefer.startTime;
                                $scope.endTimeAfterSearch = $scope.formRefer.endTime;
                                $scope.deviationAfterSearch = $scope.formRefer.deviation;
                            } else {
                                inform.common(Trans("tip.noData"));
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 排序
            $scope.sortData = function (type, sort) {
                return codeDataReportFactory.sortData($scope, type, sort, $scope.dataList);
            }
            $scope.activeClass = function (type, sort){
                return codeDataReportFactory.activeClass(type, sort, $scope.type, $scope.sort);
            }
            $scope.activeTitleClass = function (type) {
                return codeDataReportFactory.activeTitleClass(type, $scope.type);
            }
            $scope.dealBgc = function (number, index, property) {
                return codeDataReportFactory.dealBgc(number, index, property, $scope.dataList, $scope.type, $scope.sort);
            }
           
            // 页面加载后触发
            $scope.$watch('$viewContentLoaded', function () {
                
                getData($scope.pages.pageNum);
            });
        }]);
})();


(function() {
	app.controller("companyProWorkTimeManagementDetail", ['comService', '$rootScope', '$scope', 'inform', 'companyProWorkTimeService','Trans', 'AgreeConstant', '$modal', '$state', '$stateParams','LocalCache',
		function(comService, $rootScope, $scope,inform, companyProWorkTimeService, Trans, AgreeConstant, $modal, $state, $stateParams,LocalCache) {
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	//获取缓存
		$scope.projectDto ={};
        $scope.projectDto = LocalCache.getObject('companyProWorkTime_formRefer');

        //设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		//获取数据
		$scope.getData = getData;
		getData();

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */

		/**
		 * 获取项目
		 */
		function getData(pageNum) {
		    //获取系研立项项目及团队投入情况
			var urlData ={
			    'projectId':$scope.projectDto.projectDto.projectId,//公司立项项目
                'startDate':$scope.projectDto.startMonth,//开始时间
                'endDate':$scope.projectDto.endMonth//结束时间
			};
			$scope.projectList=[];
            companyProWorkTimeService.getProjectTeamWorkTime(urlData).then(function(data) {
                if (data.code===AgreeConstant.code) {
                    //系研立项项目及团队投入情况集合
                    $scope.projectTeamWorkTimeList = data.data;
                    if($scope.projectTeamWorkTimeList.length!==0){
                        angular.forEach($scope.projectTeamWorkTimeList, function(m) {
                            if($scope.projectList.indexOf(m.xyProjectName) === -1 && m.xyProjectName){
                                $scope.projectList.push(m.xyProjectName)
                            }
                        });
                        $scope.type=$scope.projectList[0];
                        setTimeout($scope.getDetailData,500);
                    }

                }else {
                	inform.common(data.message);
                }
            },
            function(error) {
            	inform.common(Trans("tip.requestError"));
            });
            //获取汇总数据
            companyProWorkTimeService.getTotalWorkTime(urlData).then(function(data) {
                if (data.code===AgreeConstant.code) {
                    $scope.totalWorkTime = data.data[0];
                }else {
                	inform.common(data.message);
                }
            },
            function(error) {
            	inform.common(Trans("tip.requestError"));
            });
		}
		//获取指定项目或团队下的需求、技术支持、个人工时
		$scope.getDetailData = function(xyProjectName){
		    if(!xyProjectName){
		        xyProjectName = $scope.projectList[0];
		    }
		    //获取所有的页签li
            var lis = document.getElementsByName("projectDetail");
            for(var i=0;i<lis.length;i++){
                if (lis[i].id === xyProjectName){
                //ng-class="{'active':type == m}"
                    lis[i].style.color = 'blue';
                    lis[i].style.fontWeight = 'bolder';
                }else {
                    lis[i].style.color = 'black';
                    lis[i].style.fontWeight = 'normal';
                }
            }
			$scope.detailData ={
			    'xyProjectName':xyProjectName,//系研项目/团队名称
			    'projectId':$scope.projectDto.projectDto.projectId,//公司立项项目
                'startDate':$scope.projectDto.startMonth,//开始时间
                'endDate':$scope.projectDto.endMonth//结束时间
			};
            //查询指定项目/团队的员工投入情况
            getPersonWorkTimeDetail();
            //查询指定项目/团队的用户需求投入情况
            getStoryWorkTimeDetail();
            //查询指定项目/团队的技术支持投入情况
            getSupportWorkTimeDetail();
		}

		function getPersonWorkTimeDetail(){
		    $scope.personList=0;
            companyProWorkTimeService.getPersonWorkTimeDetail($scope.detailData).then(function(data) {
                if (data.code===AgreeConstant.code) {
                    //员工投入情况集合
                    $scope.personWorkTimeList = data.data;
                    if($scope.personWorkTimeList.length===0){
                        $scope.personList=1;
                    }
                }else {
                	inform.common(data.message);
                }
            },
            function(error) {
            	inform.common(Trans("tip.requestError"));
            });
		}

		function getStoryWorkTimeDetail(){
		    $scope.storyList=0;
            companyProWorkTimeService.getStoryWorkTimeDetail($scope.detailData).then(function(data) {
                if (data.code===AgreeConstant.code) {
                    //用户需求投入情况集合
                    $scope.storyWorkTimeList = data.data;
                    if($scope.storyWorkTimeList.length===0){
                        $scope.storyList=1;
                    }
                }else {
                	inform.common(data.message);
                }
            },
            function(error) {
            	inform.common(Trans("tip.requestError"));
            });
		}

		function getSupportWorkTimeDetail(){
		    $scope.supportList=0;
            companyProWorkTimeService.getSupportWorkTimeDetail($scope.detailData).then(function(data) {
                if (data.code===AgreeConstant.code) {
                    //用户需求投入情况集合
                    $scope.supportWorkTimeList = data.data;
                    if($scope.supportWorkTimeList.length===0){
                        $scope.supportList=1;
                    }
                }else {
                	inform.common(data.message);
                }
            },
            function(error) {
            	inform.common(Trans("tip.requestError"));
            });
		}

		/**
    	 * 设置列表的高度
    	 */
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var clientWidth = document.body.clientWidth;

 		    $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
            $("#buttonStyle").css({"width": 100+"px"});
 		}

        /**
	     * *************************************************************
	     *              方法声明部分                                结束
	     * *************************************************************
	     */
		} ]);
})();
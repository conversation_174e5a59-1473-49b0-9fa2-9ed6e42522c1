<!-- 弹出框 -->
<script type="text/ng-template" id="myModalContent.html">
    <div ng-include="'tpl/common/modal.html'"></div>
</script>
<style>
	.disableA {
		pointer-events: none;
		cursor: default;
	}
</style>
<div vue-component="SimpleDemo" style="min-height: 200px;">
	<!-- 加载中显示 -->
	<div class="text-center" style="padding: 50px;">
		<i class="fa fa-spinner fa-spin fa-2x" style="color: #409eff;"></i>
		<p style="margin-top: 10px; color: #666;">正在加载 Vue3 组件...</p>
	</div>
</div>
<!-- 导航条 -->
<div class="animated fadeInUp">
	<ul class="breadcrumb b-a">
		<li><a href><i class="fa fa-home"></i><span translate="研发管理数据维护"></span></a></li>
		<li class="active" translate="会议信息管理"></li>
	</ul>
</div>


<!-- 查询条件 -->
<div class="panel search">
	<span class="title" translate="common.searchHeader" style="text-align: left;">按条件查询</span>
	<div class="search-list pull-right">
		<a ng-click="getData(pages.pageNum,type)"> <i class="fa fa-search"></i>
			<span translate="common.searchBtn"></span>
		</a> <a ng-click="clear()"> <i class="fa fa-repeat"></i> <span translate="common.resetBtn"></span>
		</a>
	</div>
	<div class="form-group">
		<div class="panel-body form-horizontal">

			<div class="col-sm-2" style="margin-top: 8px; width: 270px;">
				<label>会议主题：</label> <input type="text" style="width: 150px; height: 28px;" name="meetingTheme"
					ng-model="formRefer.meetingTheme">
			</div>

			<div class="col-sm-2" style="margin-top: 8px; width: 270px;">
				<label>组织者：</label> <input type="text" style="width: 150px; height: 28px;" name="projectAssistant"
					ng-model="formRefer.projectAssistant">
			</div>

			<div class="col-sm-2" style="margin-top: 8px; width: 270px;">
				<label>参会人员：</label> <input type="text" style="width: 150px; height: 28px;" name="keyFigure"
					ng-model="formRefer.keyFigure">
			</div>

			<div class="col-sm-2" style="margin-top: 8px; width: 270px;">
				<label>会议状态：</label> <select ng-model="formRefer.meetingState" style="width: 150px; height: 28px;"
					ng-options="item.value as item.label for item in meetingStateSelect" ng-verify>
					<option value="" translate="请选择"></option>
				</select>
			</div>

			<div class="col-sm-12"></div>
			<div class="col-sm-2" style="margin-top: 8px; width: 270px;">
				<label>会议类别：</label> <select ng-model="formRefer.meetingType" style="width: 150px; height: 28px;"
					ng-verify ng-options="item.paramCode as item.paramValue for item in meetingTypeList">
					<option value="" translate="请选择"></option>
				</select>
			</div>
			<div class="col-sm-2" style="margin-top: 10px; width: 450px;">
				<div class="hbox">
					<label class="col control-label w-sm" style="width: 60px">会议时间：</label>
					<div class="col">
						<input style="margin-left: -12px; width: 150px;" readonly="readonly" type="text"
							ng-blur="vm.timeJudge()" class="form-control" datepicker-popup="{{year}}年第{{week}}周会议"
							ng-model="formRefer.weekTime" style="text-align: center;" is-open="openTime1"
							datepicker-options="dateOptions" placeholder="{{'默认为当前周'|Trans}}" close-text="关闭"
							clear-text="清空" ng-click="openTime($event) " ng-change="sureWeekTime()" current-text="今天" />
					</div>
					<span class="col control-label w-sm pull-left"
						style="width: 260px; margin-left: 20px; margin-right: 20px;">第{{week}}周起止时间为：{{weekTime}}</span>
				</div>
			</div>

		</div>
	</div>

</div>
<div class="wrapper-md wrapper-b-15">
	<div class="tabbable">
		<ul class="nav nav-tabs">
			<li ng-click="type='1';getData(1,1)" ng-class="{'active':type == '1'}" class="button-hover"><a>
					<span translate="会议查询列表"></span>
				</a></li>
			<li ng-click="type='2';getData(1,2)" ng-class="{'active':type == '2'}" class="button-hover"><a>
					<span translate="会议信息维护"></span>
				</a></li>
		</ul>
	</div>
</div>
<!-- 表格操作图标 -->
<div class="panel search table-opt">
	<div class="search-list pull-right" ng-show="type=='2'">
		<a ng-click="addModal()"> <i class="fa fa-plus"></i> <span translate="新增"></span>
		</a>
	</div>
	<div class="search-list pull-right" ng-show="type=='1'">
		<a ng-click="toExcel()"> <i class="glyphicon glyphicon-arrow-down"></i>
			<span translate="下载Excel"></span>
		</a>
	</div>
</div>
<br />
<!-- 表格展示 -->
<div id="divTBDis" class="wrapperReport" ng-show="type=='2'">
	<div class="table-responsive">
		<table style="table-layout: fixed" class="table dataTable table-striped table-hover b-t b-light">
			<thead>
				<tr>
					<th style="width: 5%;">NO.</th>
					<th style="width: 8%;">会议号</th>
					<th style="width: 16%;">会议日期</th>
					<th style="width: 8%;">会议时间</th>
					<th style="width: 20%;">会议主题</th>
					<th style="width: 16%;">参会人员</th>
					<th style="width: 8%;">组织者</th>
					<th style="width: 8%;">会议状态</th>
					<th style="width: 8%;">邮件发送状态</th>
					<th style="width: 8%;">会议形式</th>
					<th style="width: 15%;text-align: center;">操作</th>
					<th style="width: 5px"></th>
				</tr>
			</thead>
		</table>
		<div id="subDivTBDis" style="overflow-y: scroll;">
			<table style="table-layout: fixed" class="table dataTable table-striped table-hover b-t b-light">
				<tbody>
					<tr ng-repeat="m in dataInfo track by $index">
						<td style="width: 5%;" title="{{$index+1}}">{{$index+1}}</td>
						<td style="width: 8%;" title="{{m.meetingNumber}}">{{m.meetingNumber | characters:20}}</td>
						<td style="width: 16%;" title="{{m.meetingDay + '(' + m.meetingWeek + ')'}}">{{m.meetingDay
							+ '(' + m.meetingWeek + ')' | characters:20}}</td>
						<td style="width: 8%;" title="{{m.startTime + '-' + m.endTime}}" ng-show="m.meetingShape!='0'">
							{{m.startTime
							+ '-' + m.endTime | characters:20}}</td>
						<td style="width: 8%;" title="{{m.startTime}}" ng-show="m.meetingShape=='0'">{{m.startTime|
							characters:20}}</td>
						<td style="width: 20%;" title="{{m.meetingTheme}}">{{m.meetingTheme
							| characters:30}}</td>
						<td style="width: 16%;" title="{{m.keyFigure}}">{{m.keyFigure
							| characters:20 }}</td>
						<td style="width: 8%;" title="{{m.realName}}">{{m.realName |
							characters:10}}</td>
						<td style="width: 8%;" title="{{meetingStateMap[m.meetingState]}}">
							{{meetingStateMap[m.meetingState]
							| characters:10}}</td>
						<td style="width: 8%;">{{m.type==0?'未发送':'已发送'}}</td>
						<td style="width: 8%;">{{m.meetingShape==1?'会议':'邮件'}}</td>
						<td style="width: 15%;text-align: center;" class="action-buttons">
							<a data-toggle="modal" ng-cancel ng-click="popModal(m)" class="green" title="修改会议信息"> <i
									class="fa fa-pencil bigger-130"></i>
							</a><a data-toggle="modal" ng-cancel ng-click="copyMeeting(m)" title="克隆"><i
									class="ace-icon fa fa-copy bigger-130 green"></i>
							</a> <a data-toggle="modal" ng-cancel ng-click="toMail(m)"
								ng-class="{'disableA':jude(m.meetingState)}" title="发送通知"> <i
									class="ace-icon fa fa-send bigger-130" ng-class="judeUp(m.meetingState)"></i>
							</a><a data-toggle="modal" ng-cancel ng-click="toFeedback(m)"
								ng-class="{'disableA':jude(m.meetingState)}" title="反馈"> <i
									class="ace-icon fa fa-reply-all bigger-130" ng-class="judeUp(m.meetingState)"></i>
							</a><a data-toggle="modal" ng-cancel ng-click="deleteInfo(m)" class="green" title="删除会议信息">
								<i class="ace-icon fa fa-trash-o bigger-130 red"></i>
							</a>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<div data-ng-include="'tpl/blocks/pagination.html'"></div>
</div>

<!-- 表格展示 -->
<div id="divTBDissel" class="wrapperReport" ng-show="type=='1'">
	<div class="table-responsive">
		<div id="subDivTBDissel" style="overflow-y: scroll; overflow-x: hidden;">
			<table id="subDivTBDisselTab" style="table-layout: fixed;" border="1px"
				class="table dataTable table-striped table-hover b-t b-light">
				<tbody>
					<tr style="width: 1800px;">
						<th style="text-align: center;width: 100px">日期</th>
						<th colspan="{{morningMax}}" style="text-align: center;">上午</th>
						<th colspan="{{neigthMax}}" style="text-align: center;">下午</th>
					</tr>
					<tr ng-repeat="m in tableData track by $index">
						<td style="vertical-align: middle; text-align: center;width: 100px">
							{{m.meetingDay}}<br />{{m.meetingWeek}}
						</td>
						<td colspan="{{morningMax}}" id="mroningTd">
							<table style="width: 100%" border="1px">
								<tr>
									<td style="width: 100px">【会议时间】</td>
									<td style="width: 300px" ng-repeat="item in m.mroningList" title="
                    【会议时间】{{item.startTime + '-' + item.endTime}}
【会议状态】{{meetingStateMap[item.meetingState]}}
【会议主题】{{item.meetingTheme}}
【参会人员】{{item.keyFigure}}
【组织者】{{item.realName}}
【会议地点】{{item.meetingRoom}}">
										{{item.startTime + '-' + item.endTime}}<br />
									</td>

									<td ng-show="m.mroningList.length != morningMax">&emsp;</td>
								</tr>
								<tr>
									<td style="width: 100px">【会议状态】</td>
									<td style="width: 300px" ng-repeat="item in m.mroningList" title="
                    【会议时间】{{item.startTime + '-' + item.endTime}}
【会议状态】{{meetingStateMap[item.meetingState]}}
【会议主题】{{item.meetingTheme}}
【参会人员】{{item.keyFigure}}
【组织者】{{item.realName}}
【会议地点】{{item.meetingRoom}}">
										{{meetingStateMap[item.meetingState]}}<br />
									</td>
									<td ng-show="m.mroningList.length != morningMax">&emsp;</td>
								</tr>
								<tr>
									<td style="width: 100px">【会议主题】</td>
									<td style="width: 300px" ng-repeat="item in m.mroningList" title="
                    【会议时间】{{item.startTime + '-' + item.endTime}}
【会议状态】{{meetingStateMap[item.meetingState]}}
【会议主题】{{item.meetingTheme}}
【参会人员】{{item.keyFigure}}
【组织者】{{item.realName}}
【会议地点】{{item.meetingRoom}}">
										{{item.meetingTheme| characters:(45/m.mroningList.length)}}<br />
									</td>
									<td ng-show="m.mroningList.length != morningMax">&emsp;</td>
								</tr>
								<tr>
									<td style="width: 100px">【参会人员】</td>
									<td style="width: 300px" ng-repeat="item in m.mroningList" title="
                    【会议时间】{{item.startTime + '-' + item.endTime}}
【会议状态】{{meetingStateMap[item.meetingState]}}
【会议主题】{{item.meetingTheme}}
【参会人员】{{item.keyFigure}}
【组织者】{{item.realName}}
【会议地点】{{item.meetingRoom}}">
										<div style="color: blue; display: inline">{{item.keyFigure}}</div>
										<br />
									</td>
									<td ng-show="m.mroningList.length != morningMax">&emsp;</td>
								</tr>
								<tr>
									<td style="width: 100px">【组织者】</td>
									<td style="width: 300px" ng-repeat="item in m.mroningList" title="
                    【会议时间】{{item.startTime + '-' + item.endTime}}
【会议状态】{{meetingStateMap[item.meetingState]}}
【会议主题】{{item.meetingTheme}}
【参会人员】{{item.keyFigure}}
【组织者】{{item.realName}}
【会议地点】{{item.meetingRoom}}">
										{{item.realName}}<br />
									</td>
									<td ng-show="m.mroningList.length != morningMax">&emsp;</td>
								</tr>
								<tr>
									<td style="width: 100px">【会议地点】</td>
									<td style="width: 300px" ng-repeat="item in m.mroningList" title="
                    【会议时间】{{item.startTime + '-' + item.endTime}}
【会议状态】{{meetingStateMap[item.meetingState]}}
【会议主题】{{item.meetingTheme}}
【参会人员】{{item.keyFigure}}
【组织者】{{item.realName}}
【会议地点】{{item.meetingRoom}}">
										{{item.meetingRoom}}</td>
									<td ng-show="m.mroningList.length != morningMax">&emsp;</td>
								</tr>
							</table>
						</td>
						<td colspan="{{neigthMax}}" id="neigthTd">
							<table style="width: 100%" border="1px">
								<tr>
									<td style="width: 100px">【会议时间】</td>
									<td style="width: 300px" ng-repeat="item in m.neightList" title="
                    【会议时间】{{item.startTime + '-' + item.endTime}}
【会议状态】{{meetingStateMap[item.meetingState]}}
【会议主题】{{item.meetingTheme}}
【参会人员】{{item.keyFigure}}
【组织者】{{item.realName}}
【会议地点】{{item.meetingRoom}}">
										{{item.startTime + '-' + item.endTime}}<br />
									</td>
									<td ng-show="m.neightList.length != neigthMax">&emsp;</td>
								</tr>
								<tr>
									<td style="width: 100px">【会议状态】</td>
									<td style="width: 300px" ng-repeat="item in m.neightList" title="
                    【会议时间】{{item.startTime + '-' + item.endTime}}
【会议状态】{{meetingStateMap[item.meetingState]}}
【会议主题】{{item.meetingTheme}}
【参会人员】{{item.keyFigure}}
【组织者】{{item.realName}}
【会议地点】{{item.meetingRoom}}">
										{{meetingStateMap[item.meetingState]}}<br />
									</td>
									<td ng-show="m.neightList.length != neigthMax">&emsp;</td>
								</tr>
								<tr>
									<td style="width: 100px">【会议主题】</td>
									<td style="width: 300px" ng-repeat="item in m.neightList" title="
                    【会议时间】{{item.startTime + '-' + item.endTime}}
【会议状态】{{meetingStateMap[item.meetingState]}}
【会议主题】{{item.meetingTheme}}
【参会人员】{{item.keyFigure}}
【组织者】{{item.realName}}
【会议地点】{{item.meetingRoom}}">
										{{item.meetingTheme| characters:(45/m.mroningList.length)}}<br />
									</td>
									<td ng-show="m.neightList.length != neigthMax">&emsp;</td>
								</tr>
								<tr>
									<td style="width: 100px">【参会人员】</td>
									<td style="width: 300px" ng-repeat="item in m.neightList" title="
                    【会议时间】{{item.startTime + '-' + item.endTime}}
【会议状态】{{meetingStateMap[item.meetingState]}}
【会议主题】{{item.meetingTheme}}
【参会人员】{{item.keyFigure}}
【组织者】{{item.realName}}
【会议地点】{{item.meetingRoom}}">
										<div style="color: blue; display: inline">{{item.keyFigure}}</div> <br />
									</td>
									<td ng-show="m.neightList.length != neigthMax">&emsp;</td>
								</tr>
								<tr>
									<td style="width: 100px">【组织者】</td>
									<td style="width: 300px" ng-repeat="item in m.neightList" title="
                    【会议时间】{{item.startTime + '-' + item.endTime}}
【会议状态】{{meetingStateMap[item.meetingState]}}
【会议主题】{{item.meetingTheme}}
【参会人员】{{item.keyFigure}}
【组织者】{{item.realName}}
【会议地点】{{item.meetingRoom}}">
										{{item.realName}}<br />
									</td>
									<td ng-show="m.neightList.length != neigthMax">&emsp;</td>
								</tr>
								<tr>
									<td style="width: 100px">【会议地点】</td>
									<td style="width: 300px" ng-repeat="item in m.neightList" title="
                    【会议时间】{{item.startTime + '-' + item.endTime}}
【会议状态】{{meetingStateMap[item.meetingState]}}
【会议主题】{{item.meetingTheme}}
【参会人员】{{item.keyFigure}}
【组织者】{{item.realName}}
【会议地点】{{item.meetingRoom}}">
										{{item.meetingRoom}}
									</td>
									<td ng-show="m.neightList.length != neigthMax">&emsp;</td>
								</tr>
							</table>
							<!-- <table style="width: 100%">
								<tr>
									<td ng-repeat="item in m.neightList"
										title="
                    【会议时间】{{item.startTime + '-' + item.endTime}}
【会议状态】{{meetingStateMap[item.meetingState]}}
【会议主题】{{item.meetingTheme}}
【参会人员】{{item.keyFigure}}
【组织者】{{item.realName}}
【会议地点】{{item.meetingRoom}}">
										【会议时间】{{item.startTime + '-' + item.endTime}}<br />
										【会议状态】{{meetingStateMap[item.meetingState]}}<br />
										【会议主题】{{item.meetingTheme|
										characters:(45/m.neightList.length)}}<br /> 【参会人员】
										<div style="color: blue; display: inline">{{item.keyFigure|
											characters:(45/m.neightList.length)}}</div> <br />
										【组织者】{{item.realName}}<br />
										【会议地点】{{item.meetingRoom}}
									</td>
								</tr>
							</table> -->
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
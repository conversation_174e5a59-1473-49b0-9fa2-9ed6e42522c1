(function() {
	'use strict';
	app.factory('kpiInfoService', kpiInfoService);
	kpiInfoService.$inject = [ "HttpService", '$rootScope' ];

	function kpiInfoService(HttpService, $rootScope) {
		var service = {
		    getData:getData,
		    refresh:refresh,
		    getFirstKpi:getFirstKpi,
		    getSecKpi:getSecKpi,
		    getThirdKpi:getThirdKpi,
		    getHistoryData:getHistoryData
		};
		return service;
        /**
		 * 根据参数查询项目或者团队Kpi数据
		 */
		function getData(urlData) {
			return HttpService.post($rootScope.getWaySystemApi + 'kpiInfo/getData', urlData);
		}
        /**
		 * 刷新项目评分数据
		 */
		function refresh(urlData) {
		    //以kpiId为条件调用定时任务方法重新计算
            return HttpService.post($rootScope.getWaySystemApi + 'kpiInfo/refresh', urlData);
        }
        /**
		 * 查一级指标数据
		 */
		function getFirstKpi(urlData) {
		    //以kpiId为条件查一级指标数据
            return HttpService.post($rootScope.getWaySystemApi + 'kpiInfo/getFirstKpi', urlData);
        }
        /**
		 * 查二级指标数据
		 */
		function getSecKpi(urlData) {
		    //以kpiId与一级指标Code为条件查二级指标数据
            return HttpService.post($rootScope.getWaySystemApi + 'kpiInfo/getSecKpi', urlData);
        }
        /**
		 * 查三级级指标数据
		 */
		function getThirdKpi(urlData) {
		    //以kpiId与一级指标Code为条件查三级指标数据
            return HttpService.post($rootScope.getWaySystemApi + 'kpiInfo/getThirdKpi', urlData);
        }
        /**
		 * 根据参数查询项目或者团队Kpi历史数据
		 */
		function getHistoryData(urlData) {
			return HttpService.post($rootScope.getWaySystemApi + 'kpiInfo/getHistoryData', urlData);
		}
	}
})();

(function () {
    app.controller("internalDemandManagementController", ['internalDemandManagementService','comService','$rootScope', '$scope', '$stateParams','$modal', 'inform', 'LocalCache', 'Trans', 'AgreeConstant', '$http','$state', 'customerDataService',
        function (internalDemandManagementService, comService, $rootScope, $scope,$stateParams, $modal, inform, LocalCache, Trans, AgreeConstant, $http,$state, customerDataService) {
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		 //查询条件
		 $scope.formRefer={};
		 //获取缓存
         $scope.formRefer = LocalCache.getObject('internalDemand_formRefer');
         //清除缓存
         LocalCache.setObject('internalDemand_formRefer', {});
         //设置编辑按钮为不可见
         $scope.edit=false;
         //初始化需求完成情况
          $scope.demandCompleteMap = [{
             value: '已拒绝',
             label: '已拒绝'
          },
          {  value: '尚未制定计划',
             label: '尚未制定计划'
          },
          {  value: '未到期',
             label: '未到期'
          },
          {  value: '临期',
             label: '临期'
          },
          {  value: '延期未完成',
             label: '延期未完成'
          },
          {  value: '延期完成',
             label: '延期完成'
          },
          {  value: '按时完成',
             label: '按时完成'
          }
          ];
		//设置列表的高度
        setDivHeight();
        //初始化根据用户名获取一级部门列表
        initPrimaryDeptList();
        //判断按钮是否具有权限
        getButtonPermission();
        //窗体大小变化时重新计算高度
        $(window).resize(setDivHeight);
        //分页
        $scope.pages = {
            pageNum: '', 		// 分页页数
            size: '', 			// 分页每页大小
            total: '' 			// 数据总数
        };
        //初始化分页信息
        $scope.pages = inform.initPages();
        //修改一级部门，二级部门进行联动
        $scope.changeDept = getDepartment;
        initInfo();

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
		    //获取完成情况状态
            $scope.getCompleteStatus = function(status) {
              if (status === 1) {
                return '是';
              } else if (status === 0) {
                return '否';
              } else {
                return '';
              }
            };

        /**
         * 初始化根据用户名获取一级部门列表
         */
        function initPrimaryDeptList() {
            $scope.primaryDeptList = [];
            comService.getOrgChildren('0002').then(function(data) {
                 if (data.data) {
                     $scope.primaryDeptList = data.data;
                 }
            });
            if(null != $scope.primaryDeptList){
                getDepartment();
            }
        }
        //缓存时，初始化二级部门
        function getDepartment(){
         //获取二级部门
         $scope.departmentList = [];
         comService.getOrgChildren($scope.formRefer.primaryDeptId).then(function (data) {
             if (data.code === AgreeConstant.code) {
                 $scope.departmentList = data.data;
             }
         });
        }
        /**
         * 设置列表的高度
         */
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight;
            if(null !== $scope.notCommonProblem){
                divHeight = clientHeight - (150 + 160);
            }else {
                divHeight = clientHeight - (150 + 250);
            }
            $("#divTBDis").height(divHeight);
        }
		//重置
		$scope.reset = function() {
			    $scope.formRefer = {
			        productLine : '',
			        demandMessage:'',
			        completeMessage:'',
			        presentStaff:'',
			        teamLeader:'',
			        dingdingFlowId:''
			        };
		}
         /**
          * 根据关键角色名称，遍历以获取其编号
          * @param item
          */
        function getEmployeeId(employeeName){
            for(var i = 0;i <$scope.employeeList.length;i++){
                if($scope.employeeList[i].realName === employeeName){
                   return  $scope.employeeList[i].employeeNo;
                }
            }
            return null;
        }
		/**
		 * 初始化
		 */
    	function initInfo() {
            //获取产品线
            $scope.lines = [];
            comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                 $scope.lines = angular.fromJson(data.data);
            });

            //获取员工信息
            $scope.employeeList = [];
            comService.getEmployeesByOrgId('').then(function(data) {
                 $scope.employeeList = data.data;
                 $scope.getData = getData; // 查询
                 getData($scope.pages.pageNum);
            });

    	}

    	/**
         * 获取所有内部需求信息
         */
        function getData(pageNum) {
            //删除已加载冻结头部和部分列的HTML模板
            $scope.dataTableShow = 0;
            var urlData ={
                'primaryDeptId': $scope.formRefer.primaryDeptId,
                'departmentId' : $scope.formRefer.departmentId,
                'productLine': $scope.formRefer.productLine,
                'processApprovalStatus': $scope.formRefer.processApprovalStatus,
                'startTime': $scope.formRefer.startTime,
                'endTime': $scope.formRefer.endTime,
                'demandMessage' : $scope.formRefer.demandMessage,
                'demandCompleteMessage' : $scope.formRefer.demandCompleteMessage,
                'presentStaffId' : getEmployeeId($scope.formRefer.presentStaffId)? getEmployeeId($scope.formRefer.presentStaffId) : $scope.formRefer.presentStaffId,
                'teamLeaderId' : getEmployeeId($scope.formRefer.teamLeaderId) ? getEmployeeId($scope.formRefer.teamLeaderId) : $scope.formRefer.teamLeaderId,
                'dingdingFlowId' : $scope.formRefer.dingdingFlowId,
                'currentPage':pageNum,//当前页数
                'pageSize':$scope.pages.size//每页显示条数

            }
            console.log(urlData);
            internalDemandManagementService.getInternalDemand(urlData).then(function (data) {
                //重新加载冻结头部和部分列的HTML模板
                $scope.dataTableShow = 1;
                if (data.code === AgreeConstant.code) {
                    if(null==data.data){
                    	$scope.sheetData = {};
                    	inform.common(Trans("tip.noData"));
                    	$scope.pages = inform.initPages();
                    	$scope.pages.size = "50";
                    } else {
                    	//内部需求列表详情
                       $scope.sheetData = data.data.demandManagementDtoPageInfo.list;
                       //内部需求完成状况
                       $scope.headData = data.data.innerDemandManagementTotalDTO;
                       angular.forEach($scope.sheetData, function (i) {
                            i.checked = false;
                        });
                        //分页信息设置
                        $scope.pages.total = data.data.demandManagementDtoPageInfo.total;
                        $scope.pages.star = data.data.demandManagementDtoPageInfo.startRow;
                        $scope.pages.end = data.data.demandManagementDtoPageInfo.endRow;
                        $scope.pages.pageNum = data.data.demandManagementDtoPageInfo.pageNum;
                    }
                    //调用DataTable组件冻结表头和左侧及右侧的列
                    setTimeout(showDataTable,300);
                } else {
                   inform.common(data.message);
                }
            },function () {
               inform.common(Trans("tip.requestError"));
            });
        }
        /**
        *调用DataTable组件冻结表头和左侧及右侧的列
        */
        function showDataTable(){
            $('#fixedLeftAndTop').DataTable( {
                //可被重新初始化
                retrieve:       true,
                //自适应高度
                scrollY:        'calc(100vh - 350px)',
                scrollX:        true,
                scrollCollapse: true,
                //控制每页显示
                paging:         false,
                //冻结列（默认冻结左1）
                fixedColumns:   {
                    leftColumns: 2,
                    rightColumns: 1
                },
                //search框显示
                searching:      false,
                //排序箭头
                ordering:       false,
                //底部统计数据
                info:           false
            } );
        }


        /**
         * 获取按钮权限
         */
        function getButtonPermission(){
            var buttons = {
                   'Button-internalDemand-edit':'edit',
            };
            var urlData = {
                'userId':LocalCache.getSession("userId"),
                'parentPermission':'customerDemand',
                'buttons':buttons
            };
            comService.getButtonPermission(urlData,$scope);
        }

        /**
         * 编辑按钮，点击跳转到编辑页面
         */
        $scope.toEdit = function (m) {
            //复制该条记录信息
            LocalCache.setObject('internalDemand_formRefer', $scope.formRefer);

            var url = 'app.office.internalDemandManagement_update';
            $state.go(url, { id: m.id});
        };
        /**
         *下载内部需求信息
         */
        $scope.toExcel = function() {
             inform.modalInstance("确定要下载内部需求信息表吗？").result.then(function() {
                var urlData ={
                             'primaryDeptId': $scope.formRefer.primaryDeptId,
                             'departmentId' : $scope.formRefer.departmentId,
                             'productLine': $scope.formRefer.productLine,
                             'startTime': $scope.formRefer.startTime,
                             'endTime': $scope.formRefer.endTime,
                             'demandMessage' : $scope.formRefer.demandMessage,
                             'demandCompleteMessage' : $scope.formRefer.demandCompleteMessage,
                             'presentStaffId' : getEmployeeId($scope.formRefer.presentStaffId),
                             'teamLeaderId' : getEmployeeId($scope.formRefer.teamLeaderId),
                             'dingdingFlowId' : $scope.formRefer.dingdingFlowId
                         }
                   inform.downLoadFile('internalDemandManagement/toExcel',urlData,"内部需求信息表"+inform.format(new Date(),'yyyy-MM-dd')+".xlsx");
            });
        };

        //通过钉钉流程ID查询工时
        $scope.viewConsumed = function (item) {
            customerDataService.viewConsumed(item.dingdingFlowId).then(function (data) {
                if (data.code === '0000') {
                    item.usedHour = data.data;
                }
            });
        }
	 		/**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */

	}]);
})();

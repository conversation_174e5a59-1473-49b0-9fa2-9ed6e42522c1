(function () {
    'use strict';
    app.factory('reportService', reportService);
    reportService.$inject = ["HttpService", '$rootScope'];

    function reportService(HttpService, $rootScope) {
        var service = {
            getReportInfo: getReportInfo,
            addReportInfo: addReportInfo,
            updateReportInfo: updateReportInfo,
            selectProjectData: selectProjectData,
            deleteById: deleteById,
            getJudgeByReview: getJudgeByReview,
            verifyReviewExist: verifyReviewExist,
            getProjectInfoById:getProjectInfoById,
            getPreRerviewReviewContentAndId:getPreRerviewRevieContentAndId,
            getReportResultGather:getReportResultGather
        };
        return service;


        
        /**
         * 根据Id删除表记录
         * @param id 参数
         * @returns {HttpPromise|Request|*|void|promise}
         */
        function deleteById(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'report/deleteById', urlData);
        }

        function updateReportInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'report/updateInfo', urlData);
        }

        /**
         * 获取报表信息
         * @param urlData 查询参数
         */
        function getReportInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'report/getInfo', urlData);
        }

        /**
         * 添加数据
         * @param urlData 数据参数
         */
        function addReportInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'report/addInfo', urlData);
        }

        /**
         * 添加数据
         * @param urlData 数据参数
         */
        function selectProjectData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'report/selectProjectData', urlData);
        }

        /**
         * 获取评委列表
         * @param urlData 数据参数
         */
        function getJudgeByReview(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'reviewJudge/getJudgeByReview', urlData);
        }

        /**
         * 校验评审是否存在
         * @param urlData 数据参数
         */
        function verifyReviewExist(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'report/verifyReviewExist', urlData);
        }

        /**
         * 根据项目id获取项目信息
         * @param projectId 项目id
         */
        function getProjectInfoById(projectId) {
            return HttpService.post($rootScope.getWaySystemApi + 'report/getProjectInfoById', projectId);
        }

        /**
         *通过项目id查询预评审的id和评审内容
         */
        function getPreRerviewRevieContentAndId(projectId) {
            return HttpService.post($rootScope.getWaySystemApi + 'report/getPreRerviewReviewContentAndId',projectId);
        }

        /**
         * 获取报表结果汇总信息
         * @param urlData 查询参数
         */
        function getReportResultGather(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'report/getReportResultGather', urlData);
        }

    }
})();
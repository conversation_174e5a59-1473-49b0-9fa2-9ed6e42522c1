
(function () {
    app.controller("humanResChangeStatisticsController", [ '$scope','$rootScope','$stateParams','$state','LocalCache','inform','Trans','comService','humanResStatisticsService','AgreeConstant','$modal',
        function ($scope,$rootScope, $stateParams,$state,LocalCache,inform,Trans,comService,humanResStatisticsService,AgreeConstant,$modal) {

    		$scope.formRefer = {};
            //标记数据获取进度
            $scope.flag=0;
            $scope.initFlag=0;
            //是否点击了查询按钮
            $scope.selectFlag;
    		$scope.timeInterval=['年度','半年度','季度'];
    		$scope.areaTableList=['威海','北京','西安','深圳','合计'];
    		$scope.secList=['当期','同期','增减幅','当期','同期','增减幅','当期','同期','增减幅','当期','同期','增减幅','当期','同期','增减幅'];
    		$scope.getData = getData;
    		$scope.resetParam = resetParam;
    		resetParam();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            initData();
            getData();

            //设置列表的高度
            function setDivHeight(){
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - 250;
                var clientWidth = document.body.clientWidth;
                var divWidth = clientWidth - 450;
                $("#divTBDis").height(divHeight);
                $("#divTBDis").width(divWidth);
                $("#subDivTBDis").height(divHeight);

                $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
                $("#buttonStyle").css({"width": 100+"px"});
            }
           /**
            * 当窗体大小变化时，修改图例大小
            */
           window.addEventListener("resize", function () {
               if ($scope.currentDepartmentHumanChangeChart) { $scope.currentDepartmentHumanChangeChart.resize(); }
               if ($scope.currentAreaHumanChangeChart) { $scope.currentAreaHumanChangeChart.resize(); }
           });
           //初始化数据
            function initData(){
                //初始化地区
                $scope.areaList = [];
                $scope.areaMap = {};
                $scope.areaMap1 = {};
                comService.getParamList('AREA_TYPE', 'AREA_TYPE').then(function (data) {
                    $scope.areaList = data.data;
                    angular.forEach($scope.areaList, function (area) {
                        $scope.areaMap[area.param_value]=area.param_code;
                        $scope.areaMap1[area.param_code]=area.param_value;
                    });
                    $scope.initFlag++;
                    getData();
                });
                //初始化部门
				$scope.departmentList = [];
				$scope.departmentMap = {};
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentList = comService.getDepartment(data.data);
                    angular.forEach($scope.departmentList, function (department) {
                        $scope.departmentMap[department.orgCode]=department.orgName;
                    });
                    $scope.initFlag++;
                    getData();
                });
                //初始化产品线
                $scope.productLineList = [];
                $scope.productLineMap = {};
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    $scope.productLineList = data.data;
                    angular.forEach($scope.productLineList, function (productLine) {
                        $scope.productLineMap[productLine.param_code]=productLine.param_value;
                    });
                    $scope.initFlag++;
                    getData();
                });
            }
            function timeout(){
                //表示图表信息全部获取完成
                if(2 === $scope.flag){
                    setTimeout(eChartForHumanChange,500);
                }
            }
            function resetParam(){
                initTime();
                $scope.formRefer.timeInterval='季度';
            }
            function initTime(){
                var nowDate = new Date();
                var endDate = new Date().setMonth((nowDate.getMonth()-3))
                $scope.formRefer.endTime = inform.format(nowDate,"yyyy-MM-dd");
                $scope.formRefer.startTime = inform.format(endDate,"yyyy-MM-dd");
            }
            //同比间隔变动
            $scope.changeTime = function(){
                var nowDate = new Date();
                var startTime ='';
                if('季度'===$scope.formRefer.timeInterval){
                    startTime = new Date().setMonth((nowDate.getMonth()-3));
                }else if('半年度'===$scope.formRefer.timeInterval){
                    startTime = new Date().setMonth((nowDate.getMonth()-6));
                }else{
                    startTime = new Date().setMonth((nowDate.getMonth()-12));
                }
                $scope.formRefer.startTime = inform.format(startTime,"yyyy-MM-dd");
            }

            function getData(flag){
                if($scope.initFlag !== 3){
                    return;
                }
                $scope.selectFlag=flag;
                //获取部门人力变动
                $scope.departmentHumanChangeList = [];
                getHumanChangeInfo(1,deptHumanChangeData);
                //获取区域人力变动
                $scope.areaHumanChangeList = [];
                getHumanChangeInfo(2,areaHumanChangeData);
                //列表明细数据
                getChangeInfoList();
            }
            //列表明细数据
            function getChangeInfoList(){
                var urlData={
                    'currentTermDate':$scope.formRefer.endTime,
                    'lastSameTermDate':$scope.formRefer.startTime
                }
                humanResStatisticsService.getChangeInfoList(urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        //处理明细数据
                        getChangeInfoTableData(data.data);
                    } else {
                        inform.common(data.message);
                    }
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            //处理明细数据
            function getChangeInfoTableData(data){
                $scope.changeInfoList=[];
                angular.forEach(data, function (changeInfo) {
                    var weihai = changeInfo.statisDetails['0001']==null?null:changeInfo.statisDetails['0001'];
                    var beijing = changeInfo.statisDetails['0002']==null?null:changeInfo.statisDetails['0002'];
                    var xian = changeInfo.statisDetails['0003']==null?null:changeInfo.statisDetails['0003'];
                    var shenzhen = changeInfo.statisDetails['0004']==null?null:changeInfo.statisDetails['0004'];
                    var currentData={
                        'department':$scope.departmentMap[changeInfo.statisTargetName],
                        'weihaiCurrent':weihai==null ?0:weihai.currentTermEmpCount,
                        'weihaiLastSame':weihai==null ?0:weihai.lastSameTermEmpCount,
                        'weihaiChangeRate':weihai==null ?0:weihai.empChangeRate,
                        'beijingCurrent':beijing==null ?0:beijing.currentTermEmpCount,
                        'beijingLastSame':beijing==null ?0:beijing.lastSameTermEmpCount,
                        'beijingChangeRate':beijing==null ?0:beijing.empChangeRate,
                        'xianCurrent':xian==null ?0:xian.currentTermEmpCount,
                        'xianLastSame':xian==null ?0:xian.lastSameTermEmpCount,
                        'xianChangeRate':xian==null ?0:xian.empChangeRate,
                        'shenzhenCurrent':shenzhen==null ?0:shenzhen.currentTermEmpCount,
                        'shenzhenLastSame':shenzhen==null ?0:shenzhen.lastSameTermEmpCount,
                        'shenzhenChangeRate':shenzhen==null ?0:shenzhen.empChangeRate,
                    }
                    //计算部门合计
                    currentData.allCurrent = currentData.weihaiCurrent+currentData.beijingCurrent+currentData.xianCurrent+currentData.shenzhenCurrent;
                    currentData.allLastSame = currentData.weihaiLastSame+currentData.beijingLastSame+currentData.xianLastSame+currentData.shenzhenLastSame;
                    currentData.allChangeRate = currentData.allLastSame===0 ?0.00:((currentData.allCurrent-currentData.allLastSame)/currentData.allLastSame*100).toFixed(1);
                    $scope.changeInfoList.push(currentData);
                });
                //计算区域的合计
                getAreaAll();
            }
            function getAreaAll(){
                if($scope.changeInfoList.length<=0){
                    return;
                }
                var areaAll={
                    'department':'合计',
                    'weihaiCurrent':0,
                    'weihaiLastSame':0,
                    'beijingCurrent':0,
                    'beijingLastSame':0,
                    'xianCurrent':0,
                    'xianLastSame':0,
                    'shenzhenCurrent':0,
                    'shenzhenLastSame':0,
                    'allCurrent':0,
                    'allLastSame':0
                };
                angular.forEach($scope.changeInfoList, function (changeInfo) {
                    areaAll.weihaiCurrent = areaAll.weihaiCurrent+changeInfo.weihaiCurrent;
                    areaAll.weihaiLastSame = areaAll.weihaiLastSame+changeInfo.weihaiLastSame;
                    areaAll.beijingCurrent = areaAll.beijingCurrent+changeInfo.beijingCurrent;
                    areaAll.beijingLastSame = areaAll.beijingLastSame+changeInfo.beijingLastSame;
                    areaAll.xianCurrent = areaAll.xianCurrent+changeInfo.xianCurrent;
                    areaAll.xianLastSame = areaAll.xianLastSame+changeInfo.xianLastSame;
                    areaAll.shenzhenCurrent = areaAll.shenzhenCurrent+changeInfo.shenzhenCurrent;
                    areaAll.shenzhenLastSame = areaAll.shenzhenLastSame+changeInfo.shenzhenLastSame;
                    areaAll.allCurrent = areaAll.allCurrent+changeInfo.allCurrent;
                    areaAll.allLastSame = areaAll.allLastSame+changeInfo.allLastSame;
                });
                //计算增减幅
                areaAll.weihaiChangeRate = areaAll.weihaiLastSame===0 ?0.00:((areaAll.weihaiCurrent-areaAll.weihaiLastSame)/areaAll.weihaiLastSame*100).toFixed(1);
                areaAll.beijingChangeRate = areaAll.beijingLastSame===0 ?0.00:((areaAll.beijingCurrent-areaAll.beijingLastSame)/areaAll.beijingLastSame*100).toFixed(1);
                areaAll.xianChangeRate = areaAll.xianLastSame===0 ?0.00:((areaAll.xianCurrent-areaAll.xianLastSame)/areaAll.xianLastSame*100).toFixed(1);
                areaAll.shenzhenChangeRate = areaAll.shenzhenLastSame===0 ?0.00:((areaAll.shenzhenCurrent-areaAll.shenzhenLastSame)/areaAll.shenzhenLastSame*100).toFixed(1);
                areaAll.allChangeRate = areaAll.allLastSame===0 ?0.00:((areaAll.allCurrent-areaAll.allLastSame)/areaAll.allLastSame*100).toFixed(1);
                $scope.changeInfoList.push(areaAll);
            }
            //下载数据
            $scope.downloadHumanChangeInfo = function(){
                 inform.modalInstance("确定要下载人力变动数据表吗？").result.then(function() {
                     inform.downLoadFile('hr_download/for_empcount_statis_detail',$scope.changeInfoList,'人力变动数据表'+inform.format(new Date(),'yyyy-MM-dd')+".xlsx");
                });
            }

            //获取部门/区域人力变动
            function getHumanChangeInfo(type,backFunction){
                var urlData={
                    'dimensionType':type,
                    'currentTermDate':$scope.formRefer.endTime,
                    'lastSameTermDate':$scope.formRefer.startTime
                }
                humanResStatisticsService.getHumanChangeInfo(urlData).then(backFunction,
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            //处理部门人力变动数据
            function deptHumanChangeData(data){
                if (data.code===AgreeConstant.code) {
                    $scope.departmentHumanChangeList = data.data;
                    if($scope.selectFlag){
                        eChartShowForHumanChange($scope.currentDepartmentHumanChangeChart,'系研',$scope.departmentHumanChangeList);
                    }
                } else {
                    inform.common(data.message);
                }
                $scope.flag++;
                timeout();
            }
            //处理区域人力变动数据
            function areaHumanChangeData(data){
                if (data.code===AgreeConstant.code) {
                    $scope.areaHumanChangeList = data.data;
                    if($scope.selectFlag){
                        eChartShowForHumanChange($scope.currentAreaHumanChangeChart,'区域',$scope.areaHumanChangeList);
                    }
                } else {
                    inform.common(data.message);
                }
                $scope.flag++;
                timeout();
            }

            //人力变动图表创建
            function eChartForHumanChange(){
                $scope.currentDepartmentHumanChangeChart = echarts.init(document.getElementById('departmentHumanChangeChart'));
                $scope.currentAreaHumanChangeChart = echarts.init(document.getElementById('areaHumanChangeChart'));
                eChartShowForHumanChange($scope.currentDepartmentHumanChangeChart,'系研',$scope.departmentHumanChangeList);
                eChartShowForHumanChange($scope.currentAreaHumanChangeChart,'区域',$scope.areaHumanChangeList);
            }

            //人力变动图表显示
            function eChartShowForHumanChange(currentChart,type,data){
                //处理变动人力获取到的返回值
                var xData=[];
                //当期
                var currentTermEmp=[];
                //同期
                var lastSameTermEmp=[];
                //增减幅
                var empChangeRate=[];
                var rotate = 0;
                var mapData = {};
                if(type==='系研'){
                    rotate = 37;
                    mapData = $scope.departmentMap;
                }
                if(type==='区域'){
                    mapData = $scope.areaMap1;
                }
                angular.forEach(data, function (changePerson) {
                    xData.push(mapData[changePerson.statisTargetName]==null? changePerson.statisTargetName:mapData[changePerson.statisTargetName]);
                    currentTermEmp.push(changePerson.currentTermEmpCount);
                    lastSameTermEmp.push(changePerson.lastSameTermEmpCount);
                    empChangeRate.push(changePerson.empChangeRate);
                });

                var option = {
                  title:{
                      text:type+"人力变动",
                      textStyle:{
                          fontSize: 12,
                          color: '#333'
                      }
                  },
                  tooltip: {
                    trigger: 'axis',
                    formatter: formatterCall,
                    axisPointer: {
                      type: 'cross',
                      crossStyle: {
                        color: '#999'
                      }
                    }
                  },
                  legend: {
                    data: ['当期', '同期', '增减幅']
                  },
                  xAxis: [
                    {
                      type: 'category',
                      data:xData,
                      axisPointer: {
                        type: 'shadow'
                      },
                      axisLabel:{
                        rotate:rotate
                      }
                    }
                  ],
                  yAxis: [
                    {
                      type: 'value',
                      name: '人数',
                      axisLabel: {
                        formatter: '{value}'
                      }
                    },
                    {
                      type: 'value',
                      name: '增减幅',
                      axisLabel: {
                        formatter: '{value}%'
                      }
                    }
                  ],
                  series: [
                    {
                      name: '当期',
                      type: 'bar',
                      barGap: 0,
                      label: {
                        show: true,
                      },
                      data: currentTermEmp
                    },
                    {
                      name: '同期',
                      type: 'bar',
                      label: {
                        show: true,
                      },
                      data: lastSameTermEmp
                    },
                    {
                      name: '增减幅',
                      type: 'line',
                      yAxisIndex: 1,
                      label:{
                          show:true,
                          formatter: '{c}%'
                      },
                      data: empChangeRate
                    }
                  ]
                };
                currentChart.setOption(option, true);
            }
        //自定义鼠标悬浮样式
		 function formatterCall (params, ticket, callback) {
            var htmlStr = '';
            for(var i=0;i<params.length;i++){
                var param = params[i];
                var xName = param.name;//x轴的名称
                var seriesName = param.seriesName;//图例名称
                var value = param.value;//y轴值
                var color = param.color;//图例颜色
                if(i===0){
                    htmlStr += xName + '<br/>';//x轴的名称
                }
                htmlStr +='<div>';
                //为了保证和原来的效果一样，这里自己实现了一个点的效果
                htmlStr += '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:'+color+';"></span>';
                //圆点后面显示的文本
                htmlStr += seriesName;
                htmlStr += '<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">';
                if('增减幅'===seriesName){
                    htmlStr += +value+ '%';
                }else{
                    htmlStr += +value;
                }

                htmlStr += '</span>';
                htmlStr += '</div>';
            }
            return htmlStr;
         }

            //返回首页
            $scope.goBack = function(){
                $state.go('app.office.humanResStatistics',{type:1});
            }

      }]);
})();
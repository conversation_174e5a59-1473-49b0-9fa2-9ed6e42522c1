(function () {
    app.controller("adviceController", ['adviceService', '$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant',
        function (adviceService, $rootScope, $scope, inform, Trans, AgreeConstant) {
            /**
             * *************************************************************
             *             初始化部分                                       开始
             * *************************************************************
             */
            $scope.formInput = {   
                department: '',   //部门名称
                initiator: '',	//发起人
                title: '',		  //流程标题
                revProStatus: '',//流程状态
                startTime: '',//开始 时间
                endTime: ''//结束 时间
            };

            $scope.datepicker = {};
            $scope.toggleMin = toggleMin;
            toggleMin();

            $scope.pages = {
                pageNum: '', 		// 分页页数
                size: '', 			// 分页每页大小
                total: '' 			// 数据总数
            };
            $scope.pages = inform.initPages(); 	// 初始化分页数据
            $scope.getData = getData; 			// 分页相关函数

            getData($scope.pages.pageNum);		//在刷新页面时调用该方法

            //初始化页面信息
            initPages();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            //设置时间控件的当前时间
            function toggleMin() {
                $scope.datepicker.currentDate = $scope.datepicker.currentDate ? null : new Date();
            }

            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 65);
            }

            //重置
            $scope.rest = function () {
                $scope.formInput.department = '';
                $scope.formInput.title = '';
                $scope.formInput.initiator = '';	//发起人
                $scope.formInput.revProStatus = '';//是否采纳
                $scope.formInput.startTime = '';//开始时间
                $scope.formInput.endTime = '';//结束时间
            };

            function initPages() {

                //获取部门
                $scope.departmentList = [];
                adviceService.getDeptList().then(function (data) {
                    if (data.data) {
                        $scope.departmentList = data.data;
                    }
                });

            }

            /**
             * 查询开始时间
             */
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;
                $scope.openedEnd = false;
                $scope.insertTime = false;
                $scope.upTimed = false;
            };

            /**
             * 查询结束时间
             */
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
                $scope.insertTime = false;
                $scope.upTimed = false;
            };

            //获取所有数据以分页的形式
            function getData(pageNum) {
                $scope.itemList = [];
                var urlData = {
                    'department': $scope.formInput.department,  			//部门名称
                    'title': $scope.formInput.title,
                    'initiator': $scope.formInput.initiator,
                    'revProStatus': $scope.formInput.revProStatus,
                    'startTime': inform.format($scope.formInput.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formInput.endTime, 'yyyy-MM-dd'),
                    'currentPage': pageNum, 								// 分页页数
                    'pageSize': $scope.pages.size    						// 分页每页大小
                };
                adviceService.getAdviceByMap(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            var jsonData = data.data;
                            $scope.itemList = jsonData.list;
                            if ($scope.itemList.length === 0) {
                                $scope.pages = inform.initPages(); 			//初始化分页数据
                            } else {
                                // 分页信息设置
                                $scope.pages.total = jsonData.total;		// 页面总数
                                $scope.pages.star = jsonData.startRow;  	//页面起始数
                                $scope.pages.end = jsonData.endRow;  		//页面大小数
                                $scope.pages.pageNum = jsonData.pageNum;  	//页面页数
                            }

                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });

            }

            //同步合理化建议信息
            $scope.adviceSynchronize = function () {
                var urlDate = {
                    'startTime': inform.format($scope.formInput.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formInput.endTime, 'yyyy-MM-dd')
                };
                adviceService.adviceSynchronize(urlDate).then(function (data) {
                    layer.confirm(data.message,{
                        title:false,
                        btn:['确定']
                    },function(result){
                        layer.close(result);
                        getData(1);
                    });
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });

            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

(function() {
    'use strict';
  app.factory('humanResStatisticsService', humanResStatisticsService);
  humanResStatisticsService.$inject=["HttpService",'$rootScope'];

  function humanResStatisticsService(HttpService,$rootScope){
    var service={
        getEmpCountTotal:getEmpCountTotal,
        getDepartmentAreaPersonInfo:getDepartmentAreaPersonInfo,
        getActualTimePersonInfo:getActualTimePersonInfo,
        getTotalData:getTotalData,
        getDepartmentEmpQuitInfo:getDepartmentEmpQuitInfo,
        getEmpQuitStatisInfo:getEmpQuitStatisInfo,
        getPersonOffIn:getPersonOffIn,
        getHumanChangeInfo:getHumanChangeInfo,
        getChangeInfoList:getChangeInfoList
    };
    return service;

    /**
     * 获取系研在职员工数量
     */
    function getEmpCountTotal() {
        return HttpService.get($rootScope.getWaySystemApi + 'hr_data/get_empcounttotal_currentday');
    }
    //获取部门区域员工数量统计
    function getDepartmentAreaPersonInfo(){
        return HttpService.get($rootScope.getWaySystemApi + 'hr_data/get_empcount_dept_region_currentday');
    }
    //获取部门、区域、产品线实时人力数据
    function getActualTimePersonInfo(){
        return HttpService.get($rootScope.getWaySystemApi + 'hr_data/get_empcount_one_dimension');
    }
    //获取系研汇总数据：期初、期末、平均等
    function getTotalData(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_data/get_humanresource_gatherdata_all',urlData);
    }
    //获取部门有效人力
    function getDepartmentEmpQuitInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_attend/get_empcounteffective_groupbydept',urlData);
    }
    //获取离职统计
    function getEmpQuitStatisInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_data/get_empquit_statisinfo_onedimension',urlData);
    }
    //获取入离职明细
    function getPersonOffIn(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_data/get_empchange_iteminfo',urlData);
    }
    //获取部门/区域人力变动
    function getHumanChangeInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_data/get_empchange_statisinfo',urlData);
    }
    //获取人力变动明细数据
    function getChangeInfoList(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'hr_data/get_empchange_detailinfo',urlData);
    }

  }
})();

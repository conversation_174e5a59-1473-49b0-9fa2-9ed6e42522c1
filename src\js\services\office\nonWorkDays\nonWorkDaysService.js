/*
* @Author: fubaole
* @Date:   2017-09-05 10:05:00
* @Last Modified by:   fubaole
* @Last Modified time: 2018-02-24 13:45:13
*/
(function () {
    'use strict';
    app.factory('nonWorkDaysService', nonWorkDaysService);
    nonWorkDaysService.$inject = ["HttpService", '$rootScope'];

    function nonWorkDaysService(HttpService, $rootScope) {
        var service = {
            getNonWorkDaysByMonth: getNonWorkDaysByMonth,
            addNonWorkDaysByYear: addNonWorkDaysByYear,
            getWorkDaysInTimeQuantum: getWorkDaysInTimeQuantum
        };
        return service;


        function getNonWorkDaysByMonth(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'NonWorkDaysAction/getNonWorkDaysByMonth', urlData);
        }

        function addNonWorkDaysByYear(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'NonWorkDaysAction/addNonWorkDaysByYear', urlData);
        }

        /**
         * 获取时间段内工作日天数（部门、产品线工时统计中使用，勿删）
         */
        function getWorkDaysInTimeQuantum(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'NonWorkDaysAction/getWorkDaysInTimeQuantum', urlData);
        }

    }
})();

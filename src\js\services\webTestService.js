(function() {
	'use strict';
	app
	.factory('WebTestService', webTestService);
	webTestService.$inject=['HttpService','$rootScope'];
	function webTestService(HttpService,$rootScope){
		var service={
			getClientType:getClientType,
			getClientTypeByInputInfo:getClientTypeByInputInfo,
			addClientType:addClientType,
			updateClientType:updateClientType,
            delClientTypeByCode:delClientTypeByCode,
            getCookie:getCookie
		};
		return service;
    function getCookie(name) 
            { 
                var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
             
                if(arr=document.cookie.match(reg))
             
                    return unescape(arr[2]); 
                else 
                    return null; 
            } 
        function getClientType(){
			return HttpService.post($rootScope.gateInfoApi+'lms/clientType/selectAll?name='+getCookie('name'),{});
		}
		function getClientTypeByInputInfo(clientTypeCode,clientTypeName){
			return HttpService.post($rootScope.gateInfoApi+'lms/clientType/select?clientTypeCode='+clientTypeCode
				+'&clientTypeName='+clientTypeName+'&name='+getCookie('name'),{});
		}
		 function addClientType(clientTypeCode,clientTypeName,clientTypeDesc){
			return HttpService.post($rootScope.gateInfoApi+'lms/clientType/add?clientTypeCode='+clientTypeCode
				+'&clientTypeName='+clientTypeName+'&clientTypeDesc='+clientTypeDesc+'&name='+getCookie('name'),{});
		}
		function updateClientType(clientTypeCode,clientTypeName,clientTypeDesc){
			return HttpService.post($rootScope.gateInfoApi+'lms/clientType/edit?clientTypeCode='+clientTypeCode
				+'&clientTypeName='+clientTypeName+'&clientTypeDesc='+clientTypeDesc+'&name='+getCookie('name'),{});
		}
        function delClientTypeByCode(clientTypeCode){
			return HttpService.post($rootScope.gateInfoApi+'lms/clientType/delete?clientTypeCode='+clientTypeCode+'&name='+getCookie('name'),{});
		}
	}
})();
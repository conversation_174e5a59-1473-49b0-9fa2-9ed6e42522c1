/*
* @Author: fubaole
* @Date:   2017-09-05 10:05:00
* @Last Modified by:   fubaole
* @Last Modified time: 2017-09-05 10:08:01
*/
(function() {
    'use strict';
  app.factory('loading', loading);

  loading.$inject=[];

  function loading(){
    var service={
      showLayer:showLayer,
      closeLayer:closeLayer,
      successTip :successTip,
      errorTip:errorTip,
      layerTip:layerTip
    };

    return service;

    // 显示遮罩层
    function showLayer(){
      return layer.load(1, {
        content:'加载中...',
        shade: [0.6,'#000'],
        success: function(layero){
          layero.find('.layui-layer-content').css({'padding-top':'70px'});
        }
      });
    }
    // 关闭遮罩层
    function closeLayer(){
      layer.closeAll();
    }
    // 加载success提示
    function successTip(){
      layer.msg('加载完成！');
    }
    // 加载error提示
    function errorTip(){
      layer.msg('加载失败！');
    }
    // 加载完成提示
    function layerTip(txt){
      layer.msg(txt);
    }
  }
})();
/*
 * @Description: 
 * @Version: 1.0
 * @Autor: yap
 * @Date: 2023-11-22 16:02:32
 * @LastEditors: yap
 * @LastEditTime: 2023-11-28 11:19:14
 */

(function() {
    'use strict';
    app.factory('ValidCodeService', ValidCodeService);
    ValidCodeService.$inject = ["HttpService", '$rootScope'];

    function ValidCodeService(HttpService, $rootScope) {
        var service = {
            testget: testget,
            getValidCodeOverview: getValidCodeOverview,
            validCodeDetail: validCodeDetail,
            codeCommitDetail: codeCommitDetail
        };
        return service;

        //for test
        function testget() {
            return 'from Service:ValidCodeService...'
        }
        
        /**
         * 1.1.1.	查询有效代码看板数据接口
         */
        function getValidCodeOverview(urlData) {
            return HttpService.get($rootScope.getWaySystemApi + 'validCode/getValidCodeOverview', urlData);
        }
        /**
         * 1.1.2.	查询统计明细数据接口
         */
        function validCodeDetail(urlData) {
            return HttpService.get($rootScope.getWaySystemApi + 'validCode/validCodeDetail', urlData);
        }
        /**
         * 1.1.3.	查询提交明细数据接口
         */
        function codeCommitDetail(urlData) {
            return HttpService.get($rootScope.getWaySystemApi + 'validCode/codeCommitDetail', urlData);
        }
    }
})();
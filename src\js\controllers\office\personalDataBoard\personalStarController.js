(function () {
    app.controller("personalStarController", ['$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'personalStarService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function ($rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, personalStarService, inform, Trans, AgreeConstant, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //页面数据
            $scope.formRefer = {};
            //标记是否是在初始化时设置快捷按钮样式
            $scope.initFlag=0;
            //标记数据获取进度
            $scope.flag = 0;
            //是否是在初始化时展示eCharts图
            $scope.eChartsFlag = 0;
            $scope.timeSelect = ['本年度','上一年度'];
            //初始化数据
            initData();
            $scope.getData = getData;
            $scope.initTime = initTime;
            /**
            * 当窗体大小变化时，修改图例大小
            */
           window.addEventListener("resize", function () {
            if ($scope.currentPersonalStarDetailChart) { $scope.currentPersonalStarDetailChart.resize(); }
            if ($scope.currentPersonalStarTopTenChart) { $scope.currentPersonalStarTopTenChart.resize(); }
            if ($scope.currentPersonalStarMonthChart) { $scope.currentPersonalStarMonthChart.resize(); }
            });

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            //初始化
            function initData(){
                //获取当前登录者的empId
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ?
                $scope.sessionEmpId : $stateParams.empId;
                //获取当前登录者的姓名
                var person = LocalCache.getObject('personDataBoardEmployee');
                $scope.formRefer.employeeName = LocalCache.getSession('employeeName');
                if(person.name){
                    $scope.formRefer.employeeName = person.name;
                }
                //默认查看本年度数据
                initTime('本年度');
                //获取缓存数据
                if(JSON.stringify(LocalCache.getObject("personal_star_formRefer")) !== '{}'){
                    $scope.formRefer = LocalCache.getObject("personal_star_formRefer");
                    LocalCache.setObject("personal_star_formRefer",{});
                    //根据缓存中的年份初始化时间
                    if($scope.formRefer.year === new Date().getFullYear()){
                        initTime("本年度");
                    }else{
                        initTime("上一年度");
                    }
                }
            }

            //初始化时间
            function initTime(flag){
                $scope.butFlag = flag;
                var date = new Date();
                var year = date.getFullYear();  //当前年份
                var month = date.getMonth();
                if('本年度' === $scope.butFlag){
                    $scope.formRefer.year = year;
                    $scope.formRefer.month = month;
                }
                if('上一年度' === $scope.butFlag){
                    $scope.formRefer.year = parseInt(year) - 1;
                    $scope.formRefer.month = 12;
                }
                if($scope.initFlag === 0){

                    setTimeout(setButton,500);
                }else {
                    setButton();
                }
                getData();
            }
            //数据全部获取后，显示数据图
            function timeout(){
                if($scope.flag === 3){
                    setTimeout(eChartForPersonalStarTopTen,500);
                    setTimeout(eChartForPersonalStarMonth,500);
                    setTimeout(eChartForPersonalStarDetail,500);
                    $scope.eChartsFlag = 1;
              }
            }

            //设置按钮颜色
            function setButton(){
                $scope.initFlag=1;
                //获取所有快捷按钮
                var buttonBoxes = document.getElementsByName("buttons");
                angular.forEach(buttonBoxes, function (but) {
                    if($scope.butFlag === but.id){
                    $("#"+but.id).css("background-color", "#16a8f8");
                    }else{
                    $("#"+but.id).css("background-color", "#CDCDC1");
                    }
                });
            }

            //跳转到我的星星明细--部门贡献详情页
            $scope.show = function(m){
                $scope.formRefer.orderRange= m.remark.substring(4);
                $scope.formRefer.scoreOrder = m.scoreOrder;
                $scope.formRefer.totalScore = m.totalScore;
                $scope.formRefer.starNum = m.starNum;
                $scope.formRefer.startDate = $scope.formRefer.year + "-01-01";
                $scope.formRefer.endDate = $scope.formRefer.year + "-12-31";
                LocalCache.setObject("personal_star_formRefer",$scope.formRefer);
                $state.go('app.personal_star_department_detail');
            }
            //返回工程师看板
            $scope.getBack = function getBack(){
                var kpiRelation = LocalCache.getObject('kpiRelation');
                $state.go('app.office.kpiRelation');
                console.log(kpiRelation);
            }

            //查询当前团队的首页信息
            function getData(){
                //查询条件
                $scope.urlData={
                    'employeeNo':$scope.formRefer.empId,
                    'year':$scope.formRefer.year,
                    'month':$scope.formRefer.month,
                    'startDate':$scope.formRefer.year + "-01-01",
                    'endDate':$scope.formRefer.year + "-12-31",
                }
                //获取星星排行榜前十数据
                selectPersonalStarTopTen();
                //获取我的星星数据
                selectPersonalStar();
                //获取我的星星个人明细数据
                selectPersonalStarDetail();
                //获取我的星星历史数据
                selectPersonalStarHis();
                //获得评审贡献获星明细
                selectMyStarReviewTopDetail();
            }
            
            //获取星星排行榜前十数据
            function selectPersonalStarTopTen(){
                $scope.personalStarTopTen=[];
                personalStarService.selectPersonalStarTopTen($scope.urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        $scope.personalStarTopTen = data.data;
                        if($scope.eChartsFlag !== 0){
                          eChartForPersonalStarTopTen();
                        }
                    } else {
                        inform.common(data.message);
                    }
                    $scope.flag++;
                    timeout();
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            //获取我的星星数据
            function selectPersonalStar(){
                $scope.personalStar={};
                personalStarService.selectPersonalStar($scope.urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        $scope.personalStar = data.data;
                        //我的总星数
                        if($scope.personalStar == null){ 
                            $scope.totalStarNumHalf = false; 
                            return ;
                        }
                        $scope.totalStarNumArray = [];
                        for (var index = 0; index < parseInt($scope.personalStar.totalStarNum); index++) {
                            $scope.totalStarNumArray[index] = index;
                        }
                        $scope.totalStarNumHalf = parseInt($scope.personalStar.totalStarNum) !== $scope.personalStar.totalStarNum;
                    } else {
                        inform.common(data.message);
                    }
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            //获取我的星星个人明细数据
            function selectPersonalStarDetail(){
                $scope.personalStarDetail=[];
                personalStarService.selectPersonalStarDetail($scope.urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.personalStarDetail = data.data;
                        angular.forEach($scope.personalStarDetail, function (eachData) {
                            if(eachData.starType === "评审贡献"){
                                $scope.reviewContributionArray = [];
                                for (var index = 0; index < parseInt(eachData.starNum); index++) {
                                $scope.reviewContributionArray[index] = index;
                                }
                                $scope.reviewContributionHalfStar = parseInt(eachData.starNum) !== eachData.starNum;
                            }
                            if(eachData.starType === "部门贡献"){
                                $scope.departmentContributionArray = [];
                                for (var index2 = 0; index2 < parseInt(eachData.starNum); index2++) {
                                $scope.departmentContributionArray[index2] = index2;
                                }
                                $scope.departmentContributionHalfStar = parseInt(eachData.starNum) !== eachData.starNum;
                            }
                            });
                        if($scope.eChartsFlag !== 0){
                            eChartForPersonalStarDetail();
                        }
                    } else {
                        inform.common(data.message);
                    }
                    $scope.flag++;
                    timeout();
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            //获取我的星星历史
            function selectPersonalStarHis(){
                $scope.personalStarHis=[];
                personalStarService.selectPersonalStarHis($scope.urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.personalStarHis = data.data;
                        if($scope.eChartsFlag !== 0){
                            eChartForPersonalStarMonth();
                        }
                    } else {
                        inform.common(data.message);
                    }
                    $scope.flag++;
                    timeout();
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            //查询评审贡献获星明细
            function selectMyStarReviewTopDetail(){
              $scope.myStarReviewTopDetail=[];
                personalStarService.selectMyStarReviewTopDetail($scope.urlData).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.myStarReviewTopDetail = data.data;
                    } else {
                        inform.common(data.message);
                    }
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            //我的星星排行榜前十eCharts
            function eChartForPersonalStarTopTen(){
                $scope.currentPersonalStarTopTenChart = echarts.init(document.getElementById('personalStarTopTenChart'));
                var dealData = [];
                var orderNumList = [];
                var data = $scope.personalStarTopTen;
                angular.forEach(data, function (eachData) {
                    dealData.push(eachData.totalStarNum);
                    orderNumList.push(eachData.orderNum);
                });
                var option = {
                    title: {
                      text: '积星榜TOP10',
                      left: "center"
                    },
                    grid: {
                      left: '3%',
                      right: '4%',
                      bottom: '3%',
                      containLabel: true
                    },
                    xAxis: {
                      type: 'value',
                      axisLabel: {
                              show: false // 不显示x轴标签
                          }
                    },
                    yAxis: {
                      type: 'category',
                      axisTick: { show: false }, // y轴刻度线
                      inverse:true,
                      data: orderNumList
                    },
                    series: [
                      {
                        type: 'bar',
                        itemStyle: {
                          //实现颜色渐变
                          color: function (params) {
                            return 'rgba(4, 75, 200, ' + (1 - 0.1 * params.dataIndex) + ')';
                          }
                        },
                        label: {
                          normal:{
                            show: true,
                            position: "right"
                          }
                        },
                        data: dealData
                      }
                    ]
                  };
                  $scope.currentPersonalStarTopTenChart.setOption(option,true);
            }

            //我的星星月排名eCharts
            function eChartForPersonalStarMonth(){
                $scope.currentPersonalStarMonthChart = echarts.init(document.getElementById('personalStarMonthChart'));
                var dealData = [];
                angular.forEach($scope.personalStarHis, function (eachData) {
                    //获取月份，月排名
                    if(eachData.orderNum === 0){
                        eachData.orderNum = null;
                    }
                    dealData.push(eachData.orderNum);
                });
                var option = {
                    title: {
                      text: "月排名趋势图",
                      left: "center"
                    },
                    xAxis: {
                      type: 'category',
                      axisTick: { show: false }, // x轴刻度线
                      axisLabel: { interval: 0},
                      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
                    },
                    yAxis: {
                      type: 'value',
                      inverse:true,
                      min:1
                    },
                    series: [
                      {
                        data: dealData,
                        type: 'line',
                        lineStyle:{
                            color:'#ED8137'
                        },
                        symbolSize:0
                      }
                    ]
                  };
                  $scope.currentPersonalStarMonthChart.setOption(option,true);
            }

            //我的星星分布eCharts
            function eChartForPersonalStarDetail(){
                $scope.currentPersonalStarDetailChart = echarts.init(document.getElementById('personalStarDetailChart'));
                var dealData = [];
                var data = $scope.personalStarDetail;
                angular.forEach(data, function (eachData) {
                    dealData.push({
                        name: eachData.starType,
                        value: eachData.starNum
                    })
                });
                var option = {
                     // 设置图表的标题
                     title: {
                        text: '我的星数分布',
                        left: 'center'
                      },
                      tooltip: {
                        trigger: 'item'
                      },
                    // 设置图表的图例
                    legend: {
                        orient: 'vertical',
                        left: 'left'
                      },
                    //设置图表数据
                    series: [
                        {
                          name: '星星数量',
                          type: 'pie',
                          radius: '50%',
                          data: dealData,
                          emphasis: {
                            itemStyle: {
                              shadowBlur: 10,
                              shadowOffsetX: 0,
                              shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                          }
                        }
                      ],
                      emphasis: {
                        itemStyle: {
                          shadowBlur: 10,
                          shadowOffsetX: 0,
                          shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                      }
                }
                $scope.currentPersonalStarDetailChart.setOption(option,true);
            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

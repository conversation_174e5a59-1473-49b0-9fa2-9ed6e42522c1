(function () {
    'use strict';
    app.factory('internalDemandBoardService', internalDemandBoardService);
    internalDemandBoardService.$inject = ["HttpService", '$rootScope'];

    function internalDemandBoardService(HttpService, $rootScope) {
        var service = {
            getInternalDemandBoardTotal: getInternalDemandBoardTotal,
            getInternalDemandByType: getInternalDemandByType
        };
        return service;

        //获取内部客户需求指标
        function getInternalDemandBoardTotal(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'DeptInternalDemand/getInternalDemandBoardTotal', urlData);
        }

        //获取不同内部客户需求的详情信息
        function getInternalDemandByType(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'DeptInternalDemand/getInternalDemandByType', urlData);
        }

    }
})();
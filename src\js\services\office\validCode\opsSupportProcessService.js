(function () {
    'use strict';
    app.factory('opsSupportProcessService', opsSupportProcessService);
    opsSupportProcessService.$inject = ["HttpService", '$rootScope'];

    function opsSupportProcessService(HttpService, $rootScope) {
        var service = {
            getOpsSupportProcess:getOpsSupportProcess,
            getApprovalTypes:getApprovalTypes
        };
        return service;
        /**
         * 获取运维支持流程
         */
        function getOpsSupportProcess(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'opsSupportProcess/getOpsSupportProcess', urlData);
        }
        /**
         * 获取运维支持流程类型
         */
        function getApprovalTypes(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'opsSupportProcess/getOpsApprovalTypes', urlData);
        }


       
    }
})();
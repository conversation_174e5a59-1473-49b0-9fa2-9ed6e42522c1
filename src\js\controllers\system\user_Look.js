/*
 * @Author: fubaole
 * @Date:   2018-01-19 11:27:57
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-06 19:01:50
 */
(function() {
  'use strict';
  app.controller("user_Look", ['$rootScope', '$scope', '$state', '$stateParams', 'inform', 'Trans', 'SystemService', 'AgreeConstant','ConfigService',
    function($rootScope, $scope, $state, $stateParams, inform, Trans, SystemService, AgreeConstant,ConfigService) {
      $scope.regionList = [];
      getData(); // 获取详情数据
      getRegion(); // 获取地域数据
      getRole(); // 获取角色数据
      var Leftsetting = angular.copy(ConfigService.dataConfig);
      // Leftsetting.callback.beforeClick = nodeSelect; // 点击节点前回调
      // 根据ID 获取用户信息
      function getData() {
        SystemService.getUser($stateParams.userId)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.userList = data.result;
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 根据ID 获取地域信息
      function getRegion() {
        SystemService.getRegionByUserId($stateParams.userId)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              // $scope.regionList = data.result;
              angular.forEach(data.result, function(res, index) {
                var jsonTree = {
                    "id": res.regionId,
                    "pId": res.parentId,
                    "name": res.regionName,
                    "open":  false,
                };
                data.result[index] = angular.extend(jsonTree, res);
                $scope.regionList.push(data.result[index]);
              });
              $scope.regionList = inform.unique($scope.regionList);
              $.fn.zTree.init($("#treeDemo"), Leftsetting, $scope.regionList);
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 根据ID 获取角色信息
      function getRole() {
        SystemService.getRoleListMapByUserId($stateParams.userId)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.roleList = data.result;
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

    }
  ]);
})();
(function () {
    app.controller("trainPlanPersonUp", ['comService', '$rootScope', '$scope', '$state', '$stateParams', '$modal', 'trainPlanService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, $state, $stateParams, $modal, trainPlanService, inform, Trans, AgreeConstant, LocalCache, $http) {
    	/**
         * *************************************************************
         *             初始化部分                                 开始
         * *************************************************************
         */
        //初始化过程flag,变为2时证明初始化结束,可以给修改页面赋值
        var initFlag = 0;
        //培训id
        var trainId = $stateParams.trainId;
        //人员id
        var personId = $stateParams.personId;
        //人员姓名
        var person = $stateParams.person;
        // 正则校验配置
        $scope.limitList = AgreeConstant.limitList;
        //初始化页面信息
        init();
        /**
         * *************************************************************
         *              初始化部分                                 结束
         * *************************************************************
         */

        /**
         * *************************************************************
         *              方法声明部分                                 开始
         * *************************************************************
         */
        /**
         * 初始化部门列表
         */
        function init() {
        	//获取评价内容
    		$scope.evaluationList = [];
    		comService.queryEffectiveParam('TRAININGEVALUATE','TRAININGEVALUATE').then(function(data) {
        		if (data.data) {
        			$scope.evaluationList =  data.data;
                    for (var i = 0; i < $scope.evaluationList.length; i++) {
                        if($scope.evaluationList[i].paramValue !== '收获' && $scope.evaluationList[i].paramValue !== '待改进') {
                            $scope.evaluationList[i].paramValue += '评价';
                        }
                    }
        			initFlag++;
        			getData();
        		}
            });
        }
        /**
         * 如果初始化结束,则根据修改传入id查询
         */
        function getData(){
            if (initFlag !== 1) {
            	return;
            }
            var param = {
            	"trainId":trainId,
            	"participantId":personId
            };
            trainPlanService.getPersonEvaluation(param).then(function (data) {
                if(data.code === AgreeConstant.code){
                	$scope.changeParamDetail = data.data;
                    switch ($scope.changeParamDetail.join) {
                        case '0':$scope.changeParamDetail.join = '出勤';
                                break;
                        case '1':$scope.changeParamDetail.join = '请假';
                                break;
                        case '2':$scope.changeParamDetail.join = '旷课';
                                break;
                        default: $scope.changeParamDetail.join = '';
                    }
                	angular.forEach(data.data.evaluates, function (evaluate, index) {
                		//为评价内容赋值
                		document.getElementById(evaluate.evaluationContent).value = evaluate.evaluationScore;
           	 		});
            	}
        	});
        }
        /**
         * 保存信息
         */
        $scope.upData = function () {
        	//如果请假
        	if ("1"===$scope.changeParamDetail.join && (null==$scope.changeParamDetail.reason || ""===$scope.changeParamDetail.reason)){
        		inform.common("当前人员请假，请输入请假原因");
        		return;
        	}
        	if ("0"===$scope.changeParamDetail.join){
        		$scope.changeParamDetail.reason='';
        	}
        	var evaluates = [];
        	angular.forEach($scope.evaluationList, function (evaluation, index) {
   	 			//根据描述，获取值
   	 			var value = document.getElementById(evaluation.paramCode).value;
   	 			var evaluate = {
   	 				'evaluationContent':evaluation.paramCode,
   	 				'evaluationScore':value,
   	 				'trainingId':trainId,
   	 				'employeeId':personId
   	 			}
   	 			evaluates.push(evaluate);
   	 		});
        	var urlData = {
        		'trainId':trainId,
        		'participantId':personId,
        		'participant':person,
        		'evaluates':evaluates,
        		'join':$scope.changeParamDetail.join,
        		'reason':$scope.changeParamDetail.reason,
        		'improvement':$scope.changeParamDetail.improvement,
        		'harvest':$scope.changeParamDetail.harvest
        	}
            trainPlanService.upEvaluates(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    layer.confirm(data.message,{
                        title:false,
                        btn:['确定']
                    },function(result){
                        layer.close(result);
                        $scope.goback();
                    });
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        };
        $scope.goback = function () {
            $state.go("app.office.trainPlanManagement",{type:2});
        };
        /**
         * *************************************************************
         *              方法声明部分                                 结束
         * *************************************************************
         */

        }]);
})();

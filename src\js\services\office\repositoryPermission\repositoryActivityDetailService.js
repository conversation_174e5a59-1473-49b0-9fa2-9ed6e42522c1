(function() {
    'use strict';
  app.factory('repositoryActivityDetailService', repositoryActivityDetailService);
  repositoryActivityDetailService.$inject=["HttpService",'$rootScope'];

  function repositoryActivityDetailService(HttpService,$rootScope){
    var service={
      getAuthorizationDetail:getAuthorizationDetail,
    };
    function getAuthorizationDetail(urlData) {
      return HttpService.get($rootScope.getWaySystemApi+'storageManage/getAuthorizationDetail',urlData);
    }
    return service;
  }
})();

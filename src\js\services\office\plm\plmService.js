(function() {
    'use strict';
    app.factory('plmService', plmService);
    plmService.$inject = ["HttpService", '$rootScope'];

    function plmService(HttpService, $rootScope) {
        var service = {
        		uploadPlantData: uploadPlantData,
        		queryProjects: queryProjects
        };
        return service;
        /**
         * 上传计划数据文件
         */
        function uploadPlantData(urlData) {
            	return HttpService.post($rootScope.getWaySystemApi + 'plm/uploadPlantData', urlData);
        }
        /**
         * 获取没有关闭的项目清单
         */
        function queryProjects() {
            	return HttpService.get($rootScope.getWaySystemApi + 'plm/queryProjects',null);
        }

    }
})();
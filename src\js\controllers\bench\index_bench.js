/*
 * @Author: fubaole
 * @Date:   2018-01-08 10:06:19
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-04-02 16:59:35
 */
(function() {
  'use strict';
  angular.module('app')
    .controller("index_bench", ['comService','personalBadgeService','kpiRelationService','$rootScope', '$scope', 'inform', '$stateParams','$compile', '$state', 'LocalCache', 'Trans', 'SystemService','$modal','AgreeConstant',
      function(comService,personalBadgeService,kpiRelationService,$rootScope, $scope, inform, $stateParams,$compile, $state, LocalCache, Trans, SystemService,$modal,AgreeConstant) {

        $scope.defaultImg = AgreeConstant.defaultImg;
        $scope.getClass = getClass; // 设置区块宽度
        $scope.setDefaultPage = setDefaultPage; // 设置股默认页
        $scope.open = open; // 删除配置项
        $scope.reload = reload; // 重新加载
        $scope.showFlag = true;//导航‘首页’是否显示
        $scope.showKpiAndAssess = false;//个人看板是否显示kpi和考核sheet页
        $scope.showKpiRelation = false;//个人看板是否显示考核关系sheet页
        $scope.showXyKpiDefault = false;//是否默认展示系研看板
        $scope.showAttendance = false;//是否默认展示系研看板
        $scope.showOpsData = false;//是否默认展示运维数据
        $scope.showQaData = false;//是否默认展示测试数据
        $scope.personalBadgeNum = 0;//个人徽章数量
		//判断按钮是否具有权限
        getButtonPermission();
        getDefaultPage();
        getData();
        checkShowAttendance();

        // 轮播图数据初始化
         $scope.slides = [];
         $scope.slides[0] = [];
         $scope.slides[1] = [];
         $scope.slides[0].push({ pageImage: '../../../img/basic/bench.png', pageName: '首页123' });
         $scope.slides[0].push({ pageImage: '../../../img/basic/bench.png', pageName: '设置页456' });
         $scope.slides[0].push({ pageImage: '../../../img/basic/bench.png', pageName: '设置页789' });
         $scope.slides[0].push({ pageImage: '../../../img/basic/bench.png', pageName: '设置页000' });
         $scope.slides[1].push({ pageImage: '../../../img/basic/bench.png', pageName: '设置页0' });
         $scope.slides[1].push({ pageImage: '../../../img/basic/bench.png', pageName: '首页1' });
         $scope.slides[1].push({ pageImage: '../../../img/basic/bench.png', pageName: '设置页2' });
         $scope.slides[1].push({ pageImage: '../../../img/basic/bench.png', pageName: '设置页3' });

        /**
         * 获取按钮权限
         */
         function getButtonPermission(){
              var buttons = {
                'Button-personalDataBoard-kpiAndAssess':'showKpiAndAssess'
              //  'Button-personalDataBoard-xyKpiDefault':'showXyKpiDefault'
              };
              var urlData = {
                 'userId':LocalCache.getSession("userId"),
                 'parentPermission':'personalDataBoard',
                 'buttons':buttons
              };
              comService.getButtonPermission(urlData,$scope);
         }
        // 获取默认首页
        function checkShowAttendance() {
            var currentUserName = LocalCache.getSession("currentUserName");
            var person = LocalCache.getObject('personDataBoardEmployee');
            if(person.loginName){
                $scope.showAttendance = person.loginName !== currentUserName;
            }else {
                $scope.showAttendance = false;
            }
        }
        function getDefaultPage(){
            var kpiRoleCode = LocalCache.getSession("kpiRoleCode");
            var roleList = JSON.parse(LocalCache.getSession('roleList'));
            var roles = "";
            angular.forEach(roleList,function (item) {
                roles = roles + item.roleName + ","
                if(item.roleName==='系研领导' || item.roleName==='部门领导'){
                    $scope.showXyKpiDefault=true;
                }
            });
            //开发工程师才展示大屏
/*            var kpiCodeMap = {"ROLE_LIBRARY_9":4,"ROLE_LIBRARY_10":4,"ROLE_LIBRARY_11":4};
            $scope.type = kpiCodeMap[kpiRoleCode];
            if (!$scope.type && $state.params.empId) {
              //从员工考核界面跳转过来的默认显示开发工程师KPI界面
               $scope.type = 4;
            }*/
            $scope.type = 17;
            // 如果参数中含有tagType，根据参数内的数据跳转
            if($stateParams.tagType){
              $scope.type = $stateParams.tagType;
            }
            $scope.employeeName = LocalCache.getSession('employeeName');

            var title = '';
            var department = '';
            var personDataBoardFlag = LocalCache.getObject('personDataBoardFlag');
      
            if($state.params.empId){
                $scope.showFlag = false;
                $scope.showXyKpiDefault=false;
                if($state.params.empId === LocalCache.getSession("employeeId")){
                    $scope.showKpiAndAssess =true;
                    $scope.showKpiRelation = true;
                    department = LocalCache.getSession("departmentName");  
                    
                }else {
                    var person = LocalCache.getObject('personDataBoardEmployee');
                    if(person.name){
                        $scope.employeeName = person.name;
                    }
                    department = person.department;
                    roles = person.title;
                }

            }else {
                if(personDataBoardFlag === 'kpiRelation'){
                    $scope.type = 12;
                }

                LocalCache.setObject('personDataBoardFlag', '');
                $scope.showKpiAndAssess =true;
                $scope.showValidcode = true;
                department = LocalCache.getSession("departmentName");
                setShowKpiRelation(LocalCache.getSession("employeeName"));
                LocalCache.setObject('personDataBoardEmployee', {});
            }
            $scope.showValidcode = true;
            checkShowQaData(department);

          SystemService.getDefaultPageByUserId()
          .then(function(data){
            if (data.code===AgreeConstant.resultCode) {
                if(data.result.rowList && data.result.rowList.length){
                  $scope.blocks = data.result.rowList[0].containerList[0].blockContentList;
                }else{
                  getDefaultPageByRoleId();
                }
              } else {
                inform.common(data.message);
              }
            }, function(error) {
              inform.common(Trans("tip.requestError"));
            });
           //查询个人徽章数量
          var badgeParam = {employeeId:$state.params.empId ? $state.params.empId:LocalCache.getSession("employeeId")};
          personalBadgeService.queryPersonalBadgeNum(badgeParam).then(function(data){
            if (data.code=== AgreeConstant.code) {
                $scope.personalBadgeNum = data.data;
              }
            });
        }

        //判断是否显示运维、测试数据 sheet页
        function checkShowQaData(department){
          if(department && department.indexOf ("软测")!=-1){
            $scope.showOpsData = true;
            $scope.showQaData = true;
          }else{
            $scope.showOpsData = false;
            $scope.showQaData = false;
          }

        }

        //判断是否显示 被考核人信息 sheet页
        function setShowKpiRelation(person) {
            var urlData = {
                'person':person,
                'page':1,
                'pageSize':200
            };
            kpiRelationService.getKpiRelationByPage(urlData).then(function (data) {
                if (data.code===AgreeConstant.code) {
                    $scope.dataList = data.data.list;
                    if ($scope.dataList.length!=0) {
                        $scope.showKpiRelation = true;
                    }
                }
            });
        }
        // 通过roleId 获取默认首页
        function getDefaultPageByRoleId() {
          SystemService.getDefaultPageByRoleId(LocalCache.getSession('roleId'))
          .then(function(data){
            if (data.code===AgreeConstant.resultCode) {
                if(data.result.rowList && data.result.rowList.length){
                  $scope.blocks = data.result.rowList[0].containerList[0].blockContentList;
                }
              } else {
                inform.common(data.message);
              }
            }, function(error) {
              inform.common(Trans("tip.requestError"));
            });
        }

        // 占屏比例
        function getClass(str) {
          var classname = '';
           switch (str) {
            case AgreeConstant.widthTwoOne:
              classname = 'col-sm-6'; // 1/2
              break;
            case AgreeConstant.widthThreeOne:
              classname = 'col-sm-4'; // 1/3
              break;
            case AgreeConstant.widthTwoThree:
              classname = 'col-sm-8'; // 2/3
              break;
            case AgreeConstant.widthFourOne:
              classname = 'col-sm-3'; // 1/4
              break;
            case AgreeConstant.widthThreeFour:
              classname = 'col-sm-9'; // 3/4
              break;
            default:
              classname = 'col-sm-12'; // 1
          }
          return classname;
        }

      // 处理数据
      function dealData() {
        var slides = $scope.slides = [];
        if($scope.resultData.length>0){
          for(var i=0;i<$scope.resultData.length/4;i++){
            slides[i] = [];
            for(var j = 0;j<4;j++){
              if($scope.resultData[i*4+j]){
                slides[i].push($scope.resultData[i*4+j]);
              }
            }
            if(slides[i].length<4){
              slides[i].push({pageImage:AgreeConstant.defaultAdd});
            }
          }
          if(slides[slides.length-1].length===4 && slides[slides.length-1][slides[slides.length-1].length-1].pageImage != AgreeConstant.defaultAdd){
            slides[slides.length] =[{pageImage:AgreeConstant.defaultAdd}];
          }
        }else{
          slides.push([{pageImage:AgreeConstant.defaultAdd}]);
        }
      }

      // 设置弹层
      function getData() {
        SystemService.getPageListByUserId()
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              angular.forEach(data.result.pageList, function(data,index,array){
                if(data.pageImage){
                  data.pageImage = $rootScope.imageDownload+data.pageImage;
                }
              });
              $scope.resultData = data.result.pageList;
              $scope.defaultPageId = data.result.defaultPageId;
              dealData();
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }
      // 设置默认页
      function setDefaultPage(id) {
        SystemService.setDefaultPageByUserId(id)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              inform.common(data.message);
              $state.go('app.index_bench',{},{reload:true});
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 删除配置项
      function removePerPage(id) {
        SystemService.removePageByUserId(id)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              inform.common(data.message);
              $state.go('app.index_bench',{ empId: ""},{reload:true});
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 删除配置页
      function open(item) {
        var modalInstance = $modal.open({
          templateUrl: 'myModalContent.html',
          controller: 'ModalInstanceCtrl',
          size: "sm",
          resolve: {
            items: function() {
              return Trans("common.deleteTip");
            }
          }
        });

        modalInstance.result.then(function() {
          if (item) {
            removePerPage(item);
          }
        });
      }

      // 重载
      function reload(id){
        $scope.$broadcast('reload', id);
      }


      }
    ]);
})();

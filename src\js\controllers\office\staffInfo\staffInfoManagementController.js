(function() {
    'use strict';
    app.controller("staffInfoManagementController", ['comService','$rootScope', '$stateParams', '$scope', 'staffInfoService','$modal','inform', 'Trans', 'AgreeConstant','$state', 'LocalCache', '$http',
        function(comService, $rootScope,$stateParams,$scope, staffInfoService,$modal,inform, Trans, AgreeConstant,$state, LocalCache, $http ) {
           /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //获得当前时间
            $scope.datepicker = {
               currentDate :  new Date()
            };
            //设置列表的高度
    		setDivHeight();
    		//设置员工访问权限
            $scope.flag = false;
            //设置员工地区的访问权限
            $scope.areaCodeFlag = true;
    		//新增数据对象
            $scope.addParam = {};
    		//窗体大小变化时重新计算高度
    		$(window).resize(setDivHeight);
    		// 列表数据
            $scope.pageData = [];
            //获取以及部门
            $scope.primaryDeptList = [];
            //获取二级部门
            $scope.deptList = [];
            //获取职称信息
            $scope.titleList = [];
            // 按钮权限
            $scope.specailAddType=false;
            //获取缓存
            $scope.searchObject = LocalCache.getObject('staffInfo_searchObject');
            //绑定文件控件改变事件
            $("#filesImg1").change(submitForm);
            $("#filesImg1").change(fileChangeEvent);
            $scope.getData=getData;
        //    $scope.searchObject.primaryDeptCode = "";
        //    $scope.searchObject.departmentCode = "";
            $scope.entrance = $stateParams.entrance;
            $scope.pages = {
                 pageNum:"1",
                 size:"100"
            };
            //判断按钮是否具有权限
            getButtonPermission();
            $scope.changeDept = changeDept;
            initPage();//初始化

            var paramObj = {
                primaryDeptId:'#primaryDeptName',
            	primaryDeptScopeModel:'searchObject.primaryDeptCode',
            	primaryDeptList:'primaryDeptList',
            	subDeptList:'deptList',
            	subDeptScopeModel:'searchObject.departmentCode'
            };
            //权限控制
            comService.checkAuthentication($scope,paramObj,departmentCallBack,LocalCache.getSession('loginName'));

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */


            /**
             * 获取按钮权限
             */
            function getButtonPermission(){
                var buttons = {
                    'Button-staffInfoManagementController-add':'specailAddType',
                    'Button-staffInfoManagementController-ins':'specailUpdateType'
                };
                var urlData = {
                    'userId':LocalCache.getSession("userId"),
                    'parentPermission':'ButtonCodeStaffInfoManagement',
                    'buttons':buttons
                };
                comService.getButtonPermission(urlData,$scope);
            }
            /**
             * 设置列表的高度
             */
            function setDivHeight(){
                //网页可见区域高度
                 var clientHeight = document.body.clientHeight;
                 var divHeight = clientHeight - (100 + 185);
                 $("#divTBDis").height(divHeight + 30);
                 $("#subDivTBDis").height(divHeight - 65);
            }

            //文件选择事件
            $scope.selectFile = function() {
                 document.getElementById("filesImg1").click();
            };



            //重置查询条件
            $scope.clearParams = function() {
                if($scope.flagAuth){
                   $scope.searchObject.departmentCode = "";
                   $scope.searchObject.primaryDeptCode = "";
                }
                //区域权限
                if(!$scope.areaCodeFlag){
                    $scope.searchObject.area = ""; 
                }
                $scope.searchObject.employeeName = "";
                $scope.searchObject.companyTitle = "";
                $scope.searchObject.startTime = "";
                $scope.searchObject.endTime = "";
                $scope.searchObject.startTime1 = "";
                $scope.searchObject.endTime1 = "";
                $scope.searchObject.education = "";
                $scope.searchObject.state = "";
                $scope.searchObject.onboardingStopTime = "";

                //清空产品线相关信息
                $scope.searchObject.softwareProductLine = "";
                $scope.searchObject.employeeType = "";

                $scope.softwareProductList = [];

                $("#form")[0].reset();
                $("#fileNameDis").text("");
            };

            /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                comService.getOrgChildren('0002').then(function(data) {
                     if (data.data) {
                         $scope.primaryDeptList = data.data;
                     }
                });

            }

            /**
             * 初始化二级部门列表
             */
            function setDept(){
                comService.getOrgChildren($scope.searchObject.primaryDeptCode).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.deptList = data.data;
                    }
                });

            }
            //修改一级部门，二级部门、公司职称进行联动
            function changeDept(){
                setDept();
                getTitleList();

            }


            //获取地区
            function getAreaList(){
               $scope.areaList = [];
               comService.getParamList('AREA_TYPE','AREA_TYPE').then(function(data) {
                  $scope.areaList = data.data;
               });
            }


             //获取职称信息
            function getTitleList() {
                comService.getParamList('STAFF_TITLE','NEW').then(function(data) {
                  $scope.titleList = data.data;
                });

            }
            //获取工作状态列表
            function getStateList() {
                //获取系研职称信息
                $scope.stateList = [];
                comService.getParamList('STAFF_STATE','STAFF_STATE').then(function(data) {
                  $scope.stateList = data.data;
                });
            }


            //定义排序对象
            $scope.orderObj = {
                title: '$index',
                desc: true,
                order: function(str) {
                    $scope.orderObj.title = str;
                    $scope.orderObj.desc = !$scope.orderObj.desc;
                }
            };

            //入职时间
            $scope.openSearchOnboardTime = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openSearchOnboardTime1 = true;
                $scope.openStopOnboardTime1 = false;
            };
            //截止时间
             $scope.openStopOnboardTime = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openSearchOnboardTime1 = false;
                $scope.openStopOnboardTime1 = true;
            };

            //分页查询
            function getData(page) {
                if (inform.format($scope.searchObject.startTime,'yyyy-MM') === "NaN-NaN") {
                    $scope.searchObject.startTime='';
                }else{
                    $scope.searchObject.startTime = inform.format($scope.searchObject.startTime,'yyyy-MM');
                }
                if (inform.format($scope.searchObject.endTime,'yyyy-MM') === "NaN-NaN") {
                    $scope.searchObject.endTime='';
                }else{
                    $scope.searchObject.endTime = inform.format($scope.searchObject.endTime,'yyyy-MM');
                }
                if (inform.format($scope.searchObject.onboardingStopTime,'yyyy-MM') === "NaN-NaN") {
                    $scope.searchObject.onboardingStopTime='';
                }else{
                    $scope.searchObject.onboardingStopTime = inform.format($scope.searchObject.onboardingStopTime,'yyyy-MM');
                }

                //对原缓存进行覆盖
                LocalCache.setObject("staffInfo_searchObject",{});

                //拼装查询条件
                var params = {
                    area:$scope.searchObject.area,
                //    primaryDept:$scope.primaryDeptCode,
                //    department:$scope.departmentCode,
                    primaryDept:$scope.searchObject.primaryDeptCode,
                    department:$scope.searchObject.departmentCode,
                    employeeName:$scope.searchObject.employeeName,
                    companyTitle:$scope.searchObject.companyTitle,
                    startTime:$scope.searchObject.startTime,
                    endTime:$scope.searchObject.endTime,
                    startTime1:$scope.searchObject.startTime1,
                    endTime1:$scope.searchObject.endTime1,
                    education:$scope.searchObject.education,
                    state:$scope.searchObject.state,
                    onboardingStopTime:$scope.searchObject.onboardingStopTime,
                    page: page,
                    size: $scope.pages.size,
                    employeeType:$scope.searchObject.employeeType,
                    softwareProductLine:$scope.searchObject.softwareProductLine,
                    softwareProduct:$scope.searchObject.softwareProduct
                };
                //获取数据
                staffInfoService.seleteByParam(JSON.stringify(params)).then(function (result) {
                    if (result.code === '0000') {
                        $scope.pageData = result.data.list;
                        if (null ==result.data.list || result.data.list.length === 0) {
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages();
                        } else {
                              $scope.pages.total = result.data.total;		// 页面总数
                              $scope.pages.star = result.data.startRow;  	//页面起始数
                              $scope.pages.end = result.data.endRow;  		//页面大小数
                              $scope.pages.pageNum = result.data.pageNum;  	//页面页数
                        }
                    } else {
                          inform.common(result.message);
                    }
                }, function (reason) {
                    console.log("error");
                });
            }
        //初始化页面
        function initPage() {

            getAreaList();//地区
            getStateList();//工作状态
            initPrimaryDeptList();//初始化根据用户名获取一级部门列表
            changeDept();
            $scope.softwareProductLineList = [];//获取产品线
            $scope.softwareProductLineMap = [];
            comService.queryEffectiveParam('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
                if (data.data) {
                    $scope.softwareProductLineList =  data.data;
                    angular.forEach($scope.softwareProductLineList, function (res, index) {
                        $scope.softwareProductLineMap[res.paramCode] = res.paramValue;
                    });
                }
            });
            //获取人员分类
            $scope.employeeTypeList = [
                {paramCode:'直接开发人员',paramValue:'直接开发人员'},
                {paramCode:'开发共用人员',paramValue:'开发共用人员'},
                {paramCode:'项目管理人员',paramValue:'项目管理人员'},
                {paramCode:'开发试制人员',paramValue:'开发试制人员'},
                {paramCode:'管理支撑人员',paramValue:'管理支撑人员'}
            ];


        }



             /**
             *部门控件的回调处理
             **/
            function departmentCallBack(result){
                 if(result.code === '00'){
                     $state.go('app.office.unAuthority');
                     return;
                 }
                 if(result.code === '01'){
                    //设置为可编辑
                    $scope.flagAuth = true;
                    //01全部权限
                    $scope.flag = true;
                 }
                 if(result.code === '02'){
                    //部门权限控制
                     $scope.flag = false;
                     //获取职称信息
                     getTitleList();
                 }
                 if (result.code === '03') {
                      $scope.flag = true;
                      $scope.areaCodeFlag = true;
                      $('#area').attr('disabled',true);
                      $scope.searchObject.area = result.data.areaCode;
                 }
                $scope.getData(1);
            }

        /**
         * 打开新增窗口
         */
        $scope.openAddModal = function () {
            LocalCache.setObject('staffInfo_searchObject',$scope.searchObject);
            $state.go('app.office.staffInfoAddController');
        };


        //打开修改窗口
        $scope.openUpdateModal = function(item) {
             LocalCache.setObject('staffInfo_searchObject',$scope.searchObject);
             $state.go('app.office.staffInfoUpdateController',{
                 id:item.id,
                 employeeId:item.employeeId
             });


        };


             /**
              * 选择上传文件后事件
            */
            function fileChangeEvent(e){
                   var fileName = "文件名称：" + e.currentTarget.files[0].name;
                   $("#fileNameDis").text(fileName);
            }

            // 删除确认
            $scope.deleteConfirm = function (item) {
                inform.modalInstance("确定要删除吗？").result.then(function () {
                    $scope.deleteByParam(item);
                });

            };

            //根据选中的id 删除数据
            $scope.deleteByParam = function (item) {
                var param = {
                    "id" : item.id,
                    "employeeId":item.employeeId
                };
                staffInfoService.deleteByIds(param)
                    .then(function (data) {
                        if (data.code === "0000") {
                            inform.common('删除成功');
                            $scope.getData(1);
                        } else {
                            inform.common(data.message);
                        }
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            };



            //下载员工基本信息模板
            $scope.toTemplateExcel = function() {
                inform.modalInstance("确定要下载基本信息模板吗？").result.then(function() {
                    var params = {};
                    inform.downLoadFile('staffInfo/toTemplateExcel',params,"员工基本信息表模板.xlsx");
                });

            };

             //下载员工基本信息表
            $scope.toExcel = function() {
                inform.modalInstance("确定要下载基本信息表吗？").result.then(function() {

                    var params = {
                        area:$scope.searchObject.area,
                        primaryDept:$scope.searchObject.primaryDeptCode,
                        department:$scope.searchObject.departmentCode,
                        employeeName:$scope.searchObject.employeeName,
                        companyTitle:$scope.searchObject.companyTitle,
                        startTime:inform.format($scope.searchObject.startTime,'yyyy-MM')==='NaN-NaN'?null:inform.format($scope.searchObject.startTime,'yyyy-MM'),
                        endTime:inform.format($scope.searchObject.endTime,'yyyy-MM')==='NaN-NaN'?null:inform.format($scope.searchObject.endTime,'yyyy-MM'),
                        education:$scope.searchObject.education,
                        state:$scope.searchObject.state,
                        onboardingStopTime:inform.format($scope.searchObject.onboardingStopTime,'yyyy-MM')==='NaN-NaN'?null:inform.format($scope.searchObject.onboardingStopTime,'yyyy-MM')
                    };
                    inform.downLoadFile('staffInfo/toExcel',params,"员工基本信息表.xlsx");
                });

            };

             //下载员工综合信息表
            $scope.downloadExcel = function() {
                inform.modalInstance("确定要下载综合信息表吗？").result.then(function() {

                    var params = {
                        area:$scope.searchObject.area,
                        primaryDept:$scope.searchObject.primaryDeptCode,
                        department:$scope.searchObject.departmentCode,
                        employeeName:$scope.searchObject.employeeName,
                        companyTitle:$scope.searchObject.companyTitle,
                        startTime:inform.format($scope.searchObject.startTime,'yyyy-MM')==='NaN-NaN'?null:inform.format($scope.searchObject.startTime,'yyyy-MM'),
                        endTime:inform.format($scope.searchObject.endTime,'yyyy-MM')==='NaN-NaN'?null:inform.format($scope.searchObject.endTime,'yyyy-MM'),
                        education:$scope.searchObject.education,
                        state:$scope.searchObject.state,
                        onboardingStopTime:inform.format($scope.searchObject.onboardingStopTime,'yyyy-MM')==='NaN-NaN'?null:inform.format($scope.searchObject.onboardingStopTime,'yyyy-MM')
                    };
                    inform.downLoadFile('staffInfo/downloadExcel',params,"员工综合信息表.xlsx");
                });

            };
        $scope.goBack = function() {
                window.history.go(-1);
        }

        //上传文件
        function submitForm(){

            var formData = new FormData(document.getElementById("form"));//表单id  初始化表单值
            var file = document.querySelector('input[type=file]').files[0]; //获取文档中有类型为file的第一个input元素
            if(!file){
                inform.common("请先选择文件!");
                return false;
            }
            formData.append('fileName', file);
            var a = file.type;
            if(a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"){
                inform.common("请选择.xlsx类型的文档进行上传!");
                return false;
            } else {
              inform.modalInstance("确定要上传文件吗？").result.then(function() {
                inform.uploadFile('staffInfo/uploadExcel',formData,function func(result){
                  if (result.code === AgreeConstant.code) {
                       // 关闭遮罩层
                       inform.closeLayer();
                       $modal.open({
                       templateUrl: 'errorModel.html',
                       controller: 'ModalInstanceCtrl',
                       size: "lg",
                       resolve: {
                           items: function () {
                               return result.message;
                           }
                       }
                       });

                  } else {
                      // 关闭遮罩层
                     inform.closeLayer();
                     inform.common("上传失败！");
                  }
                  getData(1);
                  $("#form")[0].reset();
                  $("#fileNameDis").text("");
                  });

               });
            }

        }


         /**
         * *************************************************************
         *              方法声明部分                                结束
         * *************************************************************
         */

      }
    ]);
})();
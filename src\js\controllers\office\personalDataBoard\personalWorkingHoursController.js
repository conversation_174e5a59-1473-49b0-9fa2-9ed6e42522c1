(function () {
    app.controller("personalWorkingHoursController", ['$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'personalDataBoardService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function ($rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, personalDataBoardService, inform, Trans, AgreeConstant, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //页面数据
			$scope.formRefer = {};
            $scope.getData = getData;
            $scope.reset = reset;
            $scope.chartsList = [];
            //初始化信息
            initData();

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 初始化
             */
            function initData() {
                //获取当前登录者的empId
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ? $scope.sessionEmpId : $stateParams.empId;
                //获取当前登录者的登录名
                $scope.formRefer.loginName = LocalCache.getSession('loginName');
                var person = LocalCache.getObject('personDataBoardEmployee');
                if(person.loginName){
                    $scope.formRefer.loginName = person.loginName;
                }
                reset();
                getData();
            }

            /**
             * 获取同行评审关联信息
             */
            function getData() {
                var urlData = {
                    'loginName':$scope.formRefer.loginName,
                    'startTime':inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd') //结束时间
                }
                $scope.tableData = [];
                personalDataBoardService.getPersonalWorkingHoursData(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        if (null !== data.data) {
                            $scope.tableData = data.data;
                            //设置汇总数据
                            setGatherData();
                            setTimeout(setCharts,500);
                        } else {
							inform.common(Trans("tip.noData"));
                        }
                    } else {
                        inform.common(data.message);
                    }
                },
                function () {
                    inform.common(Trans("tip.requestError"));
                });
            }

            function setGatherData(){
                $scope.allHours=0;
                $scope.allProjectHours=0;
                $scope.departmentHours=0;
                $scope.projectHoursRate=0;
                $scope.keepCodingRate=0;
                var data = $scope.tableData[0];
                if(data.hours){
                    angular.forEach($scope.tableData[0].hours,function (item) {
                        $scope.allHours = $scope.allHours+item.value*1;
                        if(item.name==='部门工作'){
                            $scope.departmentHours=item.value;
                        }
                    });
                    $scope.allHours = $scope.allHours.toFixed(1);
                    $scope.allProjectHours=($scope.allHours-$scope.departmentHours).toFixed(1);
                    $scope.projectHoursRate=($scope.allProjectHours/$scope.allHours*100).toFixed(2);
                    $scope.keepCodingRate = (data.keepCoding/$scope.allHours*100).toFixed(2);
                }
            }

            function setCharts(){
                angular.forEach($scope.tableData,function(item){
                  echartsForWorkingHours(item);
                });
            }

    		function echartsForWorkingHours(m) {
    		    var titleText = m.projectName;
    		    var currentCharts = echarts.init(document.getElementById('current_'+m.projectId));

                var option = {
                  title: {
                    text: titleText,
                    left: 'center'
                  },
                  tooltip: {
                    trigger: 'item',
                    formatter: "{b} : {c}d ({d}%)"
                  },
                  series: [
                    {
                      type: 'pie',
                      radius: '50%',
                      data: m.hours,
                      label:{
                         normal:{
                         	show: true,
                            formatter: ' {b} {d}%'
                         }
                      }
                    }
                  ]
                };
                currentCharts.setOption(option, true);
                $scope.chartsList.push(currentCharts);
    		}

            window.addEventListener("resize", function () {
                angular.forEach($scope.chartsList,function (charts) {
                    if (charts) { charts.resize(); }
                });
            });
			function reset() {
			    var date = new Date();
                $scope.formRefer.startTime = inform.format(date.setMonth(date.getMonth() - 3),'yyyy-MM-dd');
                $scope.formRefer.endTime = '';
            }

            $scope.goback = function () {
                $state.go('app.office.personKpi');
            };

            //评审时间 -开始
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = true;
                $scope.formRefer.openedEnd = false;
            };
            //评审时间 -结束
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = false;
                $scope.formRefer.openedEnd = true;
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

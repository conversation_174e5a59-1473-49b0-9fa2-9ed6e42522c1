(function() {
	'use strict';
	app.factory('earlyWarningService', earlyWarningService);
	earlyWarningService.$inject = [ "HttpService", '$rootScope' ];

	function earlyWarningService(HttpService, $rootScope) {
		var service = {
				insertEarlyWarning:insertEarlyWarning,
				updateEarlyWarning:updateEarlyWarning,
				getTotalTask:getTotalTask
		};
		return service;

		/**
		 * 新增任务信息
		 */
		function insertEarlyWarning(params) {
			return HttpService.post($rootScope.getWaySystemApi + 'earlyWarning/insertEarlyWarning', params);
		}
		/**
		 * 修改所有任务信息
		 */
		function updateEarlyWarning(params) {
			return HttpService.post($rootScope.getWaySystemApi + 'earlyWarning/updateEarlyWarning', params);
		}

		/**
		 * 查询所有定时任务
		 */
		 function getTotalTask(taskId,type) {
			return HttpService.get($rootScope.getWaySystemApi + 'scheduleJob/getTotalTask',
			{'taskId':taskId,'type':type});
		}
	}
})();

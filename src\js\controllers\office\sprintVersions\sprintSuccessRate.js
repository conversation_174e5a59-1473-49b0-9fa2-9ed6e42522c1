(function () {
    app.controller("sprintSuccessRateController", ['comService', '$rootScope', '$scope', '$modal', 'sprintVersionsService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http', '$state',
        function (comService, $rootScope, $scope, $modal, sprintVersionsService, inform, Trans, AgreeConstant, LocalCache, $http, $state) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */

            // 默认本年度
            $scope.dateRadio = '0';

            // 获取数据
            $scope.getData = getData;
            $scope.startTime = new Date().getFullYear() + '-01-01';
            $scope.endTime = inform.format(new Date(), 'yyyy-MM-dd');
            //在刷新页面时调用该方法
            getData();
            $scope.datepicker = {};
            $scope.toggleMin = toggleMin;
            toggleMin();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            //开始时间
            $scope.openDateStart = function () {
                $scope.openedStart = true; //开始时间
            };

            //结束时间
            $scope.openDateEnd = function () {
                $scope.openedEnd = true; //开始时间
            };

            //获取当前选定时间
            function toggleMin() {
                $scope.datepicker.currentDate = $scope.datepicker.currentDate ? null : new Date();
            }

            //重置
            $scope.rest = function () {
                $scope.startTime = new Date().getFullYear() + '-01-01';
                $scope.endTime = inform.format(new Date(), 'yyyy-MM-dd');
                $scope.dateRadio = '0';
            };

            /**
             * 获取所有的数据
             */
            function getData() {
                $scope.lineRateList = [];
                $scope.orgRateList = [];
                var urlData = {
                    'startDate': inform.format($scope.startTime, 'yyyy-MM-dd'),
                    'endDate': inform.format($scope.endTime, 'yyyy-MM-dd')
                }
                sprintVersionsService.getSprintSuccessRateWithLine(urlData).then(function (data) {
                        $scope.lineRateList = angular.fromJson(data.data);
                    },function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
                sprintVersionsService.getSprintSuccessRateWithOrg(urlData).then(function (data) {
                        $scope.orgRateList = angular.fromJson(data.data);
                    },function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            /**
             * 生成excel表格
             */
            $scope.toExcel = function () {
                var urlData = {
                    'startDate': inform.format($scope.startTime, 'yyyy-MM-dd'),
                    'endDate': inform.format($scope.endTime, 'yyyy-MM-dd')
                }
                inform.modalInstance("确定要下载吗！").result.then(function () {
                    //开启遮罩层
                    inform.showLayer("下载中。。。。。。");
                    $http.post(
                        $rootScope.getWaySystemApi + 'sprintVersions/sprintSuccessRateExcel',
                        urlData,
                        {
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': 'Bearer ' + LocalCache.getSession("token") || ''
                            },
                            responseType: 'arraybuffer'//防止中文乱码
                        }
                    ).success(function (data) {
                        //如果是IE浏览器
                        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                            var csvData = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
                            window.navigator.msSaveOrOpenBlob(csvData, '冲刺成功率报表.xlsx');
                        }
                        //google或者火狐浏览器
                        else {
                            var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
                            var objectUrl = URL.createObjectURL(blob);
                            var aForExcel = $("<a download='冲刺成功率报表.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href", objectUrl);
                            $("body").append(aForExcel);
                            $(".forExcel").click();
                            aForExcel.remove();
                        }

                        // 关闭遮罩层
                        inform.closeLayer();
                        inform.common("下载成功!");
                    });
                });
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
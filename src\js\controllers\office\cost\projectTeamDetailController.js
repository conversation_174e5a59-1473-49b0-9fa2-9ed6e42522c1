(function () {
    app.controller("projectTeamDetailController", ['projectTeamService', 'comService', '$rootScope', '$scope', '$state', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http', function (projectTeamService, comService, $rootScope, $scope, $state, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http) {
        /**
         * *************************************************************
         *             初始化部分                                 开始
         * *************************************************************
         */

        $scope.projectId = $stateParams.projectId;
        $scope.projectList = [];
        $scope.levelList = [];
        $scope.levelMap = {};
        $scope.areaList = [];
        $scope.areaMap = {};
        //修改的项目成员信息
        var changeSet = new Set();
        $scope.getData = getData;
        $scope.titleList = [{
            'code': '1',
            'value': '项目经理'
        }, {
            'code': '2',
            'value': '开发工程师'
        }, {
            'code': '3',
            'value': '测试工程师'
        }, {
            'code': '4',
            'value': 'PPQA'
        }, {
            'code': '5',
            'value': '架构设计师'
        }, {
            'code': '7',
            'value': '产品工程师'
        }];
        $scope.statusList = [{
            'code': '1',
            'value': '正常'
        }, {
            'code': '2',
            'value': '离开'
        }];
        //设置列表的高度
        setDivHeight();
        //窗体大小变化时重新计算高度
        $(window).resize(setDivHeight);

        initPages();
        getData($scope.projectId);

        /**
         * *************************************************************
         *              初始化部分                                 结束
         * *************************************************************
         */

        /**
         * *************************************************************
         *              方法声明部分                                 开始
         * *************************************************************
         */

        //设置列表的高度
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (150 + 180);
            $("#addInfoForm").height(divHeight);
            $("#subDivTBDis").height(divHeight - 70);
        }

        function initPages() {

            comService.getParamList('STAFF_TITLE', 'NEW').then(function (data) {
                if (data.data) {
                    $scope.levelList = data.data;
                    $scope.levelList.forEach(function (item) {
                        var key = item.param_code;
                        $scope.levelMap[key] = item.param_value;
                    });
                }
            });

            comService.getParamList('AREA_TYPE', 'AREA_TYPE').then(function (data) {
                if (data.data) {
                    $scope.areaList = data.data;
                    $scope.areaList.forEach(function (item) {
                        var key = item.param_code;
                        $scope.areaMap[key] = item.param_value;
                    });
                }
            });
        }

        function getData(projectId) {

            var urlData = {
                'projectId': projectId // 分页每页大小
            };
            //获取项目预算
            projectTeamService.getProjectTeamDetail(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    $scope.projectList = data.data;
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        }

        //页面进行了修改操作
        $scope.change = function (index) {
            //被修改过的记录
            changeSet.add(index);
        };

        //保存数据
        $scope.save = function () {

            var modifiedList = [];
            changeSet.forEach(function (set) {
                if ($scope.projectList[set].status === '2') {
                    $scope.projectList[set].outDate = inform.format(new Date(), 'yyyy-MM-dd');
                }
                modifiedList.push($scope.projectList[set]);
            });

            var urlData = {
                'changeList': modifiedList
            };
            //获取项目预算
            projectTeamService.save(urlData).then(function (data) {
                getData($scope.projectId);
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        };
        //同步项目人员
        $scope.syn = function () {
            var urlData = {
                'projectId': $stateParams.projectId
            };
            //获取项目预算
            projectTeamService.syn(urlData).then(function (data) {
                getData($scope.projectId);
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        };
        /**
         * *************************************************************
         *              方法声明部分                                 结束
         * *************************************************************
         */
    }]);
})();

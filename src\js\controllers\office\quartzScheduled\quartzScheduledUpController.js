(function () {
    'use strict';
    app.controller("quartzScheduledUpController", ['$scope','$state','comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','quartzScheduledService','$stateParams','LocalCache', '$modal','$http',
        function ($scope,$state,comService, $rootScope, inform, Trans, AgreeConstant,quartzScheduledService,$stateParams,LocalCache, $modal,$http) {
    	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
        //获取缓存
        $scope.changeParam = LocalCache.getObject('quartzScheduled_changeParam');
		//将id提取出来成为List
		var ids = ($scope.changeParam.afterTaskIds).split(",");
		$scope.changeParam.afterTaskIdList = ids;
		//获取全部定时任务信息
		quartzScheduledService.getTotalTask($scope.changeParam.id,'update').then(function (data) {
			$scope.tasksList = data.data;
		});

        $("div.input-group input").val($scope.changeParam.cronExpression);
        $("div.input-group input").attr("disabled","disabled");

		//任务状态下拉框 disabled
		$scope.jobDisabledSelect = [{
			value: '0',
			label: '启用'
		},{
			value: '1',
			label: '禁用'
		}];
        // 设置侧边的高度,随窗口变动
        inform.autoHeight();
        window.onresize = inform.autoHeight;

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */
        /**
         * 修改信息
         */
        $scope.updateInfo = function() {
        	var cronValue = $('#cron').val();
            if(cronValue === ''){
                inform.common('cron表达式不能为空');
                return;
            }
   	 		var urlData = {
   	 			'id':$scope.changeParam.id,//定时任务ID
   	 			'jobName':$scope.changeParam.jobName,//任务名称
				'disabled':$scope.changeParam.disabled,//任务状态
				'cronExpression':cronValue,//corn表达式
				'remark':$scope.changeParam.remark,//备注：
				'beanName':$scope.changeParam.beanName,//单例名称：
				'methodName':$scope.changeParam.methodName,//方法名称：
				'params':$scope.changeParam.params,//参数
				'afterTaskIds':($scope.changeParam.afterTaskIdList).join(",")//后置任务Id
          	};
   	 		quartzScheduledService.updateScheduleJob(urlData).then(function(data){
   	 			if(data.code===AgreeConstant.code) {
   	 				inform.common(data.message);
   	 				$state.go('app.office.quartzScheduledController',null);
   	 			}else {
					inform.common(data.message);
				}
   	 		}, function(error) {
   	 			inform.common(Trans("tip.requestError"));
   	 		});
    	 };
	     /**
          * *************************************************************
          *              方法声明部分                                 结束
          * *************************************************************
          */
         } 
    ]);
})();
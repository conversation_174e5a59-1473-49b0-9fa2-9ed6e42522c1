(function () {
    app.controller("roleKpiUpController", ['$rootScope', 'comService', '$scope', '$state', '$stateParams', '$modal', 'roleKpiService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function ($rootScope, comService, $scope, $state, $stateParams, $modal, roleKpiService, inform, Trans, AgreeConstant, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //设置列表的高度
            setDivHeight();
            $scope.changeParam = {};
            var flag = false;//判断chexbox初始化是否完成
            var kpiBefore = [];
            $(window).resize(setDivHeight);//窗体大小变化时重新计算高度
            initPerson();
            // 全选函数
            $scope.selectAll = selectAll;
            // 选中或取消某个元素
            $scope.checkOne = checkOne;
            // 保存信息
            $scope.saveInfo = saveInfo;
            // 返回
            $scope.goback = goback;
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
                var clientWidth = document.body.clientWidth;
                $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
            }

            /**
             * 初始化信息
             */
            function initPerson() {
                //获取角色
                $scope.roleCodeList = [];
                comService.queryEffectiveParam('ROLE_LIBRARY', 'ROLE_LIBRARY').then(function (data) {
                    if (data.data) {
                        $scope.roleCodeList = data.data;
                        flag++;
                        setValue();
                    }
                });
                //获取一级指标数据
                $scope.kpiCodeList = [];
                roleKpiService.getRoleKpiList().then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.kpiCodeList = data.data;
                        flag++;
                        setValue();
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * 全选函数
             */
            function selectAll () {
                var boxes = document.getElementsByName("roleKpi");
                if ($scope.select_all) {
                    //获取页面所有领域
                    for (var i = 0; i < boxes.length; i++) {
                        boxes[i].checked = true;
                    }
                } else {
                    //获取页面所有领域
                    for (var j = 0; j < boxes.length; j++) {
                        boxes[j].checked = false;
                    }
                }
            }
            /*
            * 选中或取消某个元素
            * */
            function checkOne () {
                $scope.select_all = false;
            }

            /**
             * 若为更新 则赋值
             */
            function setValue() {
                //初始化未完成
                if (flag !== 2) {
                    return;
                }
                $scope.type = "up";
                $scope.changeParam.roleCode = $stateParams.item;
                var urlData = {
                    'roleCode': $scope.changeParam.roleCode,
                }
                roleKpiService.selectOne(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        kpiBefore = data.data;
                        var boxes = document.getElementsByName("roleKpi");
                        //获取页面所有领域
                        for (var i = 0; i < boxes.length; i++) {
                            //据有的领域
                            for (var j = 0; j < data.data.length; j++) {
                                if (boxes[i].value === data.data[j]) {
                                    boxes[i].checked = true;
                                }
                            }
                        }
                        //角色不可修改
                        $("#roleCode").prop('disabled', true).trigger("chosen:updated");
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * 保存信息
             */
            function saveInfo () {
                var boxes = document.getElementsByName("roleKpi");
                var details = [];
                angular.forEach(boxes, function (detail, i) {
                    if (boxes[i].checked) {
                        details.push(boxes[i].value);
                    }
                });
                if (details.length === 0) {
                    inform.common("请选择指标数据");
                    return;
                }
                var del = [];
                var add = [];
                //原先的不包含现在的--新增
                for (var i = 0; i < details.length; i++) {
                    if (kpiBefore.indexOf(details[i]) < 0) {
                        add.push(details[i]);
                    }
                }
                //现在的不包含原先的--删除
                for (var j = 0; j < kpiBefore.length; j++) {
                    if (details.indexOf(kpiBefore[j]) < 0) {
                        del.push(kpiBefore[j]);
                    }
                }
                //有删除的先删除
                if (del.length > 0) {
                    delInfo(del, add, details);
                } else {
                    //没有删除的看有无新增
                    if (add.length > 0) {
                        addInfo(add);
                    } else {
                        module();
                    }
                }
            }

            function module() {
                layer.confirm("修改指标成功", {
                    title: false,
                    btn: ['确定']
                }, function (result) {
                    layer.close(result);
                    $scope.goback();
                });
            }

            /**
             * 删除
             */
            function delInfo(del, add, details) {
                var kpiCodeDetails = [];
                angular.forEach(details, function (item) {
                    kpiCodeDetails.push(item.split(':')[0]);
                })
                //查看二级指标是否依然存在，若不存在删除一级指标数据
                angular.forEach(del, function (item) {
                    if (kpiCodeDetails.indexOf(item.split(':')[0]) < 0) {
                        del.push(item.split(':')[0] + ':0')
                    }
                })
                //数组去重
                del = Array.from(new Set(del))
                var urlData = [{
                    'roleCode': $scope.changeParam.roleCode,
                    'kpiDetail': del
                }]
                roleKpiService.delInfo(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        if (add.length > 0) {
                            addInfo(add);
                        } else {
                            module();
                        }
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * 新增
             */
            function addInfo(add) {
                var urlData = [{
                    'roleCode': $scope.changeParam.roleCode,
                    'kpiDetail': add
                }]
                roleKpiService.addInfo(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        module();
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            /**
             * 返回
             */
            function goback () {
                $state.go("app.office.roleKpiController", {'type': '1'});
            }

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
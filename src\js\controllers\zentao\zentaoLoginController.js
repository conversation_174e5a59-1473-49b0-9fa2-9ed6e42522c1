(function () {
    app.controller("zentaoLoginController", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,inform,Trans,AgreeConstant,LocalCache,$http) {

		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		// 显示遮罩层
        inform.showLayer("跳转中...")
    	var info={
    		'flag':'true'
    	}
    	//设置时间戳（获取第一次登录时间）
    	var timeStamp=new Date().getTime();
    	LocalCache.setSession('zentaoLoginTime',timeStamp);
    	//设置登陆过
    	LocalCache.setObject("zentaoManagement_zentaoLoginFlag",info);
    	var urlVal = $rootScope.zentaoSystemApi+"index.php?m=user&f=login&t=html";
    	var userName = LocalCache.getSession('currentUserName');
    	$("#nameObj").val(userName);
    	var passwordVal = $rootScope.RSADecrypt(LocalCache.getSession('OAPassword'));
    	$("#pwdObj").val(passwordVal);
    	$("#zentaoLoginForm").attr("action",urlVal);
    	$("#zentaoLoginForm").submit();
    	//发送请求
        var frame = document.getElementById('zentaoLoginPage');
        frame.onload=function(){
             //根据返回路由的缓存，进行返回原先的界面
             //var goUrl = LocalCache.getObject('zentaoManagement_return');
             //$state.go(goUrl);
             window.open($rootScope.zentaoSystemApi+'index.php?m=my&f=index');
			 frame.style.display = 'none';
             inform.closeLayer();
        }
    	/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */
	}]);
})();

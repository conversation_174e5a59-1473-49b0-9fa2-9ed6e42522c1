/*
* @Author: fubaole
* @Date:   2017-09-18 14:53:05
* @Last Modified by:   fubaole
* @Last Modified time: 2018-01-15 17:20:32
*/
(function () {
    app.controller("correctController", ['$rootScope', '$scope','correctService','inform','Trans','AgreeConstant','comService','LocalCache','$state',
        function ($rootScope, $scope,correctService,inform,Trans,AgreeConstant,comService,LocalCache,$state) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //查询条件
            $scope.formRefer = {};
            //是否从我的星星跳转标志
            $scope.formRefer.flag = '0';
            //获取缓存
            $scope.formRefer = LocalCache.getObject('correct_formRefer');
            if($scope.formRefer.flag === '1'){
                $scope.formRefer.startTime = $scope.formRefer.startDate;
                $scope.formRefer.endTime = $scope.formRefer.endDate;
                $scope.formRefer.empName = $scope.formRefer.employeeName;
                console.log($scope.formRefer);
            }
            //清除缓存
            LocalCache.setObject('correct_formRefer', {});
            //分页
            $scope.pages = inform.initPages();
            $scope.pages.size = "20";
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            // 设置滚动条高度
            $scope.formRefer.subDivTBDisScrollTop = $('#subDivTBDis').scrollTop();
            initInfo();
            $scope.getData = getData;
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 200);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 80);
                setTimeout(showDataTable,500);
            }

            /**
             * 初始化
             */
            function initInfo() {
                if($scope.formRefer.flag !== '1'){
                    initTime();
                }
                //获取部门
                $scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentList = comService.getDepartment(data.data);
                });
                //地域
                $scope.areaList = [{name:"威海"},{name:"北京"},{name:"西安"},{name:"深圳"}];
                //员工
                $scope.employeeList = [];
                comService.getEmployeesName().then(function (data) {
                    $scope.employeeList = angular.fromJson(data.data);
                });
                //审批结果
                $scope.resultList = [{value:"running",name:"审批中"},{value:"agree",name:"同意"},{value:"refuse",name:"拒绝"}];
                $scope.resultMap = {"running":'审批中',"agree":'同意', "refuse":'拒绝'};
                //是否采纳
                $scope.acceptList = ["不采纳","采纳","采纳+推广","采纳+优秀","采纳+优秀+推广"];
                //审批状态
                $scope.statusList = [{value:"RUNNING",name:"进行中"},{value:"COMPLETED",name:"已完成"},{value:"TERMINATED",name:"终止"}];
                $scope.statusMap = {"RUNNING":'进行中', "COMPLETED":'已完成', "TERMINATED":'终止'};
                getData();

            }

            function getData(pageNum) {
                $scope.dataTableShow = 0;
                var urlData = $scope.formRefer;
                //当前页数
                urlData.currentPage=pageNum;
                //每页显示条数
                urlData.pageSize=$scope.pages.size;
                correctService.getData(urlData).then(function (data) {
                        $scope.dataTableShow = 1;
                        if (data.code === AgreeConstant.code) {
                            if(null==data.data){
                                $scope.tableData = {};
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages();
                                $scope.pages.size = "20";
                            } else {
                                //项目详情
                                $scope.tableData = data.data.list;
                                //分页信息设置
                                $scope.pages.total = data.data.total;
                                $scope.pages.star = data.data.startRow;
                                $scope.pages.end = data.data.endRow;
                                $scope.pages.pageNum = data.data.pageNum;
                            }
                            setTimeout(showDataTable,300);
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            /**
             *调用DataTable组件冻结表头和左侧及右侧的列
             */
            function showDataTable(){
                $('#fixedLeftAndTop').DataTable( {
                    //可被重新初始化
                    retrieve:       true,
                    //自适应高度
                    scrollY:        'calc(100vh - 350px)',
                    scrollX:        true,
                    scrollCollapse: true,
                    //控制每页显示
                    paging:         false,
                    //冻结列（默认冻结左1）
                    fixedColumns:   {
                        leftColumns: 2,
                        rightColumns: 0
                    },
                    //search框显示
                    searching:      false,
                    //排序箭头
                    ordering:       false,
                    //底部统计数据
                    info:           false
                } );

                // 获取到跟踪单信息之后滚动条跳至缓存中保存的scrollTop
                setTimeout(function () {
                    if ($scope.formRefer.subDivTBDisScrollTop) {
                        $('#fixedLeftAndTop').parent().animate({scrollTop: $scope.formRefer.subDivTBDisScrollTop},10);
                    }
                },300)
            }
            /**
             * excel下载
             */
            $scope.toExcel = function() {
                inform.modalInstance("确定要下载吗?").result.then(function () {
                    inform.downLoadFile('correct/toExcel',$scope.formRefer,'一点改善.xlsx');
                });
            }
            /**
             * 重置
             */
            $scope.clearParams = function() {
                $scope.formRefer = {};
            };
            /**
             * 开始时间
             */
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };

            /**
             * 结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };
            /**
             * 初始化检索条件开始时间
             */
            function initTime() {
                var date = new Date();
                //开始日期向前推1个月
                date.setMonth(date.getMonth() - 1);
                //对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
                $scope.formRefer.startTime = inform.format(date, 'yyyy-MM-dd');
            }
            //页面跳转到我的星星--我的星星明细部门贡献详情页
            $scope.getBack = function(){
                $state.go('app.personal_star_department_detail');
            }
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */

        }]);
})();
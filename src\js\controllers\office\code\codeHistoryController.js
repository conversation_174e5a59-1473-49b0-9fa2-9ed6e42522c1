/*
 * @Author: fubaole
 * @Date:   2017-09-18 14:53:05
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-01-15 17:20:32
 */
(function () {
    app.controller("codeHistoryManagement", ['comService', '$rootScope', '$scope', 'codeService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', '$stateParams', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, codeService, inform, Trans, AgreeConstant, $modal, $state, $stateParams,LocalCache, $http) {
    	/**
		 * ************************************************************* 
		 * 初始化部分                                                 开始 
		 * *************************************************************
		 */
    	// 设置列表的高度
        setDivHeight();
        // 窗体大小变化时重新计算高度
        $(window).resize(setDivHeight);
        // 默认显示的时间
        $scope.formRefer = {};
		var date = new Date();
		$scope.formRefer.endTime = inform.format(date,'yyyy-MM-dd');
        $scope.formRefer.startTime = inform.format(date.setMonth(date.getMonth() - 3),'yyyy-MM-dd');

        // 模块名、项目经理、类型、部门的显示
        $scope.cName = $stateParams.cName;
        $scope.cManager = $stateParams.cManager;
        $scope.cType = $stateParams.cType;
        $scope.cDepartment = $stateParams.cDepartment;
        $scope.riskNote = $stateParams.riskNote;
        $scope.name = $stateParams.name;
        // 页面分页信息
        $scope.pages = {
            pageNum: '',	// 分页页数
            size: '',		// 分页每页大小
            total: ''		// 数据总数
        };
      // 分页
    	$scope.pages = inform.initPages(); // 初始化分页数据
    	$scope.number = $scope.pages.pageNum;
		$scope.getData = getData; 
    	getData(1);
    	$scope.riskNoteUpdateFlag = false;
    	//重置查询条件
    	$scope.reset = reset;
    	// 折线图
    	$scope.getCodeChart = getCodeChart;
        //判断是否具有权限
        getButtonPermission();
        /**
         * 获取按钮权限
         */
        function getButtonPermission(){
            var buttons = {
                'Button-codeController-riskNoteUpdateFlag':'riskNoteUpdateFlag'
            };
            var urlData = {
                'userId':LocalCache.getSession("userId"),
                'parentPermission':'ButtoncodeConfigController',
                'buttons':buttons
            };
            comService.getButtonPermission(urlData,$scope);
        }
    	/**
		 * ************************************************************* 
		 * 初始化部分                                                结束 
		 * *************************************************************
		 */

		/**
		 * ************************************************************* 
		 * 方法声明部分                                                开始 
		 * *************************************************************
		 */

        // 设置列表的高度
        function setDivHeight() {
            // 网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (150 + 140);
            $("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight - 80);
        }
        
		/**
		 * 重置
		 */
        function reset(){
            var date = new Date();
            $scope.formRefer.endTime = inform.format(date,'yyyy-MM-dd');
            $scope.formRefer.startTime = inform.format(date.setMonth(date.getMonth() - 3),'yyyy-MM-dd');
        }
		
		/**
		 * 查询开始时间
		 */
        $scope.openDateStart = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = true;
            $scope.openedEnd = false;
        };

        /**
		 * 查询结束时间
		 */
        $scope.openDateEnd = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = false;
            $scope.openedEnd = true;
        };
        
        /**
		 * 根据id获取历史记录
		 */
        function getData(pageNum) {
			var urlData ={
				'projectName':$stateParams.name,// 所属项目模块名
				'startTime':inform.format($scope.formRefer.startTime,'yyyy-MM-dd'),// 开始时间
				'endTime':inform.format($scope.formRefer.endTime,'yyyy-MM-dd'),// 结束时间
	            'currentPage':pageNum,// 当前页数
		        'pageSize':$scope.pages.size// 每页显示条数
			};
			codeService.getHistoryInfo(urlData).then(function(data) {
					if (data.code===AgreeConstant.code) {
						// 项目详情
						$scope.jsonData = data.data.list;
						if ($scope.jsonData.length===0) {
		                    inform.common(Trans("tip.noData"));
	    	                $scope.pages = inform.initPages();
		                } else {
		                    // 分页信息设置
		                    $scope.pages.total = data.data.total;
		                    $scope.pages.star = data.data.startRow;
		                    $scope.pages.end = data.data.endRow;
		                    $scope.pages.pageNum = data.data.pageNum;
		                }
					} else {
						inform.common(data.message);
					}
				},
				function(error) {
					inform.common(Trans("tip.requestError"));
				});
		}
        
        /**
		 * 获得折线图
		 */
        function getCodeChart(){
        	$scope.xData = [];
        	$scope.yData = [];
        	// 遍历查询到的数据，取出时间和分数（查到的数据时按时间降序排列的）
        	for(var i=$scope.jsonData.length-1;i>=0;i--){
        		$scope.xData.push($scope.jsonData[i].createHisDate);
        		$scope.yData.push($scope.jsonData[i].score);
        	}
        	
        	// x轴点数
        	$scope.xInterval = 0;
        	if($scope.jsonData.length>31){
        		$scope.xInterval = Math.floor($scope.jsonData.length/31);
        	}
        		
        	$scope.myCodeCharts = $scope.myCodeCharts ? $scope.myCodeCharts : echarts.init(document.getElementById('codeChart'));
        	var option = {
            		// 提示框，鼠标悬浮交互时的信息提示
                	tooltip: {
                    	trigger: 'item',
                    	formatter: '{a} <br/>{b}: {c}'
                	},
                    grid:{
                        left:'10%',
                        bottom:'15%'
                    },
                	xAxis: {
                        type: 'category',
                        name:'日期',
                        data: $scope.xData,
                        axisLabel:{
                            interval:$scope.xInterval,
                            rotate:70
                        }
                    },
                    // y轴
                    yAxis: {
                        type: 'value',
                        name:'分数',
                        min:0,
                        max:100,
                        interval:5
                    },
                    series: [{
                    	name: '分数',
                        data: $scope.yData,
                        type: 'line',
                        showAllSymbol:true
                    }]
        	};
          $scope.myCodeCharts.setOption(option, true);
        }

        //关闭风险
        $scope.closeRisk = function(){
            var urlData = {
                'name':$scope.name,
                'riskNote':$scope.riskNote,
                'riskFlag':'0'
            }
            codeService.closeRisk(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        inform.common("风险已关闭");
                    } else {
                        inform.common("关闭风险失败");
                    }
                },
                function (error) {
                    inform.common(Trans("tip.requestError"));
                });
        }
        //更新风险描述
        $scope.updateRiskNote = function(){
            $scope.cName
            var urlData = {
                'name':$scope.name,
                'riskNote':$scope.riskNote
            }
            codeService.updateRiskNote(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        inform.common("更新风险描述成功");
                    } else {
                        inform.common("更新风险描述失败");
                    }
                },
                function (error) {
                    inform.common(Trans("tip.requestError"));
                });
        }
        /**
         * *************************************************************
         *              方法声明部分                                 结束
         * *************************************************************
         */
        }]);
})();
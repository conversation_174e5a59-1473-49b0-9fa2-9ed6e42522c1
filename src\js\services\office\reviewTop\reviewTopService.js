(function () {
    'use strict';
    app.factory('reviewTopService', reviewTopService);
    reviewTopService.$inject = ["HttpService", '$rootScope'];

    function reviewTopService(HttpService, $rootScope) {
        var service = {
            getReviewTop: getReviewTop,
            changeStatus: changeStatus
        };
        return service;
        /**
         * 分页查询信息
         * @param urlData 
         */
         function getReviewTop(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'reviewTop/getReviewTop', urlData);
        }

        /**
         * 更改上榜出榜数据
         * @param urlData 
         */
         function changeStatus(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'reviewTop/changeStatus', urlData);
        }
    }
})();
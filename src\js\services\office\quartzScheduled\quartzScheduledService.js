(function() {
	'use strict';
	app.factory('quartzScheduledService', quartzScheduledService);
	quartzScheduledService.$inject = [ "HttpService", '$rootScope' ];

	function quartzScheduledService(HttpService, $rootScope) {
		var service = {
			getJobInfo:getJobInfo,
			insertScheduleJob:insertScheduleJob,
			updateScheduleJob:updateScheduleJob,
			runJob:runJob,
			stopJob:stopJob,
			resumeJob:resumeJob,
			getHistoryInfo:getHistoryInfo,
			getTotalTask:getTotalTask
		};
		return service;

		/**
		 * 查询所有任务信息
		 */
		function getJobInfo(params) {
			return HttpService.post($rootScope.getWaySystemApi + 'scheduleJob/getJobInfo', params);
		}

		/**
		 * 新增任务信息
		 */
		function insertScheduleJob(params) {
			return HttpService.post($rootScope.getWaySystemApi + 'scheduleJob/insertScheduleJob', params);
		}
		/**
		 * 修改所有任务信息
		 */
		function updateScheduleJob(params) {
			return HttpService.post($rootScope.getWaySystemApi + 'scheduleJob/updateScheduleJob', params);
		}
		/**
		 * 立即执行任务
		 */
		function runJob(jobParam) {
			return HttpService.post($rootScope.getWaySystemApi + 'scheduleJob/runJob', jobParam);
		}
		/**
		 * 停用任务
		 */
		function stopJob(params) {
			return HttpService.post($rootScope.getWaySystemApi + 'scheduleJob/stopJob', params);
		}
		/**
		 * 恢复任务
		 */
		function resumeJob(params) {
			return HttpService.post($rootScope.getWaySystemApi + 'scheduleJob/resumeJob', params);
		}
		/**
		 * 获取任务历史信息
		 */
		function getHistoryInfo(params) {
			return HttpService.post($rootScope.getWaySystemApi + 'scheduleJob/getHistoryInfo', params);
		}

		/**
		 * 查询所有定时任务
		 */
		 function getTotalTask(taskId,type) {
			return HttpService.get($rootScope.getWaySystemApi + 'scheduleJob/getTotalTask',
			{'taskId':taskId,'type':type});
		}
	}
})();

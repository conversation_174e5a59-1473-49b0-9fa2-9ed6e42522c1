
(function () {
    'use strict';
    app.factory('internalDemandManagementService', internalDemandManagementService);
    internalDemandManagementService.$inject = ["HttpService", '$rootScope'];

    function internalDemandManagementService(HttpService, $rootScope) {

        var service = {
            getInternalDemand:getInternalDemand,
            getInternalDemandDetail:getInternalDemandDetail,
            updateInternalDemand:updateInternalDemand
        };
        return service;

        function getInternalDemand(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'internalDemandManagement/getInternalDemand', urlData);
        }
        function getInternalDemandDetail(urlData) {
            return HttpService.get($rootScope.getWaySystemApi + 'internalDemandManagement/getInternalDemandDetail', urlData);
        }
        function updateInternalDemand(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'internalDemandManagement/updateInternalDemand',urlData);
        }
    }
})();

//# sourceURL=js/controllers/office/projectRelease/projectRelease.js
(function () {
    app.controller("projectRelease", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','projectReleaseService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,projectReleaseService,inform,Trans,AgreeConstant,LocalCache,$http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //分页
            $scope.pages = inform.initPages(); // 初始化分页数据
            $scope.type = $stateParams.type;
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //获取项目信息
            $scope.loaclParam = LocalCache.getObject('project_detail');
            if($scope.loaclParam.projectInfoParam){
                $scope.projectInfoParam = JSON.parse($scope.loaclParam.projectInfoParam);
            }
            //获取缓存
            $scope.formRefer = LocalCache.getObject('projectRelease_formRefer');
            //对原缓存进行覆盖
            LocalCache.setObject('projectRelease_formRefer', {});
            //初始化时间
            initTime();

            //初始化
            initProductLines();
            //是否下拉框数据源
            $scope.points = [{
                value: '0',
                label: '是'
            }, {
                value: '1',
                label: '否'
            }];
            $scope.pointsCorrelation = [{
                value: '0',
                label: '已关联'
            }, {
                value: '1',
                label: '未关联'
            }];

            $scope.getData = getData; 			// 分页相关函数
            getData();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            function initTime() {
                //设置默认时间
                if ($scope.formRefer.endTime==null){
                    var now = new Date();
                    var endDate = inform.format(now, 'yyyy-MM-dd');
                    var startDate = inform.format(new Date(now.getFullYear(),0,1),"yyyy-MM-dd");
                    //默认开始时间
                    $scope.formRefer.endTime = endDate;
                    $scope.formRefer.startTime = startDate;
                }
            }

            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (165 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 80);
            }
            /**
             * 跳转修改
             */
            $scope.go = function(item) {
                LocalCache.setObject('projectRelease_formRefer',$scope.formRefer);
                $state.go("app.office.projectReleaseUp",{incident:item.incident,projectId: item.id});
            };

            /**
             * 初始化
             */
            function initProductLines() {
                //获取产品线
                $scope.productLines = [];
                comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
                    if (data.data) {
                        $scope.productLines =  data.data;
                    }
                });
            }
            /**
             * 获取所有项目的版本信息
             */
            function getData(pageNum) {
                var urlData = {
                    'productLine': $scope.formRefer.productLine,//产品线
                    'projectId': $scope.formRefer.projectId,//项目名
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间
                    'specpass': $scope.formRefer.specpass, //是否特殊放行
                    'correlation': $scope.formRefer.correlation, //是否关联
                    'page': pageNum,//当前页数
                    'size': $scope.pages.size//每页显示条数
                };
            	if($scope.projectInfoParam && $scope.type!=='menu'){
                    urlData.projectId = $scope.projectInfoParam.cname;
            	}
                $scope.totalNum=0;
                $scope.specialNum=0;
                $scope.specialRate=0;
                projectReleaseService.selectData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            //项目详情
                            $scope.tableData = data.data.list;
                            if ($scope.tableData.length === 0) {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages();
                            } else {
                                //分页信息设置
                                $scope.pages.total = data.data.total;
                                $scope.pages.star = data.data.startRow;
                                $scope.pages.end = data.data.endRow;
                                $scope.pages.pageNum = data.data.pageNum;
                                //计算合计
                                $scope.totalNum = $scope.tableData.length;
                                angular.forEach($scope.tableData, function (i) {
                                    if(i.specialReleaseReasons !== '' && i.specialReleaseReasons != null){
                                        $scope.specialNum = $scope.specialNum+1;
                                    }
                                });
                                $scope.specialRate = ($scope.specialNum/$scope.totalNum*100).toFixed(2);
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            /**
             * excel下载
             */
            $scope.toExcel = function() {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function() {
                            return "确定要下载吗！";
                        }
                    }
                });
                modalInstance.result.then(function() {
                    //拼装下载内容
                    var urlData = {
                        'productLine': $scope.formRefer.productLine,//产品线
                        'projectId': $scope.formRefer.projectId,//项目名
                        'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                        'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间
                        'specpass': $scope.formRefer.specpass, //是否特殊放行
                        'correlation': $scope.formRefer.correlation, //是否关联
                    };
                    inform.downLoadFile ('projectRelease/downloadExcel',urlData,'项目发布管理表.xlsx');

                });
            }
            /**
             * 重置
             */
            $scope.reset = function() {
                $scope.formRefer={};
                initTime();
            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
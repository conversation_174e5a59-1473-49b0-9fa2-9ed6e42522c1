
(function() {
    'use strict';
  app.factory('peerReviewService', peerReviewService);
  peerReviewService.$inject=["HttpService",'$rootScope'];

  function peerReviewService(HttpService,$rootScope){
    var service={

    		loadExcel : loadExcel,
    		getData : getData,
    		getDataByLine : getDataByLine
    };
    return service;

    /**
     * 根据参数查询
     * @param urlData 查询参数 
     */
    function getDataByLine(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'peer/getDataByLine', urlData);
    }
    /**
     * 生成表格
     * @param urlData 查询参数
     */
    function loadExcel(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'peer/loadExcel', urlData);
    }
    
    /**
     * 获取页面展示数据
     * @param urlData 查询参数
     */
    function getData(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'peer/getData', urlData);
    }
  }
})();
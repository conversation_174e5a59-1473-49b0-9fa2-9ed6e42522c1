(function () {
  'use strict';
  app.controller('earlyWarningAddController', [
    '$scope',
    '$state',
    'comService',
    '$rootScope',
    'inform',
    'Trans',
    'AgreeConstant',
    'earlyWarningService',
    '$stateParams',
    'LocalCache',
    '$modal',
    '$http',
    function (
      $scope,
      $state,
      comService,
      $rootScope,
      inform,
      Trans,
      AgreeConstant,
      earlyWarningService,
      $stateParams,
      LocalCache,
      $modal,
      $http
    ) {
      /**
       * *************************************************************
       *             初始化部分                                 开始
       * *************************************************************
       */
      $scope.formInsertin = {};
      $scope.formInsertin.addressee = [];
      $scope.formInsertin.copyTo = [];
      // 节假日策略默认为忽略
      $scope.formInsertin.holidayStrategy = '0';
      $('div.input-group input').attr('disabled', 'disabled');

      //任务状态下拉框
      $scope.jobDisabledSelect = [
        {
          value: '0',
          label: '启用',
        },
        {
          value: '1',
          label: '禁用',
        },
      ];
      // 节假日策略
      $scope.holidayStrategyList = [
        {
          value: '0',
          label: '忽略',
        },
        {
          value: '1',
          label: '发送',
        },
      ];

      // 设置侧边的高度,随窗口变动
      inform.autoHeight();
      window.onresize = inform.autoHeight;
      initPerson();
      //设置列表的高度
      setDivHeight();
      //窗体大小变化时重新计算高度
      $(window).resize(setDivHeight);
      // 抄送人可拖拽
      var draging = null;
      setTimeout(initULObject, 1000 * 6);
      $scope.setDraggableTime = setDraggableTime;
      //获取全部定时任务信息
      earlyWarningService.getTotalTask(0, 'add').then(function (data) {
        $scope.tasksList = data.data;
      });

      /**
       * *************************************************************
       *              初始化部分                                 结束
       * *************************************************************
       */

      /**
       * *************************************************************
       *              方法声明部分                                开始
       * *************************************************************
       */
      /**
       * 收件人、抄送人、密送人名单变化时延迟1秒修改li属性允许拖拽
       */
      function setDraggableTime() {
        setTimeout(setDraggableOfli, 1000 * 1);
      }
      /**
       * 修改li属性允许拖拽
       */
      function setDraggableOfli() {
        var subNodes = document.querySelectorAll('ul.chosen-choices li.search-choice');

        for (var i = 0; i < subNodes.length; i++) {
          $(subNodes[i]).attr('draggable', true);
        }
      }
      /**
       * 初始化ul，允许li元素支持拖拽
       */
      function initULObject() {
        setDraggableOfli();
        var nodes = document.querySelectorAll('ul.chosen-choices');
        //$("div#intro .head")	id="intro" 的 <div> 元素中的所有 class="head" 的元素
        //$("ul li:first")	每个 <ul> 的第一个 <li> 元素
        for (var i = 0; i < nodes.length; i++) {
          var node = nodes[i];
          //使用事件委托，将li的事件委托给ul
          node.ondragstart = dragstartOfli;
          node.ondragover = ondragoverOfli;
        }
      }

      /**
       * 将li的事件委托给ul
       */
      function dragstartOfli(event) {
        //firefox设置了setData后元素才能拖动！！！！
        if (!!window.ActiveXObject || 'ActiveXObject' in window) {
          // ie和其他浏览器不一样。。。
          event.dataTransfer.setData('Text', event.target.innerText);
        } else {
          event.dataTransfer.setData('te', event.target.innerText); //不能使用text，firefox会打开新tab
        }
        draging = event.target;
        if (draging.nodeName === 'SPAN') {
          draging = draging.parentNode;
        }
      }

      /**
       * 将li的事件委托给ul
       */
      function ondragoverOfli(event) {
        event.preventDefault();
        var target = event.target.parentNode;
        //因为dragover会发生在ul上，所以要判断是不是li
        if (target.nodeName === 'LI' && target !== draging) {
          //_index是实现的获取index
          if (_index(draging) < _index(target)) {
            target.parentNode.insertBefore(draging, target.nextSibling);
          } else {
            target.parentNode.insertBefore(draging, target);
          }
        }
      }

      /**
       * 获取li元素的下标
       */
      function _index(el) {
        var index = 0;
        if (!el || !el.parentNode) {
          return -1;
        }
        while (el && el.previousElementSibling) {
          el = el.previousElementSibling;
          index++;
        }
        return index;
      }
      /**
       * 设置列表的高度
       */
      function setDivHeight() {
        //网页可见区域高度
        var clientHeight = document.body.clientHeight;
        var clientWidth = document.body.clientWidth;
        var divHeight = clientHeight - (150 + 180);
        $('#divTBDis').height(divHeight);
        $('#subDivTBDis').height(divHeight - 50);
        $('#buttonStyle').css(inform.getButtonStyle(clientHeight, clientWidth));
      }
      /**
       * 初始化信息
       */
      function initPerson() {
        //获取员工信息
        $scope.employeeList = [];
        comService.getEmployeesByOrgId('').then(function (data) {
          if (data.data) {
            $scope.employeeList = data.data;
          }
        });
      }

      /**
       * 添加信息
       */
      $scope.addInfo = function () {
        var afterTaskIdList = '';
        if (typeof $scope.formInsertin.afterTaskIdList !== 'undefined') {
          afterTaskIdList = $scope.formInsertin.afterTaskIdList.join(',');
        }
        var cronValue = $('#cron').val();
        if (cronValue === '') {
          inform.common('cron表达式不能为空');
          return;
        }
        // 通过dom节点获取收件人
        var addressSelect = document.getElementById('addressSelect').nextElementSibling.childNodes[0].childNodes;
        var addressList = [];
        angular.forEach(addressSelect, function (item) {
          var str = item.innerText;
          var subStr = str.substring(str.indexOf('(') + 1, str.indexOf(')'));
          if (subStr !== '') {
            addressList.push(subStr);
          }
        });
        // 通过dom节点获取抄送人
        var copyTosSelect = document.getElementById('copyTosSelect').nextElementSibling.childNodes[0].childNodes;
        var ccList = [];
        angular.forEach(copyTosSelect, function (item) {
          var str = item.innerText;
          var subStr = str.substring(str.indexOf('(') + 1, str.indexOf(')'));
          if (subStr !== '') {
            ccList.push(subStr);
          }
        });
        // 数组去重
        addressList = Array.from(new Set(addressList));
        var urlData = {
          jobName: $scope.formInsertin.jobName, //任务名称
          beanName: $scope.formInsertin.beanName, //单例名称：
          cronExpression: cronValue, //corn表达式
          remark: $scope.formInsertin.remark, //备注：
          disabled: $scope.formInsertin.disabled, //任务状态
          addressee: addressList.join(), //收件人'
          copyTo: ccList.join(), //抄送
          module: $scope.formInsertin.module, //模板
          afterTaskIds: afterTaskIdList, //后置任务Id
          holidayStrategy: $scope.formInsertin.holidayStrategy, //节假日策略
          type: 1,
        };
        earlyWarningService.insertEarlyWarning(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              inform.common(data.message);
              //添加问题内容
              $scope.formInsertin = {
                jobName: '', //任务名称
                disabled: '', //任务状态
                remark: '', //备注
                beanName: '', //单例名称
                addressee: '',
                copyTo: '',
                module: '',
              };
              $state.go('app.office.earlyWarningController', null);
            } else {
              inform.common(data.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      };
      /**
       * *************************************************************
       *              方法声明部分                                 结束
       * *************************************************************
       */
    },
  ]);
})();

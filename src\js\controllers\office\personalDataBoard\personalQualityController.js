(function() {
    app.controller("personalQualityController", ['lowQualityService', 'blackEventService' ,'qualityAccidentManagementService','$state', 'comService', '$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$stateParams', '$modal', '$http', '$state',
        function(lowQualityService,blackEventService,qualityAccidentManagementService ,state, comService, $rootScope, $scope, inform, Trans, AgreeConstant, LocalCache, $stateParams, $modal, $http, $state) {
            //设置默认时间
            $scope.formRefer = {};
            initData();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //获取数据
            $scope.getData = getData;
            getData();
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 65);
            }
            function initData() {
                $scope.sessionEmpId = LocalCache.getSession('employeeId');
                $scope.formRefer.empId = $stateParams.empId == null ? $scope.sessionEmpId : $stateParams.empId;
                $scope.formRefer.startTime = inform.format(new Date(),"yyyy")+'-01-01';

                checkIsMyself();
            }
            //重置按钮
            $scope.reset = function(){
                $scope.formRefer={};
                $scope.formRefer.startTime = inform.format(new Date(),"yyyy")+'-01-01';
                $scope.formRefer.endTime = '';
            }

            function getData() {
                getLowQuality();
                getBlackEvent();
            }

            function checkIsMyself() {
                $scope.employeeName = LocalCache.getSession('employeeName');
                if($state.params.empId !== LocalCache.getSession("employeeId")) {
                    var person = LocalCache.getObject('personDataBoardEmployee');
                    if(person.name){
                        $scope.employeeName = person.name;
                    }
                }
            }

            function getLowQuality() {
                var urlData = {
                    'personLiable': $scope.employeeName, //责任人员名称
                    'status': '1', //审核状态
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd')
                };
                lowQualityService.getLowQualityInfo(urlData).then(function(data) {
                    if (data.code === '0000') {
                        if(null==data.data){
                            $scope.lowQualityData = {};
                            $scope.lowQualityDataNum = 0;
                        }else {
                            //低级质量问题
                            $scope.lowQualityData = data.data.list;
                            $scope.lowQualityDataNum = $scope.lowQualityData.length;
                        }
                    }
                }, function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            function getBlackEvent(){
                var urlData ={
                    'isAccept':'是',
                    'liablePersonName': $scope.employeeName,
                    'startDate': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endDate': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd')
                };
                blackEventService.getBlackEventList(urlData).then(function(data){
                        if(data.code===AgreeConstant.code){
                            $scope.blackEventList = data.data.list;
                            //调用DataTable组件冻结表头和左侧及右侧的列
                            setTimeout(showDataTable,300);
                        }
                    },
                    function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }



            /**
             *调用DataTable组件冻结表头和左侧及右侧的列
             */
            function showDataTable(){
                $('#fixedLeftAndTop').DataTable( {
                    //可被重新初始化
                    retrieve:       true,
                    //自适应高度
                    scrollY:        'calc(100vh - 350px)',
                    scrollX:        true,
                    scrollCollapse: true,
                    //控制每页显示
                    paging:         false,
                    //冻结列（默认冻结左1）
                    fixedColumns:   {
                        leftColumns: 5,
                        rightColumns: 0
                    },
                    //search框显示
                    searching:      false,
                    //排序箭头
                    ordering:       false,
                    //底部统计数据
                    info:           false
                } );
            }

            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.formRefer.openedStart = true;
                $scope.formRefer.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = false;
                $scope.formRefer.openedEnd = true;
            };
        }
    ]);
})();

/*
 * @Author: fubaole
 * @Date:   2017-09-25 11:39:21
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-19 10:08:55
 */
(function() {
    'use strict';
    app.controller("template_Change", ['$rootScope', '$scope', '$timeout', '$stateParams', 'LocalCache', 'MessageService', 'inform', 'Trans', '$state', 'AgreeConstant',
        function($rootScope, $scope, $timeout, $stateParams, LocalCache, MessageService, inform, Trans, $state, AgreeConstant) {
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            // 获取字典数据
            $scope.widgetCategory = [];
            $scope.alarm_notice_method = [];
            $scope.addNewsInfo = {}; // 根据Id获取消息模板信息
            $scope.onSubmit = onSubmit; // 保存提交
            $scope.getData = getData; // 初始化函数

            getDropDownData("widgetCategory"); // 获取模板类型数据
            getDropDownData("alarm_notice_method"); // 获取消息类型数据
            $scope.getData();// 初始化请求数据

            // 根据Id获取消息模板信息
            function getData() {
                MessageService.getMessageTemplate($stateParams.messageTemplateId)
                    .then(function(data) {
                        // console.log(data);
                        // console.log($stateParams.messageTemplateId);
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.addNewsInfo = data.result;
                            // console.log($scope.addNewsInfo);
                            $scope.addNewsInfo.messageTemplateType = $scope.addNewsInfo.messageTemplateType.toString();
                            // console.log($scope.addNewsInfo.messageTemplateType);
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 获取字典数据
            function getDropDownData(str) {
                MessageService.getDictValueListByDictTypeCode(str)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            if (str==="widgetCategory") {
                                $scope.widgetCategory = data.result;
                                // console.log($scope.widgetCategory);
                            }
                            if (str==="alarm_notice_method") {
                                $scope.alarm_notice_method = data.result;
                                // console.log($scope.alarm_notice_method);
                            }
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 保存修改消息模板并提交
            function onSubmit() {
                MessageService.saveOrUpdateMessageTemplate($scope.addNewsInfo)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            inform.common(Trans("tip.saveSuccess"));
                            $state.go("app.news.news_template");
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

        }
    ]);
})();
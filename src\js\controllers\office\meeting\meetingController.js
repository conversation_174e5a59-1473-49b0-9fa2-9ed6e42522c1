(function () {
    app.controller('meetingController', [
        'comService',
        '$http',
        'LocalCache',
        '$rootScope',
        '$state',
        '$stateParams',
        '$scope',
        '$modal',
        'inform',
        'Trans',
        'AgreeConstant',
        'dayjsService',
        'meetingService',
        'mailService',
        function (
            comService,
            $http,
            LocalCache,
            $rootScope,
            $state,
            $stateParams,
            $scope,
            $modal,
            inform,
            Trans,
            AgreeConstant,
            dayjsService,
            meetingService,
            mailService
        ) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            /**
             * 初始化分页数据
             */
            $scope.pages = inform.initPages();
            //会议状态展示Map
            $scope.meetingStateMap = {
                '00': '草稿',
                '01': '已确定',
            };

            // 外部所有的编辑项
            $scope.changeParam = {};

            if ($stateParams.type == null) {
                $scope.type = 1;
            } else {
                $scope.type = $stateParams.type;
            }
            if ($stateParams.dataInfo != null && $stateParams.dataInfo !== '') {
                $scope.type = $stateParams.dataInfo;
            }
            //会议状态下拉框数据源
            $scope.meetingStateSelect = [
                {
                    value: '00',
                    label: '草稿',
                },
                {
                    value: '01',
                    label: '已确定',
                },
            ];
            $scope.clear = clear;
            $scope.formRefer = {};
            initPages();
            $scope.formRefer = LocalCache.getObject('meet_form');
            $scope.sureWeekTime = sureWeekTime;

            if ($scope.formRefer.weekTime == null) {
                //默认时间(本周)
                $scope.formRefer.weekTime = inform.format(new Date(), 'yyyy-MM-dd');
            }
            LocalCache.setObject('meet_form', {});
            sureWeekTime();

            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.getData = getData; // 分页相关函数
            getData($scope.pages.pageNum, $scope.type); // 分页显示报表信息

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 操作颜色
             */
            $scope.judeUp = function (item) {
                if (item === '01') {
                    return 'green';
                }
                return 'gray';
            };
            /**
             * 判断操作是否可用
             */
            $scope.jude = function (item) {
                if (item === '01') {
                    return false;
                }
                return true;
            };
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 230);
                $('#divTBDis').height(divHeight);
                $('#subDivTBDis').height(divHeight - 50);
                $('#divTBDissel').height(divHeight);
                $('#subDivTBDissel').height(divHeight - 50);
            }
            /**
             * 获取时间改变后的一周的起止时间
             */
            function sureWeekTime() {
                $scope.week = inform.getWeek($scope.formRefer.weekTime);
                $scope.year = inform.getWeekDay($scope.formRefer.weekTime, 7, 'yyyy-MM-dd').substr(0, 4);
                $scope.weekTime =
                    inform.getWeekDay($scope.formRefer.weekTime, 1, 'yyyy-MM-dd') +
                    '至' +
                    inform.getWeekDay($scope.formRefer.weekTime, 7, 'yyyy-MM-dd');
                $scope.monday = inform.getWeekDay($scope.formRefer.weekTime, 1, 'yyyy-MM-dd');
                $scope.sunday = inform.getWeekDay($scope.formRefer.weekTime, 7, 'yyyy-MM-dd');
            }
            /**
             * 发送通知
             */
            $scope.toMail = function (m) {
                var loginer = LocalCache.getSession('currentUserName');
                if (m.loginName !== loginer) {
                    inform.common('与当前会议的组织者不匹配，无权操作');
                    return;
                }
                var urlData = {
                    id: m.id,
                };
                meetingService.getMeeting(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.keyFigureList = data.data.keyFigures;
                            packageMail(m);
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            };
            /**
             * 组装邮件
             */
            function packageMail(m) {
                var urlData = {};
                if (m.meetingType === 'MeetingType_2') {
                    urlData = {
                        beanName: 'com.snbc.office.service.impl.review.ReviewMeetingSeriveImpl',
                        serialNumber: m.id,
                    };
                } else if (m.meetingType === 'MeetingType_1') {
                    urlData = {
                        beanName: 'com.snbc.office.service.impl.OrdinaryMeetingSeriveImpl',
                        serialNumber: m.id,
                    };
                }
                mailService.packageMail(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            var info = data.data;
                            info.addressees = [];
                            info.addresseesHand = [];
                            LocalCache.setObject('meet_form', $scope.formRefer);
                            for (var i = 0; i < $scope.keyFigureList.length; i++) {
                                var name = $scope.keyFigureList[i].keyFigure;
                                if ($scope.loginMap[name] == null || $scope.loginMap[name] === '') {
                                    info.addresseesHand.push(name);
                                } else {
                                    info.addressees.push($scope.loginMap[name] + '@newbeiyang.com');
                                }
                            }
                            info.accessory = m.accessory;
                            info.accessorySize = m.accessorySize;
                            info.theme = m.meetingTheme;
                            LocalCache.setObject('mail_info', info);
                            $state.go('app.office.mailManagement', {
                                root: 'app.office.meetingController',
                                serialNumber: m.id,
                                dataInfo: 2,
                            });
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            /**
             * 页面初始化
             */
            function initPages() {
                //获取会议类别
                $scope.meetingTypeList = [];
                comService.queryEffectiveParam('MeetingType', 'MeetingType').then(function (data) {
                    if (data.data) {
                        $scope.meetingTypeList = data.data;
                    }
                });
                //获取员工信息
                $scope.loginMap = {};
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function (data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                        angular.forEach($scope.employeeList, function (employee) {
                            $scope.loginMap[employee.realName] = employee.loginName;
                        });
                    }
                });
                sureWeekTime();
                //清空查询条件
                $scope.clear();
            }

            /**
             * 清空查询条件
             */
            function clear() {
                $scope.formRefer = {
                    keyFigure: '',
                    meetingTheme: '', //会议主题
                    projectAssistant: '', //项目助理
                    meetingState: '', //会议状态
                    weekTime: inform.format(new Date(), 'yyyy-MM-dd'), //当前时间
                };
                sureWeekTime();
            }

            function getData(pageNum, type) {
                //存查询条件
                var urlData = {
                    keyFigure: $scope.formRefer.keyFigure, //参会人员
                    meetingTheme: $scope.formRefer.meetingTheme, //会议主题
                    projectAssistant: $scope.formRefer.projectAssistant, //项目助理
                    meetingState: $scope.formRefer.meetingState, //会议状态
                    meetingType: $scope.formRefer.meetingType, //会议类别
                    startTime: inform.format($scope.monday, 'yyyy-MM-dd'), //开始时间
                    endTime: inform.format($scope.sunday, 'yyyy-MM-dd'), //结束时间
                    currentPage: pageNum,
                    pageSize: $scope.pages.size,
                };
                if (type === 2 || type === '2') {
                    getMeetingInfo(urlData);
                }
                if (type === 1 || type === '1') {
                    getMeetingInfoGroup(urlData);
                }
            }
            /**
             * 根据分页显示会议
             * pageNum 当前页数
             */
            function getMeetingInfo(urlData) {
                meetingService.getMeetingInfo(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            var jsonData = angular.fromJson(data.data);
                            $scope.dataInfo = jsonData.list;
                            if (jsonData.list.length === 0) {
                                inform.common('无符合条件的会议信息');
                                $scope.pages = inform.initPages(); //初始化分页数据
                            } else {
                                // 分页信息设置
                                $scope.pages.total = jsonData.total; // 页面数据总数
                                $scope.pages.star = jsonData.startRow; // 页面起始数
                                $scope.pages.end = jsonData.endRow; // 页面结束数
                                $scope.pages.pageNum = jsonData.pageNum; //页号
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            /**
             * 分组显示会议信息
             */
            function getMeetingInfoGroup(urlData) {
                meetingService.getMeetingInfoGroup(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            var jsonData = angular.fromJson(data.data);
                            $scope.tableData = jsonData.list;
                            //获取上午最大数值
                            $scope.morningMax = 0;
                            $scope.neigthMax = 0;
                            for (var i = 0; i < $scope.tableData.length; i++) {
                                if ($scope.morningMax < $scope.tableData[i].mroningList.length) {
                                    $scope.morningMax = $scope.tableData[i].mroningList.length;
                                }
                                if ($scope.neigthMax < $scope.tableData[i].neightList.length) {
                                    $scope.neigthMax = $scope.tableData[i].neightList.length;
                                }
                            }
                            //获取全天最大数值
                            var allMax = $scope.morningMax;
                            allMax = allMax + $scope.neigthMax;
                            if (allMax < 3) {
                                allMax = 3;
                            }

                            $('#subDivTBDissel').width(100 + 100 + 100 + 300 * allMax);
                            $('#subTableTBDissel').width(200 + 100 + 300 * allMax);
                            $('#subDivTBDisselTab').width(200 + 100 + 300 * allMax);
                            $('#subMorning').width(100 + 300 * $scope.morningMax + 38);
                            $('#subNeigth').width(100 + 300 * $scope.neigthMax);

                            if (jsonData.list.length === 0) {
                                inform.common('无符合条件的会议信息');
                                $scope.pages = inform.initPages(); //初始化分页数据
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }

            /**
             * 删除信息
             */
            $scope.deleteInfo = function (m) {
                var loginer = LocalCache.getSession('currentUserName');
                if (m.loginName !== loginer) {
                    inform.common('与当前会议的组织者不匹配，无权操作');
                    return;
                }
                inform.modalInstance(Trans('common.deleteTip')).result.then(function () {
                    var urlData = {
                        id: m.id,
                    };
                    meetingService.deleteById(urlData).then(
                        function (data) {
                            if (data.code === AgreeConstant.code) {
                                if (m.roomsId) {
                                    var ids = m.roomsId.split(',');
                                    for (var i = 0; i < ids.length; i++) {
                                        delRoom(ids[i], m);
                                    }
                                } else {
                                    layer.confirm(
                                        data.message,
                                        {
                                            title: false,
                                            btn: ['确定'],
                                        },
                                        function (result) {
                                            layer.close(result);
                                            $scope.getData(1, 2);
                                        }
                                    );
                                }
                            } else {
                                inform.common(data.message);
                            }
                        },
                        function (error) {
                            inform.common(Trans('tip.requestError'));
                        }
                    );
                });
            };
            /**
             * 删除会议室
             */
            function delRoom(id, row) {
                // 判断会议状态：已结束、中途取消还是正常取消
                const currentTime = dayjsService.now().format('YYYY-MM-DD HH:MM:ss');
                // 日期
                const { meetingDay } = row;
                const endDateTime = `${meetingDay} ${row.endTime}:00`;

                // 判断会议是否已结束
                const isMeetingEnded = dayjsService.isAfter(currentTime, endDateTime);
                if (isMeetingEnded || !id) {
                    $scope.getData($scope.pages.pageNum, $scope.type);
                    return;
                }
                // 设置取消类型信息
                const confirmMessage = '该会议已预定成功，确定取消预定吗？';

                layer.confirm(
                    confirmMessage,
                    {
                        title: false,
                        btn: ['确定', '取消'],
                    },
                    function (result) {
                        layer.close(result);
                        cancelMeeting(id, '正常取消');
                    },
                    function (index) {
                        layer.close(index);
                    }
                );
            }

            /**
             * 调用接口取消会议
             * @param {Number} id oa返回的id
             * @param {String} cancellationType 取消类型
             */
            function cancelMeeting(id, cancellationType, index) {
                const urlData = {
                    meetingid: id,
                };
                const api =
                    cancellationType === '中途取消' ? meetingService.cancelMeetingMidway : meetingService.cancelMeeting;
                api(urlData).then(
                    function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.meetingSeeList = $scope.meetingList;
                            inform.common('取消会议成功');
                            $scope.getData($scope.pages.pageNum, $scope.type);
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans('tip.requestError'));
                    }
                );
            }
            /**
             * 页面选中的修改信息复制
             */
            $scope.popModal = function (m) {
                var loginer = LocalCache.getSession('currentUserName');
                if (m.loginName !== loginer) {
                    inform.common('与当前会议的组织者不匹配，无权操作');
                    return;
                }
                LocalCache.setObject('meet_form', $scope.formRefer);
                $state.go('app.office.meetingUpdateController', {
                    serialNumber: m.id,
                });
            };
            /**
             * 克隆
             */
            $scope.copyMeeting = function (m) {
                LocalCache.setObject('meet_form', $scope.formRefer);
                $state.go('app.office.meetingAddController', {
                    serialNumber: m.id,
                });
            };
            /**
             * 跳转至反馈
             */
            $scope.toFeedback = function (m) {
                var loginer = LocalCache.getSession('currentUserName');
                if (m.loginName !== loginer) {
                    inform.common('与当前会议的组织者不匹配，无权操作');
                    return;
                }
                if (m.meetingType === 'MeetingType_2') {
                    LocalCache.setObject('meet_form', $scope.formRefer);
                    $state.go('app.office.peerReviewFeedback', {
                        serialNumber: m.id,
                    });
                } else {
                    LocalCache.setObject('meet_form', $scope.formRefer);
                    $state.go('app.office.meetingUpdateController', {
                        serialNumber: m.id,
                        type: 'return',
                    });
                }
            };
            /**
             * 新增按钮
             */
            $scope.addModal = function () {
                LocalCache.setObject('meet_form', $scope.formRefer);
                $state.go('app.office.meetingAddController');
            };

            /**
             * 选择需要查询会议的时间
             */
            $scope.openTime = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openTime1 = true;
            };
            /**
             * 设置一周开始时间 默认周天0
             */
            $scope.dateOptions = {
                startingDay: 1,
            };
            /**
             * excel下载
             */
            $scope.toExcel = function () {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: 'sm',
                    resolve: {
                        items: function () {
                            return '确定要下载吗！';
                        },
                    },
                });
                modalInstance.result.then(function () {
                    //拼装下载内容
                    var urlData = {
                        meetingTheme: $scope.formRefer.meetingTheme, //会议主题
                        projectAssistant: $scope.formRefer.projectAssistant, //项目助理
                        meetingState: $scope.formRefer.meetingState, //会议状态
                        startTime: inform.format($scope.monday, 'yyyy-MM-dd'), //开始时间
                        endTime: inform.format($scope.sunday, 'yyyy-MM-dd'), //结束时间
                    };
                    inform.downLoadFile('meeting/toExcel', urlData, '会议计划安排表.xlsx');
                });
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        },
    ]);
})();

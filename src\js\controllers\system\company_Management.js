(function() {
  'use strict';
  app.controller("company_Management", ['$rootScope', '$scope', '$stateParams', '$modal', 'SystemService', 'inform', 'Trans','AgreeConstant',
    function($rootScope, $scope, $stateParams, $modal, SystemService, inform, Trans,AgreeConstant) {
      var interfaceMap ={};

      $scope.title = 'companyId';
      $scope.desc  = true;

      $scope.map = {}; //查询条件

      $scope.orderStr = "company_id desc"; // 默认排序字段
      $scope.inputData ={
        row:[
          {
            name:'company.name',
            model:'',
            holder:'company.placeholderName',
            type:'search',
            selectList:''
          },
          {
            name:'company.type',
            model:'',
            holder:'',
            type:'select',
            selectList:[]
          }
        ]
      };
      $scope.order = order; // 排序函数
      $scope.reset = reset; // 排序函数
      $scope.getData = getData; // 初始化函数
      $scope.searchData = searchData; // 查询函数

      $scope.pages = inform.initPages(); // 初始化分页数据
      getCompanyType(); //  获取公司类型
      getData($scope.pages.pageNum); // 初始化请求数据

      // 排序
      function order(str) {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      }

      // 重置
      function reset() {
        $scope.map = {};
      }

      // 查询
      function searchData(str){
        $scope.map.companyName = str.row[0].model;
        $scope.map.companyType = str.row[1].model;
        interfaceMap = angular.copy($scope.map);
        getData(AgreeConstant.pageNum);
      }

      // 获取公司类型
      function getCompanyType() {
        SystemService.getDictValueListByDictTypeCode("company_type")
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.inputData.row[1].selectList = data.result;
              // $scope.companyTypeData =
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 获取表格数据
      function getData(num) {
        if(!num){inform.common(Trans('tip.pageNumTip'));return;}

        SystemService.getCompanyByMap(JSON.stringify(interfaceMap), parseInt(num), $scope.pages.size, $scope.orderStr)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.pages.goNum = null;
              var jsonData = angular.fromJson(data.result);
              $scope.resultData = jsonData.list;
              if ($scope.resultData.length===0) {
                inform.common(Trans("tip.noData"));
                $scope.pages = inform.initPages();
              } else {
                 $scope.pages.total = jsonData.total;
                 $scope.pages.star = (jsonData.pageNum - 1) * jsonData.pageSize + 1;
                 $scope.pages.end = jsonData.pageNum * jsonData.pageSize;
                 $scope.pages.pageNum = jsonData.pageNum;
              }

            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

    }
  ]);
})();
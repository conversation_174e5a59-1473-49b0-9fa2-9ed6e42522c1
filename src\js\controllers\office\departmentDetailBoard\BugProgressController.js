(function () {
  app.controller('BugProgressController', [
    'LocalCache',
    'inform',
    'Trans',
    '$rootScope',
    'comService',
    'AgreeConstant',
    '$scope',
    '$stateParams',
    '$state',
    'deptBoardFactory',
    'BugProgressService',
    function (
      LocalCache,
      inform,
      Trans,
      $rootScope,
      comService,
      AgreeConstant,
      $scope,
      $stateParams,
      $state,
      deptBoardFactory,
      BugProgressService
    ) {
      // 初始化
      deptBoardFactory.init($scope, '15');

      $scope.getData = getData;
      function getData() {
        getTop5Number();
        // 获取top5的数据
        $scope.changeType($scope.type);
      }

      // top5标签上面数字
      function getTop5Number() {
        var currentUrlData = {
          department: $scope.formRefer.orgCode,
        };
        BugProgressService.getBugProgressCount(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.top5 = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // top5部分
      $scope.changeType = function changeType(type) {
        $scope.type = type;
        if (type === '15') {
          // 获取激活数据
          getActivationData('active', 'active');
        } else if (type === '16') {
          // 获取已解决未验证数据
          getResolvedData('resolved', 'fixed', 'resolve');
        } else if (type === '17') {
          // 获取延期处理数据
          getDeferredData('resolved', 'postponed', 'resolve');
        } else if (type === '18')  {
          //获取无法重现数据
          getNotreproData('resolved', 'notrepro', 'resolve');
        } else if (type === '19')  {
          //获取设计如此数据
          getByDesignData('resolved', 'bydesign', 'resolve');
        } else if (type === '20')  {
          //获取外部原因数据
          getExternalData('resolved', 'external', 'resolve');
        }
      };
      // 获取激活数据
      function getActivationData(bugStatus, active) {
        var currentUrlData = {
          department: $scope.formRefer.orgCode,
          bugStatus: bugStatus,
          active: active,
        };
        BugProgressService.getBugProgressDetail(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.activationData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // 获取设计如此数据
      function getByDesignData(bugStatus, solution, resolve) {
        var currentUrlData = {
          department: $scope.formRefer.orgCode,
          bugStatus: bugStatus,
          solution: solution,
          resolve: resolve,
        };
        BugProgressService.getBugProgressDetail(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.bydesignDate = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // 获取外部原因数据
      function getExternalData(bugStatus, solution, resolve) {
        var currentUrlData = {
          department: $scope.formRefer.orgCode,
          bugStatus: bugStatus,
          solution: solution,
          resolve: resolve,
        };
        BugProgressService.getBugProgressDetail(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.externalData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // 获取无法重现数据
      function getNotreproData(bugStatus, solution, resolve) {
        var currentUrlData = {
          department: $scope.formRefer.orgCode,
          bugStatus: bugStatus,
          solution: solution,
          resolve: resolve,
        };
        BugProgressService.getBugProgressDetail(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.notreproData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // 获取已解决未验证数据
      function getResolvedData(bugStatus, solution, resolve) {
        var currentUrlData = {
          department: $scope.formRefer.orgCode,
          bugStatus: bugStatus,
          solution: solution,
          resolve: resolve,
        };
        BugProgressService.getBugProgressDetail(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.resolvedData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // 获取延期处理数据
      function getDeferredData(bugStatus, solution, resolve) {
        var currentUrlData = {
          department: $scope.formRefer.orgCode,
          bugStatus: bugStatus,
          solution: solution,
          resolve: resolve,
        };
        BugProgressService.getBugProgressDetail(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.deferredData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // 页面加载后触发
      $scope.$watch('$viewContentLoaded', function () {
        var localFormRefer = LocalCache.getObject('departmentList_formRefer');
        if (Object.keys(localFormRefer).length > 0) {
          $scope.formRefer = localFormRefer;
          $scope.butFlag = localFormRefer.searchTimeString;
        }
        if ($stateParams.orgCode) {
          $scope.formRefer.orgCode = $stateParams.orgCode;
        }
        getData();
      });
      $scope.title = '';
      $scope.desc = true;
      // 排序
      $scope.order = (str) => {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      };
    },
  ]);
})();

/*
* @Author: haohongmin
* @Date:   2017-12-05 14:00:00
* @Last Modified by:   fubaole
* @Last Modified time: 2018-02-26 18:09:49
*/
(function() {
  'use strict';
  app.factory('MessageService', MessageService);
  MessageService.$inject=['HttpService','$rootScope','inform','LocalCache'];
  function MessageService(HttpService,$rootScope,inform,LocalCache){
    var service={
      // 消息模板
    	getMessageTemplateByMap:getMessageTemplateByMap,
      getMessageTemplate:getMessageTemplate,
      saveOrUpdateMessageTemplate:saveOrUpdateMessageTemplate,
      removeMessageTemplate:removeMessageTemplate,
      getDictValueListByDictTypeCode:getDictValueListByDictTypeCode,

      // 发送消息
      getNoticeMessageByMap:getNoticeMessageByMap,
      getUserNoticeMessageByMessageId:getUserNoticeMessageByMessageId,
      removeNoticeMessageByIds:removeNoticeMessageByIds,
      reSendMessage:reSendMessage,

      // 接收消息
      getReceiveMessageByMap:getReceiveMessageByMap,
      removeUserNoticeMessageByIds:removeUserNoticeMessageByIds,
      getReceiveMessageForUpdate:getReceiveMessageForUpdate,
      updateUserNoticeMessageStatus:updateUserNoticeMessageStatus,


    };
    return service;


    //=================================消息模板配置接口=============================================//
    // 获取消息模板列表
    function getMessageTemplateByMap(map,pageNum,pageSize,orderStr){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(map));
      urlData = inform.formateGetRequest(urlData,"pageNum",pageNum);
      urlData = inform.formateGetRequest(urlData,"pageSize",pageSize);
      urlData = inform.formateGetRequest(urlData,"orderStr",orderStr);
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.messageName+'messageTemplate/getMessageTemplateByMap'+urlData);
    }

    // 根据模板ID获取消息模板信息
    function getMessageTemplate(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.messageName+'messageTemplate/getMessageTemplate?messageTemplateId='+id);
    }

    // 保存修改消息模板
    function saveOrUpdateMessageTemplate(param) {
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.messageName+'messageTemplate/saveOrUpdateMessageTemplate?messageTemplateJson='+encodeURIComponent(JSON.stringify(param)));
    }

    // 根据模板ID删除模板
    function removeMessageTemplate(id) {
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.messageName+'messageTemplate/removeMessageTemplate?messageTemplateIds='+id);
    }

    // 获取模板类型和消息类型值
    function getDictValueListByDictTypeCode(code) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'dict/getDictValueListByDictTypeCode?dictTypeCode='+code);
    }


    //=================================发送消息配置接口=============================================//
    // 获取发送消息列表（获取通知消息）
    function getNoticeMessageByMap(map,pageNum,pageSize,orderStr){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(map));
      urlData = inform.formateGetRequest(urlData,"pageNum",pageNum);
      urlData = inform.formateGetRequest(urlData,"pageSize",pageSize);
      urlData = inform.formateGetRequest(urlData,"orderStr",orderStr);
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.messageName+'noticeMessage/getNoticeMessageByMap'+urlData);
    }
    // 根据ID 获取消息用户列表
    function getUserNoticeMessageByMessageId(id){
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.messageName+'noticeMessage/getUserNoticeMessageByMessageId?messageId='+id);
    }
    // 根据ID 删除通知消息
    function removeNoticeMessageByIds(id){
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.messageName+'noticeMessage/removeNoticeMessageByIds?noticeMessageIds='+id);
    }
    // 重新发送消息
    function reSendMessage(id){
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.messageName+'message/reSendMessage/'+id);
    }

    //=================================接收消息配置接口=============================================//
    // 获取接收消息列表（获取通知消息）
    function getReceiveMessageByMap(map,pageNum,pageSize,orderStr){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(map));
      urlData = inform.formateGetRequest(urlData,"pageNum",pageNum);
      urlData = inform.formateGetRequest(urlData,"pageSize",pageSize);
      urlData = inform.formateGetRequest(urlData,"orderStr",orderStr);
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.messageName+'noticeMessage/getReceiveMessageByMap'+urlData);
    }

    // 删除当前用户接收的消息
    function removeUserNoticeMessageByIds(userNoticeMessageIds) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"userNoticeMessageIds",userNoticeMessageIds);
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.messageName+'noticeMessage/removeUserNoticeMessageByIds'+urlData);
    }

    // 根据ID获取接收消息详情
    function getReceiveMessageForUpdate(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.messageName+'noticeMessage/getReceiveMessageForUpdate?messageId='+id);
    }

    // 批量标记为已读
    function updateUserNoticeMessageStatus(noticeMessageIds){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"noticeMessageIds",noticeMessageIds);
      return HttpService.put($rootScope.getWaySystemApi+$rootScope.authorityName+'noticeMessage/updateUserNoticeMessageStatus'+urlData);
    }


    }

})();
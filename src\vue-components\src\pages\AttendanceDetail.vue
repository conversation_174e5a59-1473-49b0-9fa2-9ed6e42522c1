<template>
  <div class="attendance-detail-vue">
    <!-- 搜索条件 -->
    <el-card class="search-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>查询条件</span>
          <div class="search-buttons">
            <el-button type="primary" @click="handleSearch" :icon="Search">
              查询
            </el-button>
            <el-button @click="handleReset" :icon="Refresh">
              重置
            </el-button>
          </div>
        </div>
      </template>
      
      <el-form :model="searchForm" label-width="120px" :inline="false">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="一级部门">
              <el-select 
                v-model="searchForm.primaryDeptCode" 
                placeholder="请选择"
                @change="handleDeptChange"
                clearable
              >
                <el-option
                  v-for="item in primaryDeptList"
                  :key="item.orgCode"
                  :label="item.orgName"
                  :value="item.orgCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="6">
            <el-form-item label="二级部门">
              <el-select 
                v-model="searchForm.departmentCode" 
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in departmentList"
                  :key="item.orgCode"
                  :label="item.orgName"
                  :value="item.orgCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="6">
            <el-form-item label="姓名">
              <el-input 
                v-model="searchForm.realName" 
                placeholder="请输入姓名"
                clearable
                @keyup.enter="handleSearch"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="6">
            <el-form-item label="状态">
              <el-select v-model="searchForm.status" placeholder="请选择" clearable>
                <el-option label="正常" value="正常" />
                <el-option label="异常" value="异常" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="地区">
              <el-select v-model="searchForm.region" placeholder="请选择" clearable>
                <el-option
                  v-for="item in areaList"
                  :key="item.param_code"
                  :label="item.param_value"
                  :value="item.param_code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="6">
            <el-form-item label="考勤日期">
              <el-date-picker
                v-model="searchForm.startTime"
                type="date"
                placeholder="请选择开始时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="6">
            <el-form-item label="至">
              <el-date-picker
                v-model="searchForm.endTime"
                type="date"
                placeholder="请选择结束时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="6">
            <el-form-item label="是否工作日">
              <el-select v-model="searchForm.holidayWorkFlag" placeholder="请选择" clearable>
                <el-option label="是" value="0" />
                <el-option label="否" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 标签页 -->
    <el-card class="tab-card" shadow="never">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="考勤明细" name="1" />
        <el-tab-pane label="考勤延时打卡汇总" name="2" />
      </el-tabs>
    </el-card>

    <!-- 操作按钮和汇总信息 -->
    <el-card class="operation-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>查询结果</span>
          <el-button type="success" @click="handleExport" :icon="Download">
            下载Excel
          </el-button>
        </div>
      </template>
      
      <!-- 汇总信息 -->
      <div v-if="!isInfoCardClose" class="summary-container">
        <el-button 
          class="close-btn" 
          type="text" 
          @click="toggleInfoCard"
          :icon="Close"
        />
        <el-row :gutter="20">
          <el-col :span="8" v-for="item in summaryData" :key="item.label">
            <el-card class="summary-card" shadow="hover">
              <div class="summary-label">{{ item.label }}</div>
              <div class="summary-details">
                <div class="detail-item">
                  <span class="detail-label">{{ item.workDayTitle }}:</span>
                  <span class="detail-value">{{ item.workday }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">{{ item.nonWorkDayTitle }}:</span>
                  <span class="detail-value">{{ item.nonWorkday }}</span>
                </div>
                <div class="detail-item" v-if="showAbsentPunchInfo || !item.label.startsWith('总计')">
                  <span class="detail-label">{{ item.absentPunchCountTitle }}:</span>
                  <span class="detail-value">{{ item.absentPunchCount }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      
      <el-button 
        v-else 
        type="primary" 
        @click="toggleInfoCard"
        class="info-toggle-btn"
      >
        汇总信息
      </el-button>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <!-- 考勤明细表格 -->
      <el-table 
        v-if="activeTab === '1'"
        :data="attendanceDetailList" 
        stripe 
        border
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column type="index" label="NO." width="60" />
        <el-table-column prop="employeeName" label="员工姓名" />
        <el-table-column prop="departmentName" label="二级部门" />
        <el-table-column prop="primaryDept" label="一级部门" />
        <el-table-column prop="regionName" label="区域" />
        <el-table-column prop="workDate" label="考勤日期" />
        <el-table-column prop="startWorkTime" label="上班打卡时间" align="center" />
        <el-table-column prop="endWorkTime" label="下班打卡时间" align="center" />
        <el-table-column prop="workOvertime" label="延时打卡工时(时)" align="center" sortable />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === '正常' ? 'success' : 'danger'">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="holidayWork" label="是否工作日">
          <template #default="{ row }">
            <el-tag :type="!row.holidayWork ? 'success' : 'warning'">
              {{ row.holidayWork ? '非工作日' : '工作日' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 延时打卡汇总表格 -->
      <el-table 
        v-if="activeTab === '2'"
        :data="workOverTimeGroupByPersonList" 
        stripe 
        border
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column type="index" label="NO." width="60" />
        <el-table-column prop="employeeName" label="员工姓名" />
        <el-table-column prop="departmentName" label="二级部门" />
        <el-table-column prop="primaryDept" label="一级部门" />
        <el-table-column prop="regionName" label="区域" />
        <el-table-column prop="workOverDays" label="延时打卡合计(天)" align="center" sortable />
        <el-table-column prop="workOverHours" label="延时打卡合计(时)" align="center" sortable />
        <el-table-column prop="noHolidayWorkOverHours" label="工作日延时打卡工时(时)" align="center" sortable />
        <el-table-column prop="holidayWorkOverHours" label="非工作日延时打卡工时(时)" align="center" sortable />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { Search, Refresh, Download, Close } from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('1')
const loading = ref(false)
const isInfoCardClose = ref(false)
const showAbsentPunchInfo = ref(true)

// 搜索表单
const searchForm = reactive({
  primaryDeptCode: '',
  departmentCode: '',
  realName: '',
  status: '',
  region: '',
  startTime: '',
  endTime: '',
  holidayWorkFlag: '0'
})

// 分页信息
const pagination = reactive({
  pageNum: 1,
  size: 20,
  total: 0
})

// 数据列表
const primaryDeptList = ref([])
const departmentList = ref([])
const areaList = ref([])
const attendanceDetailList = ref([])
const workOverTimeGroupByPersonList = ref([])
const summaryData = ref([])

// 方法
const handleSearch = () => {
  pagination.pageNum = 1
  getData()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (key === 'holidayWorkFlag') {
      searchForm[key] = '0'
    } else {
      searchForm[key] = ''
    }
  })
  handleSearch()
}

const handleDeptChange = () => {
  searchForm.departmentCode = ''
  // 这里应该调用获取二级部门的接口
  // loadDepartmentList()
}

const handleTabChange = (tabName) => {
  activeTab.value = tabName
  getData()
}

const handleExport = () => {
  // 导出Excel逻辑
  console.log('导出Excel')
}

const toggleInfoCard = () => {
  isInfoCardClose.value = !isInfoCardClose.value
}

const handleSizeChange = (size) => {
  pagination.size = size
  getData()
}

const handleCurrentChange = (page) => {
  pagination.pageNum = page
  getData()
}

const getData = () => {
  loading.value = true
  // 这里应该调用实际的API
  // 模拟数据加载
  setTimeout(() => {
    loading.value = false
    // 模拟数据
    if (activeTab.value === '1') {
      attendanceDetailList.value = []
    } else {
      workOverTimeGroupByPersonList.value = []
    }
  }, 1000)
}

// 组件挂载时初始化数据
onMounted(() => {
  // 初始化下拉列表数据
  // loadPrimaryDeptList()
  // loadAreaList()
  getData()
})

// 监听props变化（如果需要从AngularJS传递数据）
const props = defineProps({
  initialData: {
    type: Object,
    default: () => ({})
  }
})

// 暴露方法给父组件调用
defineExpose({
  handleSearch,
  handleReset,
  getData
})
</script>

<style scoped>
.attendance-detail-vue {
  padding: 20px;
}

.search-card,
.tab-card,
.operation-card,
.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-buttons {
  display: flex;
  gap: 10px;
}

.summary-container {
  position: relative;
  margin-bottom: 20px;
}

.close-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  z-index: 10;
}

.summary-card {
  margin-bottom: 10px;
}

.summary-label {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 12px;
  color: #2c3e50;
}

.summary-details {
  display: flex;
  flex-direction: column;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-label {
  color: #7f8c8d;
}

.detail-value {
  font-weight: bold;
  color: #2980b9;
}

.info-toggle-btn {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>

<template>
  <div class="attendance-detail-vue">
    <!-- 搜索条件 -->
    <el-card class="search-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>查询条件</span>
          <div class="search-buttons">
            <el-button type="primary" @click="handleSearch" :icon="Search">
              查询
            </el-button>
            <el-button @click="handleReset" :icon="Refresh">
              重置
            </el-button>
          </div>
        </div>
      </template>
      
      <el-form :model="searchForm" label-width="120px" :inline="false">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="一级部门">
              <el-select 
                v-model="searchForm.primaryDeptCode" 
                placeholder="请选择"
                @change="handleDeptChange"
                clearable
              >
                <el-option
                  v-for="item in primaryDeptList"
                  :key="item.orgCode"
                  :label="item.orgName"
                  :value="item.orgCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="6">
            <el-form-item label="姓名">
              <el-input 
                v-model="searchForm.realName" 
                placeholder="请输入姓名"
                clearable
                @keyup.enter="handleSearch"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="6">
            <el-form-item label="状态">
              <el-select v-model="searchForm.status" placeholder="请选择" clearable>
                <el-option label="正常" value="正常" />
                <el-option label="异常" value="异常" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="6">
            <el-form-item label="考勤日期">
              <el-date-picker
                v-model="searchForm.startTime"
                type="date"
                placeholder="请选择开始时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>考勤明细</span>
          <el-button type="success" @click="handleExport" :icon="Download">
            下载Excel
          </el-button>
        </div>
      </template>
      
      <el-table 
        :data="attendanceDetailList" 
        stripe 
        border
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column type="index" label="NO." width="60" />
        <el-table-column prop="employeeName" label="员工姓名" />
        <el-table-column prop="departmentName" label="二级部门" />
        <el-table-column prop="workDate" label="考勤日期" />
        <el-table-column prop="startWorkTime" label="上班打卡时间" align="center" />
        <el-table-column prop="endWorkTime" label="下班打卡时间" align="center" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === '正常' ? 'success' : 'danger'">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Search, Refresh, Download } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)

// 搜索表单
const searchForm = reactive({
  primaryDeptCode: '',
  realName: '',
  status: '',
  startTime: ''
})

// 分页信息
const pagination = reactive({
  pageNum: 1,
  size: 20,
  total: 0
})

// 数据列表
const primaryDeptList = ref([])
const attendanceDetailList = ref([])

// 方法
const handleSearch = () => {
  pagination.pageNum = 1
  getData()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

const handleDeptChange = () => {
  // 部门变化逻辑
}

const handleExport = () => {
  console.log('导出Excel')
}

const handleSizeChange = (size) => {
  pagination.size = size
  getData()
}

const handleCurrentChange = (page) => {
  pagination.pageNum = page
  getData()
}

const getData = () => {
  loading.value = true
  // 模拟数据加载
  setTimeout(() => {
    loading.value = false
    // 模拟数据
    attendanceDetailList.value = [
      {
        employeeName: '张三',
        departmentName: '技术部',
        workDate: '2024-01-15',
        startWorkTime: '09:00',
        endWorkTime: '18:00',
        status: '正常'
      },
      {
        employeeName: '李四',
        departmentName: '产品部',
        workDate: '2024-01-15',
        startWorkTime: '09:30',
        endWorkTime: '18:30',
        status: '异常'
      }
    ]
    pagination.total = 2
  }, 1000)
}

// 组件挂载时初始化数据
onMounted(() => {
  getData()
})

// 暴露方法给父组件调用
defineExpose({
  handleSearch,
  handleReset,
  getData
})
</script>

<style scoped>
.attendance-detail-vue {
  padding: 20px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-buttons {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>

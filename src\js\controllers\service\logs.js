(function() {
  'use strict';
  app.controller("logs", ['$rootScope', '$scope', '$filter', '$interval', '$stateParams', 'inform', 'Microservice', 'Trans',
    function($rootScope, $scope, $filter, $interval, $stateParams, inform, Microservice, Trans) {

      $scope.getRoutes = getRoutes;
      $scope.changeLevel = changeLevel;
      $scope.changeRouteInfo = changeRouteInfo;
      $scope.getRoutes();
      $scope.configTimer = 0;
      $scope.serviceType = "uaa";
      $scope.appName = "register";
      $scope.result = [{ "appName": "register" }];

      $scope.$watch('$scope.configTimer', function(newValue, oldValue) {
        if (newValue !== oldValue) {
          $interval.cancel($scope.interval);
          if (newValue !== '0') {
            $scope.interval = $interval(function() {
              // console.log($scope.timer);
              $scope.getRoutes();
              findAll();
            }, $scope.configTimer);
          }
        }
      });

      $scope.$on('$destroy', function() {
        $interval.cancel($scope.interval);
        //$interval.stop(interval);
      });
      // 获取应用
      function getRoutes() {
        Microservice.getRoutes()
          .then(function(data) {
            for (var i = 0; i < data.length; i++) {
              $scope.result.push(data[i]);
            }
            console.log(data);
            findAll();
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      function changeRouteInfo() {
        findAll();
      }


      function findAll() {
        Microservice.getLogsAll($scope.appName).then(function(data) {
          $scope.loggers = data;
        }, function(error) {
          inform.common(Trans("tip.requestError"));
        });
      }

      function changeLevel(name, level) {
        var jsonStr = { "name": name, "level": level };
        Microservice.searchLogs(jsonStr, $scope.appName).then(function(data) {
          findAll();
        }, function(error) {
          inform.common(Trans("tip.requestError"));
        });
      }


    }
  ]);
})();
/*
 * @Author: haoh<PERSON>min
 * @Date:   2017-10-10 17:25:38
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-19 10:12:53
 */
(function() {
    'use strict';
    app.controller("userGroup_Add", ['$rootScope', '$scope', '$state', 'inform', '$stateParams', '$modal', '$log', 'Trans', 'SystemService', 'AgreeConstant',
        function($rootScope, $scope, $state, inform, $stateParams, $modal, $log, Trans, SystemService, AgreeConstant) {
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.group = {}; // 条件

            // 获取所有角色类型
            $scope.map = {}; //条件
            $scope.roleTypeData = {};
            getRoleTypeData(); // 获取所有角色类型
            $scope.selected = []; // 存放选中的id
            $scope.updataType = updataType; // 角色选择

            // 新增用户获取用户列表
            $scope.list = { 'islock': AgreeConstant.unfreezeStatus};
            $scope.addUserData = [];

            $scope.reset = reset; // 重置
            $scope.addGetUserList = addGetUserList; // 根据查询条件获取用户列表
            $scope.checked = []; // 存放选中的ID
            $scope.selectAll = selectAll; // 全选
            $scope.selectOne = selectOne; // 选中一个
            $scope.open = open; // 详情页用户List删除Item
            $scope.addUserfun = addUserfun; // 点击创建用户按钮，将选中的条目传到详情页
            $scope.onSubmit = onSubmit; //保存提交操作
            $scope.deletedAddUser = deletedAddUser; // 删除添加的用户

            // 获取所有角色类型
            function getRoleTypeData() {
                SystemService.getRoleByLoginUserIdMap(JSON.stringify($scope.map))
                    .then(function(data) {
                        if (data.code === AgreeConstant.resultCode) {
                            var jsonData = data.result;
                            $scope.roleTypeData = jsonData;
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 角色选择
            function updataType() {
                angular.forEach($scope.roleTypeData, function(i) {
                    var index = $scope.selected.indexOf(i);
                    if (i.selected && index === -1) {
                        $scope.selected.push(i);
                    } else if (!i.selected && index !== -1) {
                        $scope.selected.splice(index, 1);
                    }
                });
                $scope.group.roleName = $scope.selected;
                console.log($scope.group.roleName);
            }

            // 重置函数
            function reset() {
                $scope.list.employeeNo = "";
                $scope.list.realName = "";
                $scope.list.orgName = "";
            }

            // 根据查询条件获取用户列表
            function addGetUserList() {
                $scope.checked = [];
                $scope.select_all = false;
                SystemService.getUserListByMap(JSON.stringify($scope.list))
                    .then(function(data) {
                        if (data.code === AgreeConstant.resultCode) {
                            if (data.result.length === 0) {
                                inform.common(Trans("tip.noData"));
                            }
                            $scope.addGetUsers = data.result;
                            removeHasUser();
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 弹窗中数据选择操作
            function selectAll() {
                if ($scope.select_all) {
                    $scope.checked = [];
                    angular.forEach($scope.addGetUsers, function(i) {
                        $scope.checked.push(i);
                        i.checked = true;
                    });
                } else {
                    angular.forEach($scope.addGetUsers, function(i) {
                        i.checked = false;
                    });
                    $scope.checked = [];
                }
            }

            //选择一个
            function selectOne(i) {
                var index = $scope.checked.indexOf(i);
                if (index === -1 && i.checked) {
                    $scope.checked.push(i);
                } else if (index !== -1 && !i.checked) {
                    $scope.checked.splice(index, 1);
                }
                if ($scope.addGetUsers.length === $scope.checked.length) {
                    $scope.select_all = true;
                } else {
                    $scope.select_all = false;
                }
            }

            // 详情页用户List删除Item
            function open(item) {
                var index = $scope.addUserData.indexOf(item);
                if (index !== -1) {
                    $scope.addUserData.splice(index, 1);
                }
            }

            //点击新增,用户选择列表移除详情页已有项
            function removeHasUser() {
                angular.forEach($scope.addGetUsers, function(obj, i) {
                    angular.forEach($scope.addUserData, function(obj, j) {
                        if ($scope.addGetUsers[i].userId === $scope.addUserData[j].userId) {
                            // 移除在详情页面已显示的用户
                            // $scope.addGetUsers[i].checked = true;
                            $scope.addGetUsers.splice(i, 1);
                        }
                    });
                });
            }

            // 点击创建用户按钮，将选中的条目传到详情页
            function addUserfun() {
                if ($scope.checked.length !== 0) {
                    angular.forEach($scope.checked, function(obj, index) {
                        $scope.addUserData.push($scope.checked[index]);
                    });

                    $scope.checked = [];
                    $scope.reset();
                    $("#add_user").modal("hide");
                } else {
                    inform.common(Trans('common.chooseOneOpt'));
                }
            }

            // 删除添加的用户
            function deletedAddUser(item) {
                var index = $scope.addUserData.indexOf(item);
                if (index !== -1) {
                    $scope.addUserData.splice(index, 1);
                }
            }

            // 保存提交操作(保存用户组信息、角色、用户列表)
            function onSubmit() {
                var userIds = "";
                angular.forEach($scope.addUserData, function(obj, i) {
                    if (i === $scope.addUserData.length - 1) {
                        userIds += $scope.addUserData[i].userId;
                    } else {
                        userIds += $scope.addUserData[i].userId + ',';
                    }
                });

                var roleIds = '';
                angular.forEach($scope.selected, function(obj, j) {
                    if (j === $scope.selected.length - 1) {
                        roleIds += $scope.selected[j].roleId;
                    } else {
                        roleIds += $scope.selected[j].roleId + ',';
                    }
                });

                SystemService.saveGroupAndGroupToRoleUser($scope.group, userIds, roleIds)
                    .then(function(data) {
                        if (data.code === AgreeConstant.resultCode) {
                            inform.common(Trans("tip.saveSuccess"));
                            $state.go("app.system.userGroup_Management");
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }
        }
    ]);
})();
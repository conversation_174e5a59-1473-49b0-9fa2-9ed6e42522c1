(function() {
    'use strict';
    app.factory('aggregateStatisticService', aggregateStatisticService);
    aggregateStatisticService.$inject=["HttpService",'$rootScope'];

    function aggregateStatisticService(HttpService,$rootScope){

        // 获取月度数据统计
        function getMonthCodeDataList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getCommitTimes',urlData);
        }

        function getMonthCommitTimesList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'codeDataReport/getCommitInfoOfPost',urlData);
        }




        return {
            getMonthCodeDataList: getMonthCodeDataList,
            getMonthCommitTimesList: getMonthCommitTimesList,
        };
    }
})();
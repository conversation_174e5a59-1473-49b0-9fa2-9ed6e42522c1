(function () {
    app.controller("keyTechIssueManagementController", ['keyTechIssueService', '$state', 'comService', '$rootScope', '$scope', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$stateParams', '$modal', '$http', '$state',
        function (keyTechIssueService, state, comService, $rootScope, $scope, inform, Trans, AgreeConstant, LocalCache, $stateParams, $modal, $http, $state) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //获取缓存
            $scope.formRefer = LocalCache.getObject('keyIssueController_formRefer');
            //设置默认时间
            if ($scope.formRefer.startTime == null) {
                var day_180 = new Date().setMonth((new Date().getMonth() - 6))
                $scope.formRefer.startTime = inform.format(new Date(day_180), 'yyyy-MM-dd');
            }
            //清除缓存
            LocalCache.setObject('keyIssueController_formRefer', {});
            $scope.flag = $stateParams.flag;
            $scope.createUser = LocalCache.getSession('employeeId');
            //获取部门
            $scope.deptList = [];
            comService.getOrgChildren('D010053').then(function (data) {
                $scope.deptList = comService.getDepartment(data.data);
            });
             //页面分页信息
             $scope.pages = {
                 pageNum: '', //分页页数
                 size: '100', //分页每页大小
                 total: '' //数据总数
             };
            //初始化
            initPages();
            //初始化当前时间
            $scope.datepicker = {
                currentDate: new Date()
            };
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);

            //获取数据
            $scope.getData = getData;

            //初始化页面信息

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 65);
                if ($scope.flag === 'verify') {
                    $("#subDivTBDis").height(divHeight + 8);
                }
            }

            //重置按钮
            $scope.reset = function () {
                $scope.formRefer = {};
                var day_180 = new Date().setMonth((new Date().getMonth() - 6))
                $scope.formRefer.startTime = inform.format(new Date(day_180), 'yyyy-MM-dd');
                $("#form")[0].reset();
                $("#fileNameDis").text("");
                $scope.deptList = [];
            }

            /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function (data) {
                    if (data.data) {
                        $scope.primaryDeptList = data.data;
                    }
                });

            }

            /**
             * 页面初始化
             */
            function initPages() {
                initPrimaryDeptList();

                //获取员工信息
                $scope.employeesList = [];
                comService.getEmployeesByOrgId('').then(function (data) {
                    $scope.employeeList = data.data;
                });
                //关键技术问题类型
                $scope.questionResourceList = ['年度技术规划', '项目技术难题', '紧急质量问题'];
                $scope.technologeyLineList = ['平台类', '驱动类', '终端应用类'];
                $scope.questionLevelList = ['一般技术问题', '适中技术问题', '偏难技术问题', '超难技术问题'];
                $scope.status = ['审批中', '审批通过', '审批拒绝'];
                $scope.purposeAngleList = ['新技术领域公关','产品技术创新','性能指标提升','可靠性能力提升','研发效率提升'];
                $scope.isCollegialPanelList = ['是','否'];
                //获取山东新北洋集团的下级部门信息
                $scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function (data) {
                    $scope.departmentList = comService.getDepartment(data.data);
                });
                getData();
            }

            /**
             * 获取关键技术问题
             *
             */
            function getData(pages) {
                var urlData = {
                    'departmentCode': $scope.formRefer.departmentCode,
                    'creator': $scope.formRefer.creator, //创建人名称
                    'reviewResult': $scope.formRefer.reviewResult, //审核结果
                    'questionResource': $scope.formRefer.questionResource, //问题来源
                    'solvePerson': $scope.formRefer.solvePerson, //解决人
                    'purposeAngle': $scope.formRefer.purposeAngle, //目的角度
                    'technologeyLine': $scope.formRefer.technologeyLine,//技术线
                    'questionLevel': $scope.formRefer.questionLevel,//问题等级
                    'isCollegialPanel': $scope.formRefer.isCollegialPanel,//是否合议
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),
                    'page': pages,
                    'size': $scope.pages.size
                };
                keyTechIssueService.getKeyIssueInfo(urlData).then(function (data) {
                    if (data.code === '0000') {

                        if (null == data.data) {
                            $scope.keyIssueData = {};
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages(); //初始化分页数据
                        } else {
                            //关键技术问题
                            $scope.keyIssueData = data.data.list;
                            // 分页信息设置
                            $scope.pages.total = data.data.total;
                            $scope.pages.star = data.data.startRow;
                            $scope.pages.end = data.data.endRow;
                            $scope.pages.pageNum = data.data.pageNum;
                        }
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            //查看
            $scope.toModify = function (m) {
                LocalCache.setObject('keyIssueController_formRefer', $scope.formRefer);
                $state.go("app.office.keyTechIssueDetail", {
                    item: m.approvalId
                });
            }

            /**
             * excel下载
             */
            $scope.toExcel = function () {
                inform.modalInstance("确定要下载吗?").result.then(function () {
                    //拼装下载内容
                    var params = $scope.formRefer;
                    inform.downLoadFile('keyQuestion/toExcel', params, '关键技术问题.xlsx');
                });
            };

            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        }
    ]);
})();

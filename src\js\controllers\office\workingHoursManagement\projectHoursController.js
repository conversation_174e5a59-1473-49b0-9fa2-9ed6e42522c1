/*
 * @Author: fubaole
 * @Date:   2017-09-18 14:53:05
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-01-15 17:20:32
 */
(function () {
    app.controller("projectHoursController", ['comService', '$rootScope', '$scope', 'projectHoursService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, projectHoursService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
	    	//获取跳转缓存
	    	$scope.formRefer = LocalCache.getObject('ManagementHours_formRefer');
	    	$scope.reset=reset;
	    	if ($scope.formRefer.unit==null||$scope.formRefer.unit===''){
	    		//获取缓存
	        	$scope.formRefer = LocalCache.getObject('projectHours_formRefer');
	        	//对原缓存进行覆盖
	        	LocalCache.setObject("projectHours_formRefer",{});
	        	//设置默认时间
	        	if ($scope.formRefer.startTime==null){
	        		//设置默认时间
	        		reset();
	        		$scope.formRefer.endTime = inform.format(new Date(), 'yyyy-MM-dd');
	        	}
	    	}else{
	    		$scope.formRefer.line=$scope.formRefer.unit;
	    	}
	    	//对原跳转缓存进行覆盖
	    	LocalCache.setObject("ManagementHours_formRefer",{});
	    	
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //初始化分页数据
            $scope.pages = inform.initPages();
            //被选择项目集合
            $scope.projectSelected = [];
			//初始化查询条件
			initPage();
            $scope.getData = getData;
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            function reset(){
            	var day_30 = new Date().setMonth((new Date().getMonth()-1))
	            $scope.formRefer.startTime = inform.format(new Date(day_30), 'yyyy-MM-dd');
            }
            /**
             * 初始化查询条件
             */
            function initPage(){  
            	//获取产品线
                $scope.productLines=[];
                comService.getParamList('PRO_PRODUCT_TYPE','PRO_PRODUCT_TYPE').then(function(data) {
                    $scope.productLines = angular.fromJson(data.data);
                });
                //获取山东新北洋集团的下级部门信息
				$scope.departmentList = [];
				comService.getOrgChildren('D010053').then(function(data) {
					$scope.departmentList = comService.getDepartment(data.data);
				});
				getData();
            }
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 72);
            }
            /**
             * 开始时间
             */
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;    //开始时间
                $scope.openedEnd = false;
            };
            /**
             * 结束时间
             */
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;    //结束时间
            };
            /**
             * 获取其中一条记录的详细信息
             */
            $scope.viewDetails = function(m,flag) {
            	if ($scope.formRefer.startTime==null|| $scope.formRefer.startTime===''){
            		inform.common("请选择开始时间");
            		return;
            	}
            	LocalCache.setObject('projectHours_formRefer', $scope.formRefer);
            	$state.go('app.office.projectHoursDetails', {
                	id: m,		// 版本id
                	flag:flag,
                	isSprintVersion:1
                });
            }
            /**
             * 获取汇总项目
             */
            function getData(pageNum) {
            	if ($scope.formRefer.startTime==null|| $scope.formRefer.startTime===''){
            		inform.common("请选择开始时间");
            		return;
            	}
                $scope.projectSelected = [];
                $scope.select_all = false;
                var urlData = {
                    'name': $scope.formRefer.name,						// 项目名称
                    'vaersionName': $scope.formRefer.vaersionName,		//禅道项目名称
                    'line': $scope.formRefer.line,          			//产品线
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'), 		// 开始时间
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),
                    'currentPage': pageNum, 							// 分页页数
                    'pageSize': $scope.pages.size    					// 分页每页大小
                };
                //通过项目名称获取数据库中的项目名称，开始日期，结束日期，项目状态，项目负责人
                projectHoursService.getData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                        	$scope.projectList = data.data.list;
                        	if ($scope.projectList.length === 0) {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages();
                            }
                            // 分页信息设置
                            $scope.pages.total = data.data.total;           // 页面数据总数
                            $scope.pages.star = data.data.startRow;         // 页面起始数
                            $scope.pages.end = data.data.endRow;            // 页面结束数
                            $scope.pages.pageNum = data.data.pageNum;       //页号
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            /**
             * 单选项目
             */
            $scope.selectOne = function(i) {
                $scope.select_all = false;
                var index = $scope.projectSelected.indexOf(i.name);
                if (index === -1 && i.checked) {
                    $scope.projectSelected.push(i.id + "");
                } else if (index !== -1 && !i.checked) {
                    $scope.projectSelected.splice(index, 1);//从数组中删除项目
                }
            }

            /**
             * 全选函数
             */
            $scope.selectAll = function() {
                if ($scope.select_all) {
                    $scope.projectSelected = [];
                    angular.forEach($scope.projectList, function (i) {
                        i.checked = true;
                        $scope.projectSelected.push(i.id + "");
                    });
                } else {
                    angular.forEach($scope.projectList, function (i) {
                        i.checked = false;
                    });
                    $scope.projectSelected = [];
                }
            }
            /**
             * 下载汇总信息
             */
            $scope.toExcel = function () {
            	var urlData = {
                        'name': $scope.formRefer.name,						// 项目名称
                        'vaersionName': $scope.formRefer.vaersionName,		//禅道项目名称
                        'line': $scope.formRefer.line,          			//产品线
                        'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'), 		// 开始时间
                        'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd')
                    };
            	inform.modalInstance("确定要下载吗?").result.then(function () {
                	inform.downLoadFile('projectHours/toExcel',urlData,'项目工时统计.xlsx');
            	});
            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();
(function() {
  'use strict';
  app.controller("role_Management", ['$rootScope', '$scope', 'inform', '$modal', 'SystemService','ConfigService', '$state', 'Trans','AgreeConstant','LocalCache',
    function($rootScope, $scope, inform, $modal, SystemService,ConfigService, $state, Trans,AgreeConstant,LocalCache) {
      var parentArray = [];
      $scope.permissionIds = []; // 存放选中权限ID
      $scope.title = 'roleId';
      $scope.desc = true;

      $scope.map = {}; //条件
      $scope.checked = [];

      $scope.order = order; // 排序函数
      $scope.getData = getData; // 初始化函数
      $scope.getPermission = getPermission; // 获取权限
      $scope.promissionModal = promissionModal; // 获取修改权限信息
      $scope.onSubmitPermission = onSubmitPermission; // 提交修改权限信息
      $scope.selectAll = selectAll; // 全选信息
      $scope.selectOne = selectOne; // 单选信息
      $scope.open = open; // 删除角色

      $scope.pages = inform.initPages(); // 初始化分页数据
      getData($scope.pages.pageNum); // 初始化请求数据

      // 右侧树配置
      var Rightsetting = angular.copy(ConfigService.checkboxConfig);
      Rightsetting.callback.onCheck = onCheck; // 点击节点回调
      // 右侧权限查看树配置
      var grantPromissionTree = ConfigService.dataConfig;

      // 排序
      function order(str) {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      }

      // 获取表格数据
      function getData(num) {
        $scope.checked = [];
        $scope.select_all = false;
        if (!num) { inform.common(Trans('tip.pageNumTip')); return; }

        var roleId = LocalCache.getSession('roleId');
        if(roleId === '1'){
        	var urlData = {
        			'page':parseInt(num),
        			'size':$scope.pages.size
        	};
          SystemService.getAllRoleWithPage(urlData)
              .then(function(data) {
                if (data.code === AgreeConstant.resultCode) {
                  $scope.pages.goNum = null;
                  var jsonData = angular.fromJson(data.result);
                  $scope.resultData = jsonData.list;
                  if ($scope.resultData.length === 0) {
                    inform.common(Trans("tip.noData"));
                    $scope.pages = inform.initPages();
                  } else {
                    $scope.pages.total = jsonData.total;
                    $scope.pages.star = (jsonData.pageNum - 1) * jsonData.pageSize + 1;
                    $scope.pages.end = jsonData.pageNum * jsonData.pageSize;
                    $scope.pages.pageNum = jsonData.pageNum;
                  }
                } else {
                  inform.common(data.message);
                }
              }, function() {
                inform.common(Trans("tip.requestError"));
              });
          return;
        }

        SystemService.getRoleByLoginUserIdMapWithPage(JSON.stringify($scope.map), parseInt(num), $scope.pages.size)
          .then(function(data) {
            if (data.code === AgreeConstant.resultCode) {
              $scope.pages.goNum = null;
              var jsonData = angular.fromJson(data.result);
              $scope.resultData = jsonData.list;
              if ($scope.resultData.length === 0) {
                inform.common(Trans("tip.noData"));
                $scope.pages = inform.initPages();
              } else {
                $scope.pages.total = jsonData.total;
                $scope.pages.star = (jsonData.pageNum - 1) * jsonData.pageSize + 1;
                $scope.pages.end = jsonData.pageNum * jsonData.pageSize;
                $scope.pages.pageNum = jsonData.pageNum;
              }
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      //  获取所有权限信息
      function getPermission(str) {
        $scope.everyRole = str;
        SystemService.getAllPermission()
          .then(function(data) {
            if (data.code === AgreeConstant.resultCode) {
              $scope.treeData = [];
              $scope.permissionIds = [];
              getPermById(data.result, str.roleId);
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 根据角色Id 获取权限
      function getPermById(data, id) {
        SystemService.getPermissionListByRoleId(id)
          .then(function(result) {
            if (result.code === AgreeConstant.resultCode) {
              angular.forEach(data, function(res, i) {
                var jsonTree = {
                  "id": res.permissionId,
                  "pId": res.parentId,
                  "name": res.permissionName,
                  "open": true,
                  "nocheck": res.permissionId === AgreeConstant.treeRootNode ? true : false
                };
                data[i] = angular.extend(jsonTree, res);
                angular.forEach(result.result, function(json, j) {
                  if (json.permissionId === res.permissionId) {
                    data[i].checked = true;
                    $scope.permissionIds.push(json.permissionId);
                  }
                });
              });
              $scope.treeData = data;
              $.fn.zTree.init($("#rightTree"), Rightsetting, $scope.treeData);
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 获取选中节点
      function onCheck(e, treeId, treeNode) {
        $scope.permissionIds = []; // 存放选中的树节点（修改所在组织机构）
        var treeObj = $.fn.zTree.getZTreeObj("rightTree"),
            nodes = treeObj.getCheckedNodes(true);
        angular.forEach(nodes,function(res){
          $scope.permissionIds.push(res.id);
        });
        console.log($scope.permissionIds); //获取选中节点的值
      }

      // 修改信息
      function promissionModal(id) {
        SystemService.getRoleAndGrant(id)
          .then(function(data) {
            if (data.code === AgreeConstant.resultCode) {
              $scope.grantPermission = data.result.grantPermission;
              getTreeData();
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 权限授权数据
      function getTreeData() {
        angular.forEach($scope.grantPermission, function(res, index) {
          var jsonTree = {
            "id": res.permissionId,
            "pId": res.parentId,
            "name": res.permissionName,
            "open": true
          };
          $scope.grantPermission[index] = angular.extend(jsonTree, res);
        });
        $.fn.zTree.init($("#grantPromissionTree"), grantPromissionTree, $scope.grantPermission);
      }

      // 保存权限修改操作
      function onSubmitPermission() {
        console.log($scope.permissionIds);
        SystemService.savePermissionToRole($scope.everyRole.roleId, $scope.permissionIds.join())
          .then(function(data) {
            if (data.code === AgreeConstant.resultCode) {
              inform.common(Trans("tip.saveSuccess"));
              $('#edit_power').modal("hide");
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 全选复选框
      function selectAll() {
        if ($scope.select_all) {
          $scope.checked = [];
          angular.forEach($scope.resultData, function(i) {
            $scope.checked.push(i.roleId);
            i.checked = true;
          });
        } else {
          angular.forEach($scope.resultData, function(i) {
            i.checked = false;
          });
          $scope.checked = [];
        }
      }

      function selectOne(i) {
        var index = $scope.checked.indexOf(i.roleId);
        if (index === -1 && i.checked) {
          $scope.checked.push(i.roleId);
        } else if (index !== -1 && !i.checked) {
          $scope.checked.splice(index, 1);
        }
        if ($scope.resultData.length === $scope.checked.length) {
          $scope.select_all = true;
        } else {
          $scope.select_all = false;
        }
      }

      // 删除数据
      function removeRole(str) {
        SystemService.removeRole(str)
        .then(function(data) {
          if (data.code === AgreeConstant.resultCode) {
            inform.common(Trans("tip.delSuccess"));
            $scope.checked = [];
            getData(AgreeConstant.pageNum);
          } else {
            inform.common(data.message);
          }
        }, function(error) {
          inform.common(Trans("tip.requestError"));
        });
      }

      // 删除弹框
      function open(item) {
        if (item.length) {
          var modalInstance = $modal.open({
            templateUrl: 'myModalContent.html',
            controller: 'ModalInstanceCtrl',
            size: "sm",
            resolve: {
              items: function() {
                return Trans("common.deleteTip");
              }
            }
          });
          modalInstance.result.then(function() {
            if (item.length > 0) {
              removeRole(item.join());
            }
          });
        } else {
          inform.common(Trans("common.chooseOneOpt"));
        }
      }

    }
  ]);
})();
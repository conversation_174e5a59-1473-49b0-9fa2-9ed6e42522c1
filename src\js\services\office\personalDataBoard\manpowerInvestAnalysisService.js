(function() {
    'use strict';
    app.factory('manpowerInvestAnalysisService', manpowerInvestAnalysisService);
    manpowerInvestAnalysisService.$inject = [ "HttpService", '$rootScope' ];

    function manpowerInvestAnalysisService(HttpService, $rootScope) {
        var service = {
            getAllManpowerData:getAllManpowerData,
            getProductManpowerData:getProductManpowerData,
            getTechnologyManpowerData:getTechnologyManpowerData,
            getMainManpowerData:getMainManpowerData
        };
        return service;
        //获取总人力及其所属人力投入类型的具体数值
        function getAllManpowerData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'manpowerAnalysis/getAllManpowerData',urlData);
        }
        //获取产品研发投入人力下，所有产品线的名称和数量
        function getProductManpowerData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'manpowerAnalysis/getProductManpowerData',urlData);
        }
        //获取技术研发投入人力类别下，所有类型的人力。
        function getTechnologyManpowerData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'manpowerAnalysis/getTechnologyManpowerData',urlData);
        }
        //获取总投入人力下所有孩子的名称和数量
        function getMainManpowerData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'manpowerAnalysis/getMainManpowerData',urlData);
        }

    }
})();

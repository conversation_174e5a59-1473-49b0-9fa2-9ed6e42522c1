/*
* @Author: fubaole
* @Date:   2018-01-24 17:19:53
* @Last Modified by:   fubaole
* @Last Modified time: 2018-03-19 10:12:35
*/
(function() {
  'use strict';
  app.controller("role_Change", ['$rootScope', '$scope', '$state', '$stateParams','ConfigService','inform', 'SystemService','Trans','LocalCache','AgreeConstant',
    function($rootScope, $scope, $state, $stateParams,ConfigService,inform, SystemService,Trans,LocalCache,AgreeConstant) {
      $scope.limitList = AgreeConstant.limitList; // 正则校验配置
      $scope.grantGroupIds =[]; // 存放用户组授权ID
      $scope.grantRoleIds = []; // 存放角色授权ID
      $scope.orgChecked = []; // 存放选中的组织机构信息

      // getRoleGrantData(); // 获取角色授权信息
      // getGroupGrantData(); // 获取用户组授权信息
      getData(); // 获取角色详情信息

      // 获取角色授权信息
      function getRoleGrantData() {
        SystemService.getRoleByLoginUserIdMap()
          .then(function(data){
            if(data.code===AgreeConstant.resultCode){
              $scope.roleList = data.result;
            }else{
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
        });
      }

      // 获取用户组授权信息
      function getGroupGrantData() {
        SystemService.getGroupByLoginId()
          .then(function(data){
            if(data.code===AgreeConstant.resultCode){
              $scope.groupList = data.result;
            }else{
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
        });
      }

      // 根据ID 获取角色信息
      function getData() {
        SystemService.getRoleAndGrant($stateParams.roleId)
          .then(function(data){
            if(data.code===AgreeConstant.resultCode){
              $scope.role = data.result.role;
              // $scope.grantGroup = data.result.grantGroup;
              // $scope.grantRole = data.result.grantRole;
              // 权限授权数据
              if(data.result.grantPermission && data.result.grantPermission.length){
                $scope.showPormissionTxt = true;
                $scope.grantPermission = data.result.grantPermission;
                angular.forEach($scope.grantPermission,function(res){
                  $scope.proChecked.push(res.permissionId);
                });
              }
              // 组织机构授权数据
              // if(data.result.grantOrg && data.result.grantOrg.length){
              //   $scope.showOrgTxt = true;
              //   $scope.grantOrg = data.result.grantOrg;
              //   angular.forEach($scope.grantOrg,function(res){
              //     $scope.orgChecked.push(res.orgId);
              //   });
              // }
              // 用户组授权数据
              // angular.forEach($scope.grantGroup,function(res){
              //   $scope.grantGroupIds.push(res.groupId);
              // });
              // // 角色授权数据
              // angular.forEach($scope.grantRole,function(res){
              //    $scope.grantRoleIds.push(res.roleId);
              // });
            }else{
              inform.common(data.message);
            }
          },function(){
          inform.common(Trans("tip.requestError"));
        });
      }

      // =======================================数据授权================================//
      $scope.orgChecked=[]; // 自定义所在组织机构
      // 右侧树配置
      var RightOrgSetting = angular.copy(ConfigService.checkboxConfig);
      RightOrgSetting.callback.onCheck = onRightCheck; // 点击节点回调
      RightOrgSetting.check.chkboxType = { "Y" : "", "N" : "" };

      $scope.getOrgById = getOrgById;
      $scope.saveOrg = saveOrg; // 保存按钮操作

      // 获取登录人有的组织机构信息
      function onRightCheck(){
        $scope.orgChecked=[]; // 自定义所在组织机构
        var treeObj = $.fn.zTree.getZTreeObj("rightOrgTree"),
            nodes = treeObj.getCheckedNodes(true);
        angular.forEach(nodes,function(res){
          $scope.orgChecked.push(res.id);
        });
      }

      // 根据ID获取组织机构
      function getOrgById(str) {
        if(str==AgreeConstant.selfDirective || !str){
          $("#setting_orgin").modal({backdrop: 'static', keyboard: false});
          // 获取全部权限
          if(!$scope.orgJson){
            SystemService.getAllOrg()
              .then(function(data) {
                if (data.code===AgreeConstant.resultCode) {
                  $scope.orgChecked = [];
                  $scope.orgChecked = [];
                  $scope.orgJson = data.result;
                  getOrgFilterShow($scope.orgJson);
                } else {
                  inform.common(data.message);
                }
              }, function() {
                inform.common(Trans("tip.requestError"));
            });
          }
        }else{
          $scope.orgChecked =[];
        }
      }

      // 渲染树结构数据
      function getOrgFilterShow(jsonData){
        angular.forEach(jsonData, function(res, i) {
          var jsonTree = {
            "id": res.orgId,
            "pId": res.parentId,
            "name": res.orgName,
            "nocheck": res.orgId===AgreeConstant.treeRootNode? true:false,
            "open":true
          };
          angular.forEach($scope.grantOrg, function(json) {
            if (json.orgId===res.orgId) {
              res.checked = true;
              $scope.orgChecked.push(res.orgId);
            }
          });
          jsonData[i] = angular.extend(jsonTree, res);
        });
        $scope.orgTreeData = jsonData;
        $.fn.zTree.init($("#rightOrgTree"), RightOrgSetting, $scope.orgTreeData);
      }

      // 关闭弹框
      function saveOrg() {
        $("#setting_orgin").modal("hide");
        if($scope.orgChecked.length!==0){
          $scope.showOrgTxt = true;
        }else{
          $scope.showOrgTxt = false;
        }
      }

      // ==================================权限授权=====================================//
      $scope.proChecked = []; // 自定义所在权限
      $scope.getProById = getProById; // 根据ID获取权限
      $scope.savePro = savePro; // 关闭弹框
      $scope.onSubmit = onSubmit; // 保存角色信息

      // 右侧树配置
      var RightProSetting = angular.copy(ConfigService.checkboxConfig);
      RightProSetting.callback.onCheck = onRightProCheck; // 点击节点回调

      // 获取登录人有的权限信息
      function onRightProCheck(){
        $scope.proChecked=[]; // 自定义所在权限
        var treeObj = $.fn.zTree.getZTreeObj("rightProTree"),
          nodes = treeObj.getCheckedNodes(true);
        for (var i = 0; i < nodes.length; i++) {
          $scope.proChecked.push(nodes[i].id);
        }
      }

      // 根据ID获取权限
      function getProById() {
        $("#setting_promission").modal({backdrop: 'static', keyboard: false});
        // 获取全部权限
        if(!$scope.permissionJson){
          SystemService.getAllPermission()
            .then(function(data) {
              if (data.code === AgreeConstant.resultCode) {
                $scope.proTreeData = [];
                $scope.proChecked = [];
                $scope.permissionJson = data.result;
                getPromissionById($scope.permissionJson);
              } else {
                inform.common(data.message);
              }
            }, function() {
              inform.common(Trans("tip.requestError"));
          });
        }
      }

      // 渲染树结构数据
      function getPromissionById(jsonData){
        angular.forEach(jsonData, function(res, i) {
          var jsonTree = {
            "id": res.permissionId,
            "pId": res.parentId,
            "name": res.permissionName,
            "open":true
          };
          angular.forEach($scope.grantPermission, function(json) {
            if (json.permissionId === res.permissionId) {
              res.checked = true;
              $scope.proChecked.push(res.permissionId);
            }
          });
          jsonData[i] = angular.extend(jsonTree, res);
        });
        $scope.proTreeData = jsonData;
        $.fn.zTree.init($("#rightProTree"), RightProSetting, $scope.proTreeData);
      }

      // 关闭弹框
      function savePro() {
        $("#setting_promission").modal("hide");
        if($scope.proChecked.length!==0){
          $scope.showPormissionTxt = true;
        }else{
          $scope.showPormissionTxt = false;
        }
      }

      // 提交数据
      function saveRoleData() {
        SystemService.saveRoleAndOtherToRole(
            $scope.role,
            $scope.permissionIds,
            // $scope.$scope.proChecked,
            $scope.grantGroupIds,
            $scope.grantRoleIds,
            $scope.orgChecked,
            $scope.proChecked
        )
          .then(function(data) {
            if (data.code === AgreeConstant.resultCode) {
              inform.common(Trans("tip.saveSuccess"));
              $state.go("app.system.role_Management");
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 保存角色信息
      function onSubmit() {
        $scope.role.usergroupRange ="";
        $scope.role.roleRange ="";
        $scope.role.permissionRange ="";
        // if($scope.grantGroupIds.length){
        //   $scope.role.usergroupRange = AgreeConstant.groupRange;
        // }
        // if($scope.grantRoleIds.length){
        //   $scope.role.roleRange = AgreeConstant.roleRange;
        // }
        if($scope.proChecked.length){
          $scope.role.permissionRange = AgreeConstant.permissionRange;
        }
        if($scope.role.dataRange=== AgreeConstant.selfDirective){
          if($scope.orgChecked.length===0){
            inform.common(Trans('role.GrantOrgNotSpace'));
            return false;
          }
        }
        saveRoleData();
      }


    }
  ]);
})();
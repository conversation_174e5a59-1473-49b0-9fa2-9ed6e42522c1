(function () {
    app.controller("versionReleaseManagement", ['comService', '$scope','$modal','$state','$stateParams','versionReleaseService','inform','Trans','AgreeConstant','LocalCache',
        function (comService, $scope, $modal,$state,$stateParams,versionReleaseService,inform,Trans,AgreeConstant,LocalCache) {
     	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */   
    	$scope.limitList = AgreeConstant.limitList; // 正则校验配置
    	$scope.formRefer = {
    	    endTime:'',
    		projectId:''  , //上线项目名称
    		releaseResult:'',//是否成功
    		productLine:'', //产品线code
			operator:'', //操作人
			approvalStatus:'', //审批状态
			approvalResult:'', //审批结果
			releaseType:''  //上线类型
    	};
    	$scope.pages = {
			pageNum : '', 		// 分页页数
			size : '', 			// 分页每页大小
			total : '' 			// 数据总数
		};
    	//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
    	$scope.pages = inform.initPages(); 	// 初始化分页数据
		$scope.getData = getData; 			// 分页相关函数         				
		$scope.taskList = [];				// 保存所有信息的集合  

    	initPages();//初始化页面信息

        $scope.formRefer = LocalCache.getObject('versionReleaseFormRefer');//获取缓存

        //设置初始时间
        if(null == $scope.formRefer.startTime || "" === $scope.formRefer.startTime){
            var time = inform.format(new Date(),'yyyy-MM-dd');//获取当前系统时间
            time = time.split("-");
            $scope.formRefer.startTime = time[0]+'-01-01';
        }
        //对原缓存进行覆盖
        LocalCache.setObject("versionReleaseFormRefer",{});
		// 获取数据
		getData($scope.pages.pageNum);		//在刷新页面时调用该方法
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
	  	
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */	
		
		 /**
		  * 设置列表的高度
		  */
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 180);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 50);
 		}
         //上线开始时间
        $scope.openDateStart = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = true;    //开始时间
            $scope.openedEnd = false;
        };

        //上线结束时间
        $scope.openDateEnd = function($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = false;
            $scope.openedEnd = true;    //结束时间
        };
		/**
		 * 重置
		 */
		$scope.rest = function() {
			$scope.formRefer.projectId = '';
			$scope.formRefer.releaseResult = '';
            $scope.formRefer.startTime = inform.format(new Date(),'yyyy-MM-dd').split("-")[0]+'-01-01';
			$scope.formRefer.endTime = '';
			$scope.formRefer.productLine = '';
			$scope.formRefer.releaseType = '';
			$scope.formRefer.operator = '';
			$scope.formRefer.approvalStatus = '';
			$scope.formRefer.approvalResult = '';
		};
		/**
		 * 获取产品线code
		 */
	    function initPages() {
	    	//获取产品线
	    	$scope.productLineList = [];
	    	comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
	    		if (data.data) {
	    			$scope.productLineList =  data.data;
	    			getProjectList();
	    		}
	    	});
	    }
	    /**
	     * 获取项目名称
	     */
        function getProjectList(){
            $scope.projectNameList = [];
            comService.getProjectsName().then(function (data) {
                $scope.projectNameList = angular.fromJson(data.data);
            });
        }
	     /**
	      * 页面选中的修改信息复制
	      */
	    $scope.getVersionReleaseLogInfo = function (m) {
	    	LocalCache.setObject('versionReleaseFormRefer',$scope.formRefer);
	    	$state.go('app.office.versionReleaseDetail', {
				id:m.id
	        });
	    };
	     /**
	      * 删除信息
	      */
	     $scope.deleteInfo = function(m){
	     	var urlData = {
	     		'id':m.id  //选中的id
	     	};
	    	var modalInstance = $modal.open({
				templateUrl: 'myModalContent.html',
	            controller: 'ModalInstanceCtrl',
	            size: "sm",
	            resolve: {
	              items: function() {
	              return "确定要删除吗！";
	            }
	           }
			});
		   modalInstance.result.then(function() {
		        versionReleaseService.deleteInfo(urlData).then(function(data){
		          	if(data.data===1){
		              	getData(1);
			        	inform.common(Trans("信息删除成功！"));		
			        }
				},
				function(error) {
					inform.common(Trans("tip.requestError"));
				});	
		    });
	     };
		/**
		 * 获取所有上线失败信息
		 */
		function getData(pageNum){
			$scope.taskList = [];   //保存所有信息的集合
			var start = inform.format($scope.formRefer.startTime,'yyyy-MM-dd');
			var end = inform.format($scope.formRefer.endTime,'yyyy-MM-dd 23:59:59');
			var urlData = {
				'productLine':$scope.formRefer.productLine,
				'projectId':$scope.formRefer.projectId,  		//上线项目名称
				'start':start,											//上线开始时间
				'end':end,												//上线结束时间
				'releaseResult':$scope.formRefer.releaseResult,        //是否成功
				'releaseType':$scope.formRefer.releaseType,             //上线类型
				'operator':$scope.formRefer.operator,            //操作人
				'approvalStatus':$scope.formRefer.approvalStatus, //审批状态
				'approvalResult':$scope.formRefer.approvalResult,  //审批结果
				'page' : pageNum, 								// 分页页数
				'pageSize' : $scope.pages.size    						// 分页每页大小
			};
			versionReleaseService.getVersionReleaseLogList(urlData).then(function(data){
				if(data.code===AgreeConstant.code){
					var jsonData = data.data;
					$scope.taskList = jsonData.list;
					if ($scope.taskList.length===0) {
						$scope.pages = inform.initPages(); 			//初始化分页数据
						inform.common(Trans("tip.noData"));
					} else {
						// 分页信息设置
						$scope.pages.total = jsonData.total;		// 页面总数
						$scope.pages.star = jsonData.startRow;  	//页面起始数
						$scope.pages.end = jsonData.endRow;  		//页面大小数
						$scope.pages.pageNum = jsonData.pageNum;  	//页面页数
					}
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});		
		}

		// 下载excel
		$scope.toExcel = function() {
			var start = inform.format($scope.formRefer.startTime,'yyyy-MM-dd');
			var end = inform.format($scope.formRefer.endTime,'yyyy-MM-dd 23:59:59');
			var urlData = {
				'productLine':$scope.formRefer.productLine,
				'projectId':$scope.formRefer.projectId,  		//上线项目名称
				'start':start,											//上线开始时间
				'end':end,												//上线结束时间
				'releaseResult':$scope.formRefer.releaseResult,        //是否成功
				'releaseType':$scope.formRefer.releaseType,             //上线类型
				'operator':$scope.formRefer.operator,            //操作人
				'approvalStatus':$scope.formRefer.approvalStatus, //审批状态
				'approvalResult':$scope.formRefer.approvalResult  //审批结果
			};
            inform.modalInstance("确定要下载上线流程数据表吗？").result.then(function() {
       
                inform.downLoadFile('versionReleaseLog/downloadReleaseProcess',urlData,"上线流程数据表"+inform.format(new Date(),'yyyy-MM-dd')+".xlsx");
           });
        };

		function  common(str,func){
			layer.confirm(str,{
				title:false,
				btn:['确定']
			},function(result){
				layer.close(result);
				if(typeof (func) !== 'undefined'){
					func();
				}
			});
		}

		$scope.confirm = function (str,func) {
			var modalInstance = $modal.open({
				templateUrl: 'myModalContent.html',
				controller: 'ModalInstanceCtrl',
				size: "sm",
				resolve: {
					items: function () {
						return Trans(str);
					}
				}
			});
			modalInstance.result.then(function () {
				func();
			});
		};

		$scope.syncVersionReleaseLog = function(){
			var urlData = {
				'start':'',											//上线开始时间
				'end':''										//上线结束时间
				
			};
			versionReleaseService.syncVersionReleaseLog(urlData).then(function(data){
				common(data.message,function () {});
			},
			function (error) {
				inform.common(Trans("tip.requestError"));
			});
		}

		/**
 		 * *************************************************************
 		 *              方法声明部分                                 结束
 		 * *************************************************************
 		 */	
		
		
	}]);
})();
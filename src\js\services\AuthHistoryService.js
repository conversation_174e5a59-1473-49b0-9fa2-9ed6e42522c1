(function() {
	'use strict';
	app
	.factory('AuthHistoryService', authHistoryService);
	authHistoryService.$inject=['HttpService','$rootScope'];

	function authHistoryService(HttpService,$rootScope){
		var service={
			getAuthHistoryBySignCode:getAuthHistoryBySignCode,
			getCookie:getCookie
		};
		return service;
		function getCookie(name) 
            { 
                var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
             
                if(arr=document.cookie.match(reg))
             
                    return unescape(arr[2]); 
                else 
                    return null; 
            } 
	    function getAuthHistoryBySignCode(signCode){
				return HttpService.post($rootScope.gateInfoApi+'lms/authOfflineChange/select?signCode='+signCode+'&name='+getCookie('name'),{});
			}
		
		}
})();
/*
 * @Author: sunqixian
 * @Date:   2019-05-23 17:50:05
 * @Last Modified by:   sunqixian
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
	'use strict';
	app.factory('paramManagementService', paramManagementService);
	paramManagementService.$inject = [ "HttpService", '$rootScope' ];

	function paramManagementService(HttpService, $rootScope) {

		var service = {
			getData : getData,
			upData : upData,
			getDetailData : getDetailData,
			upQualityParam : upQualityParam

		};
		return service;
		/**
		 * 获取该部门的所有指标
		 */
		function getData(department) {
			return HttpService.post($rootScope.getWaySystemApi
					+ 'parammanagement/getData', department);
		}
		/**
		 * 更新该部门的所有指标
		 */
		function upData(urlData) {
			return HttpService.post($rootScope.getWaySystemApi
					+ 'parammanagement/upData', urlData);
		}

		/**
		 * 获取质量指标
		 */
		function getDetailData() {
			return HttpService.post($rootScope.getWaySystemApi
					+ 'paramprojectquality/getQualityParam');
		}

		/**
		 * 修改质量指标
		 */
		function upQualityParam(urlData) {
			return HttpService.post($rootScope.getWaySystemApi
					+ 'paramprojectquality/upQualityParam', urlData);
		}

	}
})();

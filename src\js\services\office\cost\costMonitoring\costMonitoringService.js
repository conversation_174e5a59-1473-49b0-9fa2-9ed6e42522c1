//# sourceURL=js/services/office/cost/costMonitoring/costMonitoringService.js
(function () {
    'use strict';
    app.factory('costMonitoringService', costMonitoringService);
    costMonitoringService.$inject = ["HttpService", '$rootScope'];

    function costMonitoringService(HttpService, $rootScope) {

        var service = {
        		getData:getData,
        		getRelatedLevel:getRelatedLevel,//人力等级
        		getRelatedTitle:getRelatedTitle,//人力岗位
        		getHrDetail:getHrDetail,//人力单元格
        		getMaintainHr:getMaintainHr,//维护人力单元格
        		getProjectFee:getProjectFee,//费用表格
        		getFreeDetails:getFreeDetails,//费用明细
        		getFreeMaintain:getFreeMaintain,//维护费用明细
        		getChangeInfo:getChangeInfo,//变更信息
        		getChangeLevel:getChangeLevel,//变更人力等级
        		getChangeTitle:getChangeTitle,//变更人力岗位
        		getChangeDetail:getChangeDetail,//变更人力单元格
        		getChangeFee:getChangeFee,//变更费用
        		getAmountDetail:getAmountDetail,//人力费用明细
        		getProAllPlmAmountDetail:getProAllPlmAmountDetail,//PLM人力费用明细
        		getSum:getSum,//人力费用汇总
        		getProjectCostChartData:getProjectCostChartData//用于查询折线图相关数据
        };
        return service;
        /**
         * 获取成本监控项目列表
         */
        function getData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costMonitoring/getData', urlData);
        }
        /**
         * 获取人员级别列表
         */
        function getRelatedLevel(urlData) {
            return HttpService.post($rootScope.getWaySystemApi  + 'costCom/getRelatedLevel', urlData);
        }
        /**
         * 获取人员岗位列表
         */
        function getRelatedTitle(urlData) {
            return HttpService.post($rootScope.getWaySystemApi  + 'costCom/getRelatedTitle', urlData);
        }
        /**
         * 人力单元格
         */
        function getHrDetail(urlData) {
            return HttpService.post($rootScope.getWaySystemApi  + 'costCom/getHrDetail', urlData);
        }
        /**
         * 维护人力单元格
         */
        function getMaintainHr(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costMonitoring/getMaintainHr', urlData);
        }
        /**
         * 获取费用列表
         */
        function getProjectFee(urlData) {
            return HttpService.post($rootScope.getWaySystemApi  + 'costCom/getProjectFee', urlData);
        }
        /**
         * 获取维护费用列表
         */
        function getFreeMaintain(urlData) {
            return HttpService.post($rootScope.getWaySystemApi  + 'costMonitoring/getFreeMaintain', urlData);
        }
        /**
         * 获取费用明细列表
         */
        function getFreeDetails(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costMonitoring/getFreeDetails', urlData);
        }
        /**
         * 获取变更信息列表
         */
        function getChangeInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costMonitoring/getChangeInfo', urlData);
        }
        /**
         * 获取变更人员级别列表
         */
        function getChangeLevel(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costMonitoring/getChangeLevel', urlData);
        }
        /**
         * 获取变更人力岗位
         */
        function getChangeTitle(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costMonitoring/getChangeTitle', urlData);
        }
        /**
         * 获取变更人力单元格
         */
        function getChangeDetail(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costMonitoring/getChangeDetail', urlData);
        }
        /**
         * 获取变更费用
         */
        function getChangeFee(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costMonitoring/getChangeFee', urlData);
        }
        /**
         * 获取人力费用明细
         */
        function getAmountDetail(projectId,plmUploadId) {
            return HttpService.get($rootScope.getWaySystemApi + 'costCom/getAmountDetail', {
            	'projectId':projectId,'plmUploadId':plmUploadId
            });
        }
        /**
         * 获取PLM人力费用明细
         */
        function getProAllPlmAmountDetail(projectId) {
            return HttpService.get($rootScope.getWaySystemApi + 'costCom/getProAllPlmAmountDetail', {
            	'projectId':projectId
            });
        }
        /**
         * 获取人力费用汇总
         */
        function getSum(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'costMonitoring/getSum', urlData);
        }
        /**
         * 查询折线图相关数据
         */
        function getProjectCostChartData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'costMonitoring/getProjectCostChartData', urlData);
        }

    }
})();

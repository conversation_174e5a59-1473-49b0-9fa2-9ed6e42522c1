/*
 * @Author: sunqixian
 * @Date:   2019-05-23 17:50:05
 * @Last Modified by:   sunqixian
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
  'use strict';
  app.factory('customerDemandService', customerDemandService);
  customerDemandService.$inject=["HttpService",'$rootScope'];

  function customerDemandService(HttpService,$rootScope){
    
	var service={
			getCustomerDemand:getCustomerDemand,
			getManagerMessage:getManagerMessage,
			getFinishStateTotalInfo:getFinishStateTotalInfo,
			getPublishDetails:getPublishDetails
			
	};
    return service;
    
    /**
     * 获取所有产品线的信息
     */
    function getCustomerDemand(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'customerDemand/getAllMessage', urlData);
    }
    
    /**
     * 获取产品线下所有项目经理的信息
     */
    function getManagerMessage(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'customerDemand/getManagerMessage', urlData);
    }

    /**
     * 获取完成情况汇总信息
     */
    function getFinishStateTotalInfo(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'customerDemand/getFinishStateTotalInfo', urlData);
    }

    function getPublishDetails(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'customerDemand/getPublishDetails', urlData);
    }
  

  }
})();

// config
//(function() {
 // 'use strict';
  var app = angular.module('app')
    .config(
      ['$controllerProvider', '$compileProvider', '$filterProvider', '$provide',
        function($controllerProvider, $compileProvider, $filterProvider, $provide) {

          // lazy controller, directive and service
          app.controller = $controllerProvider.register;
          app.directive = $compileProvider.directive;
          app.filter = $filterProvider.register;
          app.factory = $provide.factory;
          app.service = $provide.service;
          app.constant = $provide.constant;
          app.value = $provide.value;
        }
      ])

    .config(['$translateProvider', function($translateProvider) {
      // Register a loader for the static files
      // So, the module will search missing translation tables under the specified urls.
      // Those urls are [prefix][langKey][suffix].
      var lang = window.localStorage.lang || 'cn';// 默认中文
      $translateProvider.useLoader('$translatePartialLoader', {
        urlTemplate: 'i18n/{lang}/{part}.json'
      });

      // $translateProvider.useStaticFilesLoader({
      //   prefix: '/i18n/',
      //   suffix: '.json'
      // });
      // Tell the module what language to use by default
      $translateProvider.preferredLanguage(lang);
      // Tell the module to store the language in the local storage
      // $translateProvider.useLocalStorage();
    }]);
//})();
/*
 * @Author: fubaole
 * @Date:   2017-11-17 14:51:58
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-22 17:58:25
 */

(function() {
  'use strict';
  app.controller("blocks_Change", ['$stateParams', '$state', '$scope', 'inform', 'SystemService', 'Trans', 'AgreeConstant',
    function($stateParams, $state, $scope, inform, SystemService, Trans, AgreeConstant) {
      $scope.limitList = AgreeConstant.limitList; // 正则校验配置
      $scope.blocks = {};
      $scope.widgetCategory = [];
      $scope.permissionType = [];
      $scope.widgetWidth = [];
      $scope.selectedRole = [];

      $scope.getData = getData;
      $scope.onSubmit = onSubmit; // 提交
      $scope.getRoleList = getRoleList; // 获取角色信息
      $scope.updataType = updataType; // 获取选中角色信息

      getDropDownData("widgetCategory"); // 获取所属模块数据
      getDropDownData("permissionType"); // 获取权限分类数据
      getDropDownData("widgetWidth"); // 获取页面宽度数据
      getData();

      // 获取字典数据
      function getDropDownData(str) {
        SystemService.getDictValueListByDictTypeCode(str)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              if (str==="widgetCategory") {
                $scope.widgetCategory = data.result;
              }
              if (str==="permissionType") {
                $scope.permissionType = data.result;
                angular.forEach($scope.permissionType, function(i) {
                  if (i.valueCode==='2') {
                    getRoleList(i.valueCode, false, true);
                  }
                });
              }
              if (str==="widgetWidth") {
                $scope.widgetWidth = data.result;
              }
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 根据ID 获取区块信息
      function getData() {
        SystemService.getBlockContent($stateParams.blockContentId)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.blocks = data.result;
              $scope.blocks.blockContentType = data.result.blockContentType.toString();
              $scope.blocks.permissionType = data.result.permissionType.toString();

              if ($scope.blocks.permissionType==='2') {
                $scope.blocks.roleIds = data.result.roleIds;
                getRoleList($scope.blocks.permissionType, true);
              }
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 获取角色权限 列表
      function getRoleList(str, display, check) {
        $scope.showRole = false;
        if (str==='2') {
          $scope.showRole = true;
          SystemService.getRoleByLoginUserIdMap()
            .then(function(data) {
              if (data.code===AgreeConstant.resultCode) {
                $scope.roleList = data.result;
                if (check) {
                  if (!$scope.roleList || !$scope.roleList.length) {
                    //没有角色信息
                    $scope.showRole = false;
                    $scope.permissions = $scope.permissionType;
                    angular.forEach($scope.permissions, function(i) {
                      if (i.valueCode==='2') {
                        var index = $scope.permissions.indexOf(i);
                        if (index !== -1) {
                          $scope.permissionType.splice(index, 1);
                        }
                      }
                    });
                  }
                  $scope.showRole = false;
                }

                if (display) {
                  //循环查询哪个角色要选中
                  angular.forEach($scope.roleList, function(i) {
                    var index = $scope.blocks.roleIds.indexOf(i.roleId);
                    if (index !== -1) {
                      i.selected = true;
                    }
                  });
                }


              } else {
                inform.common(data.message);
              }
            }, function() {
              inform.common(Trans("tip.requestError"));
            });
        }
      }

      // 获取选中角色信息
      function updataType() {
        angular.forEach($scope.roleList, function(i) {
          var index = $scope.selectedRole.indexOf(i.roleId);
          if (i.selected && index === -1) {
            $scope.selectedRole.push(i.roleId);
          } else if (!i.selected && index !== -1) {
            $scope.selectedRole.splice(index, 1);
          }
        });
        console.log($scope.selectedRole);
        $scope.blocks.roleIds = $scope.selectedRole;
      }

      function onSubmit() {
        if ($scope.blocks.permissionType==='2' && (!$scope.blocks.roleIds || !$scope.blocks.roleIds.length)) {
          inform.common(Trans("role.chooseRoleNeeded"));
          return;
        }
        SystemService.saveOrUpdateBlockContent($scope.blocks)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              inform.common(Trans("tip.saveSuccess"));
              $state.go("app.system.blocks_Management");
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }
    }
  ]);
})();
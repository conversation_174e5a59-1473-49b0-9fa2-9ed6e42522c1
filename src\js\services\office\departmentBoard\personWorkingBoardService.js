(function() {
    'use strict';
    app.factory('personWorkingBoardService', personWorkingBoardService);
    personWorkingBoardService.$inject=["HttpService",'$rootScope'];

    function personWorkingBoardService(HttpService,$rootScope){
        function getPersonHoursInfoList(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'personWorkingHours/getPersonHoursInfoList', urlData);
        }

        return {
            getPersonHoursInfoList: getPersonHoursInfoList,
        };
    }
})();
(function () {
    app.controller("processConformityManagement", ['comService', '$rootScope', '$scope', '$modal', 'processConformityService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http','$state',
        function (comService, $rootScope, $scope, $modal, processConformityService, inform, Trans, AgreeConstant, LocalCache, $http, $state) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */

            //初始化页面信息
            initPages();

            $scope.formInsert = {};
            $scope.pages = {
                pageNum: '', 		// 分页页数
                size: '', 			// 分页每页大小
                total: '' 			// 数据总数
            };

            $scope.pages = inform.initPages(); 	// 初始化分页数据
            $scope.getData = getData; 			// 分页相关函数
            $scope.taskList = [];				// 保存所有信息的集合

            // 获取数据
            getData($scope.pages.pageNum);		//在刷新页面时调用该方法
            $scope.datepicker = {};
            $scope.toggleMin = toggleMin;
            toggleMin();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            //开始时间
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;    //开始时间
                $scope.openedEnd = false;
            };

            //结束时间
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;    //结束时间
            };

            //获取当前选定时间
            function toggleMin() {
                $scope.datepicker.currentDate = $scope.datepicker.currentDate ? null : new Date();
            }

            //重置
            $scope.rest = function () {
                $scope.formInsert.moduleName = '';
                $scope.startTime = '';
                $scope.endTime = '';
                $scope.checkedProj = false;
                $scope.checkedProblem = false;
                $scope.checkedYear = true;
                $scope.type = 'year';
            };

            /**
             * 将上个页面传入的时间参数，转换为时间字符串
             * @param param
             * @returns {*}
             */
            function paramConversion(param) {
                if(null == param || 'undefined' === param || '' === param){
                    return '';
                }
                return inform.format(param,'yyyy-MM-dd');

            }

            /**
             * 初始化
             */
            function initPages() {
                //获取当前的登录用户名
                $scope.updateEmployeename = LocalCache.getSession('currentUserName');
                //登录者ID
                $scope.loginId = LocalCache.getSession('userId');  //获取当前登录者的id

                //获取查询条件的值
                var type = LocalCache.getSession('processConformityManagement_type');

                if(type === 'proj'){
                    $scope.checkedProj = true;
                    $scope.checkedProblem = false;
                    $scope.checkedYear = false;
                    //下载工时时间
                    $scope.startTime = paramConversion(LocalCache.getSession('processConformityManagement_startTime'));
                    $scope.endTime = paramConversion(LocalCache.getSession('processConformityManagement_endTime'));
                    $scope.type = 'proj';
                }
                else if(type === 'problem'){
                    $scope.checkedProj = false;
                    $scope.checkedProblem = true;
                    $scope.checkedYear = false;
                    //下载工时时间
                    $scope.startTime = paramConversion(LocalCache.getSession('processConformityManagement_startTime'));
                    $scope.endTime = paramConversion(LocalCache.getSession('processConformityManagement_endTime'));
                    $scope.type = 'problem';
                } else {
                    $scope.checkedProj = false;
                    $scope.checkedProblem = false;
                    $scope.checkedYear = true;
                    //查询条件类型默认是查询年度信息
                    $scope.type = 'year';
                }

                //获取产品线
                $scope.productLineList = [];
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.productLineList = data.data;
                    }
                });
                //获取部门
                $scope.departmentList = [];
              	comService.getOrgChildren('D010053').then(function(data) {
          			$scope.departmentList = data.data;
                });
            }
          	/**
          	 * 选择查询条件
          	 */
            $scope.select = function (item) {
            		if(item === 'year'){
                        $scope.checkedProj = false;
                        $scope.checkedProblem = false;
                        $scope.checkedYear = true;
                        $scope.type = 'year';
            		}
            		if (item === 'proj'){
            			$scope.checkedYear = false;
            			$scope.checkedProblem = false;
            			$scope.checkedProj = true;
                        $scope.type = 'proj';
                        $scope.startTime = '';
                        $scope.endTime = '';
                    }
                    if (item === 'problem'){
                        $scope.checkedYear = false;
                        $scope.checkedProj = false;
                        $scope.checkedProblem = true;
                        $scope.type = 'problem';
                        $scope.startTime = '';
                        $scope.endTime = '';
                    }
            };
            /**
             * 获取所有的数据以分页的形式
             * pageNum 页数
             */
            function getData(pageNum) {
                $scope.lineProcessList = [];
                var urlData = {};
                //判断查询方式
                if ($scope.checkedProj === true){
                    var start = inform.format($scope.startTime, 'yyyy-MM-dd');
                    var end = inform.format($scope.endTime, 'yyyy-MM-dd');
                    urlData = {
                        'moduleName': $scope.formInsert.moduleName,  			//产品线名称
                        'flag':"proj",											//查询标志
                        'createTime': start,
                        'regenerTime': end,
                        'currentPage': pageNum, 								// 分页页数
                        'pageSize': $scope.pages.size    						// 分页每页大小
                    };
                }
                if ($scope.checkedProblem === true){
                    var startTime = inform.format($scope.startTime, 'yyyy-MM-dd');
                    var endTime = inform.format($scope.endTime, 'yyyy-MM-dd');
                    urlData = {
                        'moduleName': $scope.formInsert.moduleName,  			//产品线名称
                        'flag':"problem",											//查询标志
                        'createTime': startTime,
                        'regenerTime': endTime,
                        'currentPage': pageNum, 								// 分页页数
                        'pageSize': $scope.pages.size    						// 分页每页大小
                    };
                }
                if ($scope.checkedYear === true){
                    urlData = {
                        'moduleName': $scope.formInsert.moduleName,  			//产品线名称
                        'flag':"year",											//查询标志
                        'currentPage': pageNum, 								// 分页页数
                        'pageSize': $scope.pages.size    						// 分页每页大小
                    };
                }
                processConformityService.getLineProcessConformity(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.lineProcessList = data.data;
                            angular.forEach($scope.productLineList, function(one, i) {
                            	var list = []; 
                            	angular.forEach($scope.lineProcessList, function(oneLine, i) {
                            		list.push(oneLine.moduleName);
                            	});
                            	//如果查询出的部门中不存在的部门
                          		if (list.indexOf(one.param_value) === -1){
                          			 $scope.lineProcessList.push({
                          				 "moduleName":one.param_value,//部门
                          				 "allCheckup":0,//检查总数
                          				 "pass":0,//通过数
                          				 "conformity":0,//不符合项数
                          				 "slightNum":0,//轻微问题数
                          				 "generalNum":0,//一般问题数
                          				 "severityNum":0,//严重问题问题
                          				 "fatalityNum":0,//致命问题数
                          				 "repairNum":0,//按计划修复数
                          				 "repairRate":'0%',//问题修复率
                          				 "processConformity":'0%'//过程符合度
                          			 });
                          		 }
                      	     });
                            var allCheckupSum = 0*1;
                            var passSum = 0*1;
                            var slightSum = 0*1;
                            var generalSum = 0*1;
                            var severitySum = 0*1;
                            var fatalitySum = 0*1;
                            var conformitySum = 0*1;
                            var repairNumSum = 0*1;
                            angular.forEach($scope.lineProcessList, function(oneLine, i) {
                            	allCheckupSum += oneLine.allCheckup*1;
                            	passSum += oneLine.pass*1;
                            	slightSum += oneLine.slightNum*1;
                            	generalSum += oneLine.generalNum*1;
                            	severitySum += oneLine.severityNum*1;
                            	fatalitySum += oneLine.fatalityNum*1;
                            	conformitySum += oneLine.conformity*1;
                            	repairNumSum += oneLine.repairNum*1;
                            });
                            $scope.lineProcessList.push({
                            	"moduleName":'汇总',//产品线
                            	"allCheckup":allCheckupSum,//检查总数
                            	"pass":passSum,//通过数
                            	"slightNum":slightSum,//轻微问题数
                                "generalNum":generalSum,//一般问题数
                                "severityNum":severitySum,//严重问题问题
                                "fatalityNum":fatalitySum,//致命问题数
                            	"conformity":conformitySum,//不符合项数
                            	"repairNum":repairNumSum,//按计划修复数
                            	"repairRate":conformitySum===0?'0%':((repairNumSum/conformitySum)*100).toFixed(0)+'%',//问题修复率
                            	"processConformity":allCheckupSum===0?'0%':((1-(slightSum+generalSum*1.2+severitySum*1.5+fatalitySum*2)/allCheckupSum)*100).toFixed(0)+'%',//过程符合度
                            });
                            getDep();
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });

            }

            //获取部门数据
            function getDep() {
                $scope.depList = {};   //保存所有信息的集合
                var urlData = {};
                //判断查询方式
                if ($scope.checkedProj){
                    var startTime = inform.format($scope.startTime, 'yyyy-MM-dd');
                    var endTime = inform.format($scope.endTime, 'yyyy-MM-dd');
                    urlData = {
                        'flag':"proj",
                        'createTime': startTime,
                        'regenerTime': endTime
                    };
                }
                if ($scope.checkedProblem){
                    var start = inform.format($scope.startTime, 'yyyy-MM-dd');
                    var end = inform.format($scope.endTime, 'yyyy-MM-dd');
                    urlData = {
                        'flag':"problem",
                        'createTime': start,
                        'regenerTime': end
                    };
                }
                if ($scope.checkedYear) {
                    urlData = {
                        'flag':"year"										//查询标志
                    };
                }
                processConformityService.getDeptProcessConformity(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.depList = data.data;
                            angular.forEach($scope.departmentList, function(one, i) {
                            	var list = []; 
                            	angular.forEach($scope.depList, function(oneDep, i) {
                            		list.push(oneDep.departMent);
                            	});
                            	//如果查询出的部门中不存在的部门
                          		if (list.indexOf(one.orgName)===-1 && one.orgCode !== 'D010133' && one.orgCode !== 'D010131'){
                          			 $scope.depList.push({
                          				 "departMent":one.orgName,//部门
                          				 "allCheckup":0,//检查总数
                          				 "pass":0,//通过数
                          				 "slightNum":0,//轻微问题数
                                         "generalNum":0,//一般问题数
                                         "severityNum":0,//严重问题问题
                                         "fatalityNum":0,//致命问题数
                          				 "conformity":0,//不符合项数
                          				 "repairNum":0,//按计划修复数
                          				 "repairRate":'0%',//问题修复率
                          				 "processConformity":'0%'//过程符合度
                          			 });
                          		 }
                      	     });
                            var allCheckupSum = 0*1;
                            var passSum = 0*1;
                            var slightSum = 0*1;
                            var generalSum = 0*1;
                            var severitySum = 0*1;
                            var fatalitySum = 0*1;
                            var conformitySum = 0*1;
                            var repairNumSum = 0*1;
                            angular.forEach($scope.depList, function(oneDep, i) {
                            	allCheckupSum += oneDep.allCheckup*1;
                            	passSum += oneDep.pass*1;
                            	slightSum += oneDep.slightNum*1;
                                generalSum += oneDep.generalNum*1;
                                severitySum += oneDep.severityNum*1;
                                fatalitySum += oneDep.fatalityNum*1;
                            	conformitySum += oneDep.conformity*1;
                            	repairNumSum += oneDep.repairNum*1;
                            });
                            $scope.depList.push({
                            	"departMent":'汇总',//产品线
                            	"allCheckup":allCheckupSum,//检查总数
                            	"pass":passSum,//通过数
                            	"slightNum":slightSum,//轻微问题数
                                "generalNum":generalSum,//一般问题数
                                "severityNum":severitySum,//严重问题问题
                                "fatalityNum":fatalitySum,//致命问题数
                            	"conformity":conformitySum,//不符合项数
                            	"repairNum":repairNumSum,//按计划修复数
                            	"repairRate":conformitySum===0?'0%':((repairNumSum/conformitySum)*100).toFixed(0)+'%',//问题修复率
                            	"processConformity":allCheckupSum===0?'0%':((1-(slightSum+generalSum*1.2+severitySum*1.5+fatalitySum*2)/allCheckupSum)*100).toFixed(0)+'%',//过程符合度
                            });
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            /**
             * 生成excel表格
             */
            $scope.toExcel = function () {
                var start = inform.format($scope.startTime, 'yyyy-MM-dd');
                var end = inform.format($scope.endTime, 'yyyy-MM-dd');
                var urlData = {};
                if ($scope.checkedProj){
                    urlData = {
                        'flag':"proj",
                        'moduleName': $scope.formInsert.moduleName,
                        'createTime': start,
                        'regenerTime': end
                    };
                }
                if ($scope.checkedProblem){
                    urlData = {
                        'flag':"problem",
                        'moduleName': $scope.formInsert.moduleName,
                        'createTime': start,
                        'regenerTime': end
                    };
                }
                if ($scope.checkedYear){
                    urlData = {
                        'flag':"year",
                        'moduleName': $scope.formInsert.moduleName
                    };
                }
                inform.modalInstance("确定要下载吗！").result.then(function () {
                    //开启遮罩层
                    inform.showLayer("下载中。。。。。。");
                    $http.post(
                        $rootScope.getWaySystemApi + 'processConformity/toExcel',
                        urlData,
                        {
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': 'Bearer ' + LocalCache.getSession("token") || ''
                            },
                            responseType: 'arraybuffer'//防止中文乱码
                        }
                    ).success(function (data) {
                        //如果是IE浏览器
                        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                            var csvData = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
                            window.navigator.msSaveOrOpenBlob(csvData, '03 过程符合度报表.xlsx');
                        }
                        //google或者火狐浏览器
                        else {
                            var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
                            var objectUrl = URL.createObjectURL(blob);
                            var aForExcel = $("<a download='03 过程符合度报表.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href", objectUrl);
                            $("body").append(aForExcel);
                            $(".forExcel").click();
                            aForExcel.remove();
                        }

                        // 关闭遮罩层
                        inform.closeLayer();
                        inform.common("下载成功!");
                    });
                });
            };

            /**
             * 页面点击不符合项数后跳转页面
             */
            $scope.clickModal = function (m) {
                LocalCache.setSession('processConformityManagement_startTime', $scope.startTime);
                LocalCache.setSession('processConformityManagement_endTime', $scope.endTime);
                LocalCache.setSession('processConformityManagement_type', $scope.type);
                LocalCache.setSession('processConformityManagement_param', m);
                $state.go('app.office.incongruentQuestionManagement');
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
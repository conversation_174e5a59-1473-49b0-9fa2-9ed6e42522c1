(function () {
    app.controller("opsSupportProcess", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','opsSupportProcessService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,opsSupportProcessService,inform,Trans,AgreeConstant,LocalCache,$http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //分页
            $scope.pages = inform.initPages(); // 初始化分页数据
            $scope.type = $stateParams.type;
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
           
            //流程类型
            $scope.approvalType = [];
            // $scope.approvalType = ['访问控制','架构变动','系统搭建','端口开通','公共系统升级','解决问题--生产','解决问题--日常','日常巡检'];

             //流程状态
            $scope.approvalStatus = ['新创建','审批中','被终止','完成','取消'];

            //获取缓存
            $scope.formRefer = LocalCache.getObject('opsSupportProcess_formRefer');
            //对原缓存进行覆盖
            LocalCache.setObject('opsSupportProcess_formRefer', {});
            //初始化时间
            initTime();
            getApprovalTypeList();
                    
            $scope.getData = getData; 			// 分页相关函数
            getData();
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            function initTime() {
                //设置默认时间
                if ($scope.formRefer.endTime==null){
                    var now = new Date();
                    var endDate = inform.format(now, 'yyyy-MM-dd');
                    var startDate = inform.format(now,"yyyy-MM-01");
                    //默认开始时间
                    $scope.formRefer.endTime = endDate;
                    $scope.formRefer.startTime = startDate;
                }
            }
            
            //获取运维支持流程类型
            function getApprovalTypeList(){
                opsSupportProcessService.getApprovalTypes().then(function (data) {
                    $scope.approvalType = angular.fromJson(data.data);
                });
            }

            // function getProjectList(){
            //     $scope.projectNameList = [];
            //     comService.getProjectsName().then(function (data) {
            //         $scope.projectNameList = angular.fromJson(data.data);
            //     });
            // }

            /**
             * 查询条件中的开始时间
             */
            $scope.openDateStart = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                //仅查询中的开始时间显示控件内容
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };
            /**
             * 查询条件中的结束时间
             */
            $scope.openDateEnd = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (165 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 80);
            }

            /**
             * 获取项目
             */
            function getData(pageNum) {
                var urlData ={
                    'approvalStatus': $scope.formRefer.approvalStatus,//状态
                    'type': $scope.formRefer.approvalType,//类型
                    'operatorName': $scope.formRefer.operatorName,//姓名
                    'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                    'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间    
                    'page': pageNum,//当前页数
                    'pageSize': $scope.pages.size//每页显示条数
                };
                opsSupportProcessService.getOpsSupportProcess(urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        //测试报告集合
                        $scope.opsSupportProcessData = data.data.list;
                        if ($scope.opsSupportProcessData.length===0) {
                            $scope.pages = inform.initPages(); 			//初始化分页数据
                            inform.common(Trans("tip.noData"));
                        } else {
                        // 分页信息设置
                            $scope.pages.total = data.data.total;           // 页面数据总数
                            $scope.pages.star = data.data.startRow;         // 页面起始数
                            $scope.pages.end = data.data.endRow;            // 页面结束数
                            $scope.pages.pageNum = data.data.pageNum;       //页号
                        }
                    } else {
                        inform.common(data.message);
                    }
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
            }    
        
            
           
            /**
             * 重置
             */
            $scope.reset = function() {
                $scope.formRefer={};
                initTime();
            }

            /**
         *下载运维支持流程
         */
        $scope.toExcel = function() {
            var urlData ={
                'approvalStatus': $scope.formRefer.approvalStatus,//状态
                'type': $scope.formRefer.approvalType,//类型
                'operatorName': $scope.formRefer.operatorName,//姓名
                'startTime': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),//开始时间
                'endTime': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间    
            };
            inform.modalInstance("确定要下载运维支持流程数据表吗？").result.then(function() {
       
                inform.downLoadFile('opsSupportProcess/toExcel',urlData,"运维支持流程数据表"+inform.format(new Date(),'yyyy-MM-dd')+".xlsx");
           });
        };

       

        $scope.formatDeviation = formatDeviation;
        function formatDeviation(deviation){
            return Number(deviation*100).toFixed(0)+'%';
        }

        
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
(function () {
  app.controller('PLMActiveController', [
    'LocalCache',
    'inform',
    'Trans',
    '$rootScope',
    'comService',
    'AgreeConstant',
    '$scope',
    '$stateParams',
    '$state',
    'deptBoardFactory',
    'PLMActiveService',
    function (
      LocalCache,
      inform,
      Trans,
      $rootScope,
      comService,
      AgreeConstant,
      $scope,
      $stateParams,
      $state,
      deptBoardFactory,
      PLMActiveService
    ) {
      // 初始化
      deptBoardFactory.init($scope, '14');
      // 重置部分
      $scope.resetParam = resetParam;
      function resetParam() {
        deptBoardFactory.initTime($scope, '本年度');
      }
      $scope.getData = getData;
      function getData() {
        getStatisticsData();
        getTop5Number();
        // 获取top5的数据
        $scope.changeType($scope.type);
      }
      //统计区域
      function getStatisticsData() {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
        };
        $scope.showStatisticsInfo = false;
        PLMActiveService.getPLMBoardTotal(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.PLMActiveStatisticsData = result.data;
              $scope.showStatisticsInfo = true;
            } else {
              inform.common(result.message);
              $scope.showStatisticsInfo = true;
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
            $scope.showStatisticsInfo = true;
          }
        );
      }
      // top5标签上面数字
      function getTop5Number() {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
        };
        PLMActiveService.getDeptPLMCount(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.top5 = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // top5部分
      $scope.changeType = function changeType(type) {
        $scope.type = type;
        if (type === '14') {
          // 获取延期进行中数据
          getDelayRunningData('延期进行中', '正在进行');
        } else if (type === '2') {
          // 获取临期数据
          getTemporaryData('临期', '进行中', '正在进行');
        } else if (type === '3') {
          // 获取未到期数据
          getUnexpiredData('未到期', '进行中', '正在进行');
        } else if (type === '5') {
          // 获取延期完成数据
          getDelayFinishData('延期完成', 'finishAndRun');
        } else if (type === '6') {
          // 获取已按时完成数据
          getPlanFinishData('finishAndRun', 'planFinish');
        }
      };
      // 获取已按时完成数据
      function getPlanFinishData(finishAndRun, planFinish) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          finishAndRun: finishAndRun,
          planFinish: planFinish,
        };
        PLMActiveService.getDeptPLMInfo(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.planFinishData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // 获取延期进行中数据
      function getDelayRunningData(activeProgress, projectStatus) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          activeProgress: activeProgress,
          projectStatus: projectStatus,
        };
        PLMActiveService.getDeptPLMInfo(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.delayRunningData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // 获取临期数据
      function getTemporaryData(finishState, activeProgress, projectStatus) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          finishState: finishState,
          activeProgress: activeProgress,
          projectStatus: projectStatus,
        };
        PLMActiveService.getDeptPLMInfo(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.temporaryData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // 获取未到期数据
      function getUnexpiredData(finishState, activeProgress, projectStatus) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          finishState: finishState,
          activeProgress: activeProgress,
          projectStatus: projectStatus,
        };
        PLMActiveService.getDeptPLMInfo(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.unexpiredData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // 获取延期完成数据
      function getDelayFinishData(activeProgress, finishAndRun) {
        var currentUrlData = {
          endTime: $scope.formRefer.endTime + '-31',
          startTime: $scope.formRefer.startTime + '-01',
          department: $scope.formRefer.orgCode,
          activeProgress: activeProgress,
          finishAndRun: finishAndRun,
        };
        PLMActiveService.getDeptPLMInfo(currentUrlData).then(
          function (result) {
            if (result.code === AgreeConstant.code) {
              $scope.delayFinishData = result.data;
            } else {
              inform.common(result.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      // 页面加载后触发
      $scope.$watch('$viewContentLoaded', function () {
        var localFormRefer = LocalCache.getObject('departmentList_formRefer');
        if (Object.keys(localFormRefer).length > 0) {
          $scope.formRefer = localFormRefer;
          $scope.butFlag = localFormRefer.searchTimeString;
        }
        if ($stateParams.orgCode) {
          $scope.formRefer.orgCode = $stateParams.orgCode;
        }
        getData();
      });

      $scope.title = '';
      $scope.desc = true;
      // 排序
      $scope.order = (str) => {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      };
    },
  ]);
})();

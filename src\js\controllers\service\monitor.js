(function() {
  'use strict';
  angular.module('app')
    .controller("monitor", ['$rootScope', '$scope', 'inform', '$compile', '$state', 'Microservice', 'LocalCache', 'Trans',
      function($rootScope, $scope, inform, $compile, $state, Microservice, LocalCache, Trans) {

        $scope.alertAuth = true;
        $scope.admin = LocalCache.getSession("currentUserName") || '';
        $scope.getEurekaApplications = getEurekaApplications;
        $scope.getEruekaStatus = getEruekaStatus;
        $scope.getAllHealth = getAllHealth;
        $scope.baseName = baseName;
        $scope.subSystemName = subSystemName;


        var separator = '.';
        $scope.getEurekaApplications();
        $scope.getEruekaStatus();
        $scope.getAllHealth();

        function getEruekaStatus() {
          Microservice.getEruekaStatus()
            .then(function(data) {
              console.info(data);
              $scope.status = data;
            }, function(data) {
              inform.common(Trans('tip.requestError'));
            });
        }

        function getEurekaApplications() {
          Microservice.getEurekaApplications()
            .then(function(data) {
              console.info(data);
              $scope.appInstances = data;
            }, function(data) {
              inform.common(Trans('tip.requestError'));
            });
        }

        function getAllHealth() {
          Microservice.getAllHealth()
            .then(function(data) {
              $scope.healthData = transformHealthData(data);
              console.info($scope.healthData);
            }, function(data) {
              inform.common(Trans('tip.requestError'));
            });
        }



        function baseName(name) {
          if (name) {
            var split = name.split('.');
            return split[0];
          }
        }

        function transformHealthData(data) {
          var response = [];
          flattenHealthData(response, null, data);
          return response;
        }

        function flattenHealthData(result, path, data) {
          for (var key in data) {
            if (data.hasOwnProperty(key)) {
              var value = data[key];
              if (isHealthObject(value)) {
                if (hasSubSystem(value)) {
                  addHealthObject(result, false, value, getModuleName(path, key));
                  flattenHealthData(result, getModuleName(path, key), value);
                } else {
                  addHealthObject(result, true, value, getModuleName(path, key));
                }
              }
            }
          }
          return result;
        }

        function isHealthObject(healthObject) {
          var result = false;
          for (var key in healthObject) {
            if (healthObject.hasOwnProperty(key)) {
              if (key === 'status') {
                result = true;
              }
            }
          }
          return result;
        }

        function hasSubSystem(healthObject) {
          var result = false;
          for (var key in healthObject) {
            if (healthObject.hasOwnProperty(key)) {
              var value = healthObject[key];
              if (value && value.status) {
                result = true;
              }
            }
          }
          return result;
        }

        function addHealthObject(result, isLeaf, healthObject, name) {
          var healthData = {
            name: name
          };
          var details = {};
          var hasDetails = false;
          for (var key in healthObject) {
            if (healthObject.hasOwnProperty(key)) {
              var value = healthObject[key];
              if (key === 'status' || key === 'error') {
                healthData[key] = value;
              } else {
                if (!isHealthObject(value)) {
                  details[key] = value;
                  hasDetails = true;
                }
              }
            }
          }
          // Add the details
          if (hasDetails) {
            healthData.details = details;
          }
          // Only add nodes if they provide additional information
          if (isLeaf || hasDetails || healthData.error) {
            result.push(healthData);
          }
          return healthData;
        }

        function getModuleName(path, name) {
          var result;
          if (path && name) {
            result = path + separator + name;
          } else if (path) {
            result = path;
          } else if (name) {
            result = name;
          } else {
            result = '';
          }
          return result;
        }

        function subSystemName(name) {
          if (name) {
            var split = name.split('.');
            split.splice(0, 1);
            var remainder = split.join('.');
            return remainder ? ' - ' + remainder : '';
          }
        }

        $scope.getBadgeClass = function(statusState) {
          if (statusState && statusState === 'UP') {
            return 'label-success';
          } else {
            return 'label-danger';
          }
        };

        $scope.closeAlert = function() {
          $scope.alertAuth = false;
        };


      }
    ]);
})();
(function() {
    'use strict';
    app.factory('keyTechIssueService', keyTechIssueService);
    keyTechIssueService.$inject = ["HttpService", '$rootScope'];

    function keyTechIssueService(HttpService, $rootScope) {
        var service = {
            getKeyIssueInfo: getKeyIssueInfo,
            deleteKeyIssueInfo: deleteKeyIssueInfo,
            addKeyIssueInfo: addKeyIssueInfo,
            updateKeyIssueInfo: updateKeyIssueInfo,
            updateKeyIssueCheck:updateKeyIssueCheck,
            getDataById:getDataById,
            auditById:auditById
        };
        return service;
        /**
         * 分页查询关键技术问题
         */
        function getKeyIssueInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'keyQuestion/getData', urlData);
        }
        /**
         * 删除问题
         */
        function deleteKeyIssueInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'keyQuestion/deleteData', urlData);
        }
        /**
         * 新增问题
         */
        function addKeyIssueInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'keyQuestion/addData', urlData);
        }
        /**
         * 修改问题
         */
        function updateKeyIssueInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'keyQuestion/updateData', urlData);
        }
        /**
         * 审核拒绝
         */
        function updateKeyIssueCheck(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'keyQuestion/auditRefuse', urlData);
        }

        function auditById(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'keyQuestion/audit', urlData);
        }

        /**
         * 通过id获取数据
         */
        function getDataById(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'keyQuestion/getDataById', urlData);
        }
    }
})();
(function () {
    app.controller("peerReviewFeedback", ['comService', '$http', 'LocalCache', '$rootScope', '$state', '$stateParams', '$scope', '$modal', 'meetingService', 'inform', 'Trans', 'AgreeConstant','reportService','mailService','OfficeFileTool','reviewProblemService',
        function (comService, $http, LocalCache, $rootScope, $state, $stateParams, $scope, $modal, meetingService, inform, Trans, AgreeConstant,reportService,mailService,OfficeFileTool,reviewProblemService) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
    		var flag=0;
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            //会议状态下拉框数据源
            $scope.meetingStateSelect = [{
                value: '00',
                label: '草稿'
            }, {
                value: '01',
                label: '已确定'
            }];
          //会议形式下拉框数据源
            $scope.meetingShapeSelect = [{
                value: '0',
                label: '邮件'
            }, {
                value: '1',
                label: '会议'
            }];
            $scope.roleList = [{
        	   value: '1',
               label: '关键人员'
           },{
         	 	value: '2',
         	 	label: '参会人员'
           }];
          //评审结果下拉框数据源
            $scope.resSelect = [{
                value: '0',
                label: '有条件通过'
            }, {
                value: '1',
                label: '通过'
            }, {
                value: '2',
                label: '不通过'
            }, {
                value: '3',
                label: '未评审'
            }];
            //评审级别下拉框数据源
            $scope.levelSelect = [{
                value: '0',
                label: '一级'
            }, {
                value: '1',
                label: '二级'
            }, {
                value: '2',
                label: '三级'
            }];
          //是否跟踪下拉框数据源
            $scope.trackSelect = [{
                value: '0',
                label: '是'
            }, {
                value: '1',
                label: '否'
            }];
            //评审主题Map
            $scope.themeMap = reviewThemeListConfig;
            $scope.spec = {};
            $scope.spec.attachmentAddress = [];
            $scope.spec.attachmentSize = [];
            $scope.spec.attachmentAddressID=[];
            $scope.keyFigureList = [];
            //创建文件上传组件
            var paramObj ={listId:'thelist',
            		removeCall:function (id) {
            			var index = $scope.spec.attachmentAddressID.indexOf(id);
                        $scope.spec.attachmentAddress.splice(index,1);
                        $scope.spec.attachmentSize.splice(index,1);
                        $scope.spec.attachmentAddressID.splice(index,1);
            		},
            		getFilePathCall:function (fileId) {
                        var index = $scope.spec.attachmentAddressID.indexOf(fileId);
                        var filePath = $scope.spec.attachmentAddress[index];
                        return filePath;
            		},
            		getSizeOfFiles:function () {
                        var size = 0;
                        for (var i = 0; i <  $scope.spec.attachmentSize.length; i++) {
                            size = size + parseInt($scope.spec.attachmentSize[i]);
                        }
                        return size;
            		},
            		uploadSuccess:function (file,response) {
                        $scope.spec.attachmentAddress.push(response.data);
                        $scope.spec.attachmentAddressID.push(file.id);
                        $scope.spec.attachmentSize.push(file.size);
            		}
            };
            var uploader = OfficeFileTool.createUploader(paramObj,'review');
            //初始化参数
            init();
            //绑定文件控件改变事件
            $("#files").change(submitForm);
            //时间选择框下拉框数据源
            $scope.timeSelect = [];
            //设置时间选择框
            setTimeSelect();
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.getHours = getHours;
            $scope.duringChange = duringChange;
            $scope.setHours = setHours;
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
            }
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
    		 * 文件选择事件
    		 */
    	     $scope.selectFile = function() {
    	     	document.getElementById("files").click();
    	     };
    	    /**
    	     * 上传文件
    	     */
    	    function submitForm(e){
    	    	var formData = new FormData(document.getElementById("form12"));
    	    	var file =e.currentTarget.files[0]; 
    	    	if(!file){
                    inform.common("请先选择文件!");
                    return false;
                }else if (file.type !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
                    inform.common("请选择.xlsx类型的文档进行上传!");
                    return false;
                } else if(file.size > AgreeConstant.fileSize){
                    inform.common("上传文件大小不得超过2M，请分割后重新上传!");
                    $("#form12")[0].reset();
                    return false;
                }
    	    	formData.append('file', file);
    	            inform.uploadFile('uploadFile/returnPath',formData,function func(result){
    	               $scope.spec.meetingAccessory = result.data;
    	            	// 关闭遮罩层
    	               inform.closeLayer();
    	               createFile(file.name);
    	               getMeeting();
    	            });
    	     }
    	    /**
    	     * 回显
    	     */
    	    function createFile(name){
    	    	 $('#meetingList').empty();
    	         $scope.fileName = name;
    	         $('#meetingList').append('<div  id="' + 'meetingList' + '" class="item">' +
    	         		   '<span id="fileNameDis">'+name+'</span>'+
    	                    '<p class="downLoad-this" style="display: inline-block;margin-left: 20px;" title="下载文件">' +
    	                    '<a class="glyphicon glyphicon-arrow-down green" style="top: 3px;"></a></p>' +
    	                    '</div>');
    	    }
    	    //下载文件
    	    $('#meetingList').on('click', '.downLoad-this', function() {
                var filePath = $scope.spec.meetingAccessory;
                //拼装下载内容
                var urlData = {
                    'filePath':filePath
                };
                inform.downLoadFile('mail/downLoadFile', urlData, $scope.fileName);
            });
            function init() {
            	 //获取所有项目名称
                $scope.projectList = [];
                comService.getProjectsName().then(function (data) {
                    $scope.project = angular.fromJson(data.data);
                    angular.forEach($scope.project, function (res, index) {
                        $scope.projectList.push(res);
                    });
                    flag++;
                    getMeeting();
                });
            	//获取员工信息
            	$scope.loginMap = {};
            	$scope.employeeMap = {};
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function(data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                        angular.forEach($scope.employeeList,function (employee) {
                            $scope.loginMap[employee.realName] = employee.loginName;
                            $scope.employeeMap[employee.realName] = employee.companyTitleLevel;
                        });
                        flag++;
                        getMeeting();
                    }
                });
                //获取会议地点
        		$scope.meetingPlaceList = [];
        		comService.queryEffectiveParam('MeetingRoom','MeetingRoom').then(function(data) {
            		if (data.data) {
            			$scope.meetingPlaceList =  data.data;
            			 flag++;
                         getMeeting();
            		}
                });
        		//获取会议等级
        		$scope.meetingGradeList = [];
        		comService.queryEffectiveParam('MeetingType','MeetingType_2').then(function(data) {
            		if (data.data) {
            			$scope.meetingGradeList =  data.data;
            			 flag++;
                         getMeeting();
            		}
                });

            }
            /**
             * 计算会议持续时间
             */
            function getHours(){
            	var ms = new Date($scope.spec.meetingDay+" "+$scope.spec.endTime+":00").getTime()
            			-new Date($scope.spec.meetingDay+" "+$scope.spec.startTime+":00").getTime();
            	$scope.spec.REVIEW_MEETING_TIME = Math.floor(ms/1000/60/60);
            	duringChange();
            }
            /**
             * 修改人员工作量
             */
            function duringChange(){
            	if ($scope.keyFigureList.length !== 0){
            		for (var i = 0 ;i < $scope.keyFigureList.length;i++){
                        //若评委不参加，直接置为0
                        if($scope.keyFigureList[i].join === '1'){
                            //评委工作量是会议时长
                            $scope.keyFigureList[i].judgeWorkLoad = '0';
                        }else {
                            //评委工作量是会议时长
                            $scope.keyFigureList[i].judgeWorkLoad = $scope.spec.REVIEW_MEETING_TIME;
                        }

            		}
            	}
            }
            /**
             * 根据会议类别处理会议持续时间
             */
            function setHours(){
            	if ($scope.spec.meetingShape==='0'){
            		$scope.spec.REVIEW_MEETING_TIME = 0;
            		return;
            	}
            	getHours();
		    }
            /**
             * 根据关键角色名称，获取其等级
             * @param item
             */
            $scope.setJudgeGrade = function(item){
                if(typeof (item) !== 'undefined') {
                    item.judgeGrade = $scope.employeeMap[item.keyFigure];
                }
            };
            $scope.dateToQuarter = function () {
                var month = new Date($scope.spec.meetingDay).getMonth() + 1;
                $scope.spec.QUARTER = inform.dateToQuarter(month);
            };
            /**
             * 保存信息
             */
            function saveInfo(mail) {
            	//校验关键角色列表是否有重复
                if (!verifyFigureList()) {
                    return;
                }
                //以逗号拼接关键角色
                var keyFigure = '';
                for (var i = 0; i < $scope.spec.keyFigures.length; i++) {
                    keyFigure = keyFigure.concat($scope.spec.keyFigures[i].keyFigure).concat(",")
                    .concat($scope.spec.keyFigures[i].role).concat(";");
                }
                //获取当前时间是星期几
                var now = new Date($scope.spec.meetingDay);
                var meetingWeek = '';
                if(now.getDay() === 0){
                    meetingWeek = '星期7'
                }else{
                    meetingWeek = '星期' + now.getDay();
                }
                var urlData = {
                    'id': $stateParams.serialNumber,
                    'meetingType': $scope.spec.meetingType,
                    'meetingDay': inform.format($scope.spec.meetingDay, 'yyyy-MM-dd'),
                    'meetingWeek':meetingWeek,
                    'startTime': $scope.spec.startTime,
                    'endTime': $scope.spec.endTime,
                    'meetingShape': $scope.spec.meetingShape,
                    'meetingTheme': $scope.spec.meetingTheme,
                    'projectAssistant': $scope.spec.projectAssistant,
                    'meetingPlace': $scope.spec.meetingPlace,
                    'meetingRoom': $scope.spec.meetingRoom,
                    'meetingState': $scope.spec.meetingState,
                    'meetingGrade': $scope.spec.meetingGrade,
                    'keyFigure':keyFigure,
                    'remark':$scope.spec.remark,
                    'relevance':$scope.spec.relevance,
                    'accessory':$scope.spec.attachmentAddress==null?'':$scope.spec.attachmentAddress.join(','),
                    'accessorySize':$scope.spec.attachmentSize==null?'':$scope.spec.attachmentSize.join(','),
                    'meetingAccessory':$scope.spec.meetingAccessory
                };
                meetingService.updateMeetingInfo(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                    	addProblem(mail);
                    }else{
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });

            }
            /**
             * 选择项目时，回填产品线项目经理、项目助理信息
             * @param projectId
             */
            $scope.projectChange = function(projectId){
                reportService.getProjectInfoById(projectId).then(function (data) {
                    if (data.code !== AgreeConstant.code) {
                        inform.common(data.message);
                        return;
                    }
                    data.data = angular.fromJson(data.data);
                    if (data.data.length === 0) {
                        inform.common("该项目不存在")
                    }else{
                        $scope.spec.productLineName = data.data.productLineName;
                        $scope.spec.productLine = data.data.productLine;
                        $scope.spec.PROJECT_MANAGER = data.data.projectManager;
                        $scope.spec.PROJECT_ADMINISTRATORS = data.data.projectAssistant;
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });

                //若flag存在，则表示为初始化，不置空pre_review_id
                if(!flag){
                    $scope.spec.pre_review_id = [];
                }
                reportService.getPreRerviewReviewContentAndId(projectId).then(function(data) {
                    if (data.data) {
                        $scope.preRerviewList = data.data;
                    }
                });
            };
            /**
             * 添加同行评审信息
             */
            $scope.addReview = function (mail){
            	if($scope.spec.meetingAccessory==null || $scope.spec.meetingAccessory===''){
            		inform.common("请上传会议纪要");
            		return;
            	}
            	if($scope.spec.meetingShape==='1'&&($scope.spec.meetingRoom==null||$scope.spec.meetingRoom==='')){
            		inform.common("当前会议形式为会议，请选择会议室");
            		return;
            	}
            	$scope.dateToQuarter();//获取季度
            	$scope.jList = [];
            	var WORKLOAD = 0;
            	for(var i = 0 ; i<$scope.keyFigureList.length;i++){
            		var one = {
	            		'judgeName' : $scope.keyFigureList[i].keyFigure,
	            		'judgeGrade' : $scope.keyFigureList[i].judgeGrade,
	            		'judgeWorkLoad' : $scope.keyFigureList[i].judgeWorkLoad,
	            		'judgeJoin' : $scope.keyFigureList[i].join
            		};
            		//只有参加的才计算工作量
            		if(one.judgeJoin === '0') {
                        WORKLOAD = WORKLOAD + one.judgeWorkLoad * 1;
                    }
            		$scope.jList.push(one);
            	}
                var urlData = {
                        'excelName': 'app.office.report_0004',
                        'ID': $scope.spec.relevance,
                        'QUARTER': $scope.spec.QUARTER,
                        'REVIEW_LEVEL': $scope.spec.REVIEW_LEVEL,//评审级别
                        'PLAN_COMPLETION_DATE': inform.format($scope.spec.meetingDay, 'yyyy-MM-dd'),
                        'PRODUCT_LINE': $scope.spec.productLine,
                        'TEM_NAME': $scope.spec.TEM_NAME,//项目名称
                        'REVIEW_CONTENT':  $scope.spec.meetingTheme,//评审内容
                        'REVIEW_MEETING_TIME': $scope.spec.REVIEW_MEETING_TIME,//会议持续时间
                        'REVIEW_RESULT': $scope.spec.REVIEW_RESULT,//会议结果
                        'PROJECT_MANAGER': $scope.spec.PROJECT_MANAGER,//项目经理
                        'PROJECT_ADMINISTRATORS': $scope.spec.PROJECT_ADMINISTRATORS,
                        'PARTICIPANTS': $scope.spec.PARTICIPANTS,//参加人
                        'IS_TRACK_ONZENTAO': $scope.spec.IS_TRACK_ONZENTAO,//是否追踪
                        'LIABLE_PERSON': $scope.spec.LIABLE_PERSON.join(),//责任人
                        'VERSION': $scope.spec.VERSION,//项目版本
                        'REVIEW_TYPE': $scope.spec.meetingShape,//评审类别
                        'REVIEW_THEME': $scope.spec.REVIEW_THEME,//评审主题
                        'DOC_PAGES': $scope.spec.DOC_PAGES,//评审材料页数/条数
                        'WORKLOAD': WORKLOAD,//评审工作量
                        'COMMENTS':$scope.spec.remark,
                        'REVIEW_PROBLEM_NUMBER':$scope.spec.REVIEW_PROBLEM_NUMBER,
                        'judgeList': JSON.stringify($scope.jList),
                        'isReview':$scope.spec.meetingGrade==='MeetingType_2_2'?'0':'1',
                        'pre_review_id':$scope.spec.pre_review_id==null?'':$scope.spec.pre_review_id.join(',')
                    };
                    reportService.verifyReviewExist(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                    	if (data.code !== AgreeConstant.code) {
                            inform.common(data.message);
                            return;
                        }
                        if (data.data > 0) {
                            inform.common("该评审已存在,请勿重复添加");
                            return;
                        }
                        if($scope.spec.relevance == null || $scope.spec.relevance==='' || $scope.reviewDelete != null){
                        	addReviewInfo(urlData,mail);
                        } else {
                        	updateReviewInfo(urlData,mail);
                        }
                    }else{
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });

            };
            function addReviewInfo(urlData,mail) {
                reportService.addReportInfo(urlData).then( function (data) {
                    if (data.code === AgreeConstant.code) {
                    	$scope.spec.relevance = data.data;
                    	saveInfo(mail);
                    } else {
                        inform.common("新增数据失败");
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            function updateReviewInfo(urlData,mail) {
                reportService.updateReportInfo(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                    	saveInfo(mail);
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            function addProblem(mail){
            	var uploadUrl = "";
                //0-邮件评审,1-会议评审
                if($scope.spec.meetingShape === '0'){
                    uploadUrl = 'reviewProblem/uploadMailExcelByPath';
                }else{
                    uploadUrl = 'reviewProblem/uploadMeetingExcelByPath';
                }
                var urlData={
                	'reviewId':$scope.spec.relevance,
                    'path':$scope.spec.meetingAccessory
                };
                reviewProblemService.uploadExcelByPath(urlData,uploadUrl).then(function (data) {
                	if (data.code === AgreeConstant.code) {
                		var modalInstance = $modal.open({
    	                    templateUrl: 'errorModel.html',
    	                    controller: 'ModalInstanceCtrl',
    	                    size: "lg",
    	                    resolve: {
    	                        items: function () {
    	                            return data.message;
    	                        }
    	                    }
    	                });
                        modalInstance.result.then(function () {
                        	if (mail==null) {
                        		$scope.backModal();
                        	} else {
                        		packageMail($stateParams.serialNumber);
                        	}
                        });
                    } else {
                        inform.common(data.message);
                    }
                 }, function (error) {
                     inform.common(Trans("tip.requestError"));
                 });
            }
            /**
             * 发送邮件
             */
            function packageMail(id){
            	var urlData = {};
            	if ($scope.spec.meetingType==='MeetingType_2'){
            		urlData={
                    	'beanName':'com.snbc.office.service.impl.review.ReviewMeetingSeriveImpl',
                    	'serialNumber':id
                    }
            	}else if ($scope.spec.meetingType==='MeetingType_1'){
            		urlData={
                    	'beanName':'com.snbc.office.service.impl.OrdinaryMeetingSeriveImpl',
                    	'serialNumber':id
                    }
            	}
            	mailService.packageMail(urlData).then(function (data) {
                     if (data.code === AgreeConstant.code) {
                    	 var info = data.data;
                    	 info.addressees = [];
                    	 info.addresseesHand = [];
                    	 for (var i = 0; i < $scope.spec.keyFigures.length; i++) {
                    		 var name = $scope.spec.keyFigures[i].keyFigure;
                    		 if($scope.loginMap[name]==null||$scope.loginMap[name]===''){
                    			 info.addresseesHand.push(name);
                    		 } else {
                    			 info.addressees.push($scope.loginMap[name]+"@newbeiyang.com");
                    		 }
     	                }
                    	 if($scope.spec.meetingAccessory != null && $scope.spec.meetingAccessory !== ''){
                     		info.accessory = $scope.spec.meetingAccessory;
                     		info.accessorySize = AgreeConstant.fileSize+'';
                     	}else{
                     		info.accessory = $scope.spec.attachmentAddress.join(',');
                      		info.accessorySize = $scope.spec.attachmentSize.join(',');
                     	}
                    	 info.theme = $scope.spec.meetingTheme;
                    	 LocalCache.setObject('mail_info',info);
                    	 $state.go('app.office.mailManagement', {
                             root:'app.office.meetingController',
                             serialNumber:id,
                             dataInfo:2
                         });
                     }else{
                         inform.common(data.message);
                     }
                 }, function (error) {
                     inform.common(Trans("tip.requestError"));
                 });
            }
            /**
             * 获取会议室
             */
            $scope.getMeetingRoom = function(){
            	$scope.meetingRoomList = [];
            	if($scope.spec.meetingPlace==null||$scope.spec.meetingPlace===''){
            		return;
            	}
        		comService.queryEffectiveParam('MeetingRoom',$scope.spec.meetingPlace).then(function(data) {
            		if (data.data) {
            			$scope.meetingRoomList =  data.data;
            			 flag++;
                         getMeeting();
            		}
                });
            };
            /**
             * 获取会议信息
             */
            function getMeeting(){
            	if (flag !== 4){
            		return;
            	}
            	var info = {
            			'id':$stateParams.serialNumber
            	};
            	meetingService.getMeeting(info).then(function (data) {
                	if (data.code === AgreeConstant.code) {
                		$scope.spec = data.data;
                		var rooms=[];
                		for(var i = 0;i<$scope.spec.meetingRooms.length;i++){
                			rooms.push($scope.spec.meetingRooms[i].room);
                		}
                		$scope.spec.meetingRoom=rooms.join();
                		if(null != $scope.spec.accessory && '' !== $scope.spec.accessory) {
                            $scope.spec.attachmentAddress = $scope.spec.accessory.split(',');
                            $scope.spec.attachmentSize = $scope.spec.accessorySize.split(',');
                        }
                		$scope.spec.attachmentAddressID=[];
                		getHours();
                		$scope.getMeetingRoom();
                		if ($scope.spec.relevance==null || $scope.spec.relevance===''){
                			setPerson();
                		} else {
                			getPeerReview();
                		}
                		//会议纪要回显
                        if(null != $scope.spec.meetingAccessory && '' !== $scope.spec.meetingAccessory) {
                        	var fileArray = $scope.spec.meetingAccessory.replace(/\\/g,'/').split('/');
                            var name = fileArray[fileArray.length-1];
                        	createFile(name);
                        }
                		//创建回显的文件列表 返回文件id集合
                        var fileIdList = uploader.initShowFileList($scope.spec.attachmentAddress);
                        if (fileIdList.length > 0) {
                        	$scope.spec.attachmentAddressID =fileIdList;
                        }

                    } else {
                        inform.common(data.message);
                    }
                }, function () {
                	inform.common(Trans("tip.requestError"));
            	});                
            }
            /**
             * 默认会议人员
             */
            function setPerson(){
            	var person='';
        		//添加关键人员
        		for (var i=0;i<$scope.spec.keyFigures.length;i++){
        			person=person+$scope.spec.keyFigures[i].keyFigure+"、";
        			$scope.setJudgeGrade($scope.spec.keyFigures[i]);
        			$scope.spec.keyFigures[i].judgeWorkLoad = $scope.spec.REVIEW_MEETING_TIME;
        			if ($scope.spec.keyFigures[i].role === '关键人员'){
        				$scope.keyFigureList.push($scope.spec.keyFigures[i]);
        			}
        		}
        		$scope.spec.PARTICIPANTS = person.substring(0,person.length-1);
        		//设置人员默认参加
        		for (var j=0;j<$scope.keyFigureList.length;j++){
        			$scope.keyFigureList[j].join='0';
        		}
            }
            /**
             * 获取反馈信息
             */
            function getPeerReview(){
            	var urlData = {
            			'id':$scope.spec.relevance
            	};
            	 reportService.getReportInfo(urlData).then(function (data) {
                     if (data.code === AgreeConstant.code) {
                         data.data = angular.fromJson(data.data);
                         if(data.data.list.length === 0){
                        	 $scope.reviewDelete = 'delete';
                        	 setPerson();
                        	 return;
                         }
                         var info =  data.data.list[0];
                         $scope.spec.TEM_NAME = parseInt(info.TEM_NAME);
                         $scope.spec.REVIEW_MEETING_TIME = info.REVIEW_MEETING_TIME;
                         $scope.spec.IS_TRACK_ONZENTAO = info.IS_TRACK_ONZENTAO;
                         $scope.spec.DOC_PAGES = info.DOC_PAGES;
                         $scope.spec.REVIEW_LEVEL = info.REVIEW_LEVEL;
                         $scope.spec.VERSION = info.VERSION;
                         $scope.spec.REVIEW_PROBLEM_NUMBER = info.REVIEW_PROBLEM_NUMBER;
                         $scope.spec.REVIEW_RESULT = info.REVIEW_RESULT;
                         $scope.spec.REVIEW_THEME = info.REVIEW_THEME;
                         $scope.spec.PARTICIPANTS = info.PARTICIPANTS;
                         $scope.spec.LIABLE_PERSON = info.LIABLE_PERSON.split(',');
                         $scope.spec.remark = info.COMMENTS;
                         $scope.spec.pre_review_id = info.pre_review_id.split(',');
                         //关联预评审
                         $scope.preRerviewList = [];
                         $scope.projectChange($scope.spec.TEM_NAME);
                         //获取评委列表
                         getJudgeByReview();
                     } else {
                         inform.common(data.message);
                     }
                 }, function (error) {
                     inform.common(Trans("tip.requestError"));
                 });
             }
            /**
             * 查询评委信息
             * @param reviewId
             */
            function getJudgeByReview() {
                var param = {'reviewId': $scope.spec.relevance};
                reportService.getJudgeByReview(param).then(function (data) {
                    var judgeList = angular.fromJson(data.data);
                    $scope.keyFigureList = [];
                	for(var i = 0 ; i<judgeList.length;i++){
                		var one = {
                				'keyFigure':judgeList[i].judgeName,
                				'judgeGrade':judgeList[i].judgeGrade
                		};
                		$scope.setJudgeGrade(one);
                		one.judgeWorkLoad = judgeList[i].judgeWorkLoad;
                		one.join = judgeList[i].judgeJoin;
                		$scope.keyFigureList.push(one);
                	}
                });
            }
            /**
             * 设置时间选择框
             */
            function setTimeSelect() {
                for(var i = 6; i < 24 ; i++){
                    //获取小时
                    var hour;
                    if(i < 10){
                        hour = '0' + i;
                    }else{
                        hour = i;
                    }
                    for(var j = 0; j < 12 ; j++){
                        //获取分钟（每10分钟，取一次）
                        var minutes;
                        if(j < 2){
                            minutes = '0' + j*5;
                        }else{
                            minutes = j*5;
                        }
                        var value = {
                            value:hour + ':' + minutes
                        };
                        //存入数组
                        $scope.timeSelect.push(value);
                    }
                }
                $scope.spec.startTime="08:30";
                $scope.spec.endTime="09:30";
            }

            /**
             * 校验关键角色列表中是否存在重复
             * 规则：
             * 1.重复则提示"关键角色×××,×××重复,请修改"并返回false
             * 2.无重复则返回true
             * @returns {boolean}
             */
            function verifyFigureList() {
                var verifyList = [];
                var duplicate = "";
                for (var i = 0; i < $scope.keyFigureList.length; i++) {
                    if (verifyList.indexOf($scope.keyFigureList[i].keyFigure) > -1
                        && duplicate.indexOf($scope.keyFigureList[i].keyFigure) < 0) {
                        duplicate = duplicate.concat($scope.keyFigureList[i].keyFigure).concat(",");
                    }
                    verifyList.push($scope.keyFigureList[i].keyFigure);
                }
                //如果为空,说明无重复则返回true;
                if (!duplicate) {
                    return true;
                }
                //不为空,则提示哪些评委重复,并返回false
                inform.common("关键角色" + duplicate.substring(0, duplicate.length - 1) + "存在重复,请修改");
                return false;
            }

            /**
             * 修改开始时间后，默认结束时间退后一小时
             */
            $scope.changeEnd = function(){
                var startTimeArray = $scope.spec.startTime.split(':');
                var hour = parseInt(startTimeArray[0])+1;
                var before = (hour < 10) ? '0' : '';
                $scope.spec.endTime = before + hour+':'+startTimeArray[1];
            };
            /**
             * 判断会议时间是否正常
             * 时间不正常，弹出提示信息，并清空最后选择的选择框的值
             * @param flag startTime：开始时间选择框  endTime：结束时间选择框
             */
            $scope.timeChange = function(flag){
                if($scope.spec.startTime !== '' && $scope.spec.endTime  !== ''
                    && typeof ($scope.spec.startTime) !== 'undefined' && typeof ($scope.spec.endTime)  !== 'undefined'){
                    var endTimeArray = $scope.spec.endTime.split(':');
                    var startTimeArray = $scope.spec.startTime.split(':');
                    if(startTimeArray[0] > endTimeArray[0]
                        || (startTimeArray[0] === endTimeArray[0] && startTimeArray[1] >= endTimeArray[1])){
                        inform.common('会议结束时间必须大于会议开始时间，请重新选择！！！');
                        if(flag === 'startTime'){
                            $scope.spec.startTime = '';
                        }else{
                            $scope.spec.endTime = '';
                        }
                    }
                }
            };


            /**
             * 返回按钮
             */
            $scope.backModal = function () {
                $state.go('app.office.meetingController',{type:2});
            };

            /**
             * 新增一个关键角色
             */
            $scope.addNewBind = function () {
                //评委信息
                var judge = {
                	'role': '关键人员',
                    'keyFigure': '',
                    'judgeWorkLoad':$scope.spec.REVIEW_MEETING_TIME,
                    'join': '0',
                    'flag': true
                };
                $scope.keyFigureList.push(judge);
            };

            //取消一行
            $scope.deleteNewBind = function (index) {
                if (index >= 0) {
                    $scope.keyFigureList.splice(index, 1);
                }
            };

            /**
             * 会议日期时间
             */
            $scope.meetingDayOpen = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.meetingDayOne = true;
            };

            /**
             * 评委是否参与选项改变后触发的事件
             */
            $scope.judgeJoinChange = function(item){
                //状态为参加
                if(item.join === '1'){
                    item.judgeWorkLoad = '0';
                }else{
                    //若为会议类型，则为工作量复制，否则清空
                    if($scope.spec.meetingShape !== '0'){
                        item.judgeWorkLoad = $scope.spec.REVIEW_MEETING_TIME
                    }else{
                        item.judgeWorkLoad = '';
                    }
                }
            };
            
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }]);
})();
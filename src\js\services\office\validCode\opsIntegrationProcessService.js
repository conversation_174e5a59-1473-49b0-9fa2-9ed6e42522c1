(function () {
    'use strict';
    app.factory('opsIntegrationProcessService', opsIntegrationProcessService);
    opsIntegrationProcessService.$inject = ["HttpService", '$rootScope'];

    function opsIntegrationProcessService(HttpService, $rootScope) {
        var service = {
            getOpsIntegrationProcess:getOpsIntegrationProcess
        };
        return service;
        /**
         * 获取持续集成流程
         */
        function getOpsIntegrationProcess(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'opsIntegrationProcess/getOpsIntegrationProcess', urlData);
        }
       
    }
})();
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date:   2019-01-10 15:20:05
 * @Last Modified by:   l<PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
	app.controller("treeController", [
			'$rootScope',
			'$scope',
			'treeService',
			'inform',
			'Trans',
			'AgreeConstant',
			function($rootScope, $scope, treeService, inform, Trans,
					AgreeConstant) {
                $scope.limitList = AgreeConstant.limitList; // 正则校验配置

				var key;//仓库名关键字TextObject
				var nodes = [];//存放日志详细信息
				$scope.logChangedList = [];

				//用于储存当前节点的数据
                $scope.clickTreeNode =[];

				getData();
				var zTreeObj, setting = {
					data: {
						simpleData: {
							enable: true
						}
					},
					callback : {
						onDoubleClick : onClick,
						//用于捕获节点被展开的事件回调函数
						onExpand:onClick
					},
					view: {
						nameIsHTML: true,
						selectedMulti: false,
						addDiyDom: addDiyDom,
						fontCss: getFontCss//高亮显示处理
					}
				};
				
				/**
				 * 获取数据
				 */
				function getData() {
					//存储节点信息
					$scope.treeList = [];
					//获取第一层的节点
					treeService.getFirst().then(
							function(data) {
								if (data.code === AgreeConstant.code) {
									$scope.treeList = angular
											.fromJson(data.data);
									if ($scope.treeList === "") {
										inform.common(Trans("连接错误，请检查SVN参数配置"));
										return;
									}
									angular.forEach($scope.treeList, function(res, index) {
										$scope.treeList[index].isParent = true;
									});
									
									zTreeObj = $.fn.zTree.init($("#tree"), setting,$scope.treeList);
									//ewf
									key = $("#key");
									key.bind("focus", focusKey)
									.bind("blur", blurKey)
									.bind("propertychange", searchNode)
									.bind("input", searchNode);
									$("#name").bind("change", clickRadio);
									$("#getNodesByParamFuzzy").bind("change", clickRadio);

								} else {
									inform.common(data.message);
								}
							}, function() {
								inform.common(Trans("tip.requestError"));
							});
				}
				/**
				 * 给节点右边添加查询日志按钮
				 */
				function addDiyDom(treeId, treeNode) {
					var path = treeNode.path;
					if(path.indexOf("/03-source") === -1 && path.indexOf("/03source") === -1) return;
					var aObj = $("#" + treeNode.tId + "_a");
					var editStr = "<span id='diyBtn_" +treeNode.tId+ "' title='历史日志' class='demoIcon'><span class='button icon01'></span></span>";
					aObj.append(editStr);
					var btn = $("#diyBtn_"+treeNode.tId);
					//点击事件：获取日志
					if (btn.length > 0) btn.bind("click", function(){
						
						if (treeNode.path==null) {
							return ;
						}
						$("#historyLogShow").modal('show');
						//存储日志信息
						$scope.logList = [];
						
						
						
						treeService.getLog(treeNode.path).then(function(data) {
							nodes = [];//清空日志详细信息
							if (data.code === AgreeConstant.code) {
								$scope.logList = angular.fromJson(data.data);
								//日志数量
								var index = data.data.length;
								//获取对应index的日志详细信息
								for(var i = 0; i < index; i++) {
									nodes.push(angular.fromJson(data.data[i].changedList));
								}
								//默认展示第一条的详细信息
								$scope.logChangedList = nodes[0];
								
							} else {
								inform.common(data.message);
							}
						})
					
					});

					//添加文件复制的点击事件
					var pathArray = path.split("/");
					var parentPathName = pathArray[pathArray.length-2];
					if(parentPathName === 'tags' || pathArray[pathArray.length-3] === 'tags'){
						var copyStr = "<span id='copySvnFile_" +treeNode.tId+ "' title='创建发布标签'><span class='button iconCopy'></span></span>";
						aObj.append(copyStr);

						var copyBtn = $("#copySvnFile_"+treeNode.tId);
						//点击事件：弹出窗口，准备复制文件的前提条件
						if (copyBtn.length > 0) copyBtn.bind("click", function(){
							if (treeNode.path==null) {
								return ;
							}

							//判断，已被修改的标签，禁止复制
							treeService.getLog(treeNode.path).then(function(data) {
								nodes = [];//清空日志详细信息
								if (data.code === AgreeConstant.code) {
									$scope.logList = angular.fromJson(data.data);
									//日志数量
									var index = data.data.length;
                                    $scope.copySvnFilePath = path;
                                    $scope.copySvnFilePathArray = pathArray;
									if(index > 1){
                                        $("#historyLogForCopy").modal('show');
									}else{
										$('#targetPathName').val($scope.copySvnFilePathArray[$scope.copySvnFilePathArray.length-1]);
										$('#message').val('');
										$("#copy_modal").modal('show');
									}
								} else {
									inform.common(data.message);
								}
							})

						});
					}

				}

                /**
                 * 存在修改的历史信息的标签允许被发布的 确定按钮的方法
                 */
				$scope.historyLogForCopyClick = function(){
                    $("#historyLogForCopy").modal('hide');
                    $('#targetPathName').val($scope.copySvnFilePathArray[$scope.copySvnFilePathArray.length-1]);
                    $('#message').val('');
                    $("#copy_modal").modal('show');
                };

				/**
				 * 复制文件的方法
				 */
				$scope.copyFile = function(){
					var param = {
						'path': $scope.copySvnFilePath,
						'targetPathName':$scope.copySvnFile.targetPathName,
						'message':$scope.copySvnFile.message,
						'pathName':$scope.copySvnFilePathArray[$scope.copySvnFilePathArray.length-1]
					};
					treeService.copySvnTagFile(param).then(function(data) {
						if (data.code === AgreeConstant.code) {
							common(data.message,function () {
                                //调用点击事件，查询当前节点的所有子节点
                                $scope.clickTreeNode.isOpen = 'no';
                                onClick('', '', $scope.clickTreeNode);
								$("#copy_modal").modal('hide');
							})
						} else {
							inform.common(data.message);
						}
					})
				};

			
				//输入框获得焦点
				function focusKey(e) {
					if (key.hasClass("empty")) {
						key.removeClass("empty");
					}
				}
				//输入框失去焦点
				function blurKey(e) {
					if (key.get(0).value === "") {
						key.addClass("empty");
					}
				}
				var lastValue = "", nodeList = [];
				function clickRadio(e) {
					lastValue = "";
					searchNode(e);
				}
				/**
				 * 模糊查询节点
				 */
				function searchNode(e) {
					var zTree = $.fn.zTree.getZTreeObj("tree");
					if (!$("#getNodesByFilter").attr("checked")) {
						var value = $.trim(key.get(0).value);
						var keyType = "name";//按照节点名称匹配
						if ($("#name").attr("checked")) {
							keyType = "name";
						} else if ($("#level").attr("checked")) {
							keyType = "level";
							value = parseInt(value);
						} else if ($("#id").attr("checked")) {
							keyType = "id";
							value = parseInt(value);
						}
						if (key.hasClass("empty")) {
							value = "";
						}
						if (lastValue === value) return;
						lastValue = value;
						//如果输入框内空还原
						if (value === ""){
							clearNodes();
							return;
						} 
						updateNodes(false);

						if ($("#getNodeByParam").attr("checked")) {
							var node = zTree.getNodeByParam(keyType, value);
							if (node == null) {
								nodeList = [];
							} else {
								nodeList = [node];
							}
						} else if ($("#getNodesByParam").attr("checked")) {
							nodeList = zTree.getNodesByParam(keyType, value);
						} else if ($("#getNodesByParamFuzzy").attr("checked")) {
							nodeList = zTree.getNodesByParamFuzzy(keyType, value);
						}
					} else {
						updateNodes(false);
						nodeList = zTree.getNodesByFilter(filter);
					}
					updateNodes(true);

				}
				//改变样式
				function updateNodes(highlight) {
					var zTree = $.fn.zTree.getZTreeObj("tree");
					for( var i=0, l=nodeList.length; i<l; i++) {
						nodeList[i].highlight = highlight;
						zTree.updateNode(nodeList[i]);
					}
				}
				//清空样式
				function clearNodes(highlight) {
					var zTree = $.fn.zTree.getZTreeObj("tree");
					for( var i=0, l=nodeList.length; i<l; i++) {
						nodeList[i].highlight = false;
						zTree.updateNode(nodeList[i]);
					}
				}
				function getFontCss(treeId, treeNode) {
					return (!!treeNode.highlight) ? {color:"#A60000", "font-weight":"bold"} : {color:"#333", "font-weight":"normal"};
				}
				/**
				 * 自定义过滤器函数
				 * 查询符合条件的节点
				 *true 表示符合搜索条件；false 表示不符合搜索条件
				 */
				function filter(node) {
					return !node.isParent && node.isFirstNode;
				}
				
				//获取当前详细信息
				$scope.getChangedInfo = function(index) {
					$scope.logChangedList = nodes[index];
				};
				/**
				 * 点击事件
				 * */
				function onClick(event, treeId, treeNode) {
					//有子节点并且没有被检查
					if (treeNode.type === "tree" && treeNode.isOpen === "no") {
					    //将当前节点信息储存起来
                        $scope.clickTreeNode =treeNode;
						//节点设置为被检查
						treeNode.isOpen = "true";
						//获取下级节点
						treeService.getOne(treeNode.path)
							.then(function(data) {
								if (data.code === AgreeConstant.code) {
										//数组存储点击节点下的子节点
										var nodeS = [];
										data.data = angular.fromJson(data.data);
										data.data = data.data.sort(function (a,b) {
											var p1 = a.name;
											var p2 = b.name;
											return p1.localeCompare(p2,"zh");
										});
										angular.forEach(data.data, function(res, index) {
											var jsonNode = {
													//文件名
													name : res.name,
													//在svn的路径
													path : res.path,
													//类型
													type : res.type,
													//类型为leaf就不是父节点
													isParent : res.type ==='leaf'?false:true
											};
											nodeS.push(angular.extend(jsonNode, res));
										});
										//若当前为刷新节点，则清空当前节点的所有子节点
                                        if(event === ''){
                                            zTreeObj.removeChildNodes(treeNode);
                                        }
										//下级节点添加
										zTreeObj.addNodes(treeNode,nodeS,false);
									} else {
									inform.common(data.message);
								}
							}, function(error) {
								inform.common(Trans("tip.requestError"));
							});
					}
					//被检查过
					else {
						return;
					}
				}

				/**
				 * 提示信息
				 * @param str  提示信息
				 * @param func 确定时执行的函数
				 */
				function  common(str,func){
					layer.confirm(str,{
						title:false,
						btn:['确定']
					},function(result){
						layer.close(result);
						if(typeof (func) !== 'undefined'){
							func();
						}
					});
				}
			} ]);
})();
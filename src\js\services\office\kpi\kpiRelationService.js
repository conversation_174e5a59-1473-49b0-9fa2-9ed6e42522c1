(function () {
    'use strict';
    app.factory('kpiRelationService', kpiRelationService);
    kpiRelationService.$inject = ['HttpService', '$rootScope'];

    function kpiRelationService(HttpService, $rootScope) {
        var service = {
            getKpiRelationByPage: getKpiRelationByPage,
            getPerformanceAssessData: getPerformanceAssessData,
            getAnalysisDataByArea: getAnalysisDataByArea,
            getAnalysisDataByDepartment: getAnalysisDataByDepartment,
            getAnalysisDataByTitle: getAnalysisDataByTitle,
            getManagerAnalysisData: getManagerAnalysisData,
            getKpiData: getKpiData,
            getAssessDetailData: getAssessDetailData,
            updateAssessResult: updateAssessResult,
            getAssessAnalysisHeader: getAssessAnalysisHeader,
            updateGuidelineAndDeviation: updateGuidelineAndDeviation,
            getCommunicationId: getCommunicationId,
        };
        return service;

        /**
         *分页查询
         */
        function getKpiRelationByPage(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'kpiRelation/getKpiRelationByPage', urlData);
        }
        /**
         * 考核流程结果查询
         */
        function getPerformanceAssessData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'kpiRelation/getPerformanceAssessData', urlData);
        }
        /**
         * 区域考核结果分析
         */
        function getAnalysisDataByArea(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'kpiRelation/getAnalysisDataByArea', urlData);
        }
        /**
         * 部门考核结果分析
         */
        function getAnalysisDataByDepartment(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'kpiRelation/getAnalysisDataByDepartment', urlData);
        }
        /**
         * 职称考核结果分析
         */
        function getAnalysisDataByTitle(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'kpiRelation/getAnalysisDataByTitle', urlData);
        }
        /**
         * 管理岗考核结果分析
         */
        function getManagerAnalysisData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'kpiRelation/getManagerAnalysisData', urlData);
        }
        /**
         * 查询kpi指标
         */
        function getKpiData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'kpiRelation/getKpiData', urlData);
        }
        /**
         * 查询考核详情 成长点、不足点
         */
        function getAssessDetailData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'kpiRelation/getAssessDetailData', urlData);
        }
        /**
         * 修改考核结果
         */
        function updateAssessResult(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'kpiRelation/updateAssessResult', urlData);
        }
        /**
         * 更新偏差值和理论权重
         */
        function updateGuidelineAndDeviation(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'kpiRelation/updateGuidelineAndDeviation', urlData);
        }
        /**
         * 查询表格header
         */
        function getAssessAnalysisHeader(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'kpiRelation/getAssessAnalysisHeader', urlData);
        }
        /**
         * 根据域账号和年份以及周期获取沟通id
         */
        function getCommunicationId(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'staffPerform/skip', urlData);
        }
    }
})();

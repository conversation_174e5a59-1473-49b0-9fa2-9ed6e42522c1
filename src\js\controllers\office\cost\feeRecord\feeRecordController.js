(function () {
    'use strict';
    app.controller("feeRecordController", ['$scope','$state','comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','feeRecordService','$stateParams','LocalCache', '$modal','$http',
        function ($scope,$state,comService, $rootScope, inform, Trans, AgreeConstant,feeRecordService,$stateParams,LocalCache, $modal,$http) {
    	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */ 
    	$scope.limitList = AgreeConstant.limitList; // 正则校验配置
    	$scope.formRefer={};
    	 //页面分页信息
        $scope.pages = inform.initPages();
        $scope.pages.size = '20';
        $scope.addFeeList = [];
        //设置列表的高度
        setDivHeight();
        //窗体大小变化时重新计算高度
        $(window).resize(setDivHeight);
    	//初始化页面信息
        initPages();
        $scope.getData=getData;
        $scope.addFeeInfo = addFeeInfo;
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */
        /**
         * 设置列表的高度
         */
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (175 + 185);
            $("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight - 30);
        }
        /**
         * 开始时间
         */
        $scope.openDateStart = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = true;    
            $scope.openedEnd = false;
        };

        /**
         * 结束时间
         */
        $scope.openDateEnd = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.openedStart = false;
            $scope.openedEnd = true;   
        };
        /**
         * 重置查询条件
         */
        $scope.reset = function () {
            $scope.formRefer = {};
            initTime()
        };
        /**
         * 初始化页面
         */
        function initPages() {
            //获取部门
            $scope.departmentList = [];
            comService.getOrgChildren('D010053').then(function (data) {
                if (data.data) {
                    $scope.departmentList = data.data;
                }
            });
            //获取产品线
            $scope.productLines=[];
            comService.queryEffectiveParam('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
                $scope.productLines = angular.fromJson(data.data);
            });
            //获取项目信息
            $scope.projectIdList = [];
            $scope.idManagerMap={};
   		 	comService.getProjectsByLineOffice('').then(function (data) {
                $scope.projectIdList =data.data;
                angular.forEach($scope.projectIdList, function (project, index) {
                    $scope.idManagerMap[project.id] = project.projectManager;
                });
            });
            //获取费用类型
            $scope.expenseTypeList=[];//全部
            $scope.expenseTypeUseList=[];//可维护
            comService.queryEffectiveParam('COSTFEE_TYPE','COSTFEE_TYPE').then(function(data) {
                $scope.expenseTypeList = angular.fromJson(data.data);
                angular.forEach($scope.expenseTypeList, function (type, index) {
                    if(type.paramCode!=='COSTFEE_TYPE_1'){
                    	$scope.expenseTypeUseList.push(type);
                    }
                });
            });
            $scope.statusMap={
            		'0':'待审核',
            		'1':'已审核',
            		'2':'已结算',
            		'3':'维护成本'
            }
            initTime();
            getData(1);
        }
        /**
         * 初始化检索条件开始时间
         */
        function initTime() {
            var date = new Date();
            //项目信息,开始日期向前推三个月（90天）
            date.setMonth(date.getMonth() - 3);
            //对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
            $scope.formRefer.startTime = inform.format(date, 'yyyy-MM-dd');
        }
        /**
         * 获取项目经理
         */
        $scope.getManager=function (m){
        	m.projectManager = $scope.idManagerMap[m.projectId];
        }
        /**
         * 查询费用信息
         */
        function getData(indexNum){
        	var urlData = {
            	'departmentCode': $scope.formRefer.department,
            	'productLineCode': $scope.formRefer.productLine,
            	'projectName': $scope.formRefer.cname,
            	'startDate': inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                'endDate': inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),
            	'projectManager': $scope.formRefer.projectManager,
            	'currentPage': indexNum,
            	'pageSize': $scope.pages.size
         	};
        	feeRecordService.getData(urlData).then(function (data) {
                 if (data.code === AgreeConstant.code) {
                     $scope.dataList = data.data.list;
                     if ($scope.dataList.length === 0) {
                         inform.common("无符合条件的项目信息");
                         $scope.pages = inform.initPages(); 			        //初始化分页数据
                     }else{
                         // 分页信息设置
                         $scope.pages.total = data.data.total; 			// 页面数据总数
                         $scope.pages.star = data.data.startRow; 		// 页面起始数
                         $scope.pages.end = data.data.endRow;	 		// 页面结束数
                         $scope.pages.pageNum = data.data.pageNum;       //页号
                     }
                 } else {
                     inform.common(data.message);
                 }
             }, function (error) {
                 inform.common(Trans("tip.requestError"));
             });
        }
        /**
         * 新增一个费用明细
         */
        $scope.addNewBind = function () {
            //费用信息
            var judge = {
            	'projectId': '?',
                'expenseTypeId': '',
                'feeTime': inform.format(new Date(), 'yyyy-MM-dd'),
                'amount':'',
                'feeObject':'项目组',
                'feeDescription':'',
                'status':'0'
            };
            $scope.addFeeList.push(judge);
            setTimeout(function () {
                $scope.addFeeList[$scope.addFeeList.length - 1].feeTime = inform.format(new Date(), 'yyyy-MM-dd')
            }, 200)
            var div = document.getElementById('feeInfoAdd');
            div.scrollTop =div.scrollHeight;
        };
        /**
         * 保存
         */
        $scope.saveFree=function(m){
        	if (m.projectId==null||m.expenseTypeId==null||m.feeTime==null||m.amount==null||m.feeObject==null||m.feeDescription==null){
        		inform.common("所有信息均必填，请检查。");
        		return;
        	}
        	//修改
        	upFeeInfo(m);
        }
        /**
         * 新增
         */
        function addFeeInfo(){
        	for (var i=0;i<$scope.addFeeList.length;i++){
        		if ($scope.addFeeList[i].projectId==null||$scope.addFeeList[i].expenseTypeId==null||$scope.addFeeList[i].feeTime==null||$scope.addFeeList[i].amount==null||$scope.addFeeList[i].feeObject==null||$scope.addFeeList[i].feeDescription==null){
            		inform.common("所有信息均必填，请检查。");
            		return;
            	}
        	}
        	for (var j=0;j<$scope.addFeeList.length;j++){
        		$scope.addFeeList[j].feeTime=inform.format($scope.addFeeList[j].feeTime, 'yyyy-MM-dd');
        		feeRecordService.addFeeInfo($scope.addFeeList[j]).then(function(data) {
        			if (data.code === AgreeConstant.code) {
        				inform.common(data.message);
        			}
        		}, function(error) {
        			inform.common(Trans("tip.requestError"));
        		});
        	}
        	$("#add_info").modal("hide");
        	getData(1);
        	$scope.addFeeList = [];
        }
        /**
         * 修改
         */
        function upFeeInfo(m){
        	m.feeTime=inform.format(m.feeTime, 'yyyy-MM-dd');
        	feeRecordService.upFeeInfo(m).then(function(data) {
                inform.common(data.message);
            }, function(error) {
                inform.common(Trans("tip.requestError"));
            });
        }
        /**
         * 删除
         */
        $scope.delFree=function(m,index){
        	//删除列表
        	if (m.id==null){
        		deleteNewBind(index);
        		return;
        	}
        	//删除数据库
        	delFeeInfo(m);
        }
        /**
         * 取消一行费用明细
         */
        function deleteNewBind(index) {
            if (index >= 0) {
                $scope.addFeeList.splice(index, 1);
            }
        }
        /**
         * 删除数据库
         */
        function delFeeInfo(m){
        	feeRecordService.delFeeInfo(m).then(function(data) {
                if (data.code === AgreeConstant.code) {
                    var modalInstance = $modal.open({
                        templateUrl: 'myModalContent.html',
                        controller: 'ModalInstanceCtrl',
                        size: "sm",
                        resolve: {
                            items: function items() {
                                return "删除成功，是否刷新页面？";
                            }
                        }
                    });
                    modalInstance.result.then(function () {
                    	getData(1);
                    });
                } else {
                    inform.common(data.message);
                }
            }, function(error) {
                inform.common(Trans("tip.requestError"));
            });
        }
	     /**
          * *************************************************************
          *              方法声明部分                                 结束
          * *************************************************************
          */
         } 
    ]);
})();
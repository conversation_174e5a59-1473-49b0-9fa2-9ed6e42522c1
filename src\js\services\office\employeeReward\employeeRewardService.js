(function() {
    'use strict';
    app.factory('employeeRewardService', employeeRewardService);
    employeeRewardService.$inject = ["HttpService", '$rootScope'];

    function employeeRewardService(HttpService, $rootScope) {
        var service = {
            getRewardInfo: getRewardInfo,  
            addReward: addReward,
            updateReward: updateReward,
            deleteReward: deleteReward,
            splitRewardPerson: splitRewardPerson
        };
        return service;
        /**
         * 分页查询信息
         * @param urlData 
         */
        function getRewardInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'employeeReward/getRewardInfo', urlData);
        }
        /**
         * 添加数据
         * @param urlData 
         */
        function addReward(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'employeeReward/addReward', urlData);
        }
          
        
        /**
         * 修改数据
         * @param urlData 
         */
        function updateReward(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'employeeReward/updateReward', urlData);
        }
        /**
         * 删除数据
         * @param urlData 
         */
        function deleteReward(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'employeeReward/deleteReward', urlData);
        }

        /**
         * 拆分数据
         * @param urlData
         */
        function splitRewardPerson(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'employeeReward/splitRewardPerson', urlData);
        }
    }
})();
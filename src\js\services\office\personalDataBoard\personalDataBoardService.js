(function () {
  'use strict';
  app.factory('personalDataBoardService', personalDataBoardService);
  personalDataBoardService.$inject = ['HttpService', '$rootScope'];

  function personalDataBoardService(HttpService, $rootScope) {
    var service = {
      getPersonalReviewExport: getPersonalReviewExport,
      getPersonalReviewQuestionsData: getPersonalReviewQuestionsData,
      getPersonalReviewQuestionsGather: getPersonalReviewQuestionsGather,
      getPersonalDevelopBugsData: getPersonalDevelopBugsData,
      getBugNumbers: getBugNumbers,
      getPersonalWorkingHoursData: getPersonalWorkingHoursData,
      getPersonalAssessData: getPersonalAssessData,
      getBugSolutionList: getBugSolutionList,
      getTrainPlanData: getTrainPlanData,
      getCorrectData: getCorrectData,
      getAdviceData: getAdviceData,
      getStaffInfoData: getStaffInfoData,
      getKnowledgeData: getKnowledgeData,
      selectPersonalAssessData: selectPersonalAssessData,
    };
    return service;

    function getKnowledgeData(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'knowledge/getKnowledgeData', urlData);
    }
    /**
     * 获取评审输出信息
     * @param urlData 查询参数
     */
    function getPersonalReviewExport(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'personalDataBoard/getPersonalReviewExport', urlData);
    }

    //获取评审贡献信息
    function getPersonalReviewQuestionsData(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'personalDataBoard/getPersonalReviewQuestionsData', urlData);
    }
    //获取评审贡献汇总信息
    function getPersonalReviewQuestionsGather(urlData) {
      return HttpService.post(
        $rootScope.getWaySystemApi + 'personalDataBoard/getPersonalReviewQuestionsGather',
        urlData
      );
    }
    //获取开发过程中bug数据
    function getPersonalDevelopBugsData(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'personalDataBoard/getPersonalDevelopBugsData', urlData);
    }
    //获取产生的bug数据
    function getBugNumbers(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'personalDataBoard/getSolveOrCreateBugsNum', urlData);
    }
    //获取开发过程中bug数据的解决方案列表
    function getBugSolutionList() {
      return HttpService.post($rootScope.getWaySystemApi + 'personalDataBoard/getBugSolutionList');
    }
    //获取工时数据
    function getPersonalWorkingHoursData(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'personalDataBoard/getPersonalWorkingHoursData', urlData);
    }
    //获取考核数据
    function getPersonalAssessData(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'staffPerform/selectPerformanceByParam', urlData);
    }
    //获取培训数据
    function getTrainPlanData(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'train/getInfo', urlData);
    }
    //获取一点改善
    function getCorrectData(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'correct/getData', urlData);
    }
    //获取合理化建议
    function getAdviceData(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'advice/getAdviceByMap', urlData);
    }
    //获取员工信息
    function getStaffInfoData(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'staffInfo/seleteByParam', urlData);
    }
    //获取个人看板考核结果
    function selectPersonalAssessData(urlData) {
      return HttpService.post($rootScope.getWaySystemApi + 'staffPerform/selectPersonalAssessData', urlData);
    }
  }
})();

/**
 * 用于实现点击下拉框外部关闭的指令
 * @example <div class="dropdown-btn" click-outside="showModal=false"></div>
 */
(function () {
  'use strict';
  angular.module('app').directive('clickOutside', [
    '$document',
    function ($document) {
      return {
        restrict: 'A',
        scope: {
          clickOutside: '&',
        },
        link: function (scope, element, attr) {
          var onClick = function (event) {
            // 检查点击的元素是否在目标元素内
            var isChild = element[0].contains(event.target);

            if (!isChild) {
              scope.$apply(function () {
                scope.clickOutside();
              });
            }
          };

          $document.on('click', onClick);

          // 当指令被销毁时移除事件监听
          scope.$on('$destroy', function () {
            $document.off('click', onClick);
          });
        },
      };
    },
  ]);
})();

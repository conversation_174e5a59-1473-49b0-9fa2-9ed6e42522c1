(function () {
    app.controller("zentaoController", ['$rootScope','comService', '$scope','$state','$stateParams','$location','$modal','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams,$location, $modal,inform,Trans,AgreeConstant,LocalCache,$http) {

		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		 // 显示遮罩层
         inform.showLayer("跳转中...")
    	//查看是否登陆过
    	$scope.zentaoLoginFlag = LocalCache.getObject('zentaoManagement_zentaoLoginFlag');
    	//上一次登录时间
    	var timeLogin = LocalCache.getSession('zentaoLoginTime');
    	var timeNow = new Date().getTime();
    	var minute = parseInt(timeNow-timeLogin*1)/1000/60;
        //若未登录，或者现在时间超过登录时间25分钟，跳转去登陆界面
        if ($scope.zentaoLoginFlag.flag==null||minute>25){
        	//获取到当前路由
            var pathUrl = $location.path();
            pathUrl = pathUrl.substr(1,pathUrl.length).replace(/\//g,'.');
            //设置需要返回缓存
            LocalCache.setObject("zentaoManagement_return",pathUrl);
            //跳转
        	$state.go("app.office.zentaoLoginController");
        	return;
        }
    	window.open($rootScope.zentaoSystemApi+'index.php?m=my&f=index');
    	inform.closeLayer();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
        /**
	 	 * *************************************************************
	 	 *              方法声明部分                                 结束
	 	 * *************************************************************
	 	 */

	}]);
})();
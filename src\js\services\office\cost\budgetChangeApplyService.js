(function() {
    'use strict';
  app.factory('budgetChangeApplyService', budgetChangeApplyService);
  budgetChangeApplyService.$inject=["HttpService",'$rootScope'];

  function budgetChangeApplyService(HttpService,$rootScope){

	var service={
	        getProjectBudget:getProjectBudget,//获取项目信息
			getAllLevel:getAllLevel,//获取所有级别
			getAllStageList:getAllStageList,
			getAllTitleLevelList:getAllTitleLevelList,
			getPersonBudgetByStage:getPersonBudgetByStage,
			saveBudgetChangeApply:saveBudgetChangeApply,
			getChangeLog:getChangeLog,
			deleteChangeApply:deleteChangeApply,
			agreeChange:agreeChange
	};
    return service;
    /**
     * 获取项目预算信息
     */
    function getProjectBudget() {
    	return HttpService.post($rootScope.getWaySystemApi + 'budgetChangeApply/getProjectBudget');
    }

    /**
     * 获取所有级别
     */
    function getAllLevel() {
    	return HttpService.post($rootScope.getWaySystemApi + 'budgetChangeApply/getAllLevel');
    }

    /**
     * 获取所有项目阶段(code、name)
     */
    function getAllStageList() {
        return HttpService.post($rootScope.getWaySystemApi + 'budgetChangeApply/getAllStageList');
    }
    /**
     * 获取所有岗位与级别(code、name)
     */
    function getAllTitleLevelList(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'budgetChangeApply/getAllTitleLevelList',urlData);
    }
    /**
     * 按项目阶段获取人力预算总计
     */
    function getPersonBudgetByStage(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'budgetChangeApply/getPersonBudgetByStage',urlData);
    }
    //保存变更申请数据
    function saveBudgetChangeApply(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'budgetChangeApply/saveBudgetChangeApply',urlData);
    }
    //获取变更记录
    function getChangeLog(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'budgetChangeApply/getChangeLog',urlData);
    }
    //删除变更申请
    function deleteChangeApply(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'budgetChangeApply/deleteChangeApply',urlData);
    }
    //同意变更
    function agreeChange(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'budgetChangeApply/agreeChange',urlData);
    }
  }
})();

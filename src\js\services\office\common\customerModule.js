(function() {
    'use strict';
  app.factory('customerModule', customerModule);
  customerModule.$inject=["customerManagementService","AgreeConstant","inform","Trans"];

  function customerModule(customerManagementService,AgreeConstant,inform,Trans){
    var service={
		 initModule:initModule
    };
    return service;

    //回调方法
    var customerModuleBack = null;
    //scope
    var customerModuleScope = null;
    /**
     * 初始弹框
     *  参数结构
     * dataInfo= {profession:'行业', keyWord:'客户名称' }
     */
    function initModule(dataInfo,scope,callback) {
        if(dataInfo.keyWord){
            document.getElementById("customerName").value=dataInfo.keyWord;
        }

    	//查行业
		scope.professionList = [{
			value: '金融',
			label: '金融'
		},{
			value: '新零售',
			label: '新零售'
		},{
        	value: '物流',
        	label: '物流'
        },{
        	value: '新兴',
        	label: '新兴'
        }];
		//获取客户
		scope.getCustomer = getCustomer;
		//选择客户
        scope.selectCustomer = selectCustomer;
        //选中行业
        scope.selectProfession =selectProfession;
        //回调方法
        customerModuleBack = callback;
        customerModuleScope = scope;
        customerModuleScope.colorStyle = "";
        var paramInfo={
            'customerName':dataInfo.keyWord,
            'customerStatus':'0'
        }
        getCustomer(paramInfo);
        $("#customer").click();
    }

    /**
     * 选中行业与客户触发的事件
     */
     function selectProfession(profession){
           var paramInfo={
                'profession':profession.value,
                'customerName':document.getElementById("customerName").value,
                'customerStatus':'0'
            }
         getCustomer(paramInfo);
     }
    /**
     * 根据根据行业查询客户
     */
     function getCustomer(dataInfo){

     //修改被选中的字体颜色
        customerModuleScope.professionList.forEach(function (ele) {ele.active=ele.value===dataInfo.profession;})
        customerModuleScope.moduleData = [];
		//获取客户列表
        customerManagementService.getCustomerInfoList(dataInfo).then(function (data) {
            if (data.code===AgreeConstant.code) {
                angular.forEach(data.data.list, function(res, index) {
                    var json = {
                        id: res.customerId,//客户编号
                        name: res.customerName,//客户名称
                        profession: res.profession//行业
                    };
                    customerModuleScope.moduleData.push(json);
                    //设置焦点
                    setTimeout(setFocus,500);
                });
            } else {
                inform.common(data.message);
            }
        }, function() {
            inform.common(Trans("tip.requestError"));
		});
     }
     function setFocus(){
        document.getElementById("customerName").focus()
     }

     /**
      * 选择客户
      */
      function selectCustomer(m){
        $("#customerModule").modal("hide");
        customerModuleBack(m);
      }
  }
})();
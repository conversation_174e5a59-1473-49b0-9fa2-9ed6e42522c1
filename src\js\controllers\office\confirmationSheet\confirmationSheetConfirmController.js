
(function() {
    app.controller("confirmationSheetConfirmManagement", ['comService', '$rootScope', '$scope', 'inform', 'confirmationSheetService','Trans', 'AgreeConstant', '$modal', '$state', '$stateParams','LocalCache',
        function(comService, $rootScope, $scope,inform, confirmationSheetService, Trans, AgreeConstant, $modal, $state, $stateParams,LocalCache) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.type='1';
            //获取数据
            $scope.getData = getData;
            getData();
            initPages();
            //禅道产品线下拉框
            $scope.proProductLineList = AgreeConstant.proProductLineList;
            $scope.classify = ['新增需求','修改bug','功能优化','版本定制'];
            $scope.fileTypes = ['发布包','脚本','手册','源代码'];
            //替换HTML标签的表达式
            var regxHTML = /<[^>]*>|<\/[^>]*>/gm;
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 获取确认单详情
             */
            function getData() {
                var urlData ={
                    'id':$stateParams.id,//测试报告Id
                };
                confirmationSheetService.getConfirmationSheetInfo(urlData).then(function(data) {
                        if (data.code===AgreeConstant.code) {
                            $scope.confirmationSheetInfo = data.data;
                            $scope.testSpec = $scope.confirmationSheetInfo.testSpec;
                            //发布/上线风险说明
                            if($scope.confirmationSheetInfo.riskNote) {
                                $scope.confirmationSheetInfo.riskNote = $scope.confirmationSheetInfo.riskNote.replace(regxHTML,"").replaceAll("&nbsp;"," ");
                            }
                            //内部验收测试情况说明
                            if($scope.confirmationSheetInfo.reportSummary) {
                                $scope.confirmationSheetInfo.reportSummary = $scope.confirmationSheetInfo.reportSummary.replace(regxHTML,"").replaceAll("&nbsp;"," ");
                            }
                            setConfirmationSheetType();
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            //设置确认单类型及发布风险、同意发布的反显
            function setConfirmationSheetType(){
                //确认单类型
                if($scope.confirmationSheetInfo.confirmType === 1){
                    var update = document.getElementById("update");
                    update.checked = true;
                }else if($scope.confirmationSheetInfo.confirmType === 2){
                    var online = document.getElementById("online");
                    online.checked = true;
                }else {
                    var publish = document.getElementById("publish");
                    publish.checked = true;
                }

                //是否存在发布风险
                if($scope.confirmationSheetInfo.riskFlag === 1){
                    var hasPublishRisk = document.getElementById("hasPublishRisk");
                    hasPublishRisk.checked = true;
                    $scope.riskValue='是';
                }else {
                    var noHasPublishRisk = document.getElementById("noHasPublishRisk");
                    noHasPublishRisk.checked = true;
                    $scope.riskValue='否';
                }

                //是否同意发布
                if($scope.confirmationSheetInfo.agreeFlag === 1){
                    var noAgreePublish = document.getElementById("noAgreePublish");
                    noAgreePublish.checked = true;
                    $scope.agreeValue='否';
                }else {
                    var agreePublish = document.getElementById("agreePublish");
                    agreePublish.checked = true;
                    $scope.agreeValue='是';
                }
            }
            /**
             * 页面初始化
             */
            function initPages() {
                //获取员工信息
                $scope.employeesList = [];
                $scope.employees = [];

                comService.getParamList('项目管理员', '').then(function (data) {
                    if (data.data) {
                        $scope.employeesList = data.data;
                    }
                });
                //获取产品线
                $scope.projectLine = [];
                comService.queryEffectiveParam('PRODUCT_TYPE','PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.projectLine = data.data;
                    }
                });
            }
            //返回
            $scope.goBack = function () {
                $state.go("app.office.confirmationSheetUpdate");
            };

            $scope.startOaFlow = function () {

                $scope.confirmationSheetInfo.accountInfo = {
                    "account":LocalCache.getSession('currentUserName'),
                    //私钥解密出明文 再公钥加密返回
                    "passwordVal":$rootScope.RSAEncrypt($rootScope.RSADecrypt(LocalCache.getSession('OAPassword')))
                };
                confirmationSheetService.startOaProcess($scope.confirmationSheetInfo).then(function(data) {
                        if (data.code===AgreeConstant.code) {
                            inform.modalInstance('钉钉流程创建成功.').result.then(function () {
                                $scope.goBack();
                            });
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        } ]);
})();
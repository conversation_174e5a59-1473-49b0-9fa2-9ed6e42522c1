
(function() {
	app.controller("confirmationSheetManagement", ['comService', '$rootScope', '$scope', 'inform', 'confirmationSheetService','Trans', 'AgreeConstant', '$modal', '$state', '$stateParams','LocalCache',
		function(comService, $rootScope, $scope,inform, confirmationSheetService, Trans, AgreeConstant, $modal, $state, $stateParams,LocalCache) {
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	//获取缓存
		$scope.formRefer ={};
        $scope.formRefer = LocalCache.getObject('confirmationSheet_formRefer');
        if($scope.formRefer.type!=='detail'){
		    var date = new Date();
		    date.setMonth(date.getMonth() - 1);
            $scope.formRefer.startTime = inform.format(date, 'yyyy-MM-dd');
        }
        //对原缓存进行覆盖
        LocalCache.setObject("confirmationSheet_formRefer",{});
		 // 正则校验配置
         $scope.limitList = AgreeConstant.limitList;

        //页面分页信息
        $scope.pages = {
            pageNum : '',   //分页页数
            size : '',      //分页每页大小
            total : ''      //数据总数
        };
        //禅道产品线下拉框
        $scope.proProductLineList = AgreeConstant.proProductLineList;
        //确认单状态
        $scope.confirmSheetStatusSelect = ['待生成','待发布','发布中','成功','拒绝'];
    	// 初始化分页数据
    	$scope.pages = inform.initPages();

        //设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		//获取数据
		$scope.getData = getData;
		getData();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */

		$scope.resetParam = function(){
		    var date = new Date();
		    date.setMonth(date.getMonth() - 1);
            $scope.formRefer.startTime = inform.format(date, 'yyyy-MM-dd');
            $scope.formRefer.endTime = '';
            $scope.formRefer.proProjectName = '';
            $scope.formRefer.productLine = '';
            $scope.formRefer.confirmSheetStatus = '';
            $scope.formRefer.versionNumber = '';
            $scope.formRefer.creator = '';
		}
		/**
		 * 获取项目
		 */
		function getData(pageNum) {
			var urlData ={
			    'projectName':$scope.formRefer.proProjectName,//禅道项目名称
			    'productLine':$scope.formRefer.productLine,//产品线
                'status':$scope.formRefer.confirmSheetStatus,//确认单状态
                'versionNumber':$scope.formRefer.versionNumber,//发布版本
                'creator':$scope.formRefer.creator,//创建者
                'startTime':inform.format($scope.formRefer.startTime,'yyyy-MM-dd'),//开始时间
                'endTime':inform.format($scope.formRefer.endTime,'yyyy-MM-dd'),//结束时间
                'currentPage':pageNum,
                'pageSize':$scope.pages.size
			};
			confirmationSheetService.getTestReportList(urlData).then(function(data) {
				if (data.code===AgreeConstant.code) {
                    //测试报告集合
                    $scope.testReportData = data.data.list;
                    if ($scope.testReportData.length===0) {
						$scope.pages = inform.initPages(); 			//初始化分页数据
						inform.common(Trans("tip.noData"));
                    } else {
                    // 分页信息设置
                    	$scope.pages.total = data.data.total;           // 页面数据总数
                    	$scope.pages.star = data.data.startRow;         // 页面起始数
                    	$scope.pages.end = data.data.endRow;            // 页面结束数
                    	$scope.pages.pageNum = data.data.pageNum;       //页号
                    }
				} else {
					inform.common(data.message);
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
		}

		/**
    	 * 设置列表的高度
    	 */
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 185);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 50);
 		}

        //跳转详情页
        $scope.updateConfirmationSheet = function(m){
            $scope.formRefer.testReportId = m.id;
            $scope.formRefer.status = m.status;
            $scope.formRefer.type='detail';
            LocalCache.setObject("confirmationSheet_formRefer",$scope.formRefer);
            $state.go('app.office.confirmationSheetUpdate');
        }
        /**
	     * *************************************************************
	     *              方法声明部分                                结束
	     * *************************************************************
	     */

		} ]);
})();
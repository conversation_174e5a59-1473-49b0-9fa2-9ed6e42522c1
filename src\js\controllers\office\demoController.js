/*
* @Author: fubaole
* @Date:   2017-09-18 14:53:05
* @Last Modified by:   fubaole
* @Last Modified time: 2018-01-15 17:20:32
*/
(function () {
    app.controller("demoManagement", ['comService','$rootScope', '$scope','demoService','inform','Trans','AgreeConstant','employeeTree',
        function (comService,$rootScope, $scope,demoService,inform,Trans,AgreeConstant,employeeTree) {

    	$scope.formInsert ={id:'678679',name:'99999'};
    	$scope.pages = inform.initPages(); // 初始化分页数据
    	$scope.getData = getData; // 分页相关函数
    	$scope.getDataInfo = getDataInfo; // 获取一条记录的详细信息
    	
    	//保存所有信息的集合
    	$scope.demoList = [];
    	$scope.addDataDemo = function () {
    		
        	demoService.addObject($scope.formInsert.id,$scope.formInsert.name).then(function(data) {
        		
        		if (data.code === AgreeConstant.code) {
        			
        			getData();

	              } else {
	                inform.common(data.message);
	              }
            }, function(error) {
              inform.common(Trans("tip.requestError"));
            });
    	};
    	//显示员工Tree
    	$scope.showTree = function () {
    		employeeTree.initTree(onCheckNode);
    	};
    	$scope.onSubmitDone =onSubmitDone;
    	//初始化页面信息
    	initPages();
    	//获取数据
    	getData($scope.pages.pageNum);
        
    	/**
    	 * 页面初始化
    	 */
    	function initPages() {
    		//获取地区
    		$scope.areaList = [];
    		comService.getParamList('AREA_TYPE','AREA_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.areaList =  data.data;
        		}
            });
    		//获取产品线
    		$scope.productLineList = [];
    		comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.productLineList =  data.data;
        		}
            });
    		//获取系统集成研发中心的下级部门信息
    		$scope.orgList = [];
    		comService.getOrganizationListByParentId('5').then(function(data) {
        		if (data.result) {
        			$scope.orgList =  data.result;
        		}
            });
    	}
        /**
         * 获取数据以分页的形式
         */
        function getData(pageNum) {
        	$scope.demoList = [];
    
        	demoService.getAllInfosByPage({currentPage:$scope.pages.pageNum,pageSize:$scope.pages.size}).then(function(data) {
        		
        		if (data.code === AgreeConstant.code) {
        			var jsonData = data.data;
        			$scope.demoList = jsonData.list;
        			
                    if ($scope.demoList.length === 0) {
                    	
                        inform.common(Trans("tip.noData"));
                        $scope.pages = inform.initPages();
                      } else {
                    	  
                	  	//分页信息设置
	                    $scope.pages.total = jsonData.total;
	                    $scope.pages.star = jsonData.startRow;
	                    $scope.pages.end = jsonData.endRow;
	                    $scope.pages.pageNum = jsonData.pageNum;
                      }
                    
	              } else {
	                inform.common(data.message);
	              }
            }, function(error) {
              inform.common(Trans("tip.requestError"));
            });
        }
        
        /**
         * 获取其中一条记录的详细信息
         * 
         * indexNum 下标
         */
        function getDataInfo(indexNum) {
        }
        /**
         * 员工Tree 	确认按钮事件
         */
        function onSubmitDone() {
        	$("#employeeTreeObj").modal('hide');
        }

        
      }]);
})();
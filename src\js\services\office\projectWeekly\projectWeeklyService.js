//# sourceURL=js/services/office/projectWeekly/projectWeeklyService.js
/*
* @Author: fubaole
* @Date:   2017-09-05 10:05:00
* @Last Modified by:   fubaole
* @Last Modified time: 2017-09-20 15:34:22
*/
(function() {
    'use strict';
  app.factory('projectWeeklyService', projectWeeklyService);

  projectWeeklyService.$inject=['HttpService','$rootScope'];

  function projectWeeklyService(HttpService,$rootScope){
    var service={
    	selectData:selectData,
    	updateInfo:updateInfo,
    	addInfo:addInfo,
    	selectOne:selectOne,
    	selectProductLine:selectProductLine,
        selectWeekTime:selectWeekTime
    };

    return service;
    /**
     * 获取所有的信息
     */
    function selectData(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'projectExpand/selectData', urlData);
    }
    /**
     * 修改项目信息
     */
    function updateInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'projectExpand/updateInfo', urlData);
    }
    /**
     * 新增项目信息
     */
    function addInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'projectExpand/addInfo', urlData);
    }
    /**
     * 回填项目信息
     */
    function selectOne(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'projectExpand/selectOne', urlData);
    }
    /**
     *查询所符合条件的产品线
     */
    function selectProductLine(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'projectExpand/selectProductLine', urlData);
    }
    /**
     *根据时间查询该时间所在周的开始时间和结束时间
     */
    function selectWeekTime(downLoadTime){
        return HttpService.post($rootScope.getWaySystemApi + 'projectExpand/selectWeekTime', downLoadTime);
    }
  }
})();
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date:   2018-03-19 10:13:58
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-04-04 09:21:17
 */

// 新增路由配置样例
// 项目新增页面的路由配置
// 如公司管理company_Management.html页面的路由配置
(function() {
  'use strict';
  /**
   * 二次开发路由配置
   */
  angular.module('app')
    .config(
      ['$stateProvider',
        function($stateProvider) {

          //下面开始LMS配置
          $stateProvider
            // LMS管理
            .state('app.lms', {
              abstract: true,
              url: '/system',
              template: '<div ui-view></div>',
            })
            //授权信息管理
            .state('app.lms.auth_Management', {
              url: '/auth_Management',
              templateUrl: 'tpl/lms/auth_Management.html',
              controller: 'auth_Management as vm'
            })
            // 新增授权
            .state('app.lms.auth_Add', {
              url: '/auth_Add',
              templateUrl: 'tpl/lms/auth_Add.html',
              controller: 'auth_Add as vm',
              resolve: {
                deps: ['$ocLazyLoad',
                  function($ocLazyLoad) {
                    return $ocLazyLoad.load('ui.select')
                    // .then(
                    //     function(){
                    //         return $ocLazyLoad.load('js/controllers/system/company_Add.js');
                    //     }
                    // );
                  }
                ]
              }
            })
            //更新授权
            .state('app.lms.auth_Update', {
              url: '/auth_Update/:id',
              templateUrl: 'tpl/lms/auth_Update.html',
              controller: 'auth_Update as vm'
            })
            // 客户端类型管理
            .state('app.lms.clientType_Management', {
              url: '/clientType_Management',
              templateUrl: 'tpl/lms/clientType_Management.html',
              controller: 'clientType_Management as vm'
            })
            // 新增客户端类型
            .state('app.lms.clientType_Add', {
              url: '/clientType_Add/:id',
              templateUrl: 'tpl/lms/clientType_Add.html',
              controller: 'clientType_Add as vm'
            })
            // 更新客户端类型
            .state('app.lms.clientType_Update', {
              url: '/clientType_Update/:id',
              templateUrl: 'tpl/lms/clientType_Update.html',
              controller: 'clientType_Update as vm'
            })
            // 客户管理
            .state('app.lms.customer_Management', {
              url: '/customer_Management',
              templateUrl: 'tpl/lms/customer_Management.html',
              controller: 'customer_Management as vm'
            })
            // 客户新增
            .state('app.lms.customer_Add', {
              url: '/customer_Add',
              templateUrl: 'tpl/lms/customer_Add.html',
              controller: 'customer_Add as vm'
            })
            // 客户组织机构
            .state('app.lms.customer_org_Management', {
              url: '/customer_org_Management',
              templateUrl: 'tpl/lms/customer_org_Management.html',
              controller: 'customer_org_Management as vm'
            })
            // 授权变更记录
            .state('app.lms.authHistory_Management', {
              url: '/authHistory_Management/:id',
              templateUrl: 'tpl/lms/authHistory_Management.html',
              controller: 'authHistory_Management as vm'
            })

            // 在线已授权管理
            .state('app.lms.online_Authorized', {
              url: '/online_Authorized',
              templateUrl: 'tpl/lms/online_Authorized.html',
              controller: 'online_Authorized as vm'
            })
            // 在线未授权管理
            .state('app.lms.online_Unauthorized', {
              url: '/online_Unauthorized',
              templateUrl: 'tpl/lms/online_Unauthorized.html',
              controller: 'online_Unauthorized as vm'
            })
            // 更新在线验证
            .state('app.lms.online_Update', {
              url: '/online_Update/:id',
              templateUrl: 'tpl/lms/online_Update.html',
              controller: 'online_Update as vm'
            })
            //离线统计
            .state('app.lms.auth_Statistic', {
              url: '/auth_Statistic',
              templateUrl: 'tpl/lms/auth_Statistic.html',
              controller: 'auth_Statistic as vm'
            })
            //在线授权历史
            .state('app.lms.online_History', {
              url: '/online_History',
              templateUrl: 'tpl/lms/online_History.html',
              controller: 'online_History as vm'
            })
            //在线授权统计
            .state('app.lms.online_Statistic', {
              url: '/online_Statistic',
              templateUrl: 'tpl/lms/online_Statistic.html',
              controller: 'online_Statistic as vm'
            })
            //历史数据管理
            .state('app.lms.historyData_Management', {
              url: '/historyData_Management',
              templateUrl: 'tpl/lms/historyData_Management.html',
              controller: 'historyData_Management as vm'
            })
            //数据添加
            .state('app.lms.addData_Management', {
              url: '/addData_Management',
              templateUrl: 'tpl/lms/addData_Management.html',
              controller: 'addData_Management as vm'
            })
            //数据查询
            .state('app.lms.historyData_filte', {
              url: '/historyData_filte',
              templateUrl: 'tpl/lms/historyData_filte.html',
              controller: 'historyData_filte as vm'
            })
            //测试
            .state('app.lms.webTest', {
              url: '/web_test',
              templateUrl: 'tpl/lms/web_test.html',
              controller: 'web_test as vm'
            })

        }
      ]
    );

})();
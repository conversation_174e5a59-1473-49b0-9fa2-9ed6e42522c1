(function () {
    app.controller("inAdvanceReviewProblemUpController", ['reviewProblemService','comService', '$rootScope', '$scope', '$stateParams', '$modal', 'inform', 'LocalCache', 'Trans', 'AgreeConstant', '$http', '$state',
        function (reviewProblemService,comService, $rootScope, $scope, $stateParams, $modal, inform, LocalCache, Trans, AgreeConstant, $http, $state) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.limit = AgreeConstant.limitList;
            //初始化问题级别下拉框
            $scope.problemLevel = problemLevelConfig;
            $scope.problemTypeArray = problemTypeConfig[problemProcessConfig[$stateParams.reviewTheme]];

            $scope.acceptLevel = [{
                label:"是",
                value:"0"
            },{
                label:"否",
                value:"1"
            }];
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
             /**
             *问题分类变动修改问题级别
             */
            $scope.changeProblemType = function () {
                $scope.problem.level = problemTypeLevelConfig[$scope.problem.problemType];
            };
            //页面初始化，初始化评委下拉列表、责任人下拉列表（系研所有人员）,将传入的JSON串转化为要绑定到H5页面的变量
            $scope.initParams = function () {
                $scope.employeeList = [];
                comService.getEmployeesName().then(function (data) {
                    $scope.employeeList = angular.fromJson(data.data);
                    //获取从评审会议问题页面传入的问题的JSon串,若不存在即为新增
                    $scope.problem = JSON.parse($stateParams.problemJson);
                    //设置要显示评审内容
                    $scope.reviewContent = $stateParams.reviewContent;
                    //设置要显示评审主题
                    $scope.reviewTheme = $stateParams.reviewTheme;
                });
            };

            //更新评审问题信息
            $scope.updateProblem = function () {
                var urlData = {
                    'id': $scope.problem.id,//评审问题id
                    'reviewId': $stateParams.reviewId,//评审会议id
                    'judge': $scope.problem.judge,//提出问题的评委姓名
                    'problem': $scope.problem.problem,//问题/事项
                    'problemType': $scope.problem.problemType,//问题分类
                    'level': $scope.problem.level,//问题级别
                    'solution': $scope.problem.solution,//解决方案/改进活动
                    'liable': $scope.problem.liable,//责任人
                    'deadLine': inform.format($scope.problem.deadLine,'yyyy/MM/dd'),//时间要求
                    'execution': $scope.problem.execution,//完成情况
                    'reviewAccept': $scope.problem.reviewAccept,//是否采纳
                    'reviewAcceptOpinion': $scope.problem.reviewAcceptOpinion//项目组意见
                };
                reviewProblemService.updateProblem(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            //含确定按钮的弹框,点击确定跳转至评审问题列表页
                            layer.confirm(data.message, {
                                title: false,
                                btn: ['确定']
                            }, function (result) {
                                layer.close(result);
                                $scope.back();
                            });

                        } else {
                            inform.common(data.message);
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });


            };

            //返回评审问题列表页面
            $scope.back = function () {
                $state.go('app.office.inAdvanceReviewProblem', {
                    id: $stateParams.reviewId,
                    type: $stateParams.reviewType,
                    reviewContent:$stateParams.reviewContent,
                    reviewTheme:$stateParams.reviewTheme
                });
            };

            /**
             * 时间要求
             */
            $scope.deadLineOpen = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.deadLine = true;
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法初始化调用部分                           开始
             * *************************************************************
             */
            //初始化页面
            $scope.initParams();
            /**
             * *************************************************************
             *              方法初始化调用部分                           结束
             * *************************************************************
             */
        }]);
})();
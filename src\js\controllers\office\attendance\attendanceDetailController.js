(function () {
  app.controller('attendanceDetailController', [
    'attendanceDetailService',
    'comService',
    '$rootScope',
    '$scope',
    '$stateParams',
    '$modal',
    'inform',
    'LocalCache',
    'Trans',
    'AgreeConstant',
    '$http',
    '$state',
    'proProjectModule',
    function (
      attendanceDetailService,
      comService,
      $rootScope,
      $scope,
      $stateParams,
      $modal,
      inform,
      LocalCache,
      Trans,
      AgreeConstant,
      $http,
      $state,
      proProjectModule
    ) {
      /**
       * *************************************************************
       *             初始化部分                                 开始
       * *************************************************************
       */
      //查询条件
      $scope.formInput = {};
      //一进来，默认选中并显示考勤明显
      $scope.type = '1';
      //状态
      $scope.status = '';
      //是否工作日
      $scope.holidayWorkFlag = '0';
      //非工作日延时打卡
      $scope.holidayWorkOverTime = 0.0;
      //工作日延时打卡
      $scope.noHolidayWorkOverTime = 0.0;
      // 排序
      $scope.sortKey = '';
      $scope.desc = true;
      // 排序
      $scope.title = '';
      $scope.order = order;
      function order(str) {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      }

      // 汇总信息默认展开
      $scope.isInfoCardClose = false;
      // 汇总信息
      $scope.summaryData = [];
      // 汇总信息展开/收起
      $scope.toggleInfoCard = function () {
        $scope.isInfoCardClose = !$scope.isInfoCardClose;
      };
      // 默认不显示缺卡次数总计
      $scope.showAbsentPunchInfo = false;
      //设置列表的高度
      setDivHeight();
      //初始化根据用户名获取一级部门列表
      initPrimaryDeptList();
      //窗体大小变化时重新计算高度
      $(window).resize(setDivHeight);
      $scope.pages = {
        pageNum: '', // 分页页数
        size: '', // 分页每页大小
        total: '', // 数据总数
      };
      // 初始化分页数据
      $scope.pages = inform.initPages();
      //设置员工地区的访问权限
      $scope.areaCodeFlag = true;
      var paramObj = {
        primaryDeptId: '#primaryDeptName',
        primaryDeptScopeModel: 'primaryDeptCode',
        primaryDeptList: 'primaryDeptList',
        subDeptList: 'departmentList',
        subDeptScopeModel: 'departmentCode',
      };
      //权限控制
      comService.checkAuthentication($scope, paramObj, departmentCallBack, LocalCache.getSession('loginName'));
      /**
       * *************************************************************
       *              初始化部分                                 结束
       * *************************************************************
       */

      /**
       * *************************************************************
       *              方法声明部分                                 开始
       * *************************************************************
       */

      /**
       * 设置列表的高度
       */
      function setDivHeight() {
        //网页可见区域高度
        var clientHeight = document.body.clientHeight;
        var divHeight = clientHeight - (150 + 185);
        $('#divTBDis').height(divHeight);
        $('#subDivTBDis').height(divHeight - 65);
        $('#subDivTBDis0').height(divHeight - 65);
      }

      /**
       * 初始化根据用户名获取一级部门列表
       */
      function initPrimaryDeptList() {
        $scope.primaryDeptList = [];
        comService.getOrgChildren('0002').then(function (data) {
          if (data.data) {
            $scope.primaryDeptList = data.data;
          }
        });
        //查询区域
        $scope.areaList = [];
        comService.getParamList('AREA_TYPE', 'AREA_TYPE').then(function (data) {
          $scope.areaList = data.data;
          $scope.areaMap = {};
          for (var j = 0; j < data.data.length; j++) {
            $scope.areaMap[data.data[j].param_code] = data.data[j].param_value;
          }
          //获取日期
          $scope.initDate();
        });
      }

      //修改一级部门，二级部门进行联动
      $scope.changeDept = function () {
        //获取二级部门
        $scope.deptList = [];
        comService.getOrgChildren($scope.primaryDeptCode).then(function (data) {
          if (data.code === AgreeConstant.code) {
            $scope.departmentList = data.data;
          }
        });
      };
      //初始化日期，开始时间为上个月1号，结束时间为今日
      $scope.initDate = function () {
        var date = new Date();
        $scope.formInput.endTime = inform.format(date, 'yyyy-MM-dd');
        //设置为1号，防止31号时获取到当月
        date.setDate(1);
        date.setMonth(date.getMonth() - 1);
        $scope.formInput.startTime = inform.format(date, 'yyyy-MM-dd');
      };
      //重置
      $scope.reset = function () {
        var areaCode = $scope.formInput.region;
        $scope.formInput = {};
        //区域权限
        if ($scope.areaCodeFlag) {
          $scope.formInput.region = areaCode;
        }
        $scope.initDate();
      };
      /**
       *部门控件的回调处理
       **/
      function departmentCallBack(result) {
        if (result.code === '00') {
          $state.go('app.office.unAuthority');
          return;
        }
        if (result.code === '03') {
          //地区权限控制
          $scope.areaCodeFlag = true;
          $('#region').attr('disabled', true);
          $scope.formInput.region = result.areaCode;
        }
        // 一级部门默认查询软件开发部
        $scope.primaryDeptCode = 'D010053';
        //
        $scope.changeDept();
        $scope.getData(1);
      }

      //以分页的形式获取所有数据
      $scope.getData = function (pageNum) {
        if (!$scope.formInput.startTime || !$scope.formInput.endTime) {
          inform.common('请输入统计起止时间');
          return;
        }
        var urlData = {
          startDate: inform.format($scope.formInput.startTime, 'yyyy-MM-dd'), //起始时间
          endDate: inform.format($scope.formInput.endTime, 'yyyy-MM-dd'), //结束时间
          departmentId: $scope.departmentCode, //二级部门
          primaryDeptId: $scope.primaryDeptCode, //一级部门
          status: $scope.status, //状态
          holidayWorkFlag: $scope.holidayWorkFlag, //是否工作日
          employeeName: $scope.formInput.realName, //员工姓名
          regionName: $scope.areaMap[$scope.formInput.region], //员工地域
          page: pageNum, // 分页页数
          size: $scope.pages.size, // 分页每页大小
        };
        if ($scope.type === '1') {
          //获取考勤明细
          getAttendanceDetail(urlData);
        }
        if ($scope.type === '2') {
          //获取延时打卡个人汇总
          getWorkOverTimeGroupByPerson(urlData);
        }
      };
      //获取考勤明细
      function getAttendanceDetail(urlData) {
        attendanceDetailService.getAttendanceDetail(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              var jsonData = data.data;
              $scope.attendanceDetailList = jsonData.list;
              if (jsonData.list.length === 0) {
                inform.common('无符合条件的考勤明细信息');
                $scope.pages = inform.initPages(); //初始化分页数据
              } else {
                //获取延时打卡汇总数据
                getGatherWorkOverTime(urlData);
                // 分页信息设置
                $scope.pages.total = jsonData.total; // 页面总数
                $scope.pages.star = jsonData.startRow; //页面起始数
                $scope.pages.end = jsonData.endRow; //页面大小数
                $scope.pages.pageNum = jsonData.pageNum; //页面页数
              }
            } else {
              inform.common(data.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }

      function getGatherWorkOverTime(urlData) {
        attendanceDetailService.getGatherWorkOverTime(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              const {
                firstHolidayOverHoursAvg,
                firstNoHolidayOverHoursAvg,
                twoHolidayOverHoursAvg,
                twoNoHolidayOverHoursAvg,
                firstMissCardAvg,
                twoMissCardAvg,
                missCardTimesByPerson,
                holidayWorkOverHours,
                noHolidayWorkOverHours,
              } = data.data;
              let summaryType = '';
              if (urlData.primaryDeptId) {
                summaryType = '(一级部门)';
              }
              if (urlData.departmentId) {
                summaryType = '(二级部门)';
              }
              const list = [
                {
                  label: '一级部门',
                  workDayTitle: '工作日平均延时',
                  nonWorkDayTitle: '非工作日平均延时',
                  absentPunchCountTitle: '平均缺卡次数',
                  workday: `${firstNoHolidayOverHoursAvg || 0}h`,
                  nonWorkday: `${firstHolidayOverHoursAvg || 0}h`,
                  absentPunchCount: `${firstMissCardAvg || 0}次`,
                },
                {
                  label: '二级部门',
                  workDayTitle: '工作日平均延时',
                  nonWorkDayTitle: '非工作日平均延时',
                  absentPunchCountTitle: '平均缺卡次数',
                  workday: `${twoNoHolidayOverHoursAvg || 0}h`,
                  nonWorkday: `${twoHolidayOverHoursAvg || 0}h`,
                  absentPunchCount: `${twoMissCardAvg || 0}次`,
                },
                {
                  label: '总计',
                  workDayTitle: '工作日延时',
                  nonWorkDayTitle: '非工作日延时',
                  absentPunchCountTitle: '缺卡次数',
                  workday: `${noHolidayWorkOverHours || 0}h`,
                  nonWorkday: `${holidayWorkOverHours || 0}h`,
                  absentPunchCount: `${missCardTimesByPerson || 0}次`,
                },
              ];
              // 只有查询具体人员时才会显示缺卡总计
              if (urlData.employeeName) {
                $scope.showAbsentPunchInfo = true;
              } else {
                $scope.showAbsentPunchInfo = false;
              }
              // 处理不同选择项对应展示的值
              const res = [];
              urlData.primaryDeptId && res.push(list[0]);
              urlData.departmentId && res.push(list[1]);
              res.push(list[2]);
              $scope.summaryData = res;
            } else {
              inform.common(data.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }

      //获取延时打卡个人汇总
      function getWorkOverTimeGroupByPerson(urlData) {
        //默认排序
        urlData.orderBySql = 'workOverDays desc';
        if ($scope.sortKey != '') {
          if ($scope.desc) {
            urlData.orderBySql = $scope.sortKey + ' desc';
          } else {
            urlData.orderBySql = $scope.sortKey + ' asc';
          }
        }
        attendanceDetailService.getWorkOverTimeGroupByPerson(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              var jsonData = data.data;
              $scope.workOverTimeGroupByPersonList = jsonData.list;
              if (jsonData.list.length === 0) {
                inform.common('无符合条件的考勤汇总信息');
                $scope.pages = inform.initPages(); //初始化分页数据
              } else {
                //获取延时打卡汇总数据
                getGatherWorkOverTime(urlData);
                // 分页信息设置
                $scope.pages.total = jsonData.total; // 页面总数
                $scope.pages.star = jsonData.startRow; //页面起始数
                $scope.pages.end = jsonData.endRow; //页面大小数
                $scope.pages.pageNum = jsonData.pageNum; //页面页数
              }
            } else {
              inform.common(data.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }

//      $scope.order = function (str) {
//        $scope.sortKey = str;
//        $scope.desc = !$scope.desc;
//        $scope.getData();
//      };
      /**
       *下载考勤明细
       */
      $scope.toExcel = function () {
        inform.modalInstance('确定要下载考勤明细信息表吗？').result.then(function () {
          var urlData = {
            startDate: inform.format($scope.formInput.startTime, 'yyyy-MM-dd'), //起始时间
            endDate: inform.format($scope.formInput.endTime, 'yyyy-MM-dd'), //结束时间
            status: $scope.status, //状态
            departmentId: $scope.departmentCode, //二级部门
            primaryDeptId: $scope.primaryDeptCode, //一级部门
            employeeName: $scope.formInput.realName, //员工姓名
            regionName: $scope.areaMap[$scope.formInput.region], //员工地域
          };
          inform.downLoadFile(
            'attendanceDetail/toExcel',
            urlData,
            '考勤明细信息表' + inform.format(new Date(), 'yyyy-MM-dd') + '.xlsx'
          );
        });
      };
    },

    /**
     * *************************************************************
     *              方法声明部分                                 结束
     * *************************************************************
     */
  ]);
})();

/*
* @Author: fubaole
* @Date:   2018-01-17 15:54:30
* @Last Modified by:   fubaole
* @Last Modified time: 2018-02-24 15:00:13
*/

(function() {
  'use strict';
  app.controller("company_Look", ['$scope','$stateParams', 'inform', 'SystemService','Trans','AgreeConstant',
    function($scope, $stateParams,inform, SystemService,Trans,AgreeConstant) {

      $scope.getData = getData;
      $scope.companyTypes = [];
      getCompanyType();
      getData();

      // 获取公司类型
      function getCompanyType() {
        SystemService.getDictValueListByDictTypeCode("company_type")
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.companyTypeData =data.result;
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 获取列表信息
      function getData() {
        SystemService.getCompanyById($stateParams.companyId)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.resultData = data.result;
              angular.forEach($scope.companyTypeData, function(i) {
                angular.forEach( data.result.companyTypes, function(res) {
                  if (i.valueCode===res.companyType) {
                    $scope.companyTypes.push(i.valueName);
                  }
                });
              });
              $scope.companyTypes = $scope.companyTypes.join();
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

    }
  ]);
})();
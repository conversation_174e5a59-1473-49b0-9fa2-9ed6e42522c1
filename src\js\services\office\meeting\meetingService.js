(function () {
    'use strict';
    app.factory('meetingService', meetingService);
    meetingService.$inject = ['HttpService', '$rootScope'];

    function meetingService(HttpService, $rootScope) {
        var service = {
            getMeetingInfo: getMeetingInfo,
            getMeetingInfoGroup: getMeetingInfoGroup,
            deleteById: deleteById,
            addMeetingInfo: addMeetingInfo,
            updateMeetingInfo: updateMeetingInfo,
            getMeeting: getMeeting,
            getMeetingRoom: getMeetingRoom,
            getMeetingInfoByTime: getMeetingInfoByTime,
            delMeeting: delMeeting,
            cancelMeeting: cancelMeeting,
            cancelMeetingMidway: cancelMeetingMidway,
        };
        return service;
        /**
         * 删除会议室
         * @param  参数
         */
        function delMeeting(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'meetingOA/delMeeting', urlData);
        }
        /**
         * 获取会议室使用信息
         * @param  参数
         */
        function getMeetingInfoByTime(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'meetingOA/querymeetinghistory', urlData);
        }
        /**
         * 查询某个会议信息
         * @param  参数
         */
        function getMeeting(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'meeting/getMeeting', urlData);
        }

        /**
         * 获取会议室
         */
        function getMeetingRoom(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'meetingOA/querymeetingroominfo', urlData);
        }
        /**
         * 查询会议信息
         * @param  参数
         */
        function getMeetingInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'meeting/getMeetingInfo', urlData);
        }

        /**
         * 分组查询会议信息
         * @param  参数
         */
        function getMeetingInfoGroup(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'meeting/getMeetingInfoGroup', urlData);
        }

        /**
         * 删除会议信息
         * @param id
         */
        function deleteById(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'meeting/deleteById', urlData);
        }

        /**
         * 新增会议信息
         * @param 参数
         */
        function addMeetingInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'meeting/createMeeting', urlData);
        }

        /**
         * 修改会议信息
         * @param 参数
         */
        function updateMeetingInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'meeting/updateMeetingInfo', urlData);
        }
        /**
         * 会议未开始取消会议
         * @param 参数
         */
        function cancelMeeting(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'meetingOA/cancelmeeting', urlData);
        }
        /**
         * 会议中途取消(会议已开始但是提前结束)
         * @param 参数
         */
        function cancelMeetingMidway(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'meetingOA/overmeeting', urlData);
        }
    }
})();

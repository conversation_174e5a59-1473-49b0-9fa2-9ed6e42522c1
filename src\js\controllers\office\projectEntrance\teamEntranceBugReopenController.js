//# sourceURL=js/controllers/office/projectEntrance/teamEntranceBugReopenController.js
(function () {
    app.controller("teamEntranceBugReopenController", [ '$scope','$stateParams','$state','teamEntranceDetailsService','AgreeConstant',
        function ($scope,$stateParams,$state,teamEntranceDetailsService,AgreeConstant) {

            $scope.formRefer = {};
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.getData = getData;
            getData();

            //设置列表的高度
            function setDivHeight(){
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - 350;
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight-400);
            }

            function getData(){
                var urlData = {
                    'projectName':$scope.projectInfoParam.cname,
                    'ztProjectId':$scope.formRefer.sprintVersion
                }
                $scope.reopenBugDetail;
                $scope.reopenBugDetailList = [];
                teamEntranceDetailsService.getReopenBugDetail(urlData).then(function(data) {
                	if (data.code===AgreeConstant.code) {
                	    $scope.reopenBugDetail = data.data;
                        $scope.reopenBugDetailList = data.data.bugs;
                	}
                });
            }

            $scope.rest =function(){
                $scope.formRefer = {};
            }
      }]);
})();
(function () {
    app.controller("knowledgeMessage", ['comService', '$rootScope', '$scope', 'knowledgeService', 'inform', 'Trans', 'AgreeConstant','LocalCache','$state',
        function (comService, $rootScope, $scope, knowledgeService, inform, Trans, AgreeConstant, LocalCache, $state) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //查询条件
            $scope.formRefer = {};
            //是否从我的星星跳转标志
            $scope.formRefer.flag = '0';
            //获取缓存
            $scope.formRefer = LocalCache.getObject('knowledgeMessage_formRefer');
            if($scope.formRefer.flag === '1'){
                $scope.formRefer.startTime = $scope.formRefer.startDate;
                $scope.formRefer.endTime = $scope.formRefer.endDate;
                $scope.formRefer.author = $scope.formRefer.employeeName;
                console.log($scope.formRefer);
            }
            //清除缓存
            LocalCache.setObject('knowledgeMessage_formRefer', {});
            //知识库信息
            $scope.tableData = [];
            //分页
            $scope.pages = inform.initPages();
            $scope.pages.size = "20";
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            // 设置滚动条高度
            $scope.formRefer.subDivTBDisScrollTop = $('#subDivTBDis').scrollTop();
            initInfo();
            $scope.getData = getData;
            // 设置二级部门
            $scope.getTwoDepartment = getTwoDepartment;
            if (null != $scope.formRefer.oneDepartment && $scope.formRefer.oneDepartment !== '') {
                getTwoDepartment();
            }
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 160);
                $("#divTBDis").height(divHeight);
                setTimeout(showDataTable, 500);
            }

            /**
             * 初始化
             */
            function initInfo() {
                if($scope.formRefer.flag !== '1'){
                    initTime();
                }
                //获取一级部门
                $scope.oneDepartmentList = [];
                comService.getOrgChildren('0002').then(function (data) {
                    if (data.data) {
                        $scope.oneDepartmentList = data.data;
                    }
                });
                //获取员工
                $scope.employeeList = [];
                comService.getEmployeesName().then(function (data) {
                    $scope.employeeList = angular.fromJson(data.data);
                });
                //审批状态
                $scope.statusList = [{value: "-1", name: "草稿"}, {value: "0", name: "禁用"}, {
                    value: "1",
                    name: "审核通过"
                }, {value: "2", name: "待审核"}];
                $scope.statusMap = {"-1": '草稿', "0": '禁用', "1": '审核通过', "2": "待审核"};
                getData();

            }

            function getTwoDepartment() {
                //获取二级部门
                $scope.departmentList = [];
                comService.getOrgChildren($scope.formRefer.oneDepartment).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.departmentList = data.data;
                    }
                });
            }

            function getData(pageNum) {
                $scope.dataTableShow = 0;
                var urlData = $scope.formRefer;
                urlData.startTime = inform.format($scope.formRefer.startTime, 'yyyy-MM-dd');
                urlData.endTime = inform.format($scope.formRefer.endTime, 'yyyy-MM-dd');
                //当前页数
                urlData.currentPage = pageNum;
                //每页显示条数
                urlData.pageSize = $scope.pages.size;
                knowledgeService.getMessage(urlData).then(function (data) {
                        $scope.dataTableShow = 1;
                        if (data.code === AgreeConstant.code) {
                            if (null == data.data) {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages();
                                $scope.pages.size = "20";
                            } else {
                                //知识库信息
                                $scope.tableData = data.data.list;
                                //分页信息设置
                                $scope.pages.total = data.data.total;
                                $scope.pages.star = data.data.startRow;
                                $scope.pages.end = data.data.endRow;
                                $scope.pages.pageNum = data.data.pageNum;
                            }
                            setTimeout(showDataTable, 300);
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            /**
             *调用DataTable组件冻结表头和左侧及右侧的列
             */
            function showDataTable() {
                $('#fixedLeftAndTop').DataTable({
                    //可被重新初始化
                    retrieve: true,
                    //自适应高度
                    scrollY: 'calc(100vh - 350px)',
                    scrollX: true,
                    scrollCollapse: true,
                    //控制每页显示
                    paging: false,
                    //冻结列（默认冻结左1）
                    fixedColumns: {
                        leftColumns: 0,
                        rightColumns: 0
                    },
                    //search框显示
                    searching: false,
                    //排序箭头
                    ordering: false,
                    //底部统计数据
                    info: false
                });

                // 获取到跟踪单信息之后滚动条跳至缓存中保存的scrollTop
                setTimeout(function () {
                    if ($scope.formRefer.subDivTBDisScrollTop) {
                        $('#fixedLeftAndTop').parent().animate({scrollTop: $scope.formRefer.subDivTBDisScrollTop}, 10);
                    }
                }, 300)
            }

            /**
             * excel下载
             */
            $scope.toExcel = function () {
                inform.modalInstance("确定要下载吗?").result.then(function () {
                    inform.downLoadFile('knowledge/toMessageExcel', $scope.formRefer, '知识库信息.xlsx');
                });
            }
            /**
             * 重置
             */
            $scope.reset = function () {
                $scope.formRefer = {};
            };
            /**
             * 开始时间
             */
            $scope.openDateStart = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = true;
                $scope.openedEnd = false;
            };

            /**
             * 结束时间
             */
            $scope.openDateEnd = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openedStart = false;
                $scope.openedEnd = true;
            };

            /**
             * 初始化检索条件开始时间
             */
            function initTime() {
                var date = new Date();
                //开始日期向前推3个月
                date.setMonth(date.getMonth() - 3);
                //对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
                $scope.formRefer.startTime = inform.format(date, 'yyyy-MM-dd');
            }

            //页面跳转到我的星星--我的星星明细部门贡献详情页
            $scope.getBack = function(){
                $state.go('app.personal_star_department_detail');
            }

            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */

        }]);
})();
/*
 * @Author: sun<PERSON><PERSON>a
 * @Date:   2019-05-23 17:50:05
 * @Last Modified by:   sunqixian
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
    'use strict';
  app.factory('processDataService', processDataService);
  processDataService.$inject=["HttpService",'$rootScope'];

  function processDataService(HttpService,$rootScope){
    
	var service={
			
			getAllMessage:getAllMessage,
			addMessage:addMessage,
			updateMessage:updateMessage
			
	};
    return service;
    

    
    /**
     * 获取产品线下所有的信息
     */
    function getAllMessage(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'processconformity/getAllMessage', urlData);
    }
    
    /**
     * 添加信息
     */
    function addMessage(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'processconformity/addMessage', urlData);
    }
    
    
    /**
     * 修改信息
     */
    function updateMessage(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'processconformity/updateMessage', urlData);
    }
  

  }
})();

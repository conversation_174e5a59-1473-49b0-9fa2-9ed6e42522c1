/*
 * @Author: fubaole
 * @Date:   2017-11-17 14:51:24
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-22 17:53:01
 */

(function() {
  'use strict';
  app.controller("blocks_Add", ['$rootScope', '$scope', '$state', '$stateParams', 'inform', 'SystemService','Trans','AgreeConstant',
    function($rootScope, $scope, $state, $stateParams, inform, SystemService,Trans,AgreeConstant) {
      $scope.limitList = AgreeConstant.limitList; // 正则校验配置
      $scope.blocks = {};
      $scope.widgetCategory =[];
      $scope.permissionType =[];
      $scope.widgetWidth =[];
      $scope.selectedRole =[];
      $scope.getRoleList = getRoleList;// 获取角色信息
      $scope.updataType = updataType;// 获取选中角色信息
      $scope.onSubmit = onSubmit;// 提交

      getDropDownData("widgetCategory"); // 获取所属模块数据
      getDropDownData("permissionType"); // 获取权限分类数据
      getDropDownData("widgetWidth"); // 获取页面宽度数据

      // 获取字典数据
      function getDropDownData(str) {
        SystemService.getDictValueListByDictTypeCode(str)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              if (str==="widgetCategory") {
                $scope.widgetCategory = data.result;
              }
              if (str==="permissionType") {
                $scope.permissionType = data.result;//这里获取出了角色和通用
                angular.forEach($scope.permissionType, function(i){
                  if (i.valueCode==='2') {
                    getRoleList(i.valueCode, true);
                  }
                });
              }
              if (str==="widgetWidth") {
                $scope.widgetWidth = data.result;
              }
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 获取角色权限 列表
      function getRoleList(str, check) {
        $scope.showRole = false;
        if(str =='2'){
          $scope.showRole = true;
          SystemService.getRoleByLoginUserIdMap()
            .then(function(data){
              if(data.code==AgreeConstant.resultCode){
                $scope.roleList = data.result;
                if (check) {
                  if (!$scope.roleList || !$scope.roleList.length) {
                  //没有角色信息
                  $scope.showRole = false;
                  $scope.permissions = $scope.permissionType;
                  angular.forEach($scope.permissions, function(i){
                    if (i.valueCode==='2') {
                      var index = $scope.permissions.indexOf(i);
                      if (index !== -1) {
                        $scope.permissionType.splice(index, 1);
                      }
                    }
                  });
                }
                  $scope.showRole = false;
                }
              }else{
                inform.common(data.message);
              }
            }, function() {
              inform.common(Trans("tip.requestError"));
          });
        }
      }

      // 获取选中角色信息
      function updataType() {
         angular.forEach($scope.roleList, function(i) {
            var index = $scope.selectedRole.indexOf(i.roleId);
            if (i.selected && index === -1) {
                $scope.selectedRole.push(i.roleId);
            } else if (!i.selected && index !== -1) {
                $scope.selectedRole.splice(index, 1);
            }
        });
         console.log($scope.selectedRole);
         $scope.blocks.roleIds = $scope.selectedRole;
      }

      function onSubmit() {

        if ($scope.blocks.permissionType==='2' && (!$scope.blocks.roleIds || !$scope.blocks.roleIds.length)) {
          inform.common(Trans("role.chooseRoleNeeded"));
          return;
        }
        SystemService.saveOrUpdateBlockContent($scope.blocks)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              inform.common(Trans("tip.saveSuccess"));
              $state.go("app.system.blocks_Management");
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }
    }
  ]);
})();
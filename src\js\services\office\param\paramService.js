(function() {
    'use strict';
  app.factory('paramService', paramService);
  paramService.$inject=["HttpService",'$rootScope'];

  function paramService(HttpService,$rootScope){
    var service={
      selectByValueOrDesc:selectByValueOrDesc,
      queryParam:queryParam,
      updateByParam:updateByParam,
      addParam:addParam,
      addParamList:addParamList,
      addParamByObj:addParamByObj,
      updateParamSort:updateParamSort,
      updateParamValueAndCode:updateParamValueAndCode,
      updateBatchByParam:updateBatchByParam,
      deleteParam:deleteParam

    };
    return service;

    /**
     * 获取所有的信息以分页的形式
     *
    */
	function selectByValueOrDesc(urlData) {
		return HttpService.post($rootScope.getWaySystemApi+'param/selectByValueOrDesc',urlData);
	}

    /**
     * 根据字典名称、类型编码查询(修改页面)
     *
    */
    function queryParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi+'param/queryParam',urlData);
    }

    /**
     * 更新字典信息
     *
    */
    function updateByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi+'param/updateByParam',urlData);
    }

    /**
     * 批量更新状态
     *
    */
    function updateBatchByParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi+'param/updateBatchByParam',urlData);
    }
    /**
     *新增数据字典
     *
    */
    function addParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi+'param/addParam',urlData);
    }
    /**
     *批量新增数据字典
     *
    */
    function addParamList(urlData) {
        return HttpService.post($rootScope.getWaySystemApi+'param/addParamList',urlData);
    }
    /**
     *新增数据字典参数（修改页面）
     *
    */
    function addParamByObj(urlData) {
        return HttpService.post($rootScope.getWaySystemApi+'param/addParamByObj',urlData);
    }

    /**
     *更新明细排列顺序
     *
    */
    function updateParamSort(urlData) {
        return HttpService.post($rootScope.getWaySystemApi+'param/updateParamSort',urlData);
    }
    /**
     *更新code和value
     *
    */
    function updateParamValueAndCode(urlData) {
        return HttpService.post($rootScope.getWaySystemApi+'param/updateParamValueAndCode',urlData);
    }
    /**
     *  删除
     *
    */
    function deleteParam(urlData) {
        return HttpService.post($rootScope.getWaySystemApi+'param/deleteParam',urlData);
    }
  }
})();
(function() {
    'use strict';
    app
        .factory('AddDataService', addDataService);
    addDataService.$inject = ['HttpService', '$rootScope'];

    function addDataService(HttpService, $rootScope) {
        var service = {
            getCookie: getCookie,
            addData: addData
        };
        return service;

        function getCookie(name) {
            var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");

            if (arr = document.cookie.match(reg))

                return unescape(arr[2]);
            else
                return null;
        }

        function addData(formData, grantPerson) {
            // $.ajax({
            //                  url: 'http://localhost:7788/lms/addData/addRegisterManagementData?name=' + getCookie('name'),
            //                  type: 'POST',
            //                  data: formData,
            //                  cache: false,
            //                  contentType: false,
            //                  processData: false,
            //                  success: function(data) {
            //                      return data;
            //                  }
            //              });
            return HttpService.post($rootScope.gateInfoApi + 'lms/addData/addRegisterManagementData?grantPerson=' + grantPerson + '&name=' + getCookie('name'),{'file':formData});
        }
    }
})();
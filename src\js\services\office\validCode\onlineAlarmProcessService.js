(function () {
    'use strict';
    app.factory('onlineAlarmProcessService', onlineAlarmProcessService);
    onlineAlarmProcessService.$inject = ["HttpService", '$rootScope'];

    function onlineAlarmProcessService(HttpService, $rootScope) {
        var service = {
            getOnlineAlarmProcess:getOnlineAlarmProcess
        };
        return service;
        /**
         * 获取线上监控告警流程
         */
        function getOnlineAlarmProcess(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'onlineAlarmProcess/getOnlineAlarmProcess', urlData);
        }

       
    }
})();
/*
 * @Author: fubaole
 * @Date:   2017-09-25 11:08:48
 * @Last Modified by:   haoh<PERSON><PERSON>
 * @Last Modified time: 2018-02-28 09:34:35
 */

(function() {
    'use strict';
    app.controller("news_template", ['$rootScope', '$scope', '$timeout', '$stateParams', '$modal', '$log', 'LocalCache', 'Trans', 'inform', 'MessageService', 'AgreeConstant',
        function($rootScope, $scope, $timeout, $stateParams, $modal, $log, LocalCache, Trans, inform, MessageService, AgreeConstant) {
            var interfaceMap = {};

            // 排序
            $scope.title = 'messageTemplateId';
            $scope.desc = true;
            $scope.order = order;

            $scope.map = {}; // 条件
            $scope.orderStr = "message_template_id desc"; //条件
            $scope.getData = getData; // 初始化函数
            $scope.pages = inform.initPages(); // 初始化分页数据
            $scope.getData($scope.pages.pageNum); // 初始化请求数据
            $scope.searchData = searchData; // 查询
            $scope.reset = reset; // 重置按钮
            $scope.advQuery = advQuery; //高级查询功能按钮
            $scope.checked = []; // 存放选中的Id
            $scope.selectAll = selectAll; // 全选
            $scope.selectOne = selectOne; // 单选
            $scope.editTemp = editTemp; // 编辑模板
            $scope.open = open; // 根据消息模板Id获取信息

            // 获取模板类型值类型
            getTemplateType();

            // 获取消息类型值
            getMessageType();

            // 排序
            function order(str) {
                $scope.title = str;
                $scope.desc = !$scope.desc;
            }

            // 查询
            function searchData(num) {
                interfaceMap = angular.copy($scope.map);
                getData(AgreeConstant.pageNum);
            }

            // 获取表格数据
            function getData(num) {
                if (!num) { inform.common(Trans('tip.pageNumTip')); return; }
                MessageService.getMessageTemplateByMap(JSON.stringify(interfaceMap), num, $scope.pages.size, $scope.orderStr)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.pages.goNum = null;
                            var jsonData = data.result;
                            $scope.resultData = jsonData.list;
                            if ($scope.resultData.length===0) {
                                inform.common(Trans('tip.noData'));
                                $scope.pages = inform.initPages();
                            } else {
                                $scope.pages.total = jsonData.total;
                                $scope.pages.star = (jsonData.pageNum - 1) * jsonData.pageSize + 1;
                                $scope.pages.end = jsonData.pageNum * jsonData.pageSize;
                                $scope.pages.pageNum = jsonData.pageNum;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans('tip.requestError'));
                    });
            }

            // 按条件查询操作部分

            // 重置按钮
            function reset() {
                $scope.map.messageTemplateTitle = "";
                $scope.map.messageTemplateType = "";
                $scope.map.messageSendType = "";
            }

            //高级查询功能按钮
            function advQuery() {
                $scope.isOpen = !$scope.isOpen;
                $scope.map.messageSendType = "";
            }

            // 获取模板类型值类型
            function getTemplateType() {
                MessageService.getDictValueListByDictTypeCode("widgetCategory")
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.templateTypeCode = data.result;
                            console.log(data.result);
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 获取消息类型值
            function getMessageType() {
                MessageService.getDictValueListByDictTypeCode("alarm_notice_method")
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.messageTypeCode = data.result;
                            console.log(data.result);
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 选择所有的
            function selectAll() {
                if ($scope.select_all) {
                    $scope.checked = [];
                    angular.forEach($scope.resultData, function(i) {
                        i.checked = true;
                        $scope.checked.push(i.id);
                    });
                } else {
                    angular.forEach($scope.resultData, function(i) {
                        i.checked = false;
                    });
                    $scope.checked = [];
                }
            }

            // 选择一个
            function selectOne() {
                angular.forEach($scope.resultData, function(i) {
                    var index = $scope.checked.indexOf(i.id);
                    if (index === -1 && i.checked) {
                        $scope.checked.push(i.id);
                    } else if (index !== -1 && !i.checked) {
                        $scope.checked.splice(index, 1);
                    }
                });
                if ($scope.resultData.length === $scope.checked.length) {
                    $scope.select_all = true;
                } else {
                    $scope.select_all = false;
                }
            }

            // 编辑模板
            function editTemp(item) {
                LocalCache.setObject("newTpl", item);
            }

            // 根据消息模板Id获取信息
            function open(item) {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function() {
                            return Trans('common.deleteTip');
                        }
                    }
                });
                modalInstance.result.then(function() {
                    if (item) {
                        MessageService.removeMessageTemplate(item.messageTemplateId)
                            .then(function(data) {
                                if (data.code===AgreeConstant.resultCode) {
                                    interfaceMap = {};
                                    $scope.map = {};
                                    getData(1);
                                    inform.common(Trans("tip.delSuccess"));
                                } else {
                                    inform.common(data.message);
                                }
                            }, function() {
                                inform.common(Trans("tip.requestError"));
                            });
                    }
                });
            }


        }
    ]);
})();
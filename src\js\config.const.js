/*
 * @Author: fubaole
 * @Date:   2018-03-19 09:32:55
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-29 17:59:20
 */
(function () {
  'use strict';
  angular
    .module('app')
    // 常量约定
    .constant('AgreeConstant', {
      login: {
        // 登录页配置
        loginTitle: '基础业务平台',
        loginLogo: '../../img/basic/logo.png',
        title: '基础业务平台',
        headLogo: 'img/basic/logo.png',
        desc: {
          subTitle: '新北洋基础业务平台',
          txt: [
            '新北洋基础业务平台用于快速业务集成及技术集成',
            '平台提供个性化UI设计、通用的业务功能',
            '平台简单易上手，开发效率高',
          ],
        },
      },
      limitList: {
        // 输入框限制
        user: /^(?!-)(?!.*?-$)[a-zA-Z0-9_\u4e00-\u9fa5]{1,20}$/,
        userTxt: '请输入汉字、字母、数字和下划线，最大长度为20。',
        icon: /^[A-Za-z][A-Za-z\s]*[A-Za-z-]{1,40}$/,
        iconTxt: '请输入字母、中划线、空格',
        code: /^[0-9a-zA-Z-_]{1,40}$/,
        codeTxt: '请输入字母、数字、中划线和下划线，最大长度为40。',
        numberLimit: /^[0-9.]{1,10}$/, //数字
        numberLimitTxt: '输入内容只能为数字,且最长10位。',
        numberShortLimit: /^[0-9.]{1,5}$/, //数字
        numberShortLimitTxt: '输入内容只能为数字,且最长5位。',
        numberMiddleLimit: /^[0-9.]{1,7}$/, //数字
        numberMiddleLimitTxt: '输入内容只能为数字,且最长7位。',
        numberLongLimit: /^[0-9.]{1,8}$/, //数字
        numberLongLimitTxt: '输入内容只能为数字,且最长8位。',
        number: /^[0-9]{1,}$/, //数字
        numberTxt: '输入内容只能为数字。',
        point: /^\d+(\.\d+)?$/, //小数
        pointTxt: '请输入正确小数格式。',
        stringLong: /^[\s\S\d\D]*.{0,200}$/, //字符串
        stringLongWithStart: /^.{3,200}$/, //最小3位，最大200位的字符串
        string: /^.{0,50}$/, //字符串
        stringTxt: '最大长度50。',
        addr: /^(?!-)(?!.*?-$)[a-zA-Z0-9\u4e00-\u9fa5]{0,200}$/,
        addrTxt: '请输入汉字、字母和数字，最大长度为200。',
        // tel: /^((0\d{2,3}-\d{7,8})|(1[3|4|5|6|7|8|9][0-9]{9}))$/, //电话号
        tel: /^((0\d{2,3}\d{7,8})|(1[3|4|5|6|7|8|9][0-9]{9}))$/, //电话号
        telTxt: '输入内容格式不正确。',
        phone: /^1[3|4|5|6|7|8|9][0-9]{9}$/, //手机号
        phoneTxt: '输入内容格式不正确。',
        email: /^(?=.{0,45}$)(([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+)$/, //邮箱
        emailTxt: '输入内容格式不正确，最大长度45。',
        emailNewBY: /^(?=.{0,50}$)(([a-zA-Z0-9_\.\-])+\@newbeiyang\.com+)$/, //邮箱
        emailNewBYTxt: '输入内容格式不正确，最大长度50。',
        password: /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{6,50}$/,
        passwordTxt: '输入内容必须包含数字、英文字母、特殊符号且大于等于6位，最大长度50。',
        item: /^(?!-)(?!.*?-$)[a-zA-Z0-9_\u4e00-\u9fa5]{1,30}$/,
        itemTxt: '请输入汉字、字母、数字和下划线，最大长度为30。',
        itemLong: /^(?!-)(?!.*?-$)[a-zA-Z0-9_\u4e00-\u9fa5]{1,150}$/,
        itemLongTxt: '请输入汉字、字母、数字和下划线，最大长度为150。',
        description: /^(?!-)(?!.*?-$)[a-zA-Z0-9_\u4e00-\u9fa5]{1,100}$/,
        descriptionTxt: '请输入汉字、字母、数字和下划线，最大长度为100。',
        summary: /^(?!-)(?!.*?-$)[a-zA-Z0-9_\u4e00-\u9fa5]{1,500}$/,
        summaryTxt: '请输入汉字、字母、数字和下划线，最大长度为500。',
        name: /^[\u4e00-\u9fa5]{1,10}$/,
        nameTxt: '请输入汉字，最长支持10个汉字。',
        desc: /^[\s\S]{1,500}$/,
        descTxt: '最大长度为500。',
        baseLine: /^[\s\S]{1,100}$/,
        baseLineTxt: '最大长度为100。',
        plan: /^[\s\S]{1,1000}$/,
        planTxt: '最大长度为1000。',
        idCard: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
        idCardTxt: '身份证号最大支持18位',
        subprocedure: /^[\s\S]{1,200}$/,
        subprocedureTxt: '最大长度为200。',
        longString: /^[\s\S]{1,800}$/,
        longStringTxt: '最大长度为800。',
        chargePerson: /^[\s\S]{0,50}$/,
        chargePersonTxt: '最大长度为50。',
        versions: /^[\s\S]{0,20}$/,
        versionsTxt: '最大长度为20。',
        site: /^[\s\S]{0,30}$/,
        siteTxt: '最大长度为30。',
        numberMinLimit: /^[0-9]{1,3}$/, //数字
        numberMinLimitTxt: '输入内容只能为整数,且最长3位。',
        numberFiveLimit: /^[0-9]{1,5}$/, //数字
        numberFiveLimitTxt: '输入内容只能为整数,且最长5位。',
        numberEightLimit: /^[0-9]{1,5}$/, //数字
        numberEightLimitTxt: '输入内容只能为整数,且最长8位。',
        numberTenLimit: /^[0-9]{1,10}$/, //数字
        numberTenLimitTxt: '输入内容只能为整数,且最长10位。',
        planweight: /^(\d|[1-9]\d|100)$/,
        planweightTxt: '输入内容1-100只能为整数。',
        judgeGrade: /^[1-6]$/,
        judgeGradeTxt: '请输入1-6的整数',
        appearTime: /^[0-9]$/,
        appearTimeTxt: '请输入0-9的整数',
        evaluation: /^[0-5]$/,
        evaluationTxt: '请输入0-5的整数',
        workLoad: /^(?=(\d+([.]\d{1,2})?)$)[\d.]{0,5}$/,
        workLoadTxt: '请输入整数或小数,小数位数保留一到两位,整体不超过5位。',
        checkAmounts: /^[0-9]+([.][0-9]([0-9])?)?$/,
        checkAmountsTxt: '请输入整数或小数，小数位数保留一位或两位。',
        checkAmount: /^(?=(\d+([.]\d{1,2})?)$)[\d.]{0,10}$/,
        checkAmountTxt: '请输入整数或小数,小数位数保留一到两位,整体不超过10位。',
        employeeName: /^[0-9\u4e00-\u9fa5]{1,10}$/,
        employeeNameTxt: '请输入汉字、数字，最大长度为10。',
        numberLimit10: /^[0-9]{1,10}$/, //数字
        numberLimit10Txt: '输入内容只能为数字,最大长度为10',
        letter: /^[a-zA-Z_]{1,40}$/,
        letterTxt: '请输入字母和下划线，最大长度为40。',
        date: /^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/,
        dateTxt: '请输入正确日期格式：yyyy-MM-dd。',
      },
      superAdmin: 'super',
      treeRootNode: 0, // 根节点ID
      resultCode: '00', // 请求成功code
      code: '0000', // 请求成功code
      pageSize: '100', // 默认每页条数
      pageNum: 1, // 默认页码
      freezeStatus: 2, // 冻结状态码 1 正常  2 冻结
      unfreezeStatus: 1, // 冻结状态码 1 正常  2 冻结
      selfDepart: 10, // 本部门
      departAndChild: 20, // 本部门以及下属部门
      selfDirective: 30, // 自定义
      groupRange: 20, // 用户组自定义
      roleRange: 20, // 角色自定义
      permissionRange: 20, // 权限自定义
      forbiddenStatus: 1, // 人员启用禁用状态 1 启用
      unforbiddenStatus: 0, // 人员启用禁用状态 0 禁用
      setUsergroup: 1, //人员是否设置用户 1是  默认
      widthTwoOne: 'ot', // 宽度占屏1/2
      widthThreeOne: 'to', // 宽度占屏1/3
      widthTwoThree: 'tt', // 宽度占屏2/3
      widthFourOne: 'of', // 宽度占屏1/4
      widthThreeFour: 'tf', // 宽度占屏3/4
      widthOne: 'hp', // 宽度占屏1
      defaultAdd: '../../../img/basic/add.png', // 默认添加图片
      defaultImg: '../../../img/basic/bench.png', // 默认图片
      dicParentId: 0, // 默认父级字典Id
      editDicSave: 1, // 修改字典标志
      addDicSave: 0, // 新增字典标志
      fileSize: 2097152, // 文件大小2M
      mailFileSize: 10485760, //文件大小 10M
      productLineRelation: {
        '0022': 17005, //打印扫描
        '0021': 17002, //金融设备
        '0023': 17003, //零售平台
        '0013': 17004, //自动化
        '0014': 17006, //智能自助终端
        '0016': 17007, //软件技术
        '0018': 17008, //部门工作
        '0017': 17009, //通用
        '0024': 22488, //软件运营平台
      },
      proProductLineList: [
        {
          value: '打印扫描',
          code: 17005,
        },
        {
          value: '金融设备',
          code: 17002,
        },
        {
          value: '零售平台',
          code: 17003,
        },
        {
          value: '自动化',
          code: 17004,
        },
        {
          value: '智能自助终端',
          code: 17006,
        },
        {
          value: '软件技术',
          code: 17007,
        },
        {
          value: '部门工作',
          code: 17008,
        },
        {
          value: '通用',
          code: 17009,
        },
        {
          value: '软件运营平台',
          code: 22488,
        },
      ],
      projectStatusList: [
        {
          label: '待启动',
          value: '待启动',
        },
        {
          label: '进行中',
          value: '进行中',
        },
        {
          label: '暂停',
          value: '暂停',
        },
        {
          label: '结项',
          value: '结项',
        },
        {
          label: '终止',
          value: '终止',
        },
      ],
      teamStatusList: [
        {
          label: '进行中',
          value: '进行中',
        },
        {
          label: '暂停',
          value: '暂停',
        },
        {
          label: '关闭',
          value: '关闭',
        },
      ],
      //角色下拉框列表
      roleList: [
        {
          label: '工程师',
          value: '工程师',
        },
        {
          label: '组长',
          value: '组长',
        },
        {
          label: '项目经理',
          value: '项目经理',
        },
        {
          label: '流动项目经理',
          value: '流动项目经理',
        },
        {
          label: '副主任',
          value: '副主任',
        },
        {
          label: '主任',
          value: '主任',
        },
        {
          label: '副部长',
          value: '副部长',
        },
        {
          label: '部长',
          value: '部长',
        },
        {
          label: '部长助理',
          value: '部长助理',
        },
        {
          label: '架构师',
          value: '架构师',
        },
        {
          label: '产品经理',
          value: '产品经理',
        },
        {
          label: '验证经理',
          value: '验证经理',
        },
      ],
      //获取方式下拉框列表
      getTypeList: [
        {
          label: '初始',
          value: '初始',
        },
        {
          label: '入职',
          value: '入职',
        },
        {
          label: '调动',
          value: '调动',
        },
        {
          label: '职责调整',
          value: '职责调整',
        },
        {
          label: '晋升',
          value: '晋升',
        },
        {
          label: '红头文任命',
          value: '红头文任命',
        },
      ],
      departmentTypeMap: {
        D010053: 'XTJC',
        D101003: 'JSZX',
        D102000: 'YZZX',
        D103000: 'YZZX',
        D104003: 'JSZX',
        D105000: 'JSZX',
        D106000: 'JSZX',
        D107000: 'JSZX',
        D108004: 'JSZX',
        D109000: 'YZZX',
        D110005: 'JSZX',
        D111000: 'JSZX',
        D112003: 'JSZX',
        D113000: 'JSZX',
        D114000: 'JSZX',
        D115000: 'JSZX',
      },
      // "titleLevelList":['B1','B2','B3','B4','B5','B6','B7','B8','B9','B10'],
      titleLevelList: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10','11'],
      groupTypeList: ['红头文团队-软件', '红头文团队-软硬结合', '虚拟团队'],
      titleGetTypeList: ['转正', '试用期转正', '考察期转正', '晋升', '红头文任命', '角色述职', '图谱调整', '其他'],
      personnelTypeList: [
        '暂定',
        '低效员工',
        '辅助员工',
        '一般员工',
        '潜力员工',
        '中坚力量',
        '核心骨干',
        '技术专家',
        '管理人员',
      ],
      //文件上传支持的后缀类型
      extensions: 'png,jpg,gif,svg,bmp,jpeg,pdf,pptx,doc,docx,xlsx',
      //支持的文件后缀对应的标准Response.setContentType(MIME)响应内容类型
      mimeTypes:
        'image/png,image/jpeg,image/gif,image/svg+xml,image/bmp,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      workingHoursBoard: {
        perDay: 21.75,
        sortColorList: ['#ee1111', '#ff8811', '#ffbb11', '#ffee22', '#bbff22', '#00C0F0'],
        xyWorkingHoursLegendData: [
          '总工时',
          '部门工作工时占比',
          '管理类工时占比',
          '技术支持类工时占比',
          '返工工时占比',
        ],
        deptWorkingHoursLegendData: ['投入工作量', '管理类工时占比', '技术支持类工时占比', '返工工时占比'],
        engineerInputLegendData: ['工时投入', '管理类工时占比', '技术支持类工时占比', '返工工时占比'],
      },
      departmentBoard: {
        teamScheduleLegendData: ['冲刺总次数', '冲刺成功次数', '冲刺失败次数', '待评价冲刺次数', '进行中冲刺次数', '冲刺成功率'],
        onlineProblemLegendData: ['线上问题数量', '缺陷逃逸'],
        reviewOutputLegendData: ['开发类', '测试类', '其他', '评审通过率'],
        departmentFlagColorList: ['#4C91FF', '#FFB300', '#EF5350'],
        mileStonesStatusList: ['进行中', '暂停', '关闭'],
        mileStonesLineColorList: ['#00D156', '#FFB300', '#EF5350', '#4C91FF', '#EBF1F9'],
        processColorList: ['#24A87E', '#fff'],
      },
    });
})();

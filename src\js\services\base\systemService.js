/*
* @Author: fubaole
* @Date:   2017-11-14 10:50:08
* @Last Modified by:   fubaole
* @Last Modified time: 2018-03-27 14:45:35
*/
(function() {
  'use strict';
  app.factory('SystemService', SystemService);
  SystemService.$inject=['HttpService','$rootScope','inform','LocalCache'];
  function SystemService(HttpService,$rootScope,inform,LocalCache){
    var service={
      getOrderMenuData:getOrderMenuData,
      getLeftMenuData:getLeftMenuData,
      getParamByMap:getParamByMap,
      getParamById:getParamById,
      saveParam:saveParam,
      deleteParamById:deleteParamById,
      getTypeCode:getTypeCode,
      getBlockContentListByMap:getBlockContentListByMap,
      getBlockContentByMap:getBlockContentByMap,
      getBlockContent:getBlockContent,
      saveOrUpdateBlockContent:saveOrUpdate<PERSON>lockContent,
      removeBlockContent:removeBlockContent,
      getAllRegion:getAllRegion,
      getRegionListByParentId:getRegionListByParentId,
      removeRegion:removeRegion,
      getRegion:getRegion,
      saveOrupdateRegion:saveOrupdateRegion,
      getPermissionListByParentId:getPermissionListByParentId,
      getPermission:getPermission,
      saveOrupdatePermission:saveOrupdatePermission,
      removePermission:removePermission,
      getAllPermission:getAllPermission,
      getAllRole:getAllRole,
      getRoleByLoginUserIdMap:getRoleByLoginUserIdMap,
      getGroupByLoginId:getGroupByLoginId,
      saveUserToRole:saveUserToRole,
      getRoleByLoginUserIdMapWithPage:getRoleByLoginUserIdMapWithPage,
      getAllRoleWithPage:getAllRoleWithPage,
      getRoleAndGrant:getRoleAndGrant,
      saveOrupdateRole:saveOrupdateRole,
      saveRoleAndPerToRole:saveRoleAndPerToRole,
      saveRoleAndOtherToRole:saveRoleAndOtherToRole,
      removeRole:removeRole,
      getPermissionListByRoleId:getPermissionListByRoleId,
      savePermissionToRole:savePermissionToRole,
      getDictTypeByMap:getDictTypeByMap,
      getDictValueByMap:getDictValueByMap,
      getDictValueListByDictTypeCode:getDictValueListByDictTypeCode,
      saveOrupdateDictValue:saveOrupdateDictValue,
      saveOrupdateDictType:saveOrupdateDictType,
      removeDictValue:removeDictValue,
      removeDictType:removeDictType,
      getCompanyByMap:getCompanyByMap,
      getCompanyById:getCompanyById,
      removeCompany:removeCompany,
      saveOrUpdateCompany:saveOrUpdateCompany,
      getCompanyNameByCompanyId:getCompanyNameByCompanyId,
      vaildateCompanyNameIsUsed:vaildateCompanyNameIsUsed,
      logManagementGetLogList:logManagementGetLogList,
      getOrganizationListByParentId:getOrganizationListByParentId,
      getOrganization:getOrganization,
      saveOrupdateOrganization:saveOrupdateOrganization,
      removeOrganization:removeOrganization,
      getAllOrganization:getAllOrganization,
      getUserListByMapWithPage:getUserListByMapWithPage,
      getEmployeeListByMapWithPage:getEmployeeListByMapWithPage,
      getOrgListByLoginUser:getOrgListByLoginUser,
      getAllOrg:getAllOrg,
      getOrgParentIdByLoginUser:getOrgParentIdByLoginUser,
      changeEmployeeStatus:changeEmployeeStatus,
      getOrgListByEmployeeId:getOrgListByEmployeeId,
      saveOrUpdateEmployee:saveOrUpdateEmployee,
      saveEmployeeExt:saveEmployeeExt,
      saveEmployeeToOrg:saveEmployeeToOrg,
      getEmployeeListByOrgIds:getEmployeeListByOrgIds,
      getEmployeeById:getEmployeeById,
      getGroupListByMap:getGroupListByMap,
      getGroupById:getGroupById,
      saveOrupdateGroup:saveOrupdateGroup,
      deleteGroupById:deleteGroupById,
      getRoleListByGroupId:getRoleListByGroupId,
      getRoleByMap:getRoleByMap,
      getUserListByGroupId:getUserListByGroupId,
      deleteUserAndGroupRelation:deleteUserAndGroupRelation,
      getUserListByMap:getUserListByMap,
      changeUserStatus:changeUserStatus,
      resetPassword:resetPassword,
      getRoleListByUserId:getRoleListByUserId,
      getRoleListMapByUserId:getRoleListMapByUserId,
      getUser:getUser,
      getUserByIds:getUserByIds,
      saveGroupAndGroupToRoleUser:saveGroupAndGroupToRoleUser,
      getEmployeeListNotIsUserByMap:getEmployeeListNotIsUserByMap,
      getUserListNotGroupIdByMap:getUserListNotGroupIdByMap,
      getRegionByUserId:getRegionByUserId,
      saveToUser:saveToUser,
      saveUserToRegion:saveUserToRegion,
      getDefaultPageByUserId:getDefaultPageByUserId,
      getDefaultPageByRoleId:getDefaultPageByRoleId,
      getPageListByUserId:getPageListByUserId,
      getPageListByRoleId:getPageListByRoleId,
      setDefaultPageByUserId:setDefaultPageByUserId,
      setDefaultPageByRoleId:setDefaultPageByRoleId,
      removePageByUserId:removePageByUserId,
      removePageByRoleId:removePageByRoleId,
      saveOrUpdateUserPage:saveOrUpdateUserPage,
      saveOrUpdateRolePage:saveOrUpdateRolePage,
      getPageByPageId:getPageByPageId
    };
    return service;

    // =======================================获取权限菜单=================================================
    // 获取header中功能导航数据
    function getOrderMenuData(){
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/queryPermissionMnueGroupByUserId');
    }
    // 获取左侧菜单数据 参数为权限类型 MENU、BUTTON、FUNCTION、SYS、MOD
    function getLeftMenuData(type){
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/getPermissionTree?permissionType='+type);
    }

    //=================================参数配置接口=============================================//
    // 获取参数列表
    function getParamByMap(map,pageNum,pageSize){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(map));
      urlData = inform.formateGetRequest(urlData,"pageNum",pageNum);
      urlData = inform.formateGetRequest(urlData,"pageSize",pageSize);
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'param/getParamByMap'+urlData);
    }
    // 根据ID获取参数信息
    function getParamById(id){
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'param/getParam?paramId='+id);
    }
    // 修改新增参数修改
    function saveParam(param){
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.extName+'param/saveOrupdateParam?paramJson='+encodeURIComponent(JSON.stringify(param)));
    }

    // 删除参数修改
    function deleteParamById(id){
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.extName+'param/removeParam?paramIds='+id);
    }
    // 获取参数类型值
    function getTypeCode() {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'param/queryParamCode');
    }

    //================================区块管理===========================================//
    // 查询区块列表
    function getBlockContentListByMap(){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent("{}"));
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.boardName+'blockContent/getBlockContentListByMap'+urlData);
    }
    // 查询区块列表（带分页）
    function getBlockContentByMap(map,pageNum,pageSize,orderStr){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(map));
      urlData = inform.formateGetRequest(urlData,"pageNum",pageNum);
      urlData = inform.formateGetRequest(urlData,"pageSize",pageSize);
      urlData = inform.formateGetRequest(urlData,"orderStr",orderStr);
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.boardName+'blockContent/getBlockContentByMap'+urlData);
    }

    // 根据ID 获取区块信息
    function getBlockContent(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.boardName+'blockContent/getBlockContent?blockContentId='+id);
    }
    // 修改新增区块信息
    function saveOrUpdateBlockContent(param) {
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.boardName+'blockContent/saveOrUpdateBlockContent?blockContentJson='+encodeURIComponent(JSON.stringify(param)));
    }

    // 删除区块
    function removeBlockContent(id) {
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.boardName+'blockContent/removeBlockContent?blockContentIds='+id);
    }


    //=================================地域管理========================================//
    // 获取所有地域
    function getAllRegion() {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'region/getAllRegion',{});

    }
    //查询所有地域
    function getRegionListByParentId(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'region/getRegionListByParentId?parentRegionId='+id);
    }
    // 根据ID 删除地域
    function removeRegion(id) {
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.extName+'region/removeRegion?regionIds='+id);
    }

    // 根据ID 查询信息
    function getRegion(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'region/getRegion?regionId='+id);
    }
    // 新增修改地域信息
    function saveOrupdateRegion(param) {
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.extName+'region/saveOrupdateRegion?regionJson='+encodeURIComponent(JSON.stringify(param)));
    }


    //==========================================权限管理==============================================//
    // 通过父ID获取子级数据列表
    function getPermissionListByParentId(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/getPermissionListByParentId?parentPermissionId='+id);
    }
    //通过ID获取权限信息
    function getPermission(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/getPermission?permissionId='+id);
    }
    // 新增修改权限信息
    function saveOrupdatePermission(param) {
      var urlData ="";
      urlData = inform.formateGetRequest(urlData,"permissionJson",encodeURIComponent(JSON.stringify(param)));
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/saveOrupdatePermission'+urlData);
    }

    // 根据ID 删除权限
    function removePermission(id) {
      var urlData ="";
      urlData = inform.formateGetRequest(urlData,"permissionIds",id);
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/removePermission'+urlData);
    }

    // 获取所有数据
    function getAllPermission() {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/getAllPermission',{});
    }


    //=============================================角色管理==================================================//
    // 获取角色授权列表
    function getRoleByLoginUserIdMap(){
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/getRoleByLoginUserIdMap',{});
    }

     // 获取用户组授权列表
    function getGroupByLoginId(){
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getGroupByLoginId',{});
    }

    // 获取所有角色信息
    function getAllRole() {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/getAllRole',{});
    }
    // 获取角色列表信息
    function getRoleByLoginUserIdMapWithPage(map,pageNum,pageSize){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(map));
      urlData = inform.formateGetRequest(urlData,"pageNum",pageNum);
      urlData = inform.formateGetRequest(urlData,"pageSize",pageSize);
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/getRoleByLoginUserIdMapWithPage'+urlData);
    }
    // 获取角色列表信息
    function getAllRoleWithPage(urlData){
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'common/getAllRoleWithPage', urlData);
    }
    // 保存用户和角色的关系
    function saveUserToRole(userId,roleIds){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"userId",userId);
      urlData = inform.formateGetRequest(urlData,"roleIds",roleIds);
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/saveUserToRole'+urlData);
    }
    // 根据ID 查询角色信息
    function getRoleAndGrant (id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/getRoleAndGrant?roleId='+id);
    }

    // 新增保存 角色信息
    function saveOrupdateRole(param) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"roleJson",encodeURIComponent(JSON.stringify(param)));
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/saveOrupdateRole'+urlData);
    }
    // 新增保存 角色信息
    function saveRoleAndPerToRole(param,permissionIds) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"roleJson",encodeURIComponent(JSON.stringify(param)));
      urlData = inform.formateGetRequest(urlData,"permissionIds",permissionIds);
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/saveRoleAndPerToRole'+urlData);
    }
    // 有授权的新增角色保存
    function saveRoleAndOtherToRole(param,permissionIds,grantGroupIds,grantRoleIds,grantOrgIds,grantPermissionIds){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"roleJson",encodeURIComponent(JSON.stringify(param)));
      urlData = inform.formateGetRequest(urlData,"permissionIds",permissionIds);
      urlData = inform.formateGetRequest(urlData,"grantGroupIds",grantGroupIds);
      urlData = inform.formateGetRequest(urlData,"grantRoleIds",grantRoleIds);
      urlData = inform.formateGetRequest(urlData,"grantOrgIds",grantOrgIds);
      urlData = inform.formateGetRequest(urlData,"grantPermissionIds",grantPermissionIds);
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/saveRoleAndOtherToRole'+urlData);
    }
    // 根据ID 删除角色
    function removeRole(id) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"roleIds",id);
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/removeRole'+urlData);
    }
    // 根据角色ID 获取权限信息
    function getPermissionListByRoleId(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/getPermissionListByRoleId?roleId='+id);
    }
    // 保存角色和权限关系
    function savePermissionToRole(roleId,permissionIds) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"roleId",roleId);
      urlData = inform.formateGetRequest(urlData,"permissionIds",permissionIds);
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/savePermissionToRole'+urlData);
    }


    //=================================================字典管理===============================================//
    // 查询信息列表
    function getDictTypeByMap(map,pageNum,pageSize){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(map));
      urlData = inform.formateGetRequest(urlData,"pageNum",pageNum);
      urlData = inform.formateGetRequest(urlData,"pageSize",pageSize);
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'dict/getDictTypeByMap'+urlData);
    }
    // 获取父级字典值
    function getDictValueByMap(map) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'dict/getDictValueByMap?map='+encodeURIComponent(JSON.stringify(map)));
    }

    // 根据ID 查询字典值信息
    function getDictValueListByDictTypeCode(code) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'dict/getDictValueListByDictTypeCode?dictTypeCode='+code);
    }
    // 新增修改字典值
    function saveOrupdateDictValue(param){
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.extName+'dict/saveOrupdateDictValue?dictValueJson='+encodeURIComponent(JSON.stringify(param)));
    }
    // 新增修改字典类型
    function saveOrupdateDictType(isUpdate,param){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"dictTypeJson",encodeURIComponent(JSON.stringify(param)));
      urlData = inform.formateGetRequest(urlData,"isUpdate",isUpdate);
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.extName+'dict/saveOrupdateDictType'+urlData);
    }
    // 新增修改字典值
    function removeDictValue(id) {
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.extName+'dict/removeDictValue?dictValueIds='+id);
    }

    // 新增修改字典类型
    function removeDictType(id) {
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.extName+'dict/removeDictType?dictTypeIds='+id);
    }

    //===========================================公司管理===================================================//
    // 获取公司信息列表
    function getCompanyByMap(map,pageNum,pageSize,orderStr) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(map));
      urlData = inform.formateGetRequest(urlData,"pageNum",pageNum);
      urlData = inform.formateGetRequest(urlData,"pageSize",pageSize);
      urlData = inform.formateGetRequest(urlData,"orderStr",orderStr);
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'company/getCompanyByMap'+urlData);
    }

    // 根据Id 查询公司详情信息
    function getCompanyById(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'company/getCompanyById?companyId='+id);
    }

    // 根据Id 删除公司
    function removeCompany(id) {
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.extName+'company/removeCompany?companyId='+id);
    }

    // 新增修改公司信息
    function saveOrUpdateCompany(param,companyTypeX) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"companyJson",encodeURIComponent(JSON.stringify(param)));
      urlData = inform.formateGetRequest(urlData,"companyTypeX",companyTypeX);
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.extName+'company/saveOrUpdateCompany'+urlData);
    }
    // 根据Id 获取供应商名字
    function getCompanyNameByCompanyId(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'company/getCompanyNameByCompanyId?companyId='+id);
    }
    // 验证公司名字是否存在
    function vaildateCompanyNameIsUsed(companyName,companyId) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"companyName",companyName);
      urlData = inform.formateGetRequest(urlData,"companyId",companyId);
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'company/vaildateCompanyNameIsUsed'+urlData);
    }

    //=================================日志管理配置接口=============================================//
    // 获取日志管理列表
    function logManagementGetLogList(map,pageNum,pageSize){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(map));
      urlData = inform.formateGetRequest(urlData,"pageNum",pageNum);
      urlData = inform.formateGetRequest(urlData,"pageSize",pageSize);
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'log/getLogByMap'+urlData);
    }


    //==========================================组织机构管理==============================================//
    // 授权新增接口 获取父Id
    function getOrgParentIdByLoginUser(){
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getOrgParentIdByLoginUser');
    }
    // 获取全部组织机构
    // 授权新增接口 获取全部组织机构
    function getOrgListByLoginUser(){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"userId",LocalCache.getSession("userId"));
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getOrgListByLoginUser'+urlData);
    }
    function getAllOrg() {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getAllOrg',{});
    }

    // 通过父ID获取子级数据列表
    // 授权新增接口
    // function getChildOrgByParentId(id){
    //   var urlData="";
    //   urlData = inform.formateGetRequest(urlData,"userId",LocalCache.getSession("userId"));
    //   urlData = inform.formateGetRequest(urlData,"orgParentId",id);
    //   return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getChildOrgByParentId'+urlData);
    // }
    function getOrganizationListByParentId(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getOrgListByParentId?parentOrgId='+id);
    }
    //通过ID获取组织机构信息
    function getOrganization(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getOrg?orgId='+id);
    }
    // 新增修改组织机构信息
    function saveOrupdateOrganization(param) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"orgJson",encodeURIComponent(JSON.stringify(param)));
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'org/saveOrUpdateOrg'+urlData);
    }

    // 根据ID 删除组织机构
    function removeOrganization(id) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"orgIds",id);
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.authorityName+'org/removeOrg'+urlData);
    }

    // 获取所有数据
    function getAllOrganization() {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getAllOrg',{});
    }

    //=================================人员管理==============================================//
    // 获取人员管理列表
    function getEmployeeListByMapWithPage(map,pageNum,pageSize){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(JSON.stringify(map)));
      urlData = inform.formateGetRequest(urlData,"pageNum",pageNum);
      urlData = inform.formateGetRequest(urlData,"pageSize",pageSize);
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getEmployeeListByMapWithPage'+urlData);
    }
    // 人员状态的设置
    function changeEmployeeStatus(employeeId,status) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"employeeId",employeeId);
      urlData = inform.formateGetRequest(urlData,"status",status);
      return HttpService.put($rootScope.getWaySystemApi+$rootScope.authorityName+'org/changeEmployeeStatus'+urlData);
    }
    // 根据Id 获取人员详情
    function getEmployeeById(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getEmployeeById?employeeId='+id);
    }
    // 根据人员Id 获取人员所在组织机构
    function getOrgListByEmployeeId(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getOrgListByEmployeeId?employeeId='+id);
    }

    // 新增修改人员信息
    function saveOrUpdateEmployee(param,orgIds) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"employeeJson",encodeURIComponent(JSON.stringify(param)));
      urlData = inform.formateGetRequest(urlData,"orgIds",orgIds);
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'org/saveOrUpdateEmployeeExt'+urlData);
    }
    // 合成的新增人员接口
    function saveEmployeeExt(param,orgIds) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"employeeJson",encodeURIComponent(JSON.stringify(param)));
      urlData = inform.formateGetRequest(urlData,"orgIds",orgIds);
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'org/saveEmployeeExt'+urlData);
    }

    // 保存人员与组织机构关系
    function saveEmployeeToOrg(employeeId,orgIds) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"employeeId",employeeId);
      urlData = inform.formateGetRequest(urlData,"orgIds",orgIds);
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'org/saveEmployeeToOrg'+urlData);
    }

    // 通过组织机构查询人员信息
    function getEmployeeListByOrgIds(orgIds) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getEmployeeListByOrgIds?orgIds='+orgIds);
    }
    //=================================用户管理=============================================//
    // 获取用户组管理列表
    function getUserListByMapWithPage(map,pageNum,pageSize){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(JSON.stringify(map)));
      urlData = inform.formateGetRequest(urlData,"pageNum",pageNum);
      urlData = inform.formateGetRequest(urlData,"pageSize",pageSize);
      return HttpService.post($rootScope.getWaySystemApi+'org/getUser/getUserListByMapWithPage'+urlData);
    }
    // 根据用户Id 查询用户信息
    function getUser(userId) {
      return HttpService.post($rootScope.getWaySystemApi+'org/getUser/getUserInfo?userId='+userId);
    }
    // 绑定地域中 根据userIds获取用户列表
    function getUserByIds(ids) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getUserByIds?userIds='+ids);
    }
    // 根据userId查询角色列表
    function getRoleListByUserId(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/getRoleListByUserId?userId='+id);
    }

    // 获取用户角色和所在用户组角色
    function getRoleListMapByUserId(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/getRoleListMapByUserId?userId='+id);
    }
    // 重置密码
    function resetPassword(userIds,defaultPassword){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"userIds",userIds);
      urlData = inform.formateGetRequest(urlData,"defaultPassword",defaultPassword);
      return HttpService.put($rootScope.getWaySystemApi+$rootScope.authorityName+'org/resetPassword'+urlData);
    }
    // 用户状态的设置
    function changeUserStatus(userIds,status) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"userIds",userIds);
      urlData = inform.formateGetRequest(urlData,"status",status);
      return HttpService.put($rootScope.getWaySystemApi+$rootScope.authorityName+'org/changeUserStatus'+urlData);
    }

    // 根据查询条件获取不是用户的人员列表
    function getEmployeeListNotIsUserByMap(currentEmployeeId,map){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"currentEmployeeId",currentEmployeeId);
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(map));
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getEmployeeListNotIsUserByMap'+urlData);
    }
    // 根据用户ID 获取地域
    function getRegionByUserId(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.extName+'region/getRegionByUserId?userId='+id);
    }
    // 保存人员和用用户关系
    function saveToUser(employeeIds,defaultPassword){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"employeeIds",employeeIds);
      urlData = inform.formateGetRequest(urlData,"defaultPassword",defaultPassword);
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'org/saveToUser'+urlData);
    }

    //保存地域和用户的额关系
    function saveUserToRegion(param){
      // var urlData="";
      //urlData = inform.formateGetRequest(urlData,"userIds",userIds);
      //urlData = inform.formateGetRequest(urlData,"regions",regions);
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.extName+'region/saveUserToRegion',param);
    }


    //=================================用户组管理=============================================//
    // 获取用户组管理列表
    function getGroupListByMap(map,pageNum,pageSize){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(map));
      urlData = inform.formateGetRequest(urlData,"pageNum",pageNum);
      urlData = inform.formateGetRequest(urlData,"pageSize",pageSize);
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getGroupByMap'+urlData);
    }

    // 根据ID获取用户组信息
    function getGroupById(id){
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getGroup?groupId='+id);
    }

    // 修改新增用户组
    function saveOrupdateGroup(param){
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'org/saveOrupdateGroup?groupJson='+encodeURIComponent(JSON.stringify(param)));
    }

    // 删除用户组
    function deleteGroupById(id){
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.authorityName+'org/removeGroup?groupId='+id);
    }

    // 获取角色管理列表
    function getRoleByMap(map){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(map));
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/getRoleByMap'+urlData);
    }

    // 根据groupId获取角色列表
    function getRoleListByGroupId(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'authority/getRoleListByGroupId?groupId='+id);
    }

    // 根据groupID获取用户列表
    function getUserListByGroupId(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getUserListByGroupId?groupId='+id);
    }

    // 删除用户和用户组的关系
    function deleteUserAndGroupRelation(userIds,groupId) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"userIds",userIds);
      urlData = inform.formateGetRequest(urlData,"groupId",groupId);
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.authorityName+'org/removeUserGroup'+urlData);
    }

    // 根据查询条件获取用户列表
    function getUserListByMap(map){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(map));
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getUserListByMap'+urlData);
    }

    // 根据查询条件和用户组Id查询不是该用户组的用户列表
    function getUserListNotGroupIdByMap(map,groupId){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"map",encodeURIComponent(map));
      urlData = inform.formateGetRequest(urlData,"groupId",groupId);
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.authorityName+'org/getUserListNotGroupIdByMap'+urlData);
    }

    // 新增修改用户组信息（保存用户组及该用户组与用户关系和该用户组与角色的关系）
    function saveGroupAndGroupToRoleUser(groupJson,userIds,roleIds) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"groupJson",encodeURIComponent(JSON.stringify(groupJson)));
      urlData = inform.formateGetRequest(urlData,"userIds",userIds);
      urlData = inform.formateGetRequest(urlData,"roleIds",roleIds);
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.authorityName+'org/saveGroupAndGroupToRoleUser'+urlData);
    }


    //==================================个人工作台=============================================//
    // 获取首页数据
    function getDefaultPageByUserId() {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.boardName+'page/getDefaultPageByUserId');
    }
    // 通过roleId 获取首页默认页
    function getDefaultPageByRoleId(id) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.boardName+'page/getDefaultPageByRoleId?roleId='+id);
    }

    // 查询配置
    function getPageByPageId(pageId) {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.boardName+'page/getPageByPageId?pageId='+pageId);
    }

    //首页个人工作台接口

    // 设置默认页面
    function setDefaultPageByUserId(pageId){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"pageId",pageId);
      return HttpService.put($rootScope.getWaySystemApi+$rootScope.boardName+'page/setDefaultPageByUserId'+urlData);
    }

    // 删除配置设计
    function removePageByUserId(pageId) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"pageId",pageId);
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.boardName+'page/removePageByUserId'+urlData);
    }

    // 获取选择配置页面的数据
    function getPageListByUserId() {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.boardName+'page/getPageListByUserId');
    }

    // 保存设计的配置
    function saveOrUpdateUserPage(isDefault,pageEntityJsonStr){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"isDefault",isDefault);
      urlData = inform.formateGetRequest(urlData,"pageEntityJsonStr",encodeURIComponent(JSON.stringify(pageEntityJsonStr)));
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.boardName+'page/saveOrUpdateUserPage'+urlData);
    }

    // 个人工作台接口

    // 设置默认页面
    function setDefaultPageByRoleId(pageId){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"roleId",LocalCache.getSession("roleId"));
      urlData = inform.formateGetRequest(urlData,"pageId",pageId);
      return HttpService.put($rootScope.getWaySystemApi+$rootScope.boardName+'page/setDefaultPageByRoleId'+urlData);
    }
    // 删除配置设计
    function removePageByRoleId(pageId) {
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"roleId",LocalCache.getSession("roleId"));
      urlData = inform.formateGetRequest(urlData,"pageId",pageId);
      return HttpService.delete($rootScope.getWaySystemApi+$rootScope.boardName+'page/removePageByRoleId'+urlData);
    }

    // 获取选择配置页面的数据
    function getPageListByRoleId() {
      return HttpService.get($rootScope.getWaySystemApi+$rootScope.boardName+'page/getPageListByRoleId?roleId='+LocalCache.getSession("roleId"));
    }

    // 保存设计的配置
    function saveOrUpdateRolePage(isDefault,pageEntityJsonStr){
      var urlData="";
      urlData = inform.formateGetRequest(urlData,"roleId",LocalCache.getSession("roleId"));
      urlData = inform.formateGetRequest(urlData,"isDefault",isDefault);
      urlData = inform.formateGetRequest(urlData,"pageEntityJsonStr",encodeURIComponent(JSON.stringify(pageEntityJsonStr)));
      return HttpService.post($rootScope.getWaySystemApi+$rootScope.boardName+'page/saveOrUpdateRolePage'+urlData);
    }

  }

})();
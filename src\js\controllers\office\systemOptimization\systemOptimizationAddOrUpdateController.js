(function () {
    app.controller("systemOptimizationAddOrUpdateController", ['comService', '$rootScope', '$scope', '$state', '$stateParams', '$modal', 'systemOptimizationService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function (comService, $rootScope, $scope, $state, $stateParams, $modal, systemOptimizationService, inform, Trans, AgreeConstant, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //判断是新增还是修改的标识符
            $scope.updateFlag = false;
            if(typeof ($stateParams.item) !== 'undefined' && null != $stateParams.item){
                $scope.updateFlag = true;
            }
            //判断过程域字段是否可选（只有类别为研发管理体系，才可选）
            $scope.processDomainFlag = true;

            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;

            $scope.dateParam = {};
            //分数后的问号图标提示
            $scope.scoreRemark = '说明：大版本修订，一般指文件内容的整体性优化，涉及文件逻辑结构的变动，结构变动、或核心、关键内容大范围的调整' +
                '\n说明：小版本修订，指逻辑结构不必修改，只是对原有章节的一些补充、小的调整和修改'

            $scope.quarterList = [{
                value: "1",
                label: "第一季度"
            },{
                value: "2",
                label: "第二季度"
            },{
                value: "3",
                label: "第三季度"
            },{
                value: "4",
                label: "第四季度"
            }];

            //初始化页面信息
            initPages();
            $scope.setDraggableTime = setDraggableTime;
            $scope.setScore = setScore;
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            //重置查询条件
            $scope.reset = function () {
                $scope.formRefer = {};
            };

            function initPages() {

                var flag = 0;
                //获取类别
                $scope.categoriesList = [];
                comService.getParamList('SYSOptimization', 'categories').then(function (data) {
                    if (data.data) {
                        $scope.categoriesList = data.data;
                        flag++;
                        iniGetData(flag);
                    }
                });

                //获取文件类型
                $scope.fileTypeList = [];
                comService.getParamList('SYSOptimization', 'fileType').then(function (data) {
                    if (data.data) {
                        $scope.fileTypeList = data.data;
                        flag++;
                        iniGetData(flag);
                    }
                });

                //获取过程域
                $scope.processDomainList = [];
                comService.getParamList('SYSOptimization', 'processDomain').then(function (data) {
                    if (data.data) {
                        $scope.processDomainList = data.data;
                        flag++;
                        iniGetData(flag);
                    }
                });

                getResponsiblePerson();

            }

            /**
             *  获取责任人下拉框数据
             */
            function getResponsiblePerson(){
                // 责任人下拉列表
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function (data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                        $scope.dateParam.employeeId = $scope.dateParam.employeeId == null ? [] : $scope.dateParam.employeeId.split(',');
                        setDraggableTime();
                    }
                });
            }

            /**
             * 责任人变化时延迟1秒修改li属性允许拖拽
             */
            function setDraggableTime() {

                setTimeout(setDraggableOfli, 1000 * 1);
            }

            /**
             * 修改li属性允许拖拽
             */
            function setDraggableOfli() {

                var subNodes = document.querySelectorAll("ul.chosen-choices li.search-choice");

                for (var i = 0; i < subNodes.length; i++) {
                    $(subNodes[i]).attr("draggable", true);
                }
            }

            /**
             * 获取适用范围获取二级部门
             */
            $scope.getXYDepartment = function(){
                if($scope.dateParam.adaptedRange==='二级部门'){
                    //获取二级部门
                    getTwoDepartment()
                }else{
                    $scope.departmentList = [];
                }
            }
            //获取二级部门列表
            function getTwoDepartment(){
                $scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.departmentList = data.data;
                    }
                });
            }

            //选择过程域
            $scope.selectProcessDomain = function (str,code){
                $scope.dateParam.processDomain = str;
                $scope.dateParam.processDomainCode = code;
                document.getElementById("add_info_close_button").click();
            }

            /**
             * 判定初始化结束后，且为修改，则调用查询方法
             * @param flag
             */
            function iniGetData(flag){
                if(flag < 3){
                    return;
                }
                if(!$scope.updateFlag){
                    return;
                }

                var urlData = {
                    'id': $stateParams.item
                };
                systemOptimizationService.getInfoWithPage(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if ( data.data.list.length === 0) {
                                inform.common("想要修改的项目不存在");
                            }else {
                                $scope.dateParam = data.data.list[0];
                                var scoreVal = data.data.list[0].score;
                                //设置过程域名称
                                setProcessNameByCode(data.data.list[0].processDomain);
                                //获取责任人列表
                                getResponsiblePerson();
                                //获取分数下拉框
                                setScore();
                                $scope.dateParam.score = scoreVal;
                         		 //查看过程域是否有值 进行设置
                         		 if($scope.dateParam.processDomain===''){
                         			$scope.processDomainFlag = false;
                         		 }
                                 //适用范围是二级部门时，设置二级部门属性的下拉框
                                 if ($scope.dateParam.adaptedRange==='二级部门'){
                                    getTwoDepartment();
                                 }
                            }
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            /**
             * 保存信息
             */
            $scope.saveDetail = function(){
            	//如果类别不为体系文件，设置过程域值为空
            	if ($scope.dateParam.categories !== '01' ){
            		$scope.dateParam.processDomain = '';
   	 			} else {
   	 				//如果是体系文件
   	 				if ($scope.dateParam.processDomain === ''||$scope.dateParam.processDomain == null){
   	 					inform.common("当前类别为研发管理体系文件，请选择过程域。");
   	 					return;
   	 				}
   	 			}
                //传参
            	var urlData = {
                   'categories': $scope.dateParam.categories,
                   'processDomain': $scope.dateParam.processDomainCode,
                   'responsiblePersons': $scope.dateParam.employeeId,
                   'finishTime': inform.format($scope.dateParam.finishTime, 'yyyy-MM-dd'),
                   'fileName': $scope.dateParam.fileName,
                   'fileType': $scope.dateParam.fileType,
                   'numberOfProcessFiles': $scope.dateParam.numberOfProcessFiles,
                   'numberOfTemplateFiles': $scope.dateParam.numberOfTemplateFiles,
                   'department': $scope.dateParam.department,
                   'adaptedRange': $scope.dateParam.adaptedRange,
                   'score': $scope.dateParam.score,
                   'description': $scope.dateParam.description
                };
            	//需要对已经格式化的日期进行处理设置季度
            	var month = (new Date(urlData.finishTime)).getMonth() + 1;
            	//根据结束时间设置季度
                var quarters = (inform.dateToQuarter(month)*1+1)+"";
                urlData.quarter = quarters;
            	//如果是修改，添加id字段
            	if($scope.updateFlag){
            		urlData.id = $stateParams.item;
                }
                systemOptimizationService.addOrUpdateInfo(urlData).then(function (data) {
                	if(data.code === AgreeConstant.code) {
                		$scope.goback();
 	 				 } 
                },function (error) {
                	inform.common(Trans("tip.requestError"));
                });
            };
            /**
             * 类别值改变时，显示/隐藏过程域
             */
            $scope.categoriesChange = function(){
                //判断类别的值，决定过程域是否显示
                if($scope.dateParam.categories === '01'){
                    $scope.processDomainFlag = true;
                    $scope.dateParam.processDomain = '';
                }else{
                    $scope.processDomainFlag = false;
                    $scope.dateParam.processDomain = '00';
                }
            };

            /**
             * 根据过程域code设置过程域名称
             */
            function setProcessNameByCode(processCode){
                //将过程域code转换为过程域名称并进行赋值
                var list = $scope.processDomainList;
                for (var i = 0;i<list.length;i++) {
                    if(processCode === list[i].param_code){
                        $scope.dateParam.processDomain = list[i].param_value;
                    }
                }
            }

            /**
             * 设置分数
             */
            function setScore(){
                var scoreMap = {'000':['2'],
                '001':['5'],
                '100':['2','5'],
                '101':['5'],
                '110':['2','5','10','20'],
                '111':['10','20']};
                //类别标志 0-模板类 1-其他类别
                var categoriesFlag = '0';
                //文件类型标志 0-修订 1-新制定
                var fileTypeFlag = '0';
                //范围标志 0-二级部门 1-一级部门
                var adaptedRangeFlag = '0';
                //文件类型为新制定
                if($scope.dateParam.fileType === '02') {
                    fileTypeFlag = '1';
                }
                //类别为非模板
                if($scope.dateParam.categories !== '04') {
                    categoriesFlag = '1';
                    if($scope.dateParam.adaptedRange === '一级部门') {
                        adaptedRangeFlag = '1';
                    }
                }
                $scope.scoreList = scoreMap[categoriesFlag + adaptedRangeFlag + fileTypeFlag];
            }

            /**
             * 返回项目信息
             */
            $scope.goback = function() {
                $state.go('app.office.systemOptimizationController');
            };

            //更新时间
            $scope.openDateup = function ($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.up = true; //更新时间
            };
            /**
             * *************************************************************
             *              方法调用部分                                 结束
             * *************************************************************
             */
        }]);
})();
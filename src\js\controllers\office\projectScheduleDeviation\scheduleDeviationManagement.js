
(function() {
	app.controller("scheduleDeviationManagement", ['comService', '$rootScope', '$scope','scheduleDeviationService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state','LocalCache',
		function(comService, $rootScope, $scope,scheduleDeviationService, inform, Trans, AgreeConstant, $modal, $state,LocalCache) {
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		$scope.limitList = AgreeConstant.limitList; // 正则校验配置

		//保存查询出的产品线信息
    	$scope.projectData = [];//项目情况报告
    	$scope.formRefer = {
            projectManager:'',
            cname : '',
            productLine : '',
            type :'',
            department:'',
            writeFlag:true
        };

        // 初始化分页数据
    	$scope.pages = inform.initPages();
       
 		//开发模型下拉框数据源
        $scope.typeSelect = [{
            value: '瀑布',
            label: '瀑布'
        },{
            value: '敏捷',
            label: '敏捷'
        }];
        //状态展示Map
        $scope.statueMap = {
            "0":'正常',
            "1":'较低延期风险',
            "2":'较高延期风险',
            "3":'已延期'
        };

        //项目状态展示下拉框数据源
        $scope.projectStatueSelect = AgreeConstant.projectStatusList;
        //设置列表的高度
        $scope.setDivHeight = setDivHeight;
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		$(window).resize(showDataTable);
		//获取数据
		$scope.getData = getData;

        //初始化页面信息
        initPages();

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */	
		/**
		 * 获取项目
		 */
		function getData(pageNum) {
		    //删除已加载冻结头部和部分列的HTML模板
            $scope.dataTableShow = 0;
			var urlData ={
			    'cname':$scope.formRefer.cname,//项目名称
                'productLine':$scope.formRefer.productLine,//产品线名
                'type':$scope.formRefer.type,//产品类型
                'projectStatus':$scope.formRefer.projectStatus,//项目进度
                'page':pageNum,
                'pageSize':$scope.pages.size,
                'projectManager':$scope.formRefer.projectManager,
                'department':$scope.formRefer.department === "汇总" ? "": $scope.formRefer.department,
                'startTime':$scope.formRefer.startTime,
                'endTime':$scope.formRefer.endTime,
                'selectType':$scope.formRefer.selectType
			};
            scheduleDeviationService.getProjectInfo(urlData).then(function(data) {
                //重新加载冻结头部和部分列的HTML模板
                $scope.dataTableShow = 1;
				if (data.code === AgreeConstant.code) {
                    //项目报告
                    $scope.projectData = data.data.list;
                     if ($scope.projectData.length === 0) {
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages(); //初始化分页数据
                        }
                    // 分页信息设置
                    $scope.pages.total = data.data.total;
                    $scope.pages.star = data.data.startRow;
                    $scope.pages.end = data.data.endRow;
                    $scope.pages.pageNum = data.data.pageNum;
                    //调用DataTable组件冻结表头和左侧及右侧的列
                    setTimeout(showDataTable,300);
				} else {
					inform.common(data.message);
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
		}

		//重置按钮（重置按钮，不重置writeFlag）
        $scope.reset = function(){
		    var flag = $scope.formRefer.writeFlag;
            $scope.formRefer = {
                writeFlag:flag
            };
        };

		/**
 		 * 根据计划偏移率修改字体颜色
 		 */ 
 		$scope.jude = function(item) {
 			if( item.currentPlanDeviation*1 > 100 || item.currentPlanDeviationMarket*1 > 100){
 				return "red";
 			} 
 			return "black";
 		};
		/**
    	 * 设置列表的高度
    	 */
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 210);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 80);
 		}

        $scope.myKeyup = function(e){
 
             //IE 编码包含在window.event.keyCode中，Firefox或Safari 包含在event.which中
             var keycode = window.event?e.keyCode:e.which; 
            if(keycode===13){
            $scope.showkdUsers();
            }
        };

		/**
    	 * 页面初始化
    	 */
    	function initPages() {
    		//获取产品线
    		$scope.projectLine = [];
    		comService.queryEffectiveParam('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.projectLine =  data.data;
        		}
            });
            //获取部门信息
            $scope.departmentSelect = [];
            comService.getOrgChildren('D010053').then(function(data) {
                $scope.departmentSelect = comService.getDepartment(data.data);

                //获取缓存
                var searchObject = LocalCache.getObject('scheduleDeviationController_department');
                //获取缓存数据，并设置初始参数
                if(typeof (searchObject) !== 'undefined' && typeof (searchObject.writeFlag) !== 'undefined'){
                    //为查询条件赋值
                    var flag = searchObject.writeFlag;
                    $scope.formRefer = angular.extend($scope.formRefer, searchObject);
                    $scope.formRefer = angular.extend($scope.formRefer, searchObject.searchParam);
                    angular.forEach($scope.departmentSelect,function (obj) {
                        if(obj.orgName === searchObject.department){
                            $scope.formRefer.department = obj.orgCode;
                        }
                    });
                    $scope.formRefer.writeFlag = flag;
                    LocalCache.setObject('scheduleDeviationController_department',{});
                }
                searchObject = LocalCache.getObject('scheduleDeviationDetailManagement_param');
                //获取缓存数据，并设置初始参数
                if(typeof (searchObject) !== 'undefined'  && typeof (searchObject.writeFlag) !== 'undefined'){
                    $scope.formRefer = angular.extend($scope.formRefer, searchObject);
                    LocalCache.setObject('scheduleDeviationDetailManagement_param',{});
                }

                //查询
                getData();
             });
            
    	}
    	
		/**
      	 * 报表界面切换(详情)
      	 */
     	$scope.details=function (item) {
            //写入缓存
            LocalCache.setObject('scheduleDeviationManagement_param',{
                id:item.id,
                milestonePlanResource:item.milestonePlanResource,
                cname:$scope.formRefer.cname,
                productLine:$scope.formRefer.productLine,
                type       :$scope.formRefer.type,
                projectManager:$scope.formRefer.projectManager,
                department:$scope.formRefer.department,
                writeFlag: $scope.formRefer.writeFlag,
                searchParam:$scope.formRefer.searchParam,
                brief:item.brief,
                planChangeNo:item.planChangeNo
            });
            $state.go('app.office.scheduleDeviationDetailManagement');
        };

        /**
         * 跳转至统计页面
         */
        $scope.goback = function() {
            //写入缓存
            LocalCache.setObject('scheduleDeviationManagement_searchParam',{
                searchParam:$scope.formRefer.searchParam
            });
            $state.go('app.office.projectScheduleDeviation');
        };
        /**
        *调用DataTable组件冻结表头和左侧及右侧的列
        */
        function showDataTable(){
            $('#fixedLeftAndTop').DataTable( {
                //可被重新初始化
                retrieve:       true,
                //自适应高度
                scrollY:        'calc(100vh - 350px)',
                scrollX:        true,
                scrollCollapse: true,
                //控制每页显示
                paging:         false,
                //冻结列（默认冻结左3）
                fixedColumns:   {
                    leftColumns: 3,
                    rightColumns: 1
                },
                //search框显示
                searching:      false,
                //排序箭头
                ordering:       false,
                //底部统计数据
                info:           false
            } );
        }
        /**
	     * *************************************************************
	     *              方法声明部分                                结束
	     * *************************************************************
	     */	
		} ]);
})();
(function () {
    'use strict';
    app.factory('gitService', gitService);
    gitService.$inject = ["HttpService", '$rootScope'];

    function gitService(HttpService, $rootScope) {

        var service = {
            getData:getData,
            doTag:doTag,
            synTag: synTag

        };
        return service;

        function getData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'repository/get_tag_by_page', urlData);
        }

        function doTag(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'repository/do_tag', urlData);
        }
        function synTag(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'repository/syn_tag', urlData);
        }
    }
})();

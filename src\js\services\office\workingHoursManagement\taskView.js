(function() {
    'use strict';
  app.factory('taskViewService', taskViewService);
  taskViewService.$inject=["HttpService",'$rootScope'];

  function taskViewService(HttpService,$rootScope){
    var service={
        getTask
    };
    return service;
    /**
     * 查询
     */
    function getTask(params) {
    	return HttpService.post(`${$rootScope.getWaySystemApi}/personalWorkHourView/getTaskViewData`, params);
    }
  }
})();
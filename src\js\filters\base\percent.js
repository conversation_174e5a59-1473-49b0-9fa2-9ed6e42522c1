/*
* @Author: fubaole
* @Date:   2017-08-23 11:50:08
* @Last Modified by:   fubaole
* @Last Modified time: 2017-12-08 13:47:01
* 转成百分比
* 使用方法：{{item.total | Percent }}
*/
(function(){
  'use strict';

    app.filter('Percent', function () {

      return function (o) {

          if (o != undefined && /(^(-)*\d+\.\d*$)|(^(-)*\d+$)/.test(o)) {

              // var v = parseFloat(o);
              // return Number(Math.round(v * 10000) / 10000).toFixed(2) + "%";
              return parseFloat(o).toFixed(2) + "%";
          } else {

              return "undefined";

          }

      };

  });
})();
/*
 * @Author: ha<PERSON><PERSON><PERSON>
 * @Date:   2018-01-19 10:45:26
 * @Last Modified by:   haohong<PERSON>
 * @Last Modified time: 2018-02-28 11:59:26
 */

(function() {
    'use strict';
    app.controller("userGroup_Look", ['$rootScope', '$scope', '$state', 'inform', '$stateParams', '$modal', '$log', 'Trans', 'SystemService', 'AgreeConstant',
        function($rootScope, $scope, $state, inform, $stateParams, $modal, $log, Trans, SystemService, AgreeConstant) {

            $scope.role = {}; //角色
            $scope.groupId = $stateParams.groupId; // 获取用户组id
            getData(); //获取用户组信息
            getRole(); //获取角色
            getUsers(); //获取用户列表

            // 根据用户组ID获取用户组信息
            function getData() {
                SystemService.getGroupById($scope.groupId)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.group = data.result;
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 根据用户组ID获取角色
            function getRole() {
                SystemService.getRoleListByGroupId($scope.groupId)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.roleList = [];
                            angular.forEach(data.result, function(res, index) {
                                $scope.roleList.push(res.roleName);
                            });
                            $scope.roleList = $scope.roleList.join();
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            //根据groupID获取用户列表
            function getUsers() {
                console.log($scope.groupId);
                SystemService.getUserListByGroupId($scope.groupId)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.addUserData = data.result;
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

        }
    ]);
})();
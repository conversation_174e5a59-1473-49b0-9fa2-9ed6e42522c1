/*
 * @Author: fubaole
 * @Date:   2017-09-28 13:45:34
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-16 09:53:29
 */
(function() {
    'use strict';
    app.controller("teamManagement", ['$rootScope', '$scope', 'inform', '$modal', '$state', 'Trans', 'comService', 'SystemService', 'ConfigService', 'teamService', 'LocalCache', 'AgreeConstant',
        function($rootScope, $scope, inform, $modal, $state, Trans, comService, SystemService, ConfigService, teamService, LocalCache, AgreeConstant) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //判断是否为新增，true新增，false同步
            $scope.insertFlag = false;
            //判断权限，true新增有权限，false无权限
            $scope.showFlag = false;
            //权限开关，code:02：有新增修改权限,01：有部门级新增修改权限，00：无新增修改权限
            var permissionObject = [{code:'00'}];
            //系统集成研发中心为默认一级部门
            $scope.primaryDept='D010053';
            //初始化根一级部门列表
            initPrimaryDeptList();

            var deptMap = [];//部门map
            var employeeMap = [];//人员map
            $scope.treeData = [];//结构树参数
            //初始化树
            initTreeData($scope.primaryDept);
            getPermission();// 获取权限信息

            $scope.flag = true; //判断是否符合新增与更新条件

            $scope.membersFlag = true;//判断是否有修改组成员的权限


            inform.autoHeight();// 设置侧边的高度,随窗口变动
            window.onresize = inform.autoHeight;
            //修改一级部门，二级部门进行联动
            $scope.changePrimaryDept = function(){
                initTreeData($scope.primaryDept);
            };
            // 左侧树配置
            var Leftsetting = angular.copy(ConfigService.onRightClick);
            Leftsetting.callback.beforeClick = nodeSelect; // 点击节点前回调
            Leftsetting.callback.onRightClick = onRightClick; // 右键菜单

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */
            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 权限操作
             * *************************************************************
             */

             /**
             * 根据一级部门id初始化Tree
             */
             function initTreeData(orgId) {

                deptMap = [];//部门map
                employeeMap = [];//人员map
                $scope.treeData = [];//结构树参数
                $scope.showFlag = false;
                getDeptList(orgId);//获取部门信息

             }
            /**
             * 初始化根一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function (data) {
                    if (data.data) {
                        $scope.primaryDeptList = data.data;
                    }
                });
            }
            /**
             * 获取权限信息
             */
            function getPermission(){
                comService.getRolePermissionNew("0012").then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        permissionObject = data.data
                    } else {
                        common(data.message);
                    }
                }, function() {
                    common(Trans("tip.requestError"));
                });
             }

            /**
             * 判断是否有权限
             * @param item   权限对象 [{code:'01|02|00',departmentCode:'部门code'}]
             * @param treeNode  当前树节点
             * @returns {boolean}  true：有权限，false：无权限
             */
            function adjustPermission(item,treeNode) {

                var flag = false;
                //根据权限对象，判断登录人的权限
                if (item[0].code === '02'){
                    flag = true;
                }
                if (item[0].code === '01') {
                    angular.forEach(item, function(item) {
                       if(deptMap[item.departmentCode] === treeNode.orgId ||
                           deptMap[item.departmentCode] === treeNode.parentId) {
                           flag = true;
                       }
                    });
                }
                return flag;
            }

            /**
             * 数据拼接操作，查询操作
             * *************************************************************
             */

            //获取系研二级部门信息
            function getDeptList(orgId) {
                $scope.treeData = [];
                //获取山东新北洋集团的下级部门信息
                comService.getOrgChildrenAndSelf(orgId).then(function(data) {
                    angular.forEach(data.data,function (res) {
                        deptMap[res.orgCode] = res.orgId;
                        //设置部门信息
                            var jsonTree = {
                                "id": res.orgId,
                                "pId": res.parentId,
                                "name": res.orgName,
                                "open": true,
                                "isGroup": false
                            };
                            $scope.treeData.push(angular.extend(jsonTree, res));
                    });
                    getTeamData(orgId);
                });
            }

            /**
             * 根据部门id获取员工信息
             * @param orgId 部门id
             */
            function getEmployeesByOrgId(orgId){
                //获取员工信息
                $scope.employeesList = [];
                comService.getEmployeesByOrgId(orgId).then(function (data) {
                    angular.forEach(data.data, function (res) {
                        //数字转字符串
                        res.employeeId = res.employeeId + '';
                        if(orgId === ''){
                            employeeMap[res.employeeId] = res.realName;
                        }
                    });
                     $scope.employeesList = data.data;
                });
            }

            /**
             * 获取小组信息
             */
            function getTeamData(orgId) {
                var dataParam = {"departmentId":orgId};
                teamService.getTeamData(dataParam).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.teamList = data.data;
                        //获取结构树数据
                        getTreeData();
                    } else {
                        common(data.message);
                    }
                }, function() {
                    common(Trans("tip.requestError"));
                });
            }

            /**
             * 结构树中拼接小组信息
             */
            function getTreeData() {
                $scope.teamList.sort(function (a, b) {
                    return a.orderBy - b.orderBy;
                });
                //向树中添加小组信息
                angular.forEach($scope.teamList, function (res) {
                    //id拼接逗号，以区分组织机构与小组
                    res.id = res.id + ',';
                    var jsonTree = {
                        "id":res.id ,
                        "pId":  res.departmentId * 1,
                        "parentId":  res.departmentId * 1,
                        "name": res.teamName,
                        "open": true,
                        "isGroup": true
                    };
                    $scope.treeData.push(jsonTree);
                });

                //将数据写入树中
                $scope.treeData = inform.unique($scope.treeData);
                $.fn.zTree.init($("#treeDemo"), Leftsetting, $scope.treeData);
            }

            /**
             * 增删改操作
             * *************************************************************
             */

            /**
             * 向提交数据
             */
            $scope.setInfo = function () {
                //判断是新增还是修改
                if($scope.insertFlag){
                    insertInfo();
                }else{
                    updateInfo();
                }
            };

            /**
             * 新增小组
             */
            function insertInfo() {
                //校验
                adjustParams();
                if ( !$scope.flag){
                	return;
                }
                var params = {
                    'teamName':$scope.teamInfo.teamName,
                    'teamManagerId':$scope.teamInfo.teamManagerId,
                    'members':$scope.teamInfo.members,
                    'departmentId':$scope.departmentId,
                    'orderBy':$scope.teamInfo.orderBy
                };
                teamService.insertInfo(params).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        //设置新增按键不可见，同步按键可见
                        $scope.insertFlag = false;
                        //回填小组信息，保证当前信息可进行修改
                        $scope.teamInfo.departmentId =$scope.departmentId;
                        $scope.teamInfo.id = data.data;
                        common(data.message,function () {
                            //刷新结构树
                            getDeptList($scope.primaryDept);
                        });
                    } else {
                        common(data.message);
                    }

                }, function() {
                    common(Trans("tip.requestError"));
                });
            }

            /**
             * 修改小组
             */
            function updateInfo() {
                //校验
                adjustParams();
                if ( !$scope.flag){
                	return;
                }
                var params = {
                    'id':$scope.teamInfo.id,
                    'teamName':$scope.teamInfo.teamName,
                    'teamManagerId':$scope.teamInfo.teamManagerId,
                    'members':$scope.teamInfo.members,
                    'departmentId':$scope.departmentId,
                    'orderBy':$scope.teamInfo.orderBy
                };
                teamService.updateInfo(params).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        common(data.message,function () {
                            $scope.insertFlag = false;
                            //刷新结构树
                            getDeptList($scope.primaryDept);
                        });
                    } else {
                        common(data.message);
                    }
                }, function() {
                    common(Trans("tip.requestError"));
                });
            }

            /**
             * 删除小组
             */
            function deleteTeam() {
                var params = {
                    'id':$scope.teamId
                };
                teamService.deleteInfo(params).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        //设置小组信息不可见
                        $scope.showFlag = false;
                        common(data.message,function () {
                            //刷新结构树
                            getDeptList($scope.primaryDept);
                        });
                    } else {
                        common(data.message);
                    }

                }, function() {
                    common(Trans("tip.requestError"));
                });
            }

            /**
             * 提示信息
             * @param str  提示信息
             * @param func 确定时执行的函数
             */
            function  common(str,func){
                layer.confirm(str,{
                    title:false,
                    btn:['确定']
                },function(result){
                    layer.close(result);
                    if(typeof (func) !== 'undefined'){
                        func();
                    }
                });
            }

            /**
             * 判断是否为空
             * @param item
             * @returns {boolean}
             */
            function adjustEmpty(item) {
                if(typeof(item)==='undefined'
                    || item == null || item === '') {
                    return true;
                }
                return false;
            }

            /**
             * 校验参数
             */
            function adjustParams() {
                if(adjustEmpty($scope.teamInfo.teamName)){
                    inform.common("请填写小组名称");
                    $scope.flag = false;
                    return ;
                }
                if($scope.teamInfo.teamName.length >= 50){
                    inform.common("小组名称过长，最长50位，请重新填写");
                    $scope.flag = false;
                    return ;
                }
                if(adjustEmpty($scope.teamInfo.teamManagerId)){
                    inform.common("请选择组长");
                    $scope.flag = false;
                    return ;
                }
                if($scope.teamInfo.members && $scope.teamInfo.members.length === 1 && $scope.teamInfo.teamManagerId===$scope.teamInfo.members){
                	inform.common("请选择组长以外的小组成员");
                    $scope.flag = false;
                    return ;
                }
                $scope.flag = true;
            }


            /**
             * 结构树
             * *************************************************************
             */


            /**
             * 获取左侧树节点
             * @param treeId   树id
             * @param treeNode 树节点对象
             */
            function nodeSelect(treeId, treeNode) {
                //清空复选框
                $scope.zerochecked = false;
                $scope.onechecked = false;

                //隐藏右键菜单
                $("#rMenu").hide();

                //如果点击的不是小组，不作任何操作
                if(!treeNode.isGroup){
                    $scope.showFlag = false;
                    getEmployeesByOrgId($scope.departmentId);
                    return;
                }

                //判断权限，无权限则置灰小组信息修改框
                if(!adjustPermission(permissionObject,treeNode)){
                    $("#teamName").attr("disabled","disabled");
                    $("#teamManager").attr("disabled","disabled");
                    $("#updateButton").attr("disabled","disabled");
                }else{
                    $("#teamName").removeAttr("disabled");
                    $("#teamManager").removeAttr("disabled");
                    $("#updateButton").removeAttr("disabled");
                }
                $scope.membersFlag = true;
                //显示小组信息
                $scope.showFlag = true;
                //不显示新增
                $scope.insertFlag = false;


                //根据不同部门，重新查询员工信息
                getEmployeesByOrgId(treeNode.parentId);

                var id = treeNode.id + '';
                //去掉添加在id后面的逗号，并查询小组信息
                var params = {
                    'id':id.split(',')[0]
                };
                teamService.selectTeamAndMemberInfo(params).then(function(data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.teamInfo =data.data;
                        var members = '';
                        angular.forEach($scope.teamInfo.members,function (res) {
                            members = members + employeeMap[res] + ',';
                        });
                        $scope.membersForNoPermission = members.substr(0,members.length-1);
                        //将组长工号转化为名称
                        $scope.teamManager = employeeMap[$scope.teamInfo.teamManagerId];
                    } else {
                        common(data.message);
                    }
                }, function() {
                    common(Trans("tip.requestError"));
                });
            }

            /**
             * 右键点击事件
             * @param event  事件
             * @param treeId 树的id
             * @param treeNode 树的节点
             */
            function onRightClick(event, treeId, treeNode) {
                //展示右键菜单
                $("#rMenu").show();
                //判断权限
                if(typeof (treeNode.level)==='undefined' || treeNode.level==null){
                    return;
                }
                if(treeNode.level === 1 && !adjustPermission(permissionObject,treeNode)){
                    return;
                }
                if(treeNode.level === 0 && !adjustPermission(permissionObject,treeNode)){
                    return;
                }
                if(treeNode.level === 2 && !adjustPermission(permissionObject,treeNode)){
                    return;
                }

                //显示小组信息
                $scope.showFlag = true;

                //判断节点类型，部门：隐藏删除，小组：隐藏新增
                if (!treeNode.isGroup) {
                    $("#m_del").hide();
                    $("#m_add").show();
                } else {
                    $("#m_add").hide();
                    $("#m_del").show();
                    //将小组id存起来
                    $scope.teamId = treeNode.id.split(',')[0];
                }

                //将部门id
                $scope.departmentId = treeNode.orgId;

                //展示右键菜单
                showRMenu(event.clientX, event.clientY);
            }


            /**
             * 展示右键菜单
             * @param x 右键菜单的x轴位置
             * @param y 右键菜单的Y轴位置
             */
            function showRMenu(x, y) {
                $("#rMenu ul").show();
                y += document.body.scrollTop;
                x += document.body.scrollLeft;
                $("#rMenu").css({"top":y+"px", "left":x+"px", "visibility":"visible"});
            }

            /**
             * 右键菜单——新增小组
             */
            $scope.addTeam = function (treeNode) {
                $("#rMenu").css({"visibility" : "hidden"});
                //清空小组及成员信息,并为部门赋初值
                $scope.teamInfo ={
                    'teamManagerId':$scope.departmentId
                };
                //去掉只读
                $("#teamName").removeAttr("disabled");
                $("#teamManager").removeAttr("disabled");
                $scope.membersFlag = true;

                //显示小组信息
                $scope.showFlag = true;

                //清空复选框
                $scope.zerochecked = false;
                $scope.onechecked = false;

                //设置按钮为新增
                $scope.insertFlag = true;
                //重新获取当前部门的人员
                getEmployeesByOrgId($scope.departmentId);
            };

            /**
             * 右键菜单——删除小组
             */
            $scope.removeTeam = function () {
                $("#rMenu").css({"visibility" : "hidden"});
                inform.modalInstance("确定要删除此小组吗？").result.then(function() {
                    deleteTeam();
                });

            };

            /**
             * 全选框
             * *************************************************************
             */

            /**
             * 组成员——全选变动后，全选，清空框均不被选中
             */
            $scope.notSelect = function () {
                if ($scope.zerochecked) {
                    $scope.zerochecked = false;
                }
                if ($scope.onechecked) {
                    $scope.onechecked = false;
                }
            };

            /**
             * 组成员——全选
             */
            $scope.selectAll = function () {
                $scope.teamInfo.members = [];
                angular.forEach($scope.employeesList, function (item) {
                    if ($scope.teamInfo.members.indexOf(item.employeeId) === -1) {
                        $scope.teamInfo.members.push(item.employeeId);
                    }
                });
                $scope.zerochecked = true;
                $scope.onechecked = false;
            };
            /**
             * 组成员——清空
             */
            $scope.clearAll = function () {
                $scope.teamInfo.members = [];
                $scope.onechecked = true;
                $scope.zerochecked = false;
            };

            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        }
    ]);
})();

//# sourceURL=js/services/office/projectWeekly/projectProblemService.js
(function() {
    'use strict';
    app.factory('projectProblemService', projectProblemService);
    projectProblemService.$inject = ["HttpService", '$rootScope'];

    function projectProblemService(HttpService, $rootScope) {
        var service = {
            getProjectProblemInfo: getProjectProblemInfo,
            getUpdateInfo: getUpdateInfo,
            deleteProjectProblemInfo: deleteProjectProblemInfo,
            addProjectProblemInfo: addProjectProblemInfo,
            updateProjectProblemInfo: updateProjectProblemInfo
        };
        return service;
        /**
         * 分页查询风险信息
         */
        function getProjectProblemInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectProblem/getProjectProblemInfo', urlData);
        }
        /**
         * 根据id重新查询低级质量问题
         */
        function getUpdateInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectProblem/getUpdateInfo', urlData);
        }
        /**
         * 删除低级质量问题
         */
        function deleteProjectProblemInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectProblem/deleteProjectProblemInfo', urlData);
        }
        /**
         * 新增低级质量问题
         */
        function addProjectProblemInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectProblem/addProjectProblemInfo', urlData);
        }
        /**
         * 修改低级质量问题
         */
        function updateProjectProblemInfo(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'ProjectProblem/updateProjectProblemInfo', urlData);
        }
    }
})();
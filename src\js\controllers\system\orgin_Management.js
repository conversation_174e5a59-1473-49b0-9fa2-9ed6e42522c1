/*
 * @Author: fubaole
 * @Date:   2017-10-10 17:25:38
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-26 11:01:58
 */
(function() {
  'use strict';
  app.controller("orgin_Management", ['FileUploader', '$rootScope', '$scope', '$log', 'inform', '$modal', 'Trans', 'SystemService', 'ConfigService','AgreeConstant',
    function(FileUploader, $rootScope, $scope, $log, inform, $modal, Trans, SystemService, ConfigService,AgreeConstant) {
      $scope.limitList = AgreeConstant.limitList; // 正则校验配置
      $scope.treeData = []; // 存放树结构数据
      $scope.getData = getData; // 获取数据
      $scope.getData();
      $scope.popModal = popModal; // 修改弹框
      $scope.onSubmitModal = onSubmitModal; // 新增修改操作
      $scope.open = open; // 删除弹框信息
      $scope.deleteItem = deleteItem; //删除组织机构
      // var deleteItem = deleteItem; //删除组织机构

      // 排序
      $scope.title = 'orderBy';
      $scope.order = order;

      // 设置侧边的高度,随窗口变动
      inform.autoHeight();
      window.onresize = inform.autoHeight;

      // 左侧树配置
      var Leftsetting = angular.copy(ConfigService.dataAndCb);
      Leftsetting.callback.beforeClick = nodeSelect; // 点击节点前回调
      Leftsetting.callback.onExpand = zTreeOnExpand; // 展开节点回调
      Leftsetting.callback.onCollapse = zTreeOnCollapse; // 折叠节点回调

      // 排序
      function order(str) {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      }

      // 获取树数据
      function getData() {
        SystemService.getAllOrg()
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              angular.forEach(data.result, function(res, index) {
                var jsonTree = {
                  "id": res.orgId,
                  "pId": res.parentId,
                  "name": res.orgName,
                  "open": res.orgId===AgreeConstant.treeRootNode ? true : false
                };
                data.result[index] = angular.extend(jsonTree, res);
                $scope.treeData.push(data.result[index]);
              });
              $scope.treeData = inform.unique($scope.treeData);
              $.fn.zTree.init($("#leftTree"), Leftsetting, $scope.treeData);
              // $scope.result = [];
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 获取当前节点数据
      function isLastData(id) {
        SystemService.getOrganization(id)
          .then(function(res) {
            if (res.code===AgreeConstant.resultCode) {
              $scope.result = res.result;
            } else {
              inform.common(res.message);
            }
          }, function() {
            inform.common(Trans('tip.requestError'));
          });
      }

      // 点击树节点调用
      function nodeSelect(treeId, treeNode) {
        $scope.selectedId = treeNode.orgId;
        isLastData(treeNode.orgId);
      }

      // 展开操作
      function zTreeOnExpand(event, treeId, treeNode) {
        angular.forEach($scope.treeData, function(res, index) {
          if (res.orgId===treeNode.orgId) {
            res.open = true;
          }
        });
      }

      // 折叠操作
      function zTreeOnCollapse(event, treeId, treeNode) {
        angular.forEach($scope.treeData, function(res, index) {
          if (res.orgId===treeNode.orgId) {
            res.open = false;
          }
        });
      }

      // 修改弹框信息
      function popModal(item, str) {
        if (str) {
          $scope.parent = item;
          $scope.flag = false;
          $scope.perData = {
            orgName: "",
            orgCode: "",
            orgDesc: ""
          };
        } else {

          $scope.parent = null;
          $scope.flag = true;
          $scope.perData = angular.copy(item);
        }
      }

      // 新增修改组织信息
      function onSubmitModal() {
        if ($scope.parent) {
          $scope.perData.parentId = $scope.parent.orgId; // 新增
        }
        SystemService.saveOrupdateOrganization($scope.perData)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              inform.common(Trans('tip.saveSuccess'));
              $("#edit_orgin").modal("hide");
              $scope.treeData = [];
              getData();
              isLastData($scope.selectedId);
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans('tip.requestError'));
          });
      }

      // 根据组织机构id删除组织机构
      function deleteItem(item) {
        SystemService.removeOrganization(item.orgId)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              inform.common(Trans('tip.delSuccess'));
              $scope.treeData = [];
              getData();
              isLastData($scope.selectedId);
            } else {
              inform.common(data.message);
            }
          }, function() {
            inform.common(Trans('tip.requestError'));
          });
      }

      // 删除功能
      function open(item) {
        var modalInstance = $modal.open({
          templateUrl: 'myModalContent.html',
          controller: 'ModalInstanceCtrl',
          size: "sm",
          resolve: {
            items: function() {
              return Trans("common.deleteTip");
            }
          }
        });
        modalInstance.result.then(function() {
          if (item) {
            SystemService.getOrganizationListByParentId(item.orgId) // 查询有没有子级
              .then(function(res) {
                if (res.code===AgreeConstant.resultCode) {
                  // 最后一级
                  if (res.result.length===0) {
                    deleteItem(item); // 没子级直接删除
                  } else {
                    inform.common(Trans("common.deleteChild"));
                  }
                } else {
                  inform.common(res.message);
                }
              }, function() {
                inform.common(Trans('tip.requestError'));
              });
          }
        });
      }

      // ---------导入功能------------
      var uploader = $scope.uploader = new FileUploader({
        url: '',
        queue: []
      }); /*实例化一个FileUploader对象*/

      // 上传回调
      uploader.onSuccessItem = function(fileItem, response, status, headers) {
        console.info('onSuccessItem', fileItem, response, status, headers);
      };
      uploader.onErrorItem = function(fileItem, response, status, headers) {
        console.info('onErrorItem', fileItem, response, status, headers);
      };
      // ---------导入功能------------

    }
  ]);
})();
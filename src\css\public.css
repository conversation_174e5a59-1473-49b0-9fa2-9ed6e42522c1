
/*解决导航条 左侧显示问题*/
/*#nav>.nav-sub .nav-sub{
    height: auto !important;
    margin-left: 0;
    overflow: auto;
    opacity: 1;
}*/
/*折叠左侧菜单 三级菜单显示问题*/
#nav {
    font-size: 12px;
}
#nav .navi {
    background-color: #42485B;
}
#nav>.nav-sub .nav-sub{
    display: none;
}
/*折叠左侧菜单 三级菜单显示问题*/
#nav>.nav-sub .two-flag:hover .nav-sub{
    display: block;
}
/*折叠左侧菜单 三级菜单显示问题*/
#nav>.nav-sub .two-flag .nav-sub li a{
    padding-left: 55px !important;
}

/*解决导航条 上侧三级显示问题*/
.app-aside-dock .app-aside .navi > ul > li .nav-sub .nav-sub{
    position: relative;
}
.app-aside-dock .app-aside .navi > ul > li .nav-sub .nav-sub,
.app-aside-folded .app-aside .navi > ul > li .nav-sub .nav-sub {
    display: none;
}
.app-aside-dock .app-aside .navi > ul > li .nav-sub .two-flag:hover .nav-sub,
.app-aside-folded .app-aside .navi > ul > li .nav-sub .two-flag:hover .nav-sub {
    display: block;
}
.navi ul.nav.nav-sub.dk li.two-flag ul li a {
    padding-left: 85px;
}

/* 展开上侧菜单，三级菜单显示位置*/
.app-aside-dock .app-aside .navi > ul > li .nav-sub .nav-sub a {
    padding-left: 50px !important;
}
/*  折叠上侧菜单，三级菜单显示位置 */
.app-aside-folded .app-aside .navi > ul > li .nav-sub .nav-sub a {
    padding-left: 54px !important;
}
@media (max-width: 991px) {
    .navi ul.nav.nav-sub.dk li.two-flag ul li a {
        padding-left: 85px !important;
    }
}
.app-content-body,
.app-content-body .btn {
    font-size: 12px;
}
/*表格*/
.table {
    margin-bottom: 0px;
    border: 1px solid #e1e6eb;
}
.table.dataTable > thead {
    background-color: #f5f6fa;
}
.table > thead > tr > th {
    padding: 8px 8px;
    font-weight: normal;
    color: #999;
    border-bottom: 1px solid #E1E6EB;
    background-color: #F5F6FA;
    vertical-align: middle;
}
.table > tbody > tr > td.text-right{
    border-top: 1px solid #eaeff0;
}
.table-striped > tbody > tr:nth-child(odd) {
    background-color: #f9fafc;
}
.table-hover > tbody > tr:hover {
    background-color: #f7f7f7;
}
table.dataTable thead .sorting,
table.dataTable thead .sorting_asc,
table.dataTable thead .sorting_desc{
  cursor: pointer;
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:after{
  font-family: FontAwesome;
  color: #ccc;
  position: relative;
  font-weight: normal;
  left: 8px;
}

table.dataTable thead .sorting:after{content:"\f0dc";}
table.dataTable thead .sorting_asc:after{content:"\f0de";}
table.dataTable thead .sorting_desc:after{content:"\f0dd";}

table.dataTable thead .sorting:after, table.dataTable thead .sorting_asc:after, table.dataTable thead .sorting_desc:after {
    color: #888;
}
.btn:hover, .btn:focus, .btn.focus {
    text-decoration: none;
}
.alert {
    margin-bottom: 0px;
}
.breadcrumb {
    margin-bottom: 0px;
    background-color: #f5f6fa;
    border-radius: 0;
    border-color: #edf1f2;
}
.pagination > .active > a, .pagination > .active > span, .pagination > .active > a:hover, .pagination > .active > span:hover, .pagination > .active > a:focus, .pagination > .active > span:focus {
    background-color: #00c1de;
    border-color: #00c1de;
}
.col-sm-2 {
    padding-right: 0px;
}
@media (min-width: 768px){
    .modal-dialog {
        margin: 100px auto;
    }
}
.table-responsive {
    width: 100%;
}
/*导入 导出 新增 按钮*/
.buttonModule {
    display: inline-block;
    float: right;
    margin-right: 24px;
}
.nav .summary{
    display: inline-block;
    line-height: 40px;
    border: 1px solid transparent;
    padding: 0 20px;
}
.buttonModule a{
    display: block;
    line-height: 32px;
    padding: 0 6px;
    float: left;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin: 2px 4px 4px;
}
.buttonModule a.none{
    display: none;
}
.buttonModule a:hover{
    color: #58666e !important;
    background-color: #edf1f2;
    border-color: #c7d3d6;
}
.buttonModule a i{
    display: inline-block;
    width: 22px;
    margin: 0;
    font-size: 12px;
    text-align: center;
    vertical-align: middle;

}
.button-hover i,td a i{
    -webkit-transition: all 0.2s;
    -o-transition: all 0.2s;
    transition: all 0.2s;
}
.button-hover:hover i,td a:hover i{
    text-decoration: none;
    -moz-transform: scale(1.4);
    -webkit-transform: scale(1.4);
    -o-transform: scale(1.4);
    -ms-transform: scale(1.4);
    transform: scale(1.4);
    color: #000;
}

/*导航条*/
.nav-tabs > li > a {
    border-top: 2px solid transparent;
}

.search .w-xs{
    width: 110px;
}
/*分页*/
.pagination {
    margin: 0;
}

.wrapper-b-15{
    padding:15px 15px 0;
}
/*颜色*/
.styleGreen {
    color: green;
}
.styleRed {
    color: red;
}
.green,.green * {
    color: #69aa46 !important;
}
.red,.red * {
    color: #dd5a43 !important;
}
.orange {
    color: #ff892a !important;
}
.grey {
    color: #777777 !important;
}

/*表格*/
td a{
    color: #00c1de;
}
td a i{
    color: #777;
    font-size: 16px!important;
    margin:0 2px;
}

/*搜索框*/
.form-control {
    height: 32px;
    border-radius: 0px;
    padding: 6px;
    -webkit-transition: none;
    transition: none;
    font-size: 12px;
}
.panel.search{
    margin: 15px 15px 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    border-bottom: 1px solid #ddd;
}
.panel.search.table-opt {
    border-bottom: none;
}
.panel.search .title {
    padding-left: 8px;
    border-left: 2px solid #00c1de;
}
.panel.search label{
    line-height: 34px;
}
.panel.search label.w-xxs30{
    text-align: center;
}
.hbox select.col{
    width:100%;
    border: 1px solid #ccc;
}
.search-list a>i ,
.search-list a>span >i {
    color: #00c1de;
    padding: 0 5px 0 15px;
}
.search-list a:hover>span  {
    color: #00c1de;
}
.unfirst-row {
    margin-top: 15px;
}
.w-c-850{
    width:850px;
}

/*弹窗*/
.modal{
    z-index: 1051;
}
.modal-backdrop{
    background-color: #111;
    z-index: 1050;
    opacity: 0.5!important;
}
.modal-footer {
    padding: 5px 0 15px;
    text-align: center;
     border-top: 0;
}
.modal-content{
    border-radius: 5px;
    overflow: hidden;
}
.modal-header .close {
    margin-top: 0px;
    color: #fff;
    opacity: 0.8;
}
.modal-footer .btn + .btn {
    margin-left: 15px;
}
/*时间搜索框*/
.form-control.time{
    width: 100%;
    display: inline-block;
}
/*下侧所有保存按钮*/
.save-footer{
    text-align: center;
}
.save-footer .btn + .btn {
    margin-left: 15px;
}
.form-vertical{
    color: #f05050;
    position: absolute;
    right: 40px;
    top: 0px;
}
.form-unit{
    padding-right: 40px;
}
.form-unit label{
    position: absolute;
    top: 6px;
    right: 30px;
}
.form-unit span{
    right: 70px;
}

@media (min-width: 768px){
    .sm-22 {
        width: 22%;
    }
    .form-group div.col{
        padding-left: 15px;
    }
    .form-horizontal .control-label {
         padding-top: 0px;
    }
    .modal-body .hbox.w50{
        float: left;
        width: 50%;
    }
    .modal-body .hbox.w50{
        float: left;
        width: 50%;
    }
    .modal-body .hbox.w33{
        float: left;
        width: 33.333%;
    }
    .modal-body .col{
        padding-left: 4px !important;
    }
}

.search>div>div{
    padding:0;
}
.font-white{
    color: #fff!important;
}

.select {
    width: 70% !important;
}

/*公用样式*/
.textDetai {
    border: 1px solid #ccc;
    width: 306px;
    height: 30px;
    line-height: 30px;
    margin-left: 12px;
}
.w-style{
    width: 75%;
    border-color: #ddd;
    height: 34px;
}
.bitian{
    position: absolute;
    right: 4px;
    top: 0;
    line-height: 32px;
    color: red;
}

/*服务管理开始*/
.resource .col-md-4 {
    padding: 0;
}
.resource .col-md-4:nth-of-type(2) {
    border-right: 1px solid #e1e6eb;
    border-left: 1px solid #e1e6eb;
}
.resource-title {
    background-color: #f9f9f9;
    height: 36px;
    line-height: 36px;
    padding-left:14px;
}
.service-title .title{
    padding-left: 8px;
    text-align: left;
    line-height: 1.4;
    border-left: 2px solid #00c1de;
}
.break {
    white-space: normal;
    word-break:break-all;
}

.bg-black > pre{
    background-color:#373d41;
    color:#f1bb5a;
}
.my-api {
    padding: 5px 15px;
    margin: 10px;
    margin-bottom: 0;
    border: 1px solid #bce8f1;
}
.top-zero {
    margin-top: 0;
    border-top: none;
    margin-bottom: 10px;
    background-color: #d9edf7;
}
.api-item a {
    color: #31708f;
}
.api-item a:hover {
    text-decoration:underline;
}
.my-api .blue-title {
    color: #0f6ab4;
}
.padder-b-sm {
    padding-bottom: 10px;
}

.mytable{
  table-layout:fixed;/* 只有定义了表格的布局算法为fixed，下面td的定义才能起作用。 */
}
.mytable td{
  word-break:keep-all;/* 不换行 */
  white-space:nowrap;/* 不换行 */
  overflow:hidden;/* 内容超出宽度时隐藏超出部分的内容 */
  text-overflow:ellipsis;/* 当对象内文本溢出时显示省略标记(...) ；需与overflow:hidden;一 起使用。*/
}

/*表格上侧的操作开始*/
.dropdown-btn {
    position: relative;
}
.setting-item.dropdown-menu{
    right:0;
    left: inherit;
    display: block;
    padding: 0;
    margin: 0;
    font-size: 12px;
}
.setting-item .is-selete {
    margin-bottom: 10px;
}
.setting-item .is-selete:last-child {
    margin-bottom: 0px;
}
.bottomline {
    border-bottom: 1px solid #eaeff0;
}
/*表格上侧的操作结束*/

/* 进度条文字颜色*/
.precent-color {
    color: #666;
}

/*树*/
#aside {
    -position: relative;
}
#aside .pack-up {
    position: absolute;
    top: 50%;
    right: -1px;
    width: 15px;
    height: 32px;
    background-color: #fff;
}
.bg-tree {
    background-color: #d9dee4;
    padding: 9px;
}

/*日期插件*/
.input-date-btn {
    position: absolute;
    top: 0;
    right: 15px;
    line-height: 1;
}
.input-date-btn >.btn {
    width: 32px;
    line-height: 30px;
    padding: 0;
}

/*header样式开始*/
.nav .open > a,
.nav .open > a:hover,
.nav .open > a:focus {
    background-color: #eee;
    border-color: #428bca;
}
.navbar-nav > li > a {
    padding-left: 16px;
    padding-right: 16px;
}
.bg-black .navbar-nav > li > a:hover,
.bg-black .navbar-nav > li > a:focus ,
.bg-black .navbar-nav > a:hover,
.bg-black .navbar-nav > a:focus {
    background-color: #2a2f32;
    color: #fff;
    border-color: #2a2f32;
}
.bg-dark .navbar-nav > li > a:hover,
.bg-dark .navbar-nav > li > a:focus,
.bg-dark .navbar-nav > a:hover,
.bg-dark .navbar-nav > a:focus{
    color: #fff;
    background-color: #000;
    border-color: #000;
}
.bg-primary .navbar-nav > li > a:hover,
.bg-primary .navbar-nav > li > a:focus,
.bg-primary .navbar-nav > a:hover,
.bg-primary .navbar-nav > a:focus{
    color: #fff;
    background-color: #6658b8;
    border-color: #6658b8;
}
.bg-info .navbar-nav > li > a:hover,
.bg-info .navbar-nav > li > a:focus,
.bg-info .navbar-nav > a:hover,
.bg-info .navbar-nav > a:focus{
    color: #fff;
    background-color: #17b2e2;
    border-color: #17b2e2;
}
.bg-success .navbar-nav > li > a:hover,
.bg-success .navbar-nav > li > a:focus,
.bg-success .navbar-nav > a:hover,
.bg-success .navbar-nav > a:focus{
    color: #fff;
    background-color: #038478;
    border-color: #038478;
}

.bg-danger .navbar-nav > li > a:hover,
.bg-danger .navbar-nav > li > a:focus,
.bg-danger .navbar-nav > a:hover,
.bg-danger .navbar-nav > a:focus{
    color: #fff;
    background-color: #ebaa01;
    border-color: #ebaa01;
}
.bg-white-only .navbar-nav > li > a:hover,
.bg-white-only .navbar-nav > li > a:focus,
.bg-white-only .navbar-nav > a:hover,
.bg-white-only .navbar-nav > a:focus{
    color: #363f44;
    background-color: #eee;
    border-color: #eee;
}

.bg-black .nav > li > a ,
.bg-black .nav > a {
  border-right: 1px solid #2a2f32;
}
.bg-dark .nav > li > a ,
.bg-dark .nav > a {
  border-right: 1px solid #000;
}
.bg-primary .nav > li > a ,
.bg-primary .nav > a {
  border-right: 1px solid #6658b8;
}
.bg-info .nav > li > a ,
.bg-info .nav > a {
  border-right: 1px solid #17b2e2;
}
.bg-success .nav > li > a ,
.bg-success .nav > a {
  border-right: 1px solid #038478;
}
.bg-danger .nav > li > a ,
.bg-danger .nav > a {
  border-right: 1px solid #ebaa01;
}
.bg-white-only .nav > li > a ,
.bg-white-only .nav > a {
  border-right: 1px solid #eee;
}
.navbar-nav > .btn {
    margin: 0;
    padding: 14px 16px;
}

/*header样式结束*/


/*菜单样式开始*/
.bg-black .nav .nav > li > a:hover,
.bg-black .nav .nav > li > a:focus,
.bg-black .nav .nav > .two-flag > a:hover,
.bg-dark .nav .nav > li > a:hover,
.bg-dark .nav .nav > li > a:focus,
.bg-dark .nav .nav > .two-flag > a:hover{
  background-color: #42485B;
}

.bg-black .nav .nav > .two-flag > a:focus,
.bg-dark .nav .nav > .two-flag > a:focus{
  background-color: #00c1de;
}
.bg-black .nav .nav > li:not(.two-flag).active > a,
.bg-dark .nav .nav > li:not(.two-flag).active > a{
  background-color: #00c1de;
}

/*菜单样式结束*/


/*功能导航开始*/
.dropdown-menu {
    color: #333 !important;
}
.bg-dropdown {
    background-color: #fff;
}
.bg-dropdown a,
.bg-dropdown .text-muted{
    color: #333 !important;
}
.list-unstyled li {
    background-color: #f7f8fa;
    padding-left: 10px;
}

.list-unstyled .bgc-unstyled {
    background-color: #FFF;
    margin-bottom: 0;
    padding: 0;
    margin-left: 10px;
}

.list-unstyled li:hover {
    background-color: #00c1de;
}

.list-unstyled .bgc-unstyled:hover {
    background-color: #FFF;
}

.list-unstyled li:hover a ,
.list-unstyled li:hover .text-muted {
    color: #fff !important;
}
/*功能导航结束*/

/*页码跳转到开始*/
.goToPage {
    float: right;
    padding: 0 15px;
}
.goToPage >input {
    display: inline-block;
    width: 80px;
    height: 29px;
    margin-right: 10px;
}
.goToPage > button {
    color: #333;
    padding: 5px 8px;
    border: 1px solid #ddd;
    background-color: #f7f7f7;
    vertical-align:top;
}
.goToPage > button:hover {
    background-color: #fff;
}
/*页码跳转到结束*/

/*删除弹框图标*/
.tip-icon {
    position: absolute;
    top: 10px;
    left: 25px;
    width: 30px;
    height: 30px;
    background:url("../library/component/skin/default/icon.png") no-repeat;
    background-position:0 0;
}


/*右侧弹出框样式开始*/
.modal-right {
    position: absolute;
    top: -1px;
    right: -1px;
    width:400px;
    height: 100%;
    margin: 0;
}
.modal-right .modal-content {
    border-radius: 0;
    height: 100%;
}
.modal-right .modal-body {
    overflow-y: auto;
    height: 88%;
    padding-bottom: 12%;
}
.modal-right .modal-footer {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 400px;
    margin-left: -200px;
    padding-top: 15px;
    border-top: 1px solid #e5e5e5;
    background-color: #fff;
}
.modal.fade .modal-dialog.modal-right {
    -webkit-transition: -webkit-transform .3s ease-out;
    -o-transition: -o-transform .3s ease-out;
    transition: transform .3s ease-out;
    -webkit-transform: translate(125%, 0);
    -ms-transform: translate(125%, 0);
    -o-transform: translate(125%, 0);
    transform: translate(125%, 0);
}
.modal.in .modal-dialog.modal-right {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
}
/*右侧弹出框样式结束*/

/*树结构样式开始*/

.tree-node {
  min-height: 24px;
  line-height: 24px;
  margin-left: 24px;
  min-width: 24px;
}
.tree-anchor {
  line-height: 24px;
  height: 24px;
}
.tree-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
}
.tree-icon:empty {
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
}
.tree-rtl .tree-node {
  margin-right: 24px;
}
.tree-wholerow {
  height: 24px;
}
.tree-icon {
  background-image: url("../img/32px.png");
}
.tree-node {
  background-position: -292px -4px;
  background-repeat: repeat-y;
}
.tree-last {
  background: transparent;
}
.tree-open > .tree-ocl {
  background-position: -132px -4px;
}
.tree-closed > .tree-ocl {
  background-position: -100px -4px;
}
.tree-leaf > .tree-ocl {
  background-position: -68px -4px;
}

.tree-themeicon {
  background-position: -260px -4px;
}
.tree-no-dots .tree-node,
.tree-no-dots .tree-leaf > .tree-ocl {
  background: transparent;
}
.tree-no-dots .tree-open > .tree-ocl {
  background-position: -36px -4px;
}
.tree-no-dots .tree-closed > .tree-ocl {
  background-position: -4px -4px;
}
/*树结构样式结束*/


(function () {
    'use strict';
    app.controller("workingHoursManagementController", ['$scope', '$state', 'comService', '$rootScope', 'inform', 'Trans', 'AgreeConstant', 'workingHoursService', '$stateParams', 'LocalCache', '$modal', '$http',
        function ($scope, $state, comService, $rootScope, inform, Trans, AgreeConstant, workingHoursService, $stateParams, LocalCache, $modal, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 路由中传参数，区分是团队还是项目信息
            $scope.type = $stateParams.type;
            //获取缓存
            $scope.formRefer = {};
            if (Object.keys(LocalCache.getObject('workingHoursManagement_formRefer')).length > 0) {
                $scope.formRefer = LocalCache.getObject('workingHoursManagement_formRefer');
            } else {
                // 如果没有缓存调用默认方法
                reset();
            }
            LocalCache.setObject('workingHoursManagement_formRefer', {});
            // 初始化分页数据
            $scope.pages = inform.initPages();
            //项目状态下拉框数据源
            $scope.statusSelect = [{
                value: 'closed',
                label: '已关闭'
            }, {
                value: 'doing',
                label: '进行中'
            }, {
                value: 'suspended',
                label: '已挂起'
            }];
            $scope.statusMap = {
                'closed': '已关闭',
                'doing': '进行中',
                'suspended': '已挂起'
            }
            //拆分标记
            $scope.splitTypeSelect = ['待拆分','已拆分'];

            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //初始化查询
            initPages();
            //获取数据
            $scope.getData = getData;
            getData();
            $scope.reset = reset;
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 重置
             */
            function reset() {
                $scope.formRefer = {};
                $scope.formRefer.status = 'closed';
                $scope.formRefer.manager = LocalCache.getSession('employeeName');
                $scope.formRefer.splitType = '待拆分';
                var date = new Date();
                date.setMonth(date.getMonth() - 12);
                $scope.formRefer.startTime = inform.format(date, 'yyyy-MM-dd');
            }

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
            }

            /**
             * 初始化
             */
            function initPages() {
                //获取产品线
                $scope.projectLine = [];
                comService.queryEffectiveParam('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.projectLine = data.data;
                    }
                });
                //获取部门信息
                $scope.departmentSelect = [];
                comService.getOrgChildren('D010053').then(function (data) {
                    $scope.departmentSelect = comService.getDepartment(data.data);
                });
            }

            /**
             * 获取kpi查询信息
             */
            function getData(pageNum) {
                var urlData = {
                    'orgName': $scope.formRefer.orgName,
                    'lineName': $scope.formRefer.lineName,
                    'tname': $scope.formRefer.tname,
                    'manager': $scope.formRefer.manager,
                    'status': $scope.formRefer.status,
                    'splitType':$scope.formRefer.splitType,
                    'startDate':$scope.formRefer.startTime,//禅道项目开始时间
                    'endDate':$scope.formRefer.endTime,//禅道项目开始时间
                    'currentPage': pageNum,
                    'pageSize': $scope.pages.size,
                };
                workingHoursService.getData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            //信息
                            $scope.infoList = data.data.list;
                            // 分页信息设置
                            $scope.pages.total = data.data.total;           // 页面数据总数
                            $scope.pages.star = data.data.startRow;         // 页面起始数
                            $scope.pages.end = data.data.endRow;            // 页面结束数
                            $scope.pages.pageNum = data.data.pageNum;       //页号
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            /**
             * 详情方法
             */
            $scope.workingHoursSplit = function (m) {
                // 查询条件放缓存
                LocalCache.setObject('workingHoursManagement_formRefer', $scope.formRefer);
                // 表中的行信息放入缓存
                LocalCache.setObject('workingHoursSplit_formRefer', m);
                // 跳转
                $state.go("app.office.workingHoursSplit");
            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        }
    ]);
})();
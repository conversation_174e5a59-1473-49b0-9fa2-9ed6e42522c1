(function () {
    'use strict';
    app.factory('testReportProductService', testReportProductService);
    testReportProductService.$inject = ["HttpService", '$rootScope'];

    function testReportProductService(HttpService, $rootScope) {
        var service = {
        	getData:getData
        };
        return service;
        /**
         * 获取汇总页面表单
         */
        function getData(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'testReport/getData', urlData);
        }
        
    }
})();
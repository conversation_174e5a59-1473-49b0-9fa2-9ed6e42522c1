
(function () {
    app.controller("projectTaskDetailsController", ['comService','$rootScope', '$scope','$state','$stateParams','$modal','projectTaskService','inform','Trans','AgreeConstant','LocalCache',
        function (comService,$rootScope, $scope,$state,$stateParams, $modal,projectTaskService,inform,Trans,AgreeConstant,LocalCache) {
    	
       	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */ 
    	$scope.isSprintVersion=$stateParams.isSprintVersion;
    	// 页面分页信息
        $scope.pages = {
            pageNum: '',	// 分页页数
            size: '',		// 分页每页大小
            total: ''		// 数据总数
        };
		//保存查询出的项目信息
    	$scope.itemList = [];
    	// 分页
    	$scope.pages = inform.initPages(); // 初始化分页数据
    	$scope.number = $scope.pages.pageNum;
		$scope.getData = getData; 
    	getData(1);
		//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
	  	/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
	  	
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */	
		/**
		 * 页面初始化
		 */

		/**
	     * 设置列表的高度
	     */
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 180);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 50);
 		}
 		
 		//获取所有数据以分页的形式
        function getData(pageNum) {
        	var urlData = {};

     		//禅道项目查询
     			urlData = {
         				'proProjectId':$stateParams.id,			//版本id
         				'currentPage':pageNum,// 当前页数
        		        'pageSize':$scope.pages.size// 每页显示条数
         			};

     		projectTaskService.getTaskInfoByProId(urlData).then(function(data) {
    					if (data.code===AgreeConstant.code) {
    						// 项目详情
    						$scope.itemList = data.data.list;
    						//项目名（看是不是禅道项目，如果不是，取平台项目名）
    	     				$scope.name=$scope.itemList[0].proName;
    						if ($scope.itemList.length===0) {
    		                    inform.common(Trans("tip.noData"));
    	    	                $scope.pages = inform.initPages();
    		                } else {
    		                    // 分页信息设置
    		                    $scope.pages.total = data.data.total;
    		                    $scope.pages.star = data.data.startRow;
    		                    $scope.pages.end = data.data.endRow;
    		                    $scope.pages.pageNum = data.data.pageNum;
    		                }
    					} else {
    						inform.common(data.message);
    					}
    				},
    				function(error) {
    					inform.common(Trans("tip.requestError"));
    				});
        }
    	/**
  		 * *************************************************************
  		 *              方法声明部分                                 结束
  		 * *************************************************************
  		 */
     	
	}]);
})();
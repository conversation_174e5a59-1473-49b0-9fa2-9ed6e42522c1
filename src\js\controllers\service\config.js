(function() {
  'use strict';
  app.controller("config", ['$rootScope', '$scope', '$filter', '$interval', '$stateParams', 'inform', 'Microservice', 'Trans',
    function($rootScope, $scope, $filter, $interval, $stateParams, inform, Microservice, Trans) {

      $scope.getRoutes = getRoutes;
      $scope.getConfigprops = getConfigprops;
      $scope.getConfigenv = getConfigenv;
      $scope.changeRouteInfo = changeRouteInfo;
      $scope.getRoutes();
      $scope.configTimer = 0;
      $scope.serviceType = "uaa";
      $scope.appName = "register";
      $scope.result = [{ "appName": "register" }];

      $scope.$watch('$scope.configTimer', function(newValue, oldValue) {
        if (newValue !== oldValue) {
          $interval.cancel($scope.interval);
          if (newValue !== '0') {
            $scope.interval = $interval(function() {
              // console.log($scope.timer);
              $scope.getRoutes();
              $scope.getConfigprops();
              $scope.getConfigenv();
            }, $scope.configTimer);
          }
        }
      });

      $scope.$on('$destroy', function() {
        $interval.cancel($scope.interval);
        //$interval.stop(interval);
      });

      function changeRouteInfo() {
        $scope.getConfigprops();
        $scope.getConfigenv();
      }
      // 获取应用
      function getRoutes() {
        Microservice.getRoutes()
          .then(function(data) {
            for (var i = 0; i < data.length; i++) {
              $scope.result.push(data[i]);
            }
            console.log(data);
            $scope.getConfigprops();
            $scope.getConfigenv();
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 获取数据
      function getConfigprops() {
        Microservice.getConfigprops($scope.appName)
          .then(function(data) {
            var properties = [];
            angular.forEach(data, function(data) {
              properties.push(data);
            });
            var orderBy = $filter('orderBy');
            $scope.configuration = orderBy(properties, 'prefix');

            console.log("getConfigprops:" + $scope.configuration);

          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

      function getConfigenv() {
        Microservice.getConfigenv($scope.appName)
          .then(function(data) {
            var properties = {};
            angular.forEach(data, function(val, key) {
              var vals = [];
              angular.forEach(val, function(v, k) {
                vals.push({ key: k, val: v });
              });
              properties[key] = vals;
            });
            $scope.allConfiguration = properties;
            console.log("getConfigenv:" + $scope.allConfiguration);
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }

    }
  ]);
})();
(function() {
    'use strict';
    app.factory('departmentSprintService', departmentSprintService);
    departmentSprintService.$inject=["HttpService",'$rootScope'];

    function departmentSprintService(HttpService,$rootScope){

        function getDeptSprintTotalInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptSprint/getDeptSprintTotalInfo',urlData);
        }

        function getDeptSprintTotalInfoByTeam(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptSprint/getDeptSprintTotalInfoByTeam',urlData);
        }

        function getDeptSprintInfoForTable(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptSprint/getDeptSprintInfoForTable',urlData);
        }

        function getDeptSprintCountForTable(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptSprint/getDeptSprintCountForTable',urlData);
        }

        return {
            getDeptSprintTotalInfo: getDeptSprintTotalInfo,
            getDeptSprintTotalInfoByTeam: getDeptSprintTotalInfoByTeam,
            getDeptSprintInfoForTable: getDeptSprintInfoForTable,
            getDeptSprintCountForTable: getDeptSprintCountForTable,
        };
    }
})();
(function () {
    'use strict';
    app.factory('reviewProblemService', reviewProblemService);
    reviewProblemService.$inject = ["HttpService", '$rootScope'];

    function reviewProblemService(HttpService, $rootScope) {
        var service = {
            getReviewProblem: getReviewProblem,
            insertProblem: insertProblem,
            updateProblem: updateProblem,
            deleteProblemById: deleteProblemById,
            selectByReviewAndJudge: selectByReviewAndJudge,
            uploadExcelByPath:uploadExcelByPath,
            toTask:toTask
        };
        return service;
        function toTask (data) {
            return HttpService.post($rootScope.getWaySystemApi+ 'reviewProblem/toTask', data);
        }
        /**
         * 根据文件地址上传文件
         * @param urlData 查询参数
         */
        function uploadExcelByPath(urlData,uploadUrl) {
            return HttpService.post($rootScope.getWaySystemApi + uploadUrl, urlData);
        }
        /**
         * 根据评审查找关联的评审问题
         * @param urlData 查询参数
         */
        function getReviewProblem(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'reviewProblem/getReviewProblem', urlData);
        }

        /**
         * 新增评审问题信息
         * @param urlData 评审问题信息
         */
        function insertProblem(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'reviewProblem/insertProblem', urlData);
        }

        /**
         * 更新评审问题信息
         * @param urlData 评审问题信息
         */
        function updateProblem(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'reviewProblem/updateProblem', urlData);
        }

        /**
         * 根据问题id删除评审问题
         * @param urlData
         */
        function deleteProblemById(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'reviewProblem/deleteProblemById', urlData);
        }

        /**
         * selectByReviewAndJudge方法是 根据评审id和评委姓名查询此评委在此次评审中提出的问题数量
         *
         * @param urlData 含有评审id和评委姓名的查询条件
         * @return  此评委在此次评审中提出的问题数量
         * <AUTHOR>
         * @date 2019/11/14 16:35
         */
        function selectByReviewAndJudge(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'reviewProblem/selectByReviewAndJudge', urlData);
        }
    }
})();
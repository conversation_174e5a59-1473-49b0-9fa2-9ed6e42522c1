/**
** 参数配置页面
**/
(function(){
   'use strict'
   app.controller("paramController",['$scope','$state','paramService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','$stateParams','LocalCache', '$modal','$http',
   function ($scope,$state,paramService, $rootScope, inform, Trans, AgreeConstant,$stateParams,LocalCache, $modal,$http) {
   /**
     * *************************************************************
     *             初始化部分                                 开始
     * *************************************************************
     */
   $scope.limitList = AgreeConstant.limitList; // 正则校验配置
   $scope.searchObject = {};//查询条件
   $scope.pages = {
        pageNum : '', 		// 分页页数
        size : '', 			// 分页每页大小
        total : '' 			// 数据总数
   };
   //设置列表的高度
   setDivHeight();
   //窗体大小变化时重新计算高度
   $(window).resize(setDivHeight);
   $scope.pages = inform.initPages(); 	// 初始化分页数据
   $scope.getData = getData; 			// 分页相关函数

   //新增时明细列表
   $scope.jList = [];
   //获取缓存
   $scope.searchObject = LocalCache.getObject('param_searchObject');
   //对原缓存进行覆盖
   LocalCache.setObject("param_searchObject",{});
   // 获取数据
   getData();		//在刷新页面时调用该方法
   /**
     * *************************************************************
     *              初始化部分                                 结束
     * *************************************************************
     */

    /**
     * *************************************************************
     *              方法声明部分                                 开始
     * *************************************************************
     */

    /**
    * 设置列表的高度
    */
    function setDivHeight(){
        //网页可见区域高度
        var clientHeight = document.body.clientHeight;
        var divHeight = clientHeight - (150 + 180);
        $("#divTBDis").height(divHeight);
        $("#subDivTBDis").height(divHeight - 50);
    }
    /**
     * 重置
     */
    $scope.reset = function() {
        $scope.searchObject.paramTypeName = '';
    }




    /**
     * 修改字典名称
     */
    $scope.updateInfo = function (m) {

        inform.modalInstance("是否确定更新字典信息？").result.then(function() {
            var urlData = {
                    'paramId':m.paramId,
                    'paramTypeCode':m.paramTypeCode,
                    'paramCode': m.paramCode,
                    'paramName': m.paramName,
                    'paramValue':m.paramValue,
                    'paramDesc':m.paramDesc
            };
            paramService.updateByParam(urlData).then(function (data) {
                 layer.confirm(data.message,{
                     title:false,
                     btn:['确定']
                 },function(result){
                     layer.close(result);
                     getData (1);
                 });

            },
            function (error) {
                inform.common(Trans("tip.requestError"));
            });
        });
    };

    /**
    ** 查询
    **/
    function getData (page){
       var param = {
          "paramTypeName":$scope.searchObject.paramTypeName,
          "currentPage":page,
          "pageSize":$scope.pages.size
       };
       paramService.selectByValueOrDesc(param).then(function(data){
          if(data.code===AgreeConstant.code){
             $scope.itemList = data.data.list;
             if($scope.itemList.length===0){
               $scope.pages = inform.initPages(); 			//初始化分页数据
               inform.common(Trans("tip.noData"));
             }else {
               // 分页信息设置
               $scope.pages.total = data.data.total;		// 页面总数
               $scope.pages.star = data.data.startRow;  	//页面起始数
               $scope.pages.end = data.data.endRow;  		//页面大小数
               $scope.pages.pageNum = data.data.pageNum;  	//页面页数
             }

          }

       }
       );
    }
    //该节点为最后一层节点，无法编辑
    $scope.openErrorTips= function(){
       inform.common(Trans("该节点为最后一层节点，不提供编辑功能"));
    }

    //跳转新增页面
    $scope.openAddPage = function(){
        LocalCache.setObject('param_searchObject',$scope.searchObject);
        $state.go('app.office.paramAddController');
    }

    //跳转修改页面
    $scope.openUpdatePage = function(item){
        LocalCache.setObject('param_searchObject',$scope.searchObject);
        $state.go('app.office.paramUpdateController',{
             "paramTypeCode":item.paramTypeCode,
             "paramCode":item.paramCode,
             "paramName":item.paramName,
             "paramValue":item.paramValue,
             "paramDesc":item.paramDesc
        });
    }

    //返回主页面
    $scope.goBack = function(){
        $state.go('app.office.paramController');
    }






   }
   ]);
})();
/**
 ** 参数配置页面
 **/
(function(){
    'use strict'
    app.controller("zentaoParamController",['$scope','$state','paramService', '$rootScope', 'inform', 'Trans', 'AgreeConstant','$stateParams','LocalCache', '$modal','$http',
        function ($scope,$state,paramService, $rootScope, inform, Trans, AgreeConstant,$stateParams,LocalCache, $modal,$http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.searchObject = {};//查询条件
            $scope.pages = {
                pageNum : '', 		// 分页页数
                size : '', 			// 分页每页大小
                total : '' 			// 数据总数
            };
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.pages = inform.initPages(); 	// 初始化分页数据
            $scope.getData = getData; 			// 分页相关函数
            $scope.list = [];
            //获取缓存
            $scope.searchObject = LocalCache.getObject('param_searchObject');
            //对原缓存进行覆盖
            LocalCache.setObject("param_searchObject",{});
            // 获取数据
            getData();		//在刷新页面时调用该方法
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 设置列表的高度
             */
            function setDivHeight(){
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 180);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
            }
            /**
             * 重置
             */
            $scope.reset = function() {
                $scope.searchObject.paramTypeName = '';
            }
            //添加数据
            $scope.addZentaoProject = function () {
                if($scope.list.length === 0){
                    $scope.addNewBind(1);
                }
                $("#addProject").click();
            }
            //修改数据
            $scope.updateZentaoProject = function (m) {
                $scope.updateProjectInfo  = m;
                $("#updateProject").click();
            }
            /**
             * 新增一个数据字典明细
             */
            $scope.addNewBind = function (index) {
                //参数明细
                var judge = {
                    'paramName':'',
                    'paramTypeCode': '',
                    'paramCode': '',//参数编码
                    'paramValue': '',//参数名称
                    'paramDesc': '',//描述
                    'sort': '', //排列顺序
                    'paramStatus':"0",//参数状态 默认为0，启用
                    'isSystem':"1",//是否为系统参数 默认为1，业务参数
                    'createUser':LocalCache.getSession('currentUserName')
                };
                $scope.list.push(judge);

            };
            /**
             * 添加信息
             */
            function addInfo(m) {
                angular.forEach(m,function (index){
                    if(index.paramCode === ''||index.paramCode == null){
                        inform.common('项目ID不能为空');
                        return;
                    }
                    if(index.paramValue === ''||index.paramValue == null){
                        inform.common('项目名称不能为空');
                        return;
                    }
                    index.paramTypeCode = 'PLM_PROJECT_PLANT';
                    index.paramName = 'PLM_PROJECT_PLANT';
                });
                paramService.addParamList(m).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        layer.confirm(data.message,{
                            title:false,
                            btn:['确定']
                        },function(result){
                            layer.close(result);
                            $scope.list = [];
                            $("#add_param").modal('hide');
                            getData();
                        });
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 删除信息
             */
            $scope.deleteInfo =  function(m) {
                var urlData = {
                    'paramId':m.paramId
                };
                paramService.deleteParam(urlData).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        layer.confirm(data.message,{
                            title:false,
                            btn:['确定']
                        },function(result){
                            layer.close(result);
                            getData();
                        });
                    } else {
                        inform.common(data.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            //取消一行
            $scope.deleteNewBind = function (index) {
                if (index >= 0) {
                    $scope.list.splice(index, 1);
                }
            };
            /**
             * 保存信息
             */
            $scope.saveInfo = function () {
                if ($scope.list.length === 0){
                    inform.common("请输入明细");
                    return;
                }
                //新增
                addInfo($scope.list);

            };
            /**
             * 修改字典名称
             */
            $scope.updateInfo = function (m) {
                if(m.paramCode === ''||m.paramCode == null){
                    inform.common('项目ID不能为空');
                    return;
                }
                if(m.paramValue === ''||m.paramValue == null){
                    inform.common('项目名称不能为空');
                    return;
                }
                    var urlData = {
                        'paramId':m.paramId,
                        'paramTypeCode':m.paramTypeCode,
                        'paramCode': m.paramCode,
                        'paramName': m.paramName,
                        'paramValue':m.paramValue,
                        'paramDesc':m.paramDesc
                    };
                    //update时为修改
                    paramService.updateParamValueAndCode(urlData).then(function (data) {
                            if (data.code === AgreeConstant.code) {
                                $("#updateProjectCancel").click();
                                inform.common('更新成功');
                                //清空缓存
                            } else {
                                inform.common(data.message);
                            }
                        },
                        function (error) {
                            inform.common(Trans("tip.requestError"));
                        });
            }
            /**
             ** 查询
             **/
            function getData (page){
                var param = {
                    "paramTypeName":$scope.searchObject.paramTypeName,
                    "paramTypeCode":'PLM_PROJECT_PLANT',
                    "currentPage":page,
                    "pageSize":$scope.pages.size
                };
                paramService.selectByValueOrDesc(param).then(function(data){
                        if(data.code===AgreeConstant.code){
                            $scope.itemList = data.data.list;
                            if($scope.itemList.length===0){
                                $scope.pages = inform.initPages(); 			//初始化分页数据
                                inform.common(Trans("tip.noData"));
                            }else {
                                // 分页信息设置
                                $scope.pages.total = data.data.total;		// 页面总数
                                $scope.pages.star = data.data.startRow;  	//页面起始数
                                $scope.pages.end = data.data.endRow;  		//页面大小数
                                $scope.pages.pageNum = data.data.pageNum;  	//页面页数
                            }
                        }
                    }
                );
            }

        }
    ]);
})();
/*
 * @Author: fubaole
 * @Date:   2017-09-18 14:53:05
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-01-15 17:20:32
 */
(function() {
	app.controller("stafftransferManagement",['$rootScope','$scope','$modal','stafftransferService','inform','Trans','AgreeConstant','LocalCache','comService',
	    function($rootScope, $scope, $modal,stafftransferService,inform, Trans, AgreeConstant, LocalCache,comService) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //获取当前的登录用户名
            $scope.updateEmployeename = LocalCache.getSession('currentUserName');
			//登录者ID
			$scope.loginId = LocalCache.getSession('userId');
			//读取本地缓存
			$scope.formInsert = LocalCache.getObject('formInsert') || {};
			$scope.pages = inform.initPages();		// 初始化分页数据
			$scope.getData = getData; 				// 分页相关函数
            getData($scope.pages.pageNum);			//在刷新页面时调用该方法获取数据

			$scope.staffList = [];					// 保存所有信息的集合
            $scope.formInsertCopy = [];				// 保存用戶查詢時的查询条件
			var organId = "";     					//定义一个空的orangId字符串
			$scope.checked = [];  					//初始化复选框为未选中状态

            initPages();//初始化數據
			

			setDivHeight();//设置列表的高度

			$(window).resize(setDivHeight);//窗体大小变化时重新计算高度


            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */

            //设置列表的高度
            function setDivHeight(){
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 190);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
            }

            /**
             * 页面初始化
             */
            function initPages() {
                //获取员工信息
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function (data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                    }
                });
            }

            //重置
            $scope.rest = function() {
                $scope.formInsert = {};
            };

			/**
			 * 获取数据
             * pageNum 页面页数
			 */
			function getData(pageNum) {
				$scope.staffList = [];
				$scope.checked = [];
				//写入本地缓存
				LocalCache.setObject('formInsert',$scope.formInsert);
				var urlData = {
					'repositoryName': $scope.formInsert.repositoryName,		//搜索框中输入的仓库名
					'moduleName':	$scope.formInsert.moduleName,			//搜索框张输入的模块名
					'employeeNo':	$scope.formInsert.code, 				//员工编号
					'currentPage' : pageNum,								// 分页页数
					'pageSize' : $scope.pages.size  						//分页每页显示数量
				};
                stafftransferService.getAllInfos(urlData).then(function(data) {
                    if (data.code===AgreeConstant.code) {
                        $scope.pages.goNum = null;
                        var jsonData = data.data;
                        $scope.staffList = jsonData.list;
                        //保存查询时的查询条件
                        $scope.formInsertCopy = angular.copy($scope.formInsert);
                        if ($scope.staffList.length===0) {
                            $scope.pages = inform.initPages(); 			//初始化分页数据
                            inform.common(Trans("tip.noData"));
                        } else {
                            // 分页信息设置
                            $scope.pages.total = jsonData.total;		// 页面总数
                            $scope.pages.star = jsonData.startRow;  	//页面起始数
                            $scope.pages.end = jsonData.endRow;  		//页面大小数
                            $scope.pages.pageNum = jsonData.pageNum;  	//页面页数
                            organId="";
                        }
                    } else {
                         inform.common(data.message);
                    }
                },
                function(error) {
                    inform.common(Trans("tip.requestError"));
                });
			}
		
			/**
			 *  复选框一个一个选
			 */
			$scope.selectOne = function(id) {
				angular.forEach($scope.staffList, function(i) {
				    var index = $scope.checked.indexOf(i);
                    // 判断  indexof(i)中下标不存在 并且 当前为选中状态
					if (index === -1 && i.checked) {
						$scope.checked.push(i);
                        //判断organId 中是否已经存在
						if (organId.indexOf(id) >= 0) {
                            //存在就给去除
							organId = organId.replace(';'+ id, "");
						} else {
                            //将新的id 加在后面  id;id;id
							organId = organId + ";" + id;
						}
                        //如果下标存在并且复选框 未选中
					} else if (index !== -1 && !i.checked) {
                        //下标 去除一个
						$scope.checked.splice(index, 1);
                        //判断organId 中是否已经存在
						if (organId.indexOf(id) >= 0) {
                            //存在就给去除
							organId = organId.replace(';'+ id, "");
						}
					}
				});								
			};
								
			/**
			 * 人员离职
			 */
			$scope.leave = function(){
                //如果未选中则提示
                if(!$scope.formInsertCopy.code){
                    inform.common("请先选择要离职的人员！");
                    return;
                }

				var urlData = {
					'employeeNo':$scope.formInsertCopy.code,   			//员工编号
					'id':'',                  						//关系id 置为空  
					'updateEmployeename':$scope.updateEmployeename,  //更新者
					'loginId':$scope.loginId,    //更新者id
					'repositoryName':$scope.formInsertCopy.repositoryName,	//仓库名
					'repositoryId':$scope.formInsertCopy.repositoryId		//	仓库id
				};

                //如果执行成功则进行下面的操作
                inform.modalInstance("确定该人员已经离职吗！").result.then(function() {
                    //人员离职
                    stafftransferService.staffTransfer(urlData).then(function(data) {
                        if (data.code===AgreeConstant.code) {
                            inform.common(Trans("人员离职成功！"));
                            getData();
                            $scope.formInsert.name="";
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
                });
            };

			/**
			 *  人员调出
			*/
			$scope.transfer = function() {
                //如果未选中 则提示
                if((!$scope.formInsertCopy.code && !$scope.formInsertCopy.repositoryName
                    && !$scope.formInsertCopy.moduleName) || organId === ''){
                    inform.common("请先选择要调出的相应信息！");
                    return;
                }
				var urlData = {
                    'employeeNo':$scope.formInsertCopy.code,    			//员工编号
                    'id':organId,              				//关系id
                    'updateEmployeename':$scope.updateEmployeename,      //更新者
                    'loginId':$scope.loginId,    //更新者id
                    'repositoryName':$scope.formInsertCopy.repositoryName,	//仓库名
                    'repositoryId':$scope.formInsertCopy.repositoryId		//	仓库id
				};
                //如果执行成功则进行下面的操作
                inform.modalInstance("确定要调出吗！").result.then(function() {
                   //人员调出
                    stafftransferService.staffTransfer(urlData).then(function(data) {
                        if (data.code===AgreeConstant.code) {
                            getData();
                            organId="";                      //成功后关系id置为空
                            inform.common(Trans("人员调出成功！"));
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
                });
            }

	}]);

})();
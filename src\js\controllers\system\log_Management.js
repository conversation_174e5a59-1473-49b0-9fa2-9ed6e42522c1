/*
 * @Author: fubaole
 * @Date:   2017-07-25 11:28:56
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-06 09:28:17
 */
(function() {
  'use strict';
  app.controller("log_Management", ['$rootScope', '$scope', '$timeout', '$stateParams', 'inform', 'SystemService', 'Trans','AgreeConstant',
    function($rootScope, $scope, $timeout, $stateParams, inform, SystemService, Trans,AgreeConstant) {
      var interfaceMap ={};
      // 排序
      $scope.title = 'logId';
      $scope.desc = true;

      $scope.map = {}; //查询条件

      // $scope.inputData ={
      //   row:[
      //     {
      //       name:'log.name',
      //       model:'',
      //       holder:'log.placeholderName',
      //       type:'search',
      //       selectList:''
      //     },
      //     {
      //       name:'log.type',
      //       model:'',
      //       holder:'',
      //       type:'select',
      //       selectList:[]
      //     }
      //   ],
      //   unfirstRow:[
      //    {
      //       name:'log.startTime',
      //       model:'',
      //       closeText:'common.close',
      //       clearText:'common.clear',
      //       currentText:'common.today',
      //       type:'date',
      //       selectList:'',
      //     },
      //     {
      //       name:'log.endTime',
      //       model:'',
      //       closeText:'common.close',
      //       clearText:'common.clear',
      //       currentText:'common.today',
      //       type:'date',
      //       selectList:''
      //     }
      //   ]
      // };
      $scope.order = order; // 排序函数
      $scope.getData = getData; // 初始化函数
      $scope.searchData = searchData; // 查询函数
      $scope.reset = reset;
      $scope.advQuery = advQuery;
      $scope.startOpen = startOpen;
      $scope.endOpen = endOpen;
      $scope.toggleMin = toggleMin;

      $scope.pages = inform.initPages(); // 初始化分页数据
      getLogOperationType(); //获取日志操作类型
      toggleMin(); // 获取最小选择日期
      getData($scope.pages.pageNum); // 初始化请求数据
      $scope.dateOptions = {
          formatYear: 'yy',
          class: 'datepicker',
          showWeeks: false
      };
      function order(str) {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      }

      // 查询
      function searchData(str){
        // $scope.map.userName = str.row[0].model;
        // $scope.map.logType = str.row[1].model;
        // $scope.map.startT = str.unfirstRow[0].model;
        // $scope.map.endT = str.unfirstRow[1].model;
        interfaceMap = angular.copy($scope.map);
        getData(AgreeConstant.pageNum);
      }

      // 获取表格数据
      function getData(num) {
        if(!num){inform.common(Trans('tip.pageNumTip'));return;}
        // 日期格式转化
        if($scope.map.startT){
          interfaceMap.startT = inform.formatDate($scope.map.startT, "yyyy-MM-dd");
        }
        if($scope.map.endT){
          interfaceMap.endT = inform.formatDate($scope.map.endT, "yyyy-MM-dd");
        }
        SystemService.logManagementGetLogList(JSON.stringify(interfaceMap), parseInt(num), $scope.pages.size)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.pages.goNum = null;
              var jsonData = angular.fromJson(data.result);
              $scope.resultData = jsonData.list;
              if($scope.resultData.length==0){
                inform.common(Trans('tip.noData'));
                $scope.pages = inform.initPages();
              }else{
                $scope.pages.total = jsonData.total;
                $scope.pages.star = (jsonData.pageNum - 1) * jsonData.pageSize + 1;
                $scope.pages.end = jsonData.pageNum * jsonData.pageSize;
                $scope.pages.pageNum = jsonData.pageNum;
              }
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans('tip.requestError'));
          });
      }

      // 获取日志操作类型
      function getLogOperationType() {
        SystemService.getDictValueListByDictTypeCode("log_type")
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.logTypeCode =data.result;
              // $scope.inputData.row[1].selectList =data.result;
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 重置按钮
      function reset(){
        $scope.map.userName = "";
        $scope.map.logType = "";
        $scope.map.startT = null;
        $scope.map.endT = null;
      }

      // 高级查询功能按钮
      function advQuery() {
        $scope.isOpen =!$scope.isOpen;
        $scope.map.startT = null;
        $scope.map.endT = null;
      }

      function startOpen($event) {
        $event.preventDefault();
        $event.stopPropagation();
        $scope.startOpened = true;
      }

      function endOpen($event) {
        $event.preventDefault();
        $event.stopPropagation();
        $scope.endOpened = true;
      }

      function toggleMin() {
        $scope.currentDate = $scope.currentDate ? null : new Date();
      }

    }
  ]);
})();
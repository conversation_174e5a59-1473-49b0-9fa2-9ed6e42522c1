(function () {
    app.controller("expertController", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','expertService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,expertService,inform,Trans,AgreeConstant,LocalCache,$http) {
       	
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
		//分页
    	$scope.pages = inform.initPages();
		//设置列表的高度
        setDivHeight();
        //窗体大小变化时重新计算高度
        $(window).resize(setDivHeight);
        initDepartment();
        //获取缓存
        $scope.formRefer = LocalCache.getObject('expertManagement_formRefer');
        //对原缓存进行覆盖
        LocalCache.setObject("expertManagement_formRefer",{});
        $scope.getDetails = getDetails;
        if (null != $scope.formRefer.territory){
        	getDetails();
        }
        $scope.getData = getData; 			// 分页相关函数    
        getData();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
		
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */
        /**
         * 设置列表的高度
         */
        function setDivHeight() {
            //网页可见区域高度
            var clientHeight = document.body.clientHeight;
            var divHeight = clientHeight - (150 + 185);
            $("#divTBDis").height(divHeight);
            $("#subDivTBDis").height(divHeight - 50);
        }
        /**
		 * 跳转修改
		 */ 
		$scope.go = function(item) {
			if(null ==item){
				LocalCache.setObject('expertManagement_formRefer',$scope.formRefer);
				$state.go("app.office.expertControllerAdd", null);
			} else {
				 LocalCache.setObject('expertManagement_formRefer',$scope.formRefer);
				$state.go("app.office.expertControllerUp", {item: item.id});
			}
		};
		
		/**
		 * 初始化
		 */
    	function initDepartment() {
    		//获取部门
            $scope.oneDepartmentList = [];
            comService.getOrgChildren('0002').then(function (data) {
                if (data.data) {
                    $scope.oneDepartmentList = data.data;
                }
            });
    		//获取技术领域
    		$scope.professionalList = [];
    		comService.queryEffectiveParam('STAFF_PROFESSIONAL_MODULE','STAFF_PROFESSIONAL_MODULE').then(function(data) {
        		if (data.data) {
        			$scope.professionalList =  data.data;
        		}
            });
    		//获取行业领域
    		$scope.expertTradeList = [];
    		comService.queryEffectiveParam('ExpertTrade','ExpertTrade').then(function(data) {
        		if (data.data) {
        			$scope.expertTradeList =  data.data;
        		}
            });
    	}
    	$scope.getTwoDepartment = function(){
        	//获取二级部门
            $scope.departmentList = [];
            comService.getOrgChildren($scope.formRefer.oneDepartment).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    $scope.departmentList = data.data;
                }
            });
        };
    	/**
    	 * 获取行业详情
    	 */
    	function getDetails(){
    		//获取行业详情
    		$scope.TradeDetailsList = [];
    		comService.queryEffectiveParam('ExpertTrade',$scope.formRefer.territory).then(function(data) {
        		if (data.data) {
        			$scope.TradeDetailsList =  data.data;
        		}
            });
    	}
    	/**
         * 获取所有专家信息
         */
        function getData(pageNum) {
        	 var urlData = {
                     'name': $scope.formRefer.name,
                     'oneDepartment': $scope.formRefer.oneDepartment,
                     'department': $scope.formRefer.department,
                     'professionalModule': $scope.formRefer.professionalModule, 
                     'territory': $scope.formRefer.territory,
                     'territoryDetails': $scope.formRefer.territoryDetails,
                     'currentPage': pageNum,//当前页数
                     'pageSize': $scope.pages.size//每页显示条数
             };
        	 expertService.selectData(urlData).then(function (data) {
             	if (data.code === AgreeConstant.code) {
                 		if(null==data.data){
                 			$scope.tableData = {};
                 			inform.common(Trans("tip.noData"));
                 			$scope.pages = inform.initPages();
                 		} else {
                 			//项目详情
                 			$scope.tableData = data.data.list;
                             //分页信息设置
                             $scope.pages.total = data.data.total;
                             $scope.pages.star = data.data.startRow+1;
                             $scope.pages.end = data.data.endRow+1;
                             $scope.pages.pageNum = data.data.pageNum;
                         }
                     } else {
                         inform.common(data.message);
                     }
                 },
                 function () {
                     inform.common(Trans("tip.requestError"));
                 });
        }
        /**
         * 删除弹框
         */ 
        $scope.open = function (m) {
            var modalInstance = $modal.open({
                templateUrl: 'myModalContent.html',
                controller: 'ModalInstanceCtrl',
                size: "sm",
                resolve: {
                    items: function () {
                        return Trans("common.deleteTip");
                    }
                }
            });
            modalInstance.result.then(function () {
                if (m) {
                    $scope.delete(m);
                }
            });
        };

        /**
         * 删除信息
         */
        $scope.delete = function (m) {
            var urlData = {
                'employeeId': m.id
            };
            expertService.delData(urlData).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    layer.confirm(data.message, {
                        title: false,
                        btn: ['确定']
                    }, function (result) {
                        layer.close(result);
                        getData(1);
                    });
                } else {
                    inform.common(data.message);
                }
            }, function (error) {
                inform.common(Trans("tip.requestError"));
            });
        };
        /**
		 * excel下载
		 */
		$scope.toExcel = function() {
			var modalInstance = $modal.open({
				templateUrl: 'myModalContent.html',
				controller: 'ModalInstanceCtrl',
				size: "sm",
				resolve: {
					items: function() {
						return "确定要下载吗！";
					}
				}
			});
			modalInstance.result.then(function() {
				//拼装下载内容
				var urlData={
						 'name': $scope.formRefer.name,
	                     'department': $scope.formRefer.department,
	                     'professionalModule': $scope.formRefer.professionalModule, 
	                     'territory': $scope.formRefer.territory,
	                     'territoryDetails': $scope.formRefer.territoryDetails
				};
				inform.downLoadFile ('expertDatabase/toExcel',urlData,'技术人才库.xlsx');
			});
		}
	 		/**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */	
		
	}]);
})();
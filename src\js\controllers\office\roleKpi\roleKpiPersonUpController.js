(function () {
    app.controller("roleKpiPersonUpController", ['$rootScope', 'comService', '$scope', '$state', '$stateParams', '$modal', 'roleKpiService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function ($rootScope, comService, $scope, $state, $stateParams, $modal, roleKpiService, inform, Trans, AgreeConstant, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.roleName = $stateParams.roleName;
            initDepartment();
            $scope.getData = getData; 			// 获取初始数据
            getData();
            // 下拉框发生改变
            $scope.notSelect = notSelect;
            // 全选
            $scope.selectAll = selectAll;
            // 清空
            $scope.clearAll = clearAll;
            // 更新
            $scope.saveData = saveData;
            // 返回
            $scope.goback = goback;
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 210);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 75);
                $("#divTBDis1").height(divHeight);
                $("#subDivTBDis1").height(divHeight - 75);
            }

            /**
             * 初始化
             */
            function initDepartment() {
                //获取员工信息
                $scope.employeesList = [];
                comService.getEmployeesName().then(function (data) {
                    angular.forEach(data.data, function (res) {
                        //数字转字符串
                        res.employeeId = res.employeeId + '';
                    });
                    $scope.employeesList = data.data;
                });
            }

            // 下拉框发生改变
            function notSelect () {
                //清空复选框
                $scope.zerochecked = false;
                $scope.onechecked = false;
            }
            /**
             * 全选
             */
            function selectAll () {
                $scope.rolePersonList = [];
                angular.forEach($scope.employeesList, function (item) {
                    if ($scope.rolePersonList.indexOf(item.employeeNo) === -1) {
                        $scope.rolePersonList.push(item.employeeNo);
                    }
                });
                $scope.zerochecked = true;
                $scope.onechecked = false;
            }
            /**
             * 清空
             */
            function clearAll () {
                $scope.rolePersonList = [];
                $scope.onechecked = true;
                $scope.zerochecked = false;
            }

            /**
             * 获取所有人员信息
             */
            function getData() {
                var urlData = {
                    'roleName': $stateParams.roleName
                };
                roleKpiService.selectOneRolePersonData(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null == data.data) {
                                $scope.tableData = {};
                                inform.common(Trans("tip.noData"));
                            } else {
                                //人员信息
                                $scope.rolePersonList = data.data;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            //更新
            function saveData () {
                var urlData = {
                    'roleName': $stateParams.roleName,
                    'roleEmployeeList': $scope.rolePersonList
                };
                roleKpiService.upRolePersonData(urlData).then(function (data) {
                    layer.confirm(data.message, {
                        title: false,
                        btn: ['确定']
                    }, function (result) {
                        layer.close(result);
                        $state.go("app.office.roleKpiController", {'type': '2'});
                    });
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });

            }
            /*
            * 返回
            * */
            function goback () {
                $state.go("app.office.roleKpiController", {'type': '2'});
            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
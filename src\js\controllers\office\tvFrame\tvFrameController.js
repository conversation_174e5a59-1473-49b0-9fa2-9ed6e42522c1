(function () {
	app.controller("tvFrameController", ['comService', 'SystemService', 'tvService', '$state', '$rootScope', '$scope', '$stateParams', '$modal', 'AgreeConstant', '$http', "LocalCache", function (comService, SystemService, tvService, $state, $rootScope, $scope, $stateParams, $modal, AgreeConstant, $http, LocalCache) {
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
			//ws地址
		var wsUri = $rootScope.getWaySystemApi + "webSocket/" + LocalCache.getSession('currentUserName');
		wsUri = wsUri.replace("http", "ws"); //初始化WebSocket

		var websocket = null;
		createWebSocket(wsUri);
		var tvFrameObj = {
			tvList: [],
			//看板集合
			tvIndex: -1 //播放看板的下标

		}; //跑马灯滚动使用参数

		var marqueeItemCount = document.body.clientWidth;
		var marqueeItemCountWidth; // 子页面鼠标事件发送时调用

		$scope.mouseEvent = mouseEvent; //窗体大小变化时重新计算高度

		$(window).resize(setIframeHeight); //看板计时器

		var timeoutID = null; //消息计时器

		var messageTimeoutID = null;
		var tvMessageObj = {
			tvList: [],
			//消息集合
			tvIndex: -1 //消息的下标

		}; // 初始化看板信息

		initTvFrame(); // 初始化消息信息

		initTvMessage(); //顶部操作栏显示隐藏事件

		fun(); // 设置有关高度、宽度css

		setCss(); // 上一页看板

		$scope.goPrePage = goPrePage; // 下一页看板

		$scope.goNextPage = goNextPage; // 返回主菜单

		$scope.goBack = goBack; // 隐藏消息

		$scope.messageIsHide = messageIsHide; // 跑马灯消息滚动事件

		var intervalFun;
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */

		/**
		 * 设置iframe的高度
		 */

		function setIframeHeight() {
			marqueeItemCount = document.body.clientWidth; //网页可见区域高度

			var clientHeight = document.body.clientHeight; //网页可见区域宽度

			var clientWidth = document.body.clientWidth; //设置返回框居中

			$('#showActive').css({
				'margin-left': clientWidth / 4 + 'px'
			});
			$('#active').css({
				'margin-left': clientWidth / 4 + 'px'
			}); // 跑马灯位置

			$('#marqueeDiv').css({
				'margin-top': clientHeight - 100 + 'px'
			});
			$('#mouseEventTrigger').css({
				'height': clientHeight + 'px'
			});
			$('#mouseEventTrigger').css({
				'width': clientWidth + 'px'
			});
		}
		/*
        * 设置css样式
        * */


		function setCss() {
			var clientWidth = document.body.clientWidth;
			var clientHeight = document.body.clientHeight; //设置返回框居中

			$('#showActive').css({
				'margin-left': clientWidth / 4 + 'px'
			});
			$('#active').css({
				'margin-left': clientWidth / 4 + 'px'
			}); // 跑马灯位置

			$('#marqueeDiv').css({
				'margin-top': clientHeight - 100 + 'px'
			}); // 监听点击事件

			$('#mouseEventTrigger').css({
				'height': clientHeight + 'px'
			});
			$('#mouseEventTrigger').css({
				'width': clientWidth + 'px'
			});
		}
		/**
		 * 初始化看板信息
		 */


		function initTvFrame() {
			SystemService.getRoleListMapByUserId(LocalCache.getSession('userId')).then(function (data) {
				if (data.code === AgreeConstant.resultCode) {
					tvService.getTvFrameDetailListByRole(data.result.userRoleList.map(function (item) {
						return item.roleId;
					})).then(function (data) {
						if (data.code === AgreeConstant.code) {
							tvFrameObj.tvList = data.data;
							tvFrameObj.tvIndex = -1;
							tvFrameObj.tvList.forEach(function (item, itemIndex) {
								$('#subTVDiplay' + itemIndex).remove();
								var iframeDiv = $('#iframeDiv')
								const contentUrl = item.contentUrl
								if (contentUrl && contentUrl.split('/')[contentUrl.split('/').length - 1] === 'reviewContribution' ||
									contentUrl && contentUrl.split('/')[contentUrl.split('/').length - 1] === 'trainGlorious' ||
									contentUrl && contentUrl.split('/')[contentUrl.split('/').length - 1] === 'reviewEfficiency'){
									iframeDiv.append(`<iframe id="subTVDiplay`+ itemIndex
										+`" style="height: 100vh;width: 100%;margin-top: -50px"></iframe>`)
								} else {
									iframeDiv.append(`<iframe id="subTVDiplay`+ itemIndex
										+`" style="height: 2560px;width: 100%;margin-top: -50px"></iframe>`)
								}
							});
							//循环播放看板
							tvPlay();
						} else {
							inform.common(data.message);
						}
					}, function () {
						inform.common(Trans("tip.requestError"));
					});
				} else {
					inform.common(data.message);
				}
			}, function () {
				inform.common(Trans("tip.requestError"));
			});
		}
		/*
        * 初始化消息信息
        * */


		function initTvMessage() {
			tvService.getSortTvMessageListByLoginName({
				'loginName': LocalCache.getSession('currentUserName')
			}).then(function (data) {
				if (data.code === AgreeConstant.code) {
					// 获取登录者的角色列表
					var roleIdList = JSON.parse(LocalCache.getSession('roleList')).map(function (item) {
						return item.roleId;
					}); // 过滤掉角色不符合的消息

					tvMessageObj.tvList = data.data.filter(function (item) {
						return roleIdList.find(function (ele) {
							return item.role.split(',').map(Number).find(function (e) {
								return e === ele;
							});
						});
					});
					tvMessageObj.tvIndex = -1; //循环播放消息

					tvMessagePlay();
				} else {
					inform.common(data.message);
				}
			}, function () {
				inform.common(Trans("tip.requestError"));
			});
		}
		/*
        *顶部操作栏显示隐藏事件
        * */


		function fun() {
			$('#showActive').mouseover(function () {
				$('#active').show();
			});
			$('#active').mouseleave(function () {
				$('#active').hide();
			});
		}
		/*
        * 点击隐藏消息显示
        * */


		function messageIsHide() {
			clearTimeout(messageTimeoutID);
			clearInterval(intervalFun);
			$('#marqueeDiv').hide();
			$('#adMove').hide();
			setTimeout(messageIsShow, 1000 * 60);
		}
		/*
        * 显示消息
        * */


		function messageIsShow() {
			tvMessagePlay();
			$('#marqueeDiv').show();
			$('#adMove').show();
		}
		/*
        * 返回按钮
        * */


		function goBack() {
			clearTimeout(timeoutID);
			clearTimeout(messageTimeoutID);
			clearInterval(intervalFun);
			tvFrameObj.tvList.forEach(function (item, itemIndex) {
				var child = $("#subTVDiplay" + itemIndex).contents();
				child.find("#isHide").click();
				$("#subTVDiplay" + itemIndex).hide();
				$('#subTVDiplay' + itemIndex).remove();
			});
			websocket.close();
			websocket = null;
			document.getElementsByTagName("body")[0].style.overflow = "unset";
			$state.go('app.index_bench');
		}
		/*
        * 上一页按钮
        * */


		function goPrePage() {
			clearTimeout(timeoutID);
			tvFrameObj.tvIndex = tvFrameObj.tvIndex - 2 + tvFrameObj.tvList.length;
			tvPlay();
		}
		/*
        * 下一页按钮
        * */


		function goNextPage() {
			clearTimeout(timeoutID);
			tvPlay();
		}
		/**
		 * 播放看板
		 */


		function tvPlay() {
			//下个看板下标
			var index = (tvFrameObj.tvIndex + 1) % tvFrameObj.tvList.length;
			tvFrameObj.tvIndex = index; //添加参数

			var stringParams = '?' + tvFrameObj.tvList[index].tvParamList.map(function (item) {
				return item.paramName + '=' + item.paramValue;
			}).join('&'); // 添加数据库中tvFrameId

			if (stringParams !== '?') {
				stringParams = stringParams + '&';
			} // 是否禁用消息


			if (tvFrameObj.tvList[index].tvParamList.find(function (item) {
				return item.paramName === 'showMessage' && item.paramValue === 'false';
			})) {
				$('#marqueeDiv').hide();
				$('#adMove').hide();
			} else {
				$('#marqueeDiv').show();
				$('#adMove').show();
			}
			tvFrameObj.tvList.forEach(function (item, itemIndex) {
				if (itemIndex === index) {
					$("#subTVDiplay" + itemIndex).show();
					var child = $("#subTVDiplay" + itemIndex).contents();
					child.find("#isShow").click();
				} else {
					var _child = $("#subTVDiplay" + itemIndex).contents();

					_child.find("#isHide").click();

					$("#subTVDiplay" + itemIndex).hide();
				}
			});
            stringParams = stringParams + 'tvFrameId=' + tvFrameObj.tvList[index].id
                                        + '&quarter=' + tvFrameObj.tvList[index].quarter
                                        + '&year=' + tvFrameObj.tvList[index].year;
			$("#subTVDiplay" + index).attr("src", tvFrameObj.tvList[index].contentUrl + stringParams); // 只有一个面板不需要循环播放

			if (tvFrameObj.tvList.length !== 1) {
				timeoutID = setTimeout(tvPlay, tvFrameObj.tvList[index].playDuration * 1000);
			}
		}
		/*
        * 播放消息
        * */


		function tvMessagePlay() {
			if (tvMessageObj.tvList.length === 0) {
				return;
			}

			var index = (tvMessageObj.tvIndex + 1) % tvMessageObj.tvList.length;
			tvMessageObj.tvIndex = index;
			$('#marqueeDiv').children().remove();
			$('#adMove').children().remove();
			clearInterval(intervalFun);
			marqueeItemCount = document.body.clientWidth;

			if (tvMessageObj.tvList[index].showForm === '0') {
				marqueeItemCountWidth = -(tvMessageObj.tvList[tvMessageObj.tvIndex].content.length * tvMessageObj.tvList[tvMessageObj.tvIndex].fontSize); // 跑马灯

				$('#marqueeDiv').append("<div id=\"marqueeItemDiv\" style=\"margin-left: " + document.body.clientWidth + "px\"><span style=\"color: " + tvMessageObj.tvList[index].fontColor + ";font-size:" + tvMessageObj.tvList[index].fontSize + "px;line-height: 50px\">" + tvMessageObj.tvList[index].content + "</span></div>");
				intervalFun = setInterval(showMarquee, tvMessageObj.tvList[index].playSpeed);
			} else {
				// 随机漂浮
				$('#adMove').append("<div style=\"color: " + tvMessageObj.tvList[index].fontColor + ";font-size:" + tvMessageObj.tvList[index].fontSize + "px;width: " + tvMessageObj.tvList[index].content.length * tvMessageObj.tvList[index].fontSize + "px;display: flex;\">" + tvMessageObj.tvList[index].content + "</div>");
			}

			messageTimeoutID = setTimeout(tvMessagePlay, tvMessageObj.tvList[index].playDuration * 1000);
		}
		/*
        * 跑马灯滚动
        * */


		function showMarquee() {
			if (marqueeItemCount === marqueeItemCountWidth) {
				marqueeItemCount = document.body.clientWidth;
			}

			--marqueeItemCount;
			$('#marqueeItemDiv').css('margin-left', marqueeItemCount);
		}
		/**WebSocket 相关事件处理方法 **/

		/**
		 * 创建websocket
		 */


		function createWebSocket(wsUri) {
			websocket = new WebSocket(wsUri); //绑定事件

			websocket.onopen = function (evt) {
				onOpenWS(evt);
			};

			websocket.onclose = function (evt) {
				onCloseWS(evt);
			};

			websocket.onmessage = function (evt) {
				onMessageWS(evt);
			};

			websocket.onerror = function (evt) {
				onErrorWS(evt);
			};
		}
		/**
		 * 连接成功后触发事件
		 */


		function onOpenWS(evt) {
			websocket.send('Hello World');
		}
		/**
		 * 断开连接后触发事件
		 */


		function onCloseWS(evt) {
			console.log("DISCONNECTED");
		}
		/**
		 * 接收到消息后触发事件
		 */


		function onMessageWS(evt) {
			if (evt.data.toLowerCase().indexOf('frame') >= 0) {
				clearTimeout(timeoutID);
				initTvFrame();
			} else if (evt.data.toLowerCase().indexOf('message') >= 0) {
				clearTimeout(messageTimeoutID);
				initTvMessage();
			}

			console.log(evt.data);
		}
		/**
		 * 发生异常时触发事件
		 */


		function onErrorWS(evt) {
			console.log(evt.data);
		}
		/**
		 * 看板页面发生鼠标事件时调用 中断轮播
		 * 超过5分钟没有收到任何鼠标事件恢复自动轮播
		 * @returns
		 */


		function mouseEvent() {
			clearTimeout(timeoutID);
			timeoutID = setTimeout(tvPlay, 1000 * 60 * 5); //时间后期从看板信息中获取
		}
		/**
		 * *************************************************************
		 *              方法声明部分                                 结束
		 * *************************************************************
		 */

	}]);
})();
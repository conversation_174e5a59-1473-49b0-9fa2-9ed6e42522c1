/*
 * @Author: haohong<PERSON>
 * @Date:   2017-09-28 13:53:26
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-02 14:27:45
 */

(function() {
  'use strict';
  app.controller("userGroup_Management", ['$rootScope', '$scope', 'inform', '$modal', '$log', 'Trans', 'SystemService', 'AgreeConstant',
    function($rootScope, $scope, inform, $modal, $log, Trans, SystemService, AgreeConstant) {

      var interfaceMap = {};
      $scope.map = {}; //条件
      $scope.getData = getData; // 初始化函数
      $scope.pages = inform.initPages(); // 初始化分页数据
      $scope.getData($scope.pages.pageNum); // 初始化请求数据
      $scope.searchData = searchData; // 查询
      $scope.reset = reset; // 重置按钮
      $scope.open = open; // 删除弹框显示
      $scope.inputData = {
        row: [{
            name: 'userGroup.serial',
            model: '',
            holder: 'userGroup.placeholderSerial',
            type: 'search',
            selectList: ''
          },
          {
            name: 'userGroup.name',
            model: '',
            holder: 'userGroup.placeholderName',
            type: 'search',
            selectList: ''
          }
        ]
      };
      // 排序
      $scope.title = 'groupId';
      $scope.desc = true;
      $scope.order = order;

      // 排序
      function order(str) {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      }

      // 查询
      function searchData(str) {
        $scope.map.groupCode = str.row[0].model;
        $scope.map.groupName = str.row[1].model;
        interfaceMap = angular.copy($scope.map);
        getData(AgreeConstant.pageNum);
      }

      // 获取表格数据
      function getData(num) {
        if (!num) { inform.common(Trans('tip.pageNumTip')); return; }
        SystemService.getGroupListByMap(JSON.stringify(interfaceMap), parseInt(num), $scope.pages.size)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.pages.goNum = null;
              var jsonData = angular.fromJson(data.result);
              $scope.resultData = jsonData.list;
              if ($scope.resultData.length===0) {
                inform.common(Trans("tip.noData"));
                $scope.pages = inform.initPages();
              } else {
                $scope.pages.total = jsonData.total;
                $scope.pages.star = (jsonData.pageNum - 1) * jsonData.pageSize + 1;
                $scope.pages.end = jsonData.pageNum * jsonData.pageSize;
                $scope.pages.pageNum = jsonData.pageNum;
              }
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 重置函数
      function reset() {
        $scope.map.groupCode = "";
        $scope.map.groupName = "";
      }

      // 删除弹框显示
      function open(item) {
        var modalInstance = $modal.open({
          templateUrl: 'myModalContent.html',
          controller: 'ModalInstanceCtrl',
          size: "sm",
          resolve: {
            items: function() {
              return Trans('common.deleteTip');
            }
          }
        });

        modalInstance.result.then(function() {
          if (item) {
            SystemService.deleteGroupById(item.groupId)
              .then(function(data) {
                if (data.code===AgreeConstant.resultCode) {
                  interfaceMap = {};
                  $scope.map = {};
                  getData(1);
                  inform.common(Trans("tip.delSuccess"));
                } else {
                  inform.common(data.message);
                }
              }, function() {
                inform.common(Trans("tip.requestError"));

              });
          }
        });
      }
    }
  ]);
})();
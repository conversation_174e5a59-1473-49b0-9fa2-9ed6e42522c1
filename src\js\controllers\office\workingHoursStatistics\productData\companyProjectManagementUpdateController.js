(function () {
  app.controller('companyProjectManagementUpdate', [
    'OfficeFileTool',
    'productTypeNameModule',
    'projectManagementService',
    'proProjectModule',
    'hardwareModeModule',
    'customerModule',
    'comService',
    '$rootScope',
    '$scope',
    'companyProjectManagementService',
    '$stateParams',
    'inform',
    'Trans',
    '$modal',
    'AgreeConstant',
    'LocalCache',
    '$state',
    function (
      OfficeFileTool,
      productTypeNameModule,
      projectManagementService,
      proProjectModule,
      hardwareModeModule,
      customerModule,
      comService,
      $rootScope,
      $scope,
      companyProjectManagementService,
      $stateParams,
      inform,
      Trans,
      $modal,
      AgreeConstant,
      LocalCache,
      $state
    ) {
      /**
       * *************************************************************
       *             初始化部分                                 开始
       * *************************************************************
       */
      // 正则校验配置
      $scope.limitList = AgreeConstant.limitList;
      //增加还是修改0：增加，1：修改
      $scope.isAdd = $stateParams.isAdd;
      //保存平台项目名称
      $scope.SystemProjectName = '';
      //保存已关联的禅道项目信息-id
      $scope.proProjectIdInfoForUL = [];
      //保存已关联的禅道项目信息-name
      $scope.proProjectNameInfoForUL = [];

      $scope.projectApprovalDateUpdate = projectApprovalDateUpdate;
      $scope.xqtcsjDateUpdate = xqtcsjDateUpdate;
      $scope.projectId = $stateParams.projectId;
      $scope.param = {};
      $scope.param.attachmentAddress = [];
      $scope.param.currentAddress = [];
      $scope.param.attachmentSize = [];
      $scope.param.attachmentAddressID = [];
      $scope.param.marketDecisionName = '';
      //项目状态下拉框数
      $scope.projectStatusSelect = [
        {
          value: '0',
          label: '待启动',
        },
        {
          value: '1',
          label: '进行中',
        },
        {
          value: '2',
          label: '结项',
        },
        {
          value: '3',
          label: '暂停',
        },
        {
          value: '4',
          label: '终止',
        },
      ];
      if ($scope.isAdd === '0') {
        $scope.param.projectStatus = '1';
      }

      //级别
      $scope.xmjbList = ['I级', 'II级', 'III级'];
      //行业
      $scope.xqsshySelect = ['金融', '物流', '新零售', '新兴', '通用'];
      //产品用途
      $scope.projectPurposeSelect = ['技术规划', '市场规划', '销售支撑', '运营支撑'];
      //项目类型
      $scope.projectTypeSelect = ['开发类', '维护类'];
      //软件价值分类
      $scope.softwareValueClassificationSelect = ['软硬一体', '以软促硬', '硬件配套', '技术研发', '其他'];
      //立项类型
      $scope.sqlxSelect = ['预研立项', '开发立项', '预研转开发'];
      //需求类型
      $scope.xqlxList = ['定制产品', '通用产品'];
      //市场定位
      $scope.xqsyfwSelect = ['国内', '海外', '全球'];
      //开发主体
      $scope.kfztList = ['产品研发', '技术研发', '系统集成', '对外合作'];
      //产权归属  多选
      $scope.cqgsSelect = ['新北洋', '荣鑫', '数码', '正棋'];
      //成本重要等级
      $scope.cbzydjSelect = ['一般', '重要'];
      //产品类别
      $scope.cplbSelect = ['软件类', '硬件类', '综合类'];
      //生产归属
      $scope.scgsSelect = ['新北洋', '荣鑫', '数码'];
      //项目归属
      $scope.xmgzwbList = ['新北洋', '荣鑫', '数码', '正棋'];
      //创建文件上传组件
      var paramObj = {
        listId: 'thelist',
        removeCall: function (id) {
          var index = $scope.param.attachmentAddressID.indexOf(id);
          $scope.param.marketDecisionName = '';
          $scope.$apply();
          $scope.param.currentAddress.splice(index, 1);
          $scope.param.attachmentAddress.splice(index, 1);
          $scope.param.attachmentSize.splice(index, 1);
          $scope.param.attachmentAddressID.splice(index, 1);
        },
        getFilePathCall: function (fileId) {
          var index = $scope.param.attachmentAddressID.indexOf(fileId);
          var filePath = $scope.param.attachmentAddress[index];
          return filePath;
        },
        getSizeOfFiles: function () {
          var size = 0;
          for (var i = 0; i < $scope.param.attachmentSize.length; i++) {
            size = size + parseInt($scope.param.attachmentSize[i]);
          }
        },
        uploadSuccess: function (file, response) {
          if ($scope.param.marketDecisionName === '') {
            $scope.param.marketDecisionName = file.name.split('.')[0];
            $scope.$apply();
          }
          $scope.param.attachmentAddress.push(response.data);
          $scope.param.currentAddress.push(response.data + '|' + file.size);
          $scope.param.attachmentAddressID.push(file.id);
          $scope.param.attachmentSize.push(file.size);
        },
      };
      var uploader = OfficeFileTool.createUploader(paramObj, 'decision');
      //获取数据
      $scope.getData = getData;
      getData();

      //获取已关联的禅道项目清单
      getProProjectInfoList();
      //设置列表的高度
      setDivHeight();
      //初始化页面信息
      initPages();
      $(window).resize(setDivHeight); //窗体大小变化时重新计算高度

      /**
       * *************************************************************
       *              初始化部分                                 结束
       * *************************************************************
       */

      /**
       * *************************************************************
       *              方法声明部分                                开始
       * *************************************************************
       */

      /**
       * 设置列表的高度
       */
      function setDivHeight() {
        //网页可见区域高度
        var clientHeight = document.body.clientHeight;
        var divHeight = clientHeight - (150 + 185);
        $('#divTBDis').height(divHeight);
        $('#subDivTBDis').height(divHeight - 50);
        var clientWidth = document.body.clientWidth;
        $('#buttonStyle').css(inform.getButtonStyle(clientHeight, clientWidth));
      }
      /**
       * 页面初始化
       */
      function initPages() {
        //获取产品线-产品类别-产品名称集合
        $scope.productLineAndType = [];
        $scope.productLineAndTypeMap = {};
        comService.getParamList('PRODUCT_TYPE', '').then(function (data) {
          if (data.data) {
            $scope.productLineAndType = data.data;
            angular.forEach($scope.productLineAndType, function (item) {
              $scope.productLineAndTypeMap[item['param_code']] = item['param_value'];
            });
          }
        });
        //获取项目活动完成率表中相关的项目
        companyProjectManagementService.getFinishRatePro().then(function (data) {
          if (data.data) {
            $scope.finishRateProjectList = data.data;
          }
        });
      }
      /**
       * 获取项目
       */
      function getData() {
        if ($scope.isAdd === '0') {
          return;
        }
        var urlData = {
          projectId: $scope.projectId, //项目编号
        };
        companyProjectManagementService.getCompanyProjectInfoList(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              //项目
              $scope.param = data.data.list[0];
              //设置开发主体的反显
              if ($scope.param.kfztwb) {
                var kfztBoxes = document.getElementsByName('kfztwb');
                var currentKfzt = $scope.param.kfztwb.split('&');
                //获取页面所有开发主体
                for (var i = 0; i < kfztBoxes.length; i++) {
                  //据有的开发主体
                  for (var j = 0; j < currentKfzt.length; j++) {
                    if (kfztBoxes[i].value === currentKfzt[j]) {
                      kfztBoxes[i].checked = true;
                    }
                  }
                }
              }

              //设置产权归属的反显
              if ($scope.param.cqgzwb) {
                var cqgsBoxes = document.getElementsByName('cqgzwb');
                var currentCqgs = $scope.param.cqgzwb.split('&');
                //获取页面所有产权归属
                for (var m = 0; m < cqgsBoxes.length; m++) {
                  //据有的产权归属
                  for (var n = 0; n < currentCqgs.length; n++) {
                    if (cqgsBoxes[m].value === currentCqgs[n]) {
                      cqgsBoxes[m].checked = true;
                    }
                  }
                }
              }

              $scope.param.attachmentAddressID = [];
              $scope.param.currentAddress = [];
              $scope.param.attachmentAddress = [];
              $scope.param.attachmentSize = [];
              if (null != $scope.param.marketDecisionPath && '' !== $scope.param.marketDecisionPath) {
                $scope.param.flag = '0';
                angular.forEach($scope.param.marketDecisionPath.split(','), function (res, index) {
                  var path = res.split('|');
                  $scope.param.attachmentAddress.push(path[0]);
                  $scope.param.attachmentSize.push(path[1]);
                  $scope.param.currentAddress.push(path[0] + '|' + path[1]);
                });
                //创建回显的文件列表 返回文件id集合
                var fileIdList = uploader.initShowFileList($scope.param.attachmentAddress);
                if (fileIdList.length > 0) {
                  $scope.param.attachmentAddressID = fileIdList;
                }
              }
            } else {
              inform.common(data.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }
      /**
       * 修改信息
       */
      $scope.updateInfo = function () {
        // 将多选项拼接为字符串
        function getCheckedValues(checkboxName, separator) {
          var checkboxes = document.getElementsByName(checkboxName);
          var values = [];

          checkboxes.forEach(function (checkbox) {
            if (checkbox.checked) {
              values.push(checkbox.value);
            }
          });

          return values.join(separator);
        }

        // 开发主体赋值
        var kfztwb = getCheckedValues('kfztwb', '&');

        // 产权归属赋值
        var cqgzwb = getCheckedValues('cqgzwb', '&');

        $scope.param.proProjectId = $scope.proProjectIdInfoForUL.join(',');
        var urlData = {
          projectId: $scope.projectId,
          projectCode: $scope.param.projectCode,
          projectName: $scope.param.projectName,
          projectType: $scope.param.projectType,
          projectStatus: $scope.param.projectStatus,
          productLine: $scope.param.productLine, //软件产品线
          productType: $scope.param.productType, //产品类别
          productSubType: $scope.param.productSubType, //产品名称
          hardwareModeNo: $scope.param.hardwareModeNo, //型号
          customerId: $scope.param.customerId, //客户
          softwareValueClassification: $scope.param.softwareValueClassification, //软件价值分类
          projectPurpose: $scope.param.projectPurpose, //产品用途
          projectApprovalDate: inform.format($scope.param.projectApprovalDate, 'yyyy-MM-dd'),
          projectEndDate: inform.format($scope.param.projectEndDate, 'yyyy-MM-dd'),
          marketDecisionName: $scope.param.marketDecisionName, //决议函名称
          marketDecisionId: $scope.param.marketDecisionId, //决议函id
          marketDecisionAcceptDate: inform.format($scope.param.marketDecisionAcceptDate, 'yyyy-MM-dd'), //决议函接收时间
          marketDecisionPath: $scope.param.currentAddress.join(','), //决议函附件路径
          proProjectId: $scope.param.proProjectId, //关联禅道项目
          finishRateProCode: $scope.param.finishRateProCode, //项目活动完成率关联的项目

          sqlxwb: $scope.param.sqlxwb, //立项类型
          sqr: $scope.param.sqr, //申请人
          xqtcbmwb: $scope.param.xqtcbmwb, //申请提出部门
          xqtcsj: inform.format($scope.param.xqtcsj, 'yyyy-MM-dd'), //申请提出时间
          scdwwb: $scope.param.scdwwb, //市场定位
          xqszywbkwb: $scope.param.xqszywbkwb, //行业
          xmgzwb: $scope.param.xmgzwb, //项目归属
          cqgzwb: cqgzwb, //产权归属
          xmjbwb: $scope.param.xmjbwb, //项目级别
          cbzydj: $scope.param.cbzydj, //成本重要等级
          xqlxwb: $scope.param.xqlxwb, //需求类型
          cplbwb: $scope.param.cplbwb, //产品类别
          kfztwb: kfztwb, //开发主体
          scgzwb: $scope.param.scgzwb, //生产归属

          scgmwb: $scope.param.scgmwb, //市场规模
          xmyq: $scope.param.xmyq, //项目要求
          xssl1: $scope.param.xssl1, //销售预期第一年
          xssl2: $scope.param.xssl2, //销售预期第二年
          xssl3: $scope.param.xssl3, //销售预期第三年
          cpsmzq: $scope.param.cpsmzq, //产品生命周期
          cpdj: $scope.param.cpdj, //产品单价
          gnyjsjyq: $scope.param.gnyjsjyq,
          gnyjslyq: $scope.param.gnyjslyq,
          gcyjsjyq: $scope.param.gcyjsjyq,
          gcyjslyq: $scope.param.gcyjslyq,
          mjyjsjyq: $scope.param.mjyjsjyq,
          mjyjslyq: $scope.param.mjyjslyq,
          zsyzsjyq: $scope.param.zsyzsjyq,
          zsyzslyq: $scope.param.zsyzslyq,
          plghsjyq: $scope.param.plghsjyq,
          plghslyq: $scope.param.plghslyq,
          yjddxdsj: $scope.param.yjddxdsj,

          //    'spType':$scope.param.spType//sp类型
        };
        if ($scope.isAdd === '0') {
          urlData.isSyncProject = '1';
          companyProjectManagementService.addCompanyProjectInfo(urlData).then(
            function (data) {
              judgeData(data);
            },
            function (error) {
              inform.common(Trans('tip.requestError'));
            }
          );
        } else {
          companyProjectManagementService.updateCompanyProjectInfo(urlData).then(
            function (data) {
              judgeData(data);
            },
            function (error) {
              inform.common(Trans('tip.requestError'));
            }
          );
        }
      };
      function judgeData(data) {
        if (data.code === AgreeConstant.code) {
          inform.common(data.message);
          $scope.goback();
        } else {
          //弹框提示
          inform.common(data.message);
        }
      }
      /**
       *清空产品线-产品类别-产品名称-型号
       */
      $scope.clearProductLines = function () {
        $scope.param.productLine = '';
        $scope.param.productType = '';
        $scope.param.productSubType = '';
      };
      /**
       *清空型号
       */
      $scope.clearHardwareModeNo = function () {
        $scope.param.hardwareModeNo = '';
      };
      //清空客户名称
      $scope.clearCustomerName = function () {
        $scope.param.customerId = '';
        $scope.param.customerName = '';
      };

      /**
       * 返回项目信息历
       */
      $scope.goback = function () {
        $state.go('app.office.companyProjectManagement');
      };
      /**
       *  新增的开始时间按钮
       */
      $scope.projectApprovalDate_update = function ($event) {
        $event.preventDefault();
        $event.stopPropagation();
        $scope.startTime1 = false;
        $scope.startTime_update1 = true;
        $scope.endTime1 = false;
        $scope.endTime_update1 = false;
      };
      /**
       *  新增的结束时间按钮
       */
      $scope.projectEndDate_update = function ($event) {
        $event.preventDefault();
        $event.stopPropagation();
        $scope.startTime1 = false;
        $scope.startTime_update1 = false;
        $scope.endTime1 = false;
        $scope.endTime_update1 = true;
      };
      //决议函立项时间
      function projectApprovalDateUpdate($event) {
        $event.preventDefault();
        $event.stopPropagation();
        $scope.projectApprovalDate_update1 = true;
      }
      //需求提出时间
      function xqtcsjDateUpdate($event) {
        $event.preventDefault();
        $event.stopPropagation();
        $scope.xqtcsj_update1 = true;
      }
      $scope.initCustomerModule = function () {
        var data = {
          keyWord: $scope.param.customerName,
        };
        customerModule.initModule(data, $scope, setCustomerInfo);
      };
      /**
       * 根据所选中的客户回填信息
       */
      function setCustomerInfo(data) {
        $scope.param.customerName = data.name;
        $scope.param.customerId = data.id;
      }

      $scope.initHardwareModeModule = function () {
        var data = {
          productLine: $scope.param.productLine,
          productType: $scope.param.productType,
          productName: $scope.param.productSubType,
          hardwareModeNo: $scope.param.hardwareModeNo,
        };
        hardwareModeModule.initModule(data, $scope, setHardwareModeInfo);
      };
      /**
       * 根据所选中的硬件型号回填信息
       */
      function setHardwareModeInfo(data) {
        $scope.param.hardwareModeNo = data.name;
      }
      /**
       * 产品类别-名称
       */
      $scope.initProductTypeNameModule = function () {
        var data = {
          productLine: $scope.param.productLine,
          productType: $scope.param.productType,
          productName: $scope.param.productSubType,
        };
        productTypeNameModule.initModule(data, $scope, setProductTypeNameInfo);
      };
      /**
       * 根据所选中的产品回填信息
       */
      function setProductTypeNameInfo(data) {
        $scope.param.productLine = data.productLine;
        $scope.param.productType = data.productType;
        $scope.param.productSubType = data.productName;
      }
      /**
       * 初始化产品线与禅道项目弹框
       */
      $scope.initProProjectModule = function (flag) {
        var data = {
          keyWord: '', //$scope.param.projectName
        };
        proProjectModule.initModule(data, $scope, setProProjectInfo);
      };
      /**
       * 根据所选中的禅道项目回填信息
       */
      function setProProjectInfo(data) {
        getProProjectRelation(data);
      }
      /**
       * 根据所选禅道项目查看是否有关联的公司立项项目
       */
      function getProProjectRelation(paramData) {
        var urlData = {
          projectId: $scope.projectId,
          proProjectId: paramData.id,
        };
        companyProjectManagementService.getProProjectRelation(urlData).then(
          function (data) {
            if (data.data && data.data.length !== 0) {
              inform.common('该禅道项目已关联公司立项项目-' + data.data[0] + '，请重新选择。');
              return;
            }
            if ($scope.proProjectIdInfoForUL.indexOf(paramData.id) === -1) {
              $scope.proProjectIdInfoForUL.push(paramData.id);
              $scope.proProjectNameInfoForUL.push(paramData.name);
              initProProjectUL();
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      }

      /**
       * 获取已关联的禅道项目清单
       **/
      function getProProjectInfoList() {
        //新增时不需要跳过
        if ($scope.isAdd === '0') {
          return;
        }
        var urlData = {
          projectId: $scope.projectId,
        };
        companyProjectManagementService.getProProjectInfoList(urlData).then(function (data) {
          if (data.code === AgreeConstant.code) {
            if (data.data && data.data.length > 0) {
              angular.forEach(data.data, function (item) {
                $scope.proProjectIdInfoForUL.push(item.projectId);
                $scope.proProjectNameInfoForUL.push(item.projectName);
              });
              //初始化关联禅道项目组件值
              initProProjectUL();
            }
          }
        });
      }
      /**
       * 初始化关联禅道项目组件值
       **/
      function initProProjectUL() {
        if ($scope.proProjectNameInfoForUL.length === 0) {
          return;
        }
        $('#proProjectUL li').remove();
        angular.forEach($scope.proProjectNameInfoForUL, function (item, index) {
          var liHtml =
            '<li index=' +
            index +
            ' class="search-choice"><span>' +
            item +
            '</span><a class="search-choice-close" href="javascript:void(0);"></a></li>';
          $('#proProjectUL').append(liHtml);
          //绑定删除事件
          clearEventOfLI();
        });
      }

      /**
       * 绑定删除事件
       **/
      function clearEventOfLI() {
        //先解绑
        $('#proProjectUL li a').unbind();
        //绑定删除事件
        $('#proProjectUL li a')
          .bind()
          .click(function () {
            var projectName = $(this).parent().children('span').text();
            var index = $scope.proProjectNameInfoForUL.indexOf(projectName);
            $scope.proProjectIdInfoForUL.splice(index, 1);
            $scope.proProjectNameInfoForUL.splice(index, 1);

            //删除li节点
            $(this).parent().remove();
            if ($scope.proProjectNameInfoForUL.length === 0) {
              var liHtml = '<li class="search-choice"><span>请单击右侧图标选择禅道项目</span></li>';
              $('#proProjectUL').append(liHtml);
            }
            $(this).parent().remove();
          });
      }



      /**
       * *************************************************************
       *              方法声明部分                                结束
       * *************************************************************
       */
    },
  ]);
})();

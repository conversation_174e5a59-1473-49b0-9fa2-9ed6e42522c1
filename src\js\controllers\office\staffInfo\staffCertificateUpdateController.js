(function() {
    'use strict';
    app.controller("staffCertificateUpdateController", ['comService','$rootScope', '$stateParams', '$scope', 'staffCertificateService','$modal','inform', 'Trans', 'AgreeConstant','$state', 'LocalCache', '$http',
        function(comService, $rootScope,$stateParams,$scope, staffCertificateService,$modal,inform, Trans, AgreeConstant,$state, LocalCache, $http) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            $scope.datepicker = {
               currentDate :  new Date()
            };

            setDivHeight();
            $scope.flag = $stateParams.searchObjectState==='05'?true:false;
            getStaffAllName();//通过员工编号获取员工姓名
            getCertificateTypeList();//获取证书列表
            getCertificateInfo();//根据id查询员工证书信息
            getPicture();//根据id查询图片
            var fileTmp = "";

           /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */

             function getPicture(){
               staffCertificateService.getPicture($stateParams.id).then(function(data){
                   document.getElementById('img_picture_view').src ="data:image/*;Base64,"+data.data;
                   if(data.data ==null||data.data===''){
                      $("#img_picture_del").css("display", "none");
                      $("#img_picture_view").css("display", "none");
                      $("#prompt3").css("display", "block");
                   }else{
                      fileTmp =data.data;
                      $("#prompt3").css("display", "none");
                      $("#img_picture_del").css("display", "block");
                      $("#img_picture_view").css("display", "block");
                      $("#img_picture_view").css("height", "398px");
                      $("#img_picture_view").css("width", "798px");
                   }
               });

             }

             //根据id获取证书信息
             function getCertificateInfo(){
               staffCertificateService.getCertificateInfo($stateParams.id).then(function(data){
                   $scope.updateParam = data.data;
               });
             }


            //获取证书类别
            function getCertificateTypeList(){
                $scope.certificateTypeList = [];
                comService.getCertificateTypeList().then(function(data) {
                    $scope.certificateTypeList = data.data;
                });
            }

          //获得所有员工姓名
          function getStaffAllName(){
              $scope.staffNameList = [];
              staffCertificateService.getStaffAllName().then(function (result){
                  if (result.code===AgreeConstant.code) {
                      $scope.staffNameList = result.data;
                  }else{
                      inform.common("获取失败");
                  }
              });
          }


          $scope.$watch('updateParam.employeeName',function(employeeName){
              if (employeeName !== undefined) {
                  staffCertificateService.getStaffId(employeeName).then(function(data) {
                      $scope.updateParam.employeeId = data.data;
                  });
              }
          });


            //选择图片
           $scope.changePic = function() {
                 var f = document.getElementById("myFile").files[0];
                 var index_int = f.name.lastIndexOf(".");
                 var lastArr = f.name.substring(index_int);
                 var readsFile = new FileReader();
                 var formData = new FormData($('#formTmp')[0]);
                 formData.append('file', f);
                 formData.append('id', $stateParams.id);
                 var file = document.querySelector('input[type=file]').files[0]; //获取文档中有类型为file的第一个input元素
                 event.target.value="";//每次都要清除file
                 if(lastArr!==".bmp"&&lastArr!==".png"&&lastArr!==".gif"&&lastArr!==".jpg"&&lastArr!==".jpeg"){
                     layer.confirm("只支持bmp、png、gif、jpg、jpeg格式上传。",{
                        title:false,
                        btn:['确定']
                     },function(result){
                        if(""=== fileTmp){
                            $("#prompt3").css("display", "block");
                        }
                        layer.close(result);
                     });
                 }else{
                 //上传文件大小不能超过4M (1024*1024*4 = 4194304)
                     if(f.size > 4194304){
                         inform.common("上传文件大小不能超过4M");
                         if(""=== fileTmp){
                            $("#prompt3").css("display", "block");
                         }
                       return;
                     }
                     $("#prompt3").css("display", "none");
                     var reads = new FileReader();
                           reads.readAsDataURL(f);
                           reads.onload = function(e) {
                             document.getElementById('img_picture_view').src = this.result;
                             $("#img_picture_view").css("display", "block");
                             $("#img_picture_del").css("display", "inline-block");
                             $("#img_picture_view").css("height", "398px");
                             $("#img_picture_view").css("width", "798px");
                     };

                     inform.modalInstance("确定要修改照片吗?").result.then(function() {
                         if(f.size>0){
                             fileTmp = f;
                             if(!file){
                                 inform.common("请先选择文件!");
                                 return false;
                             }

                             inform.uploadFile('picture/updatePhoto',formData,function (result) {

                                 // 关闭遮罩层
                                 inform.closeLayer();
                                 layer.confirm("上传图片成功",{
                                      title:false,
                                      btn:['确定']
                                 },function(result){

                                      layer.close(result);
                                      //刷新图片
                                      getPicture();
                                 });

                             });
                             readsFile.readAsDataURL(f);
                             readsFile.onload = function(e) {
                                  document.getElementById('img_picture_view').src = this.result;
                                  $("#img_picture_del").css("display", "block");
                                  $("#img_picture_view").css("height", "398px");
                                  $("#img_picture_view").css("width", "798px");
                             };

                         }
                     }, function (reason) {
                     //点击取消或者空白区域刷新图片
                         getPicture();
                     });
                }
            }
            /**
             * 设置列表的高度
             */
            function setDivHeight(){
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var clientWidth = document.body.clientWidth;
                $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
            }

            //删除该员工的照片
           $scope.img_del = function(){
               inform.modalInstance("确定要删除照片吗？").result.then(function() {
                  staffCertificateService.delPicByParam($stateParams.id).then(function (result) {
                      if (result.code ==='0000'){
                         inform.common(result.message);
                         getPicture();
                         $("#img_picture_del").css("display", "none");
                         document.getElementById('img_picture_view').src = "#";
                         var obj = document.getElementById('myFile');
                         obj.outerHTML = obj.outerHTML;

                      }else{
                         inform.common(result.message);
                      }

                  });
               });
           }


           //返回
           $scope.goBack = function (){
                $state.go('app.office.staffCertificateController');
           };


           //保存修改数据
           $scope.saveUpdateData = function () {
               if($scope.updateParam.getCertificateTime==='' ||$scope.updateParam.getCertificateTime==null||$scope.updateParam.getCertificateTime=== undefined ){
                  $scope.updateParam.getCertificateTime = "";
               }else{
                  $scope.updateParam.getCertificateTime = inform.format($scope.updateParam.getCertificateTime,'yyyy-MM-dd');
               }
               if($scope.updateParam.certificateValidity==='' ||$scope.updateParam.certificateValidity==null||$scope.updateParam.certificateValidity=== undefined ){
                  $scope.updateParam.certificateValidity = "";
               }else{
                  $scope.updateParam.certificateValidity = inform.format($scope.updateParam.certificateValidity,'yyyy-MM-dd');
               }
               var param = {
                   "id":$stateParams.id,
                   "employeeId":$scope.updateParam.employeeId,
                   "certificate":$scope.updateParam.certificate,
                   "getCertificateTime":$scope.updateParam.getCertificateTime,
                   "certificateType":$scope.updateParam.certificateType,
                   "certificateValidity":$scope.updateParam.certificateValidity

               };
               staffCertificateService.updateByParam(param).then(function (result) {
                   if ( result.code==='0000') {
                   $("#update_staffInfo").modal("hide");
                        layer.confirm(result.message,{
                           title:false,
                           btn:['确定']
                        },function(res){
                           $scope.goBack();
                        });
                   }else{
                       inform.common(result.message);
                   }
               });
           }


            //点击选择图片
            $scope.updatePic = function() {
                  $('#myFile').trigger('click');
            };

           //获得证书时间
           $scope.openCertificateTime = function($event) {
               $event.preventDefault();
               $event.stopPropagation();
               $scope.openCertificateTime1 = true;
               $scope.openCertificateTime2 = false;
           };
           //获得证书有效期时间
           $scope.openCertificateValidityTime = function($event) {
               $event.preventDefault();
               $event.stopPropagation();
               $scope.openCertificateTime1 = false;
               $scope.openCertificateTime2 = true;
           };

 /**
         * *************************************************************
         *              方法声明部分                                结束
         * *************************************************************
         */


}
  ]);
})();
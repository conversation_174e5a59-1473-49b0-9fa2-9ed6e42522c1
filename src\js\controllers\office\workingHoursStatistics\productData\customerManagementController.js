
(function() {
	app.controller("customerManagement", ['comService', '$rootScope', '$scope', 'customerManagementService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', '$stateParams','LocalCache',
		function(comService, $rootScope, $scope, customerManagementService, inform, Trans, AgreeConstant, $modal, $state, $stateParams,LocalCache) {
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	//获取缓存
		$scope.formRefer ={};
        $scope.formRefer = LocalCache.getObject('customerManagement_formRefer');
        if (Object.keys($scope.formRefer).length <= 0) {
            $scope.formRefer.customerStatus = "0";
        }
        //对原缓存进行覆盖
        LocalCache.setObject("customerManagement_formRefer",{});
        //页面分页信息
        $scope.pages = {
            pageNum : '',   //分页页数
            size : '',      //分页每页大小
            total : ''      //数据总数
        };
    	// 初始化分页数据
    	$scope.pages = inform.initPages();
        //行业
        $scope.professionList = ['金融','物流','新零售','新兴'];
        //客户状态下拉框数
		$scope.customerStatusSelect = [{
			value: '0',
			label: '启用'
		},{
			value: '1',
			label: '归档'
		}];
		//状态展示Map
		$scope.customerStatusMap = {
			"0":'启用',
			"1":'归档'
		};
        $scope.addInfo=false;
        $scope.updateInfo=false;
        //设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		//获取数据
		$scope.getData = getData;
		getData();
        //判断按钮是否具有权限
        getButtonPermission();
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */

         /**
          * 获取按钮权限
          */
         function getButtonPermission(){
             var buttons = {
                 'Button-customerController-addInfo':'addInfo',
                 'Button-customerController-updateInfo':'updateInfo'
             };
             var urlData = {
                 'userId':LocalCache.getSession("userId"),
                 'parentPermission':'ButtonCustomerController',
                 'buttons':buttons
             };
             comService.getButtonPermission(urlData,$scope);
         }

		/**
		 * 获取项目
		 */
		function getData(pageNum) {
			var urlData ={
			    'customerName':$scope.formRefer.customerName,//客户名称
			    'customerId':$scope.formRefer.customerId,//客户编号
			    'profession':$scope.formRefer.profession,//行业
			    'customerStatus':$scope.formRefer.customerStatus,//状态
                'page':pageNum,
                'pageSize':$scope.pages.size
			};
			customerManagementService.getCustomerInfoList(urlData).then(function(data) {
				if (data.code===AgreeConstant.code) {
                    //客户 信息
                    $scope.customerData = data.data.list;
                    if ($scope.customerData.length===0) {
						$scope.pages = inform.initPages(); 			//初始化分页数据
						inform.common(Trans("tip.noData"));
                    } else {
                    // 分页信息设置
                    	$scope.pages.total = data.data.total;           // 页面数据总数
                    	$scope.pages.star = data.data.startRow;         // 页面起始数
                    	$scope.pages.end = data.data.endRow;            // 页面结束数
                    	$scope.pages.pageNum = data.data.pageNum;       //页号
                    }
				} else {
					inform.common(data.message);
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
		}
		/**
    	 * 设置列表的高度
    	 */
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 185);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 85);
 		}

	   /**
	    * 修改信息弹框，str存在，就是新增
	    */
       $scope.popModal = function (item,str){
        if(str==="0"){
                //新增
          $scope.customerInfoParam = {};
        }else{
        //修改
          $scope.customerInfoParam = angular.copy(item);
        }
        LocalCache.setObject('customerManagement_formRefer', $scope.formRefer);
        $state.go('app.office.customerManagementUpdate',{
        	customerInfoParam:JSON.stringify($scope.customerInfoParam),
        	isAdd:str
        });
      };

        /**
	     * *************************************************************
	     *              方法声明部分                                结束
	     * *************************************************************
	     */
		} ]);
})();

(function () {
    app.controller("projectWorkingHoursBoardController", ['$ocLazyLoad', '$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http', 'workingHoursBoardFactory', 'projectWorkingBoardService',
        function ($ocLazyLoad, $rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, inform, Trans, AgreeConstant, LocalCache, $http, workingHoursBoardFactory, projectWorkingBoardService) {
            // 初始化
            workingHoursBoardFactory.init($scope, '人年');
            $scope.title = '查询列表';
            $scope.pages = {
                pageNum : '', 		// 分页页数
                size : '', 			// 分页每页大小
                total : '' 			// 数据总数
            };
            $scope.sortColorList = AgreeConstant.workingHoursBoard.sortColorList;
            //初始化分页数据
            $scope.pages = inform.initPages();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.setDivHeight = setDivHeight;
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (370);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 72);
            }

            // 重置部分
            $scope.resetParam = resetParam;
            function resetParam(){
                $scope.formRefer={};
                workingHoursBoardFactory.initTime($scope, '本年度');
            }

            // 获取列表数据
            $scope.getData = getData;
            function getData(pageNum) {
                var urlData = {
                    'startDate': $scope.formRefer.startTime,
                    'endDate': $scope.formRefer.endTime,
                    'orgCode': $scope.formRefer.orgCode,
                    'productLineCode': $scope.formRefer.productLine,
                    'page': pageNum,
                    'pageSize': $scope.pages.size
                }
                projectWorkingBoardService.getProjectHoursInfo(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.dataList = data.data.list;
                                $scope.sortData($scope.type, $scope.sort);
                                // 分页信息设置
                                $scope.pages.total = data.data.total;           // 页面数据总数
                                $scope.pages.star = data.data.startRow;         // 页面起始数
                                $scope.pages.end = data.data.endRow;            // 页面结束数
                                $scope.pages.pageNum = data.data.pageNum;       //页号
                                $scope.startTimeAfterSearch = $scope.formRefer.startTime;
                                $scope.endTimeAfterSearch = $scope.formRefer.endTime;
                            } else {
                                inform.common(Trans("tip.noData"));
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 排序
            $scope.sortData = function (type, sort) {
                return workingHoursBoardFactory.sortData($scope, type, sort, $scope.dataList);
            }
            $scope.activeClass = function (type, sort){
                return workingHoursBoardFactory.activeClass(type, sort, $scope.type, $scope.sort);
            }
            $scope.activeTitleClass = function (type) {
                return workingHoursBoardFactory.activeTitleClass(type, $scope.type);
            }
            $scope.dealBgc = function (number, index, property) {
                return workingHoursBoardFactory.dealBgc(number, index, property, $scope.dataList, $scope.type, $scope.sort);
            }

            // 获取产品线和部门下拉框选项
            function getInitData() {
                //获取产品线
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                    if (data.data) {
                        $scope.projectLine = data.data;
                    }
                });
                //获取系统集成研发的下级部门信息
                $scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentList = comService.getDepartment(data.data);
                });
            }

            // 跳转到详情页
            $scope.toProjectWorkingDetail = function (m) {
                var projectWorkingHoursDetail_formRefer = $scope.formRefer;
                projectWorkingHoursDetail_formRefer['searchTimeString'] = $scope.butFlag;
                LocalCache.setObject('projectWorkingHoursDetail_formRefer', projectWorkingHoursDetail_formRefer);
                $state.go('app.office.projectWorkingHoursDetail', {
                    'startTime': $scope.startTimeAfterSearch,
                    'endTime': $scope.endTimeAfterSearch,
                    'searchTimeString': $scope.butFlag,
                    'teamCode': m.projectId,
                    "projectName": m.projectName
                });
            }
            // 页面加载后触发
            $scope.$watch('$viewContentLoaded', function () {
                $scope.type = 'allHoursMonth';
                $scope.sort = 'desc';
                getInitData();
                if ($stateParams.orgCode) {
                    $scope.formRefer.orgCode = $stateParams.orgCode;
                }
                if ($stateParams.sortType) {
                    $scope.type = $stateParams.sortType;
                }
                var localFormRefer = LocalCache.getObject('projectWorkingHoursDetail_formRefer');
                if (Object.keys(localFormRefer).length > 0) {
                    $scope.formRefer = localFormRefer;
                    $scope.butFlag = localFormRefer.searchTimeString;
                    LocalCache.setObject("projectWorkingHoursDetail_formRefer",{});
                }
                getData($scope.pages.pageNum);
            });
        }]);
})();

/*
 * @Author: dongyinggang
 * @Date:   2019-06-13 17:50:05
 * @Last Modified by:   dongyinggang
 * @Last Modified time:  2019-06-13 17:50:05
 */
(function () {
    'use strict';
    app.factory('bugDataManagementService', bugDataManagementService);
    bugDataManagementService.$inject = ["HttpService", '$rootScope'];

    function bugDataManagementService(HttpService, $rootScope) {

        var service = {

            getBugData: getBugData,
            getBugDetail: getBugDetail,
            updateBugDetail: updateBugDetail

        };
        return service;

        /**
         * 分页查询bug数据
         */
        function getBugData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'bugDataManagement/getBugData', urlData);
        }

        function getBugDetail(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'bugDataManagement/getBugDataById', urlData);
        }

        function updateBugDetail(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'bugDataManagement/updateBugDetail', urlData);
        }

    }
})();


(function() {
	app.controller("customerWorkTimeManagementDetail", ['comService', '$rootScope', '$scope', 'inform', 'customerWorkTimeService','Trans', 'AgreeConstant', '$modal', '$state', '$stateParams','LocalCache',
		function(comService, $rootScope, $scope,inform, customerWorkTimeService, Trans, AgreeConstant, $modal, $state, $stateParams,LocalCache) {
		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
    	//获取缓存
		$scope.customerDto ={};
        $scope.customerDto = LocalCache.getObject('customerWorkTime_formRefer');
        $scope.showType = '1';
        //设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		//获取数据
		$scope.getData = getData;
		getData();

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                开始
		 * *************************************************************
		 */

		/**
		 * 获取项目
		 */
		function getData(pageNum) {
			var urlData ={
			    'customerId':$scope.customerDto.customerDto.customerId,//客户id
                'startDate':$scope.customerDto.startMonth,//开始时间
                'endDate':$scope.customerDto.endMonth//结束时间
			};
			//查询客户的公司立项项目
			getCustomerCompanyPro(urlData);
            //查询其他立项项目
            getOtherCompanyPro(urlData);
            //查询客户下的系研项目
            getXyProjectList(urlData);
            //查询客户下plm工时信息
            getPLMWorkTimeDetail(urlData);
		}

		function getPLMWorkTimeDetail(urlData){
            customerWorkTimeService.getPLMWorkTimeDetail(urlData).then(function(data) {
                if (data.code===AgreeConstant.code) {
		            $scope.PLMWorkTimeList=data.data;
                }else {
                	inform.common(data.message);
                }
            },
            function(error) {
            	inform.common(Trans("tip.requestError"));
            });
		}

		function getXyProjectList(urlData){
            customerWorkTimeService.getXyProjectList(urlData).then(function(data) {
                if (data.code===AgreeConstant.code) {
		            $scope.projectList=data.data;
                    setTimeout($scope.getDetailData,600);
                }else {
                	inform.common(data.message);
                }
            },
            function(error) {
            	inform.common(Trans("tip.requestError"));
            });
		}

		function getOtherCompanyPro(urlData){
		    $scope.otherCompanySize=0;
		    $scope.otherPlmNum=0;
		    $scope.otherWorkTimeTotal=0;
            customerWorkTimeService.getOtherCompanyPro(urlData).then(function(data) {
                if (data.code===AgreeConstant.code) {
                    $scope.otherCompanyProWorkTimeList = data.data;
                    $scope.otherCompanySize = $scope.otherCompanyProWorkTimeList.length;
                    //遍历集合，获取plm及人力投入合计
                    angular.forEach($scope.otherCompanyProWorkTimeList, function (item, index) {
                        $scope.otherPlmNum = $scope.otherPlmNum+item.plmStoryNum*1;
                        $scope.otherWorkTimeTotal = $scope.otherWorkTimeTotal+item.personWorkTime*1;
                    });
                    $scope.otherWorkTimeTotal = $scope.otherWorkTimeTotal.toFixed(1);
                }else {
                	inform.common(data.message);
                }
            },
            function(error) {
            	inform.common(Trans("tip.requestError"));
            });
		}

		function getCustomerCompanyPro(urlData){
		    $scope.companySize=0;
		    $scope.plmNum=0;
		    $scope.workTimeTotal=0;
            customerWorkTimeService.getCustomerCompanyProWorkTime(urlData).then(function(data) {
                if (data.code===AgreeConstant.code) {
                    $scope.customerCompanyProWorkTimeList = data.data;
                    $scope.companySize = $scope.customerCompanyProWorkTimeList.length;
                    //遍历集合，获取plm及人力投入合计
                    angular.forEach($scope.customerCompanyProWorkTimeList, function (item, index) {
                        $scope.plmNum = $scope.plmNum+item.plmStoryNum*1;
                        $scope.workTimeTotal = $scope.workTimeTotal+item.personWorkTime*1;
                    });
                    $scope.workTimeTotal = $scope.workTimeTotal.toFixed(1);
                }else {
                	inform.common(data.message);
                }
            },
            function(error) {
            	inform.common(Trans("tip.requestError"));
            });
		}

		//获取指定项目或团队下的需求、技术支持、个人工时
		$scope.getDetailData = function(m){
		    if(!m){
		        m = $scope.projectList[0];
		    }
		    //获取所有的页签li
            var lis = document.getElementsByName("projectDetail");
            for(var i=0;i<lis.length;i++){
                if (lis[i].id === m.xyProjectName){
                    lis[i].style.color = 'blue';
                    lis[i].style.fontWeight = 'bolder';
                }else {
                    lis[i].style.color = 'black';
                    lis[i].style.fontWeight = 'normal';
                }
            }
			$scope.detailData ={
			    'xyProjectId':m.xyProjectId,//系研项目/团队id
			    'xyProjectName':m.xyProjectName,//系研项目/团队名称
			    'customerId':$scope.customerDto.customerDto.customerId,//客户id
                'startDate':$scope.customerDto.startMonth,//开始时间
                'endDate':$scope.customerDto.endMonth//结束时间
			};
            //查询指定项目/团队的员工投入情况
            getPersonWorkTimeDetail();
            //查询指定项目/团队的用户需求投入情况
            getStoryWorkTimeDetail();
            //查询指定项目/团队的技术支持投入情况
            getSupportWorkTimeDetail();
		}

		function getPersonWorkTimeDetail(){
		    $scope.personList=0;
            customerWorkTimeService.getPersonWorkTimeDetail($scope.detailData).then(function(data) {
                if (data.code===AgreeConstant.code) {
                    //员工投入情况集合
                    $scope.personWorkTimeList = data.data;
                    if($scope.personWorkTimeList.length===0){
                        $scope.personList=1;
                    }
                }else {
                	inform.common(data.message);
                }
            },
            function(error) {
            	inform.common(Trans("tip.requestError"));
            });
		}

		function getStoryWorkTimeDetail(){
		    $scope.storyList=0;
            customerWorkTimeService.getStoryWorkTimeDetail($scope.detailData).then(function(data) {
                if (data.code===AgreeConstant.code) {
                    //用户需求投入情况集合
                    $scope.storyWorkTimeList = data.data;
                    if($scope.storyWorkTimeList.length===0){
                        $scope.storyList=1;
                    }
                }else {
                	inform.common(data.message);
                }
            },
            function(error) {
            	inform.common(Trans("tip.requestError"));
            });
		}

		function getSupportWorkTimeDetail(){
		    $scope.supportList=0;
            customerWorkTimeService.getSupportWorkTimeDetail($scope.detailData).then(function(data) {
                if (data.code===AgreeConstant.code) {
                    //用户需求投入情况集合
                    $scope.supportWorkTimeList = data.data;
                    if($scope.supportWorkTimeList.length===0){
                        $scope.supportList=1;
                    }
                }else {
                	inform.common(data.message);
                }
            },
            function(error) {
            	inform.common(Trans("tip.requestError"));
            });
		}

		/**
    	 * 设置列表的高度
    	 */
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 205);
 			var clientWidth = document.body.clientWidth;
 			$("#divTBDis").height(divHeight-315);
 			$("#subDivTBDis").height(divHeight-335);

 		    $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
            $("#buttonStyle").css({"width": 100+"px"});
 		}

        /**
	     * *************************************************************
	     *              方法声明部分                                结束
	     * *************************************************************
	     */
		} ]);
})();
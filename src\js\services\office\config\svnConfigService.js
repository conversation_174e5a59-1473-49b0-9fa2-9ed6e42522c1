/*
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date:   2019-01-10 15:20:05
 * @Last Modified by:   l<PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-01-10 15:20:05
 */
(function() {
    'use strict';
  app.factory('svnConfigService', svnConfigService);
  svnConfigService.$inject=["HttpService",'$rootScope'];

  function svnConfigService(HttpService,$rootScope){
    
    var service={
      getConfig:getConfig,
      updateConfig:updateConfig,
      upLogSize:upLogSize,
      getLogSize:getLogSize
    };
    return service;
   /**
    * 获取svn配置
    * 
    * @return       [svn配置信息]
    * */  
	function getConfig() {
		return HttpService.get($rootScope.getWaySystemApi+'config/getConfig',null);
	}
	
	function updateConfig(username, password, path) {
		
		var configData = {'username':username,'password':password,'path':path};
		return HttpService.post($rootScope.getWaySystemApi+'config/updateConfig',configData)
	}
	 /**
	   * 更新显示日志信息条数
	   * @param   logSize [日志信息条数]
	   * @return       [文件日志信息]
	   */
	  function upLogSize(logSize) {
	    return HttpService.post($rootScope.getWaySystemApi+'config/upLogSize',logSize);
	  }
	  /**
	   * 显示日志信息限制
	   * @return       [文件日志信息]
	   */
	  function getLogSize() {
	    return HttpService.post($rootScope.getWaySystemApi+'config/getLogSize',null);
	  }
  }
})();

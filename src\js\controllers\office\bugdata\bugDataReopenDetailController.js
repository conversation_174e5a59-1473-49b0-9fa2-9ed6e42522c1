/*
 * @Author: fubaole
 * @Date:   2017-09-18 14:53:05
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-01-15 17:20:32
 */
(function () {
    app.controller("bugDataReopenDetailController", ['comService', '$rootScope', '$scope', 'bugDataManagementService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http','$stateParams',
        function (comService, $rootScope, $scope, bugDataManagementService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http, $stateParams) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            $scope.getData = getData;
            //设置列表的高度
            setDivHeight();
            //初始化页面信息
            initPages();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            //设置列表的高度
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 210);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 65);
                var clientWidth = document.body.clientWidth;
                $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
            }
            function initPages() {
                //获取员工信息
                $scope.employeeList = [];
                comService.getEmployeesByOrgId('').then(function(data) {
                    if (data.data) {
                        $scope.employeeList = data.data;
                        $scope.employeeMap = {};
                        for(var i = 0;i < $scope.employeeList.length; i++) {
                            $scope.employeeMap[$scope.employeeList[i].realName] = $scope.employeeList[i].companyTitleLevel;
                        }
                    }
                    getData();
                });
            }
            //获取所有数据以分页的形式
            function getData() {

                var urlData = {
                    'bugId':  $stateParams.bugId,  			//项目
                };
                bugDataManagementService.getBugDetail(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            $scope.paramInfo = data.data;
                            document.getElementById("comment").innerHTML = $scope.paramInfo.remark;
                            document.getElementById("steps").innerHTML = $scope.paramInfo.step;
                            if ($scope.paramInfo.reopenCount > $scope.paramInfo.reopenList.length) {
                                while($scope.paramInfo.reopenCount !== $scope.paramInfo.reopenList.length) {
                                    var count = $scope.paramInfo.reopenList.length + 1

                                    var reopenVO = {
                                        "bugId": $scope.paramInfo.bugId,
                                        "count": count,
                                        "reopenPrincipal": $scope.paramInfo.resolver,
                                        "reopenReason": ""
                                    };
                                    $scope.paramInfo.reopenList.push(reopenVO);
                                }

                            }
                            //reopen责任人为空时补充
                            angular.forEach($scope.paramInfo.reopenList, function (item) {
                                if (item.reopenPrincipal === "") {
                                    item.reopenPrincipal = $scope.paramInfo.resolver;
                                }
                            })
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            //更新数据
            $scope.updateInfo = function() {

                var flag = true;

                angular.forEach($scope.paramInfo.reopenList, function (item) {
                    if (item.reopenPrincipal == null || item.reopenPrincipal ==="") {
                        inform.common("reopen责任人为必填项,请填写后提交.");
                        flag = false;
                        return;
                    }
                    if (item.reopenReason == null || item.reopenReason ==="") {
                        inform.common("reopen原因为必填项,请填写后提交.");
                        flag = false;
                        return;
                    }
                })

                if (!flag) {
                    return;
                }

                bugDataManagementService.updateBugDetail($scope.paramInfo).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            inform.common(data.message);
                            return;
                        }
                    },
                    function (error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            //返回
            $scope.goBack = function () {
                $state.go('app.office.bugDataManagement');
            }
            //修改页面新增一行
            $scope.addBind = function () {

                var count = $scope.paramInfo.reopenList.length + 1

                var reopenVO = {
                    "bugId": $scope.paramInfo.bugId,
                    "count": count,
                    "reopenPrincipal": $scope.paramInfo.resolver,
                    "reopenReason": ""
                };
                $scope.paramInfo.reopenList.push(reopenVO);
            };
            /**
             * 修改页面取消一行,若有关联问题,则弹出提示框,否则直接删除
             * @param index
             */
            $scope.deleteBind = function (index) {

                $scope.paramInfo.reopenList.pop(index);


            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

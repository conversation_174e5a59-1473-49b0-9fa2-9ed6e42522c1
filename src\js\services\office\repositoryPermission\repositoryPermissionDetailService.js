(function() {
    'use strict';
  app.factory('repositoryPermissionDetailService', repositoryPermissionDetailService);
    repositoryPermissionDetailService.$inject=["HttpService",'$rootScope'];

  function repositoryPermissionDetailService(HttpService,$rootScope){
    var service={
      getAuthorizationDetail:getAuthorizationDetail,
    };
    function getAuthorizationDetail(urlData) {
      return HttpService.get($rootScope.getWaySystemApi+'storageManage/getAuthorizationDetail',urlData);
    }
    return service;
  }
})();

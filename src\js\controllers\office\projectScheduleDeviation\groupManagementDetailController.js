//# sourceURL=js/controllers/office/projectScheduleDeviation/groupManagementDetailController.js
(function () {
    app.controller("groupManagementDetail", ['comService', '$rootScope', 'LocalCache','$scope', 'projectManagementService', '$stateParams', 'inform', 'Trans', 'AgreeConstant', '$state',
        function (comService, $rootScope, LocalCache,$scope, projectManagementService, $stateParams, inform, Trans, AgreeConstant, $state) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //获取上一个页面的传参
            $scope.param = JSON.parse(LocalCache.getObject('project_detail').projectInfoParam);
            $scope.specDetail = {
            	'reportProhibitions': []
            };
            $scope.flag = 0;
            //设置列表的高度
            setDivHeight();

            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //初始化页面信息
            initPages();

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 50);
                var clientWidth = document.body.clientWidth;
                $("#buttonStyle").css(inform.getButtonStyle(clientHeight,clientWidth));
                $("#buttonStyle").css({"width": 100+"px"});
            }

            /**
             * 页面初始化
             */
            function initPages() {
                    //获取报表信息
                    $scope.reportLine = [];
                    comService.queryEffectiveParam('REPORT', 'REPORT').then(function (data) {
                        if (data.data) {
                            $scope.reportLine = data.data;
                            $scope.flag++;
                            $scope.setParams();
                        }
                    });
            }

            $scope.setParams = function () {
                if ($scope.flag===1) {
                     if (null ==$scope.param.reportProhibitions) {
                          return;
                     }
                     var participants = $scope.param.reportProhibitions.slice(0, $scope.param.reportProhibitions.length);
                     var reportProhibitionsCode = participants.split(',');
                    angular.forEach($scope.reportLine, function (reportLine, i) {//遍历报表禁用
                            popModalDetail(reportProhibitionsCode,reportLine);
                     });

                }
            };
        //报表禁用控制赋值
            function popModalDetail(reportProhibitionsCode,reportLine) {
                angular.forEach(reportProhibitionsCode, function (code, i) {//遍历软件产品分类
                      if (code===reportLine.paramCode) {
                            $scope.specDetail.reportProhibitions = $scope.specDetail.reportProhibitions+reportLine.paramValue+";";
                            return;
                      }
                });
            }

            /**
             * 返回项目信息
             */
            $scope.goback = function () {
                $state.go('app.office.groupManagement');
            };

            /**
             * *************************************************************
             *              方法声明部分                                结束
             * *************************************************************
             */
        }]);
})();
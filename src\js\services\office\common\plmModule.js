(function() {
    'use strict';
  app.factory('plmModule', plmModule);
  plmModule.$inject=["comService","customerDataService","AgreeConstant","inform","Trans"];

  function plmModule(comService,customerDataService,AgreeConstant,inform,Trans){
    var service={
		 initModule:initModule
    };
    return service;

    //回调方法
    var callBackFun = null;
    //scope
    var moduleScope = null;
    //初始化传入的参数
    var initParams = [];
    /**
     * 初始化plm弹框
     *  参数结构
     * dataInfo= {
                    productLine:'产品线id',
                    upgradeId:'升级请求编号'
                    }
     */
    function initModule(dataInfo,scope,callback) {
        scope.getData = getData;
        //获取plm
        scope.selectPlm = selectPlm;
        scope.resetData = resetData;
        initParams = dataInfo;
    	//查产品线
		scope.productLines = [];
        //回调方法
        callBackFun = callback;
        moduleScope = scope;
        moduleScope.showFlag = "0";
        if(moduleScope.customerStoryNames != null && moduleScope.customerStoryNames != ""){
            moduleScope.showFlag = "1";
        }

        // 初始化分页数据
        moduleScope.pages = inform.initPages();
        moduleScope.pages.size='50';
        moduleScope.inputInfo = {};
        moduleScope.inputInfo.productLine = dataInfo.productLine;
        moduleScope.inputInfo.upgradeId = dataInfo.upgradeId;
        //初始化产品线
        comService.queryEffectiveParam('PRODUCT_TYPE','PRODUCT_TYPE').then(function (data) {
			if (data.data) {
				scope.productLines=data.data;
                //初始化产品类型
			    getData("1");
			    //显示弹框
			    $("#plm").click();
			}
		});
    }

    function resetData(){
        moduleScope.inputInfo = {};
    }

    /**
     * 获取plm列表
     */
     function getData(pageNum){
        var dataInfo={
            'productLine':moduleScope.inputInfo.productLine,
            'upgradeId':moduleScope.inputInfo.upgradeId,
            'creator':moduleScope.inputInfo.creator,
            'analyor':moduleScope.inputInfo.analyor,
            'projectManager':moduleScope.inputInfo.projectManager,
            'documentTitle':moduleScope.inputInfo.documentTitle,
            'finishState': '',
            'fromFlag':'1',
            'currentPage': pageNum, 								// 分页页数
            'pageSize': moduleScope.pages.size
        }
        //获取plm列表
        customerDataService.getAllMessage(dataInfo).then(function (data) {
			if (data.code===AgreeConstant.code) {
                //plm
                moduleScope.plmData = data.data.list;
                if (moduleScope.plmData.length===0) {
					moduleScope.pages = inform.initPages(); 			//初始化分页数据
					inform.common(Trans("tip.noData"));
                } else {
                // 分页信息设置
                	moduleScope.pages.total = data.data.total;           // 页面数据总数
                	moduleScope.pages.star = data.data.startRow;         // 页面起始数
                	moduleScope.pages.end = data.data.endRow;            // 页面结束数
                	moduleScope.pages.pageNum = data.data.pageNum;       //页号

                }
			} else {
				inform.common(data.message);
			}
		},
		function(error) {
			inform.common(Trans("tip.requestError"));
		});
     }

     /**
      * 选择plm
      */
      function selectPlm(m){
        $("#plmModule").modal("hide");
        callBackFun(m);
      }
  }
})();
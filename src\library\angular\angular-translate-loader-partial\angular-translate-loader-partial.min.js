/*!
 * angular-translate - v2.11.1 - 2016-07-17
 * 
 * Copyright (c) 2016 The angular-translate team, <PERSON>; Licensed MIT
 */
!function(a,b){"function"==typeof define&&define.amd?define([],function(){return b()}):"object"==typeof exports?module.exports=b():b()}(this,function(){function a(){"use strict";function a(a,b){this.name=a,this.isActive=!0,this.tables={},this.priority=b||0}function b(a){return Object.prototype.hasOwnProperty.call(g,a)}function c(a){return angular.isString(a)&&""!==a}function d(a){if(!c(a))throw new TypeError("Invalid type of a first argument, a non-empty string expected.");return b(a)&&g[a].isActive}function e(a,b){for(var c in b)b[c]&&b[c].constructor&&b[c].constructor===Object?(a[c]=a[c]||{},e(a[c],b[c])):a[c]=b[c];return a}function f(){var a=[];for(var b in g)g[b].isActive&&a.push(g[b]);return a.sort(function(a,b){return a.priority-b.priority}),a}a.prototype.parseUrl=function(a,b){return angular.isFunction(a)?a(this.name,b):a.replace(/\{part\}/g,this.name).replace(/\{lang\}/g,b)},a.prototype.getTable=function(a,b,c,d,e,f){if(this.tables[a])return b.when(this.tables[a]);var g=this;return c(angular.extend({method:"GET",url:this.parseUrl(e,a)},d)).then(function(b){return g.tables[a]=b.data,b.data},function(){return f?f(g.name,a).then(function(b){return g.tables[a]=b,b},function(){return b.reject(g.name)}):b.reject(g.name)})};var g={};this.addPart=function(d,e){if(!c(d))throw new TypeError("Couldn't add part, part name has to be a string!");return b(d)||(g[d]=new a(d,e)),g[d].isActive=!0,this},this.setPart=function(d,e,f){if(!c(d))throw new TypeError("Couldn't set part.`lang` parameter has to be a string!");if(!c(e))throw new TypeError("Couldn't set part.`part` parameter has to be a string!");if("object"!=typeof f||null===f)throw new TypeError("Couldn't set part. `table` parameter has to be an object!");return b(e)||(g[e]=new a(e),g[e].isActive=!1),g[e].tables[d]=f,this},this.deletePart=function(a){if(!c(a))throw new TypeError("Couldn't delete part, first arg has to be string.");return b(a)&&(g[a].isActive=!1),this},this.isPartAvailable=d,this.$get=["$rootScope","$injector","$q","$http",function(h,i,j,k){var l=function(a){if(!c(a.key))throw new TypeError("Unable to load data, a key is not a non-empty string.");if(!c(a.urlTemplate)&&!angular.isFunction(a.urlTemplate))throw new TypeError("Unable to load data, a urlTemplate is not a non-empty string or not a function.");var b=a.loadFailureHandler;if(void 0!==b){if(!angular.isString(b))throw new Error("Unable to load data, a loadFailureHandler is not a string.");b=i.get(b)}var d=[],g=f();return angular.forEach(g,function(c){d.push(c.getTable(a.key,j,k,a.$http,a.urlTemplate,b)),c.urlTemplate=a.urlTemplate}),j.all(d).then(function(){var b={};return g=f(),angular.forEach(g,function(c){e(b,c.tables[a.key])}),b},function(){return j.reject(a.key)})};return l.addPart=function(d,e){if(!c(d))throw new TypeError("Couldn't add part, first arg has to be a string");return b(d)?g[d].isActive||(g[d].isActive=!0,h.$emit("$translatePartialLoaderStructureChanged",d)):(g[d]=new a(d,e),h.$emit("$translatePartialLoaderStructureChanged",d)),l},l.deletePart=function(a,d){if(!c(a))throw new TypeError("Couldn't delete part, first arg has to be string");if(void 0===d)d=!1;else if("boolean"!=typeof d)throw new TypeError("Invalid type of a second argument, a boolean expected.");if(b(a)){var e=g[a].isActive;if(d){var f=i.get("$translate"),j=f.loaderCache();"string"==typeof j&&(j=i.get(j)),"object"==typeof j&&angular.forEach(g[a].tables,function(b,c){j.remove(g[a].parseUrl(g[a].urlTemplate,c))}),delete g[a]}else g[a].isActive=!1;e&&h.$emit("$translatePartialLoaderStructureChanged",a)}return l},l.isPartLoaded=function(a,b){return angular.isDefined(g[a])&&angular.isDefined(g[a].tables[b])},l.getRegisteredParts=function(){var a=[];return angular.forEach(g,function(b){b.isActive&&a.push(b.name)}),a},l.isPartAvailable=d,l}]}return angular.module("pascalprecht.translate").provider("$translatePartialLoader",a),a.displayName="$translatePartialLoader","pascalprecht.translate"});
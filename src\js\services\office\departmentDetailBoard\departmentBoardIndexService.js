(function() {
    'use strict';
    app.factory('departmentBoardIndexService', departmentBoardIndexService);
    departmentBoardIndexService.$inject=["HttpService",'$rootScope'];

    function departmentBoardIndexService(HttpService,$rootScope){
        function getTotalSumInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'TeamAndProject/getTotalSumInfo',urlData);
        }

        function getRateInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'TeamAndProject/getRateInfo',urlData);
        }

        function getProjectInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'TeamAndProject/getProjectInfo',urlData);
        }

        function getTeamSprintInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'TeamAndProject/getTeamSprintInfo',urlData);
        }

        function getDeptPLMCount(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptPLM/getDeptPLMCount',urlData);
        }

        function getDeptPLMInfo(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'deptPLM/getDeptPLMInfo',urlData);
        }

        return {
            getTotalSumInfo: getTotalSumInfo,
            getRateInfo: getRateInfo,
            getProjectInfo: getProjectInfo,
            getTeamSprintInfo: getTeamSprintInfo,
            getDeptPLMCount: getDeptPLMCount,
            getDeptPLMInfo: getDeptPLMInfo,
        };
    }
})();
(function () {
    app.controller("trackingSheetManagementController", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','trackingSheetService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope, comService,$scope,$state,$stateParams, $modal,trackingSheetService,inform,Trans,AgreeConstant,LocalCache,$http) {

		/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */
        $scope.formRefer = {};
		$scope.limitList = AgreeConstant.limitList;
        initInfo();
        $scope.getData = getData;
        $scope.selectOne = selectOne;
        //被选择关联上的跟踪单
        $scope.relateApprovalNum;
        //分页数据
        $scope.pages = {
            goNum:null, // 初始化跳转页码
            star:0, //开始条数
            end:0, //结束条数
            total:0, // 总条数
            size:"50", //每页条数
            pageNum:AgreeConstant.pageNum //默认页
        };
        $scope.sheetData;
        //关联列表
        $scope.sheetDataList = [];
        $scope.type = 1;
        //修改、详情查看
        getData();
        $scope.getRelatedList = getRelatedList;
        $scope.getUnRelatedList = getUnRelatedList;
        $scope.flag = $stateParams.flag;
        if ($stateParams.flag==='see'){
        //如果是查看详情，则页面所有控件处于只读状态
            var boxes = document.getElementsByName("trackingRead");
            for(var i=0;i<boxes.length;i++){
	   			boxes[i].disabled = 'disabled';
        	}
        }
        //获取部门
        $scope.departmentList = ['应用研究室','驱动研究室','平台研究室','软测研究室'];
        //紧急程度
        $scope.problemUrgencySelect = ['极高','高','中','低'];
        //应急事件等级
        $scope.emergencyLevelSelect = ['严重的','一般的','轻微的'];
        //临时解决
        $scope.isTemporarySolutionSelect = ['是','否','不需要'];
        //问题来源
        $scope.problemSourceSelect = ['客户','市场','测试组','项目组','产品组','售前','售后','运维','荣鑫保障','下游项目组','其他'];
        //严重程度
        $scope.problemSeveritySelect = ['事件','轻微线上问题','一般线上问题','严重线上问题'];
        //是否应急处置
        $scope.isEmergencyResponseSelect = ['是','否'];
        //问题根因
        $scope.problemRootSelect = ['需求','设计','研发','部署','运维','其他'];
        //共性问题
        $scope.commonProblemsClassificationSelect = ['非共性问题','数据问题','异常场景','功能问题','查询问题','环境配置','外部引入','日志问题','流量问题','权限问题','需求问题','其它'];
        //漏检原因
        $scope.leakReasonSelect = ['非漏检-未提测','非漏检-未在提测范围','非漏检-需求如此','非漏检-概率性问题','非漏检-缺少环境','漏检-测试设计未涵盖','漏检-用例错误','漏检-回归测试不足','漏检-漏执行用例','非漏检-其他'];
        var flag=0;

		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */

		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */

		function selectOne(item) {

            //变为选中状态
            if (!item.checked) {
                item.checked = true;
                $scope.item.relatedApprovalNum = item.approvalNum;
                $scope.item.relatedBatchNum = item.batchNum;
                $scope.item.relatedActivatedCount = item.activatedCount;
                $scope.item.relatedStartTime = item.startTime;
            } else {
                item.checked = false;
                //取消选中状态
                $scope.item.relatedApprovalNum = null;
                $scope.item.relatedBatchNum = null;
                $scope.item.relatedActivatedCount = null;
                $scope.item.relatedStartTime = null;
            }
        }
		/**
		 * 初始化
		 */
    	function initInfo() {

            //跳转时发起人带入
            $scope.formRefer.sponsorName = $stateParams.sponsorName;
            $scope.formRefer.productLine = $stateParams.productLine;
            $scope.formRefer.department = $stateParams.department;

            //获取员工信息
            $scope.employeeList = [];
            comService.getEmployeesByOrgId('').then(function(data) {
                if (data.data) {
                    $scope.employeeList = data.data;
                    flag++;
                    getData();
                }
            });
            //获取所有项目名称
            $scope.projectList = [];
            $scope.projectMap = {};
            comService.getProjectsName().then(function (data) {
                $scope.projectList = angular.fromJson(data.data);
                for(var j = 0; j < data.data.length; j++) {
                    $scope.projectMap[data.data[j].id] = data.data[j].cname;
                }
                flag++;
                getData();
            });
            //获取部门
            $scope.departmentList = ['应用研究室','驱动研究室','平台研究室','软测研究室'];
            //获取产品线
            $scope.projectLine = [];
            comService.queryEffectiveParam('PRODUCT_TYPE','PRODUCT_TYPE').then(function (data) {
                if (data.data) {
                    $scope.projectLine = data.data;
                }
            });
    	}
    	//
        function getUnRelatedList() {
            //获取待关联跟踪单列表
            $scope.formRefer.currentPage = $scope.pages.pageNum;
            $scope.formRefer.pageSize = $scope.pages.size;
            $scope.formRefer.batchNum = $stateParams.batchNum;
            $scope.formRefer.approvalNum = $stateParams.approvalNum;
            var condition = $scope.formRefer;

            trackingSheetService.getUnRelatedList(condition).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    //项目详情
                    $scope.sheetDataList = data.data.list;

                    if ($stateParams.batchNum !== null && $stateParams.batchNum !== '') {

                        angular.forEach($scope.sheetDataList, function(item) {
                            if (item.batchNum !== null && item.batchNum !== '') {
                                item.notShow = true;
                            }
                        });
                    }

                    //分页信息设置
                    $scope.pages.total = data.data.total;
                    $scope.pages.star = data.data.startRow;
                    $scope.pages.end = data.data.endRow;
                    $scope.pages.pageNum = data.data.pageNum;
                } else {
                    inform.common(data.message);
                }
            }, function () {
                inform.common(Trans("tip.requestError"));
            });
        }

        function getRelatedList() {
            //获取关联的测试单数据
            var param = {
                'batchNum': $stateParams.batchNum
            };
            trackingSheetService.getRelatedData(param).then(function (data) {
                if (data.code === AgreeConstant.code) {
                    //项目详情
                    $scope.sheetData = data.data.list;
                } else {
                    inform.common(data.message);
                }
            }, function () {
                inform.common(Trans("tip.requestError"));
            });
        }

            /**
         * 获取所有、某个反馈问题
         */
        function getData() {
             if (flag!==2){
                return;
             }
        	 var urlData = {
                'approvalId':$stateParams.item
        	 };
        	 trackingSheetService.getData(urlData).then(function (data) {
             	if (data.code === AgreeConstant.code) {
                    //项目详情
                    $scope.item = data.data.list[0];
                    $scope.item.reopenReason = '';
                    $scope.item.officeProjectId = ($scope.item.officeProjectId==null||$scope.item.officeProjectId==="")?$scope.item.officeProjectId:$scope.item.officeProjectId*1;
                    if ( $scope.flag==='see'){
                        $scope.item.officeProjectId = $scope.projectMap[$scope.item.officeProjectId];
                    }
                } else {
                    inform.common(data.message);
                }
            }, function () {
                inform.common(Trans("tip.requestError"));
            });
        	 //$stateParams.activatedCount===0表示 从来没有关联其他跟踪单 关联列表为空
            if ($stateParams.activatedCount !=="0") {
                getRelatedList();
            }

            getUnRelatedList();
            }

        /**
         * 提交详情信息,更新
         */
        $scope.manageDetail = function (){
            if(!($scope.item.softwareProgram && $scope.item.problemHappenDate && $scope.item.problemDescription && $scope.item.problemSource
             && $scope.item.projectName && $scope.item.problemDiagnosis && $scope.item.problemRootPerson
             && $scope.item.problemRoot && $scope.item.problemRoot && $scope.item.commonProblemsClassification
             && $scope.item.commonProblemsAnalysis && $scope.item.leakPerson && $scope.item.leakReason && $scope.item.leakAnalysis)){
                inform.common("请检查各必输项是否已填写");
                return;
            }
            //是否有新需要关联的跟踪单
            if($scope.item.relatedApprovalNum && !$scope.item.reopenReason){
                inform.common("请填入reopen原因！");
                return;
            }
            $scope.item.startTime = $stateParams.startTime;
            trackingSheetService.manageDetail($scope.item).then(function (result) {
                if (result.code === AgreeConstant.code) {
                        inform.common('维护跟踪单信息成功！');
                        window.history.go(-1);
                    } else {
                        inform.common(result.message);
                    }
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
        }

            $scope.updateReopen = function (m){
                var urlData = {
                    'approvalNum' : m.approvalNum,
                    'reopenReason' : m.reopenReason
                }
                trackingSheetService.updateReopenReason(urlData).then(function(data){
                        inform.common(data.message);
                }, function () {
                    inform.common(Trans("tip.requestError"));
                });
            }
	 		/**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */

	}]);
})();
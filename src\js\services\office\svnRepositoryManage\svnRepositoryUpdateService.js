
(function() {
    'use strict';
  app.factory('svnRepositoryUpdateService', svnRepositoryUpdateService);
  svnRepositoryUpdateService.$inject=["HttpService",'$rootScope'];

  function svnRepositoryUpdateService(HttpService,$rootScope){
    var service={
	  addRepository:addRepository,
	  getRepositoryEmployee:getRepositoryEmployee,
	  saveRepository:saveRepository
    };
    return service;

    /**
 	 * 新增一条仓库数据
 	 */
 	function addRepository(urlData) {
 		// return HttpService.post($rootScope.getWaySystemApi+'svnRepository/insertRepository',urlData);
 		return HttpService.post($rootScope.getWaySystemApi+'storageManage/addStorage',urlData);
 	}

    /**
	 * 查询所修改的仓库下的人员信息
	 */
	function getRepositoryEmployee(repositoryid) {
		// return HttpService.post($rootScope.getWaySystemApi+'svnRepository/getRepositoryEmployee',repositoryid);
		return HttpService.post($rootScope.getWaySystemApi+'storageManage/getStorageStaff',repositoryid);
	}

    /**
     * 保存修改后的仓库信息
     */
    function saveRepository(urlData) {
    	// return HttpService.post($rootScope.getWaySystemApi+'svnRepository/saveRepository',urlData);
    	return HttpService.post($rootScope.getWaySystemApi+'storageManage/updateStorage',urlData);
    }


  }
})();


(function() {
	app.controller("reworkManagement", ['comService', '$rootScope', '$scope', 'reworkService', 'inform', 'Trans', 'AgreeConstant', '$modal', '$state', 'LocalCache', '$http',
		function(comService, $rootScope, $scope, reworkService, inform, Trans, AgreeConstant, $modal, $state, LocalCache, $http) {
		//设置列表的宽度
		setDivWidth();
		//窗体大小变化时重新计算宽度
		$(window).resize(setDivWidth);
	    //设置列表的宽度
 		function setDivWidth(){
 			//网页可见区域宽度
 			var clientWidth = document.body.clientWidth - 200;
 			$("#projectsee").width(clientWidth);
 		}
		$scope.datepicker = {};
        $scope.toggleMin = toggleMin;
        toggleMin();
 		//查询条件
		$scope.formRefer = {
	    	projectname:'',//项目名称
	    	startTime:'',//开始时间
	    	endTime:''//结束时间	
	    };
 		//保存查询出的产品线信息
    	$scope.lineList = [];
    	//汇总信息
    	$scope.lineListFin = [];
    	//查询返回的项目列表
		$scope.projectList = [];
    	//重置
		$scope.rest = function() {
			$scope.formRefer.startTime = '';
			$scope.formRefer.endTime = '';
			$scope.formRefer.projectname = '';
            $scope.formRefer.productLine = '';
			initTime();
		}
		//获取当前选定时间
        function toggleMin() {
            $scope.datepicker.currentDate = $scope.datepicker.currentDate ? null : new Date();
        }
    	//开始时间
		$scope.openDateStart = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.openedStart = true;
			$scope.openedEnd = false;
		};
		//结束时间
		$scope.openDateEnd = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.openedStart = false;    
			$scope.openedEnd = true;
		};
		$scope.start = "";//格式化后的开始时间
		$scope.end = "";//格式化后的结束时间
		$scope.time = "";//格式化后的当前时间

		$scope.getData = getData;
		//某产品线线下的项目
    	$scope.getLine = getLine;
    	initTime();
		//获取数据
		getData();
		/**
		 * 初始化检索条件开始时间 及 结束时间
		 */
		function initTime(endDate){
			if (endDate==null || endDate==="" ){
				$scope.formRefer.endTime = inform.format(new Date(),'yyyy-MM-dd');//获取当前系统时间
			} 
			var time = $scope.formRefer.endTime.split("-");
			var start = time[0]+"/01"+"/01";
			$scope.formRefer.startTime = inform.format(start,'yyyy-MM-dd');
			//对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
			 //获取产品线
            $scope.productLineList = [];
            comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                if (data.data) {
                    $scope.productLineList = data.data;
                }
            });
		}
		//生成Excel
		$scope.createExcelDetails = function() {
			//判断时间条件
			time();
			if ($scope.start <= $scope.end && $scope.start <= $scope.time){
				var modalInstance = $modal.open({
					templateUrl: 'myModalContent.html',
					controller: 'ModalInstanceCtrl',
					size: "sm",
					resolve: {
						items: function() {
							return "确定要下载吗！";
						}
					}
				});
				//存查询条件
				var urlData = {
						'start':$scope.start,
						'end':$scope.end,
						'projectName':$scope.formRefer.projectname,
                        'productLine':$scope.formRefer.productLine
					};
				if ($scope.formRefer.startTime==null){/*||$scope.formRefer.startTime==='null'*/
					$scope.formRefer.startTime = '';
				}
				if ($scope.formRefer.endTime==null){/*||$scope.formRefer.endTime==='null'*/
					$scope.formRefer.endTime = '';
				}
				modalInstance.result.then(function() {
					inform.downLoadFile('rework/createExcelDetails',urlData,'rework.zip');
		       });
			}else {
     			inform.common(Trans("任务开始时间不得大于任务结束时间,且不得大于当前日期！"));
     		}
	      };
	      $scope.toExcel = function() {
				//判断时间条件
				time();
				if ((!$scope.start <= $scope.end) || (!$scope.start <= $scope.time)){
					inform.common(Trans("任务开始时间不得大于任务结束时间,且不得大于当前日期！"));
					return;
				}
					//存查询条件
					var urlData = {
							'start':$scope.start,
							'end':$scope.end,
							'projectName':$scope.formRefer.projectname,
	                        'productLine':$scope.formRefer.productLine
						};
					if ($scope.formRefer.startTime==null){/*||$scope.formRefer.startTime==='null'*/
						$scope.formRefer.startTime = '';
					}
					if ($scope.formRefer.endTime==null){/*||$scope.formRefer.endTime==='null'*/
						$scope.formRefer.endTime = '';
					}
					inform.modalInstance("确定要下载吗?").result.then(function () {
	                	inform.downLoadFile('rework/toExcel',urlData,'开发与线上返工汇总信息.xlsx');
	            	});
		      };
		//获取项目
		function getData() {
			//查询返回的产品线列表
			$scope.lineList = [];
			//判断时间条件
			time();
			if ($scope.start <= $scope.end && $scope.start <= $scope.time){
				var urlData = {
						'start':$scope.start,
						'end':$scope.end,
						'projectName':$scope.formRefer.projectname,
                        'productLine':$scope.formRefer.productLine
					};
          		reworkService.selectAllLine(urlData).then(function(data) {
          			if (data.code===AgreeConstant.code) {
          				var jsonData = data.data;
          				angular.forEach(jsonData, function(project, i) {//遍历每个项目
          					project.workTotal=project.workTotal+"%";
          					project.onlineTotal=project.onlineTotal+"%";
          					if (i<jsonData.length - 1){
          						$scope.lineList.push(project);
          					}
          				});
          				//只有不选择产品线时，才会展示所有产品线数据
          				if(null == urlData.productLine) {
                            angular.forEach($scope.productLineList, function (one, i) {
                                var list = [];
                                angular.forEach($scope.lineList, function (oneLine, i) {
                                    list.push(oneLine.line);
                                });
                                //如果查询出的产品线中不存在的产品线
                                if (list.indexOf(one.param_value) === -1) {
                                    $scope.lineList.push({
                                        "line": one.param_value,//产品线
                                        "taskmap": {
                                            "fixing": 0,
                                            "repair": 0,
                                            "non_conformity": 0,
                                            "online": 0,
                                        },
                                        "total": 0,
                                        "workTotal": '0%',
                                        "onlineTotal": '0%',
                                    });
                                }
                            });
                        }
          				$scope.lineListFin = jsonData[jsonData.length - 1] ;
          			} else {
          				inform.common(data.message);
          			}
          		},
          		function(error) {
          			inform.common(Trans("tip.requestError"));
          		});
			}else {
     			inform.common(Trans("任务开始时间不得大于任务结束时间,且不得大于当前日期！"));
     		}
		}
		
		//获取产品线下的项目工时和
		function getLine(line) {
			//查询返回的项目列表
			$scope.projectList = [];
			$scope.start = inform.format($scope.formRefer.startTime,'yyyy-MM-dd');
			$scope.end = inform.format($scope.formRefer.endTime,'yyyy-MM-dd');
			var urlData = {
					'start':$scope.start,
					'end':$scope.end,
					'projectName':$scope.formRefer.projectname,
					'line':line,
				} 
			if ($scope.formRefer.startTime==null){/*||$scope.formRefer.startTime==='null'*/
				$scope.formRefer.startTime = '';
			}
			if ($scope.formRefer.endTime==null){/*||$scope.formRefer.endTime==='null'*/
				$scope.formRefer.endTime = '';
			}
			reworkService.selectLine(urlData).then(function(data) {
				if (data.code===AgreeConstant.code) {
					var jsonData = data.data;
					$scope.projectList = jsonData;
					angular.forEach($scope.projectList, function(project, i) {//遍历每个项目
						project.workTotal=project.workTotal+"%";
						project.onlineTotal=project.onlineTotal+"%";
					});
				} else {
					inform.common(data.message);
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});
		}
		
		//校验时间
		function time() {
			$scope.start = "";
			$scope.end = "";
			$scope.time = "";
			if ($scope.formRefer.startTime != null && $scope.formRefer.startTime !== "" ){
				$scope.start = inform.format($scope.formRefer.startTime,'yyyy-MM-dd');
				if ($scope.formRefer.endTime==null || $scope.formRefer.endTime===""){
					$scope.end = inform.format(new Date(),'yyyy-MM-dd');
				}
			} 
			if ($scope.formRefer.endTime != null && $scope.formRefer.endTime !== "" ){
				$scope.end = inform.format($scope.formRefer.endTime,'yyyy-MM-dd');
			} 
			$scope.time = inform.format(new Date(),'yyyy-MM-dd');
		}

		} ]);
})();
(function () {
    app.controller("projectReleaseUp", ['$rootScope','comService', '$scope','$state','$stateParams','$modal','projectReleaseService','reportService','inform','Trans','AgreeConstant','LocalCache','$http',
        function ($rootScope,comService,$scope,$state,$stateParams, $modal,projectReleaseService,reportService,inform,Trans,AgreeConstant,LocalCache,$http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */

            $scope.limitList = AgreeConstant.limitList; // 正则校验配置

            $scope.paramInfo = {};
            //项目id
            $scope.projectId = Number.parseInt($stateParams.projectId);
            //实例号
            $scope.incident = $stateParams.incident;

            //初始化
            initProject();

            //是否下拉框数据源
            $scope.points = [{
                value: '0',
                label: '是'
            }, {
                value: '1',
                label: '否'
            }];

            $scope.setModelCategorys = setModelCategorys;

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 初始化
             */
            function initProject() {
                getOne();
                //获取所有项目名称
                $scope.projectList = [];
                comService.getProjectsName().then(function (data) {
                    $scope.project = angular.fromJson(data.data);
                    angular.forEach($scope.project, function (res) {
                        $scope.projectList.push(res);
                    })
                });
                setModelCategorys(Number.parseInt($scope.projectId));
            }
            /*
            * 项目名称发生改变
            * */

            function setModelCategorys(projectId){
                if(projectId === '' || projectId === null) {
                    $scope.paramInfo.productLineName = '';
                    $scope.paramInfo.projectManager = '';
                    return;
                }
                if(projectId === 0 || projectId === '0') {
                    return;
                }
                reportService.getProjectInfoById(projectId).then(function (data) {
                    if (data.code !== AgreeConstant.code) {
                        inform.common(data.message);
                        return;
                    }
                    data.data = angular.fromJson(data.data);
                    if (data.data.length === 0) {
                        $scope.paramInfo.productLineName = '';
                        $scope.paramInfo.projectManager = '';
                    }else{
                        $scope.paramInfo.productLineName = data.data.productLineName;
                        $scope.paramInfo.projectManager = data.data.projectManager;
                    }

                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }
            /**
             * 回填信息
             */
            function getOne() {
                var urlData = {
                    'incident': $scope.incident,//发布实例号
                };
                projectReleaseService.selectOne(urlData).then(function (data) {
                        $scope.paramInfo = { ...data.data, ...$scope.paramInfo};
                        $scope.paramInfo.projectId = $scope.projectId;
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }
            /**
             * 保存信息
             */
            $scope.saveInfo = function(){
                if (!$scope.paramInfo.projectId || 0 === $scope.paramInfo.projectId){
                    inform.common("请选择所属项目");
                    return;
                }
                var urlData = {
                    'projectId':$scope.paramInfo.projectId,//
                    'incident': $scope.incident,
                    'remark':$scope.paramInfo.remark
                };
                //修改
                upInfo(urlData)
            }
            /**
             * 修改信息
             * @param urlData
             */
            function upInfo(urlData) {
                projectReleaseService.updateInfo(urlData).then(function (data) {
                    callBackFunction(data);
                }, function (error) {
                    inform.common(Trans("tip.requestError"));
                });
            }

            function callBackFunction(data){
                if (data.code === AgreeConstant.code) {
                    layer.confirm(data.message,{
                        title:false,
                        btn:['确定']
                    },function(result){
                        layer.close(result);
                        $state.go("app.office.projectRelease");
                    });
                } else {
                    inform.common(data.message);
                }

            }
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();
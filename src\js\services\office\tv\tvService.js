(function () {
    'use strict';
    app.factory('tvService', tvService);
    tvService.$inject = ["HttpService", '$rootScope'];

    function tvService(HttpService, $rootScope) {
        var service = {
            getTvDisplay: getTvDisplay,
            setOrderNum: setOrderNum,
            resumeJob: resumeJob,
            stopJob: stopJob,
            getTvFrameDetail: getTvFrameDetail,
            updateTvFrameDetail: updateTvFrameDetail,
            addTvFrameDetail: addTvFrameDetail,
            getTvMessage: getTvMessage,
            messageSetOrderNum: messageSetOrderNum,
            messageResumeJob: messageResumeJob,
            messageStopJob: messageStopJob,
            updateTvMessage: updateTvMessage,
            addTvMessage: addTvMessage,
            getTvFrameDetailListByRole: getTvFrameDetailListByRole,
            getSortTvMessageListByLoginName: getSortTvMessageListByLoginName,
            getReviewContributionData: getReviewContributionData,
            getTrainGloriousData: getTrainGloriousData,
            deleteJob:deleteJob
        };
        return service;
        /**
         * 交换顺序
         * @param  参数
         */
        function setOrderNum(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'tvFrame/setOrderNum', urlData);
        }
        /**
         * 禁用信息
         * @param  参数
         */
        function resumeJob(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'tvFrame/resumeJob', urlData);
        }
        /**
         * 启用信息
         * @param  参数
         */
        function stopJob(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'tvFrame/stopJob', urlData);
        }
        /**
         * 删除信息
         * @param  参数
         */
        function deleteJob(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'tvFrame/deleteJob', urlData);
        }
        /**
         * 获取看板详情
         * @param  参数
         */
        function getTvFrameDetail(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'tvFrame/getTvFrameDetail', urlData);
        }
        /**
         * 修改看板详情
         * @param  参数
         */
        function updateTvFrameDetail(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'tvFrame/updateTvFrameDetail', urlData);
        }
        /**
         * 新增看板信息
         * @param  参数
         */
        function addTvFrameDetail(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'tvFrame/addTvFrameDetail', urlData);
        }
        /**
         * 获取所有消息
         * @param  参数
         */
        function getTvMessage() {
            return HttpService.get($rootScope.getWaySystemApi + 'tvMessage/getTvMessage');
        }
        /**
         * 调整顺序
         * @param  参数
         */
        function messageSetOrderNum(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'tvMessage/setOrderNum',urlData);
        }
        /**
         * 启动消息
         * @param  参数
         */
        function messageResumeJob(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'tvMessage/resumeJob',urlData);
        }
        /**
         * 禁用消息
         * @param  参数
         */
        function messageStopJob(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'tvMessage/stopJob',urlData);
        }
        /**
         * 新增消息
         * @param  参数
         */
        function addTvMessage(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'tvMessage/addTvMessage',urlData);
        }
        /**
         * 修改消息
         * @param  参数
         */
        function updateTvMessage(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'tvMessage/updateTvMessage',urlData);
        }
        /**
         * 查询所有看板信息
         */
        function getTvDisplay() {
            return HttpService.get($rootScope.getWaySystemApi + 'tvFrame/getTvFrame');
        }
        /**
         * 通过角色查询所有看板信息
         */
        function getTvFrameDetailListByRole(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'tvFrame/getTvFrameDetailListByRole',urlData);
        }
        /**
         * 查询该用户应当显示的消息
         */
        function getSortTvMessageListByLoginName(loginName) {
            return HttpService.get($rootScope.getWaySystemApi + 'tvMessage/getSortTvMessageListByLoginName',loginName);
        }
        /**
         * 查询2022年第一季度评审贡献光荣榜
         */
        function getReviewContributionData(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'reviewTop/getReviewTop4Screen', urlData);
        }
        /**
         * 查询2022年第一季度培训光荣榜
         */
        function getTrainGloriousData() {
            return HttpService.post($rootScope.getWaySystemApi + 'train/getTrainTop4Screen');
        }
    }
})();

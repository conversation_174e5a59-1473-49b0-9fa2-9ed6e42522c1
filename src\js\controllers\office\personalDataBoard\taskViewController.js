(function () {
    app.controller('taskViewController', [
        '$rootScope',
        '$scope',
        '$stateParams',
        'inform',
        'taskViewService',
        'LocalCache',
        '$state',
        '$timeout',
        function ($rootScope, $scope, $stateParams, inform, taskViewService, LocalCache, $state, $timeout) {
            // 初始化快捷选择按钮
            $scope.timeSelect = ['上月', '上周', '本周', '本月'];
            $scope.formRefer = {};
            // 排序
            $scope.sortKey = '';
            $scope.desc = true;
            $scope.order = order;
            // 如果作为非标签页的形式进入页面，调整页面式样
            if ($state.current.name === 'app.office.taskView') {
                $scope.placeholder = true;
            }
            // 初始化任务列表
            $scope.taskList = [];
            // 初始化页面信息
            init($scope);
            if ($stateParams.startTime) {
                const { startTime, endTime } = $stateParams;
                $scope.formRefer.endTime = endTime;
                $scope.formRefer.startTime = startTime;
            }
            // 初始化监听日志滚动的数据
            // let date;
            // let nextMonth;
            // let endTime;
            // 日历开始时间
            let calendarStartDate = inform.format($scope.formRefer.startTime, 'yyyy-MM-dd');
            // 初始化日历参数
            const calendarParams = {
                lang: 'zh_cn',
                // 不启用拖拽功能
                dragThenDrop: false,
                startDate: calendarStartDate,
                // 不显示带操作栏的头部
                withHeader: false,
            };
            // 初始化日历列表
            $scope.taskViewCalendarList = [{ id: 'Calendar1' }];
            // 起始日历
            let calendar;
            $timeout(function () {
                // dom元素挂载完成之后
                $('#Calendar1').calendar(calendarParams);
                calendar = $('#Calendar1').data('zui.calendar');
                // dom渲染完成之后查询
                $scope.getData();
            }, 100);

            // 任务事件
            let newEvent;
            /**
             * 处理任务列表，算出耗时
             * 按日期对任务进行分组，并为每个日期创建一个事件数组
             *
             * @param {Array} data - 任务列表
             * @return {Array} res - 处理后的任务列表
             */
            function dataHandler(data) {
                // 从任务列表中提取唯一日期
                let date = [...new Set(data.map((item) => item.date))];
                // 为每个日期创建一个空数组
                const res = Array.from(new Array(date.length), () => new Array());
                // 按日期对任务进行分组
                let tmp = date.map((dateVal) => {
                    return data.filter((item) => item.date === dateVal);
                });
                tmp.forEach((array, index) => {
                    // 计算每个日期的总小时数
                    let totalHour = array.reduce((pre, cur) => {
                        return pre + Number(cur.consumed);
                    }, 0);
                    totalHour = +totalHour.toFixed(1);
                    res[index].unshift({
                        title: `● ${array[0].taskType}(${totalHour}h)`,
                        desc: array[0].taskType,
                        allDay: true,
                        start: array[0].date,
                    });
                    array.forEach((item) => {
                        res[index].push({
                            title: `(${item.consumed}h) ${item.work}`,
                            desc: item.work,
                            allDay: true,
                            start: item.date,
                        });
                    });
                });
                return res;
            }
            /**
             * 实例化日历组件
             * @param {object} projectLogList 项目任务列表
             * @param {object} sectorLogList 部门任务列表
             */
            function setCalendar(projectLogList, sectorLogList, logStartDate) {
                $scope.taskViewCalendarList = [];
                let projectLogListEvent = dataHandler(projectLogList);
                let sectorLogListEvent = dataHandler(sectorLogList);
                newEvent = projectLogListEvent.concat(sectorLogListEvent).flat();
                calendarParams.startDate = logStartDate;
                let endTime = inform.format($scope.formRefer.endTime, 'yyyy-MM-dd');
                // 遍历日历列表，给每个日历更新
                const calendarCount = monthDiff(new Date(logStartDate), new Date(endTime)) + 1;
                for (let i = 1; i <= calendarCount; i++) {
                    // 避免使用push
                    $scope.taskViewCalendarList = $scope.taskViewCalendarList.concat({ id: `calendar${i}` });
                }
                $timeout(function () {
                    // dom元素挂载完成之后
                    $scope.taskViewCalendarList.map((item, index) => {
                        $(`#${item.id}`).calendar(calendarParams);
                        let calendar = $(`#${item.id}`).data('zui.calendar');
                        let initTime = new Date(logStartDate);
                        let currentMonth = initTime.getMonth() + index;
                        let currentYear = initTime.getFullYear();
                        if (currentMonth >= 12) {
                            currentYear = currentYear + Math.floor(currentMonth / 12);
                            currentMonth = currentMonth % 12;
                        }
                        // 设置月份时，由于2月没有31号，实际会被设置为3月份，不使用setMonth
                        let nextDate = new Date(currentYear, currentMonth, 1);
                        nextDate = inform.format(nextDate, 'yyyy-MM-01');
                        if (index > 0) {
                            // 首个日历不更新startDate，startDate用于控制有数据之前的日期是否展示
                            calendarParams.startDate = nextDate;
                        }
                        // 刷新日历
                        calendar.resetData({
                            calendars: {
                                ...calendarParams,
                            },
                            events: newEvent,
                        });
                        calendar.display('month', nextDate);
                    });
                }, 100);
            }
            /**
             * 查询数据
             */
            $scope.getData = function () {
                let params = {};
                if ($stateParams.account) {
                    const { account } = $stateParams;
                    params.account = account;
                } else if ($stateParams.empId) {
                    const { empId } = $stateParams;
                    params.employeeId = empId;
                } else {
                    let empId = LocalCache.getSession('employeeId');
                    params.employeeId = empId;
                }
                if ($scope.sortKey != '') {
                    params.sortKey = $scope.sortKey;
                    if ($scope.desc) {
                        params.sortOrder = 'desc';
                    } else {
                        params.sortOrder = 'asc';
                    }
                }
                if ($scope.formRefer.projectName != '') {
                    params.projectName = $scope.formRefer.projectName;
                }
                params = {
                    ...params,
                    startTime: inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    endTime: inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),
                };
                taskViewService
                    .getTask(params)
                    .then((res) => {
                        if (res.code && res.code === '0000') {
                            // 项目工时或部门工时不为空
                            if (
                                res.data.personalLogDataDTO.sectorLogList[0] ||
                                !!res.data.personalLogDataDTO.projectLogList[0]
                            ) {
                                ({ projectLogList, sectorLogList } = res.data.personalLogDataDTO);
                                setCalendar(projectLogList, sectorLogList, res.data.logStartDate);
                            } else {
                                layer.msg('日志数据为空');
                                calendarParams.startDate = params.startTime;
                                calendar.resetData({
                                    calendars: {
                                        ...calendarParams,
                                    },
                                    events: [],
                                });
                                calendar.display();
                            }
                            if (res.data.taskList) {
                                $scope.taskList = res.data.taskList;
                            } else {
                                layer.msg('任务列表数据为空');
                                $scope.taskList = [];
                            }
                            $scope.info = {
                                ...$scope.info,
                                theoryWorkHours: res.data.theoryWorkHours,
                                consumed: res.data.consumed,
                                projectConsumed: res.data.projectConsumed,
                                sectorConsumed: res.data.sectorConsumed,
                                taskNum: res.data.taskNum,
                                logNum: res.data.logNum,
                            };
                        } else {
                            layer.msg(res.message);
                        }
                    })
                    .catch((err) => {
                        layer.msg('系统异常');
                    });
            };

            function order(str) {
                $scope.sortKey = str;
                $scope.desc = !$scope.desc;
                $scope.getData();
            }

            /**
             * 事件选择器初始化
             * @param {*} scope
             * @param {*} gatherHoursInfoUnit
             * @param {*} type
             */
            function init(scope, gatherHoursInfoUnit, type) {
                gatherHoursInfoUnit = gatherHoursInfoUnit || '人年';
                type = type || '1';
                // 参数对象
                scope.formRefer = {};
                // 初始化时间
                scope.initTime = function (m) {
                    initTime(scope, m);
                    // 点击之后立即查询
                    $scope.getData();
                };
                // 如果入参含有时间，就不默认查询本周数据
                if ($stateParams.startTime) {
                    initTime(scope, '');
                } else {
                    initTime(scope, '本周');
                }
                scope.type = type;
                scope.unit = gatherHoursInfoUnit;
            }
            // 时间段选择
            function initTime(scope, flag) {
                // 快捷键选项
                scope.butFlag = flag;
                let date = new Date();
                let y = date.getFullYear(); //当前年份
                let lastMonth = date.getMonth() - 1; //上月
                let lastWeekday = date.getLastWeekday(); //上周最后一天
                if ('上月' === scope.butFlag) {
                    // 获取上月天数
                    let daysInMonth = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
                    scope.formRefer.startTime = inform.format(new Date(y, lastMonth, 1), 'yyyy-MM-01');
                    scope.formRefer.endTime = inform.format(new Date(y, lastMonth, daysInMonth), 'yyyy-MM-dd');
                }
                if ('上周' === scope.butFlag) {
                    scope.formRefer.startTime = inform.format(
                        new Date(y, lastWeekday.getMonth(), lastWeekday.getDate() - 7),
                        'yyyy-MM-dd'
                    );
                    scope.formRefer.endTime = inform.format(
                        new Date(y, lastWeekday.getMonth(), lastWeekday.getDate() - 1),
                        'yyyy-MM-dd'
                    );
                }
                if ('本周' === scope.butFlag) {
                    scope.formRefer.startTime = inform.format(
                        new Date(y, lastWeekday.getMonth(), lastWeekday.getDate()),
                        'yyyy-MM-dd'
                    );
                    scope.formRefer.endTime = inform.format(date, 'yyyy-MM-dd');
                }
                if ('本月' === scope.butFlag) {
                    scope.formRefer.startTime = inform.format(date, 'yyyy-MM-01');
                    scope.formRefer.endTime = inform.format(date, 'yyyy-MM-dd');
                }
            }
            /**
             * 查询区域选择开始时间
             */
            $scope.openDateStart = function ($event) {
                $scope.butFlag = '';
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = true; //开始时间
                $scope.formRefer.openedEnd = false;
            };
            /**
             * 查询区域选择结束时间
             */
            $scope.openDateEnd = function ($event) {
                $scope.butFlag = '';
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = false;
                $scope.formRefer.openedEnd = true; //结束时间
            };
            /*
             * 重置查询数据
             */
            $scope.reset = () => {
                $scope.formRefer = {};
                $scope.butFlag = '';
            };
            // 默认显示返回按钮
            $scope.showReturnButton = true;
            // 配置任务视图名称
            $scope.info = {};
            let currentUser = LocalCache.getSession('employeeName');
            if ($stateParams.name) {
                $scope.info.name = $stateParams.name;
            } else if ($stateParams.empId) {
                let person = LocalCache.getObject('personDataBoardEmployee');
                $scope.info.name = person.name || currentUser;
            } else {
                $scope.info.name = currentUser;
                // 如果从首页看板进入，隐藏返回按钮
                $scope.showReturnButton = false;
            }
            /**
             * 计算两个日期之间有几个月
             * @param {Date} dateFrom 开始时间
             * @param {Date} dateTo 结束时间
             * @returns
             */
            function monthDiff(dateFrom, dateTo) {
                return dateTo.getMonth() - dateFrom.getMonth() + 12 * (dateTo.getFullYear() - dateFrom.getFullYear());
            }
        },
    ]);
})();

(function () {
    app.controller("budgetController", ['budgetService','costInputService','comService','$rootScope', '$scope','$state','$stateParams','$modal','inform','Trans','AgreeConstant','LocalCache','$http',
        function (budgetService,costInputService,comService,$rootScope, $scope,$state,$stateParams, $modal,inform,Trans,AgreeConstant,LocalCache,$http) {
     	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */ 	
    	//获取缓存
		$scope.formInput ={
				department:'',   //部门名称
				productLine:'',    	//产品线
				projectName:'',		//项目名称
				proManagerName:'',	//项目经理
				plmUpgradeId:'',		//plm升级请求
				startDate:'',	//起始时间
				endDate:'',		//结束时间
				projectIdForUpload:''		//所属项目ID，用于导入预算时使用
		};
        $scope.formInput = LocalCache.getObject('budget_formRefer');
        //对原缓存进行覆盖
        LocalCache.setObject("budget_formRefer",{});
    	//一进来，默认选中并显示的预算管理
        if ($stateParams.isProjectBudget==null){
			$scope.type = '1';
		} else {
			$scope.type = $stateParams.isProjectBudget;
		}
        //绑定预算文件控件改变事件
        $("#filesUpload").change(submitForm);
    	
    	$scope.pages = {
				pageNum : '', 		// 分页页数
				size : '', 			// 分页每页大小
				total : '' 			// 数据总数
		};
    	$scope.pages = inform.initPages(); 	// 初始化分页数据
		$scope.getData = getData; 	
		//初始化页面信息
    	initPages();

		getData($scope.pages.pageNum);		//在刷新页面时调用该方法
		
		
		//设置列表的高度
		setDivHeight();
		//窗体大小变化时重新计算高度
		$(window).resize(setDivHeight);
		
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
	  	
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */	
		
	    //设置列表的高度
 		function setDivHeight(){
 			//网页可见区域高度
 			var clientHeight = document.body.clientHeight;
 			var divHeight = clientHeight - (150 + 200);
 			$("#divTBDis").height(divHeight);
 			$("#subDivTBDis").height(divHeight - 80);
 			$("#subDivTBDis0").height(divHeight - 80);
 		}
 		/**
         * 初始化检索条件开始时间
         */
        function initTime() {
            var date = new Date();
            //项目信息,开始日期向前推三个月（90天）
            date.setMonth(date.getMonth() - 3);
            //对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
            $scope.formInput.startDate = inform.format(date, 'yyyy-MM-dd');
        }
		//重置
		$scope.reset = function() {
			$scope.formInput.department='';
			$scope.formInput.productLine='';
			$scope.formInput.projectName='';
			$scope.formInput.proManagerName='';
			$scope.formInput.plmUpgradeId='';
			$scope.formInput.startDate = '';
			$scope.formInput.endDate = '';
			$scope.formInput.projectIdForUpload='';
			initTime();
		}
		
	
    	function initPages() {
    		//获取山东新北洋集团的下级部门信息
    		$scope.departmentList = [];
    		comService.getOrgChildren('D010053').then(function(data) {
    			$scope.departmentList = comService.getDepartment(data.data);
             });
    		
            $scope.lineList = [];
            comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
            	if(data.data) {
            		$scope.lineList = data.data;
            	}
            });
            
            //初始化预算状态
            $scope.budgetStatusMap={
            		'0':'草稿',
            		'1':'待结算',
            		'2':'结算申请',
            		'3':'已结算'
            };
            
            if($scope.formInput.startDate===''){
            	initTime();
            }
    	}
     	
		//获取所有数据以分页的形式
		function getData(pageNum){
		   	var start = inform.format($scope.formInput.startDate,'yyyy-MM-dd');
			var end = inform.format($scope.formInput.endDate,'yyyy-MM-dd');
			if (start > end) {
				inform.common(Trans("申请的结束时间必须大于开始时间！"));
				return false;
			}	
        	var urlData ={
					'departmentCode':$scope.formInput.department,  			
					'productLineCode':$scope.formInput.productLine,
					'projectName':$scope.formInput.projectName,
					'projectManager':$scope.formInput.proManagerName,
					'startDate':inform.format($scope.formInput.startDate,'yyyy-MM-dd'),
					'endDate':inform.format($scope.formInput.endDate,'yyyy-MM-dd'),
					'currentPage' : pageNum, 								// 分页页数
					'pageSize' : $scope.pages.size    						// 分页每页大小
        	};
        	if ($scope.type === '2'){
            	urlData.plmUpgradeId= $scope.formInput.plmUpgradeId
        		getPlmBudgetInfo(urlData);
        		return;
        	}
            getProjectBudgetInfo(urlData);
		}
		//获取项目预算
		function getProjectBudgetInfo(urlData){
			budgetService.getProjectBudget(urlData).then(function(data){
				if(data.code===AgreeConstant.code){
					var getata = data.data;
					$scope.projectBudgetList = getata.list;
					if ($scope.projectBudgetList.length===0) {
							$scope.pages = inform.initPages(); 			//初始化分页数据
							inform.common(Trans("tip.noData"));
					} else {
						for(var i = 0;i<$scope.projectBudgetList.length;i++){
							$scope.projectBudgetList[i].personBudget = inform.removeZero($scope.projectBudgetList[i].personBudget);
							$scope.projectBudgetList[i].feeBudget = inform.formatMoney($scope.projectBudgetList[i].feeBudget);
						}
						// 分页信息设置
						$scope.pages.total = getata.total;		// 页面总数
						$scope.pages.star = getata.startRow;  	//页面起始数
						$scope.pages.end = getata.endRow;  		//页面大小数
						$scope.pages.pageNum = getata.pageNum;  	//页面页数
					}
				
				}		
        	},
        	function(error) {
				inform.common(Trans("tip.requestError"));
			});		
       }
		
		//获取plm预算
		function getPlmBudgetInfo(urlData){
			budgetService.getPlmBudget(urlData).then(function(data){
                if (data.code === AgreeConstant.code) {
                	$scope.plmBudgetList = data.data.list;
					if ($scope.plmBudgetList.length===0) {
							$scope.pages = inform.initPages(); 			//初始化分页数据
							inform.common(Trans("tip.noData"));
					} else {
						for(var i = 0;i<$scope.plmBudgetList.length;i++){
							$scope.plmBudgetList[i].personBudget = inform.removeZero($scope.plmBudgetList[i].personBudget);
						}
						// 分页信息设置
						$scope.pages.total = data.data.total;		// 页面总数
						$scope.pages.star = data.data.startRow;  	//页面起始数
						$scope.pages.end = data.data.endRow;  		//页面大小数
						$scope.pages.pageNum = data.data.pageNum;  	//页面页数
					}
                }
            },
            function (error) {
                inform.common(Trans("tip.requestError"));
            });
       }
		/**
		 * 跳转至预算变更页面
		 */
		$scope.toChange = function(m){
			LocalCache.setObject('budget_formRefer', $scope.formInput);
			$state.go('app.office.changePersonBudget', {
        		projectId:m.projectId,
        		projectName:m.projectName,
        		version:m.version
            });
		}
		/**
         * 确认预算
         */
        $scope.confrimBudget = function(isProjectBudget,m) {
        	$scope.gotoType='confrim';
        	getConfrimData(isProjectBudget,m);
        }
        
        function getConfrimData(isProjectBudget,m){
        	LocalCache.setObject('budget_formRefer', $scope.formInput);
        	var urlData ={
    				'projectId':m.projectId,  			
    				'plmUpgradeId':m.plmUpgradeId === null? '0':m.plmUpgradeId,
    				'version':m.version,
    				'changeApplyStatus':"1"
        	};
        	$scope.success = 0;
        	$scope.isProjectBudget = isProjectBudget;
        	$scope.currentM = m;
        	//获取指定项目包含的项目阶段
            $scope.projectStageList = [];
            if (isProjectBudget === '1') {
	            budgetService.getProjectStageList(urlData).then(function(data) {
	            	if(data.data) {
	            		$scope.projectStageList = data.data;
	            	}
	            	gotoConfrimDetailBudget();
	            });
            }else{
            	gotoConfrimDetailBudget();
            }
            //获取岗位和级别
            $scope.titleLevelList = [];
            budgetService.getTitleLevelList(urlData).then(function(data) {
            	if(data.data) {
            		$scope.titleLevelList = data.data;
            	}
            	gotoConfrimDetailBudget();
            });
            
            $scope.personBudgetInfo = [];
			 //查询人力投入
			budgetService.getPersonBudgetInfo(urlData).then(function(data){
				if(data.code===AgreeConstant.code){
					$scope.personBudgetInfo = data.data;
				}
				gotoConfrimDetailBudget();
		       });		
			
			//查询费用投入
		   	$scope.feeBudgetInfo = [];
			if (isProjectBudget === '1') {
				//（项目费用预算）
				budgetService.getFeeBudgetInfo(urlData).then(function(data){
					if(data.code===AgreeConstant.code){
						$scope.feeBudgetInfo = data.data;
					}
					gotoConfrimDetailBudget();
	        	});		
			}else{
				gotoConfrimDetailBudget();
			}	
            //查询差旅费用明细投入
		   	$scope.travelFeeBudgetInfo = [];
			if (isProjectBudget === '1') {
				//（项目差旅费用明细预算）
				budgetService.travelFeeBudgetInfo(urlData).then(function(data){
					if(data.code===AgreeConstant.code){
						$scope.travelFeeBudgetInfo = data.data;
					}
					gotoConfrimDetailBudget();
	        	});
			}else{
				gotoConfrimDetailBudget();
			}
			//获取所有岗位信息
            $scope.costTitleMap = new Map();
            costInputService.getCostWeekTitle().then(function (result) {
                if (result.code === AgreeConstant.code) {
                    var jsonMap = angular.fromJson(result.data);
                    // 把map转为数组
                    for (var key in jsonMap) {
                        $scope.costTitleMap.set(key, jsonMap[key]);
                    }
                    $scope.costTitleMap.set('99','总计(人天)');
                    // 对数组排序
                    $scope.costTitleMap = Array.from($scope.costTitleMap).sort(function (a, b) {
                        return parseInt(a[0]) - parseInt(b[0]);
                    });
                    gotoConfrimDetailBudget();
                }
            });
            
            //获取所有级别信息
            $scope.costLevelMap = new Map();
            budgetService.getCostLevel().then(function (result) {
                if (result.code === AgreeConstant.code) {
                    var jsonMap = angular.fromJson(result.data);
                    // 把map转为数组
                    for (var key in jsonMap) {
                        $scope.costLevelMap.set(key, jsonMap[key]);
                    }
                    $scope.costLevelMap.set('99','总计(人天)');
                    // 对数组排序
                    $scope.costLevelMap = Array.from($scope.costLevelMap).sort(function (a, b) {
                        return parseInt(a[0]) - parseInt(b[0]);
                    });
                    gotoConfrimDetailBudget();
                }
            });
            
			//查询人力预算汇总数据
			$scope.personBudgetAll = [];
			//（项目费用预算）
			budgetService.getPersonBudgetAll(urlData).then(function(data){
				if(data.code===AgreeConstant.code){
					$scope.personBudgetAll = data.data;
				}
				gotoConfrimDetailBudget();
        	});		
			
			var plmId = m.plmUpgradeId === null? '0':m.plmUpgradeId;
			
			//查询人力费用合计明细
			$scope.amountDetail = [];
			budgetService.getAmountDetail(m.projectId,plmId).then(function(data){
				if(data.code===AgreeConstant.code){
					$scope.amountDetail = data.data;
				}
				gotoConfrimDetailBudget();
        	});		
			//查询人力费用合计汇总
			$scope.amountAll = [];
			budgetService.getAmountAll(m.projectId,plmId).then(function(data){
				if(data.code===AgreeConstant.code){
					$scope.amountAll = data.data;
				}
				gotoConfrimDetailBudget();
        	});		
        }
        
        //跳转预算确认/详情界面
        function gotoConfrimDetailBudget(){
        	$scope.success = $scope.success+1;
        	if($scope.success===10){
        		var plmInfoList = [
        		    {
        		    	'code':$scope.currentM.plmUpgradeId === null? '0':$scope.currentM.plmUpgradeId,
        		    	'name':$scope.currentM.note
        		    }               
        		];
				var confirmBudgetData={
					//	'projectStageList':$scope.projectStageList,
						'titleLevelList':$scope.titleLevelList,
						'personBudgetInfo':$scope.personBudgetInfo,
						'feeBudgetInfo':$scope.feeBudgetInfo,
						'travelFeeBudgetInfo':$scope.travelFeeBudgetInfo,
						'firstRowList':$scope.projectStageList.length===0 ? plmInfoList:$scope.projectStageList,
						'costTitleMap':$scope.costTitleMap,
						'costLevelMap':$scope.costLevelMap,
						'personBudgetAll':$scope.personBudgetAll,
						'amountDetail':$scope.amountDetail,
						'amountAll':$scope.amountAll
				}
				
	        	 // 数据存入缓存
	            LocalCache.setObject('confirmBudgetData',confirmBudgetData);
	        	if($scope.gotoType === 'confrim'){
	        		$state.go('app.office.confrimBudgetController', {
		        		isProjectBudget: $scope.isProjectBudget,
		        		projectId:$scope.currentM.projectId,
		        		projectName:$scope.currentM.projectName,
		        		plmUpgradeId:$scope.currentM.plmUpgradeId === null? '0':$scope.currentM.plmUpgradeId,
		        		version:$scope.currentM.version,
		        		isAddBudget:"false",
		        		isDetail:"false"
		            });
	        	}
	        	if($scope.gotoType === 'detail'){
	        		$state.go('app.office.detailBudgetController', {
		        		isProjectBudget: $scope.isProjectBudget,
		        		projectId:$scope.currentM.projectId,
		        		projectName:$scope.currentM.projectName,
		        		plmUpgradeId:$scope.currentM.plmUpgradeId === null? '0':$scope.currentM.plmUpgradeId,
		        		version:$scope.currentM.version,
		        		isDetail:"true"
		            });
	        	}
	        	$scope.success = 0;
			}
        }
        
        /**
         * 获取其中一条记录的详细信息
         */
        $scope.detailBudget = function(isProjectBudget,m) {
        	$scope.gotoType='detail';
        	getConfrimData(isProjectBudget,m);
        }
        
      //获取项目
    	$scope.getProjectId = function () {
    		$scope.projectList = [];
            comService.getProjectsName().then(function (data) {
                $scope.projectList = angular.fromJson(data.data);
            });
    	}
        
        /**
	      * 导入预算
	      */
	     $scope.selectFile = function() {
	    	 for(var item = 0;item<$scope.projectList.length;item++){
	    		 if($scope.projectList[item]['id']===$scope.formInput.projectIdForUpload){
	    			 $scope.projectName=$scope.projectList[item]['cname'];
	    			 break;
	    		 }
	    	 }
             $("#config_modal").modal("hide");
	     	document.getElementById("filesUpload").click();
	     }
 		
	    /**
	     * PLM预算上传文件
	     */
	    function submitForm(e){	    	
	    	
	    	//表单id  初始化表单值
            var formData = new FormData();
            //获取文档中有类型为file的第一个input元素
            var file = document.querySelector('input[type=file]').files[0];
            if (!file) {
                inform.common("请先选择文件!");
                $scope.formInput.projectIdForUpload='';
                return false;
            }
            if (file.size > AgreeConstant.fileSize) {
                inform.common("上传文件大小不能超过2M");
                fileChangeReset();
                $scope.formInput.projectIdForUpload='';
                return false;
            }
            formData.append('file', file);
            var a = file.type;
            if (a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
                inform.common("请选择.xlsx类型的文档进行上传!");
                $scope.formInput.projectIdForUpload='';
                return false;
            } else {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function items() {
                            return "确定要导入吗！";
                        }
                    }
                });
                var uploadUrl;
                if($scope.type ==='1'){
                	uploadUrl = $rootScope.getWaySystemApi + 'budgetAction/uploadProBudgetExcel';
                }else{
                	uploadUrl = $rootScope.getWaySystemApi + 'budgetAction/uploadPlmBudgetExcel';
                }
                formData.append('projectId', $scope.formInput.projectIdForUpload);
                formData.append('projectName', $scope.projectName);
                modalInstance.result.then(function () {
                    //开启遮罩层
                    inform.showLayer("上传中。。。。。。");
                    $.ajax({
                        url: uploadUrl,
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        beforeSend: function beforeSend(request) {
                            request.setRequestHeader("Authorization", 'Bearer ' + LocalCache.getSession("token") || '');
                        },
                        success: function success(result) {
                            if (result.code === AgreeConstant.code) {
                                // 关闭遮罩层
                                inform.closeLayer();
                                if(result.data!==null){
                                	var budgetDto = result.data
                                	$scope.confrimBudget($scope.type,budgetDto);
                                }else {
                                	layer.confirm(result.message,{
                                        title:false,
                                        btn:['确定']
                                    },function(result){
                                        layer.close(result);
                                        getData(1);
                                    });
                                }
                            } else {
                                inform.closeLayer();
                                $modal.open({
                                    templateUrl: 'tpl/common/errorModel.html',
                                    controller: 'ModalInstanceCtrl',
                                    size: "lg",
                                    resolve: {
                                        items: function() {
                                            return result.message;
                                        }
                                    }
                                });
                            }
                            //移除文件名称 通过表单元素的reset方法实现选择文件的重置
                            $("#formUpload")[0].reset();
                        },
                        error: function error(_error) {
                            inform.common(Trans("tip.requestError"));
                        }
                    });
                });
            }
	     }
	    
        
	     /**
	 		 * *************************************************************
	 		 *              方法声明部分                                 结束
	 		 * *************************************************************
	 		 */	
	
	}]);
})();
/*
 * @Author: fubaole
 * @Date:   2018-01-03 11:23:21
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-30 10:00:49
 */

(function() {
  'use strict';
  app.controller("autoBarCtrl", ['$scope','$rootScope', 'inform', '$timeout', 'Trans', '$interval', '$location',
    function($scope,$rootScope, inform, $timeout, Trans, $interval, $location) {

      $scope.getData = getData;
      $scope.initConfig = initConfig;
      var dataAxis = ['点', '击', '柱', '子', '或', '者', '两', '指', '在', '触', '屏', '上', '滑', '动', '能', '够', '自', '动', '缩', '放'];
      var data = [220, 182, 191, 234, 290, 330, 310, 123, 442, 321, 90, 149, 210, 122, 133, 334, 198, 123, 125, 220];
      var yMax = 500;
      var dataShadow = [];
      var option = {};

      for (var i = 0; i < data.length; i++) {
        dataShadow.push(yMax);
      }
      
      initConfig();
      getData();


      function getData () {
        
        option = {
          xAxis: {
            data: dataAxis,
            axisLabel: {
              inside: true,
              textStyle: {
                color: '#fff'
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            z: 10
          },
          yAxis: {
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#999'
              }
            }
          },
          dataZoom: [{
            type: 'inside'
          }],
          series: [{ // For shadow
              type: 'bar',
              itemStyle: {
                normal: { color: 'rgba(0,0,0,0.05)' }
              },
              barGap: '-100%',
              barCategoryGap: '40%',
              data: dataShadow,
              animation: false
            },
            {
              type: 'bar',
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0, 0, 0, 1, [
                      { offset: 0, color: '#83bff6' },
                      { offset: 0.5, color: '#188df0' },
                      { offset: 1, color: '#188df0' }
                    ]
                  )
                },
                emphasis: {
                  color: new echarts.graphic.LinearGradient(
                    0, 0, 0, 1, [
                      { offset: 0, color: '#2378f7' },
                      { offset: 0.7, color: '#2378f7' },
                      { offset: 1, color: '#83bff6' }
                    ]
                  )
                }
              },
              data: data
            }
          ]
        };
        //tip:接口数据成功后，调用此方法，渲染页面
        reloadPageData(option);
        
      }

      //接口获取的数据，执行数据渲染操作
      function reloadPageData(option){
        if($scope.$parent.screenFlag){

          $timeout(function(){
            $scope.fullBar.setOption(option,true);
          },0);
        }else{
          $timeout(function(){
            $rootScope.autoBar.setOption(option,true);
          },0);
        }
      }
      
      $rootScope.autoBar = echarts.init(document.getElementById('autoBar'));

      // Enable data zoom when user click bar.
      var zoomSize = 6;
      $rootScope.autoBar.on('click', function(params) {
        $rootScope.autoBar.dispatchAction({
          type: 'dataZoom',
          startValue: dataAxis[Math.max(params.dataIndex - zoomSize / 2, 0)],
          endValue: dataAxis[Math.min(params.dataIndex + zoomSize / 2, data.length - 1)]
        });
      });

      function initConfig(){
        // 全屏图表
        if($scope.$parent.screenFlag){
          $('.fullscreen #autoBar').width(document.body.clientWidth*0.85);
          $('.fullscreen #autoBar').height(document.body.clientHeight*0.6);
          console.log($('.fullscreen #autoBar').height());
          console.log($('.fullscreen #autoBar').width());
          $timeout(function(){
            $scope.fullBar = echarts.init($('.fullscreen #autoBar')[0]);
            $scope.fullBar.setOption(option,true);
          },0);
        }else{
          $timeout(function(){
            $rootScope.autoBar.setOption(option,true);
          },0);
        }
      }

      // 刷新该模块
      $scope.$on('reload', function(e, id) {
          console.log("父级传来的数据ID"+id+"根据ID重新加载该模块");
          //tip:根据接口给的contentId，判断if(contentId===id),则执行刷新操作,调用getData();
      });

      var timeout_upd = $interval(function(){
        if ($location.path() === '/app/index_bench' || $location.path().indexOf('preview_page')!== -1) {
          getData();
        }
      } ,16000);

      // 清除定时器
      $scope.$on('$destroy',function(){
        $interval.cancel(timeout_upd);
      });
      

    }
  ]);
})();
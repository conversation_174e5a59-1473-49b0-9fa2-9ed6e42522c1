(function () {
    app.controller("versionReleaseReport", ['comService','$rootScope', '$scope','$modal','versionReleaseService','inform','Trans','AgreeConstant','LocalCache','$http',
        function (comService,$rootScope, $scope, $modal,versionReleaseService,inform,Trans,AgreeConstant,LocalCache,$http) {
    	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */ 
		$scope.datepicker = {};
        $scope.toggleMin = toggleMin;
        toggleMin();
        
		$scope.getData = getData; 			// 获取产品线信息
		$scope.getDepartmentData = getDepartmentData;	//获取部门信息			
		$scope.taskList = [];				// 保存所有信息的集合     

		initTime();//下载工时时间
		getData();		// 获取产品线信息
		getDepartmentData();	//获取部门信息	
		
	  	
		/**
		 * *************************************************************
		 *              初始化部分                                 结束
		 * *************************************************************
		 */	
	  	
		/**
		 * *************************************************************
		 *              方法声明部分                                 开始
		 * *************************************************************
		 */	
		/**
		 * 初始化检索条件开始时间 及 结束时间
		 */
        function initTime(endDate){
			if (endDate == null || endDate === "" ){
				$scope.endTime = inform.format(new Date(),'yyyy-MM-dd');//获取当前系统时间
			} 
			var time = $scope.endTime.split("-");
			var start = time[0]+"/01"+"/01";
			$scope.startTime = inform.format(start,'yyyy-MM-dd');
			//对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
        }
		/**
		 * 获取当前选定时间
		 */
	  	function toggleMin() {
            $scope.datepicker.currentDate = $scope.datepicker.currentDate ? null : new Date();
            //获取产品线
            $scope.productLineList = [];
            comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (data) {
                if (data.data) {
                    var arrayLine = [];
                    angular.forEach(data.data, function(item, i) {
                        if (item.param_code !== '0017' && item.param_code !== '0018') {
                            arrayLine.push(item);
                        }
                    });
                    $scope.productLineList = arrayLine;
                }
            });

	  	}
		 //上线开始时间
		$scope.openDateStart = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.openedStart = true;    //开始时间
			$scope.openedEnd = false;
		};
		
		//上线结束时间
		$scope.openDateEnd = function($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.openedStart = false;    
			$scope.openedEnd = true;    //结束时间
		};
		
		/**
		 * 重置
		 */
		$scope.rest = function() {
			initTime();
		};
		/**
		 * 获取所有的产品线信息
		 */
		function getData(){
			$scope.failCount = '0';//发布总合计
			$scope.onlineCount = '0';//特殊情况发布总合计
			$scope.onlineFailureRate = '0%';//特殊放行率合计
			$scope.taskList = [];   //保存所有信息的集合
			var start = inform.format($scope.startTime,'yyyy-MM-dd');
			var end = inform.format($scope.endTime,'yyyy-MM-dd 23:59:59');
			var urlData = {
				'start':start,											//上线开始时间
				'end':end											//上线结束时间
			};
			versionReleaseService.getRateOfProductLine(urlData).then(function(data){
				getDepartmentData();
				if(data.code === AgreeConstant.code){
					var jsonData = data.data;
					$scope.taskList = jsonData.dataList;
					angular.forEach($scope.productLineList, function(one, i) {
                     	var list = [];
						if (jsonData.dataList) {
							angular.forEach($scope.taskList, function(oneLine, i) {
								list.push(oneLine.productLineName);
							});
							if (list.indexOf(one.param_value) === -1){
								$scope.taskList.push({
									"productLineName":one.param_value,
									"count":0,
									"failCount":0,
									"onlineFailureRate":'0%'
								});
							}
						}

               	     });
					$scope.onlineCount = data.data.onlineCount;
					$scope.failCount = data.data.failCount;
					$scope.onlineFailureRate = data.data.onlineFailureRate;
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});		
		}
		/**
		 * 获取所有的部门信息
		 */
		function getDepartmentData(){
			$scope.failCount = '0';//发布总合计
			$scope.onlineCount = '0';//特殊情况发布总合计
			$scope.onlineFailureRate = '0%';//特殊放行率合计
			$scope.taskList = [];   //保存所有信息的集合
			var start = inform.format($scope.startTime,'yyyy-MM-dd');
			var end = inform.format($scope.endTime,'yyyy-MM-dd 23:59:59');
			var urlData = {
					'start':start,											//上线开始时间
					'end':end												//上线结束时间
			};
			versionReleaseService.getRateOfDepartment(urlData).then(function(data){
				if(data.code === AgreeConstant.code){
					var jsonData = data.data;
					$scope.departList = jsonData.dataList;
					$scope.deonlineCount = data.data.onlineCount;
					$scope.defailCount = data.data.failCount;
					$scope.deonlineFailureRate = data.data.onlineFailureRate;
				}
			},
			function(error) {
				inform.common(Trans("tip.requestError"));
			});		
		
		}
		/**
		 * 生成Excel表格
		 */
		$scope.toExcel = function() {
			inform.modalInstance("确定要下载吗？").result.then(function() {
				var start = inform.format($scope.startTime,'yyyy-MM-dd');
				var end = inform.format($scope.endTime,'yyyy-MM-dd 23:59:59');
				var urlData = {
						'start':start,											//上线开始时间
						'end':end												//上线结束时间
				};
                inform.downLoadFile('versionReleaseLog/downloadExcel',urlData,"上线失败率报表.xlsx");
            });
		};
		
	    /**
	     * *************************************************************
	     *              方法声明部分                                 结束
	     * *************************************************************
	     */	
		
	}]);
})();

(function() {
    'use strict';
  app.factory('companyProWorkTimeService', companyProWorkTimeService);
  companyProWorkTimeService.$inject=["HttpService",'$rootScope'];

  function companyProWorkTimeService(HttpService,$rootScope){

    var service={
        getCompanyProWorkTimeList:getCompanyProWorkTimeList,
        getProjectTeamWorkTime:getProjectTeamWorkTime,
        getPersonWorkTimeDetail:getPersonWorkTimeDetail,
        getStoryWorkTimeDetail:getStoryWorkTimeDetail,
        getSupportWorkTimeDetail:getSupportWorkTimeDetail,
        getTotalWorkTime:getTotalWorkTime,
        getAllCompanyProWorkTime:getAllCompanyProWorkTime
    };
    return service;

    /**
     * 分页查询公司立项项目工时统计
     */
    function getCompanyProWorkTimeList(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'companyProWorkTimeManagement/getCompanyProWorkTimeList', urlData);
    }
    /**
     * 获取系研立项项目及团队投入情况
     */
    function getProjectTeamWorkTime(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'companyProWorkTimeManagement/getProjectTeamWorkTime', urlData);
    }
    /**
     * 获取员工工时投入情况
     */
    function getPersonWorkTimeDetail(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'companyProWorkTimeManagement/getPersonWorkTimeDetail', urlData);
    }
    /**
     * 获取用户需求工时投入情况
     */
    function getStoryWorkTimeDetail(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'companyProWorkTimeManagement/getStoryWorkTimeDetail', urlData);
    }
    /**
     * 查询已关联的禅道项目清单
     */
    function getSupportWorkTimeDetail(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'companyProWorkTimeManagement/getSupportWorkTimeDetail', urlData);
    }
    /**
     * 获取汇总数据
     */
    function getTotalWorkTime(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'companyProWorkTimeManagement/getTotalWorkTime', urlData);
    }
    //获取所有公司立项项目的工时
    function getAllCompanyProWorkTime(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'companyProWorkTimeManagement/getAllCompanyProWorkTime', urlData);
    }
  }
})();

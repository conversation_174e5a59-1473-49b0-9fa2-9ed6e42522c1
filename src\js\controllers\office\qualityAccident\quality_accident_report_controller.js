(function() {
    'use strict';
    app.controller("qualityAccidentReportController", ['comService', '$rootScope', '$stateParams', '$scope', 'qualityAccidentManagementService','$modal','inform', 'Trans', 'AgreeConstant','$state', 'LocalCache', '$http',
        function(comService, $rootScope, $stateParams,$scope, qualityAccidentManagementService,$modal,inform, Trans, AgreeConstant,$state, LocalCache, $http) {
    	/**
		 * *************************************************************
		 *             初始化部分                                 开始
		 * *************************************************************
		 */ 
        // 列表数据
        $scope.pageData = [];
        // 查询条件对象
        $scope.searchObject = {
                occurTimeStart:"", //创建时间起
                occurTimeEnd:"" //创建时间止
            };
        //信息显示
        $scope.getPageData = getPageData;

        //初始化参数
        initParam();


         // 设置列表的高度
         setDivHeight();
    	 // 窗体大小变化时重新计算高度
    	 $(window).resize(setDivHeight);
       
         //定义排序对象
         $scope.orderObj = {
             title: '$index',
             desc: true,
             order: function(str) {
                 $scope.orderObj.title = str;
                 $scope.orderObj.desc = !$scope.orderObj.desc;
             }
         };
         //下载工时时间
 		 initTime();


         /**
 		 * *************************************************************
 		 *              初始化部分                                 结束
 		 * *************************************************************
 		 */	
 	  	
 		/**
 		 * *************************************************************
 		 *              方法声明部分                                 开始
 		 * *************************************************************
 		 */

        /**
         * 初始化参数
         */
 		function initParam() {
            //事故类型
            $scope.accidentTypeObject = [];
            //重大事故标题的list
            $scope.accidentTypeList = {};
            comService.queryEffectiveParam('accident_type', 'accident_type').then(function (data) {
                if (data.data) {
                    $scope.accidentTypeObject = data.data;
                    //拼接部门，code为0标识要占据map下标为0的位置
                    $scope.accidentTypeList['0'] = '部门名称';
                    angular.forEach($scope.accidentTypeObject,function (res) {
                        $scope.accidentTypeList[res.paramCode] = res.paramValue + "问题数量";
                    });
                }
            });
            $scope.datepicker = {};
            $scope.datepicker.currentDate = $scope.datepicker.currentDate ? null : new Date();
            //获取部门
            $scope.departmentList = [];
            comService.getOrgChildren('D010053').then(function(data) {
                $scope.departmentList = data.data;

                getPageData();
            });
        }
         /**
 		 * 初始化检索条件开始时间 及 结束时间
 		 */
         function initTime(endDate){
 			if (endDate==null || endDate==="" ){
 				$scope.searchObject.occurTimeEnd = inform.format(new Date(),'yyyy-MM-dd');//获取当前系统时间
 			} 
 			var time = $scope.searchObject.occurTimeEnd.split("-");
 			var start = time[0]+"/01"+"/01";
 			$scope.searchObject.occurTimeStart = inform.format(start,'yyyy-MM-dd');
 			//对最后计算出的日期进行格式化（此时 若计算的时间不合理 会进行合理化）
         }
        /**
         * 查询条件的开始时间
         */
         $scope.openDateStart = function($event) {
             $event.preventDefault();
             $event.stopPropagation();
             //仅查询部分的开始时间控件显示
             $scope.openedStart = true;    
             $scope.openedEnd = false;
         };
         
        /**
         * 查询条件的结束时间
         */
         $scope.openDateEnd = function($event) {
             $event.preventDefault();
             $event.stopPropagation();
             $scope.openedStart = false;
             $scope.openedEnd = true;
         };
         
        /**
         * 重置查询条件
         */
         $scope.clearParams = function() {
        	 initTime();
         };
         
        /**
     	 * 设置列表的高度
     	 */
  		function setDivHeight(){
  			//网页可见区域高度
  			var clientHeight = document.body.clientHeight;
  			var divHeight = clientHeight - (150 + 185);
  			$("#divTBDis").height(divHeight);
  			$("#subDivTBDis").height(divHeight - 65);
  		}
  		
  		/**
  		 * 分页查询
  		 */
        function getPageData() {
            //拼装查询条件
            var params = {
                occurTimeStart:inform.format($scope.searchObject.occurTimeStart,'yyyy-MM-dd HH:mm:ss'),
                occurTimeEnd:inform.format($scope.searchObject.occurTimeEnd,'yyyy-MM-dd HH:mm:ss')
            };
            //获取数据
            qualityAccidentManagementService.getForQualityPages(JSON.stringify(params)).then(function (result) {
                if (result.code === '0000') {
                    $scope.pageData = result.data;
                    var resultList = [];
                    //建立一个新的对象，用来存所有页面需要显示的字段
                    $scope.pageDataTitleList = {};
                    //将map转换为对象的形式
                    for(var deptNameKey in $scope.pageData){
                        var list = {};
                        //拼接部门，code为0标识要占据map下标为0的位置
                        list['0'] =deptNameKey;
                        //拼接该部门的指标
                        var value = $scope.pageData[deptNameKey];
                        for(var valueKey in value){
                            list[valueKey] = value[valueKey];
                        }
                        resultList.push(list);

                        //如果查询出的部门列表中存在的部门
                        for(var i=0; i< $scope.departmentList.length; i++) {
                            if (deptNameKey === $scope.departmentList[i].orgName){
                                $scope.departmentList.splice(i,1);
                            }
                        }
                    }
                    $scope.pageData = resultList;

                    //向显示的内容中追加没有数据的部门的数据
                    for(var j=0; j< $scope.departmentList.length; j++) {
                        $scope.pageData.push({'0':$scope.departmentList[j].orgName});
                    }
                    //添加汇总行
                    var count = {'0':'汇总'};
                    angular.forEach($scope.pageData,function (data) {
                        //遍历对象中的所有字段
                        for (var item in data) {
                            //若不是部门字段，则进行累加
                            if(item !== '0'){
                                if(null == count[item]){
                                    count[item] = 0;
                                }
                                if(null == data[item]){
                                    data[item] = 0;
                                }
                                count[item] = count[item] *1 + data[item] * 1;
                            }
                        }
                    });
                    //添加汇总行
                    $scope.pageData.push(count);

                    //对显示的内容进行部门排序
                    $scope.pageData.sort(function(a,b){
                        // order是规则  objs是需要排序的数组
                        var order = ["汇总","项目管理办公室", "平台开发研究室","产品开发四室", "产品开发三室",
                            "产品开发二室","产品开发一室", "测试研究室",
                            "项目管理办公室"];
                        return order.indexOf(a['0']) - order.indexOf(b['0']);
                    });
                } else {
                    inform.common(result.message);
                }
            }, function (reason) {
                console.log("error");
            });
        }
        
       /**
        * 生成Excel表格
        */
        $scope.toExcel = function() {
            var modalInstance = $modal.open({
                templateUrl: 'myModalContent.html',
                controller: 'ModalInstanceCtrl',
                size: "sm",
                resolve: {
                    items: function() {
                        return "确定要下载吗！";
                    }
                }
            });
            modalInstance.result.then(function() {
                //开启遮罩层
                inform.showLayer("下载中。。。。。。");
                var params = {
                    "occurTimeStart":inform.format($scope.searchObject.occurTimeStart, 'yyyy-MM-dd'),
                    "occurTimeEnd":inform.format($scope.searchObject.occurTimeEnd, 'yyyy-MM-dd')
                };
                $http.post(
                    $rootScope.getWaySystemApi+'qualityAccident/toExcel',params,
                    {
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization':'Bearer ' + LocalCache.getSession("token")||''
                        },
                        responseType: 'arraybuffer'//防止中文乱码
                    }
                ).success(function(data){
                    //如果是IE浏览器
                    if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                        var csvData = new Blob([data], {type:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
                        window.navigator.msSaveOrOpenBlob(csvData,'11 质量事故情况.xlsx');
                    }
                    //google或者火狐浏览器
                    else{
                        var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
                        var objectUrl = URL.createObjectURL(blob);
                        var aForExcel = $("<a download='11 质量事故情况.xlsx'><span class='forExcel'>下载excel</span></a>").attr("href",objectUrl);
                        $("body").append(aForExcel);
                        $(".forExcel").click();
                        aForExcel.remove();
                    }
                    // 关闭遮罩层
                    inform.closeLayer();
                    inform.common("下载成功!");
                });
            });
        };
        /**
  		 * *************************************************************
  		 *              方法声明部分                                 结束
  		 * *************************************************************
  		 */ 
        }
    ]);
})();
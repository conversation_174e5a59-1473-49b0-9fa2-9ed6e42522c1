/*
 * @Author: fubaole
 * @Date:   2017-11-17 14:21:59
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-03-01 10:46:39
 */
(function() {
  'use strict';
  app.controller("blocks_Management", ['$rootScope', '$scope', '$timeout', '$stateParams', '$modal', 'SystemService', 'Trans', 'inform','AgreeConstant',
    function($rootScope, $scope, $timeout, $stateParams, $modal, SystemService, Trans, inform,AgreeConstant) {
      var interfaceMap ={};

      $scope.title = 'paramId';
      $scope.inputData ={
        row:[
          {
            name:'blocks.name',
            model:'',
            holder:'blocks.placeholderName',
            type:'search',
            selectList:''
          },
          {
            name:'blocks.modal',
            model:'',
            holder:'',
            type:'select',
            selectList:[]
          }
        ],
        unfirstRow:[
         {
            name:'blocks.classify',
            model:'',
            holder:'',
            type:'select',
            selectList:[]
          }
        ]
      };
      $scope.map = {}; //条件

      $scope.orderStr = "block_content_id asc";

      $scope.order = order; // 排序函数
      $scope.getData = getData; // 初始化函数
      $scope.searchData = searchData; // 查询函数
      // $scope.advQuery = advQuery; // 高级查询
      // $scope.reset = reset; // 重置
      $scope.open = open; // 删除弹框

      $scope.pages = inform.initPages(); // 初始化分页数据
      getDropDownData("widgetCategory"); // 获取所属模块数据
      getDropDownData("permissionType"); // 获取权限分类数据
      getDropDownData("widgetWidth"); // 获取页面宽度数据
      getData($scope.pages.pageNum);

      // 排序
      function order(str) {
        $scope.title = str;
        $scope.desc = !$scope.desc;
      }
      // 查询
      function searchData(str){
        $scope.map.blockContentTitle = str.row[0].model;
        $scope.map.blockContentType = str.row[1].model;
        $scope.map.permissionType = str.unfirstRow[0].model;
        interfaceMap = angular.copy($scope.map);
        getData(AgreeConstant.pageNum);
      }

      //高级查询功能按钮
      // function advQuery() {
      //   $scope.isOpen = !$scope.isOpen;
      //   $scope.map.permissionType = "";
      // }

      // 获取字典数据
      function getDropDownData(str) {
        SystemService.getDictValueListByDictTypeCode(str)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              if (str==="widgetCategory") {
                $scope.inputData.row[1].selectList = data.result;
              }
              if (str==="permissionType") {
                $scope.inputData.unfirstRow[0].selectList  = data.result;
              }
              if (str==="widgetWidth") {
                $scope.widgetWidth = data.result;
              }
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 获取表格数据
      function getData(num) {
        if (!num){inform.common(Trans('tip.pageNumTip'));return;}

        SystemService.getBlockContentByMap(JSON.stringify(interfaceMap), parseInt(num), $scope.pages.size, $scope.orderStr)
          .then(function(data) {
            if (data.code===AgreeConstant.resultCode) {
              $scope.pages.goNum = null;
              var jsonData = angular.fromJson(data.result);
              $scope.resultData = jsonData.list;
              if ($scope.resultData.length===0) {
                inform.common(Trans("tip.noData"));
                $scope.pages = inform.initPages();
              } else {
                $scope.pages.total = jsonData.total;
                $scope.pages.star = (jsonData.pageNum - 1) * jsonData.pageSize + 1;
                $scope.pages.end = jsonData.pageNum * jsonData.pageSize;
                $scope.pages.pageNum = jsonData.pageNum;
              }
            } else {
              inform.common(data.message);
            }
          }, function(error) {
            inform.common(Trans("tip.requestError"));
          });
      }

      // 删除弹框
      function open(item) {
        var modalInstance = $modal.open({
          templateUrl: 'myModalContent.html',
          controller: 'ModalInstanceCtrl',
          size: "sm",
          resolve: {
            items: function() {
              return Trans('common.deleteTip');
            }
          }
        });
        modalInstance.result.then(function() {
          if (item) {
            SystemService.removeBlockContent(item.blockContentId)
              .then(function(data) {
                if (data.code===AgreeConstant.resultCode) {
                  interfaceMap = {};
                  $scope.map ={};
                  getData(AgreeConstant.pageNum);
                  inform.common(Trans("tip.delSuccess"));
                } else {
                  inform.common(data.message);
                }
              }, function() {
                inform.common(Trans("tip.requestError"));
              });
          }
        });
      }

      // 重置查询条件
      // function reset() {
      //   $scope.map.blockContentTitle = "";
      //   $scope.map.blockContentType = "";
      //   $scope.map.permissionType = "";
      // }
    }
  ]);
})();
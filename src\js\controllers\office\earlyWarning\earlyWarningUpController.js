(function () {
  'use strict';

  app.controller('earlyWarningUpController', [
    '$scope',
    '$state',
    'comService',
    '$rootScope',
    'inform',
    'Trans',
    'AgreeConstant',
    'earlyWarningService',
    '$stateParams',
    'LocalCache',
    '$modal',
    '$http',
    function (
      $scope,
      $state,
      comService,
      $rootScope,
      inform,
      Trans,
      AgreeConstant,
      earlyWarningService,
      $stateParams,
      LocalCache,
      $modal,
      $http
    ) {
      /**
       * *************************************************************
       *             初始化部分                                 开始
       * *************************************************************
       */
      $('div.input-group input').attr('disabled', 'disabled');
      //任务状态下拉框
      $scope.jobDisabledSelect = [
        {
          value: '0',
          label: '启用',
        },
        {
          value: '1',
          label: '禁用',
        },
      ];
      // 节假日策略
      $scope.holidayStrategyList = [
        {
          value: '0',
          label: '忽略',
        },
        {
          value: '1',
          label: '发送',
        },
      ];
      // 设置侧边的高度,随窗口变动
      inform.autoHeight();
      window.onresize = inform.autoHeight;
      initPerson();
      //设置列表的高度
      setDivHeight();
      //窗体大小变化时重新计算高度
      $(window).resize(setDivHeight);
      // 抄送人可拖拽
      var draging = null;
      setTimeout(initULObject, 1000 * 6);
      $scope.setDraggableTime = setDraggableTime;
      /**
       * *************************************************************
       *              初始化部分                                 结束
       * *************************************************************
       */

      /**
       * *************************************************************
       *              方法声明部分                                开始
       * *************************************************************
       */
      /**
       * 收件人、抄送人、密送人名单变化时延迟1秒修改li属性允许拖拽
       */
      function setDraggableTime() {
        setTimeout(setDraggableOfli, 1000 * 1);
      }

      /**
       * 修改li属性允许拖拽
       */
      function setDraggableOfli() {
        var subNodes = document.querySelectorAll('ul.chosen-choices li.search-choice');

        for (var i = 0; i < subNodes.length; i++) {
          $(subNodes[i]).attr('draggable', true);
        }
      }

      /**
       * 初始化ul，允许li元素支持拖拽
       */
      function initULObject() {
        setDraggableOfli();
        var nodes = document.querySelectorAll('ul.chosen-choices');
        //$("div#intro .head")	id="intro" 的 <div> 元素中的所有 class="head" 的元素
        //$("ul li:first")	每个 <ul> 的第一个 <li> 元素
        for (var i = 0; i < nodes.length; i++) {
          var node = nodes[i];
          //使用事件委托，将li的事件委托给ul
          node.ondragstart = dragstartOfli;
          node.ondragover = ondragoverOfli;
        }
      }

      /**
       * 将li的事件委托给ul
       */
      function dragstartOfli(event) {
        //firefox设置了setData后元素才能拖动！！！！
        if (!!window.ActiveXObject || 'ActiveXObject' in window) {
          // ie和其他浏览器不一样。。。
          event.dataTransfer.setData('Text', event.target.innerText);
        } else {
          event.dataTransfer.setData('te', event.target.innerText); //不能使用text，firefox会打开新tab
        }
        draging = event.target;
        if (draging.nodeName === 'SPAN') {
          draging = draging.parentNode;
        }
      }

      /**
       * 将li的事件委托给ul
       */
      function ondragoverOfli(event) {
        event.preventDefault();
        var target = event.target.parentNode;
        //因为dragover会发生在ul上，所以要判断是不是li
        if (target.nodeName === 'LI' && target !== draging) {
          //_index是实现的获取index
          if (_index(draging) < _index(target)) {
            target.parentNode.insertBefore(draging, target.nextSibling);
          } else {
            target.parentNode.insertBefore(draging, target);
          }
        }
      }

      /**
       * 获取li元素的下标
       */
      function _index(el) {
        var index = 0;
        if (!el || !el.parentNode) {
          return -1;
        }
        while (el && el.previousElementSibling) {
          el = el.previousElementSibling;
          index++;
        }
        return index;
      }

      /**
       * 设置列表的高度
       */
      function setDivHeight() {
        //网页可见区域高度
        var clientHeight = document.body.clientHeight;
        var clientWidth = document.body.clientWidth;
        var divHeight = clientHeight - (150 + 180);
        $('#divTBDis').height(divHeight);
        $('#subDivTBDis').height(divHeight - 50);
        $('#buttonStyle').css(inform.getButtonStyle(clientHeight, clientWidth));
      }

      /**
       * 初始化信息
       */
      function initPerson() {
        // 抄送人下拉列表
        $scope.employeeList = [];
        // 发件人下拉列表
        $scope.employeeListAddress = [];

        comService.getEmployeesByOrgId('').then(function (data) {
          if (data.data) {
            var _$scope$employeeListA;

            $scope.employeeList = data.data;
            // 数组拷贝
            _$scope$employeeListA = $scope.employeeListAddress;
            _$scope$employeeListA.push.apply(_$scope$employeeListA, _toConsumableArray($scope.employeeList));
            //获取缓存
            $scope.changeParam = LocalCache.getObject('earlyWarning_changeParam');
            //将id提取出来成为List
            var ids = $scope.changeParam.afterTaskIds.split(',');
            $scope.changeParam.afterTaskIdList = ids;
            //获取全部定时任务信息
            earlyWarningService.getTotalTask($scope.changeParam.id, 'update').then(function (data) {
              $scope.tasksList = data.data;
            });
            $scope.changeParam.addressee =
              $scope.changeParam.addressee == null ? [] : $scope.changeParam.addressee.split(',');
            // $scope.employeeListAddress
            $scope.employeeListAddress = changeListOrder($scope.changeParam.addressee, $scope.employeeListAddress);
            $scope.changeParam.copyTo = $scope.changeParam.copyTo == null ? [] : $scope.changeParam.copyTo.split(',');
            // $scope.employeeList排序
            $scope.employeeList = changeListOrder($scope.changeParam.copyTo, $scope.employeeList);
            // 节假日策略默认为忽略
            if (!$scope.changeParam.holidayStrategy) {
              $scope.changeParam.holidayStrategy = '0';
            }
            setDraggableTime();
            $('div.input-group input').val($scope.changeParam.cronExpression);
          }
        });
      }

      /*
       * 让bigList按照 smallList 的顺序显示
       * */
      function changeListOrder(smallList, bigList) {
        /*
         * reverse() 数组倒叙
         * unshift() 插入元素至数组头部
         *
         * */
        smallList.reverse().forEach(function (copy) {
          // 在所有人员中取到抄送人
          var copyItem = bigList.find(function (item) {
            return item.loginName === copy;
          });
          // 在所有人员中删除抄送人
          bigList = bigList.filter(function (item) {
            return item.loginName !== copy;
          });
          // 把抄送人添加到数组头部
          bigList.unshift(copyItem);
        });
        return bigList;
      }

      /**
       * 修改信息
       */
      $scope.updateInfo = function () {
        var cronValue = $('#cron').val();
        if (cronValue === '') {
          inform.common('cron表达式不能为空');
          return;
        }
        // 通过dom节点获取收件人
        var addressSelect = document.getElementById('addressSelect').nextElementSibling.childNodes[0].childNodes;
        var addressList = [];
        angular.forEach(addressSelect, function (item) {
          var str = item.innerText;
          var subStr = str.substring(str.indexOf('(') + 1, str.indexOf(')'));
          if (subStr !== '') {
            addressList.push(subStr);
          }
        });
        // 通过dom节点获取抄送人
        var copyTosSelect = document.getElementById('copyTosSelect').nextElementSibling.childNodes[0].childNodes;
        var ccList = [];
        angular.forEach(copyTosSelect, function (item) {
          var str = item.innerText;
          var subStr = str.substring(str.indexOf('(') + 1, str.indexOf(')'));
          if (subStr !== '') {
            ccList.push(subStr);
          }
        });
        // 数组去重
        addressList = Array.from(new Set(addressList));
        var urlData = {
          id: $scope.changeParam.id, //定时任务ID
          jobName: $scope.changeParam.jobName, //任务名称
          disabled: $scope.changeParam.disabled, //任务状态
          cronExpression: cronValue, //corn表达式
          remark: $scope.changeParam.remark, //备注：
          beanName: $scope.changeParam.beanName, //单例名称：
          addressee: addressList.join(), //收件人'
          copyTo: ccList.join(), //抄送
          module: $scope.changeParam.module, //模板
          afterTaskIds: $scope.changeParam.afterTaskIdList.join(','), //后置任务Id
          holidayStrategy: $scope.changeParam.holidayStrategy, //节假日策略
          type: 1,
        };
        earlyWarningService.updateEarlyWarning(urlData).then(
          function (data) {
            if (data.code === AgreeConstant.code) {
              inform.common(data.message);
              $state.go('app.office.earlyWarningController', null);
            } else {
              inform.common(data.message);
            }
          },
          function (error) {
            inform.common(Trans('tip.requestError'));
          }
        );
      };

      // 数组复制
      function _toConsumableArray(arr) {
        if (Array.isArray(arr)) {
          for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) {
            arr2[i] = arr[i];
          }
          return arr2;
        } else {
          return Array.from(arr);
        }
      }

      /**
       * *************************************************************
       *              方法声明部分                                 结束
       * *************************************************************
       */
    },
  ]);
})();

/*
 * @Author: lijing
 * @Date:   2020-12-08 10:37:05
 */
(function() {
    'use strict';
  app.factory('budgetService', budgetService);
  budgetService.$inject=["HttpService",'$rootScope'];

  function budgetService(HttpService,$rootScope){
    
	var service={
			getProjectBudget:getProjectBudget,
			getPlmBudget:getPlmBudget,
			getFeeBudgetInfo:getFeeBudgetInfo,
			getProjectStageList:getProjectStageList,
			getTitleLevelList:getTitleLevelList,
			saveBudget:saveBudget,
			getPersonBudgetInfo:getPersonBudgetInfo,
			getCostLevel:getCostLevel,
			getPersonBudgetAll:getPersonBudgetAll,
			getAmountAll:getAmountAll,
			getAmountDetail:getAmountDetail,
			getProAllPlmAmountDetail:getProAllPlmAmountDetail,
			travelFeeBudgetInfo:travelFeeBudgetInfo
	};
    return service;
    
    /**
     * 获取项目预算信息
     */
    function getProjectBudget(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'budgetAction/getProjectBudget', urlData);
    }
    
    /**
     * 获取plm预算信息
     */
    function getPlmBudget(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'budgetAction/getPlmBudget', urlData);
    }
    
    /**
     * 获取人力预算明细
     */
    function getPersonBudgetInfo(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'budgetAction/getPersonBudgetInfo', urlData);
    }
    /**
     * 获取项目费用预算明细
     */
    function getFeeBudgetInfo(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'budgetAction/getFeeBudgetInfo', urlData);
    }

    //获取差旅预算明细
    function travelFeeBudgetInfo(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'budgetAction/travelFeeBudgetInfo', urlData);
    }
    
    /**
     * 获取项目相关阶段
     */
    function getProjectStageList(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'costCom/getProjectStageList', urlData);
    }
    
    /**
     * 查询项目相关岗位和级别
     */
    function getTitleLevelList(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'costCom/getTitleLevelList', urlData);
    }
    
    /**
     * 保存预算
     */
    function saveBudget(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'budgetAction/saveBudget',urlData);
    }
    
    /**
     * 查询所有级别信息
     */
    function getCostLevel() {
        return HttpService.get($rootScope.getWaySystemApi + 'budgetAction/getCostLevel');
    }
    
    /**
     * 获取人力预算汇总
     */
    function getPersonBudgetAll(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'budgetAction/getPersonBudgetAll', urlData);
    }
    
    /**
     * 获取指定项目/plm人力预算金额汇总
     */
    function getAmountAll(projectId,plmUploadId) {
    	return HttpService.get($rootScope.getWaySystemApi + 'costCom/getAmountAll', 
    			{'projectId':projectId,'plmUploadId':plmUploadId});
    }
    
    /**
     * 获取指定项目/plm人力预算金额汇总
     */
    function getAmountDetail(projectId,plmUploadId) {
    	return HttpService.get($rootScope.getWaySystemApi + 'costCom/getAmountDetail', 
    			{'projectId':projectId,'plmUploadId':plmUploadId});
    }
    
    /**
     * 获取指定项目下所有plm人力预算金额汇总
     */
    function getProAllPlmAmountDetail(projectId) {
    	return HttpService.get($rootScope.getWaySystemApi + 'costCom/getProAllPlmAmountDetail', 
    			{'projectId':projectId});
    }
  }
})();

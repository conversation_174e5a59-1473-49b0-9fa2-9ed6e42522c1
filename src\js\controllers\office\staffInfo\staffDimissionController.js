(function() {
    'use strict';
    app.controller("staffDimissionManagementController", ['comService', '$rootScope', '$stateParams', '$scope','staffDimissionService','LocalCache','$modal','inform', 'Trans', 'AgreeConstant','$state',
        function(comService, $rootScope, $stateParams,$scope,staffDimissionService,LocalCache,$modal,inform, Trans, AgreeConstant,$state) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            // 列表数据
            $scope.dataList = [];
            $scope.datepicker = {
               currentDate :  new Date()
            };

            $scope.flag = false;
            //设置员工地区的访问权限
            $scope.areaCodeFlag = true;
            //绑定文件控件改变事件
            $("#filesImg1").change(submitForm);
            $("#filesImg1").change(fileChangeEvent);
            //设置列表的高度
    		setDivHeight();
    		//窗体大小变化时重新计算高度
    		$(window).resize(setDivHeight);
            //新增数据对象
            $scope.addParam = {};
            //修改数据对象
            $scope.updateParam = {};
            getStaffList();//员工编号和姓名列表
            getAreaList();//地区列表
            getLeavingReasonList();//获取员工离职原因列表
            getStateList();//状态列表
            $scope.getData = getData;
            $scope.pages = {
               pageNum:"1",
               size:"100"
            };
            //查询条件对象
            $scope.searchObject = {};
            initPrimaryDeptList();//初始化一级部门列表
            $scope.searchObject.primaryDept = "";//初始化一级部门
            getButtonPermission();//判断按钮是否具有权限

            //初始化页面
            initPage();
           /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                开始
             * *************************************************************
             */
            /**
             * 获取按钮权限
             */
            function getButtonPermission(){
                var buttons = {
                    'Button-s32r-add':'specailAddType',
                    'Button-s32r-allAdd':'specailAllAddType',
                    'Button-s32r-update':'specailUpdateType'
                };
                var urlData = {
                    'userId':LocalCache.getSession("userId"),
                    'parentPermission':'ButtonCodeStaffInfoManagement',
                    'buttons':buttons
                };
                comService.getButtonPermission(urlData,$scope);
            }

            //重置查询条件
            $scope.clearParams = function() {
                //区域权限
                if (!$scope.areaCodeFlag) {
                    $scope.searchObject.area = "";
                }
                if($scope.flag){
                   $scope.searchObject.primaryDept = "";
                   $scope.searchObject.department = "";

                }

                $scope.searchObject.employeeName = "";
                $scope.searchObject.onboardingTime = "";
                $scope.searchObject.exitClassify = "";
                $scope.searchObject.classificationReasonsForLeaving = "";
                $("#form")[0].reset();
                $("#fileNameDis").text("");
            };
          
           /**
            ** 员工编号和姓名列表
            */
            function getStaffList(){
                comService.getStaffList().then(function(data) {
                  $scope.staffList = data.data;
                });
            }

            /**
              * 选择上传文件后事件
            */
            function fileChangeEvent(e){
                var fileName = "文件名称：" + e.currentTarget.files[0].name;
                $("#fileNameDis").text(fileName);
            } 


            //获取员工离职原因列表
            function getLeavingReasonList() {
                //获取系研职称信息
                $scope.leavingReasonList = [];
                $scope.leavingReasonMap = [];
                comService.getParamList('CLASSIFICATION_REASONS_FOR_LEAVING','CLASSIFICATION_REASONS_FOR_LEAVING').then(function(data) {
                   $scope.leavingReasonList = data.data;
                    angular.forEach(data.data, function (res) {
                        $scope.leavingReasonMap[res.param_value] = res.param_code;
                    });
                });

            }

            //获取地区
            function getAreaList(){
                $scope.areaList = [];
                comService.getParamList('AREA_TYPE','AREA_TYPE').then(function(data) {
                   $scope.areaList = data.data;
                });
            }
            //获取工作状态列表
            function getStateList() {
                //获取系研职称信息
                $scope.stateList = [];
                comService.getParamList('STAFF_STATE','STAFF_STATE').then(function(data) {
                   $scope.stateList = data.data;
                });

            }


            /**
             * 初始化根据用户名获取一级部门列表
             */
            function initPrimaryDeptList() {
                $scope.primaryDeptList = [];
                comService.getOrgChildren('0002').then(function(data) {
                     if (data.data) {
                         $scope.primaryDeptList = data.data;
                     }
                });
            }

             /**
             * 初始化二级部门列表
             */
            function initSecDeptList() {
                 $scope.changeDept();
            }

            //修改一级部门，二级部门进行联动
            $scope.changeDept = function(){
                //获取二级部门
                $scope.deptList = [];
                comService.getOrgChildren($scope.searchObject.primaryDept).then(function (data) {
                    if (data.code === AgreeConstant.code) {
                        $scope.deptList = data.data;
                    }
                });
            };

            //定义排序对象
            $scope.orderObj = {
                title: '$index',
                desc: true,
                order: function(str) {
                    $scope.orderObj.title = str;
                    $scope.orderObj.desc = !$scope.orderObj.desc;
                }
            };

           
            /**
        	 * 设置列表的高度
        	 */
     		function setDivHeight(){
     			//网页可见区域高度
     			 var clientHeight = document.body.clientHeight;
     			 var divHeight = clientHeight - (150 + 185);
     			 $("#divTBDis").height(divHeight);
     			 $("#subDivTBDis").height(divHeight - 65);
     		}

            //分页查询
            function getData(page) {
                if (inform.format($scope.searchObject.onboardingTime,'yyyy-MM-dd')==="NaN-NaN-NaN") {
                    $scope.searchObject.onboardingTime='';
                }else{
                    $scope.searchObject.onboardingTime = inform.format($scope.searchObject.onboardingTime,'yyyy-MM-dd');
                }
               
                //拼装查询条件
                var params = {
                    area:$scope.searchObject.area,
                    department:$scope.searchObject.department,
                    primaryDept:$scope.searchObject.primaryDept,
                    employeeName:$scope.searchObject.employeeName,
                    onboardingTime:$scope.searchObject.onboardingTime,
                    exitClassify:$scope.searchObject.exitClassify,
                    classificationReasonsForLeaving:$scope.searchObject.classificationReasonsForLeaving,
                    page: page,
                    size: $scope.pages.size
                };
                //获取数据
                staffDimissionService.selectDimissionByParam(JSON.stringify(params)).then(function (result) {
                     if (result.code === '0000') {
                        $scope.dataList = result.data.list;
                        if (null ==result.data.list || result.data.list.length === 0) {
                            inform.common(Trans("tip.noData"));
                            $scope.pages = inform.initPages();
                        } else {
                            // 分页信息设置
                            $scope.pages.total = result.data.total;		// 页面总数
                            $scope.pages.star = result.data.startRow;  	//页面起始数
                            $scope.pages.end = result.data.endRow;  		//页面大小数
                            $scope.pages.pageNum = result.data.pageNum;  	//页面页数
                        }
                     } else {
                        inform.common(result.message);
                     }
                }, function (reason) {
                    console.log("error");
                });
            }

            //初始化页面Function
            function initPage() {

                getDepartmentCode();
            }

            //获取部门权限码(先判断是否为中心办，如果不是，在判断是否为白名单的人员)
            function getDepartmentCode(){
                   //判断是否为中心办
                   comService.isCenterOffice().then(function (res) {
                      if(res.code==="0000" && res.data.code==='01'){
                        //01全部权限
                        $scope.flag = true;
                        getData(1);
                        return;
                      }else{
                        comService.validAuthentication("0006").then(function (result) {
                             if(result.code==='0000'){
                                 if(result.data.code==='00'){
                                     $state.go('app.office.unAuthority');
                                     return;
                                 }
                                 if(result.data.code==='01'){
                                     $scope.flag = true;
                                     getData(1);
                                     return;
                                 }
                                 if (result.data.code === '03') {
                                     $scope.areaCodeFlag = true;
                                     $('#area').attr('disabled', true);
                                     $scope.searchObject.area = result.data.areaCode;
                                     getData(1);
                                     return;
                                 }
                                $scope.searchObject.primaryDept = res.data.primaryDeptCode;
                                $("#primaryDeptName").attr("disabled","disabled");
                                initSecDeptList();
                                if(res.data.departmentCode){
                                    $scope.searchObject.department = res.data.departmentCode;
                                    $("#departmentName").attr("disabled","disabled");
                                }
                                getData(1);


                             }
                          });


                      }
                   });

            }




            //打开新增窗口
            $scope.openAddModal = function() {
                $scope.addParam = {};
            };
            //保存新增数据
            $scope.saveAddData = function () {
                if(!$scope.addParam.leavingDate){
                    inform.common("离职时间不能为空");
                    return;
                }
                if(!$scope.addParam.classificationReasonsForLeaving){
                    inform.common("离职原因分类不能为空");
                    return;
                }
                var param = {
                    "employeeId":$scope.addParam.employeeId,
                    "employeeName":$scope.addParam.employeeName,
                    "leavingDate":inform.format($scope.addParam.leavingDate,'yyyy-MM-dd')==='NaN-NaN-NaN'?null:inform.format($scope.addParam.leavingDate,'yyyy-MM-dd'),
                    "classificationReasonsForLeaving":$scope.addParam.classificationReasonsForLeaving,
                    "leavingReasons":$scope.addParam.leavingReasons
                };
                staffDimissionService.addDimissionByParam(param).then(function (result) {
                    if (result.code==='0000') {
                        $("#add_staffInfo").modal('hide');
                        layer.msg("信息新增成功",
                          {
                            time:1000//1秒自动关闭
                          }, function () {
                          getData(1);
                          $scope.addParam = {};
                        }
                      );
                    }else{
                        inform.common(result.message);
                    }
                    
                });
            };

            $scope.selectFile = function() {
                document.getElementById("filesImg1").click();
            };

            //离职时间
            $scope.openLeavingTime = function($event) {
                $event.preventDefault();
                $event.stopPropagation();  
                $scope.openLeavingTime1 = true;
                $scope.openOnboardTime2 = false;
                $scope.openGraduationTime1 = false;
                $scope.openOnboardingTime1 = false; 
            };
            //入职时间
            $scope.openOnboardTime = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openLeavingTime1 = false;    
                $scope.openOnboardTime2 = true;  
                $scope.openGraduationTime1 = false;
                $scope.openOnboardingTime1 = false;        
            };
            //毕业时间
             $scope.openGraduationTime = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openLeavingTime1 = false;    
                $scope.openGraduationTime1 = true;   
                $scope.openOnboardTime2 = false;
                $scope.openOnboardingTime1 = false;   
            };
            //入职时间（搜索栏）
             $scope.openOnboardingTime = function($event) {
                $event.preventDefault();
                $event.stopPropagation();
                $scope.openLeavingTime1 = false;    
                $scope.openOnboardTime2 = false;  
                $scope.openGraduationTime1 = false;
                $scope.openOnboardingTime1 = true;      
            };

            //打开修改窗口
            $scope.openUpdateModal = function(item) {
                $scope.updateParam = angular.copy(item);
                $scope.updateParam.classificationReasonsForLeaving = $scope.leavingReasonMap[item.classificationReasonsForLeaving];
                $scope.updateParam.oldState = item.oldStateCode;
                $scope.updateParam.currentState = "离职";
            };

            //保存修改数据
            $scope.saveUpdateData = function () {
                var param = {
                    "id":$scope.updateParam.id,
                    "employeeName":$scope.updateParam.employeeName,
                    "employeeId":$scope.updateParam.employeeId,
                    "leavingDate":inform.format($scope.updateParam.leavingDate,'yyyy-MM-dd')==='NaN-NaN-NaN'?null:inform.format($scope.updateParam.leavingDate,'yyyy-MM-dd'),
                    "currentState":$scope.updateParam.currentState,
                    "oldState":$scope.updateParam.oldState,
                    "classificationReasonsForLeaving":$scope.updateParam.classificationReasonsForLeaving,
                    "leavingReasons":$scope.updateParam.leavingReasons
                };
                staffDimissionService.updateDimissionByParam(param).then(function (result) {
                    if ( result.code==='0000') {
                     $("#update_staffInfo").modal("hide");
                        layer.msg("修改成功",
                          {
                            time:1000//1秒自动关闭
                          }, function () {
                            getData(1);
                          }
                        );
                    }else{
                        inform.common(result.message);
                    }
                });
            };

            // 删除确认
            $scope.deleteConfirm = function (id) {
                inform.modalInstance("确定要删除吗？").result.then(function () {
                    $scope.deleteByIds(id);
                });
            };

            //根据选中的id 删除数据
            $scope.deleteByIds = function (id) {
                var param = {
                    "id" : id
                };
                staffDimissionService.deleteDimissionByIds(param)
                    .then(function (data) {
                        if (data.code === "0000") {
                            inform.common('删除成功');
                            getData(1);
                        } else {
                            inform.common(data.message);
                        }
                    }, function (error) {
                        inform.common(Trans("tip.requestError"));
                });
            };



            //生成Excel模板
            $scope.toTemplateExcel = function() {

                inform.modalInstance("确定要下载吗?").result.then(function() {
                    var params = {};
                    inform.downLoadFile('staffDimission/toTemplateExcel',params,"员工离职信息表模板.xlsx");
                });
            };

             //生成Excel表格
            $scope.toExcel = function() {
                inform.modalInstance("确定要下载吗?").result.then(function() {

                    var params = {
                       area:$scope.searchObject.area,
                       department:$scope.searchObject.department,
                       employeeName:$scope.searchObject.employeeName,
                       onboardingTime:$scope.searchObject.onboardingTime,
                       exitClassify:$scope.searchObject.exitClassify,
                       classificationReasonsForLeaving:$scope.searchObject.classificationReasonsForLeaving

                    };
                    inform.downLoadFile('staffDimission/toExcel',params,'员工离职信息表.xlsx');
                });
            };


            //上传文件
        function submitForm(){
            var formData = new FormData(document.getElementById("form"));//表单id  初始化表单值
            var file = document.querySelector('input[type=file]').files[0]; //获取文档中有类型为file的第一个input元素
            if(!file){
                inform.common("请先选择文件!");
                return false;
            }
            formData.append('fileName', file);
            var a = file.type;
            if(a !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"){
                inform.common("请选择.xlsx类型的文档进行上传!");
                return false;
            } else {

              inform.modalInstance("确定要上传文件吗?").result.then(function() {
                 inform.uploadFile('staffDimission/uploadExcel',formData,function func(result){
                  if (result.code===AgreeConstant.code) {
                       // 关闭遮罩层
                       inform.closeLayer();
                       $modal.open({
                       templateUrl: 'errorModel.html',
                       controller: 'ModalInstanceCtrl',
                       size: "lg",
                       resolve: {
                           items: function () {
                               return result.message;
                           }
                       }
                       });

                  } else {
                      // 关闭遮罩层
                     inform.closeLayer();
                     inform.common("上传失败！");
                  }
                  getData(1);
                  $("#form")[0].reset();
                  $("#fileNameDis").text("");



                });
              });
            }

        }
 /**
         * *************************************************************
         *              方法声明部分                                结束
         * *************************************************************
         */

           

        }
    ]);
})();
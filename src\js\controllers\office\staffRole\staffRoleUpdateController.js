
(function () {
    app.controller("staffRoleUpdateController", ['comService','$rootScope', '$scope','$filter','$state','$stateParams','$modal','staffRoleUpdateService','inform','Trans','AgreeConstant','LocalCache',
        function (comService,$rootScope, $scope, $filter,$state,$stateParams, $modal,staffRoleUpdateService,inform,Trans,AgreeConstant,LocalCache) {

        /**
         * *************************************************************
         *             初始化部分                                 开始
         * *************************************************************
         */
		//设置列表的高度
		 setDivHeight();
		//窗体大小变化时重新计算高度
		 $(window).resize(setDivHeight);
		//角色下拉框列表
		$scope.rolesList = [];
		//获取方式下拉框列表
		$scope.getTypeList = [];
    	//角色列表
        $scope.roleList = [];
		//人员信息
		$scope.formUpRole = {};
		//保存方法
		$scope.saveRole = saveRole;
		//返回列表
		$scope.toList = toList;
        
		$scope.formUpRole = JSON.parse($stateParams.dataDetails);
		var dataDetails =  $scope.formUpRole.staffRoleList;
		
		angular.forEach(dataDetails,function(item){
			if(item.role !== null && item.role !== ''){
				var role = {
					'id':item.id,
					'role':item.role,
					'applyDate':item.getDate,
					'duty':item.duty,
					'getType':item.getType,
					'disabled':true,
					'openOnboardTime':false
				}
				$scope.roleList.push(role)
			}		
		})
			
		
		
    	//初始化页面信息
		initPages()
        /**
         * *************************************************************
         *              初始化部分                                 结束
         * *************************************************************
         */

        /**
         * *************************************************************
         *              方法声明部分                                 开始
         * *************************************************************
         */

        /**
    	 * 页面初始化
    	 */
    	function initPages() {
    		//获取角色下拉框
			$scope.rolesList = AgreeConstant.roleList;
			//获取获取方式下拉框
			$scope.getTypeList = AgreeConstant.getTypeList;
    	}


		/**
		* 保存修改
		*/
		function saveRole() {
			angular.forEach($scope.roleList,function(item){
				item.employeeNo = $scope.formUpRole.employeeNo;
				item.remarks = $scope.formUpRole.remarks;
			});

			var urlData = {
				'staffRoleList':$scope.roleList
			};
			staffRoleUpdateService.saveStaffRole(urlData).then(function (data) {
				if (data.code === AgreeConstant.code) {
					inform.common("修改角色成功");
					setTimeout(toList,500);
				}else {
					inform.common(data.message);
				}
			}, function (error) {
				inform.common(Trans("tip.requestError"));
			});
		}

		
        
    


		/**
		* 申请时间
		*/
		$scope.openOnboardTime = function ($event,item) {
			$event.preventDefault();
			$event.stopPropagation();
			angular.forEach($scope.roleList,function(i){
				if(i === item){
					i.openOnboardTime = true;
				}
			});
		};

		/**
		* 设置日期为一号
		*/
		$scope.setDate = function (item) {
			var date = $filter('date')(item.applyDate,'yyyy-MM-dd');
			item.applyDate = date;
	   };

	   /**
		* 跳转至列表
		*/
		function toList() {
			$state.go("app.office.staffRole");
		}

		function setDivHeight() {
			//网页可见区域高度
			var clientHeight = document.body.clientHeight;
			var divHeight = clientHeight - 365;
			$("#roleUp").height(divHeight - 40);
		}
    	
        
     	
     	
     	
	}]);
})();
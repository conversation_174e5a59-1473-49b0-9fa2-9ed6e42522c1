(function () {
    app.controller("personalBoardController", ['$rootScope', 'comService', '$scope', '$state', '$timeout', '$stateParams', '$modal', 'personalBoardService', 'inform', 'Trans', 'AgreeConstant', 'LocalCache', '$http',
        function ($rootScope, comService, $scope, $state, $timeout, $stateParams, $modal, personalBoardService, inform, Trans, AgreeConstant, LocalCache, $http) {

            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            //页面数据
            $scope.formRefer = LocalCache.getObject('personalBoardSearchCondition');
            LocalCache.setObject('personalBoardSearchCondition',{});
            //季度下拉框数据源
            $scope.quarterSelect = [
                {
                    value: '6',
                    label: '上半年'
                },{
                    value: '7',
                    label: '下半年'
                }, {
                    value: '5',
                    label: '年度'
                }
                // , {
                //     value: '1',
                //     label: '第1季度'
                // }, {
                //     value: '2',
                //     label: '第2季度'
                // }, {
                //     value: '3',
                //     label: '第3季度'
                // }, {
                //     value: '4',
                //     label: '第4季度'
                // }
                ];
            //分页
            $scope.pages = {
                pageNum:"1",
                size:"200"
            };

            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            $scope.getData = getData;
            //初始化信息
            initData();

            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */

            /**
             * 初始化
             */
            function initData() {
                initTime();
                //获取部门信息
                $scope.departmentSelect = [];
                comService.getOrgChildren('D010053').then(function(data) {
                    $scope.departmentSelect = comService.getDepartment(data.data);
                });
                //获取地区
                $scope.areaList = [];
                comService.getAreaList().then(function(data) {
                    $scope.areaSelect = data.data;
                });
                //获取系研职称信息
                $scope.titleList = [];
                comService.getParamList('STAFF_TITLE','NEW').then(function(data) {
                    $scope.titleList = data.data;
                });
                getData();
            }
            /**
             * 初始化检索条件年度与季度
             */
            function initTime(){
                //当前时间（Date类型）
                var date = new Date();
                //得到上一个季度的第一天
                var lastQuarterFirstDay = new Date(date.getFullYear() , date.getMonth() - 3 , 1);
                var day = inform.format(lastQuarterFirstDay, 'yyyy-MM-dd').split("-");
                $scope.formRefer.year = $scope.formRefer.year != null ? $scope.formRefer.year : day[0];
                //设置季度
                var month = lastQuarterFirstDay.getMonth() + 1;
                $scope.formRefer.quarter = month>6?'7':'6';
            }
            /**
             * 获取考核数据
             */
            function getData() {
                var urlData = {
                    'name':$scope.formRefer.name,
                    'year':$scope.formRefer.year,
                    'quarter':$scope.formRefer.quarter,
                    'area':$scope.formRefer.area,
                    'department':$scope.formRefer.department,
                    'title':$scope.formRefer.title,
                    'currentPage': $scope.pages.pageNum,
                    'pageSize': $scope.pages.size
                }

                personalBoardService.getPersonBoardList(urlData).then(function (data) {
                        if (data.code === AgreeConstant.code) {
                            if (null !== data.data) {
                                $scope.dataList = data.data.list;
                                //分页信息设置
                                $scope.pages.total = data.data.total;
                                $scope.pages.star = data.data.startRow;
                                $scope.pages.end = data.data.endRow;
                                $scope.pages.pageNum = data.data.pageNum;
                            } else {
                                inform.common(Trans("tip.noData"));
                                $scope.pages = inform.initPages();
                                $scope.pages.size = "200";
                            }
                        } else {
                            inform.common(data.message);
                        }
                    },
                    function () {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 210);
                $("#divTBDis").height(divHeight);
                $("#subDivTBDis").height(divHeight - 40);
                $("#divTBDis1").height(divHeight);
                $("#subDivTBDis1").height(divHeight - 40);
            }

            $scope.detail = function(item) {
                $state.go('app.index_bench',
                    {empId:item.employeeId, years:$scope.formRefer.year, quarter:$scope.formRefer.quarter});
                LocalCache.setObject('personDataBoardEmployee', item);
                LocalCache.setObject('personalBoardSearchCondition',$scope.formRefer);
            }

            /**
             * 重置
             */
            $scope.clearParams = function() {
                $scope.formRefer = {};
                initTime();
            };

            //定义排序对象
            $scope.orderObj = {
                title: '$index',
                desc: false,
                order: function(str) {
                    $scope.orderObj.title = str;
                    $scope.orderObj.desc = !$scope.orderObj.desc;
                }
            };

            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */

        }]);
})();

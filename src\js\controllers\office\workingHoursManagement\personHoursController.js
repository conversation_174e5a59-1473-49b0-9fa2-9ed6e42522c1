/*
 * @Author: liyunmeng
 * @Date:   2020-08-03
 */
(function () {
    app.controller('personHoursController', [
        'comService',
        '$rootScope',
        '$scope',
        'personHoursService',
        'inform',
        'Trans',
        'AgreeConstant',
        '$modal',
        '$state',
        'LocalCache',
        '$http',
        function (
            comService,
            $rootScope,
            $scope,
            personHoursService,
            inform,
            Trans,
            AgreeConstant,
            $modal,
            $state,
            LocalCache,
            $http
        ) {
            /**
             * *************************************************************
             *             初始化部分                                 开始
             * *************************************************************
             */
            // 获取跳转缓存
            $scope.formRefer = LocalCache.getObject('ManagementHours_formRefer');
            if ($scope.formRefer.unit == null || $scope.formRefer.unit === '') {
                //获取缓存
                $scope.formRefer = LocalCache.getObject('personHours_formRefer');
                //设置默认时间
                if ($scope.formRefer.startTime == null) {
                    init($scope);
                    initTime($scope, '本周');
                }
                //对原缓存进行覆盖
                LocalCache.setObject('personHours_formRefer', {});
            } else {
                $scope.formRefer.department = $scope.formRefer.unit;
                $scope.formRefer.startTime = $scope.formRefer.startTime;
                $scope.formRefer.endTime = $scope.formRefer.endTime;
            }
            //对原跳转缓存进行覆盖
            LocalCache.setObject('ManagementHours_formRefer', {});
            $scope.formRefer = {
                ...$scope.formRefer,
                personName: '',
                productLine: '',
                area: '',
            };
            $scope.pages = {
                pageNum: '', // 分页页数
                size: '', // 分页每页大小
                total: '', // 数据总数
            };
            $scope.pages = inform.initPages(); // 初始化分页数据
            $scope.getData = getData; // 分页相关函数
            $scope.reset = reset;
            //初始化查询条件
            initPage();
            //在刷新页面时调用该方法
            getData($scope.pages.pageNum);
            //设置列表的高度
            setDivHeight();
            //窗体大小变化时重新计算高度
            $(window).resize(setDivHeight);
            //初始化分页数据
            $scope.projectSelected = [];
            /**
             * *************************************************************
             *              初始化部分                                 结束
             * *************************************************************
             */

            /**
             * *************************************************************
             *              方法声明部分                                 开始
             * *************************************************************
             */
            /**
             * 初始化查询条件
             */
            function initPage() {
                // 获取产品线
                $scope.productLines = [];
                comService.getParamList('PRODUCT_TYPE', 'PRODUCT_TYPE').then(function (res) {
                    if (res && res.data) {
                        $scope.productLines = angular.fromJson(res.data);
                    }
                });
                //获取山东新北洋集团的下级部门信息
                $scope.departmentList = [];
                comService.getOrgChildren('D010053').then(function (res) {
                    if (res && res.data) {
                        $scope.departmentList = comService.getDepartment(res.data);
                    }
                });
                //初始化地区
                $scope.areaList = [];
                comService.getParamList('AREA_TYPE', 'AREA_TYPE').then(function (res) {
                    if (res && res.data) {
                        $scope.areaList = res.data;
                    }
                });
                // 参数初始化
                let searchInfo = LocalCache.getObject('searchInfo');
                // 第一次进入时，默认查询本周，以后的查询条件时都会按照缓存的来
                if (searchInfo.startTime) {
                    $scope.formRefer.personName = searchInfo.employeeName;
                    $scope.formRefer.department = searchInfo.orgCode;
                    $scope.formRefer.startTime = searchInfo.startTime;
                    $scope.formRefer.endTime = searchInfo.endTime;
                    $scope.formRefer.productLine = searchInfo.productLine;
                    $scope.formRefer.area = searchInfo.area;
                    $scope.formRefer.currentPage = searchInfo.currentPage;
                    $scope.formRefer.pageSize = searchInfo.pageSize;
                    // 如果选择过快捷查询就用缓存中的
                    if (searchInfo.butFlag) {
                        $scope.butFlag = searchInfo.butFlag;
                    }
                }
                getData('', searchInfo);
            }
            /**
             * 获取个人工时信息
             */
            function getData(pageNum, searchInfo) {
                $scope.personHoursList = [];
                var urlData = {
                    employeeName: $scope.formRefer.personName, //员工名称
                    orgCode: $scope.formRefer.department, //部门
                    startTime: inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'), //开始时间
                    endTime: inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间
                    productLine: $scope.formRefer.productLine, //产品线
                    area: $scope.formRefer.area, //区域
                    currentPage: pageNum, //分页页数
                    pageSize: $scope.pages.size, // 分页每页大小
                    butFlag: $scope.butFlag,
                };
                if (searchInfo && searchInfo.startTime) {
                    urlData = searchInfo;
                }
                if (!urlData.startTime) {
                    layer.msg('查询条件为空');
                    return;
                }
                // 将查询结果存入缓存
                LocalCache.setObject('searchInfo', urlData);
                personHoursService.getPersonHoursList(urlData).then(function (data) {
                    if (data && data.data) {
                        $scope.personHoursList = angular.fromJson(data.data.list);
                        angular.forEach($scope.personHoursList, function (item) {
                            item.proHoursProportion = (
                                ((item.zeroType + item.oneType) /
                                    (item.zeroType + item.oneType + item.twoType + item.threeType + item.fourType)) *
                                100
                            ).toFixed(2);
                            item.orgHoursProportion = (
                                ((item.twoType + item.threeType) /
                                    (item.zeroType + item.oneType + item.twoType + item.threeType + item.fourType)) *
                                100
                            ).toFixed(2);
                            if (item.proHoursProportion !== 'NaN') {
                                item.proHoursProportion += '%';
                            } else {
                                item.proHoursProportion = '-';
                            }
                            if (item.orgHoursProportion !== 'NaN') {
                                item.orgHoursProportion += '%';
                            } else {
                                item.orgHoursProportion = '-';
                            }
                            if (item.zeroType === null || item.zeroType === '') {
                                item.zeroType = 0;
                            }
                            if (item.oneType === null || item.oneType === '') {
                                item.oneType = 0;
                            }
                            if (item.twoType === null || item.twoType === '') {
                                item.twoType = 0;
                            }
                            if (item.threeType === null || item.threeType === '') {
                                item.threeType = 0;
                            }
                            if (item.fourType === null || item.fourType === '') {
                                item.fourType = 0;
                            }
                            if (item.personAllHours === null || item.personAllHours === '') {
                                item.personAllHours = 0;
                            }
                            if (item.personOrgHours === null || item.personOrgHours === '') {
                                item.personOrgHours = 0;
                            }
                            if (item.personProjectHours === null || item.personProjectHours === '') {
                                item.personProjectHours = 0;
                            }
                        });
                        // 分页信息设置
                        $scope.pages.total = data.data.total; // 页面数据总数
                        $scope.pages.star = data.data.startRow; // 页面起始数
                        $scope.pages.end = data.data.endRow; // 页面结束数
                        $scope.pages.pageNum = data.data.pageNum; //页号
                    }
                });
            }
            /**
             * 设置列表的高度
             */
            function setDivHeight() {
                //网页可见区域高度
                var clientHeight = document.body.clientHeight;
                var divHeight = clientHeight - (150 + 185);
                $('#divTBDis').height(divHeight);
                $('#subDivTBDis').height(divHeight - 72);
            }
            /**
             * 开始时间
             */
            $scope.openDateStart = function ($event) {
                $scope.butFlag = '';
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = true; //开始时间
                $scope.formRefer.openedEnd = false;
            };
            /**
             * 结束时间
             */
            $scope.openDateEnd = function ($event) {
                $scope.butFlag = '';
                $event.preventDefault();
                $event.stopPropagation();
                $scope.formRefer.openedStart = false;
                $scope.formRefer.openedEnd = true; //结束时间
            };
            /**
             * 进入个人项目投入页面
             */
            $scope.goPersonHoursProject = function goPersonHoursProject(account) {
                LocalCache.setObject('personHours_formRefer', $scope.formRefer);
                $state.go('app.office.personHoursProject', {
                    account: account,
                    startTime: $scope.formRefer.startTime,
                    endTime: $scope.formRefer.endTime,
                });
            };
            /*
             * 重置查询数据
             * */
            function reset() {
                $scope.formRefer = {};
                $scope.butFlag = '';
            }
            /**
             * 下载汇总信息
             */
            $scope.toExcel = function () {
                var urlData = {
                    employeeName: $scope.formRefer.personName,
                    orgCode: $scope.formRefer.department,
                    startTime: inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'), //开始时间
                    endTime: inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'), //结束时间
                };
                inform.modalInstance('确定要下载吗?').result.then(function () {
                    inform.downLoadFile('personHours/personHoursToExcel', urlData, '个人工时统计.xlsx');
                });
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
            /**
             * 事件选择器初始化
             * @param {*} scope
             * @param {*} gatherHoursInfoUnit
             * @param {*} type
             */
            function init(scope, gatherHoursInfoUnit, type) {
                gatherHoursInfoUnit = gatherHoursInfoUnit || '人年';
                type = type || '1';
                // 时间段选择部分
                scope.timeSelect = ['上月', '上周', '本周', '本月'];
                scope.initTime = function (m) {
                    initTime(scope, m);
                };
                scope.type = type;
                scope.unit = gatherHoursInfoUnit;
            }
            init($scope);
            // 时间段选择
            function initTime(scope, flag) {
                // 快捷键选项
                scope.butFlag = flag;
                let date = new Date();
                let y = date.getFullYear(); //当前年份
                // 上月
                let lastMonth = date.getMonth() - 1;
                let lastWeekday = date.getLastWeekday();
                if ('上月' === scope.butFlag) {
                    // 获取上月天数
                    let daysInMonth = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
                    scope.formRefer.startTime = inform.format(new Date(y, lastMonth, 1), 'yyyy-MM-01');
                    scope.formRefer.endTime = inform.format(new Date(y, lastMonth, daysInMonth), 'yyyy-MM-dd');
                }
                if ('上周' === scope.butFlag) {
                    // 上周的开始日期
                    scope.formRefer.startTime = inform.format(
                        new Date(y, lastWeekday.getMonth(), lastWeekday.getDate() - 7),
                        'yyyy-MM-dd'
                    );
                    //上周的结束日期
                    scope.formRefer.endTime = inform.format(
                        new Date(y, lastWeekday.getMonth(), lastWeekday.getDate() - 1),
                        'yyyy-MM-dd'
                    );
                }
                if ('本周' === scope.butFlag) {
                    // 本周开始时间
                    scope.formRefer.startTime = inform.format(
                        new Date(y, lastWeekday.getMonth(), lastWeekday.getDate()),
                        'yyyy-MM-dd'
                    );
                    scope.formRefer.endTime = inform.format(date, 'yyyy-MM-dd');
                }
                if ('本月' === scope.butFlag) {
                    scope.formRefer.startTime = inform.format(date, 'yyyy-MM-01');
                    scope.formRefer.endTime = inform.format(date, 'yyyy-MM-dd');
                }
            }
            /**
             * 点击任务视图后的跳转函数
             * @param {*} item
             */
            $scope.goTaskView = function (item) {
                $state.go('app.office.taskView', {
                    empId: null,
                    years: null,
                    quarter: null,
                    tagType: 17,
                    account: item.account,
                    startTime: inform.format($scope.formRefer.startTime, 'yyyy-MM-dd'),
                    endTime: inform.format($scope.formRefer.endTime, 'yyyy-MM-dd'),
                    name: item.employeeName,
                });
            };
        },
    ]);
})();

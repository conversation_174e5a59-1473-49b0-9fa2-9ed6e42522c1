
(function () {
    app.controller("repositoryAddController", ['comService','$rootScope', '$scope','$state','$stateParams','$modal','repositoryService','inform','Trans','AgreeConstant','LocalCache',
        function (comService,$rootScope, $scope,$state,$stateParams, $modal,repositoryService,inform,Trans,AgreeConstant,LocalCache) {

        /**
         * *************************************************************
         *             初始化部分                                 开始
         * *************************************************************
         */
    	//添加的仓库信息
    	$scope.formInsertrepository ={};
    	//添加的模块信息
    	$scope.formInsertmodule ={};
    	//更新后的仓库信息
    	$scope.formUprepository ={};
    	//更新后的模块信息
    	$scope.formUpmodule ={};
    	//用于判断是否已经点击新增仓库按钮
    	$scope.add=true;
    	//开发者列表
        $scope.team = [];
        //非开发人员列表
        $scope.teamNoDev = [];
        //保存模块原有人员工号
        var teBe = [];
        //保存模块原有人员工号和权限
        var perBefor = [];
    	//保存修改页面中所有模块以及其开发者的集合
    	$scope.moduleAndEmployeeList = [];
    	//添加仓库返回的仓库ID
    	$scope.repositoryreturnid="";
    	//添加模块返回的模块id
    	$scope.modulereturnid="";
		$scope.stateMap = {    //状态
				"00" : '正常',
				"01" : '离职',
				"02" : '调出'
			};
		$scope.limitedMap = {	//权限
				"0":"无",
				"1":"读",
				"2":"写"
			};
    	//初始化页面信息
    	initPages();
    	$scope.formRefer={};
    	$scope.formRefer.moduleName = '';
    	//根据仓库id回填仓库信息
    	if ($stateParams.repositoryid!=null){
    		getRepositoryById($stateParams.repositoryid);
    	}
    	$scope.repositoryid = $stateParams.repositoryid;
    	$scope.addRepository = addRepository;//添加仓库
    	$scope.addModule = addModule;//添加模块
    	$scope.saveRepository = saveRepository;//保存仓库修改信息
    	$scope.getModule = getModule;//根据id回填模块信息
    	$scope.saveModule = saveModule;//保存模块修改信息
    	$scope.getModulesAndEmployees = getModulesAndEmployees;
        /**
         * *************************************************************
         *              初始化部分                                 结束
         * *************************************************************
         */

        /**
         * *************************************************************
         *              方法声明部分                                 开始
         * *************************************************************
         */

        /**
    	 * 页面初始化
    	 */
    	function initPages() {
    		//获取地区
    		$scope.areaList = [];
    		comService.getParamList('AREA_TYPE','AREA_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.areaList =  data.data;
        		}
            });
    		//获取产品线
    		$scope.productLineList = [];
    		comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.productLineList =  data.data;
        			var other = {
        				'param_code':'0000',
        				'param_value':'无'
        			};
        			$scope.productLineList.push(other);
        		}
            });
    		//获取山东新北洋集团的下级部门信息
    		$scope.oneorgList = [];
    		comService.getOrgChildren('0002').then(function(data) {
        		if (data.data) {
        			$scope.oneorgList =  data.data;
        		}
            });
    		//获取员工信息
            $scope.employeeList = [];
            comService.getEmployeesByOrgId('').then(function(data) {
                if (data.data) {
                    $scope.employeeList = data.data;
                }
            });
            //获取部门
            $scope.orgList = [];
            comService.getOrgChildren('D010053').then(function(data) {
                if (data.data) {
                    $scope.orgList =  data.data;
                    var other = {
            			'orgCode':'0000',
            			'orgName':'其他'
            		};
            		$scope.orgList.push(other);
                }
            });
            //获取开发者类别
    		$scope.personTypes = [];
    		comService.getParamList('PERSON_TYPE','SVN_PERSON_TYPE').then(function(data) {
        		if (data.data) {
        			$scope.personTypes =  data.data;
        		}
            });
    	}

        /**
         * 人员的模糊查询：
         */
        $('#see').bind('input propertychange',function() {
            var name = $(this).val();
            if(name != null && name !== ""){
                var employee = [];
                var flag = false;
                //如果不包含name返回-1
                angular.forEach($scope.employeeRepositorysec, function(obj, i) {
                    flag = true;
                    if(obj.employeeName.indexOf(name) >= 0){
                        employee.push(obj);
                    }
                });
                if(flag){
                    $scope.employeeRepository=employee;
                    $scope.$apply();
                }
            } else {
                $scope.employeeRepository=$scope.employeeRepositorysec;
                $scope.$apply();
            }
        });
        
        /**
         * 新增一个开发人员
         */
        $scope.addNewBind = function () {

            //开发人员
            var person = {
                'employeeNo': '',
                'limited': '2',
                'state': '00',
                'actionFlag': '1',
                'type':'0001'
            };
            $scope.team.push(person);

            //将滚动条置于底部
            var div = document.getElementById('moduleUp');
            div.scrollTop = div.scrollHeight;
            var divAdd = document.getElementById('moduleAdd');
            divAdd.scrollTop = divAdd.scrollHeight;
        };
        
        /**
         * 取消一行
         */
        $scope.deleteNewBind = function (index) {
            if (index >= 0) {
                $scope.team.splice(index, 1);
            }
        };
        
        /**
         * 新增一个非开发人员
         */
        $scope.addNewBindNoDev = function () {

            //开发人员
            var person = {
                'employeeNo': '',
                'limited': '0',
                'state': '00',
                'actionFlag': '1',
                'type':'0002'
            };
            $scope.teamNoDev.push(person);

            //将滚动条置于底部
            var div = document.getElementById('NoDevModule');
            div.scrollTop = div.scrollHeight;
        };
        
        /**
         * 取消一行非开发人员
         */
        $scope.deleteNewBindNoDev = function (index) {
            if (index >= 0) {
                $scope.teamNoDev.splice(index, 1);
            }
        };
        
        /***********************************新增仓库***********************************/
    	
        /**
    	 * /新增仓库
    	 */
    	function addRepository() {
    	        $scope.repositoryreturnid="";
    	        var urlData={   //新增仓库信息
    	    			'repositoryname':$scope.formInsertrepository.repositoryname,//仓库名
    	    			'productline':$scope.formInsertrepository.productline,//产品线
    	    			'onedepartment':'',//一级所属部门
    	    			'department':$scope.formInsertrepository.department,//二级所属部门
    	    			'territory':$scope.formInsertrepository.territory,//地域
    	    			'projectname':$scope.formInsertrepository.projectname,//项目名
    	    			'projectmanager':$scope.formInsertrepository.projectmanager,//项目经理
    	    			'projectmanagrment':$scope.formInsertrepository.projectmanagrment,//项目管理员
    	    			'regenerator':LocalCache.getSession('userId')//更新者
    	    	};
    	    	var modalInstance = $modal.open({
    	    		templateUrl: 'myModalContent',
    	            controller: 'ModalInstanceCtrl',
    	            size: "sm",
    	            resolve: {
    	            	items: function() {
    	            		return "是否确定新增该仓库？";
    	            	}
    	            }
    	        });
    	    	modalInstance.result.then(function() {
    	    		repositoryService.addRepository(urlData).then(function(data) {
    	            	if (data.code === AgreeConstant.code) {
    	            		inform.common("添加仓库成功");
    	            		//返回新增仓库的id
    	            		$scope.repositoryreturnid = data.data;
    	            		//新增非开发人员管理模块
    	            		$scope.addNoDevMo();
    	            		//新增仓库的项目人员
    	            		addRepositoryEmployee($scope.employeeRepository,$scope.repositoryreturnid);
    	            		//使新增模块按钮可用 是新增仓库模块不可用
    	            		$scope.add=false;
    	    	        } else {
    	    	            inform.common(data.message);
    	    	        }
    	    		}, function(error) {
    	    			inform.common(Trans("tip.requestError"));
    	    		});
    	    	});
    	}
    	
    	/**
    	 * 新增非开发人员管理模块
    	 */
    	$scope.addNoDevMo = function(){
    		$scope.noDevMoid="";
	        	var urlData={  //新增模块信息
	        			'modulename':'~%@NoDevModule`52',//模块名
	        			'modulefunction':$scope.formInsertrepository.repositoryname+":~%@NoDevModule`52",//模块功能
	        			'regenerator':LocalCache.getSession('userId')//更新者
				};
	        repositoryService.addModule(urlData).then(function(data) {
	        	if (data.code === AgreeConstant.code) {
      				$scope.noDevMoid = data.data;
      				//新增模块与仓库的关系
      				addRepositoryModule($scope.noDevMoid,$scope.repositoryreturnid);
	        	} else {
      				inform.common(data.message);
      			}
      		}, function(error) {
      			inform.common(Trans("tip.requestError"));
      		});
    	};
    	
    	/**
         * 新增人员与仓库的关系
         */
       	function addRepositoryEmployee(employee,repositoryid) {
       		angular.forEach(employee, function(obj, i) {
       			var urlData={
          			'repositoryid':repositoryid,//仓库id
          			'employeeno':obj.employeeNo,//员工id
          			'limited':obj.limited,//员工权限
          			'type':obj.type,//员工权限
          			'state':obj.state//员工状态
          		};     
       			repositoryService.addRepositoryEmployee(urlData).then(function(data) {
       				callbackFunction(data);
       			}, function(error) {
       				inform.common(Trans("tip.requestError"));
       			});
       		});
    	}
    	
    	/***********************************回填仓库***********************************/  	
    	
    	/**
         * 根据ID 获取仓库信息
         */
        function getRepositoryById(repositoryid) {
        	$scope.employeeRepositoryUpAdd=[];//修改时需要添加的人员
        	repositoryService.getRepositoryById(repositoryid).then(function(data) {
                 if (data.code === AgreeConstant.code) {
                      $scope.formUprepository = angular.fromJson(data.data);
                      	if(null ==$scope.formUprepository.productline){
                      		//默认产品线为“无”
                      		$scope.formUprepository.productline = "0000";
              			}
              			if(null ==$scope.formUprepository.department){
              				//默认所属部门为其他
              				$scope.formUprepository.department = "0000";
              			}
              		//根据id获取仓库模块以及开发者信息
                      getModulesAndEmployees(repositoryid);
                      //根据id获取仓库的人员信息
                      getRepositoryEmployee(repositoryid);
                      //获取默认模块ID
                      getDefaultModuleId(repositoryid);
                 } else {
                       inform.common(data.message);
                 }
             }, function() {
                 inform.common(Trans("tip.requestError"));
             });
         }
        /**
         * 获取默认模块ID
         */
        function getDefaultModuleId(repositoryid){
        	 repositoryService.getDefaultModuleId(repositoryid).then(function(data) {
        		 if (data.code === AgreeConstant.code) {
      		   		//默认模块ID
      		   		$scope.defaultModuleId = data.data;
                 } else {
                  	 inform.common(data.message);
                 }
        	 }, function() {
                 inform.common(Trans("tip.requestError"));
            });
        }
        /**
         *  根据id获取仓库模块以及开发者信息
         */
        function getModulesAndEmployees(repositoryid) {
     	   $scope.moduleAndEmployeeList = [];
     	   repositoryService.getModulesAndEmployees(repositoryid,$scope.formRefer.moduleName).then(function(data) {
     		   	if (data.code === AgreeConstant.code) {
     		   		//修改页面下模块的人员信息
     		   		$scope.moduleAndEmployeeList = angular.fromJson(data.data);
                } else {
                 	 inform.common(data.message);
                }
             }, function() {
                  inform.common(Trans("tip.requestError"));
             });
        }
        
        /**
    	 *  根据ID 获取仓库的人员信息
    	 */
       	function getRepositoryEmployee(repositoryid) {
       		$scope.employeeRepository = [];//修改页面下仓库的人员信息
       		$scope.employeeRepositorysec = [];
       		repositoryService.getRepositoryEmployee(repositoryid).then(function(data) {
                 if (data.code === AgreeConstant.code) {
                	 $scope.employeeRepository = angular.fromJson(data.data);
                	 $scope.employeeRepositorysec = $scope.employeeRepository;
                	 angular.forEach($scope.employeeRepository, function(employee, i) {//遍历人员
                		 if(employee.state === "00"){
                			 employee.state="正常";
                		 } else if(employee.state === "01"){
                			 employee.state="离职";
                		 } else if(employee.state === "02"){
                			 employee.state="调出";
                		 }
                		 angular.forEach($scope.personTypes, function(type) {//遍历人员类别
                    		 if(type.param_code === employee.type){
                    			 employee.type=type.param_value;
                    		 } 
                    	 });
                	 });
                	 
                 } else {
                    inform.common(data.message);
                 }
            }, function() {
           		inform.common(Trans("tip.requestError"));
            });
       	}
       	
        /***********************************更新仓库***********************************/
        
       	/**
      	 *  保存仓库信息修改
      	 */
      	function saveRepository() {
 	        	var urlData={
      				'id':$scope.formUprepository.id, //仓库id
      				'repositoryname':$scope.formUprepository.repositoryname,//仓库名
      				'productline':$scope.formUprepository.productline,//产品线
      				'onedepartment':'',//一级所属部门
      				'department':$scope.formUprepository.department,//二级所属部门
      				'territory':$scope.formUprepository.territory,//地域
      				'projectname':$scope.formUprepository.projectname,//项目名
      				'projectmanager':$scope.formUprepository.projectmanagerid,//项目经理
      				'projectmanagrment':$scope.formUprepository.projectmanagrmentid,//项目管理员
      				'regenerator':LocalCache.getSession('userId')//更新者
      			};
 	        	var modalInstance = $modal.open({
 	        		templateUrl: 'myModalContent',
 	        		controller: 'ModalInstanceCtrl',
 	        		size: "sm",
 	        		resolve: {
 	        			items: function() {
 	        				return "是否确定修改该仓库？";
 	        			}
 	        		}
 	        	});
 	        	modalInstance.result.then(function() {
 	        		repositoryService.saveRepository(urlData).then(function(data) {
 	        			if (data.code === AgreeConstant.code) {
                        	inform.common("修改仓库信息成功");
                        	//保存原先人员的权限信息
                        	upRepositoryEmployee($scope.employeeRepository,$scope.formUprepository.id);
                        	//添加新增的项目人员
                        	addRepositoryEmployee($scope.employeeRepositoryUpAdd,$scope.formUprepository.id);
                        	$scope.employeeRepositoryUpAdd=[];
 	        			} else {
 	        				inform.common(data.message);
 	        			}
 	        		}, function() {
                      inform.common(Trans("tip.requestError"));
 	        		});
 	        	});
         }
      	/**
         * 保存仓库的人员权限
         */
       	function upRepositoryEmployee(employee,repositoryid) {
       		angular.forEach(employee, function(obj, i) {
       			var urlData={
          			'repositoryid':repositoryid,//仓库id
          			'employeeno':obj.employeeNo,//员工id
          			'limited':obj.limited,//员工权限
          			'type':obj.type//员工类别
          		};     
       			repositoryService.upRepositoryEmployee(urlData).then(function(data) {
       				callbackFunction(data);
       			}, function(error) {
       				inform.common(Trans("tip.requestError"));
       			});
       		});
    	}

    	function callbackFunction(data){
    	    if (data.code !== AgreeConstant.code) {
                inform.common(data.message);
            }
    	}
      	
    	/***********************************新增模块***********************************/

    	/**
    	 * 新增模块信息判断
    	 */
    	function addModule(repositoryid) {
    		if ($scope.team.length === 0){
          		inform.common("请选择开发者");
          		return;
          	 }
    		//循环人员，查看是否有重复信息
         	 var list = []; 
         	 $scope.flagList = true;
         	 angular.forEach($scope.team, function (one, index) {
         		 setList(list,one);
         	 });
         	 if (!$scope.flagList){
         		 var mains = $scope.falseNum;
         		 //将员工工号值转名字进行提示
         		 traversalEmployeeList(mains,$scope.employeeList);
         		 inform.common("开发者中"+mains+"重复,请修正。");
         		 return;
         	 }
         	 //实际新增模块
  	         addModuleInfo(repositoryid);
    	}
        //将员工工号值转名字进行提示
    	function traversalEmployeeList(mains,employeeList){
    	   angular.forEach(employeeList, function (one, index) {
             if (one.employeeNo === mains){
                mains = one.realName;
             }
           });
    	}

    	function setList(list,one){
    	     var num = list.indexOf(one.employeeNo);
             if (num > -1){
                $scope.falseNum = one.employeeNo;
                $scope.flagList = false;
                return;
             }
             list.push(one.employeeNo);

    	}
    	
    	/**
    	 * 新增模块
    	 */
    	function addModuleInfo(repositoryid){
    		$scope.modulereturnid="";
	        	var urlData={  //新增模块信息
				'modulename':$scope.formInsertmodule.modulename,//模块名
				'modulefunction':$scope.formInsertmodule.modulefunction,//模块功能
				'relevantmodule':$scope.formInsertmodule.relevantmodule,//相关联模块
				'regenerator':LocalCache.getSession('userId')};//更新者
	        	$("#add_Module").modal('hide');
	        	var modalInstance = $modal.open({
	        		templateUrl: 'myModalContent',
	        		controller: 'ModalInstanceCtrl',
	        		size: "sm",
	        		resolve: {
	        			items: function() {
	        				return "是否确定新增该模块？";
	        			}
	        		}
	        	});
	        	modalInstance.result.then(function() {
	        		repositoryService.addModule(urlData).then(function(data) {
	        			if (data.code === AgreeConstant.code) {
	        				inform.common("新增模块信息成功");
	        				$scope.modulereturnid = data.data;
	        				//新增模块与仓库的关系
	        				addRepositoryModule($scope.modulereturnid,repositoryid);
	        				setTimeout(function() {
	  	        				//根据id获取仓库信息
	  	        				getRepositoryById(repositoryid); //lazy load   
	        				}, 500);
	        				//新增模块与人员关系
	        				angular.forEach($scope.team, function(obj, i) {
	        					addModuleEmployee(obj,$scope.modulereturnid,LocalCache.getSession('userId'));
	        				});
	        			} else {
	        				inform.common(data.message);
	        			}
	        		}, function(error) {
	        			inform.common(Trans("tip.requestError"));
	        		});
	        	});
    	}
    	
    	/**
     	 *  新增模块与仓库的关系
     	 */
     	function addRepositoryModule(moudleid,repositoryid) {
     		repositoryService.addRepositoryModule(moudleid,repositoryid).then(function(data) {
     			callbackFunction(data);
            }, function() {
               inform.common(Trans("tip.requestError"));
            });
        }
     	
     	/**
     	 * 新增人员与模块的关系
     	 */
        function addModuleEmployee(employee,moduleid,userId) {
        		var urlData={
          			'moduleid':moduleid,//模块id
          			'employeeno':employee.employeeNo,//员工id
          			'limited':employee.limited,//员工权限
          			'type':employee.type,//员工类别
          			'state':employee.state//员工状态
          		}; 
        		repositoryService.addModuleEmployee(urlData).then(function(data) {
        			if (data.code === AgreeConstant.code) {
        				//添加人员与模块的关系进入历史变动表
        				addModuleEmployeeChange(employee.employeeNo,moduleid,userId,'00')
        			} else {
        				inform.common(data.message);
    	            }
        		}, function(error) {
        			inform.common(Trans("tip.requestError"));
        		});
     	}
     	
     	/**
     	 * 新增人员与模块的关系到变动表
     	 */
        function addModuleEmployeeChange(employeeNo,moduleid,userId,state) {
        	//在修改时 也添加变动信息 需要传递权限信息 所以用‘--’用于区分
        	var emp = [];
        	//若存在 说明时修改：员工号--新权限
        	if (employeeNo.indexOf('--')>-1){
        		emp = employeeNo.split('--');
        		employeeNo = emp[0];
        	}
        	var urlData={
        			'mouduleId':moduleid,
    				'employeeNo':employeeNo,
    				'state':state?state:'00',
    				'updateEmployeename':userId
    		};
    		//如果是修改 还需要新增备注信息
        	if (emp.length > 1){
        		var per=[];
        		//查看该员工原始权限是什么
       			angular.forEach(perBefor, function(person, i) {//遍历原有人员与权限
    				per = person.split('--');
    				//该员工权限有变 添加备注信息
    				if (per[0] === employeeNo && per[1] !== emp[1]){
    					urlData.remark = $scope.formUpmodule.modulename+'模块：由\"'+$scope.limitedMap[per[1]]+'\"权限修改为\"'+$scope.limitedMap[emp[1]]+'\"权限';
    				}
       			});
        	}
        	repositoryService.addModuleEmployeeChange(urlData).then(function(data) {
        	    callbackFunction(data);

        	}, function(error) {
        		inform.common(Trans("tip.requestError"));
        	});
     	}
     	
     	/***********************************回填模块***********************************/
    	 
     	/**
      	 * 根据id获取模块信息
      	 */
      	function getModule(moudleid) {
      		var id = "";
      		if (moudleid === "noDev"){
      			id = $scope.formInsertrepository.repositoryname+":~%@NoDevModule`52";
      		}else if (moudleid === "noDevUp"){
      			id = $scope.defaultModuleId;
      		}else {
      			id = moudleid;
      		}
      		$scope.employeeModuleUpAdd=[];//修改时需要添加的人员
      		repositoryService.getModule(id).then(function(data) {
                if (data.code === AgreeConstant.code) {
                   $scope.formUpmodule = angular.fromJson(data.data);
                   //根据模块id或全部模块的人员信息
                   getModuleEmployee(moudleid,id);
                } else {
                   inform.common(data.message);
                }
             }, function() {
                inform.common(Trans("tip.requestError"));
             });
         }
      	
      	/**
         * / 根据ID 获取模块的人员信息
         */
        function getModuleEmployee(moudleid,id) {
     	   $scope.team = [];//修改页面下模块的人员信息
     	   teBe = [];//模块的原有人员
     	   perBefor = [];//模块原有人员权限
     	   repositoryService.getModuleEmployee(id).then(function(data) {
     		   if (data.code === AgreeConstant.code) {
     			   var teamnodev = [];
     			   if (moudleid === "noDev"||moudleid === "noDevUp"){
     				   $scope.teamNoDev = angular.fromJson(data.data);
     				   teamnodev = $scope.teamNoDev;
     	      		}else{
     	      			$scope.team = angular.fromJson(data.data);
     	      			teamnodev = $scope.team;
     	      		}
                		angular.forEach(teamnodev, function(employee, i) {//遍历人员
                			employee.actionFlag = "0";//0 :来源数据库    1 :页面选择
                			teBe.push(employee.employeeNo);
                			//如果没有权限信息 下拉框显示无信息
                			if(null ==employee.limited){
                				employee.limited = '0';
                			}
                			perBefor.push(employee.employeeNo+"--"+employee.limited);
                		});
                  } else {
                 	 inform.common(data.message);
                  }
             }, function() {
                  inform.common(Trans("tip.requestError"));
             });
        }
        
        /***********************************更新模块***********************************/
    	
        /**
      	 * 保存模块信息修改判断
      	 */
      	function saveModule(repositoryid,flag) {
      		var team = [];
      		if (null==flag){
      			team = $scope.team;
      		} else {
      			team = $scope.teamNoDev;
      		}
      		if (team.length === 0){
          		inform.common("请选择人员");
          		return;
          	 }
    		//循环人员，查看是否有重复信息
         	 var list = []; 
         	 $scope.flagList = true;
         	 angular.forEach(team, function (one, index) {
         		 setList(list,one);
         	 });
         	 if (!$scope.flagList){
         		 var mains = $scope.falseNum;
         		 //将员工工号值转名字进行提示
         		 traversalEmployeeList(mains,$scope.employeeList);
         		 inform.common("人员中"+mains+"重复,请修正。");
         		 return;
         	 }
   	         //实际更新模块信息
         	 saveModuleInfo(repositoryid,flag,team);
      	}
      	
      	/**
      	 * 更新模块信息
      	 */
      	function saveModuleInfo(repositoryid,flag,team){
      		var urlData={  //更新模块信息
   	        		'id':$scope.formUpmodule.id,//模块ID
   	        		'modulename':$scope.formUpmodule.modulename,//模块名
   	        		'modulefunction':$scope.formUpmodule.modulefunction,//模块功能
   	        		'relevantmodule':$scope.formUpmodule.relevantmodule,//相关联模块
   	        		'regenerator':LocalCache.getSession('userId')//更新者
   	        	};
   	        	$("#edit_Module").modal('hide');
   	        	$("#NoDev_Module").modal('hide');
   	        	var modalInstance = $modal.open({
   	        		templateUrl: 'myModalContent',
   	        		controller: 'ModalInstanceCtrl',
   	        		size: "sm",
   	        		resolve: {
   	        			items: function() {
   	        				return "是否确定修改该模块？";
   	        			}
   	        		}
   	        	});
   	        	modalInstance.result.then(function() {
   	        		repositoryService.saveModule(urlData).then(function(data) {
   	        			if (data.code === AgreeConstant.code) {
   	        				inform.common("修改模块信息成功");
   	        				//保存数据库既存数据
   	        				var dbData = [];
	   	                	 angular.forEach(team, function(employee, i) {//遍历人员
	   	                		 if(employee.actionFlag !== "1"){
	   	                			dbData.push(employee);
	   	                		 }
	   	                	 });
   	        				//保存模块的人员修改的权限
   	        				upModuleEmployee(dbData,$scope.formUpmodule.id);
   	        				//添加模块与人员的关系
   	        				$scope.changeEm(flag);
   	        				//获得仓库详细属性
   	        				getRepositoryById(repositoryid); 
   	        			} else {
   	        				inform.common(data.message);
   	        			}
   	        		}, function() {
   	        			inform.common(Trans("tip.requestError"));
   	        		});
   	        	});
      	}
      	
      	/**
         * 保存模块的人员权限
         */
       	function upModuleEmployee(employee,moduleid) {
       		angular.forEach(employee, function(obj, i) {
       			var urlData={
          			'moduleid':moduleid,//模块id
          			'employeeno':obj.employeeNo,//员工id
          			'limited':obj.limited,//员工权限
          			'type':obj.type//员工类别
          		};     
       			repositoryService.upModuleEmployee(urlData).then(function(data) {
       				if (data.code === AgreeConstant.code) {
       					var per=[];
       	       			angular.forEach(perBefor, function(person) {//遍历原有人员与权限
       	    				per = person.split('--');
       	    				//是原先的人员 且 有权限修改信息
       	    				if (per[0] === obj.employeeNo && per[1] !== obj.limited){
       	    					//添加人员与模块的关系进入历史变动表
       	        				addModuleEmployeeChange(obj.employeeNo+"--"+obj.limited,moduleid,LocalCache.getSession('userId'),'04')
       	    				}
       	       			});
       				} else {
       					inform.common(data.message);
	                }
       			}, function(error) {
       				inform.common(Trans("tip.requestError"));
       			});
       		});
    	}
    	
      	/**
     	 * 当人员改变时，删除原来人员新增新的人员
     	 */
      	$scope.changeEm = function(flag){
      		var team = [];
      		if (null==flag){
      			team = $scope.team;
      		} else {
      			team = $scope.teamNoDev;
      		}
      		var num = [];
      		//遍历现在的人员 如果以前的人员中不存在该人员 则新增
         	angular.forEach(team, function (one, index) {
        		if (teBe.indexOf(one.employeeNo) === -1){
        			//新增模块与人员关系
      				addModuleEmployee(one,$scope.formUpmodule.id,LocalCache.getSession('userId'));
       		 	}
        		num.push(one.employeeNo);
       	  	});
         	//遍历以前的人员 如果现在的人员中不存在该人员 则删除
         	angular.forEach(teBe, function (one, index) {
        		if (num.indexOf(one) === -1){
        			//删除数据库里的数据
               		deleteModuleEmployee(one,$scope.formUpmodule.id,LocalCache.getSession('userId'));
       		 	}
       	  	});
      	};

      	/**
     	 * 删除人员与模块的关系
     	 */
        function deleteModuleEmployee(employee,moduleid,userId) {
        	
    		var urlData={
      			'moduleid':moduleid,//模块id
      			'employeeno':employee//员工id
      		}; 
    		repositoryService.deleteModuleEmployee(urlData).then(function(data) {
    			if (data.code === AgreeConstant.code) {
    				//添加人员与模块的关系进入历史变动表
    				//当前状态 00-新增 01-离职 02-调出  03-删除
    				addModuleEmployeeChange(employee,moduleid,userId,'03');
    			} else {
    				inform.common(data.message);
	            }
    		}, function(error) {
    			inform.common(Trans("tip.requestError"));
    		});
     	}
     	
	}]);
})();
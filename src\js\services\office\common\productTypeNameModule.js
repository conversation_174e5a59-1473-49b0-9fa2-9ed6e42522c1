(function() {
    'use strict';
  app.factory('productTypeNameModule', productTypeNameModule);
  productTypeNameModule.$inject=["comService","projectManagementService","AgreeConstant","inform","Trans"];

  function productTypeNameModule(comService,projectManagementService,AgreeConstant,inform,Trans){
    var service={
		 initModule:initModule
    };
    return service;

    //回调方法
    var callBackFun = null;
    //scope
    var moduleScope = null;
    //初始化传入的参数
    var initParams = [];
    /**
     * 初始化产品类别名称弹框
     *  参数结构
     * dataInfo= {
                    productLine:'产品线id',
                    productType:'产品类别',
                    productName:'产品名称'
                    }
     */
    function initModule(dataInfo,scope,callback) {

        scope.selectProductLineList=selectProductLineList;
        scope.selectProductName = selectProductName;
        scope.search = search;
        initParams = dataInfo;
    	//查产品线
		scope.productLines = [];
        //回调方法
        callBackFun = callback;
        moduleScope = scope;
        moduleScope.productLine = "";
		//获取产品线-产品类别-产品名称集合
        moduleScope.productLineAndType = [];
        moduleScope.productLineAndTypeMap = {};
        moduleScope.productLineAndTypeValueMap = {};
        comService.getParamList('PRODUCT_TYPE','').then(function (data) {
			if (data.data) {
				moduleScope.productLineAndType = data.data;
                angular.forEach(moduleScope.productLineAndType,function (item) {
				    moduleScope.productLineAndTypeMap[item["param_code"]] = item["param_value"];
				    moduleScope.productLineAndTypeValueMap[item["param_value"]] = item["param_code"];
			    });
			    moduleScope.productTypeValue = moduleScope.productLineAndTypeMap[dataInfo.productType];
			    moduleScope.productNameValue = moduleScope.productLineAndTypeMap[dataInfo.productName];
			}
		});
        //初始化产品线
        comService.getParamList('PRODUCT_TYPE','PRODUCT_TYPE').then(function (data) {
			if (data.data) {

			    var productLineParam = dataInfo.productLine;
			    if(!productLineParam) {
			        productLineParam = data.data[0].param_code;
			    }
			    scope.productLines=data.data;
			    //初始化产品类型
			    selectProductLineList(productLineParam);
			    //显示弹框
			    $("#productTypeName").click();
			}
		});
    }

    /**
     * 获取产品线下所有的产品类别
     */
     function selectProductLineList(productLine){
        moduleScope.productLine = productLine;
        //修改被选中的字体颜色
        moduleScope.productLines.forEach(function (ele) {ele.active=ele.param_code===productLine;})
		//产品类型
		moduleScope.productTypes = [];
        //获取产品类型及产品名称
        projectManagementService.getProductTypeNameList(productLine).then(function (data) {
			if (data.code===AgreeConstant.code) {
                moduleScope.productTypes = data.data;
            } else {
                inform.common(data.message);
            }
		});
     }
     //点击查询按钮
     function search(){

     }
    /**
     * 选中产品名称触发的事件
     */
     function selectProductName(productType,productName){
            var paramInfo={
                'productLine':moduleScope.productLine,
                'productType':productType.code,
                'productName':productName.code
            }
            $("#productTypeNameModule").modal("hide");
            callBackFun(paramInfo);
     }
  }
})();
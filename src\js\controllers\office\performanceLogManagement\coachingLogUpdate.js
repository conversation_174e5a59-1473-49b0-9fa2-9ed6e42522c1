(function () {
    app.controller('coachingLogUpdate', [
        'comService',
        'performanceLogManagementService',
        '$rootScope',
        '$scope',
        '$state',
        '$stateParams',
        '$modal',
        'inform',
        'Trans',
        'AgreeConstant',
        'LocalCache',
        '$http',
        '$timeout',
        function (
            comService,
            performanceLogManagementService,
            $rootScope,
            $scope,
            $state,
            $stateParams,
            $modal,
            inform,
            Trans,
            AgreeConstant,
            LocalCache,
            $http,
            $timeout
        ) {
            /**
             * *************************************************************
             *              初始化部分                                 开始
             * *************************************************************
             */
            // 表单初始化
            $scope.formRefer = {
                // 被考核人域账号（员工姓名）
                assessedAccount: '',
                // 记录类型
                recordType: '',
                // 考核周期
                assessmentCycle: '',
                // 辅导日期
                communicateDate: '',
                // 沟通人账号
                communicateAccount: LocalCache.getSession('currentUserName'),
                // 所需资源
                resourceRequirement: '',
                // 成绩和个人成长
                personalGrowth: '',
                // 不足及改进点
                lackAndImprove: '',
                // 反馈、建议、答复情况
                feedbackAdvise: '',
                // 结果认同
                resultAgree: '',
            };
            // 改进目标及评价
            $scope.performanceTargetDtoList = [];
            $scope.isExpanded = true;
            $scope.zentaoTaskList = [];
            let dataTable;
            // dataTable插件，用于处理展开树状结构
            let treeGrid;
            // 确认是否为仅查看
            const type = $state.params.type;
            if (type === 'add' || type === 'edit') {
                $scope.isEditable = true;
            } else {
                $scope.isEditable = false;
            }
            const typeList = {
                add: '新增',
                edit: '编辑',
                view: '查看',
            };
            $scope.showType = typeList[type];
            // 部门对应的下拉列表
            $scope.departmentList = [];
            // 员工对应的下拉列表
            $scope.employeeList = [];
            // （绩效沟通）记录类型对应的下拉选项
            $scope.logType = ['日常绩效辅导', '低绩效改进', '述职改进', '试用期考核'];
            // 导入上期未达成目标的按钮
            $scope.isImportButtonDestroy = false;
            // 正则校验配置
            $scope.limitList = AgreeConstant.limitList;
            /**
       * *************************************************************
       *              初始化部分                                 结束
       * *************************************************************

       */

            /**
             * *********************************************************
             *              方法声明部分                                 开始
             * *********************************************************
             */
            /**
             * 处理接口返回的数据
             * @returns {Array} 处理之后的禅道任务树状数组
             */
            const handleZentaoTaskList = (data) => {
                const map = {
                    childTaskId: 'taskId',
                    childTaskName: 'taskName',
                    childTaskStatus: 'taskStatus',
                    childConsumedHours: 'consumedHours',
                    childPlanFinishedDate: 'planFinishedDate',
                    childActualFinishedDate: 'actualFinishedDate',
                };
                const res = data.map((i) => {
                    if (!i.children || i.children.length === 0) {
                        return i;
                    }
                    // 替换掉children里的变量名，将其和一级菜单保持一致
                    const child = i.children.map((j) => {
                        for (const key in j) {
                            const keyMap = map[key];
                            if (keyMap) {
                                j[keyMap] = j[key];
                            }
                        }
                        return j;
                    });
                    i.children = child;
                    return i;
                });
                return res;
            };

            // 获取全部表单数据
            const getFormData = async () => {
                try {
                    const params = {
                        communicateId: $state.params.id,
                    };
                    const data = await performanceLogManagementService.getPerformanceLogDetailById(params);
                    if (data.data) {
                        const { performanceTargetDtoList, performanceCommFatherTaskInfo, ...rest } = data.data;
                        $scope.performanceTargetDtoList = performanceTargetDtoList;
                        $scope.zentaoTaskList = handleZentaoTaskList(performanceCommFatherTaskInfo);
                        $scope.formRefer = rest;
                        $timeout(() => getDataTable());
                    }
                } catch (error) {
                    console.error(error, '获取表单数据失败');
                }
            };

            //获取二级部门对应的员工姓名
            const getEmployeeByDepartment = async () => {
                try {
                    const data = await comService.getProUser('');
                    if (data.data) {
                        $scope.employeeList = data.data;
                    }
                } catch (error) {
                    console.error(error, '获取员工数据失败');
                }
            };
            // 返回按钮
            $scope.goBack = () => {
                history.go(-1);
            };
            // 定义一个函数来获取列定义
            const getColumns = () => {
                const baseColumns = [
                    { data: 'taskId', title: '禅道任务ID', width: '10%' },
                    { data: 'taskName', title: '禅道任务名称', width: '30%' },
                    { data: 'taskStatus', title: '任务状态', width: '10%' },
                    { data: 'consumedHours', title: '消耗（H）', width: '7%' },
                    { data: 'planFinishedDate', title: '计划完成时间', width: '10%' },
                    { data: 'actualFinishedDate', title: '实际完成时间', width: '10%' },
                ];

                if ($scope.isEditable) {
                    baseColumns.push({
                        data: (data) => {
                            const closeButton = `<a class="performance-log-management-link" style="margin-right: 10px">关闭</a>`;
                            const activeButton = `<a class="performance-log-management-link">激活</a>`;
                            let res = '';
                            if (data.taskStatus === '已完成') {
                                res += closeButton;
                            }
                            if (data.taskStatus !== '进行中' && data.taskStatus !== '未开始') {
                                res += activeButton;
                            }
                            return res;
                        },
                        title: '操作',
                        width: '10%',
                    });
                }
                return baseColumns;
            };

            // 修改后的getDataTable函数
            const getDataTable = () => {
                // 如果表格已存在，先销毁
                if (dataTable) {
                    dataTable.destroy();
                    $('#treeTable').empty();
                }

                const columns = getColumns();

                dataTable = $('#treeTable').DataTable({
                    dom: 'tr',
                    ordering: false,
                    processing: true,
                    data: $scope.zentaoTaskList,
                    language: {
                        emptyTable: '暂无数据',
                    },
                    columns,
                });
                // dataTable插件，用于处理展开树状结构
                // 插件位置：src\library\jquery\datatables\plugins\treeGrid.js
                treeGrid = $.fn.dataTable.treeGrid(dataTable, {
                    treeColumn: 1,
                    expandAll: true,
                });

                treeGrid.expandAll();
                const treeTableListener = (dataTable) => {
                    $('#treeTable').on('click', '.performance-log-management-link', function () {
                        const data = dataTable.row($(this).closest('tr')).data();
                        const action = $(this).text();
                        if (action === '关闭') {
                            handleTaskClose(data);
                        } else if (action === '激活') {
                            handleTaskActivate(data);
                        }
                    });
                };
                treeTableListener(dataTable);
            };

            // 在$scope.zentaoTaskList变化时调用
            $scope.$watch('zentaoTaskList', function (newVal, oldVal) {
                getDataTable();
            });

            /**
             * 初始化页面
             */
            async function init() {
                getEmployeeByDepartment();
                if ($state.params.id) {
                    getFormData();
                } else {
                    getDataTable();
                }
            }
            init();
            // 关闭禅道任务
            const handleTaskClose = async (data) => {
                if (!$scope.isEditable) {
                    return;
                }
                try {
                    const params = {
                        proTaskId: data.taskId,
                        communicateId: $scope.formRefer.id,
                    };
                    const res = await performanceLogManagementService.closeZentaoTask(params);
                    if (res.code !== '0000') {
                        inform.common(res.message);
                        return;
                    }
                    inform.common('关闭成功');
                    getFormData();
                } catch (error) {
                    console.error(error, '关闭失败');
                }
            };
            // 激活禅道任务
            const handleTaskActivate = async (data) => {
                if (!$scope.isEditable) {
                    return;
                }
                try {
                    const params = {
                        proTaskId: data.taskId,
                        assignedTo: $scope.formRefer.assessedAccount,
                        communicateId: $scope.formRefer.id,
                    };
                    const res = await performanceLogManagementService.activeZentaoTask(params);
                    if (res.code !== '0000') {
                        inform.common(res.message);
                        return;
                    }
                    inform.common('激活成功');
                    getFormData();
                } catch (error) {
                    console.error(error, '激活失败');
                }
            };
            // 处理禅道任务展开/折叠
            $scope.handleCollapse = () => {
                $scope.isExpanded = !$scope.isExpanded;
                if ($scope.isExpanded) {
                    treeGrid.expandAll();
                } else {
                    treeGrid.collapseAll();
                }
            };
            // 增加改进目标
            $scope.addToBeImporvedGoal = () => {
                $scope.performanceTargetDtoList.push({
                    // 绩效目标类型
                    targetType: $scope.formRefer.recordType,
                    // 绩效目标
                    targetVal: '',
                    // 关键结果
                    keyResult: '',
                    // 计划完成时间
                    planEndDate: '',
                    //预计工时
                    plannedTime: '',
                    // 实际完成时间
                    endDate: null,
                    // 状态
                    targetStatus: '未达成',
                    // 评价
                    evaluateVal: '',
                });
            };
            // 删除改进目标及评价
            $scope.deleteGoal = async (index) => {
                //自定义弹出框
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: 'sm',
                    resolve: {
                        items: function () {
                            return '确认删除此记录吗？';
                        },
                    },
                });
                // 确认删除之后的处理
                await modalInstance.result;
                $scope.performanceTargetDtoList.splice(index, 1);
            };
            /**
             * 保存
             */
            $scope.save = async (type) => {
                $scope.performanceTargetDtoList.forEach((i) => (i.targetType = $scope.formRefer.recordType));
                try {
                    const typeMap = {
                        save: 0,
                        saveAndCreateTask: 1,
                    };
                    const saveMethod = typeMap[type];
                    const params = {
                        ...$scope.formRefer,
                        saveMethod,
                        performanceTargetDtoList: $scope.performanceTargetDtoList,
                    };
                    const res = await performanceLogManagementService.savePerformanceLogDetail(params);
                    if (res.code === '0000') {
                        inform.common('保存成功');
                        $state.go('app.office.performanceLogManagement');
                    } else {
                        inform.common(res.message);
                    }
                } catch (error) {
                    console.error(error, '保存失败');
                }
            };
            /**
             * 导入上期未达成目标
             */
            $scope.importUnfinishedGoal = async () => {
                if (!$scope.formRefer.assessedAccount) {
                    inform.common('请选择被考核人');
                    return;
                }
                if (!$scope.formRefer.communicateDate) {
                    inform.common('请选择辅导日期');
                    return;
                }
                if (!$scope.formRefer.recordType) {
                    inform.common('请选择记录类型');
                    return;
                }
                const id = $state.params.id ? Number($state.params.id) : null;
                try {
                    const params = {
                        id,
                        userAccount: $scope.formRefer.assessedAccount,
                        communicateDate: $scope.formRefer.communicateDate,
                        recordType: $scope.formRefer.recordType,
                    };
                    const res = await performanceLogManagementService.getCoachingUnfinishedTask(params);
                    if (res.code === '0000') {
                        $scope.isImportButtonDestroy = true;
                        $scope.$apply();
                        if (res.data.length > 0) {
                            $scope.performanceTargetDtoList = [...$scope.performanceTargetDtoList, ...res.data];
                            $scope.$apply();
                            inform.common('导入成功');
                        } else {
                            inform.common('无上期未达成目标');
                        }
                    } else {
                        inform.common(res.message);
                    }
                } catch (error) {
                    console.error(error, '获取上期未达成目标失败');
                }
            };
            /**
             * *************************************************************
             *              方法声明部分                                 结束
             * *************************************************************
             */
        },
    ]);
})();

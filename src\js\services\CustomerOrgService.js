(function() {
    'use strict';
    app
        .factory('CustomerOrgService', customerOrgService);
    customerOrgService.$inject = ['HttpService', '$rootScope'];

    function customerOrgService(HttpService, $rootScope) {
        var service = {
            getCookie: getCookie,
            getAll: getAll,
            getSubOrg:getSubOrg,
            saveOrupdateOrganization:saveOrupdateOrganization,
            delOrg:delOrg
        };
        return service;

        function getCookie(name) {
            var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");

            if (arr = document.cookie.match(reg))

                return unescape(arr[2]);
            else
                return null;
        }

        function getAll() {
            return HttpService.post($rootScope.gateInfoApi + 'lms/customerOrg/getAllCustomerOrg?&name=' + getCookie('name'),{});
        }

        function getSubOrg(orgId) {
            return HttpService.post($rootScope.gateInfoApi + 'lms/customerOrg/getCustomerOrgAndChildById?customerOrgId='+orgId+'&name=' + getCookie('name'),{});
        }
        function saveOrupdateOrganization(perData) {

            return HttpService.post($rootScope.gateInfoApi + 'lms/customerOrg/saveOrUpdateOrg?name=' + getCookie('name'), JSON.stringify(perData));
        }

        function delOrg(orgId) {

            return HttpService.post($rootScope.gateInfoApi + 'lms/customerOrg/delete?name=' + getCookie('name')+'&customerOrgId='+orgId);
        }
    }
})();
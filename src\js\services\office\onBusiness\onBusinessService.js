
(function() {
    'use strict';
  app.factory('onBusinessService', onBusinessService);
    onBusinessService.$inject=["HttpService",'$rootScope'];

  function onBusinessService(HttpService,$rootScope){
    
    var service={
        getOnBusinessInfo:getOnBusinessInfo,
        deleteOnBusinessInfo:deleteOnBusinessInfo,
        addOnBusinessInfo:addOnBusinessInfo,
        updateOnBusinessInfo:updateOnBusinessInfo
    };
    return service;

    /**
     * 分页查询出差信息
     */
    function getOnBusinessInfo(urlData) {
       return HttpService.post($rootScope.getWaySystemApi + 'onBusiness/getOnBusinessInfo', urlData);
    }


    /**
     * 删除出差信息
     */
    function deleteOnBusinessInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'onBusiness/deleteOnBusinessInfo', urlData);
    }

    /**
     * 新增出差信息
     */
    function addOnBusinessInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'onBusiness/addOnBusinessInfo', urlData);
    }

     /**
     * 修改出差信息
     */
    function updateOnBusinessInfo(urlData) {
        return HttpService.post($rootScope.getWaySystemApi + 'onBusiness/updateOnBusinessInfo', urlData);
    }

    
  }
})();

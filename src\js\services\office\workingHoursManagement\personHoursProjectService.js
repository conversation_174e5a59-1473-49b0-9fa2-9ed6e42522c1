/*
 * @Author: liyunmeng
 * @Date:   2020-08-03
 */
(function() {
    'use strict';
    app.factory('personHoursProjectService', personHoursProjectService);
    personHoursProjectService.$inject=["HttpService",'$rootScope'];

    function personHoursProjectService(HttpService,$rootScope){
        var service={
            getviewDetails: getviewDetails
        };
        return service;
        /**
         * 查询
         */
        function getviewDetails(urlData) {
            return HttpService.post($rootScope.getWaySystemApi + 'personHours/viewDetails', urlData);
        }
    }
})();

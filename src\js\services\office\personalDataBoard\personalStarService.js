(function() {
    'use strict';
    app.factory('personalStarService', personalStarService);
    personalStarService.$inject = [ "HttpService", '$rootScope' ];

    function personalStarService(HttpService, $rootScope) {
        var service = {
            selectPersonalStarTopTen:selectPersonalStarTopTen,
            selectPersonalStarDetail:selectPersonalStarDetail,
            selectPersonalStarHis:selectPersonalStarHis,
            selectPersonalStar:selectPersonalStar,
            selectMyStarReviewTopDetail:selectMyStarReviewTopDetail
        };
        return service;

        //查询我的星星排行榜前10
        function selectPersonalStarTopTen(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'personalStar/selectPersonalStarTopTen', urlData);
        }
        //查询我的星星
        function selectPersonalStar(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'personalStar/selectPersonalStar', urlData);
        }
        //查询我的星星明细
        function selectPersonalStarDetail(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'personalStar/selectPersonalStarDetail', urlData);
        }
        //查询我的星星历史
        function selectPersonalStarHis(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'personalStar/selectPersonalStarHis', urlData);
        }
        //查询评审贡献获星明细
        function selectMyStarReviewTopDetail(urlData){
            return HttpService.post($rootScope.getWaySystemApi + 'personalStar/selectMyStarReviewTopDetail', urlData);
        }
        


    }
})();

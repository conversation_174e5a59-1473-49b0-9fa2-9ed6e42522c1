(function() {
  'use strict';
  app.controller("gateway", ['$rootScope', '$scope', '$interval', '$stateParams', 'inform', 'Microservice', 'Trans',
    function($rootScope, $scope, $interval, $stateParams, inform, Microservice, Trans) {

      $scope.size = 10;
      $scope.gatewayRoutes = gatewayRoutes;
      $scope.gatewayRoutes();
      $scope.gatewayTimer = 0;

      $scope.$watch('$scope.gatewayTimer', function(newValue, oldValue) {
        if (newValue !== oldValue) {
          $interval.cancel($scope.interval);
          if (newValue !== '0') {
            $scope.interval = $interval(function() {
              // console.log($scope.timer);
              gatewayRoutes();
            }, $scope.gatewayTimer);
          }
        }
      });


      $scope.$on('$destroy', function() {
        $interval.cancel($scope.interval);
        //$interval.stop(interval);
      });

      function gatewayRoutes() {
        Microservice.gatewayRoutes()
          .then(function(data) {
            console.log("gatewayRoutes:" + data);
            $scope.gatewayRoutes = data;
          }, function() {
            inform.common(Trans("tip.requestError"));
          });
      }
    }
  ]);
})();
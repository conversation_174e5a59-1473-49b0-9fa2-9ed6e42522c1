/*
 * @Author: fubaole
 * @Date:   2017-09-25 11:11:04
 * @Last Modified by:   fubaole
 * @Last Modified time: 2018-02-28 09:35:42
 */

(function() {
    'use strict';
    app.controller("receive_news", ['$rootScope', '$scope', '$timeout', '$stateParams', '$modal', '$log', 'Trans', 'MessageService', 'inform', 'LocalCache', 'AgreeConstant',
        function($rootScope, $scope, $timeout, $stateParams, $modal, $log, Trans, MessageService, inform, LocalCache, AgreeConstant) {
            var interfaceMap = {};

            $scope.map = {}; // 条件
            $scope.orderStr = "is_read asc, create_time desc"; //条件
            $scope.getData = getData; // 初始化函数
            $scope.pages = inform.initPages(); // 初始化分页数据
            $scope.getData($scope.pages.pageNum); // 初始化请求数据
            $scope.searchData = searchData; // 查询
            $scope.checked = []; // 存放选中的Id
            $scope.selectAll = selectAll; // 全选
            $scope.selectOne = selectOne; // 单选
            $scope.changeMsgStatus = changeMsgStatus; // 更改消息状态
            $scope.open = open; // 根据用户Id删除接收消息操作

            // 排序
            $scope.title = 'id';
            $scope.order = order;

            // 按条件查询操作部分
            $scope.reset = reset; // 重置按钮
            $scope.advQuery = advQuery; //高级查询功能按钮

            // 获取消息类型值
            getMessageType();
            // 获取读取状态值类型
            getReadType();

            // 排序
            function order(str) {
                $scope.title = str;
                $scope.desc = !$scope.desc;
            }

            // 查询
            function searchData(num) {
                interfaceMap = angular.copy($scope.map);
                getData(AgreeConstant.pageNum);
            }

            // 获取表格数据
            function getData(num) {
                $scope.checked = [];
                $scope.select_all = false;
                if (!num) { num = 1; }
                MessageService.getReceiveMessageByMap(JSON.stringify(interfaceMap), num, $scope.pages.size, $scope.orderStr)
                    .then(function(data) {
                        // console.log(data)
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.pages.goNum = null;
                            var jsonData = data.result;
                            $scope.resultData = jsonData.list;
                            console.log($scope.resultData);
                            if ($scope.resultData.length===0) {
                                inform.common(Trans('tip.noData'));
                                $scope.pages = inform.initPages();
                            } else {
                                $scope.pages.total = jsonData.total;
                                $scope.pages.star = (jsonData.pageNum - 1) * jsonData.pageSize + 1;
                                $scope.pages.end = jsonData.pageNum * jsonData.pageSize;
                                $scope.pages.pageNum = jsonData.pageNum;
                            }
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans('tip.requestError'));
                    });
            }

            // 重置按钮
            function reset() {
                $scope.map.messageTitle = "";
                $scope.map.messageType = "";
                $scope.map.isRead = "";
            }

            //高级查询功能按钮
            function advQuery() {
                $scope.isOpen = !$scope.isOpen;
                $scope.map.isRead = "";
            }

            // 获取消息类型值
            function getMessageType() {
                MessageService.getDictValueListByDictTypeCode("alarm_notice_method")
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.messageTypeCode = data.result;
                            console.log(data.result);
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 获取读取状态值类型
            function getReadType() {
                MessageService.getDictValueListByDictTypeCode("messageReadStatus")
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.readTypeCode = data.result;
                            console.log(data.result);
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 选中所有的
            function selectAll() {
                if ($scope.select_all) {
                    $scope.checked = [];
                    angular.forEach($scope.resultData, function(i) {
                        i.checked = true;
                        $scope.checked.push(i);
                    });
                } else {
                    angular.forEach($scope.resultData, function(i) {
                        i.checked = false;
                    });
                    $scope.checked = [];
                }
            }

            // 选中一个
            function selectOne() {
                angular.forEach($scope.resultData, function(i) {
                    var index = $scope.checked.indexOf(i);
                    if (index === -1 && i.checked) {
                        $scope.checked.push(i);
                    } else if (index !== -1 && !i.checked) {
                        $scope.checked.splice(index, 1);
                    }
                });
                if ($scope.resultData.length === $scope.checked.length) {
                    $scope.select_all = true;
                } else {
                    $scope.select_all = false;
                }
            }

            // 更改消息状态
            function changeMsgStatus() {
                if ($scope.checked.length) {
                    var messageIds = [];
                    angular.forEach($scope.checked, function(data, index) {
                        if (data.isRead) {
                            inform.common(Trans("News.choseUnread"));
                            return;
                        }
                        messageIds.push(data.messageId);
                    });
                    console.log($scope.checked);
                    if (messageIds.length===$scope.checked.length) {
                        onSubmitStatus(messageIds.join());
                    }
                } else {
                    inform.common(Trans('common.chooseOneOpt'));
                }
            }

            // 保存提交消息状态
            function onSubmitStatus(ids) {
                MessageService.updateUserNoticeMessageStatus(ids)
                    .then(function(data) {
                        if (data.code==='00') {
                            inform.common(Trans("tip.saveSuccess"));
                            getData(1);
                        } else {
                            inform.common(data.message);
                        }
                    }, function() {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 根据用户Id删除接收消息操作
            function open(item) {
                var modalInstance = $modal.open({
                    templateUrl: 'myModalContent.html',
                    controller: 'ModalInstanceCtrl',
                    size: "sm",
                    resolve: {
                        items: function() {
                            return Trans('common.deleteTip');
                        }
                    }
                });
                modalInstance.result.then(function() {
                    if (item) {
                        MessageService.removeUserNoticeMessageByIds(item.messageId)
                            .then(function(data) {
                                if (data.code===AgreeConstant.resultCode) {
                                    interfaceMap = {};
                                    $scope.map = {};
                                    getData(1);
                                    inform.common(Trans("tip.delSuccess"));
                                } else {
                                    inform.common(data.message);
                                }
                            }, function() {
                                inform.common(Trans("tip.requestError"));
                            });
                    }
                });
            }

        }
    ]);
})();
(function() {
    'use strict';
    app.factory('repositoryPermissionMainService', repositoryPermissionMainService);
    repositoryPermissionMainService.$inject=["HttpService",'$rootScope'];

    function repositoryPermissionMainService(HttpService,$rootScope){
        var service={
            findRepositoryListByPage:findRepositoryListByPage,
            findRepositoryActiveListByPage:findRepositoryActiveListByPage,
        };
        function findRepositoryListByPage(urlData) {
            // return HttpService.post($rootScope.getWaySystemApi+'svnRepository/selectRepositoryByMap',urlData);
            return HttpService.post($rootScope.getWaySystemApi+'storageManage/getStorageData',urlData);
        }

        function findRepositoryActiveListByPage(urlData) {
            return HttpService.post($rootScope.getWaySystemApi+'storageManage/getStorageActiveData',urlData);
        }
        return service;
    }
})();

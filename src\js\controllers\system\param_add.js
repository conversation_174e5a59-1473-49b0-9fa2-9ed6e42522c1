(function() {
    'use strict';
    app.controller("param_add", ['$rootScope', '$scope', '$state', 'inform', 'SystemService', 'Trans', 'AgreeConstant',
        function($rootScope, $scope, $state, inform, SystemService, Trans, AgreeConstant) {
            $scope.limitList = AgreeConstant.limitList; // 正则校验配置
            $scope.param = {};
            $scope.getTypeCode = getTypeCode; //获取参数类型值
            $scope.getTypeCode();
            $scope.onSubmit = onSubmit; // 保存新增参数数据

            // 获取参数类型值
            function getTypeCode() {
                SystemService.getTypeCode()
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            $scope.typeCode = data.result;
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }

            // 保存新增参数数据
            function onSubmit() {
                SystemService.saveParam($scope.param)
                    .then(function(data) {
                        if (data.code===AgreeConstant.resultCode) {
                            inform.common(Trans("tip.saveSuccess"));
                            $state.go("app.system.param_Management");
                        } else {
                            inform.common(data.message);
                        }
                    }, function(error) {
                        inform.common(Trans("tip.requestError"));
                    });
            }
        }
    ]);
})();
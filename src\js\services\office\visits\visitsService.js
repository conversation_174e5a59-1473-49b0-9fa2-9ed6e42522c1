/*
* @Author: lijing
* @Date:   2021-04-28 10:05:00
* @Last Modified by:   lijing
* @Last Modified time: 2021-04-28 13:45:13
*/
(function() {
    'use strict';
  app.factory('visitsService', comService);
  comService.$inject=["HttpService",'$rootScope'];

  function comService(HttpService,$rootScope){
    var service={
            addVisits:addVisits,//增加访问量
            getVisits:getVisits,//获取访问量
            getVisitsDetail:getVisitsDetail
       //     getVisitsByPerson:getVisitsByPerson//获取访问量-人员维度
    };
    return service;

     //增加访问量
    function addVisits(urlData) {
    	return HttpService.post($rootScope.getWaySystemApi + 'visits/addVisits', urlData);
    }
     //获取访问量
     function getVisits(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'visits/getVisits',urlData);
     }
     function getVisitsDetail(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'visits/getVisitsDetail',urlData);
     }
/*    //获取访问量-人员维度
     function getVisitsByPerson(urlData){
        return HttpService.post($rootScope.getWaySystemApi + 'visits/getVisitsByPerson',urlData);
     }*/
   }
})();